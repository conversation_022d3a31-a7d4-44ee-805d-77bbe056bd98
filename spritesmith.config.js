const SpritesPlugin = require('webpack-spritesmith') // 引入雪碧图
const fs = require('fs')
const path = require('path')
const resolve = (dir) => path.join(__dirname, dir)

const spritesmithTasks = []

fs.readdirSync('src/common/assets/icons').forEach((dirname) => {
	if (fs.statSync(`src/common/assets/icons/${dirname}`).isDirectory()) {
		spritesmithTasks.push(
			new SpritesPlugin({
				src: {
					cwd: resolve(`src/common/assets/icons/${dirname}`),
					glob: '*.png'
				},
				target: {
					image: resolve(`src/common/assets/sprites/${dirname}.png`),
					css: [
						[
							resolve(`src/common/assets/sprites/${dirname}.less`),
							{
								format: 'function_based_template',
								spritesheetName: dirname
							}
						]
					]
				},
				customTemplates: {
					function_based_template: resolve('/sprite_handlebars_template.handlebars')
				},
				// 样式文件中调用雪碧图地址写法
				apiOptions: {
					cssImageRef: `~@/common/assets/sprites/${dirname}.png`
				},
				spritesmithOptions: {
					algorithm: 'binary-tree',
					padding: 10
				}
			})
		)
	}
})

module.exports = spritesmithTasks
