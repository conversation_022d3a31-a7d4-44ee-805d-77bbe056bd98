## doc 目录说明
```
doc
├─ package
│  └─ sqlite3
│     ├─ napi-v3-linux-glibc-arm64.tar.gz // linux arm64 sqlite3 包
│     ├─ napi-v3-linux-glibc-x64.tar.gz // linux x64 sqlite3 包
│     └─ napi-v3-win32-unknown-ia32.tar.gz // windows sqlite3 包
└─ README.md

```

### sqlite3 包 说明
```
下载依赖完成后，将对应架构的sqlite3 包存放在 解压到 node_modules/sqlite3/lib/binding 目录下
windows 为例：
sqlite3
├─ lib
│  └─ binding
│     ├─ napi-v6-win32-unknown-ia32
│     │  └─ node_sqlite3.node
│     └─ napi-v3-win32-unknown-ia32
│        └─ node_sqlite3.node
└─ README.md
```
