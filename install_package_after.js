const fs = require('fs')
const path = require('path')
const tar = require('tar')

function extract(targzFilePath, targetDirectory) {
	if (!fs.existsSync(targetDirectory)) {
		fs.mkdirSync(targetDirectory, { recursive: true })
	}
	tar.x({
		file: targzFilePath,
		cwd: targetDirectory
		// strip: 1 // 去掉顶层目录
	})
		.then(() => {
			console.log('文件解压并复制成功')
		})
		.catch((err) => {
			console.error('解压失败', err)
		})
}

// fs 解压targz压缩包，再将解压文件复制到 node_modules/sqlite3/lib/binding 目录
const sqlite3FileMap = {
	linux_arm64: 'napi-v3-linux-glibc-arm64.tar.gz',
	linux_x64: 'napi-v3-linux-glibc-x64.tar.gz',
	win32_ia32: 'napi-v3-win32-unknown-ia32.tar.gz',
	win32_x64: 'napi-v3-win32-unknown-ia32.tar.gz'
}
const targzFilePath = path.join(__dirname, 'doc/package/sqlite3/', sqlite3FileMap[process.platform + '_' + process.arch])
const targetDirectory = path.join(__dirname, 'node_modules/sqlite3/lib/binding/')
extract(targzFilePath, targetDirectory)
