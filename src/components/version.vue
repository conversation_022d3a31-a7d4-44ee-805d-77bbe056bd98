<template>
	<div class="versionBox">
		<div class="head">
			<i @dblclick="quit"></i>
			<h2 v-if="systemConfig.terminal">{{ systemConfig.terminal }}</h2>
		</div>
		<p class="version">版本信息：{{ version }}</p>
		<p v-if="systemConfig.copyright" class="copyright">{{ systemConfig.copyright }}</p>
		<div class="confirmBtn" @click="confirm">确认</div>
	</div>
</template>

<script>
export default {
	name: 'Version',
	data() {
		return {
			version: CURRENT_VERSION,
			systemConfig: {}
		}
	},
	mounted() {
		this.systemConfig = Object.assign({}, this.serverConfig)
	},
	methods: {
		quit() {
			const ipcRenderer = window.electronAPI?.ipcRenderer || null
			ipcRenderer && ipcRenderer.send('close')
		},
		confirm() {
			this.$parent.close('confirm')
		}
	}
}
</script>

<style lang="less" scoped>
.versionBox {
	background: #fff;
	padding: 40px 40px 20px;
	.head {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 60px;
		i {
			display: inline-block;
			width: 125px;
			height: 125px;
			background-image: url('../assets/images/pym.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			&.hj {
				background-image: url('../assets/images/logo_haijing.png');
			}
		}
		h2 {
			font-size: 50px;
			margin-left: 20px;
		}
	}
	.version {
		font-size: 35px;
		margin-bottom: 60px;
		text-align: center;
	}
	.copyright {
		font-size: 34px;
		color: #999;
		text-align: center;
	}
	.confirmBtn {
		width: 180px;
		height: 80px;
		line-height: 80px;
		text-align: center;
		color: #fff;
		font-size: 35px;
		background: #298bf0;
		margin: 40px auto 0;
		border-radius: 10px;
	}
}
</style>
<style>
.versionBox .ivu-modal-close .ivu-icon-ios-close {
	font-size: 90px;
}
.ivu-modal-header {
	background: #298bf0;
}
.ivu-modal-header-inner {
	height: 60px;
	line-height: 60px;
	font-size: 36px;
	color: #fff;
}
</style>
