<template>
	<div id="head">
		<div class="flex title1">
			<i class="logo" :class="{ hj: serverConfig.isHaiJing }" @dblclick="openDialog('config')"></i>
			{{ title1 }}
		</div>
		<div class="flex title2">{{ title2 }}</div>
		<div class="versionClickView" @dblclick="openDialog('version')"></div>
	</div>
</template>

<script>
import Config from '@/components/setConfig'
import Version from '@/components/version'
export default {
	name: 'Head',
	inject: ['updateAppInfo'],
	data() {
		return {
			title1: 'XX市XXX分局XXX派出所',
			title2: '智能物管柜管理系统',
			dialogObj: null
		}
	},
	created() {
		if (this.serverConfig) {
			const { policeName, appName } = this.serverConfig
			this.title1 = policeName
			this.title2 = appName
		}
	},
	methods: {
		closeDialog() {
			this.dialogObj && this.dialogObj.close()
			this.dialogObj = null
		},
		openDialog(type) {
			this.closeDialog()
			const componentMap = {
				config: {
					title: '系统配置',
					component: Config,
					componentParams: { isWeb: false }
				},
				version: {
					title: '版本信息',
					component: Version,
					componentParams: {}
				}
			}
			const currentComponent = componentMap[type]
			this.dialogObj = this.$GxxDialog({
				title: currentComponent.title,
				showHeader: true,
				showHeaderClose: false,
				component: currentComponent.component,
				componentParams: currentComponent.componentParams
			})
			this.dialogObj.on.then((res) => {
				this.dialogObj = null
				if (type == 'config' && res.type == 'confirm') {
					this.updateAppInfo(res.data)
				}
			})
		}
	},
	beforeDestroy() {
		this.closeDialog()
	}
}
</script>

<style lang="less" scoped>
@assets: '../assets/';
#head {
	width: 100%;
	height: 360px;
	background: #4f5eff;
	background: url('@{assets}/images/head_text_icon.png') no-repeat;
	background-size: 100% 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	flex-wrap: wrap;
	.flex {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: bold;
		color: #ebf5ff;
		letter-spacing: 3px;
	}
	.title1 {
		font-size: 56px;
		.logo {
			display: inline-block;
			width: 95px;
			height: 96px;
			background-image: url('@{assets}/images/logo.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			margin-right: 32px;
			margin-top: 15px;
			&.hj {
				background-image: url('@{assets}/images/logo_haijing.png');
			}
		}
	}
	.title2 {
		font-size: 75px;
		margin-top: 25px;
		line-height: 60px;
		-webkit-box-reflect: below 10px -webkit-gradient(linear, left top, left bottom, from(transparent), to(rgba(255, 255, 255, 0.31)));
	}
}
</style>
