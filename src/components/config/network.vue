<template>
	<div class="config-module">
		<div class="gxx-subtitle">网络配置</div>
		<div class="module-content">
			<div class="content-row">
				<div class="row-label required">IP</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.localIp" v-xKeyboard placeholder="127.0.0.1"></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label required">DNS</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.localDNS" v-xKeyboard placeholder="127.0.0.1"></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label required">网关</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.localGetway" v-xKeyboard placeholder="127.0.0.1"></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label required">子网掩码</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.localNetmask" v-xKeyboard placeholder="0~32"></gxx-input>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'NetworkConfig',
	props: ['params'],
	data() {
		return {
			configParams: {
				localIp: '',
				localDNS: '',
				localGetway: '',
				localNetmask: ''
			},
			config: this.$store.getters['getTerminalConfig']
		}
	},
	created() {
		const { localIp, localDNS, localGetway, localNetmask } = this.params.isWeb ? this.params.configParams : this.config
		this.configParams.localIp = localIp || ''
		this.configParams.localDNS = localDNS || ''
		this.configParams.localGetway = localGetway || ''
		this.configParams.localNetmask = localNetmask || ''
	},
	methods: {
		validate(type) {
			const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
			const obj = type == 'confirm' ? this.configParams : this.config
			if (!reg.test(obj.localIp)) {
				this.$GxxMessage({ message: '请配置IP信息！', type: 'error' })
				return false
			}
			if (!reg.test(obj.localDNS)) {
				this.$GxxMessage({ message: '请配置DNS信息！', type: 'error' })
				return false
			}
			if (!reg.test(obj.localGetway)) {
				this.$GxxMessage({ message: '请配置网关信息！', type: 'error' })
				return false
			}
			const netmaskReg = /^([0-9]|[1-2][0-9]?|3[0-2]?)$/
			if (!netmaskReg.test(obj.localNetmask)) {
				this.$GxxMessage({ message: '请配置子网掩码信息！', type: 'error' })
				return false
			}
			return true
		}
	}
}
</script>

<style lang="less" scoped>
@import url('./common.less');
.config-module .module-content .content-row .row-label {
	width: 75px;
}
</style>
