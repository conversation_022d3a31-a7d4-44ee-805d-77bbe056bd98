.config-module {
	display: flex;
	flex-direction: column;
	margin: 0 20px 0;
	.module-content {
		margin-top: 10px;
		display: flex;
		width: 100%;
		flex-wrap: wrap;
		.content-row {
			display: flex;
			height: 30px;
			line-height: 30px;
			width: calc(50% - 20px);
			position: relative;
			margin-bottom: 20px;
			.row-label {
				width: 155px;
				text-align: right;
				padding-left: 8px;
				text-align: justify;
				text-align-last: justify;
				font-size: 16px;
				&.required::after {
					content: '*';
					color: red;
					position: absolute;
					top: 0;
					left: 0;
				}
			}
			.row-colon {
				margin-left: 5px;
				margin-right: 10px;
			}
			> span {
				display: flex;
			}
			/deep/ .gxx-input-container,
			.gxx-select-wrap {
				flex: 1;
				width: 100% !important;
			}
			/deep/ .gxx-popover__reference-wrapper {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 5px;
				.doubt {
					width: 20px;
					height: 20px;
					line-height: 20px;
					border-radius: 50%;
					text-align: center;
					background-color: #515a6e;
					color: #fff;
				}
			}
			&:nth-child(2n) {
				margin-left: 40px;
			}
		}
	}
}
