<template>
	<div class="config-module">
		<div class="gxx-subtitle">智能硬件云平台配置</div>
		<div class="module-content">
			<div class="content-row">
				<div :class="['row-label', isRequired ? 'required' : '']">平台IP</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.iotAddress" v-xKeyboard placeholder="ws://127.0.0.1:17314/mqtt"></gxx-input>
			</div>
			<div class="content-row">
				<div :class="['row-label', isRequired ? 'required' : '']">产品编码</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.productKey" v-xKeyboard></gxx-input>
			</div>
			<div class="content-row">
				<div :class="['row-label', isRequired ? 'required' : '']">产品名称</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.productName" v-xKeyboard :min="1" :maxlength="32"></gxx-input>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'NetworkConfig',
	props: {
		params: Object,
		isRequired: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			configParams: {
				iotAddress: '',
				productKey: '',
				productName: '' // 默认值：涉案财物管理终端(IP)
			},
			config: this.$store.getters['getTerminalConfig']
		}
	},
	mounted() {
		const { iotAddress, productKey, productName } = this.params.isWeb ? this.params.configParams : this.config
		this.configParams.iotAddress = iotAddress || ''
		this.configParams.productKey = productKey || ''
		this.configParams.productName = productName || ''
	},
	methods: {
		validate(type) {
			if (!this.isRequired) {
				return true
			}
			const obj = type == 'confirm' ? this.configParams : this.config
			if (!obj.iotAddress) {
				this.$GxxMessage({ message: '请配置智能硬件云平台IP信息！', type: 'error' })
				return false
			}
			if (!obj.productKey) {
				this.$GxxMessage({ message: '请配置智能硬件云平台产品编码信息！', type: 'error' })
				return false
			}
			if (!obj.productName) {
				this.$GxxMessage({ message: '请配置智能硬件云平台产品名称信息！', type: 'error' })
				return false
			} else {
				if (obj.productName.length > 32) {
					this.$GxxMessage({ message: '智能硬件云平台产品名称不能超过32位！', type: 'error' })
					return false
				}
			}
			return true
		}
	}
}
</script>

<style lang="less" scoped>
@import url('./common.less');
</style>
