<template>
    <div class="deal-wrap">
        <div class="content-wrap">
            <span v-if="params.iconType" :class="iconTypeLink[params.iconType]"></span>
            <span class="tips">{{ params.text }}</span>
        </div>
        <div class="foolter">
            <span v-if="params.cancelText" class="btn btn-cancel" @click="evCancel">{{ params.cancelText }}</span>
            <span v-if="params.okText" class="btn btn-confirm" @click="evConfirm">{{ params.okText }}{{ confirmTxt ? confirmTxt : '' }}</span>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        params: {
            type: Object,
            default: () => {
                return {
                    okText: '',
                    cancelText: '',
                    duration: '',
                    text: '',
                    iconType: 'error'
                }
            }
        }
    },
    data() {
        return {
            iconTypeLink: {
                error: 'tipsIcon2',
                warning: 'tipsIcon1',
                success: 'tipsIcon3'
            },
            confirmTxt: '',
            cancelText: '',
            duration: 0
        }
    },
    mounted() {
        if (this.params.duration) {
            this.startDuration()
        }
    },
    methods: {
        startDuration() {
            this.duration = this.params.duration
            this.confirmTxt = '(' + this.duration + 'S)'
            setInterval(() => {
                if (this.duration < 1) {
                    this.$parent.close('confirm')
                } else {
                    this.duration--
                    this.confirmTxt = '(' + this.duration + 'S)'
                }
            }, 1000)
        },
        evConfirm() {
            // this.$emit('getReturn', this.delIndex)
            this.$parent.close('confirm')
        },
        evCancel() {
            this.$parent.close('cancel')
        }
    }
}
</script>
<style lang="less" scoped>
.deal-wrap {
    background: #ebf5ff;
    border-radius: 12px;
}
.content-wrap {
    width: 600px;
    height: 300px;
    text-align: center;
    .tips {
        display: block;
    }
}
.foolter {
    padding: 16px 0 30px;
    text-align: center;
    .btn {
        display: inline-block;
        margin-bottom: 0;
        font-size: 25px;
        font-weight: 400;
        min-width: 135px;
        height: 65px;
        line-height: 65px;
        border-radius: 5px;
        padding: 0 15px;
        text-align: center;
        vertical-align: middle;
        -ms-touch-action: manipulation;
        touch-action: manipulation;
        border: 1px solid transparent;
        white-space: nowrap;
        cursor: pointer;
        &.btn-confirm {
            color: #f2f2f2;
            background-color: #2b85e4;
            border-color: #2b85e4;
        }
        &.btn-cancel {
            background: #f26b6b;
            color: #fff;
            & + .btn-confirm {
                margin-left: 30px;
            }
        }
    }
}
</style>
