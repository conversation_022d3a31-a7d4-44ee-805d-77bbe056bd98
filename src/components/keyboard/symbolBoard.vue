<template>
    <ul class="en-board-container">
        <template v-for="(item, index) in numMap">
            <li :key="index" v-if="!['number','en', 'del'].includes(item)" @click="clickKey(item)">
                <span>{{ item }}</span>
            </li>
            <li :key="index" v-if="item === 'number'" class="key-num" @click="clickKey('number')">123</li>
            <li :key="index" v-if="item === 'en'" class="key-en" @click="clickKey('en')">abc</li>
            <li :key="index" v-if="item === 'del'" class="key-del"  @click="clickKey('backspace')"><Icon type="md-backspace" /></li>
        </template>
    </ul>
</template>
<script>
export default {
    name: 'symbolBoard',
    data() {
        return {
            numMap: [
                '[',']','{','}','#','%','^','*','+','=','number','_','\\','|','~','<','>','¥','£','•','en','.',',',':','\'','`','?','!','del'
            ]
        }
    },
    methods: {
        clickKey(key) {
            this.$emit('clickKey', key)
        }
    }
}
</script>
<style lang="less" scoped>
.en-board-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    margin-left: -11px;
    li {
        width: 96px;
        height: 120px;
        font-family: ArialMT;
        font-size: 54px;
        color: #669EFF;
        text-align: center;
        background: rgba(235,245,255,0);
        border-radius: 12px;
        border: 2px solid #669EFF;
        margin-left: 11px;
        margin-top: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        &:active {
            color: #fff;
            background: #669EFF;
        }
        span {
            height: 54px;
            line-height: 54px;
        }
        &.onCase {
            color: #fff;
            background: #669EFF;
            border: 2px solid #669EFF;
        }
        &.key-num {
            font-size: 42px;
        }
        &.key-en {
            font-size: 42px;
            width: 148px;
        }
        &.key-del {
            font-size: 66px;
            width: 148px;
            color: #669EFF;
        }
    }
}
</style>