<template>
    <div>
        <en-board @clickKey="KeyboardEvent" v-if="keyType === 'en'"></en-board>
        <num-board @clickKey="KeyboardEvent" v-if="keyType === 'number'"></num-board>
        <symbol-board @clickKey="KeyboardEvent" v-if="keyType === 'symbol'"></symbol-board>
    </div>
</template>
<script>
import enBoard from './enBoard.vue'
import numBoard from './numberBoard.vue'
import symbolBoard from './symbolBoard.vue'
export default {
    components: {
        enBoard,
        numBoard,
        symbolBoard
    },
    data() {
        return {
            // en number symbol
            keyType: 'en'
        }
    },
    methods: {
        KeyboardEvent(e) {
            console.log(e)
            switch (e) {
                case 'backspace':
                    this.$emit('delete')
                    break;
                case 'en':
                    this.keyType = 'en'
                    break;
                case 'number':
                    this.keyType = 'number'
                    break;
                case 'symbol':
                    this.keyType = 'symbol'
                    break;
                default:
                    this.$emit('input', e)
            }
        }
    }
}
</script>