<template>
    <ul class="en-board-container">
        <template v-for="(item, index) in numMap">
            <li :key="index" v-if="!['符','en', 'del'].includes(item)" @click="clickKey(item)">
                <span :class="{'not': isNaN(item)}">{{ item }}</span>
            </li>
            <li :key="index" v-if="item === '符'" class="key-symbol" @click="clickKey('symbol')">符</li>
            <li :key="index" v-if="item === 'en'" class="key-en" @click="clickKey('en')">abc</li>
            <li :key="index" v-if="item === 'del'" class="key-del"  @click="clickKey('backspace')"><Icon type="md-backspace" /></li>
        </template>
    </ul>
</template>
<script>
export default {
    name: 'numberBoard',
    data() {
        return {
            numMap: [
                1,2,3,4,5,6,7,8,9,0,'符','-','/',';','(',')','$','&','@','"','en','.',',',':','\'','`','?','!','del'
            ]
        }
    },
    methods: {
        clickKey(key) {
            this.$emit('clickKey', key)
        }
    }
}
</script>
<style lang="less" scoped>
.en-board-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    margin-left: -11px;
    li {
        width: 96px;
        height: 120px;
        font-family: ArialMT;
        font-size: 60px;
        color: #669EFF;
        text-align: center;
        background: rgba(235,245,255,0);
        border-radius: 12px;
        border: 2px solid #669EFF;
        margin-left: 11px;
        margin-top: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        &:active {
            color: #fff;
            background: #669EFF;
        }
        span {
            height: 60px;
            line-height: 60px;
            &.not {
                height: 54px;
                line-height: 54px;
            }
        }
        &.key-symbol {
            font-size: 46px;
        }
        &.key-en {
            font-size: 42px;
            width: 148px;
        }
        &.key-del {
            font-size: 66px;
            width: 148px;
            color: #669EFF;
        }
    }
}
</style>