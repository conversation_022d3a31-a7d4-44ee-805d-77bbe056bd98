<template>
    <ul class="en-board-container">
        <template v-for="(item, index) in ENMap">
            <li :key="index" v-if="!['maxToMin','number', 'del'].includes(item)" :class="{'enmax': isMinToMax}" @click="clickKey(isMinToMax ? item: item.toLowerCase())">
                <span>{{ isMinToMax ? item: item.toLowerCase() }}</span>
            </li>
            <li :key="index" v-if="item === 'maxToMin'" :class="{'max': isMinToMax}" @click="toggleCase">
                <i class="icons"></i>
            </li>
            <li :key="index" v-if="item === 'number'" class="key-num" @click="clickKey('number')">123</li>
            <li :key="index" v-if="item === 'del'" class="key-del"  @click="clickKey('backspace')"><Icon class="backspace" type="md-backspace" /></li>
        </template>
    </ul>
</template>
<script>
export default {
    name: 'enBoard',
    data() {
        return {
            ENMap: [
                'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P','maxToMin', 'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L','number', 'Z', 'X', 'C', 'V', 'B', 'N', 'M', 'del'
            ],
            isMinToMax: false
        }
    },
    methods: {
        // 大小写切换
        toggleCase() {
            this.isMinToMax = !this.isMinToMax
        },
        // 点击键盘
        clickKey(key) {
            this.$emit('clickKey', key)
        }
    }
}
</script>
<style lang="less" scoped>
.en-board-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    margin-left: -11px;
    li {
        width: 96px;
        height: 120px;
        font-family: ArialMT;
        font-size: 58px;
        color: #669EFF;
        text-align: center;
        background: rgba(235,245,255,0);
        border-radius: 12px;
        border: 2px solid #669EFF;
        margin-left: 11px;
        margin-top: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        .icons {
            display: inline-block;
            width: 48px;
            height: 43px;
            background: url(../../assets/images/key-min.png) no-repeat left center;
        }
        &:active,
        &.max {
            color: #fff;
            background: #669EFF;
        }
        &.max {
            .icons {
                background: url(../../assets/images/key-max.png) no-repeat left center;
            }
        }
        span {
            height: 60px;
            line-height: 50px;
        }
        &.enmax {
            span {
                height: 58px;
                line-height: 58px;
            }
        }
        &.onCase {
            color: #fff;
            background: #669EFF;
            border: 2px solid #669EFF;
        }
        &.key-num {
            font-size: 48px;
            width: 148px;
        }
        &.key-del {
            font-size: 66px;
            width: 148px;
            color: #669EFF;
            &:active {
                .backspace {
                    color: #fff;
                }
            }
        }
    }
}
</style>