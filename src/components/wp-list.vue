<template>
    <div class="wp-list">
        <div v-for="(item, i) in wp_list" :key="i" class="wp-list-item" @click="handleSelect(item, i)">
            <span v-if="selection" class="selection" :class="{ checked: item.checked }"></span>
            <span class="index">{{ i + 1 > 9 ? i + 1 : '0' + (i + 1) }}</span>
            <img v-if="showImage" :src="item.pictureUrl || item.wpxxPhotoData[0].url" />
            <div class="wo-info-warp">
                <p class="wpbh">{{ item.wpbh }}</p>
                <p class="wpmc">{{ item.wpmc }}</p>
            </div>
            <div v-if="showTool" class="deal">
                <span class="del" @click="handleDel(item, i)">移除</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        list: {
            type: [Array],
            default: () => []
        },
        selection: {
            type: [Bo<PERSON>an]
        },
        showTool: {
            type: [Boolean]
        },
        selected: {
            type: [Array],
            default: () => []
        },
        currentSelected: {
            type: [Array],
            default: () => []
        },
        showImage: {
            type: [Boolean]
        }
    },
    data() {
        return {
            wp_list: [],
            selected_wp: []
        }
    },
    watch: {
        list(val) {
            this.wp_list = JSON.parse(JSON.stringify(val))
            this.$forceUpdate()
        },
        selected(val = []) {
            if (!this.selection) return
            this.selected_wp = JSON.parse(JSON.stringify(val))
        }
    },
    created() {
        this.wp_list = JSON.parse(JSON.stringify(this.list))
    },
    mounted() {
        if (!this.selection) return
        this.selected_wp = JSON.parse(JSON.stringify(this.selected))
    },
    methods: {
        handleConfirm() {
            this.$emit('select', this.selected_wp)
        },
        handleSelect(item, index) {
            if (!this.selection) return
            this.$set(this.wp_list[index], 'checked', !item.checked)
            if (item.checked) {
                this.selected_wp.push(item)
            } else {
                const idx = this.selected_wp.findIndex((select_item) => select_item.id == item.id)
                this.selected_wp.splice(idx, 1)
            }
            this.$emit('select', this.selected_wp)
            if (this.selected_wp.length == this.wp_list.length) {
                this.$emit('selectedAll', true)
            } else {
                this.$emit('selectedAll', false)
            }
        },
        handleDel(item, i) {
            this.$emit('del', item)
        },
        selectedAll(val) {
            this.wp_list = this.wp_list.map((item) => {
                item.checked = val
                return item
            })
            if (val) {
                this.selected_wp = this.wp_list
                this.$emit('select', this.selected_wp)
            } else {
                this.selected_wp = []
                this.$emit('select', [])
            }
        }
    }
}
</script>

<style lang="less" scoped>
@assets: '~@/assets/images/temp';
.wp-list {
	display: flex;
	flex-direction: column;
	padding: 0 30px;
	.wp-list-item {
		height: 130px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-shadow: 0px 5px 10px 1px rgba(24, 144, 255, 0.1);
		margin-bottom: 24px;
		border: 1px solid #c0d3ff;
		border-radius: 16px;
		flex-shrink: 0;
		background-color: #fff;
		.index {
			width: 80px;
			color: #191e3b;
			font-size: 32px;
			flex-shrink: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 4px;
		}
		.selection {
			flex-shrink: 0;
			margin-left: 32px;
			display: inline-block;
			width: 48px;
			height: 48px;
			background-size: 100% 100%;
			background-image: url('@{assets}/wp-no-checked.png');
			&.checked {
				background-image: url('@{assets}/wp-checked.png');
			}
		}
		img {
			width: 148px;
			height: 110px;
			border-radius: 8px;
			margin-right: 32px;
		}
		.wo-info-warp {
			width: 100%;
			.wpbh {
				color: #3663b3;
				font-size: 36px;
			}
			.wpmc {
				font-size: 28px;
				color: #191e3b;
			}
		}
		.deal {
			flex-shrink: 0;
			margin-right: 30px;
			span {
				cursor: pointer;
			}
			.del {
				display: inline-block;
				width: 88px;
				height: 48px;
				border-radius: 24px;
				background-color: #fee0e4;
				color: #f84558;
				font-size: 24px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
}
</style>
