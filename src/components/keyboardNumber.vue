<template>
    <div class="loginByIDToast">
        <div class="keyboard-title">开柜密码认证</div>
        <input v-model="keycode" class="loginPassword" type="text" placeholder="请输入密码" readonly />
        <div class="loginByIDToastContent">
            <div class="keyBoxMain">
                <div class="keyOneBox" @click="keycodeEvent('1')">1</div>
                <div class="keyOneBox" @click="keycodeEvent('2')">2</div>
                <div class="keyOneBox" @click="keycodeEvent('3')">3</div>
                <div class="keyOneBox" @click="keycodeEvent('4')">4</div>
                <div class="keyOneBox" @click="keycodeEvent('5')">5</div>
                <div class="keyOneBox" @click="keycodeEvent('6')">6</div>
                <div class="keyOneBox" @click="keycodeEvent('7')">7</div>
                <div class="keyOneBox" @click="keycodeEvent('8')">8</div>
                <!-- <div class="keyOneBox" @click="keycodeEvent('')">X</div> -->
                <div class="keyOneBox" @click="keycodeEvent('9')">9</div>
                <div class="keyOneBox fontStyle" @click="clearAll('clearAll')">重输</div>
                <div class="keyOneBox" @click="keycodeEvent('0')">0</div>
                <div class="keyOneBox" @click="clearAll('clear')"><span class="delIcon"></span></div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'KeyBoard',
    data() {
        return {
            keycode: ''
        }
    },
    created() {},
    methods: {
        keycodeEvent(keycode) {
            this.keycode += keycode

            this.$emit('changeCode', this.keycode)
        },
        clearAll(type) {
            if (type == 'clearAll') {
                this.keycode = ''
            } else {
                this.keycode = this.keycode.substring(0, this.keycode.length - 1)
            }
            this.$emit('changeCode', this.keycode)
        },
        confirm() {
            if (this.keycode.length >= 4) {
                this.keycode = this.keycode.substring(0, 10)
            }
            this.$emit('closeModal')
            if (this.keycode) {
                this.keycode = String(parseInt(this.keycode))
            }
            this.$emit('changeCode', this.keycode)
        }
    }
}
</script>

<style lang="less" scoped>
.loginByIDToast {
	padding-bottom: 0;
	height: 910px;
	.keyboard-title {
		font-weight: bold;
		color: #337eff;
		font-size: 48px;
		text-align: center;
		margin-bottom: 30px;
		width: 100%;
	}
}
.keyOneBox {
	width: 31%;
	border: 2px solid #669eff;
	background: #ebf5ff;
}
.fontStyle {
	font-size: 40px;
}
.loginPassword,
.loginId {
	margin: 20px 0;
}
input {
	outline: none;
}
input {
	background: #ebf5ff;
	border: 2px solid #669eff;
}
input:focus-visible {
	outline: none;
	background: #ebf5ff;
}
input::placeholder {
	color: #999;
	font-size: 40px;
}
.tips {
	color: #fff;
	margin: 19px 0;
}
.keyBoxMain {
	margin-top: 0;
}
</style>
