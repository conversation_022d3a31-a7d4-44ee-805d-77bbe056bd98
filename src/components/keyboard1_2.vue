<template>
	<div class="loginByIDToast">
		<div class="loginByIDToastContent">
			<div class="keyboard-title">账号密码认证</div>
			<input id="loginId" v-model="keycode" autocomplete="off" type="text" class="loginId" onfocus="" placeholder="请输入账号" readonly @focus="inputAction(1)" />
			<input id="passwd" v-model="keycode2" autocomplete="off" type="password" class="loginPassword" placeholder="请输入密码" readonly @focus="inputAction(2)" />
			<p class="tips"></p>
			<div class="keyBoxMain">
				<div class="keyOneBox" @click="keycodeEvent('1')">1</div>
				<div class="keyOneBox" @click="keycodeEvent('2')">2</div>
				<div class="keyOneBox" @click="keycodeEvent('3')">3</div>
				<div class="keyOneBox" @click="keycodeEvent('4')">4</div>
				<div class="keyOneBox" @click="keycodeEvent('5')">5</div>
				<div class="keyOneBox" @click="keycodeEvent('6')">6</div>
				<div class="keyOneBox" @click="keycodeEvent('7')">7</div>
				<div class="keyOneBox" @click="keycodeEvent('8')">8</div>
				<div class="keyOneBox" @click="keycodeEvent('9')">9</div>
				<div class="keyOneBox" @click="keycodeEvent('0')">0</div>
				<div class="keyOneBox fontStyle" @click="clearAll('clearAll')">重输</div>
				<div class="keyOneBox" @click="clearAll('clear')"><span class="delIcon"></span></div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'KeyBoard',
	data() {
		return {
			keycode: '',
			keycode2: '',
			keyType: 1
		}
	},
	mounted() {},
	methods: {
		inputAction(type) {
			this.keyType = type
		},
		keycodeEvent(keycode) {
			if (this.keyType == 1) {
				this.keycode += keycode
			} else {
				this.keycode2 += keycode
			}
		},
		clearAll(type) {
			if (this.keyType == 1) {
				if (type == 'clearAll') {
					this.keycode = ''
				} else {
					this.keycode = this.keycode.substring(0, this.keycode.length - 1)
				}
			} else {
				if (type == 'clearAll') {
					this.keycode2 = ''
				} else {
					this.keycode2 = this.keycode2.substring(0, this.keycode2.length - 1)
				}
			}
		},
		getKeycodeResult() {
			return {
				keycode: this.keycode,
				keycode2: this.keycode2
			}
		}
	}
}
</script>

<style lang="less" scoped>
.loginByIDToast {
	padding-bottom: 0;
	height: 910px;
	.keyboard-title {
		font-weight: bold;
		color: #337eff;
		font-size: 48px;
		text-align: center;
		margin-bottom: 30px;
		width: 100%;
	}
}
.keyOneBox {
	border: 2px solid #669eff;
	background: #ebf5ff;
}
.fontStyle {
	font-size: 40px;
}
.loginPassword,
.loginId {
	margin-top: 20px;
}
input {
	background: #ebf5ff;
	border: 2px solid #669eff;
}
input {
	outline: none;
}
input:focus-visible {
	outline: none;
	background: #ebf5ff;
}
input::placeholder {
	color: #999;
	font-size: 40px;
}
.tips {
	color: #fff;
	margin: 19px 0;
}
.keyBoxMain {
	margin-top: 0;
}
</style>
