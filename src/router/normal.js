import ViewBox from '@/views/viewBox'

export default [
	{
		path: '/',
		redirect: '/home',
		meta: {
			requireAuth: true
		},
		component: ViewBox,
		children: [
			{
				path: 'home',
				name: 'home',
				meta: {
					title: '首页',
					requireAuth: true
				},
				component: () => import('@/views/normal_mode/home/<USER>')
			},
			{
				path: 'faceLogin',
				name: 'faceLogin',
				meta: {
					title: '登录',
					requireAuth: true
				},
				component: () => import('@/views/login/index')
			},
			{
				path: 'selfbatchTakeOut',
				name: 'selfbatchTakeOut',
				meta: {
					title: '取出物品列表',
					requireAuth: true
				},
				component: () => import('@/views/normal_mode/selfStorage/takeOut/selfbatchTakeOut')
			},
			{
				path: 'cabinetsOpenTakeOut',
				name: 'cabinetsOpenTakeOut',
				meta: {
					title: '批量取出开柜页面',
					requireAuth: true
				},
				component: () => import('@/views/normal_mode/selfStorage/takeOut/cabinetsOpenTakeOut')
			},
			{
				path: 'scanCode',
				name: 'scanCode',
				meta: {
					title: '扫描二维码',
					requireAuth: true
				},
				component: () => import('@/views/normal_mode/selfStorage/putIn/scanCode.vue')
			},
			{
				path: 'cabinetOpenPutIn',
				name: 'cabinetOPenPutIn',
				meta: {
					title: '扫码存入页面',
					requireAuth: true
				},
				component: () => import('@/views/normal_mode/selfStorage/putIn/cabinetOpenPutIn.vue')
			},
			{
				path: 'FileInventory',
				name: 'FileInventory',
				meta: {
					title: '案卷盘点',
					requireAuth: true
				},
				component: () => import('@/views/common/FileInventory')
			}
		]
	}
]
