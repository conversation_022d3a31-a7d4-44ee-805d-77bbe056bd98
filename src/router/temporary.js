import ViewBox from '@/views/viewBox'

export default [
    {
        path: '/',
        redirect: '/home',
        meta: {
            requireAuth: true
        },
        component: ViewBox,
        children: [
            {
                path: 'home',
                name: 'home',
                meta: {
                    title: '首页',
                    requireAuth: true
                },
                component: () => import('@/views/temporary_mode/home/<USER>')
            },
            {
                path: 'faceLogin',
                name: 'faceLogin',
                meta: {
                    title: '登录',
                    requireAuth: true
                },
                component: () => import('@/views/login/index')
            },
            {
                path: 'FileInventory',
                name: 'FileInventory',
                meta: {
                    title: '物品盘点',
                    requireAuth: true
                },
                component: () => import('@/views/common/FileInventory')
            },
            {
                path: 'choose_wp',
                name: 'choose_wp',
                meta: {
                    title: '选择物品',
                    requireAuth: true
                },
                component: () => import('@/views/temporary_mode/putIn/choose_wp')
            },
            {
                path: 'take_photo',
                name: 'take_photo',
                meta: {
                    title: '拍摄照片',
                    requireAuth: true
                },
                component: () => import('@/views/temporary_mode/putIn/take_photo')
            },
            {
                path: 'choose_police',
                name: 'choose_police',
                meta: {
                    title: '选择取货人',
                    requireAuth: true
                },
                component: () => import('@/views/temporary_mode/putIn/choose_police')
            },
            {
                path: 'choose_cabinet',
                name: 'choose_cabinet',
                meta: {
                    title: '选择柜位',
                    requireAuth: true
                },
                component: () => import('@/views/temporary_mode/putIn/choose_cabinet')
            },
            {
                path: 'cabinet_put_in',
                name: 'cabinet_put_in',
                meta: {
                    title: '存入物品',
                    requireAuth: true
                },
                component: () => import('@/views/temporary_mode/putIn/cabinet_put_in')
            },

            // 取件码
            {
                path: 'pickup_code',
                name: 'pickup_code',
                meta: {
                    title: '输入取件码',
                    requireAuth: true
                },
                component: () => import('@/views/temporary_mode/putOut/pickup_code')
            },
            {
                path: 'confirm_wp',
                name: 'confirm_wp',
                meta: {
                    title: '确认信息',
                    requireAuth: true
                },
                component: () => import('@/views/temporary_mode/putOut/confirm_wp')
            },
            {
                path: 'cabinet_put_out',
                name: 'cabinet_put_out',
                meta: {
                    title: '开柜取物',
                    requireAuth: true
                },
                component: () => import('@/views/temporary_mode/putOut/cabinet_put_out')
            }
        ]
    }
]
