export default new (function () {
	this.url = 'http://localhost:1606/'

	// --------------------------------------------------------
	// �����뷨����һ������
	// param : ����������磺show/pos(10,10)/size(800,300)/
	// async : true ��ʾ�첽��false ����ͬ��
	// --------------------------------------------------------
	this.SendCmdExt = function (param, async) {
		var json = null
		var xhr = new XMLHttpRequest()
		xhr.open('POST', this.url + param, async)

		xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded')
		xhr.onreadystatechange = function () {
			// ��ȡ���ݺ�Ĵ�������
			if (xhr.readyState == 4 && xhr.status == 200) {
				// ע�⣺�����Ҫ��ȡ���ؽ������ʹ��ͬ������
				json = JSON.parse(xhr.responseText)
			}
		}

		try {
			xhr.send(null)
		} catch (e) {
			alert('Please start "DWMain" first��')
			return null
		}

		return json
	}

	// �첽��ʽ
	this.SendCmd = function (param) {
		return this.SendCmdExt(param, true)
	}

	// ͬ����ʽ
	this.SendCmd2 = function (param) {
		return this.SendCmdExt(param, false)
	}

	// ���������ڼ��ݾɰ�ӿڣ�û�ù��ɰ汾�ľ�����Ҫʹ�á�
	this.IMEShow = function () {
		this.SendCmd('show')
	}
	this.IMEClose = function () {
		this.SendCmd('close')
	}
	this.IMEConfig = function () {
		this.SendCmd('config')
	}
	this.IMETermination = function () {
		this.SendCmd('termination')
	}
	this.IMEReloadSkin = function () {
		this.SendCmd('reskin')
	}
	this.IMEReloadConf = function () {
		this.SendCmd('reconf')
	}

	this.IMESetMode = function (mode) {
		this.SendCmd('mode(' + mode + ')')
	}
	this.IMESetSkin = function (skin) {
		this.SendCmd('skin(' + skin + ')')
	}
	this.IMESetSize = function (w, h) {
		this.SendCmd('size(' + w + ',' + h + ')')
	}
	this.IMESetPos = function (x, y) {
		this.SendCmd('pos(' + x + ',' + y + ')')
	}
	this.IMESetFollow = function (x, y) {
		this.SendCmd('mpos(' + x + ',' + y + ')')
	}
	this.IMESetFollowChrome = function (x, y) {
		this.SendCmd('mpos(' + x + ',' + y + ')')
	}
	this.IMEConfSet = function (s, k, v) {
		this.SendCmd('confset(' + k + ',' + v + ')')
	}
	this.IMESetCapslock = function (lock) {
		this.SendCmd('capslock(' + lock + ')')
	}
	this.IMESetPlaceholder = function (txt) {
		this.SendCmd('placeholder(' + txt + ')')
	}
	this.IMESetAutoHide = function (open) {
		this.SendCmd('autohide(' + open + ')')
	}
	this.IMESetAutoShow = function (open) {
		this.SendCmd('autoshow(' + open + ')')
	}

	// ���½ӿ���Ҫͬ����ʽ���ܻ�÷��ؽ��
	this.IMEGetPath = function () {
		this.SendCmd2('getpath')
	}
	this.IMEShowing = function () {
		this.SendCmd2('showing')
	}
	this.IMEGetWidth = function () {
		this.SendCmd2('getwidth')
	}
	this.IMEGetHeight = function () {
		this.SendCmd2('getheight')
	}
	this.IMEGetTop = function () {
		this.SendCmd2('gettop')
	}
	this.IMEGetLeft = function () {
		this.SendCmd2('getleft')
	}

	// ���½ӿ���Ч
	this.IMESetKBType = function (kbType) {}
	this.IMESkinSet = function (skin, s, k, v) {}
	this.IMESetBtnVisible = function (vb, ids) {}
	this.IMEStartup = function () {}
	this.IMERunning = function () {}
})()
