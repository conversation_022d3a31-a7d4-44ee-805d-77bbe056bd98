import Vue from 'vue'
// import dwime from './dwimeX'
const platform = window.electronAPI?.platform || ''
const exec = window.nodeAPI?.exec || null

Vue.directive('longpress', {
    bind: function(el, binding, vNode) {
        // Make sure expression provided is a function
        if (typeof binding.value !== 'function') {
            // Fetch name of component
            const compName = vNode.context.name
            // pass warning to console
            let warn = `[longpress:] provided expression '${binding.expression}' is not a function, but has to be`
            if (compName) {
                warn += `Found in component '${compName}' `
            }
            console.warn(warn)
        }
        // Define variable
        let pressTimer = null
        // Define funtion handlers
        // Create timeout ( run function after 1s )
        const start = (e) => {
            if (e.type === 'click' && e.button !== 0) {
                return
            }
            if (pressTimer === null) {
                pressTimer = setTimeout(() => {
                    // 如果要在按住按钮的时候循环行一个函数，将setTimeout改为setInterval即可
                    // Run function
                    handler()
                }, 1000)
            }
        }
        // Cancel Timeout
        const cancel = (e) => {
            // Check if timer has a value or not
            if (pressTimer !== null) {
                clearTimeout(pressTimer)
                pressTimer = null
            }
        }
        // Run Function
        const handler = (e) => {
            binding.value(e)
        }
        // Add Event listeners
        el.addEventListener('mousedown', start)
        el.addEventListener('touchstart', start)
        // Cancel timeouts if this events happen
        el.addEventListener('click', cancel)
        el.addEventListener('mouseout', cancel)
        el.addEventListener('touchend', cancel)
        el.addEventListener('touchcancel', cancel)
    }
})

Vue.directive('xKeyboard', {
    inserted: function(el, binding, vnode) {
        if (platform == 'win32') {
            // el.addEventListener('click', () => {
			// 	window.electronAPI.ipcRenderer.send('osk-open-dll')
            // })
        } else if (platform == 'linux') {
            el.addEventListener('click', () => {
                exec && exec('dbus-send --type=method_call --dest=org.onboard.Onboard /org/onboard/Onboard/Keyboard org.onboard.Onboard.Keyboard.Show')
            })
            el.addEventListener('blur', () => {
                exec && exec('dbus-send --type=method_call --dest=org.onboard.Onboard /org/onboard/Onboard/Keyboard org.onboard.Onboard.Keyboard.Hide')
            })
        }
    }
})
