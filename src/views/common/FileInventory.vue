<template>
    <div id="FileInventory">
        <headNav :show-box-index="[true, true, true]"></headNav>
        <h1>物品管理柜盘点</h1>
        <div class="cabinetBox">
            <div class="cabinetItem">
                <table>
                    <tr v-for="(item2, index) in cabinetBoxNum" :key="index + 'T'">
                        <td v-for="item3 in item2.col" :key="item3.id" :rowspan="item3.merge" :class="item3.num == '000' ? 'isScreen' : item3.isForbidden !== '0' ? 'isForbidden' : item3.fault ? 'isFault' : item3.assign ? 'inStock' : 'isFree'" @click="cabinetAction(item3, index)">
                            <Col style="width: 100%;height: 100%;position: absolute;top: 0;left: 0;right: 0;" @dblclick.native="dblclick(item3)"></Col>
                            {{ item3.num }}
                            <span v-show="item3.count > 0" class="AJcount">{{ item3.count }}</span>
                        </td>
                    </tr>
                </table>

                <div class="info">
                    <label>库存状态：</label>
                    <div class="info_free">
                        <i></i>
                        <span>空闲（{{ cabinetTotal.free }}）</span>
                    </div>
                    <div class="info_inStock">
                        <i></i>
                        <span>在库（{{ cabinetTotal.assign }}）</span>
                    </div>
                    <div class="info_forbidden">
                        <i></i>
                        <span>故障（{{ cabinetTotal.fault }}）</span>
                    </div>
                </div>
                <div v-if="showDetail" class="cabinetDetail">
                    <div class="head">
                        <div class="left">
                            <i class="titleIcon"></i>
                            <span>{{ cabinetDetail.info.num }}-已占用（{{ cabinetDetail.list.length }}）</span>
                        </div>
                        <div class="right" @click="showDetail = false">
                            <i class="closedBtn"></i>
                        </div>
                    </div>
                    <div class="caseList">
                        <div v-for="(item, index) in cabinetDetail.list" :key="index" class="caseItem">
                            <div class="xh">{{ index > 9 ? index + 1 : '0' + (index + 1) }}</div>
                            <div class="content">
                                <div v-if="item.wpmc">
                                    <label>物品名称</label>
                                    <span>{{ item.wpmc }} {{ item.wpsl }} ({{ item.jldw }})</span>
                                </div>
                                <div v-if="item.wpbh">
                                    <label>物品编号</label>
                                    <span>{{ item.wpbh }}</span>
                                </div>
                            </div>
                            <div class="putInTime">
                                <div>
                                    <label>存入时间：</label>
                                    <span>{{ item.zysj }}</span>
                                </div>
                                <div>
                                    <label>责任人：</label>
                                    <span>{{ item.cbrXm }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <ul class="cabinetBoxNum">
            <li v-for="(item, index) in boxArr" :key="index" :class="{ active: selectIndex == index }" @click="sideAction(item, index)">{{ item.sideCode }}</li>
        </ul>
        <Modal v-model="modal" :closable="false" :mask-closable="false" footer-hide :width="800">
            <Keyboard ref="KeyboardNumber"></Keyboard>
            <div class="openDoorBtn" @click="openDoor">开柜</div>
            <i class="closeModalBtn" @click="closeModal"></i>
        </Modal>
    </div>
</template>

<script>
import { voiceBroadcast } from '@/libs/utils'
import { getGHEvent } from '@/libs/lock/util'
import headNav from '_c/headNav'
import Keyboard from '_c/keyboardNumber'
import Lock from '@/libs/lock/index'
import { modalConfirm, modalInfo } from '@/libs/modal'
export default {
    components: {
        headNav,
        Keyboard
    },
    data() {
        return {
            cabinetId: '',
            cabinetBoxNum: [],
            selectIndex: 0,
            boxArr: [],
            cabinetTotal: {
                free: '',
                assign: '',
                fault: ''
            },
            modal: false,
            cabinetDetail: {},
            showDetail: false,
            sideId: '',
            falutNum: '',
            activeGh: '',
            trCount: '',
            lockObj: null
        }
    },
    created() {
        this.getCabinetBox()
    },
    beforeDestroy() {
        this.handleComDestroyed()
        voiceBroadcast(false)
        this.$Modal.remove()
    },
    methods: {
        // com开锁
        handleComInit(callback) {
            this.lockObj = new Lock()
            this.lockObj.on('open', (data) => {
                this.logger({ title: 'open-查锁记录-串口', value: data })
                if (data.code == 0) {
                    callback && callback()
                } else {
                    modalConfirm('重新连接', '返回首页', 'tipsIcon2', '连接锁控服务失败！', (flag) => {
                        this.lockObj = null
                        if (!flag) {
                            this.$router.push('/home')
                            return
                        }
                        this.handleComInit(() => {
                            this.handleComOpenDoor(this.activeGh)
                        })
                    })
                }
            })
            this.lockObj.on('error', (data) => {
                this.logger({ title: 'error-存入查锁记录-串口', value: data })
                this.handleComDestroyed()
                // this.sendFaultCabinet(this.activeGh)
                modalConfirm('重新连接', '返回首页', 'tipsIcon2', data.code == 1 ? '连接锁控服务失败！' : data.msg, (flag) => {
                    this.lockObj = null
                    if (!flag) {
                        this.$router.push('/home')
                        return
                    }
                    this.handleComInit(() => {
                        this.handleComOpenDoor(this.activeGh)
                    })
                })
            })
            this.lockObj.on('message', (data) => {
                this.logger({ title: 'message-存入查锁记录-串口', value: data })
                this.handleComDestroyed()
                const { result, actionType, box, door, msg } = data
                const gh = getGHEvent(box, door)
                if (actionType == 'openlock') {
                    if (result == 1) {
                        modalInfo('确定', 'tipsIcon3', `${gh}号柜开锁成功`)
                    } else {
                        // this.sendFaultCabinet(gh)
                        modalConfirm('重新连接', '确定', 'tipsIcon2', msg, (flag) => {
                            if (!flag) return
                            this.lockObj = null
                            this.handleComInit(() => {
                                this.handleComOpenDoor(gh)
                            })
                        })
                    }
                }
            })
            this.lockObj.init()
        },
        handleComOpenDoor(gh) {
            this.lockObj.handle('openlock', gh)
        },
        handleComDestroyed() {
            this.lockObj && this.lockObj.destroyed()
            this.lockObj = null
        },
        closeModal() {
            this.modal = false
            this.$refs.KeyboardNumber.keycode = ''
        },
        sendFaultCabinet(item) {
            // 发送故障请求
            const params = {
                cabinetCode: this.serverConfig.cabinetCode,
                centerId: this.serverConfig.centerId,
                gh: item,
                gzyy: '不能开柜'
            }
            this.$Post(this.PymApi.saveCabinetFault, params).then((res) => {})
        },
        dblclick(item) {
            if (item.num == '000') return
            if (!item.assign) {
                this.modal = true
                this.activeGh = item.num
            }
        },
        openDoor() {
            const keyboardNumberRef = this.$refs.KeyboardNumber
            if (!keyboardNumberRef.keycode) {
                this.ModalInfo('确认', 'tipsIcon2', '密码不能为空')
                return
            }
            if (keyboardNumberRef.keycode == this.serverConfig.cabinetPwd) {
                this.modal = false
                keyboardNumberRef.keycode = ''
                this.handleComInit(() => this.handleComOpenDoor(this.activeGh))
            } else {
                this.ModalInfo('确认', 'tipsIcon2', '密码错误')
            }
        },
        getCabinetBox() {
            const params = {
                cabinetCode: this.serverConfig.cabinetCode,
                centerId: this.serverConfig.centerId
            }
            this.$Post(this.PymApi.GET_CABINETSIDE, params).then((res) => {
                if (res.success) {
                    this.boxArr = res.sides
                    this.cabinetId = res.sides[0].cabinetId
                    this.dealData(res)
                } else {
                    this.ModalInfo('确认', 'tipsIcon2', res.msg)
                }
            })
        },
        dealData(res) {
            const faultCount = res.fault.length
            const assignCount = res.assign.length
            const iscdsCount = res.details.length
            this.cabinetTotal.fault = faultCount < 10 && faultCount > 0 ? '0' + faultCount : faultCount
            this.cabinetTotal.assign = assignCount < 10 && assignCount > 0 ? '0' + assignCount : assignCount
            let count = 0
            res.fault.forEach((item) => {
                if (res.assign.includes(item)) {
                    count++
                }
            })
            this.cabinetTotal.free = iscdsCount - assignCount - faultCount + count
            const list = []
            res.details.forEach((item2) => {
                if (item2.num == '000') {
                    this.cabinetTotal.free--
                }
                item2.assign = false
                item2.fault = false
                res.assign.forEach((item3) => {
                    if (item2.num == item3) {
                        item2.assign = true
                    }
                })
                res.fault.forEach((item3) => {
                    if (item2.num == item3) {
                        item2.fault = true
                    }
                })
                const sides = res.sides || this.boxArr
                if (sides.length) {
                    sides.forEach((item) => {
                        if (item.sideCode == item2.num.slice(0, 1)) {
                            item2.sideId = item.id
                        }
                    })
                }
                list.push(item2)
            })
            this.cabinetTotal.free = this.cabinetTotal.free < 10 && this.cabinetTotal.free > 0 ? '0' + this.cabinetTotal.free : this.cabinetTotal.free
            const positionArr = []
            const arr = []
            const itemArr = []
            const itemArr2 = []
            list.forEach((item2) => {
                if (arr.indexOf(item2.rowPos) == -1) {
                    arr.push(item2.rowPos)
                    itemArr.push(item2)
                } else {
                    itemArr2.push(item2)
                }
            })
            itemArr.forEach((item2, index2) => {
                const obj = {
                    row: item2.rowPos,
                    col: [item2]
                }
                itemArr2.forEach((item3) => {
                    if (item2.rowPos == item3.rowPos) {
                        obj.col.push(item3)
                    }
                })
                positionArr.push(obj)
            })
            this.trCount = positionArr.length
            positionArr[0].col.splice(1, 0, { merge: this.trCount })
            this.cabinetBoxNum = positionArr
        },
        getArchiveListByGh(item) {
            const params = {
                cabinetCode: this.serverConfig.cabinetCode,
                centerId: this.serverConfig.centerId,
                gh: item.num,
                sideId: item.sideId
            }
            this.$Post(this.PymApi.getWpxxDetail, params).then((res) => {
                if (res.success) {
                    this.cabinetDetail.info = item
                    this.cabinetDetail.list = res.data
                    this.$set(this.cabinetDetail.info, item)
                    this.$set(this.cabinetDetail.list, res.data)
                    this.showDetail = true
                    this.$forceUpdate()
                } else {
                    this.ModalInfo('确认', 'tipsIcon2', res.msg)
                }
            })
        },
        sideAction(item, index) {
            this.showDetail = false
            this.selectIndex = index
            this.$Post(this.PymApi.GET_SIDEDETAIL, { sideId: item.id }).then((res) => {
                if (res.success) {
                    this.dealData(res)
                } else {
                    this.ModalInfo('确认', 'tipsIcon2', res.msg)
                }
            })
        },
        cabinetAction(item, index) {
            if (item.assign) {
                this.getArchiveListByGh(item)
            }
        }
    }
}
</script>

<style lang="css" scoped>
.openDoorBtn {
	width: 798px;
	height: 120px;
	line-height: 120px;
	text-align: center;
	background: #337eff;
	border-radius: 16px;
	box-shadow: 0px 12px 20px 0px rgba(51, 126, 255, 0.3);
	color: #fff;
	font-size: 48px;
	margin: 0 auto 50px;
}
</style>
<style lang="less" scoped>
@assets: '../../assets/images';
#FileInventory {
	h1 {
		width: 100%;
		font-size: 48px;
		font-weight: 700;
		text-align: center;
		color: #2e71e5;
		letter-spacing: 5px;
		margin-top: 70px;
	}
	.cabinetBox {
		width: 920px;
		height: 1160px;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 40px auto;
		.cabinetItem {
			width: 100%;
			height: 100%;
			background: linear-gradient(#4ce7ff 0%, #5a90ff 100%);
			border-radius: 32px;
			box-shadow: 0px 20px 18px 0px rgba(51, 126, 255, 0.4);
			overflow: hidden;
			padding: 40px;
			box-sizing: border-box;
			position: relative;
			table {
				width: 100%;
				height: calc(~'100% - 81px');
				tr {
					td {
						width: calc(~'50% - 61px');
						text-align: center;
						font-size: 48px;
						color: #00d18b;

						border-radius: 4px;
						font-size: 20px;
						overflow: hidden;
						position: relative;
						&.isFault {
							background: #ffccd4;
						}
						.AJcount {
							display: inline-block;
							width: 56px;
							height: 32px;
							line-height: 32px;
							text-align: center;
							background: #e60012;
							border-radius: 4px;
							color: #fff;
							font-size: 24px;
							position: absolute;
							top: 0;
							left: 0;
						}
					}

					.isScreen {
						background: url('@{assets}/zg_icon.png') no-repeat;
						background-size: 100% 100%;
						color: transparent;
					}
					.isForbidden {
						background: #ffccd4;
						color: #e70a54;
					}
					.isFree {
						background: #f5fffc;
						color: #00d18b;
					}
					.inStock {
						background: #99ceff;
						color: #496eb8;
					}
				}
				tr:first-child {
					td:nth-child(2) {
						width: 121px;
						background: transparent;
						border-radius: 4px;
						margin: 0 17px;
						padding: 0 17px;
						span {
							display: block !important;
							width: 88px;
							height: 100%;
							background: #99d5ff;
							left: 17px;
						}
					}
				}
			}
			.info {
				width: 100%;
				height: 72px;
				background: #ffffff;
				border-radius: 8px;
				margin-top: 24px;
				display: flex;
				justify-content: space-around;
				align-items: center;
				div {
					display: flex;
					justify-content: space-around;
					align-items: center;
					label {
						font-size: 26px;
						color: #196aff;
					}
					i {
						display: inline-block;
						width: 56px;
						height: 32px;
						border-radius: 10px;
						margin-right: 24px;
					}
				}
				.info_free {
					i {
						background: #f5fffc;
						border: 2px solid #73e6bf;
					}
					span {
						color: #00d18b;
					}
				}
				.info_inStock {
					i {
						background: #99ceff;
						border: 2px solid #5ca3e6;
					}
					span {
						color: #4a70b9;
					}
				}
				.info_isFault {
					i {
						background: #ffccd4;
						border: 2px solid #e67384;
					}
					span {
						color: #e60050;
					}
				}
				.info_forbidden {
					i {
						background: #ffccd4;
						border: 2px solid #e67384;
					}
					span {
						color: #e60050;
					}
				}
			}
			.cabinetDetail {
				width: calc(~'100% - 80px');
				max-height: 700px;
				background: #fff;
				padding: 24px;
				box-sizing: border-box;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				overflow: hidden;
				.head {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 24px;
					i {
						display: inline-block;
					}
					.left {
						display: flex;
						justify-content: center;
						align-items: center;
						.titleIcon {
							width: 56px;
							height: 56px;
							background: url('@{assets}/listBtn.png') no-repeat;
							background-size: 100% 100%;
						}
						span {
							font-size: 28px;
							text-align: left;
							color: #007ef5;
							margin-left: 30px;
						}
					}
					.right {
						.closedBtn {
							width: 48px;
							height: 48px;
							background: url('@{assets}/closedBtn.png') no-repeat;
							background-size: 100% 100%;
						}
					}
				}
				.caseList {
					padding-right: 5px;
					padding-bottom: 10px;
					max-height: 600px;
					overflow: auto;
					.caseItem {
						.xh {
							width: 64px;
							height: 134px;
							line-height: 134px;
							background: #99ceff;
							font-size: 32px;
							text-align: center;
							color: #315ba3;
							margin-right: 8px;
							float: left;
						}
						.content {
							margin-left: 82px;
							height: 134px;
							background: #ebf5ff;
							padding: 14px;
							display: flex;
							flex-direction: column-reverse;
							justify-content: space-around;
							div {
								display: flex;
								align-items: center;
								label {
									display: inline-block;
									width: 100px;
									font-size: 24px;
									color: #999999;
									margin-right: 17px;
									text-align: left;
								}
								span {
									font-size: 24px;
									display: inline-block;
									width: calc(~'100% - 100px');
									white-space: nowrap;
									text-overflow: ellipsis;
									overflow: hidden;
									color: #3663b3;
								}
							}
						}
						.putInTime {
							display: flex;
							justify-content: flex-end;
							align-items: center;
							margin-top: 8px;
							div {
								margin-left: 16px;
								label {
									font-size: 20px;
									color: #666666;
								}
								span {
									font-size: 20px;
									color: #007ef5;
								}
							}
						}
					}
				}
				.takeOutBtn {
					width: 320px;
					height: 64px;
					line-height: 64px;
					text-align: center;
					background: #337eff;
					border-radius: 8px;
					font-size: 28px;
					color: #ffffff;
					margin: 24px auto 0;
				}
			}
		}
	}
	.cabinetBoxNum {
		width: 920px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-wrap: wrap;
		margin: 0 auto;
		li {
			width: 48px;
			height: 48px;
			line-height: 48px;
			overflow: hidden;
			text-align: center;
			background: #cce6ff;
			border-radius: 50%;
			margin: 0 12px;
			font-size: 26px;
			color: #5a90ff;
			&.active {
				background: #337eff;
				box-shadow: 0px 4px 8px 0px rgba(51, 126, 255, 0.5);
				color: #fff;
			}
		}
	}
}
</style>
<style scoped lang="less">
@assets: '../../assets/images';

.errorIcon {
	display: inline-block;
	width: 120px;
	height: 120px;
	background: url('@{assets}/red_error.png') no-repeat;
	background-size: 100% 100%;
	margin: 40px;
}
.openIcon {
	display: inline-block;
	width: 120px;
	height: 120px;
	background: url('@{assets}/laoding2.png') no-repeat;
	background-size: 100% 100%;
	margin: 40px;
}
.djcgIcon {
	display: inline-block;
	width: 120px;
	height: 120px;
	background: url('@{assets}/djcg.png') no-repeat;
	background-size: 100% 100%;
	margin: 40px;
}
.closedIcon {
	display: inline-block;
	width: 120px;
	height: 120px;
	background: url('@{assets}/closedIcon.png') no-repeat;
	background-size: 100% 100%;
	margin: 40px;
}
p {
	color: #5779b3;
	.CabinetDoor {
		color: #4f5eff;
		font-size: 38px;
	}
}
</style>
