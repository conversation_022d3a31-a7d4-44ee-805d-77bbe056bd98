import cryptoJS from 'crypto-js' // 进行 AES 对称加密
import jsRsaSign from 'jsrsasign' // 生成 RSA 密钥对
import JSEncrypt from 'jsencrypt' // 进行 RSA 加解密

/**
 * rsa 生成私钥 和 公钥
 */
export const generateRSAKey = function() {
	/**
	 * privateKey: 客户端RSA私钥
	 * publicKey: 客户端RSA公钥
	 */
	const rsaKeypair = jsRsaSign.KEYUTIL.generateKeypair('RSA', 1024)
	// 获取公钥
	let publicKey = jsRsaSign.KEYUTIL.getPEM(rsaKeypair.pubKeyObj)
	publicKey = publicKey.replace(/\r\n/g, '')
	publicKey = publicKey.replace('-----BEGIN PUBLIC KEY-----', '')
	publicKey = publicKey.replace('-----END PUBLIC KEY-----', '')
	// 获取私钥
	let privateKey = jsRsaSign.KEYUTIL.getPEM(rsaKeypair.prvKeyObj, 'PKCS8PRV')
	privateKey = privateKey.replace(/\r\n/g, '')
	privateKey = privateKey.replace('-----BEGIN PRIVATE KEY-----', '')
	privateKey = privateKey.replace('-----END PRIVATE KEY-----', '')

	return { privateKey, publicKey }
}
/**
 * rsa encrypt
 */
export const rsaEncrypt = function(encrypted, publicKey) {
	if (!publicKey) return ''
	const encrypt = new JSEncrypt()
	// encrypt.setPublicKey(clientInfo.publicKey)
	encrypt.setPublicKey(publicKey)
	const cipherText = encrypt.encrypt(encrypted)
	console.log('rsa加密后数据: ', cipherText)
	return cipherText.toString()
	return cipherText
}
/**
 * rsa decrypt
 */
export const rsaDecrypt = function(decrypted, privateKey) {
	if (!privateKey) return ''
	const privateKeyParse = '-----BEGIN PRIVATE KEY-----\n' + privateKey + '\n-----END PRIVATE KEY-----'
	const jsEncrypt = new JSEncrypt()
	jsEncrypt.setPrivateKey(privateKeyParse)
	const decrypt = jsEncrypt.decrypt(decrypted)
	console.log('rsa解密后数据: ', decrypt)
	return decrypt.toString(cryptoJS.enc.Utf8)
	return decrypt
}
/**
 * aes 生成 数据加密 key， 同时用后台publicKey加密
 */
export const generateAESKey = function(publicKey) {
	let key = cryptoJS.lib.WordArray.random(128 / 8)
	key = key.toString()
	const encryptKey = rsaEncrypt(key, publicKey)
	return { key, encryptKey }
}
/**
 * aes 数据加密
 */
export const aesEncrypt = function(encrypted, aesKey) {
	const srcs = cryptoJS.enc.Utf8.parse(encrypted)
	const key = cryptoJS.enc.Utf8.parse(aesKey)
	// console.log('aes加密: utf-8 key', key)
	const encrypt = cryptoJS.AES.encrypt(srcs, key, { mode: cryptoJS.mode.ECB, padding: cryptoJS.pad.Pkcs7 })
	// return encrypt.ciphertext.toString()
	return encrypt.toString()
}
/**
 * aes 数据解密
 */
export const aesDecrypt = function(decrypted, aesKey) {
	console.log('aes解密: aesKey', aesKey)

	const key = cryptoJS.enc.Utf8.parse(aesKey)
	// let encryptedHexStr = cryptoJS.enc.Hex.parse(decrypted)
	// let srcs = cryptoJS.enc.Base64.stringify(encryptedHexStr)
	// let srcs = cryptoJS.enc.Utf8.parse(encryptedHexStr)
	// let srcs = cryptoJS.enc.Base64.parse(decrypted)
	let decrypt = cryptoJS.AES.decrypt(decrypted, key, { mode: cryptoJS.mode.ECB, padding: cryptoJS.pad.Pkcs7 })
	// console.log('aes解密: decrypt', decrypt)
	let decryptedStr = decrypt.toString(cryptoJS.enc.Utf8)
	// console.log('aes解密: decryptedStr', decryptedStr)
	return decryptedStr.toString()
	// console.log('aes解密: decryptedStr', cryptoJS.enc.Utf8.stringify(decrypt))
	return cryptoJS.enc.Utf8.stringify(decrypt)
}
