// MQTT
import mqtt from 'mqtt'
import { EventEmitter } from 'events'
import { generateAESKey, aesEncrypt, rsaDecrypt, aesDecrypt } from '@/views/mqtt/auth'
import { logger } from '@/libs/log'

const eventObj = {
    getLicense: 'license/get', // 获取授权
    postProperty: 'property/post', // 属性上报
    getProperty: 'property/get_reply', // 属性获取
    getConfig: 'config/get', // 获取配置
    // postOta: 'ota/post_reply', // 设备升级
    getOta: 'ota/get', // 获取设备版本
    // 事件
    diskCapacityAlarmEvent: 'event/diskCapacityAlarmEvent', // 磁盘容量告警事件
    faultReportEvent: 'event/faultReportEvent', // 故障上报事件
    upgradeEvent: 'event/upgradeEvent', // 设备升级进度事件
    // 功能
    reboot: 'service/reboot_reply', // 重启应用
    rebootDevice: 'service/rebootDevice_reply', // 重启设备
    restart: 'service/restart_reply', // 重启设备
    config: 'service/config_reply', // 配置设备
    openBox: 'service/openBox_reply', // 打开柜子
    upgrade: 'service/upgrade_reply', // 设备升级
    ntp: 'service/ntp_reply', // 同步设备时间
    license: 'service/license_reply', // license授权文件
    postEncrypt: 'encrypt/get' // 数据加密密钥交换
}

class MQTTServer extends EventEmitter {
    constructor(iotAddress, options, reconnectionTime = 5000) {
        super()
        this.iotAddress = iotAddress // mqtt连接地址 ws://ip:port/mqtt
        this.macAddress = options.macAddress
        this.client = null
        this.options = Object.assign(
            {
                productKey: '', // 产品编码
                deviceCode: this.macAddress, // 设备唯一标识。如。mac
                model: 'm1' // 设备型号。没有则用m1
            },
            options
        )
        this.publishPrefix = `sys/${this.options.productKey}/${this.options.deviceCode}/s/`
        this.topicPrefix = `sys/${this.options.productKey}/${this.options.deviceCode}/c/`
        this.reconnectionTime = reconnectionTime
        this.isDestroy = false // 是否销毁
    }

    /**
	 * 初始化 mqtt
	 */
    init() {
        if (!this.iotAddress) {
            this.emit('init', false)
            return
        }
        const options = {
            clientId: `${this.options.productKey}&${this.options.deviceCode}&${this.options.model}`,
            username: this.macAddress,
            password: this.macAddress,
            clean: true, // true: 清除会话, false: 保留会话
            connectTimeout: 8 * 1000, // 重连时间
            reconnectPeriod: 60 * 1000,
            keepalive: 60
        }

        console.log('init this.client&options', this.client, options)

        this.client = mqtt.connect(this.iotAddress, options)

        this.client.on('connect', () => {
            // 先将数据加密方式置为默认值1
            this.setIotServerInfo(
                JSON.stringify({
                    encryptType: 1
                })
            )
            this.emit('init', true)

            this.receive()
        })
        this.client.on('reconnect', () => {
            console.log('重连了reconnect...')
            logger('mqtt 服务重新连接成功')
        })

        this.client.on('error', (err) => {
            console.log('mqtt 异常：', err)
            logger('mqtt 服务异常', err)
            if (String(err).indexOf('Not authorized') > -1) {
                this.destroy()
                return
            }

            this.client = null
            if (this.isDestroy) {
                this.emit('close')
            } else {
                console.log('mqtt服务连接异常关闭, 准备重连...')
                this.reconnect()
            }
        })

        this.client.on('close', () => {
            logger('mqtt 服务关闭')
            console.log('mqtt ws was closed')
        })

        this.client.on('message', (topic, message) => {
            const type = topic.replace(this.topicPrefix, '')
            // 终端上行请求数据，返回结果是data 下行(被动)接收IOT平台推送结果, 放回结果是params
            const result = JSON.parse(message) || {}
            const data = (result.data ? result.data : result.params) || {}
            console.log('mqttServer接收到 message: ', type)
            switch (type) {
                case 'license/get_reply':
                    this.emit('message', type, data)
                    break
                case 'encrypt/get_reply':
                    /**
					 * 保存服务端数据传输授权文件公钥
					 * data
					 * encryptType 数据加密方式 1.不加密 2.AES+RSA双向加密
					 * publicKey 服务端RSA公钥
					 */
                    console.log('获取到服务器加密公钥信息', data)
                    this.setIotServerInfo(JSON.stringify(data))

                    /* const aes = generateAESKey(data.publicKey)
					const clientInfo = this.getClientInfo()
					clientInfo.aesKey = aes.key
					clientInfo.encryptKey = aes.encryptKey

					this.setClientRsaInfo(JSON.stringify(clientInfo))
					console.log('clientInfo', clientInfo) */

                    this.emit('message', type, JSON.stringify(data))

                    break
                    // 属性获取
                case 'property/get':
                    this.emit('message', type, this.dataDecrypt(result))
                    break
                    // 设备升级
                case 'service/upgrade':
                    this.emit('message', type, this.dataDecrypt(result))
                    break
                    // 设备版本信息
                case 'ota/get_reply':
                    this.emit('message', type, this.dataDecrypt(result))
                    break
                    // 配置设备
                case 'service/config':
                    this.emit('message', type, this.dataDecrypt(result))
                    break
                case 'service/reboot':
                    this.emit('message', type, this.dataDecrypt(result))
                    break
                    // 开柜
                case 'service/openBox':
                    this.emit('message', type, this.dataDecrypt(result))
                    break
                    // 下发授权文件
                case 'service/license':
                    this.emit('message', type, this.dataDecrypt(result))
                    break
                    // 重启设备
                case 'service/rebootDevice':
                    this.emit('message', type, this.dataDecrypt(result))
                    break
                    // 重启设备
                case 'service/restart':
                    this.emit('message', type, this.dataDecrypt(result))
                    break
                    // 同步设备时间
                case 'service/ntp':
                    this.emit('message', type, this.dataDecrypt(result))
                    break
                default:
                    break
            }
        })
    }
    /**
	 * 重连
	 */
    reconnect() {
        console.log('重连....')
        const timer = setTimeout(() => {
            this.init()
            clearTimeout(timer)
        }, this.reconnectionTime)
    }

    /**
	 * 发布
	 */
    publish(key, params) {
        const topic = this.publishPrefix + eventObj[key]
        if (eventObj[key]) {
            const serverConfig = this.getIotServerInfo()
            const isEncrypt = serverConfig.encryptType && serverConfig.encryptType == 2

            const message = {
                id: new Date().getTime(),
                method: eventObj[key].replace(/\//g, '.')
            }

            if (isEncrypt) {
                const aes = generateAESKey(serverConfig.publicKey)
                params = JSON.stringify(params)
                message.params = aesEncrypt(params, aes.key)
                message.aesKey = aes.encryptKey
            } else {
                message.params = params
            }
            console.log('主动上报: ', topic, params)
            this.client.publish(topic, JSON.stringify(message))
        } else {
            this.emit('error', { topic, message: `推送主题：${key}失败` })
        }
    }

    /**
	 * 订阅
	 */
    receive() {
        console.log('开始订阅', this.client)
        this.client.subscribe(this.topicPrefix + '#', (error) => {
            console.log('订阅主题成功')
            if (error) {
                console.log(`${this.topicPrefix} subscribe error: ${error}`)
                this.emit('receive', false)
                this.destroy()
                this.reconnect()
            } else {
                this.emit('receive', true)
            }
        })
    }

    /**
	 * 销毁
	 */
    destroy() {
        if (this.client && this.client.end) {
            this.client.end(false, () => {
                console.log('Successfully disconnected!')
            })
        }
        this.client = null
    }
    /**
	 * 关闭
	 */
    close() {
        console.log('调用mqtt close()...')
        this.isDestroy = true
        this.destroy()
        this.removeAllListeners('init')
        this.removeAllListeners('message')
        this.removeAllListeners('error')
        this.removeAllListeners('close')
        this.removeAllListeners('receive')
    }

    /**
	 * 数据解码
	 */
    dataDecrypt(result) {
        const serverConfig = this.getIotServerInfo()
        let data = (result.data ? result.data : result.params) || {}
        if (serverConfig.encryptType == 2) {
            // 获取客户端RSA私钥
            // const clientInfo = MQTTServer.getClientInfo()
            // if (clientInfo.privateKey) {
            // 	return ''
            // }
            const privateKey = this.getClientInfo().privateKey
            const aesKey = rsaDecrypt(result.aesKey, privateKey)
            console.log('数据解码: 获取后台返回的aesKey解密后: ', aesKey)
            data = aesDecrypt(data, aesKey)

            data = JSON.parse(data)
        }
        return data
    }
    /**
	 * 保存客户端加密信息
	 */
    setClientRsaInfo(clientConfig) {
        localStorage.setItem('iot_client_info', clientConfig)
    }
    /**
	 * 保存客户端加密信息
	 */
    getClientInfo() {
        return JSON.parse(localStorage.getItem('iot_client_info') || '{}')
    }
    /**
	 * 保存IOT平台加密信息
	 */
    setIotServerInfo(serverConfig) {
        localStorage.setItem('iot_server_info', serverConfig)
    }
    /**
	 * 获取IOT平台加密信息
	 */
    getIotServerInfo() {
        return JSON.parse(localStorage.getItem('iot_server_info') || '{}')
    }
}

export default MQTTServer
