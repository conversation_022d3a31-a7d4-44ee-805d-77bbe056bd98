<template>
	<div class="mqtt-container"></div>
</template>

<script>
import MQTT from './index.js'
import { lockTypeList } from '@/utils/common_dict'
import { versionCompare } from '@/utils/normal_util'
import { getGHEvent } from '@/libs/lock/util'
import Lock from '@/libs/lock/index'
import { generateRSAKey } from '@/views/mqtt/auth'
const ipcRenderer = window.electronAPI?.ipcRenderer || null
export default {
    name: 'Mqtt',
    data() {
        return {
            mqtt: null,
            mqttLink: false,
            upgradeStatus: false
        }
    },
    computed: {
        config() {
            return this.$store.getters['getTerminalConfig'] || {}
        }
    },
    mounted() {
        this.initializeIPC()
    },
    methods: {
        initializeIPC() {
            if (!ipcRenderer) return
			
            // 初始化MQTT连接
            if (this.config.iotAddress && this.config.productKey) {
                this.initMqtt()
            }
			
            // 订阅IPC事件
            ipcRenderer.on('property-post', (event, data) => {
                this.handlePropertyPost(data)
            })
            ipcRenderer.on('upgrade-progress', (event, downloadInfo) => {
                this.mqtt.publish('upgradeEvent', downloadInfo)
            })
            ipcRenderer.on('upgrade-status', (event, status) => {
                this.handleUpgradeStatus(status)
            })
        },
        async handlePropertyPost(data) {
            data.name = this.config.productName
            data.model = lockTypeList.filter((item) => item.value == this.config.lockType)[0]?.label || 'Unknown Model'
            try {
                const { total, free } = await this.getStatisticsInfo()
                data.idleBox = free
                data.totalBox = total
                this.mqtt.publish('postProperty', data)
            } catch (error) {
                console.error('Failed to get statistics info:', error)
            }
        },
        handleUpgradeStatus(status) {
            // 根据实际状态更新upgradeStatus
            this.upgradeStatus = status; // 假设status是一个布尔值，表示升级状态
        },
        initMqtt() {
            if (!ipcRenderer) {
                return
            }
            const macAddress = ipcRenderer ? ipcRenderer.sendSync('get-mac-address') : ''
            console.log('initMqtt -------------------')
            this.mqtt = new MQTT(this.config.iotAddress, { productKey: this.config.productKey, macAddress })

            this.mqtt.on('init', (bool) => {
                this.mqttLink = bool
                console.log(`mqtt 连接${bool ? '成功' : '失败'}....`)
                if (!bool) {
                    this.mqtt = null
                }
            })
			
            this.mqtt.on('message', (topic, data) => {
                console.log('mqtt组件message data: ', data)
                switch (topic) {
                    // 获取加密信息
                    case 'encrypt/get_reply':
                        if(!this.upgradeStatus) {
                            // 属性上报
                            this.propertyPost()

                            // 版本号获取
                            console.log('获取设备版本，检查更新')
                            this.mqtt.publish('getOta', { version: CURRENT_VERSION })
                        }
                        break
                        // 开柜
                    case 'service/openBox':
                        if(!this.upgradeStatus) {
                            this.mqttOpenLock(data, (res) => {
                                // 将开柜结果推送iot平台
                                this.mqtt.publish('openBox', res)
                            })
                        }
                        break
                        // 属性获取
                    case 'property/get':
                        if(!this.upgradeStatus) {
                            // 属性上报
                            this.propertyPost()
                        }
                        break
                        // 获取柜子配置
                    case 'config/get_reply':
                        if(!this.upgradeStatus) {
                            console.log('获取配置, 推送终端配置信息')
                            this.mqtt.publish('getConfig', this.config)
                        }
                        break
                        // 更新柜子配置
                    case 'service/config':
                        if(!this.upgradeStatus) {
                            console.log('下发更新配置', data)
                            this.updateConfig(data)
                        }
                        break
                        // 应用升级
                    case 'service/upgrade':
                        if(!this.upgradeStatus) {
                            this.logger('接收升级指令', data)
                            this.upgrade(data)
                            const replyData = {
                                taskId: data.taskId,
                                packId: data.packId
                            }
                            this.mqtt.publish('upgrade', replyData)
                        }
                        break
                        // 获取应用版本信息
                    case 'ota/get_reply':
                        if(!this.upgradeStatus) {
                            console.log('最新升级包信息, 比对版本信息, 进行升级, 推送升级进度')
                            this.upgrade(data)
                        }
                        break
                        // 下发授权文件
                    case 'service/license':
                        console.log('下发更新配置', data)
                        break
                        // 同步时间
                    case 'service/ntp':
                        ipcRenderer && ipcRenderer.send('ntp', data.time)
                        this.mqtt.publish('ntp', { success: true })
                        break
                        // 重启应用
                    case 'service/reboot':
                        if(!this.upgradeStatus) {
                            ipcRenderer && ipcRenderer.send('reboot-terminal')
                            this.mqtt.publish('reboot', { success: true })
                        }
                        break
                        // 重启设备
                    case 'service/restart':
                        if(!this.upgradeStatus) {
                            ipcRenderer && ipcRenderer.send('reboot-device')
                            this.mqtt.publish('restart', { success: true })
                        }
                        break
                    default:
                        break
                }
            })

            this.mqtt.on('error', (error) => {
                console.log('error', error)
            })

            this.mqtt.on('receive', (bool) => {
                console.log(`订阅${bool ? '成功' : '失败'}...`)
                // 订阅成功， 进行 数据加密私钥 推送
                if (bool) {
                    // rsa 生成公私钥
                    const data = generateRSAKey()
                    // 本地存储公私钥
                    this.mqtt.setClientRsaInfo(JSON.stringify(data))
                    // 向服务端推送 公钥
                    console.log('向服务端推送公钥')
                    this.mqtt.publish('postEncrypt', { publicKey: data.publicKey })
                }
            })

            this.mqtt.on('close', () => {
                console.log('mqtt 连接关闭...')
                this.mqtt = null
                this.mqttLink = false
            })

            this.mqtt.init()
        },

        // 属性上报
        propertyPost() {
            ipcRenderer && ipcRenderer.send('property-post')
        },

        // 更新配置
        updateConfig(data) {
            console.log('this.updateConfig')
            this.$store.commit('setTerminalConfig', { ...this.config, ...data })
            window.location.reload()
        },

        // 应用升级
        upgrade(data) {
            if (this.config.upgradType == 'iot') {
                const version = data.version
                const downloadUrl = data.fileUrl || data.url
                console.log('升级----->：当前应用版本号: ' + CURRENT_VERSION)
                console.log('升级----->：线上最新版本号: ' + version)
                console.log('升级----->：下载地址: ' + downloadUrl)
                const diffVersion = versionCompare(CURRENT_VERSION, version)
                console.log('升级----->：版本比对结果: ' + diffVersion)
                this.logger('升级----->：版本比对结果: ', diffVersion)
                if (diffVersion > 0) {
                    try {
                        this.upgradeStatus = true
                        ipcRenderer && ipcRenderer.send('upgrage', data)
                    } catch (e) {
                        this.logger('升级----->：通知升级异常：', e)
                    }

                }
            }
        },

        // 远程开锁
        mqttOpenLock(arg, callback) {
            const lockNum = arg.lockNum ? arg.lockNum : getGHEvent(arg.box, arg.door)
            console.log('mqtt远程开锁信息：', JSON.stringify(arg))
            if (lockNum) {
                // 连接锁控服务
                const lock = new Lock(false)
                lock.on('init-success', () => {
                    lock.openLock(lockNum)
                })
                lock.on('message', (data) => {
                    callback({ success: data.result == 1 })
                    lock.destroyed()
                })
                lock.on('error', (err) => {
                    callback(false)
                    lock.destroyed()
                })
                lock.init()
            }
        },

        // 获取统计信息
        getStatisticsInfo() {
            return new Promise((resolve, reject) => {
                let total = 0
                let free = 0
                const params = {
                    cabinetCode: this.config.cabinetCode,
                    centerId: this.config.centerId,
                    searchObj: '{}',
                    deviceType: ''
                }
                this.$Post(this.PymApi.getStatisticsInfo, params)
                    .then((res) => {
                        if (res.success) {
                            total = res.total || 0
                            free = res.free || 0
                        }
                    })
                    .finally(() => {
                        resolve({ total, free })
                    })
            })
        },

        // 运维平台下发升级
        downUpgrade() {
            if (this.mqtt) {
                this.mqtt.publish('getOta')
                return { code: 200, msg: '开始升级' }
            } else {
                return { code: 500, msg: '未连接智能硬件云平台，请重试...' }
            }
        }
    },
    beforeDestroy() {
        this.mqtt && this.mqtt.close()
    }
}
</script>
