import Light from '@/libs/light/index'
import { filterLightCom } from '@/libs/filterGh'
export default {
    data() {
        return {
            // 是否开启补光灯
            isOpenLight: false,
            // 补光灯对象
            lightObj: null,
            // DC锁板需要配置锁口
            lightCom: [],
            isDestroy: false // 是否销毁
        }
    },
    created() {
        this.lightCom = filterLightCom(this.serverConfig.lockPlateStartIndex)
    },
    methods: {
        handleInitLight(callback) {
            this.lightObj = new Light()
            this.lightObj.on('open', (data) => {
                if (this.isDestroy) {
                    this.destroyLight()
                    return
                }
                if (data.code == 0) {
                    callback && callback()
                } else {
                    this.ModalInfo('确认', 'tipsIcon2', data.msg)
                    this.destroyLight()
                }
            })
            this.lightObj.on('message', (data) => {
                if (this.isDestroy) {
                    this.destroyLight()
                    return
                }
                if (data.code == 2) {
                    this.isOpenLight = !this.isOpenLight
                } else {
                    this.ModalInfo('确认', 'tipsIcon2', `${this.isOpenLight ? '关闭' : '开启'}补光灯失败`)
                }
            })
            this.lightObj.on('error', (data) => {
                if (this.isDestroy) {
                    this.destroyLight()
                    return
                }
                this.ModalInfo('确认', 'tipsIcon2', data.msg)
                if (data.code == 1) {
                    this.destroyLight()
                }
            })
            this.lightObj.init()
        },
        destroyLight() {
            this.lightObj && this.lightObj.destroyed()
            this.lightObj = null
        },
        handleLight() {
            if (this.lightObj) {
                if (this.serverConfig.lightType == 3) {
                    this.lightCom.forEach((com) => {
                        this.lightObj.handle(this.isOpenLight ? 'closelight' : 'openlight', com[0], com[1])
                    })
                } else {
                    this.lightObj.handle(this.isOpenLight ? 'closelight' : 'openlight')
                }
            } else {
                this.handleInitLight(this.handleLight)
            }
        }
    },
    beforeDestroy() {
        if (this.isOpenLight) {
            this.isDestroy = true
            if (this.serverConfig.lightType == 3) {
                this.lightCom.forEach((com) => {
                    this.lightObj.handle('closelight', com[0], com[1])
                })
            } else {
                this.lightObj.handle('closelight')
            }
        } else {
            this.destroyLight()
        }
    }
}
