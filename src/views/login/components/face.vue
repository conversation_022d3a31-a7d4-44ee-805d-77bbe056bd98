<template>
    <div class="faceCollectWrap">
        <div class="switchBox">
            <i :class="isOpenLight ? 'switchBtn_open' : 'switchBtn_close'" @click="handleLight"></i>
            <p>{{ isOpenLight ? '关闭补光灯' : '开启补光灯' }}</p>
        </div>
        <!-- <div class="closeBtnBox"><span class="closeBtnText">自动退出({{ countdown }}秒)</span></div> -->
        <div class="faceIdentifying">
            <div class="Identifying"></div>
            <canvas id="canvasCamera" class="canvasStyle" style="display: none;" width="550" height="450"></canvas>
            <video id="videoBox" ref="faceVideo" autoplay playsinline class="video-style" :style="`transform: rotateZ(${videoConfig.rotate}deg) rotateY(${videoConfig.horizontal == 'Y' ? 180 : 0}deg)`"></video>
            <img v-show="isSnap" id="faceImage" alt="" class="imageStyle" />
        </div>
        <h3 class="IdentifyingTitle driveSB">{{ tipText }}</h3>
        <!-- <div class="loginTips">
			<div class="tipsItem3 tipsItem">
				<div id="cardToId" class="loginByID" @click="handleSwitchLogin">账号密码认证</div>
			</div>
		</div> -->
        <div class="switch-warp">
            <p class="tip">认证出现问题？试试其他认证方式</p>
            <i></i>
            <span class="switch-btn" @click="handleSwitchLogin"></span>
            <span class="switch-btn-text">账号密码认证</span>
        </div>
        <div class="operateBox">
            <div class="buttonStyle3" @click="loginOut">退出认证（{{ countdown }}s）</div>
        </div>
    </div>
</template>

<script>
import commonServ from '@/libs/common_serv.js'
import store from '@/stroe'
import light from './mixin/light'
import { setNewToken } from '@/libs/utils'
export default {
    name: 'FaceCollect',
    mixins: [light],
    props: {
        // 退出路由
        fromPath: {
            type: String,
            default: ''
        },
        // 认证后的去处路由
        toPath: {
            type: String,
            default: ''
        },
        handleGetRules: {
            type: Function,
            default: () => {}
        }
    },
    data() {
        return {
            modal: false,
            // 认证倒计时
            countdown: 120,
            // 倒计时的定时器对象
            timer: null,
            downCount: 3,
            countdownTime: null,
            // 视频流
            currentStream: null,
            videoConfig: {
                horizontal: '',
                rotate: 0
            },
            videoStyle: {
                width: 460,
                height: 460
            },
            tipText: '',
            isSnap: false,
            systemConfig: {}
        }
    },
    mounted() {
        this.systemConfig = store.state.config.terminalConfig
        this.countdown = this.serverConfig.loginOutTime || 120
        this.videoConfig.horizontal = this.systemConfig.horizontal || 'X'
        this.videoConfig.rotate = this.systemConfig.rotate || 0
        this.countdownEvent()
        this.openConnect()
        this.handleInitLight(() => {
            // 根据配置初始化是否打开补光灯
            if (this.serverConfig.openFillLight == '0') return
            this.handleLight()
        })
    },
    beforeDestroy() {
        this.StopPreview()
        this.timer && clearInterval(this.timer)
        this.timer = null
    },
    methods: {
        // 倒计时
        countdownEvent() {
            this.timer = setInterval(() => {
                this.countdown--
                if (this.countdown == 0) {
                    clearInterval(this.timer)
                    this.countdown = 0
                    this.$router.push(this.fromPath)
                }
            }, 1000)
        },
        openConnect() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
                console.log('menumerateDevices is not supported!')
                return
            }
            navigator.mediaDevices
                .enumerateDevices()
                .then(this.gotDevices)
                .catch(this.handleError)
        },
        // 释放摄像头
        stopVideo() {
            if (this.countdownTime) {
                clearInterval(this.countdownTime)
                this.countdownTime = null
            }
            this.currentStream &&
				this.currentStream.getTracks().forEach(function(track) {
				    track.stop()
				})
            this.currentStream = null
        },
        // 暂停流
        pauseVideo() {
            this.countdownTime && clearInterval(this.countdownTime)
            this.countdownTime = null
            this.currentStream && this.$refs.faceVideo.pause()
        },
        // 重新播放
        playVideo(msg) {
            this.currentStream && this.$refs.faceVideo.play()
            this.getPicAndSave(msg)
        },
        // 遍历所有的设备，包括视频和音频设备,找到摄像头
        gotDevices(deviceInfos) {
            const videoSource = []
            const cameraName = this.systemConfig.faceCamera || ''
            deviceInfos.forEach((deviceInfo) => {
                if (deviceInfo.kind === 'videoinput' && (!cameraName || (cameraName && deviceInfo.deviceId.indexOf(cameraName) !== -1))) {
                    const option = {
                        text: deviceInfo.label,
                        value: deviceInfo.deviceId
                    }
                    videoSource.push(option)
                }
            })
            if (videoSource.length > 0) {
                this.play(videoSource[0].value)
            } else {
                this.handleError('没有摄像头')
            }
        },
        // 获取摄像头进行播放摄像头
        play(deviceId) {
            const constraints = {
                video: {
                    height: 480,
                    width: 665,
                    // frameRate: 15,
                    // facingMode: 'enviroment',
                    deviceId: deviceId ? { exact: deviceId } : undefined
                },
                audio: false
            }
            navigator.mediaDevices
                .getUserMedia(constraints)
                .then(this.gotMediaStream)
                .catch(this.handleError)
        },
        // 错误输出
        handleError(err) {
            this.tipText = '无法获取摄像头'
            console.log(err)
        },
        // 异常处理
        handleFaceError(err, retry) {
            if (this.countdownTime) {
                clearInterval(this.countdownTime)
                this.countdownTime = null
            }
            console.log(err, retry)
            this.faceErrorEvent('没有检测到人脸')
        },
        // 播放
        gotMediaStream(stream) {
            if (this.$refs.faceVideo) {
                this.$refs.faceVideo.srcObject = stream
                this.currentStream = stream
            }
            this.getPicAndSave()
        },
        // 拍照
        getPicAndSave(msg) {
            this.downCount = this.$options.data().downCount
            this.countdownTime && clearInterval(this.countdownTime)
            this.countdownTime = null
            this.countdownTime = setInterval(() => {
                this.downCount--
                if (msg) {
                    const _msg = msg.replace('。', '')
                    this.tipText = _msg + '，' + this.downCount + '秒后重试'
                }
                if (this.downCount <= 0) {
                    this.downCount = 0
                    clearInterval(this.countdownTime)
                    this.countdownTime = null
                    this.tipText = ''
                    this.snapPhoto()
                }
            }, 1000)
        },
        snapPhoto() {
            try {
                const canvas = commonServ.handleCameraCof(
                    {
                        height: this.videoStyle.height,
                        width: this.videoStyle.width
                    },
                    this.videoConfig,
                    'canvasCamera',
                    'videoBox'
                )
                const base64Code = canvas.toDataURL('image/jpeg')
                const photoshopMode = this.systemConfig?.photoshopMode || -1
                this.pauseVideo()
                this.tipText = '正在认证身份，请稍后...'
                if (photoshopMode != -1) {
                    // 调C++SDK库，进行人脸抠图
                    this.photoshop(base64Code)
                } else {
                    const faceImage = document.getElementById('faceImage')
                    faceImage.src = base64Code
                    this.isSnap = true
                    const base64 = base64Code.replace('data:image/jpeg;base64,', '')
                    this.handleFaceLogin(base64)
                }
            } catch (error) {
                console.log(error)
                this.StopPreview()
            }
        },
        // 抠图
        photoshop(base64Code) {
            try {
                const ipcRenderer = window.electronAPI.ipcRenderer
                const faceResult = ipcRenderer.sendSync('face_recognition_path', { base64: base64Code, width: this.videoStyle.width, height: this.videoStyle.height })
                if (faceResult) {
                    // 处理后的绘制点
                    const afterDrawX = faceResult.x - faceResult.width / 2
                    const afterDrawY = faceResult.y - faceResult.height / 2
                    // 最终实际的绘制点
                    const drawX = afterDrawX <= 0 ? 0 : afterDrawX
                    const drawY = afterDrawY <= 0 ? 0 : afterDrawY
                    // 最终实际的区域大小
                    const drawWidth = faceResult.width * 2
                    const drawHeight = faceResult.height * 2
                    const canvas = document.createElement('canvas')
                    canvas.width = drawWidth
                    canvas.height = drawHeight
                    canvas.getContext('2d').drawImage(document.getElementById('canvasCamera'), drawX, drawY, drawWidth, drawHeight, 0, 0, canvas.width, canvas.height)
                    const faceImage = document.getElementById('faceImage')
                    faceImage.src = canvas.toDataURL('image/jpeg')
                    this.isSnap = true
                    const base64 = faceImage.src.replace('data:image/jpeg;base64,', '')
                    this.handleFaceLogin(base64)
                } else {
                    const tt = setTimeout(() => {
                        clearTimeout(tt)
                        this.handleFaceError()
                    }, 2 * 1000)
                }
            } catch (e) {
                this.faceErrorEvent('人脸检测异常')
            }
        },
        // 统筹保管员|暂存模式下的登录入口
        handleFaceLogin(base64) {
            const loginMap = {
                0: this.faceAction_bg,
                1: this.faceAction
            }
            // 先判断物管柜配置使用的模式
            const patternTypeOfCabinet = this.serverConfig.patternType
            if (loginMap[patternTypeOfCabinet]) {
                loginMap[patternTypeOfCabinet](base64)
            } else {
                // 因5033版本兼容5011的保管服务，物管柜配置是没有返回patternType的，所以再从本地终端配置获取
                const patternTypeOfTerminal = this.systemConfig.patternType
                loginMap[patternTypeOfTerminal](base64)
            }
        },
        // 暂存模式（bsp）登录接口
        faceAction(photo) {
            this.tipType = 0
            const params = {
                grant_type: 'password',
                password: '',
                client_id: 'user_client',
                client_secret: 'user_client',
                auth_type: 'face',
                scope: 'trust',
                app_mark: 'sacw',
                faceCode: this.systemConfig.bspFaceLoginCode,
                faceImage: photo
            }
            this.$Post(this.BspApi.login_url, params).then(
                (resp) => {
                    console.log('resp', resp)
                    if (resp.code == 400) {
                        this.faceErrorEvent(resp.msg)
                        return
                    }
                    window.frist_in = 0
                    this.$store.commit('setUser', resp)
                    setNewToken(resp.access_token)
                    this.$router.push(this.toPath)
                },
                () => {
                    this.faceErrorEvent('人脸服务异常')
                }
            )
        },
        // 保管模式登录接口
        faceAction_bg(photo) {
            this.tipType = 0
            // this.showMsgBoxText = '识别中，请稍后'
            var params = {
                centerId: this.serverConfig.centerId,
                faceImg: photo
            }
            this.$Post(this.FaceApi.BUS_FACE, params).then(
                (resp) => {
                    if (resp.success) {
                        if (resp.compare && resp.data) {
                            this.headlFaceData(resp.data)
                        } else if (resp.compare && !resp.data) {
                            this.faceErrorEvent('认证成功，未识别到人员')
                        } else {
                            // 比对失败
                            this.faceErrorEvent('认证失败')
                        }
                    } else {
                        this.faceErrorEvent('服务器认证失败')
                    }
                },
                () => {
                    this.faceErrorEvent('人脸服务异常')
                }
            )
        },
        /**
		 * 人脸识别到的数据处理
		 * @param {object} data shuj
		 * @param {string} data.idCard
		 * @param {string} data.name
		 * @param {string} data.loginId
		 * @param {string} data.orgCode
		 * @param {string} data.orgName
		 * @param {string} data.centerId
		 * @param {string} data.isAdmin
		 */
        headlFaceData(data) {
            const params3 = {
                centerId: this.serverConfig.centerId,
                type: '03',
                idCard: data.idCard
            }
            this.$Post(this.PymApi.getAdminInfo, params3).then((res) => {
                if (res.success) {
                    localStorage.setItem('userInfo', JSON.stringify(res.admin))
                    this.$store.commit('setUser', res.admin)
                    window.frist_in = 0
                    this.$router.push(this.toPath)
                } else {
                    this.faceErrorEvent(res.msg)
                }
            })
        },
        faceErrorEvent(tipVal) {
            // 人脸失败处理方
            this.isSnap = false
            this.playVideo(tipVal)
        },
        // //关闭摄像头
        StopPreview() {
            this.currentStream && this.currentStream.getTracks()[0].stop()
            this.countdownTime && clearInterval(this.countdownTime)
            this.countdownTime = null
        },
        // 账号登录
        handleSwitchLogin() {
            this.pauseVideo()
            this.StopPreview()
            this.timer && clearInterval(this.timer)
            this.timer = null
            this.$emit('switchLogin', 'account')
        },
        loginOut() {
            // 退出注册
            this.$router.push(this.fromPath)
        }
    }
}
</script>

<style lang="css">
.AuthenticateNow {
	width: 798px;
	height: 120px;
	line-height: 120px;
	text-align: center;
	background: #337eff;
	border-radius: 16px;
	box-shadow: 0px 12px 20px 0px rgba(51, 126, 255, 0.3);
	color: #fff;
	font-size: 48px;
	margin: 0 auto 50px;
}
</style>
<style lang="less" scoped>
@assets: '~@/assets/images';
.faceCollectWrap {
	width: 100%;
	height: 100%;
	background: rgba(24, 22, 67, 0.9);
	position: fixed;
	top: 0;
	left: 0;
	overflow: auto;
}
.video-style {
	float: left;
	object-fit: cover;
	width: 550px;
	height: 450px;
	&.horizontal {
		transform: rotateY(180deg);
	}
	&.rotate {
		transform: rotate(270deg);
	}
	&.horizontal.rotate {
		transform: rotate(270deg) rotateX(180deg);
	}
}
.closeBtnBox {
	width: 330px;
	border-radius: 8px;
	border: solid 2px #4f5eff;
	color: #ffffff;
	text-align: center;
	line-height: 96px;
	position: absolute;
	right: 50px;
	top: 50px;
	height: 96px;
	text-align: center;
}
.closeBtnText {
	font-size: 36px;
	color: #fff;
}

.operateBox {
	margin-top: 85px;
	margin-bottom: 100px;
}
.tipsItem {
	display: flex;
	justify-content: center;
	align-items: center;
}
.tipsItem3 {
	.loginByID {
		width: 400px;
		height: 96px;
		background-color: #252357;
		border-radius: 48px;
		margin: auto;
		margin-top: 100px;
		color: #669eff;
		font-size: 36px;
		text-align: center;
		line-height: 96px;
	}

	.loginType {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin: 0 102px;
		.fingerprint {
			display: inline-block;
			width: 128px;
			height: 128px;
			background: url('@{assets}/fingerIcon.png') no-repeat;
			background-size: 100% 100%;
		}
		.accountNumber {
			display: inline-block;
			width: 128px;
			height: 128px;
			background: url('@{assets}/accountIcon.png') no-repeat;
			background-size: 100% 100%;
		}
		span {
			display: inline-block;
			width: 100%;
			font-size: 28px;
			text-align: center;
			color: #669eff;
			line-height: 40px;
			margin-top: 19px;
		}
	}
}
.switch-warp {
	display: flex;
	flex-direction: column;
	align-items: center;
	.tip {
		color: #669eff;
		font-size: 28px;
		margin-top: 44px;
		line-height: 1;
	}
	i {
		display: inline-block;
		width: 32px;
		height: 30px;
		margin-top: 18px;
		background-size: 100% 100%;
		background-image: url('@{assets}/temp/arrow-down.png');
	}
	.switch-btn {
		display: inline-block;
		width: 128px;
		height: 128px;
		background-size: 100% 100%;
		background-image: url('@{assets}/temp/switch-account-icon.png');
		margin-top: 25px;
	}
	.switch-btn-text {
		font-size: 28px;
		color: #669eff;
		margin-top: 15px;
	}
}
</style>
