<template>
    <div class="login-warp">
        <Face v-if="type === 'face'" :from-path="fromPath" :to-path="toPath" :handle-get-rules="handleGetRules" @switchLogin="handleSwitchLogin"></Face>
        <Account v-else :from-path="fromPath" :to-path="toPath" :handle-get-rules="handleGetRules" @switchLogin="handleSwitchLogin"></Account>
    </div>
</template>

<script>
import Face from './components/face.vue'
import Account from './components/account.vue'
export default {
    components: {
        Face,
        Account
    },
    data() {
        return {
            type: 'face',
            fromPath: '',
            toPath: ''
        }
    },
    created() {
        const query = this.$route.query
        this.fromPath = query.fromPath || '/home'
        this.toPath = query.toPath || '/'
    },
    methods: {
        handleSwitchLogin(type) {
            this.type = type
        },
        // 获取bsp Rule
        handleGetRules() {
            if (this.$store.getters['getTerminalConfig'].serverVersion == 5) {
                const { access_token, roleIds } = this.$store.state.user.userInfo
                Object.values(window.webConfig.RULES).map(async(ruleName) => {
                    const params = {
                        access_token,
                        roleIds,
                        mark: ruleName,
                        centerId: this.serverConfig.centerId
                    }
                    await this.$Post_SH(this.BspApi.get_rule_sql, params).then((res) => {
                        if (res.success) {
                            localStorage.setItem(ruleName, res.data)
                        }
                    })
                })
            }
        }
    }
}
</script>

<style lang="less" scoped>
.login-warp {
	width: 100%;
}
</style>
