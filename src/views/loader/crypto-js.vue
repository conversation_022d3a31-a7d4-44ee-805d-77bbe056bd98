<template>
	<div class="warp">
		<div>
			<input v-model="jm" placeholder="请输入明文" type="text" />
		</div>
		<br />
		<button @click="handle">加密</button>
		<br />
		<p>{{ jmh }}</p>
	</div>
</template>

<script>
import cryptoObj from '@/assets/js/crypto.js'
export default {
	data() {
		return {
			jm: '',
			jmh: ''
		}
	},
	methods: {
		handle() {
			if (!this.jm) return
			const jmh = cryptoObj.encrypt(this.jm)
			this.jmh = jmh
		}
	}
}
</script>

<style>
.warp {
	height: 50vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
</style>
