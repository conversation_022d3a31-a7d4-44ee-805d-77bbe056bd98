<template>
	<div id="systemWarp" class="loader">
		<p v-if="config.copyright" class="copyright">{{ config.copyright }}</p>
	</div>
</template>

<script>
export default {
	name: 'Loader',
	computed: {
		config() {
			return this.$store.getters['getTerminalConfig'] || {}
		}
	},
	mounted() {
		const ipcRenderer = window.electronAPI?.ipcRenderer || null
		if (!ipcRenderer) {
			return
		}
		setTimeout(() => {
			ipcRenderer.send('close-loader')
		}, 5000)
	}
}
</script>

<style lang="less" scoped>
.loader {
	width: 100%;
	height: 100%;
	background: url('~@/assets/images/loader/loadPage.png') no-repeat;
	background-size: 100% 100%;
	position: relative;
	.title {
		font-size: 36px;
		color: #fff;
		font-weight: bold;
		line-height: 42px;
		position: absolute;
		top: 186px;
		left: 41px;
	}
	.copyright {
		width: 100%;
		color: #b2c4ff;
		text-align: center;
		font-size: 12px;
		position: absolute;
		bottom: 19px;
		left: 0;
	}
}
</style>
