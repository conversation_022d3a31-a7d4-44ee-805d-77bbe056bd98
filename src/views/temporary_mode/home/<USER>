<template>
	<div id="home">
		<div class="home-tool">
			<Step v-if="isLogin" ref="step" :is-home="true"></Step>
		</div>
		<div class="TopBox">
			<div class="selectionType">
				<div class="vertical">
					<div @click="putInAction"></div>
					<div @click="putOutAction"></div>
				</div>
			</div>
		</div>
		<div class="count" @click="$router.push('/FileInventory')">
			<ul class="number">
				<li>{{ cabinetTotal.total }}</li>
				<li>{{ cabinetTotal.assign }}</li>
				<li>{{ cabinetTotal.free }}</li>
				<li>{{ cabinetTotal.fault }}</li>
			</ul>
			<ul class="title">
				<li>总量</li>
				<li>不可用</li>
				<li>空闲</li>
				<li>故障</li>
			</ul>
		</div>
		<p class="manual-title">操作说明</p>
		<div class="manual-warp">
			<div class="manual-cr-warp">
				<p>存物步骤</p>
				<div>
					<i>1</i>
					登录后选择要存入的物品
				</div>
				<div>
					<i>2</i>
					拍摄入库照片
				</div>
				<div>
					<i>3</i>
					选择取货人
				</div>
				<div>
					<i>4</i>
					选择柜格开柜，存入物品
				</div>
			</div>
			<div class="manual-qc-warp">
				<p>取物步骤</p>
				<div>
					<i>1</i>
					登录后输入取件码取出物品
				</div>
				<div>
					<i>2</i>
					开柜取出物品
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { setToken, removeNewToken } from '@/libs/utils.js'
import Step from '_c/step'
export default {
	components: {
		Step
	},
	data() {
		return {
			cabinetTotal: {
				total: '',
				fault: '',
				free: '',
				assign: ''
			},
			cabinetDetail: [],
			lockObj: null,
			intervalTime: null
		}
	},
	computed: {
		isLogin() {
			return this.$store.getters.isLogin
		}
	},
	created() {
		this.getClientToken()
	},
	mounted() {
		localStorage.removeItem('facereturn')
		this.$store.commit('removeWplist')
		this.$store.commit('removePolice')
		this.$store.commit('removeCabinet')
		this.$store.commit('removeCabinetOfWp')
		this.$store.commit('removeProcessKssj')
		this.intervalTime = setInterval(() => {
			this.getCabinetBox()
		}, 30 * 1000)
	},
	beforeDestroy() {
		clearInterval(this.intervalTime)
		this.intervalTime = null
	},
	methods: {
		refresh() {
			this.$store.commit('removeUser')
			removeNewToken()
			setTimeout(() => {
				location.reload()
			}, 500)
		},
		getClientToken() {
			const params = {
				grant_type: 'client_credentials',
				client_id: this.serverConfig?.clientName || '',
				client_secret: this.serverConfig?.clientPwd || ''
			}
			this.$http.bsp.getOauthToken(params).then((res) => {
				if (res) {
					setToken(res.access_token)
					this.getCabinetBox()
				}
			})
		},
		getCabinetBox() {
			const params = {
				cabinetCode: this.serverConfig.cabinetCode,
				centerId: this.serverConfig.centerId,
				searchObj: '{}',
				deviceType: ''
			}
			this.$Post(this.PymApi.getStatisticsInfo, params).then((res) => {
				res.success && this.dealData(res)
			})
		},
		dealData(res) {
			this.cabinetTotal.total = res.total || 0
			this.cabinetTotal.fault = res.fault || 0 // 故障
			this.cabinetTotal.assign = res.used || 0 // 占用
			this.cabinetTotal.free = res.free || 0 // 空闲
		},
		putInAction() {
			if (this.cabinetTotal.free < 1) {
				this.$GxxPrompt.info({
					type: 'warning',
					showBtn: false,
					showCountdown: true,
					timeout: 5000,
					autoClose: true,
					message: '<h1 style="padding: 30px 0 50px;">当前物品柜已满，请稍后尝试或选择其他柜机。</h1>'
				})
				return
			}
			if (this.isLogin) {
				this.$router.push({ name: 'choose_wp' })
				return
			}
			this.$router.push({
				path: 'faceLogin',
				query: {
					fromPath: '/home',
					toPath: `/choose_wp`
				}
			})
		},
		putOutAction() {
			if (this.isLogin) {
				this.$router.push({ name: 'pickup_code' })
				return
			}
			this.$router.push({
				path: 'faceLogin',
				query: {
					fromPath: '/home',
					toPath: '/pickup_code'
				}
			})
		}
	}
}
</script>

<style lang="less" scoped>
@assets: '../../../assets/images';
.offline-warp {
	width: 600px;
	height: 400px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	.tipsIcon {
		display: inline-block;
		width: 100px;
		height: 100px;
		background-image: url(data:image/png;base64,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);
		background-size: 100% 100%;
		margin-bottom: 30px;
	}
	.tips {
		font-size: 32px;
		text-align: center;
		margin-bottom: 60px;
		color: #2863ca;
	}
	.tool {
		display: flex;
		justify-content: center;
		span.btn {
			display: inline-flex;
			width: 150px;
			height: 50px;
			font-size: 24px;
			border-radius: 4px;
			text-align: center;
			line-height: 50px;
			text-align: center;
			justify-content: center;
			background: #0099e6;
			color: #fff;
			cursor: pointer;
		}
	}
}
#home {
	display: flex;
	justify-content: space-between;
	flex-direction: column;
	.home-tool {
		height: 140px;
		display: flex;
		align-items: center;
		padding: 0 36px;
	}
	.selectionType {
		width: 100%;
		margin: 0 auto;

		h3 {
			width: 100%;
			font-size: 48px;
			font-weight: 700;
			text-align: center;
			color: #2e71e6;
			line-height: 36px;
			letter-spacing: 5px;
		}

		.vertical {
			display: flex;
			justify-content: space-between;
			padding: 0 80px;
			div {
				&:first-child {
					width: 420px;
					height: 520px;
					background: url('@{assets}/temp/home-qc-icon.png') no-repeat;
					background-size: 100% 100%;
				}
				&:nth-child(2) {
					width: 420px;
					height: 520px;
					background: url('@{assets}/temp/home-cr-icon.png') no-repeat;
					background-size: 100% 100%;
				}
			}
		}
	}
	.count {
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		ul.number {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
			margin: 80px auto 0;
			padding: 0 80px;
			li {
				width: 140px;
				height: 140px;
				border-radius: 50%;
				font-size: 48px;
				font-weight: bold;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			li:nth-child(1) {
				background-color: #ccdfff;
				color: #3377ff;
			}
			li:nth-child(2) {
				background-color: #dbdeff;
				color: #675cff;
			}
			li:nth-child(3) {
				background-color: #c9f5f3;
				color: #00d19f;
			}
			li:nth-child(4) {
				background-color: #fcdee8;
				color: #f84558;
			}
		}
		ul.title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
			margin: 10px auto 0;
			padding: 0 80px;
			li {
				width: 140px;
				font-weight: 400;
				font-size: 40px;
				color: #3a416e;
				text-align: center;
				line-height: 1.6;
			}
		}
	}
	.manual-title {
		width: 100%;
		margin: 70px auto 0;
		color: #5779b3;
		font-size: 40px;
		text-align: center;
		font-weight: bold;
	}
	.manual-warp {
		width: 100%;
		margin: 0 auto;
		display: flex;
		justify-content: space-between;
		padding: 40px 48px 0;
		> div {
			width: 460px;
			height: 436px;
			background-color: #e5efff;
			border-radius: 4px;
			padding: 32px;
			box-sizing: border-box;
			> p {
				color: #5779b3;
				font-size: 28px;
				font-weight: bold;
			}
			> div {
				display: flex;
				align-items: center;
				color: #3a416e;
				font-size: 24px;
				margin-top: 32px;
				i {
					width: 48px;
					height: 48px;
					background-color: #fff;
					border-radius: 50%;
					color: #337eff;
					font-size: 32px;
					font-weight: bold;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20px;
				}
			}
		}
	}
}
.open-dev-tool {
	background-color: #d9ebff;
	text-align: center;
	width: 80px;
	height: 32px;
	line-height: 32px;
	margin: 0px auto;
	font-size: 20px;
	float: right;
	color: #0099e6;
	font-weight: 600;
	cursor: pointer;
	border-radius: 4px;
}
</style>
