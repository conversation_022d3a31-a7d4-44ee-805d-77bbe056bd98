<template>
    <div class="confrim-wp-warp">
        <Step :current-step="2" :is-take-out="true"></Step>
        <div class="confrim-wp-content">
            <p class="title">请确认取出物品信息</p>
            <div class="wp-warp">
                <wp-list v-if="wplist.length" :show-image="true" :list="wplist"></wp-list>
            </div>
            <div class="tool">
                <span class="prev" @click="handlePrev">上一步</span>
                <span class="next" @click="handleNext">确认开柜</span>
            </div>
        </div>
    </div>
</template>

<script>
import Step from '_c/step.vue'
import WpList from '_c/wp-list.vue'
export default {
    components: {
        Step,
        WpList
    },
    data() {
        return {
            wplist: [],
            cabinetList: []
        }
    },
    created() {
        this.wplist = this.$store.getters.getWplist
        console.log('this.wplist', this.wplist)
    },
    mounted() {
        this.handleGetCabinet()
    },
    methods: {
        handlePrev() {
            this.$router.go(-1)
        },
        handleNext() {
            this.$router.push({
                name: 'cabinet_put_out',
                query: {
                    sqdId: this.$route.query.sqdId,
                    cabinetList: this.cabinetList
                }
            })
        },
        // 获取物品柜信息（柜号，sideId）
        handleGetCabinet() {
            this.cabinetList = []
            this.wplist.forEach((wp) => {
                const isHad = this.cabinetList.find((item) => item.gh == wp.gh)
                !isHad && this.cabinetList.push({
                    gh: wp.gh,
                    sideId: wp.sideId,
                    cabineCode: wp.cabineCode
                })
            })
        }
    }
}
</script>

<style lang="less" scoped>
.confrim-wp-warp {
	width: 100%;
	margin: 0 auto;
	.confrim-wp-content {
		.title {
			color: #3a416e;
			font-size: 48px;
			font-weight: bold;
			line-height: 1;
			padding: 30px 40px 0;
		}
		.wp-warp {
			padding: 40px 10px 0;
			height: 1050px;
			overflow-y: auto;
		}
		.tool {
			display: flex;
			height: 112px;
			justify-content: center;
			span {
				display: inline-flex;
				width: 320px;
				height: 96px;
				border-radius: 8px;
				font-size: 36px;
				align-items: center;
				justify-content: center;
				margin: 0 60px;
				cursor: pointer;
				&.prev {
					border: 3px solid #337eff;
					color: #337eff;
					background-color: #fff;
				}
				&.next {
					background-color: #337eff;
					color: #fff;
				}
			}
		}
	}
}
</style>
