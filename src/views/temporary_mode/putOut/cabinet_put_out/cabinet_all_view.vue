<template>
    <div ref="cabinetMain" class="cabinet-main" :style="{ 'justify-content': cabinetArr.length > 2 ? 'space-between' : 'center' }">
        <div v-for="item in cabinetArr" :key="item.sideCode" class="cabinet">
            <div class="title">{{ item.sideCode == 'A' ? '主柜' : item.sideCode + '柜' }}</div>
            <div class="cabinet-body">
                <div v-for="(item1, index) in item.positionArr" :key="index + 'col'" class="col" :style="{ width: 100 / item.positionArr.length + '%' }">
                    <div v-for="(item2, index2) in item1.row" :key="index2 + 'row'" :class="{ row: true, screen: item2.num === '000', active: item2.open, activeAnimation: currentCabinetNum == item2.num }" :style="{ flex: ' 1 ' + ' 0 ' + item2.flexBasis * 100 + '%', border: item1.row.length == 1 ? 'none' : '4px solid #ebf5ff;'}">
                        <div :class="{ active: item2.open, activeAnimation: currentCabinetNum == item2.num }">{{ item2.open ? item2.num : '' }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { getToken } from '@/libs/utils'
export default {
    name: 'CabinetMain',
    props: {
        // 暂时没有使用
        currentCabinetNum: {
            require: true,
            type: String,
            default: ''
        },
        openedCabinetNum: {
            require: true,
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            cabinetArr: [],
            titleSpan: 0,
            cabinetDetail: []
        }
    },
    created() {
        this.getCabinetGh()
    },
    methods: {
        // 柜面滚动到第一个
        handlesCabinetSide() {
            // 复制-先后排序获取第一个开柜面
            const _openedCabinetNum = [...this.openedCabinetNum]
            _openedCabinetNum.sort()
            const cabinetSideCode = _openedCabinetNum[0] && _openedCabinetNum[0].charAt()
            console.log('cabinetSideCode', cabinetSideCode)
            if (cabinetSideCode && !['A', 'B'].includes(cabinetSideCode)) {
                const oneCabinetWidth = document.querySelectorAll('.cabinet')[0].offsetWidth
                this.cabinetArr.forEach((cabinet, i) => {
                    if (cabinet.sideCode == cabinetSideCode) {
                        this.$refs.cabinetMain.scrollTo(oneCabinetWidth * i, 0)
                    }
                })
            }
        },
        init() {
            this.cabinetArr = []
            this.cabinetDetail.map((item) => {
                const obj = {
                    sideCode: item.sideCode,
                    positionArr: []
                }
                obj.positionArr = this.dealCabinetDetail(item)
                this.cabinetArr.push(obj)
            })
            console.log(this.cabinetArr)
            setTimeout(this.handlesCabinetSide, 800)
        },
        dealCabinetDetail(data) {
            const colData = []
            data.data.map((item) => {
                this.openedCabinetNum.indexOf(item.num) > -1 ? (item.open = true) : (item.open = false)
                if (colData.indexOf(item.colPos) == -1) colData.push(item.colPos)
            })
            colData.sort((a, b) => a - b)
            const posData = []

            colData.map((col) => {
                const cabinetData = {
                    col
                }
                cabinetData.row = data.data.filter((item) => item.colPos == col)
                const totalMerges = cabinetData.row.reduce((newVal, item) => {
                    return Number(newVal) + Number(item.merge)
                }, 0)
                cabinetData.row.forEach((item) => {
                    item.flexBasis = item.merge / totalMerges
                })
                posData.push(cabinetData)
            })
            this.titleSpan = colData.length
            return posData
        },
        // 获取物品柜柜号
        getCabinetGh() {
            this.$Post(this.PymApi.getCabinetByCabinetCode, {
                access_token: getToken(),
                centerId: this.serverConfig.centerId,
                cabinetCode: this.serverConfig.cabinetCode
            }).then((resGh) => {
                if (!resGh.success || !resGh.data) {
                    return
                }
                this.$Post(this.PymApi.getSideByCabinetId, {
                    access_token: getToken(),
                    centerId: this.serverConfig.centerId,
                    cabinetId: resGh.data && resGh.data.id
                }).then((res) => {
                    if (res.success && res.data && res.data.length) {
                        this.cabinetSides = res.data
                        // 遍历请求柜子结构信息
                        const CabinetDetail = this.cabinetSides.map((sideData) => {
                            return this.getCabinetDetails(sideData.id)
                        })
                        Promise.all(CabinetDetail).then((res) => {
                            this.cabinetDetail = res.map((item, i) => {
                                item.sideCode = this.cabinetSides[i].sideCode
                                item.sideId = this.cabinetSides[i].id
                                return item
                            })
                            this.init()
                        })
                    }
                })
            })
        },
        // 获取物品柜结构信息
        getCabinetDetails(sideId) {
            if (!sideId) return false
            return new Promise((resolve, reject) => {
                const params = {
                    access_token: getToken(),
                    centerId: this.serverConfig.centerId,
                    sideId
                }
                this.$Post(this.PymApi.getCabinetDetail, params)
                    .then((res) => {
                        resolve(res)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        }
    }

}
</script>

<style lang="less" scoped>
@assets: '../../../../assets/images';
.cabinet-main {
	width: 840px;
	height: auto;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 0 auto 40px;
    overflow: auto;
	.cabinet {
		width: calc(50% - 16px);
        flex-shrink: 0;
		&:last-child {
			.cabinet-body {
				.col:last-child {
					margin-right: 0;
				}
			}
		}
		.title {
			width: 100%;
			height: 60px;
			background: #4d5f80;
			margin-bottom: 4px;
			color: #fff;
			font-size: 28px;
			line-height: 60px;
			text-align: center;
		}
		.cabinet-body {
			width: 100%;
			height: 712px;
			display: flex;
			justify-content: space-between;
			margin: 0 auto;
			.col {
				height: 100%;
				flex: 1;
				margin-right: 4px;
				display: flex;
				justify-content: space-between;
				flex-direction: column;
                line-height: 1;
				.row {
					flex: 1;
					background-color: #5c8ae6;
					// margin-bottom: 4px;
					border-bottom: 4px solid #ebf5ff;
					div {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;
						height: 100%;
						font-size: 32px;
						color: #fff;
						&.active {
							background-color: #e5ac00;
							animation: borderColor 1s infinite alternate;
						}
						// &.activeAnimation {
						//   animation: borderColor 1s infinite  alternate;
						// }
					}
					&.screen {
						display: flex;
						justify-content: center;
						align-items: flex-start;
						div {
							display: block;
							width: 100%;
							height: 50%;
							background: url('@{assets}/zg_icon.png') no-repeat;
							background-size: 100% 100%;
							background-position: 0 0;
							border-bottom: 2px solid #77b8e6;
						}

						background-color: #99d5ff;
					}
					// &.active{
					//   background-color: #E6AC00;
					//   animation: borderColor 1s infinite  alternate;
					// }
				}
			}
		}
	}
}

@keyframes borderColor {
	from {
		border: 4px solid #e6ac00;
	}

	to {
		border: 4px solid #cc8e0f;
	}
}
</style>
