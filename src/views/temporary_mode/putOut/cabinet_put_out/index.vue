<template>
    <div class="finish-warp">
        <Step ref="Step" :current-step="3" :is-take-out="true" :save="handleSubmitTakeOut" :stop-count-down="true" @backHome="handleBackSave"></Step>
        <div class="cabinet-deal-status">
            <div v-if="statusCode == 0" class="deal-status error">
                <i></i>
                <p class="title">柜门存在故障</p>
                <p class="tip">
                    <span>{{ failOpenGh.join(',') }}</span>
                    号柜子开柜失败
                </p>
            </div>
            <div v-if="statusCode == 1" class="deal-status opening">
                <i></i>
                <p class="title">柜门开启中</p>
                <p class="tip">
                    <span>{{ openedCabinetNum.join(',') }}</span>
                    号柜子开柜中，请稍候···
                </p>
            </div>
            <div v-if="statusCode == 2" class="deal-status opened">
                <i></i>
                <p class="title">柜门开启</p>
                <p class="tip">
                    <span>{{ openedCabinetNum.join(',') }}</span>
                    号柜子已开启，请将物品取出并
                    <span>关闭柜门</span>
                </p>
            </div>
            <div v-if="statusCode == 3" class="deal-status closed">
                <i></i>
                <p class="title">柜门已关闭</p>
                <p class="tip">
                    <span>{{ openedCabinetNum.join(',') }}</span>
                    号柜子已关闭，请确认所有物品已取出
                </p>
            </div>
        </div>
        <Cabinet :opened-cabinet-num="openedCabinetNum" @showCabinet="handleShowCabinet"></Cabinet>
        <p v-if="statusCode == 1" class="open-tip">正在开门中，请您留意！</p>

        <p v-if="statusCode == 2" class="open-tip">柜门未正常开启？你可以点击【异常处理】进行处置</p>

        <div v-if="[0, 2].includes(statusCode)" class="tool">
            <span class="fault-btn" @click="handleShowErrorModal">异常处理</span>
        </div>

        <p v-if="statusCode == 3" class="open-tip"></p>
        <div v-if="statusCode == 3 && isUpload" class="tool tool-closed">
            <span class="back-btn" @click="handleBackSave">返回首页({{ returnHomeTime + 'S'}})</span>
        </div>

        <!-- 错误提示 -->
        <Modal v-model="tipErrorModal" :closable="false" :mask-closable="false" footer-hide>
            <tip-error-modal v-if="tipErrorModal" :open-count="errorCount" @close="tipErrorModal = false" @keepOn="handleReopen" @error="handleError"></tip-error-modal>
        </Modal>
    </div>
</template>

<script>
import Step from '_c/step.vue'
import Cabinet from './cabinet_all_view'
import TipErrorModal from './tip_error_modal.vue'
import { voiceBroadcast, removeNewToken } from '@/libs/utils'
import { getGHEvent } from '@/libs/lock/util'
import Lock from '@/libs/lock/index'
import { modalConfirm, modalInfo } from '@/libs/modal'
export default {
    components: {
        Step,
        Cabinet,
        TipErrorModal
    },
    data() {
        return {
            loaded: false,
            isSaveSuccess: false,
            // 柜门状态 已关闭：3、开启中：1、已开启：2、失败：0
            statusCode: 1,
            errorCount: 0,
            tipErrorModal: false,
            drawer: false,
            retryTimes: 3,
            timer: null,
            comfrimTimer: null,
            order: 0,
            allCabinetStatus: {},
            failOpenGh: [],
            successOpenGh: [],
            network: true,
            isUpload: false,
            returnHomeTime: 120,
            returnHomeTimer: null // 返回首页定时器
        }
    },
    computed: {
        cabinetList() {
            return this.$route.query.cabinetList || []
        },
        openedCabinetNum() {
            const cabinetList = this.$route.query.cabinetList || []
            return cabinetList.map((cabinet) => cabinet.gh)
        }
    },
    watch: {
        // 检查状态已为关门状态，触发自动上传
        statusCode(code) {
            if (code != 3) return
            this.handleCountDownComfrim()
        }
    },
    beforeDestroy() {
        this.timer && clearInterval(this.timer)
        this.comfrimTimer && clearTimeout(this.comfrimTimer)
        voiceBroadcast(false)
        this.destroyedLock()
        this.returnHomeTimer && clearInterval(this.returnHomeTimer)
        this.returnHomeTimer = null
    },
    mounted() {
        this.returnHomeTime = this.serverConfig.loginOutTime || 120
        // 给所有柜子赋值对应开门状态
        this.cabinetList.forEach((cabinet) => {
            this.allCabinetStatus[cabinet.gh] = 1
        })
        this.$refs.Step && this.$refs.Step.handleClearAll()
        this.initLock(() => {
            this.handleOpenDoor()
        })
    },
    methods: {
        initLock(callback) {
            if (this.lockObj) {
                callback && callback()
                return
            }
            this.lockObj = new Lock()
            this.lockObj.on('open', (data) => {
                this.logger({ title: 'open-存入查锁记录-串口', value: data })
                if (data.code == 0) {
                    callback && callback()
                } else {
                    this.destroyedLock()
                    modalConfirm('重新连接', '返回首页', 'tipsIcon2', '连接锁控服务失败！', (flag) => {
                        if (!flag) {
                            this.returnHome()
                            return
                        }
                        this.initLock(callback)
                    })
                }
            })
            this.lockObj.on('error', (data) => {
                this.logger({ title: 'error-存入查锁记录-串口', value: data })
                modalConfirm('重新连接', '返回首页', 'tipsIcon2', data.code == 1 ? '连接锁控服务失败！' : data.msg, (flag) => {
                    if (!flag) {
                        this.returnHome()
                        return
                    }
                    this.initLock(callback)
                })
            })
            this.lockObj.on('message', (data) => {
                this.logger({ title: 'message-存入查锁记录-串口', value: data })
                const { result, actionType, box, door } = data
                const gh = getGHEvent(box, door)
                if (actionType == 'openlock') {
                    // result：1：门开启、0：门关闭
                    if (result == 1) {
                        this.allCabinetStatus[gh] = 2
                    }
                    if (this.allCabinetStatus[gh] != 2) {
                        this.retryTimes--
                    } else {
                        this.order++
                        this.retryTimes = 3
                    }
                    this.handleOpenDoor()
                }
                if (actionType == 'checklock') {
                    // result：1：门开启、0：门关闭
                    if (result == 0) {
                        this.allCabinetStatus[gh] = 3
                        // 开启返回首页倒计时
                        this.returnHomeTimer && clearInterval(this.returnHomeTimer)
                        this.returnHomeTimer = setInterval(() => {
                            if (this.returnHomeTime <= 0) {
                                this.returnHome()
                            } else {
                                this.returnHomeTime--
                            }
                        }, 1000)
                    }
                    this.order++
                    this.handleCheckLock()
                }
            })
            this.lockObj.init()
        },
        destroyedLock() {
            this.lockObj && this.lockObj.destroyed()
            this.lockObj = null
        },
        openLock(gh) {
            this.lockObj && this.lockObj.handle('openlock', gh)
        },
        checkLock(gh) {
            this.lockObj && this.lockObj.handle('checklock', gh)
        },
        handleShowCabinet() {
            this.loaded = true
        },
        // 柜格开始逐个开门
        handleOpenDoor() {
            const gh = this.openedCabinetNum[this.order]
            if (gh) {
                if (!this.retryTimes) {
                    this.allCabinetStatus[gh] = 0
                    this.order++
                    this.retryTimes = 3
                    this.handleOpenDoor()
                    return
                }
                this.openLock(gh)
            } else {
                let isAllOpen = true
                const failOpenGh = []
                const successOpenGh = []
                for (const key in this.allCabinetStatus) {
                    if (this.allCabinetStatus[key] == 0) {
                        isAllOpen = false
                        failOpenGh.push(key)
                    } else {
                        successOpenGh.push(key)
                    }
                }
                this.$set(this, 'failOpenGh', failOpenGh)
                this.$set(this, 'successOpenGh', successOpenGh)
                if (isAllOpen) {
                    this.statusCode = 2
                    this.retryTimes = 3
                    this.order = 0
                    this.voiceBroadcastSuccessOpen()
                    this.handleCheckLock()
                }
                if (!isAllOpen) {
                    this.statusCode = 0
                    this.retryTimes = 3
                    this.order = 0
                    this.handleShowErrorModal()
                }
            }
        },
        // 间隔2秒遍历查询柜子状态
        handleCheckLock() {
            const gh = this.openedCabinetNum[this.order]
            if (gh) {
                this.checkLock(gh)
            } else {
                let isAllClosed = true
                for (const key in this.allCabinetStatus) {
                    if (this.allCabinetStatus[key] != 3) {
                        isAllClosed = false
                    }
                }
                if (isAllClosed) {
                    this.isSaveSuccess = true
                    this.statusCode = 3
                }
                if (!isAllClosed) {
                    setTimeout(() => {
                        this.order = 0
                        this.handleCheckLock()
                    }, 2000)
                }
            }
        },
        // 开门成功
        voiceBroadcastSuccessOpen() {
            const tips = `${this.openedCabinetNum.join(',')}号柜门已开启，请取出物品，并关好柜门`
            voiceBroadcast(true, tips, 'putOutSuccess')
        },
        // 开门失败
        voiceBroadcastFailedOpen() {
            const tips = `开锁失败,请联系管理员`
            voiceBroadcast(true, tips, 'openLockFailed')
        },
        // 关门（存入成功）
        voiceBroadcastClosed() {
            const tips = `${this.openedCabinetNum.join(',')}号柜门已关闭，物品已取出，请您确认下一步操作`
            voiceBroadcast(true, tips, 'putOutClosed')
        },
        // 重新开柜
        handleReopen() {
            this.tipErrorModal = false
            this.statusCode = 1
            for (const key in this.allCabinetStatus) {
                this.allCabinetStatus[key] = 1
            }
            this.$refs.Step && this.$refs.Step.handleClearAll()
            this.initLock(() => {
                this.handleOpenDoor()
            })
        },
        // 故障上报回调
        async handleError() {
            // 确认是故障上报时，把状态置为0【error】
            this.statusCode = 0
            await this.handleFaultCabinet()
            await this.handleSubmitTakeOut()
            this.tipErrorModal = false
            this.$router.push({ name: 'home' })
        },
        async handleFaultCabinet() {
            const failUpload = this.failOpenGh.map((gh) => {
                const params = {
                    cabinetCode: this.serverConfig.cabinetCode,
                    centerId: this.serverConfig.centerId,
                    gh: gh,
                    gzyy: gh + '不能开柜'
                }
                this.$Post(this.PymApi.saveCabinetFault, params).then((res) => {
                    this.logger({ title: '取出-柜子故障', value: params })
                })
            })
            await Promise.all(failUpload)
        },
        handleShowErrorModal() {
            this.errorCount++
            this.tipErrorModal = true
        },
        async handleCountDownComfrim() {
            if (!this.network) return
            this.handleSubmitTakeOut(() => {
                this.$refs.Step && this.$refs.Step.handleActionCountDown()
            })
        },
        async handleBackSave() {
            if (this.statusCode != 0 && !this.isUpload) {
                try {
                    modalInfo('确定', 'tipsIcon2', '当前记录未保存！请稍等')
                } catch (e) {
                    console.log(e)
                }
                return
            }
            this.$router.push({ name: 'home' })
        },
        handleSubmitTakeOut(callback) {
            clearTimeout(this.comfrimTimer)
            if (this.statusCode != 3 && this.statusCode != 0) {
                modalInfo('确定', 'tipsIcon2', '当前操作未完成！请稍等')
                return
            }
            if (this.isUpload) {
                callback && callback()
                return
            }
            const allWpList = this.$store.getters.getWplist
            const successOpenWpList = []
            this.successOpenGh.map((gh) => {
                const wplist = allWpList.filter((wp) => wp.gh == gh)
                successOpenWpList.push(...wplist)
            })
            if (successOpenWpList && successOpenWpList.length) {
                this.$Post(this.PymApi.save_wpzcCk, {
                    sqdId: this.$route.query.sqdId,
                    centerId: this.serverConfig.centerId,
                    wpid: successOpenWpList.map((wp) => wp.wpId).join(','),
                    ckkssj: this.$store.getters.getCkkssj
                }).then((res) => {
                    if (res.success) {
                        this.isUpload = true
                        this.voiceBroadcastClosed()
                        callback && callback()
                    } else {
                        modalConfirm('返回首页', '重新上报', 'tipsIcon2', '数据保存失败，请联系管理员，谢谢！', (res) => {
                            if (res) {
                                this.returnHome()
                            } else {
                                this.handleSubmitTakeOut(callback)
                            }
                        })
                    }
                }).catch(() => {
                    modalConfirm('重试', '返回首页', 'tipsIcon2', '服务异常，请联系运维，谢谢！', (res) => {
                        if (res) {
                            this.handleSubmitTakeOut(callback)
                        } else {
                            this.returnHome()
                        }
                    })
                })
            } else {
                // 当前开柜失败时回调
                callback && callback()
            }
        },
        returnHome() {
            this.$store.commit('removeUser')
            removeNewToken()
            this.$router.push({ name: 'home' })
        }
    }
}
</script>

<style lang="less">
@assets: '~@/assets/images/temp';
.finish-warp {
	padding-bottom: 40px;
	.cabinet-deal-status {
		padding-bottom: 15px;
		.deal-status {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			i {
				display: inline-block;
				width: 120px;
				height: 120px;
				background-size: 100% 100%;
				background-image: url('@{assets}/opening-icon.png');
				margin-top: 32px;
			}
			.title {
				font-size: 36px;
				color: #33a0ff;
				font-weight: bold;
				line-height: 1;
				margin-top: 20px;
			}
			.tip {
				font-size: 40px;
				color: #5779b3;
				margin-top: 30px;
			}
			span {
				font-size: 40px;
				color: #3377ff;
				font-weight: bold;
			}
			&.opened {
				i {
					background-image: url('@{assets}/opened-icon.png');
				}
				.title {
					color: #56cd45;
				}
			}
			&.error {
				i {
					background-image: url('@{assets}/error-icon.png');
				}
				.title {
					color: #f31d68;
				}
				.title.success {
					color: #56cd45;
				}
			}
		}
	}
	.open-tip {
		font-size: 32px;
		color: #191e3b;
		text-align: center;
		line-height: 1;
		margin-top: 40px;
		&.closed-tip {
			height: 60px;
			width: 80%;
			margin: 40px auto 0;
			background-color: #fee3e6;
			border-radius: 8px;
			color: #f84558;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.tool {
		display: flex;
		justify-content: center;
		margin-top: 60px;
		.fault-btn {
			width: 264px;
			height: 64px;
			background: #f26b6b;
			border-radius: 32px;
			font-size: 24px;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
		}
		&.tool-closed {
			span {
				width: 320px;
				height: 96px;
				border-radius: 8px;
				font-size: 36px;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 60px;
				cursor: pointer;
			}
			.replay-btn {
				border: 2px solid #f84558;
				color: #f84558;
				background-color: #fff;
			}
			.back-btn {
				background-color: #337eff;
				color: #fff;
			}
		}
	}
}
</style>
