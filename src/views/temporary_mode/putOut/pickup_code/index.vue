<template>
    <div class="pickup-code-warp">
        <Step :current-step="1" :is-take-out="true"></Step>
        <div class="pickup-content">
            <p class="title">请输入取件码开柜取出物品</p>
            <div class="keyboard-number-warp">
                <div class="key-value-warp">
                    <input ref="keyValue" v-model="keyValue" :maxlength="4" type="text" placeholder="请输入取件码" />
                    <span class="btn" @click="handleNext">立即取物</span>
                    <p class="tip">{{ errorTxt }}</p>
                </div>
                <div class="keyword-warp">
                    <div class="row">
                        <span @click="keyup(1)">1</span>
                        <span @click="keyup(2)">2</span>
                        <span @click="keyup(3)">3</span>
                    </div>
                    <div class="row">
                        <span @click="keyup(4)">4</span>
                        <span @click="keyup(5)">5</span>
                        <span @click="keyup(6)">6</span>
                    </div>
                    <div class="row">
                        <span @click="keyup(7)">7</span>
                        <span @click="keyup(8)">8</span>
                        <span @click="keyup(9)">9</span>
                    </div>
                    <div class="row">
                        <span class="zero" @click="keyup(0)">0</span>
                        <span class="backspace" @click="keyup('del')">
                            <i></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="tool">
            <span class="cancel" @click="handleCancel">取消</span>
        </div>
    </div>
</template>

<script>
import Step from '_c/step.vue'
import { debounce } from '@/libs/utils'
import moment from 'moment'
const serverConfig = JSON.parse(localStorage.getItem('serverConfig') || '{}')
export default {
    components: {
        Step
    },
    data() {
        return {
            keyValue: '',
            errorTxt: ''
        }
    },
    watch: {
        keyValue() {
            this.errorTxt = ''
        }
    },
    mounted() {
        // 开始存入流程开始时间
        if (!this.$store.getters.getCkkssj) {
            const setCkkssj = moment().format('YYYY-MM-DD HH:mm:ss')
            console.log('开始取出流程开始时间', setCkkssj)
            this.$store.commit('setCkkssj', setCkkssj)
        }
    },
    methods: {
        handleNext() {
            if (!this.keyValue) {
                this.errorTxt = '请输入取件码！'
                return
            }
            if (this.keyValue.length < 4) {
                this.errorTxt = '请输入4位数字取件码！'
                return
            }
            this.errorTxt = ''
            this.$Post(this.PymApi.get_zcwp_by_take_code, {
                takeCode: this.keyValue,
                centerId: serverConfig.centerId,
                cabinetCode: serverConfig.cabinetCode
            }).then((res) => {
                if (!res.success) {
                    this.errorTxt = res.msg
                    return
                }
                if (!res.data || !res.data.length) {
                    this.errorTxt = '查询物品返回为空！'
                    return
                }
                this.errorTxt = ''
                this.$store.commit('setWplist', { wplist: res.data })
                this.$router.push({
                    name: 'confirm_wp',
                    query: {
                        sqdId: res.sqdId
                    }
                })
            })
        },
        keyup(value) {
            if (value != 'del') {
                this.keyValue = this.keyValue + String(value)
                return
            }
            if (!this.keyValue) return
            this.keyValue = this.keyValue.substring(0, this.keyValue.length - 1)
            debounce(() => {
                this.$refs[this.currentRef].focus()
            }, 500)
        },
        handleCancel() {
            this.$router.push({ name: 'home' })
        }
    }
}
</script>

<style lang="less" scoped>
@assets: '~@/assets/images/temp';
.pickup-code-warp {
	width: 100%;
	margin: 0 auto;
	.pickup-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		.title {
			color: #2e71e5;
			font-size: 48px;
			font-weight: bold;
			line-height: 1;
			padding: 60px 0;
		}
		.keyboard-number-warp {
			width: 920px;
			height: 916px;
			border-radius: 16px;
			background-color: #fff;
		}
	}
	.keyboard-number-warp {
		.key-value-warp {
			display: flex;
			justify-content: space-between;
			padding: 60px 60px 0;
			margin-bottom: 80px;
			position: relative;
			input {
				width: 520px;
				height: 112px;
				border: 3px solid #99beff;
				border-radius: 16px;
				font-size: 40px;
				padding-left: 40px;
				color: #333;
				&:focus {
					outline: none;
				}
				&:focus-visible {
					background-color: #fff;
				}
			}
			.btn {
				width: 240px;
				height: 112px;
				background-color: #337eff;
				border-radius: 16px;
				color: #ffffff;
				font-size: 40px;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
			}
			.tip {
				position: absolute;
				left: 60px;
				bottom: -40px;
				color: #fc3e51;
				font-size: 24px;
			}
		}
		.keyword-warp {
			display: flex;
			flex-direction: column;
			padding: 0 60px;
			.row {
				display: flex;
				justify-content: space-between;
				margin-bottom: 40px;
				span {
					display: flex;
					width: 240px;
					height: 120px;
					border-radius: 12px;
					border: 3px solid #99beff;
					align-items: center;
					justify-content: center;
					font-size: 72px;
					color: #739de5;
					cursor: pointer;
					&.zero {
						width: 520px;
					}
					&.backspace {
						display: flex;
						justify-content: center;
						align-items: center;
						i {
							display: inline-block;
							width: 66px;
							height: 40px;
							background-size: 100% 100%;
							background-image: url('@{assets}/backspace-icon.png');
						}
					}
					&:active {
						color: #fff;
						background-color: #669eff;
						border-color: #669eff;
					}
				}
			}
		}
	}
	.tool {
		display: flex;
		justify-content: center;
		padding: 84px 0 48px;
		.cancel {
			display: inline-flex;
			align-items: center;
			justify-content: center;
			width: 320px;
			height: 96px;
			border-radius: 8px;
			border: 3px solid #337eff;
			background-color: #fff;
			font-size: 36px;
			color: #337eff;
			cursor: pointer;
			&:active {
				transform: scale(0.98);
			}
		}
	}
}
</style>
