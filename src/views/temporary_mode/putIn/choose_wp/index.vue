<template>
    <div class="choose-wp-warp">
        <Step></Step>
        <div class="choose-wp-content">
            <div class="search-warp">
                <span class="tip">请扫描二维码/手动查询添加物品</span>
                <span class="search-btn" @click="handleShowSearch">手动查询</span>
            </div>
            <!-- <div>{{ value }}</div> -->
            <div class="wp-list-box">
                <wp-list ref="wpList" :list="selectedWp" :show-tool="true" @del="handleDel"></wp-list>
                <div v-if="!selectedWp.length" class="default"></div>
            </div>
            <!-- <div v-else class="default"></div> -->
        </div>
        <div class="deal-warp">
            <span class="cancel" @click="handleCancel">取消存入</span>
            <span class="next" :class="{ pass: selectedWp.length }" @click="handleNext">下一步</span>
        </div>

        <!-- 存入提示 -->
        <Modal v-model="tipModal" :closable="false" :mask-closable="false" footer-hide>
            <tip-modal v-if="tipModal" @close="tipModal = false"></tip-modal>
        </Modal>

        <!-- 手动查询 -->
        <Drawer v-model="drawer" :styles="{ padding: 0, overflow: 'inherit' }" placement="bottom" height="80" :closable="false" :mask-closable="false">
            <search-wp v-if="drawer" :selected-wp="selectedWp" @close="handleCloseDrawer" @confrim="handleChooseWp"></search-wp>
        </Drawer>
    </div>
</template>

<script>
import Step from '_c/step.vue'
import WpList from '_c/wp-list'
import SearchWp from './search-wp.vue'
import TipModal from './tip_modal.vue'
import scanCode from './scanCode.js'
import moment from 'moment'
export default {
    components: {
        WpList,
        Step,
        SearchWp,
        TipModal
    },
    mixins: [scanCode],
    data() {
        return {
            // selectedWp: [{ 'ajbh': 'L002', 'zhlxName': '', 'wplbXl': '100200', 'wply': '1', 'wplx': '1', 'wpmc': '消防斧-入库退回', 'id': 'F5701950FB0F4E68BF9BEA3FC9818B79', 'centerMsg': { 'id': 'DEFAULT_WG', 'isDel': '0', 'addUser': '超级管理员', 'addTime': '2020-05-28 17:56:25', 'updateUser': '1572063950164594688', 'updateTime': '2023-09-22 09:40:28', 'name': '默认物管中心', 'faceService': 'http://***********:8089', 'isYybq': '2', 'yybqService': '', 'isWsd': '1', 'wsdSocket': '321', 'isShowRoom': '0', 'isFace': '1', 'isCkFlag': '1', 'isJcFlag': '1', 'isPrintLabel': '0', 'isScanCode': '0', 'enableGosuncnDevice': '4', 'groupId': 'DEFAULT_WG', 'isOldZfpt': '0', 'isIntelligentWarehouse': '1', 'intelligentWarehouseFrom': '01', 'rfidReaderType': '3', 'isVertu': '-1', 'isYwsm': '0', 'isCrkImg': 0, 'newFaceServiceUrl': '', 'isJzWgzx': '2', 'isCzfkFlag': '0', 'authCode': 'GXX-YHZ2MQMU6T', 'isRkFlag': '1', 'isCf': '1' }, 'wplxName': '普通物品1', 'isPhone': false, 'wptz': '123123123', 'zbdwName': '广东省公安厅', 'czck': '0', 'jldwName': '升', 'czsw': '1', 'zbrXm': '超级管理员', 'zt': '1', 'ajxzCode': '02', 'clysName': '', 'zbrSfzh': '512501196512305186', 'isStoreInSmartHouse': '0', 'ajmc': '历史001', 'ztName': '已登记', 'zbmjXm': '超级管理员', 'centerId': 'DEFAULT_WG', 'jldw': '升', 'cyrSfzh': '', 'wplbDlName': '工具类', 'wplbMx': '100201', 'cyrXm': '', 'zbdw': '440000000000', 'addTime': '2023-09-20 14:10:05', 'addUser': '超级管理员', 'dqsl': '123', 'shslSum': 0, 'wplbDl': '100000', 'wplbMxName': '消防斧', 'zbmjSfz': '512501196512305186', 'wpxz': '99', 'wplyName': '接受证据', 'cpysName': '', 'unbind': false, 'zbmjOrgCode': '440000000000', 'wpxzName': '其他', 'wpsl': '123', 'ajxz': '刑事', 'orgCode': '440000000000', 'sfsm': '2', 'yhCodeName': '', 'cfbs': '0', 'wpjz': '0.00', 'wpztName': '待入库', 'aymc': '案由案由案由', 'wpzt': '22', 'wpbh': 'W4400000000002023090041', 'centerIdName': '默认物管中心', 'sfzk': '0', 'clppName': '', 'wplbXlName': '斧', 'czswName': '是', 'checked': true }],
            selectedWp: [],
            drawer: false,
            tipModal: false,
            loading: false,
            value: ''
        }
    },
    mounted() {
        // 开始存入流程开始时间
        if (!this.$store.getters.getRkkssj) {
            const setRkkssj = moment().format('YYYY-MM-DD HH:mm:ss')
            console.log('开始存入流程开始时间', setRkkssj)
            this.$store.commit('setRkkssj', setRkkssj)
        }
        if (!window.frist_in) {
            this.tipModal = true
            window.frist_in = 1
        }
        // store获取勾选物品
        const selectedWp = this.$store.getters.getWplist
        if (!selectedWp || !selectedWp.length) return
        this.selectedWp = [...selectedWp]
    },
    methods: {
        handleShowSearch() {
            this.unBindKeydown()
            this.drawer = true
        },
        handleCloseDrawer() {
            this.drawer = false
            this.smsb()
        },
        handleChooseWp(selectedWp) {
            this.drawer = false
            this.selectedWp = [...selectedWp]
            this.smsb()
        },
        handleCancel() {
            this.$router.push({ name: 'home' })
        },
        handleNext() {
            if (!this.selectedWp.length) return

            const _seletedWp = this.selectedWp.map((item) => {
                delete item.checked
                return item
            })
            this.$store.commit('setWplist', { wplist: _seletedWp })

            this.$router.push({ name: 'take_photo' })
        },
        handleDel(item) {
            const idx = this.selectedWp.findIndex((item2) => item2.id == item.id)
            this.selectedWp.splice(idx, 1)
        }
    }
}
</script>

<style lang="less" scoped>
@assets: '~@/assets/images/temp';
.choose-wp-warp {
	width: 100%;
	margin: 0 auto;
	.choose-wp-content {
		width: 984px;
		height: 1068px;
		background-color: #fff;
		border-radius: 8px;
		margin: 32px auto 0;
		display: flex;
		flex-direction: column;
		.search-warp {
			flex-shrink: 0;
			display: flex;
			justify-content: space-between;
			padding: 30px;
			box-sizing: border-box;
			align-items: center;
			.tip {
				color: #5779b3;
				font-size: 28px;
			}
			.search-btn {
				display: flex;
				width: 280px;
				height: 96px;
				border-radius: 16px;
				background-color: #669eff;
				color: #fff;
				font-size: 32px;
				align-items: center;
				justify-content: center;
				cursor: pointer;
			}
		}
	}
	.deal-warp {
		display: flex;
		justify-content: space-evenly;
		margin-top: 40px;
		padding-bottom: 100px;
		.cancel {
			width: 320px;
			height: 96px;
			border-radius: 8px;
			border: 2px solid #337eff;
			color: #337eff;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 36px;
			cursor: pointer;
		}
		.next {
			width: 320px;
			height: 96px;
			background-color: #9bbdfa;
			border-radius: 8px;
			font-size: 36px;
			color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			&.pass {
				background-color: #337eff;
			}
		}
	}

	.default {
		width: 100%;
		height: 100%;
		background-image: url('@{assets}/tip_no_rfid.png');
		background-size: 100% auto;
		background-repeat: no-repeat;
	}
	.wp-list-box {
		height: 100%;
		overflow-y: scroll;
	}
}
/deep/.ivu-drawer {
	top: auto;
	bottom: 0;
}
</style>
