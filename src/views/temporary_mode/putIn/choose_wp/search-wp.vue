<template>
    <div class="search-wp-warp">
        <span class="close" @click="handleClose"></span>
        <h1>请选择物品</h1>
        <div class="wp-search" @click="showKeyboard = true">
            <i></i>
            <div class="search-warp">
                <span v-show="showPlaceholder">请输入物品编号/名称</span>
                <input v-model="value" type="text" @keydown="debounce(handleSearch, 1000)" @focus="handleFocus" @blur="handleBlur" v-xKeyboard />
            </div>
        </div>
        <div ref="warp" class="wp-content">
            <div ref="content" class="content-box" v-gxx-infinite-scroll="handleScrollLoad" :gxx-infinite-scroll-disabled="disabledScroll" :gxx-infinite-scroll-distance="10">
                <wp-list ref="wplist" :list="wplist" :current-selected="selectedWpList2" :selected="selectedWplist" :selection="true" @select="handleSelected"></wp-list>
                <div v-if="endScroll" class="end" style="height: 100px;color: #B6BED0;font-size: 28px;display: flex;align-items: center;justify-content: center;">到底了~</div>
            </div>
        </div>
        <div class="tool">
            <div class="cancel" @click="handleClose">取消</div>
            <div class="confrim" @click="handleConfrim">确认</div>
        </div>
    </div>
</template>

<script>
import WpList from '_c/wp-list'
import { debounce } from '@/libs/utils.js'
export default {
    components: {
        WpList
    },
    props: {
        // 外部传入已选中物品
        selectedWp: {
            type: [Array],
            default: () => []
        }
    },
    data() {
        return {
            showPlaceholder: true,
            showKeyboard: false,
            value: '',
            form: {},
            page: {
                pageSize: 15,
                pageNo: 1
            },
            disabledScroll: true,
            endScroll: false,
            loading: false,
            wplist: [],
            // 传入组件已勾选物品
            selectedWplist: [],
            // 组建内接收到的勾选物品
            selectedWpList2: [],
            // 不存在搜索物品列表的物品
            noInListWp: []
        }
    },
    watch: {
        value(val) {
            this.showPlaceholder = !val
        }
    },
    mounted() {
        setTimeout(() => {
            if (this.$refs.content && this.$refs.warp) {
                this.$refs.content.style.height = this.$refs.warp.offsetHeight + 'px'
            }
        }, 300)
        this.noInListWp = [...this.selectedWp]
        this.page.pageNo = 1
        this.wplist = []
        this.handleDetWpData()
    },
    methods: {
        debounce: debounce,
        handleConfrim() {
            this.$refs.wplist.handleConfirm()
            // 合并不在搜索列表的物品和已勾选的物品回传
            this.$emit('confrim', [...this.noInListWp, ...this.selectedWpList2])
        },
        handleSelected(wplist) {
            this.selectedWpList2 = [...wplist]
            this.wplist.forEach((wp, i) => {
                this.$set(this.wplist[i], 'checked', false)
                this.selectedWpList2.forEach((wp2) => {
                    if (wp2.id == wp.id) {
                        this.$set(this.wplist[i], 'checked', true)
                    }
                })
            })
        },
        // 是否存在汉字
        isChinese(str) {
            const filter = /[\u4E00-\u9FA5\uF900-\uFA2D]{1,}/
            return filter.test(str)
        },
        // 条件查询，识别物品编号还是姓名查询
        handleSearch() {
            this.form.wpBhMc = this.value
            this.noInListWp = [...this.selectedWp]
            this.page.pageNo = 1
            this.wplist = []
            this.selectedWplist = []
            this.handleDetWpData()
        },
        // 滚动加载
        handleScrollLoad() {
            if (this.loading) return
            this.loading = true
            this.page.pageNo++
            this.handleDetWpData()
        },
        handleDetWpData() {
            const params = {
                wplx: 1,
                isFromZfpt: false,
                wpxzWithOut: 7,
                wpzts: '1,3,9,11,22',
                sfzk: 0,
                ruleSql: localStorage.getItem('sacw:mjwpglcx'),
                ruleId: 'sacw:mjwpglcx',
                centerId: this.serverConfig.centerId
            }
            this.$Post(this.PymApi.get_wpxx_page_data, Object.assign(params, this.page, this.form)).then((res) => {
                if (!res.success) return
                if (!res.rows) {
                    res.rows = []
                }
                // 勾选已选中物品| 筛选出不在勾选列表物品
                const noInListWp = []
                this.noInListWp.forEach((item) => {
                    const idx = res.rows.findIndex((item2) => item2.id == item.id)
                    if (idx != -1) {
                        this.$set(res.rows[idx], 'checked', true)
                        this.selectedWplist.push(res.rows[idx])
                        this.selectedWpList2 = []
                    } else {
                        noInListWp.push(item)
                    }
                })
                this.noInListWp = [...noInListWp]
                this.wplist.push(...(res.rows || []))
                this.wplist.forEach((wp, i) => {
                    this.selectedWpList2.forEach((wp2) => {
                        if (wp2.id == wp.id) {
                            this.$set(this.wplist[i], 'checked', true)
                        }
                    })
                })

                // 物品加载完禁止滚动加载
                if (this.wplist.length >= res.total) {
                    this.endScroll = true
                    this.disabledScroll = true
                } else {
                    this.disabledScroll = false
                }
                setTimeout(() => {
                    this.loading = false
                }, 100)
            })
        },
        handleFocus() {
            this.showPlaceholder = false
        },
        handleClose() {
            this.$emit('close')
        },
        handleBlur() {
            this.showPlaceholder = !this.value
        }
    }
}
</script>

<style lang="less" scoped>
@assets: '~@/assets/images/temp';
.ivu-drawer {
	width: 100%;
}
/deep/.ivu-drawer-content {
	background-color: transparent;
}
.search-wp-warp {
	width: 100vw;
	height: 100%;
	background: #ebf5ff;
	border-radius: 30px 30px 0px 0px;
	position: relative;
	display: flex;
	flex-direction: column;
	.close {
		display: inline-flex;
		width: 96px;
		height: 96px;
		border-radius: 96px;
		align-items: center;
		justify-content: center;
		position: absolute;
		right: 48px;
		top: -136px;
		background-image: url('@{assets}/closeBtnIcon.png');
		background-size: 100% 100%;
	}
	h1 {
		height: 100px;
		padding: 40px 48px 32px;
		line-height: 1;
		flex-shrink: 0;
	}
	.wp-search {
		height: 96px;
		border-radius: 96px;
		background-color: #fff;
		display: flex;
		margin: 0 48px 40px;
		align-items: center;
		flex-shrink: 0;
		i {
			display: inline-flex;
			width: 42px;
			height: 42px;
			background-image: url('@{assets}/wp-search-icon.png');
			background-size: 100% 100%;
			margin: 0 20px;
			flex-shrink: 0;
		}
		.search-warp {
			position: relative;
			width: 100%;
		}
		span {
			font-size: 40px;
			color: #64759a;
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
		}
		input {
			height: 48px;
			width: 95%;
			border: none;
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			background-color: transparent;
			font-size: 38px;
			color: #64759a;
			&:focus {
				outline: none;
			}
		}
	}
	.wp-content {
		flex: 1;
		padding: 0 10px;
		height: calc(100% - 380px);
		box-sizing: border-box;
		.content-box {
			height: 100% !important;
			overflow-y: auto;
		}
	}
	.tool {
		width: 100%;
		height: 184px;
		background: #ebf5ff;
		display: flex;
		justify-content: center;
		align-items: center;
		.cancel {
			width: 320px;
			height: 96px;
			border: 2px solid #337eff;
			font-size: 36px;
			color: #337eff;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #fff;
			border-radius: 8px;
			cursor: pointer;
			margin: 0 60px;
		}
		.confrim {
			width: 320px;
			height: 96px;
			font-size: 36px;
			background-color: #337eff;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 8px;
			cursor: pointer;
			margin: 0 60px;
		}
	}
}
</style>
