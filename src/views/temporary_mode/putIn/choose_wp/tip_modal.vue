<template>
    <div class="tip-warp">
        <div>
            <img src="@/assets/images/temp/tip-modal-bg.png" alt="" />
            <div class="tip-text">
                <p>
                    每次存入仅支持开启
                    <i>一个柜格</i>
                </p>
                <p>请勿选择过多物品导致放不下</p>
            </div>
            <div class="tool">
                <span @click="handleGo">继续（{{ count }}s）</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            count: 5,
            timer: null
        }
    },
    mounted() {
        clearInterval(this.timer)
        this.timer = setInterval(() => {
            if (this.count == 0) {
                this.$emit('close')
            } else {
                this.count--
            }
        }, 1000)
    },
    methods: {
        handleGo() {
            this.$emit('close')
        }
    }
}
</script>

<style lang="less" scoped>
.tip-warp {
	width: 920px;
	height: 1120px;
	text-align: center;
	> div {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	.close {
		position: absolute;
		display: inline-block;
		width: 96px;
		height: 96px;
		background-image: url('~@/assets/images/temp/closeBtnIcon.png');
		background-size: 100% 100%;
		margin: 48px auto;
		cursor: pointer;
	}
	img {
		width: 576px;
		height: 360px;
		margin-top: 180px;
		margin-left: 110px;
	}
	.tip-text {
		margin-top: 95px;
		p {
			font-size: 48px;
			color: #3663b3;
			&:nth-child(1) {
				margin-bottom: 30px;
			}
			i {
				font-size: 48px;
				font-weight: bold;
				color: #337eff;
			}
		}
	}
	.tool {
		margin-top: 140px;
		span {
			display: inline-block;
			width: 320px;
			height: 96px;
			border-radius: 8px;
			background-color: #337eff;
			color: #fff;
			font-size: 36px;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
		}
	}
}
</style>
