import { getNewToken } from '@/libs/utils'
const serverConfig = JSON.parse(localStorage.getItem('serverConfig'))

export default {
    data() {
        return {
            smsbVal: '',
            caseList_scan: [],
            key: {
                '48': '0',
                '49': '1',
                '50': '2',
                '51': '3',
                '52': '4',
                '53': '5',
                '54': '6',
                '55': '7',
                '56': '8',
                '57': '9',
                '65': 'A',
                '66': 'B',
                '67': 'C',
                '68': 'D',
                '69': 'E',
                '70': 'F',
                '71': 'G',
                '72': 'H',
                '73': 'I',
                '74': 'J',
                '75': 'K',
                '76': 'L',
                '77': 'M',
                '78': 'N',
                '79': 'O',
                '80': 'P',
                '81': 'Q',
                '82': 'R',
                '83': 'S',
                '84': 'T',
                '85': 'U',
                '86': 'V',
                '87': 'W',
                '88': 'X',
                '89': 'Y',
                '90': 'Z',
                '188': ',',
                '219': '{',
                '220': '\\',
                '221': '}',
                '222': '"',
                '186': ':'
            }
        }
    },
    created() {
        this.smsb()
    },
    methods: {
        // 扫码识别
        smsb() {
            this.smsbVal = ''
            this.unBindKeydown()
            const flag = this.GetOSInfo()

            $(document).bind('keydown', (e) => {
                const _code = e.keyCode + ''
                const key = this.key
                if (_code != 16 && _code != 13 && key[_code] != undefined && !(e.shiftKey && e.keyCode == 54)) {
                    this.smsbVal += key[_code]
                }
                if (e.shiftKey && e.keyCode == 54) {
                    this.smsbVal += '^'
                }
                if (flag && e.shiftKey && e.keyCode == 59 && this.smsbVal.substr(this.smsbVal.length - 1, 1) != ':') {
                    this.smsbVal += ':'
                }
                if (_code == 13) {
                    this.judgeFormat(this.smsbVal)
                    this.value = this.smsbVal + ''
                    this.smsbVal = ''
                }
            })
        },
        // 查询当前的操作系统
        GetOSInfo() {
            return String(navigator.platform).indexOf('Linux') > -1
        },
        judgeFormat(srnr) {
            // 不在选择物品步骤不执行
            if (this.$route.name != 'choose_wp') return
            srnr = srnr.replace(/\\000026/g, '')
            // 判断是否二维码内容
            if (!srnr) {
                this.ModalInfo('确认', 'tipsIcon2', '暂不支持该二维码格式')
                this.smsb()
                return
            }
            const wpid = srnr.split(',')[1]
            wpid && this.getWpxxByWpid(wpid)
            // 暂时屏蔽rfid获取物品功能
            // !wpid && this.getWpxxByRfid(srnr)
        },
        // 二维码-根据物品id获取物品信息
        getWpxxByWpid(wpid) {
            const params = {
                wplx: 1,
                isFromZfpt: false,
                wpxzWithOut: 7,
                wpzts: '1,3,9,11,22',
                sfzk: 0,
                ruleSql: localStorage.getItem('sacw:mjwpglcx'),
                ruleId: 'sacw:mjwpglcx',
                centerId: this.serverConfig.centerId,
                pageNo: 1,
                pageSize: 1,
                ids: wpid
            }
            this.$Post(this.PymApi.get_wpxx_page_data, params)
                .then((res) => {
                    if (res.success) {
                        this.ModalInfo('确定', 'tipsIcon3', '扫码成功，请稍候...')
                        setTimeout(() => {
                            if (!res.success) {
                                this.ModalInfo('确定', 'tipsIcon2', '查询物品失败！')
                                this.smsb()
                                return
                            }
                            if (!res.rows || !res.rows[0]) {
                                this.ModalInfo('确定', 'tipsIcon2', '未查询到物品！')
                                this.smsb()
                                return
                            }
                            if ([1, 3, 9, 11, 22].includes(Number(res.rows[0].wpzt))) {
                                const isHad = this.selectedWp.find((WP) => WP.id == res.rows[0].id)
                                if (isHad) {
                                    this.ModalInfo('确定', 'tipsIcon2', '当前物品已选中！')
                                } else {
                                    this.$Modal.remove()
                                    this.selectedWp.push(res.rows[0])
                                }
                                this.smsb()
                            } else {
                                this.ModalInfo('确定', 'tipsIcon2', '当前物品状态不可暂存！')
                                this.smsb()
                            }
                        }, 1000)
                    } else {
                        this.ModalInfo('确认', 'tipsIcon2', res.msg || '涉案物品未登记，请先登记！')
                        this.smsb()
                    }
                })
                .finally(() => {
                    this.smsb()
                })
        },
        // rfid-根据rfidCode获取物品信息
        getWpxxByRfid(rfidCode) {
            const params = {
                rfidCode,
                centerId: serverConfig.centerId,
                access_token: getNewToken(),
                pageSize: 10,
                pageNo: 1,
                ruleSql: localStorage.getItem('sacw:mjwpglcx'),
                ruleId: 'sacw:mjwpglcx'
            }
            this.$Post(this.PymApi.getOperGeneralList, params).then((res) => {
                if (res.success) {
                    this.ModalInfo('确定', 'tipsIcon3', '扫码成功，请稍候...')
                    setTimeout(() => {
                        if (!res.row) {
                            this.ModalInfo('确定', 'tipsIcon2', '查询物品失败！')
                            this.smsb()
                            return
                        }
                        if (!res.row.length) {
                            this.ModalInfo('确定', 'tipsIcon2', '当前REFID未绑定！')
                            this.smsb()
                            return
                        }
                        if ([1, 3, 9, 11, 22].includes(Number(res.row[0].wpzt))) {
                            const isHad = this.selectedWp.find((WP) => {
                                WP.id == res.data[0].id
                            })
                            if (isHad) {
                                this.ModalInfo('确定', 'tipsIcon2', '当前物品已选中！')
                            } else {
                                this.$Modal.remove()
                                this.selectedWp.push(...res.data)
                            }
                            this.smsb()
                        } else {
                            this.ModalInfo('确定', 'tipsIcon2', '当前物品状态不可暂存！')
                            this.smsb()
                        }
                    }, 1000)
                }
            })
        },
        unBindKeydown() {
            $(document).unbind('keydown')
        }
    },
    beforeDestroy() {
        this.unBindKeydown()
    }
}
