<template>
    <div class="choose-police">
        <Step :current-step="3"></Step>
        <div class="search-police">
            <i></i>
            <div class="search-warp">
                <span v-show="showPlaceholder">请输入姓名/{{ isHaiJing?'编号':'警号' }}查询</span>
                <input v-model="value" type="text" @input="debounce(handleFilterPolice, 1000)" @focus="handleFocus" @blur="handleBlur" v-xKeyboard />
            </div>
        </div>
        <div class="org-select">
            <div class="org-warp">
                <span :class="{ active: !isAllOrg }" @click="handleChangeOrg(false)">当前机构</span>
                <span :class="{ active: isAllOrg }" @click="handleChangeOrg(true)">所有机构</span>
            </div>
        </div>
        <div class="police-list">
            <div v-for="(item, key) in policeList" :id="key" :key="key" class="police-item" :class="{ selection: item.id == choosePoliceId }" @click="handleChoosePolice(item, key)">
                <i class="index">{{ key + 1 > 9 ? key + 1 : '0' + (key + 1) }}</i>
                <div class="police-info">
                    <p class="police-name">{{ item.name_code }}</p>
                    <p class="police-org">{{ item.orgName }}</p>
                </div>
            </div>
            <div v-if="policeList.length > 8" class="ending">已经到底了</div>
            <div v-if="!policeList.length" class="ending">暂无数据</div>
        </div>
        <div class="tool">
            <span class="prev" @click="handlePrev">上一步</span>
            <span class="next" :class="{ pass: !!choosePoliceId }" @click="handleNext">下一步</span>
        </div>
    </div>
</template>

<script>
import Step from '_c/step.vue'
import { debounce } from '@/libs/utils.js'
export default {
    components: {
        Step
    },
    data() {
        return {
            showPlaceholder: true,
            value: '',
            choosePoliceId: null,
            choosePolice: null,
            page: {
                pageSize: -1,
                pageNo: 1
            },
            allPoliceList: [],
            policeList: [],
            endScroll: false,
            loading: true,
            disabledScroll: false,
            isAllOrg: false,
            isHaiJing: false
        }
    },
    computed: {
        userInfo() {
            const user = localStorage.getItem('userInfo') || {}
            console.log('user', user)
            return user
        }
    },
    mounted() {
        this.handleGetPolice()
        this.isHaiJing = this.serverConfig.isHaiJing
    },
    methods: {
        debounce: debounce,
        handleChangeOrg(isAllOrg) {
            this.isAllOrg = isAllOrg
            this.handleGetPolice()
        },
        handleChoosePolice(item, key) {
            this.choosePolice = item
            this.choosePoliceId = item.id
        },
        handlePrev() {
            // this.$router.go(-1)
            this.$router.push({ name: 'take_photo' })
        },
        handleNext() {
            if (!this.choosePolice || JSON.stringify(this.choosePolice) == '{}') return

            this.$store.commit('setPolice', { police: this.choosePolice })

            this.$router.push({ name: 'choose_cabinet', params: { fromName: 'choose_police', getWplist: this.$store.getters.getWplist }})
        },
        handleFilterPolice() {
            // this.choosePolice = null
            // this.choosePoliceId = null
            if (!this.value) {
                this.policeList = [...this.allPoliceList]
                return
            }
            this.policeList = this.allPoliceList.filter((item) => item.name.includes(this.value) || item.loginId.includes(this.value))
        },
        handleGetPolice() {
            const user = JSON.parse(localStorage.getItem('userInfo') || {})
            this.$Post(this.BspApi.get_org_user_page_data, {
                orgCode: this.isAllOrg ? '' : user.orgCode,
                ...this.page
            }).then((res) => {
                if (!res.success) return
                const data = res.data.records.map((record) => {
                    record.name_code = `${record.name}（${record.loginId}）`
                    return record
                })
                this.choosePolice = this.$store.getters.getPolice
                if (this.choosePolice) {
                    this.choosePoliceId = this.choosePolice.id
                }
                this.allPoliceList = data
                this.handleFilterPolice()
            })
        },
        handleFocus() {
            this.showPlaceholder = false
        },
        handleBlur() {
            this.showPlaceholder = !this.value
        }
    }
}
</script>

<style lang="less" scoped>
@assets: '~@/assets/images/temp';
.choose-police {
	width: 100%;
	margin: 0 auto;
}
.org-select {
	display: flex;
	justify-content: flex-end;
	margin: 0 20px 20px;
	.org-warp {
		width: 280px;
		height: 50px;
		background: #fff;
		border-radius: 50px;
		margin-right: 40px;
		display: flex;
	}
	span {
		flex-shrink: 0;
		display: flex;
		width: 50%;
		height: 50px;
		justify-content: center;
		align-items: center;
		font-size: 24px;
		color: #64759a;
		cursor: pointer;
		text-align: center;
		&.active {
			color: #fff;
			background: #337eff;
			border-radius: 50px;
			box-sizing: border-box;
			// font-weight: bold;
		}
	}
}
.police-list {
	display: flex;
	flex-direction: column;
	margin: 0 40px;
	height: 970px;
	overflow-y: auto;
	padding: 0;
	.police-item {
		width: 100%;
		height: 130px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-shadow: 0px 5px 10px 1px rgba(24, 144, 255, 0.1);
		margin-bottom: 24px;
		border: 1px solid #c0d3ff;
		border-radius: 16px;
		flex-shrink: 0;
		background-color: #fff;
		&.selection {
			background-color: #669eff;
			.index {
				color: #fff;
			}
			.police-info {
				.police-name {
					color: #fff;
				}
				.police-org {
					color: #fff;
				}
			}
		}
		.index {
			width: 80px;
			color: #191e3b;
			font-size: 32px;
			flex-shrink: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 4px;
		}
		.police-info {
			width: 100%;
			.police-name {
				color: #3663b3;
				font-size: 36px;
			}
			.police-org {
				font-size: 28px;
				color: #191e3b;
			}
		}
	}
	.ending {
		color: #b6bed0;
		font-size: 28px;
		height: 60px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
.tool {
	display: flex;
	justify-content: center;
	span {
		display: inline-block;
		width: 320px;
		height: 96px;
		font-size: 36px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 8px;
		margin: 10px 60px 40px;
		cursor: pointer;
		&.prev {
			color: #337eff;
			border: 2px solid #337eff;
			background-color: #fff;
		}
		&.next {
			color: #fff;
			background-color: #9bbdfa;
			&.pass {
				background-color: #337eff;
			}
		}
	}
}
.search-police {
	// width: 100%;
	height: 96px;
	border-radius: 96px;
	background-color: #fff;
	display: flex;
	margin: 32px 48px 20px;
	align-items: center;
	flex-shrink: 0;
	i {
		display: inline-flex;
		width: 42px;
		height: 42px;
		background-image: url('@{assets}/wp-search-icon.png');
		background-size: 100% 100%;
		margin: 0 20px;
		flex-shrink: 0;
	}
	.search-warp {
		position: relative;
		width: 100%;
	}
	span {
		font-size: 40px;
		color: #64759a;
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
	}
	input {
		height: 48px;
		width: 95%;
		border: none;
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		background-color: transparent;
		font-size: 38px;
		color: #64759a;
		&:focus {
			outline: none;
		}
	}
}
</style>
