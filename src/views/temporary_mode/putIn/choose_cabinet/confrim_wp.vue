<template>
    <div class="confrim-wp">
        <h2>请确认存入物品信息</h2>
        <div class="info">
            <span class="label">取货人:</span>
			&nbsp;
            <span class="name">{{ userInfo.name }}</span>
            <span class="label">暂存柜位:</span>
			&nbsp;
            <span class="gh">{{ currentCabinet.num }}</span>
        </div>
        <div class="wp-list-warp">
            <WpList :list="wplist" :show-image="true" :show-tool="true" @del="handleDel"></WpList>
        </div>
    </div>
</template>

<script>
import WpList from '_c/wp-list'
import { modalInfo } from '@/libs/modal'
export default {
    components: {
        WpList
    },
    data() {
        return {
            wplist: []
        }
    },
    computed: {
        userInfo() {
            return this.$store.getters.getPolice
        },
        currentCabinet() {
            return this.$store.getters.getCabinet
        }
    },
    mounted() {
        this.wplist = [].concat(this.$route.params.getWplist ? this.$route.params.getWplist : this.$store.getters.getWplist)
        console.log(this.wplist, '=======确认物品列表数据=======')
    },
    methods: {
        handleDel(item) {
            if (this.wplist.length == 1) {
                modalInfo('确定', 'tipsIcon2', '存入物品不能为空！', () => {})
                return
            }
            const idx = this.wplist.findIndex((item2) => item2.id == item.id)
            this.wplist.splice(idx, 1)
        },
        handleComfrimWp() {
            // this.$store.commit('setWplist', { wplist: [...this.wplist] })
            return this.wplist
        }
    }
}
</script>

<style lang="less" scoped>
.confrim-wp {
	width: 100%;
	margin: 0 auto;
	padding: 0 15px;
	h2 {
		font-size: 48px;
		color: #3a416e;
		font-weight: bold;
		padding: 0 30px;
		line-height: 1;
		margin: 30px 0 40px;
	}
	.info {
		padding: 0 30px 40px;
		span {
			font-size: 32px;
			line-height: 1;
		}
		.label {
			color: #64759a;
		}
		.name {
			color: #2e71e5;
			margin-right: 204px;
		}
		.gh {
			color: #191e3b;
		}
	}
	.wp-list-warp {
		height: 1080px;
		overflow: auto;
	}
}
</style>
