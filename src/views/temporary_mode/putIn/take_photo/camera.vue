<template>
    <div>
        <div class="preview">
            <div class="camera-warp">
                <video :id="'video' + sort" ref="video" class="camera" :style="`transform: rotateZ(${videoConfig.rotate}deg) rotateY(${videoConfig.horizontal == 'Y' ? 180 : 0}deg)`"></video>
                <canvas v-show="!isActionShot" :id="'canvas' + sort" ref="canvas"></canvas>
                <img v-show="imgBase64 && !isActionShot" :src="imgBase64" />
                <div v-show="isActionShot" class="countdown">
                    <span>{{ countDown }}</span>
                </div>
                <div v-if="showError" class="mark">摄像头开启失败，请检查摄像头是否可用！</div>
            </div>
            <span class="top-left"></span>
            <span class="top-right"></span>
            <span class="bottom-left"></span>
            <span class="bottom-right"></span>
        </div>
        <div class="shot-warp">
            <!-- class="replay" -->
            <span :class="{ replay: imgBase64 }" @click="delayedThree"></span>
        </div>
    </div>
</template>

<script>
import commonServ from '@/libs/common_serv.js'
export default {
    props: {
        url: {
            type: String,
            default: ''
        },
        sort: {
            type: [String, Number],
            default: '0'
        }
    },
    data() {
        return {
            showError: false,
            isActionShot: false,
            countDown: 3,
            countDownTimer: null,
            mediaStreamTrack: null,
            imgBase64: null,
            videoConfig: {
                horizontal: '',
                rotate: 0
            }
        }
    },
    computed: {
        p_width() {
            return this.$refs['canvas']?.offsetWidth || 0
        },
        p_height() {
            return this.$refs['canvas']?.offsetHeight || 0
        }
    },
    mounted() {
        this.videoConfig.horizontal = this.serverConfig.horizontal
        this.videoConfig.rotate = this.serverConfig.rotate || 0
        this.imgBase64 = this.url || null

        this.callCamera()
    },
    destroyed() {
        clearInterval(this.countDownTimer)
        this.countDownTimer = null
    },
    methods: {
        callCamera() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
                console.error('menumerateDevices is not supported!')
                return
            }
            // H5调用电脑摄像头API，成功则返回视频流
            navigator.mediaDevices
                .enumerateDevices()
                .then(this.gotDevices)
                .catch((err) => {
                    console.log('没有摄像头', err)
                })
        },
        // 遍历所有的设备，包括视频和音频设备,找到摄像头
        gotDevices(deviceInfos) {
            const videoSource = []
            const cameraName = this.$store.getters.getTerminalConfig.faceCamera || ''
            deviceInfos.forEach((deviceInfo) => {
                if (deviceInfo.kind === 'videoinput' && (!cameraName || (cameraName && deviceInfo.deviceId.indexOf(cameraName) !== -1))) {
                    const option = {
                        text: deviceInfo.label,
                        value: deviceInfo.deviceId
                    }
                    videoSource.push(option)
                }
            })
            if (videoSource.length > 0) {
                this.play(videoSource[0].value)
            } else {
                console.log('没有摄像头')
                this.showError = true
            }
        },
        // 获取摄像头进行播放摄像头
        play(deviceId) {
            const constraints = {
                video: {
                    width: this.p_width,
                    height: this.p_height,
                    deviceId: deviceId ? { exact: deviceId } : undefined
                },

                audio: false
            }
            navigator.mediaDevices
                .getUserMedia(constraints)
                .then(this.gotMediaStream)
                .catch((err) => {
                    console.log('没有摄像头', err)
                    this.showError = true
                })
        },
        gotMediaStream(stream) {
            if (this.$refs['video']) {
                this.$refs['video'].srcObject = stream
                this.mediaStreamTrack = stream
                this.$refs['video'].play()
                this.showError = false
            }
        },
        // 倒计时3秒
        delayedThree() {
            if (this.isActionShot) return
            this.countDown = 3
            this.isActionShot = true
            this.countDownTimer = setInterval(() => {
                this.countDown--
                if (this.countDown < 1) {
                    this.shotPhoto()
                    clearInterval(this.countDownTimer)
                    this.isActionShot = false
                }
            }, 1000)
        },
        shotPhoto() {
            // this.$refs['canvas'].setAttribute('height', this.p_height)
            // this.$refs['canvas'].setAttribute('width', this.p_width)
            // const ctx = this.$refs['canvas'].getContext('2d')
            const canvas = commonServ.handleCameraCof(
                {
                    height: this.p_height,
                    width: this.p_width
                },
                this.videoConfig,
                'canvas' + this.sort,
                'video' + this.sort
            )
            // 把当前视频帧内容渲染到canvas上
            // ctx.drawImage(this.$refs['video'], 0, 0, this.p_width, this.p_height)
            // 转base64格式、图片格式转换、图片质量压缩
            this.imgBase64 = canvas.toDataURL('image/jpeg')
            this.$emit('returnImg', this.imgBase64)
        },
        clearCamera() {
            this.mediaStreamTrack &&
			this.mediaStreamTrack.getTracks().forEach((track) => { track.stop() })
            this.mediaStreamTrack = null
            this.$refs['video'].srcObject = null
        }
    }
}
</script>

<style lang="less">
@assets: '~@/assets/images/temp';
.preview {
	width: 984px;
	height: 760px;
	border: 42px solid rgba(8, 5, 44, 0.6);
	border-radius: 16px;
	margin-top: 20px;
	position: relative;
	.win {
		position: absolute;
		width: 100%;
		height: 100%;
		background-color: yellow;
	}
	> span {
		display: inline-block;
		width: 72px;
		height: 72px;
		position: absolute;
		&.top-left {
			border-left: 6px solid #66e6ff;
			border-top: 6px solid #66e6ff;
			left: -6px;
			top: -6px;
		}
		&.top-right {
			border-right: 6px solid #66e6ff;
			border-top: 6px solid #66e6ff;
			right: -6px;
			top: -6px;
		}
		&.bottom-right {
			border-right: 6px solid #66e6ff;
			border-bottom: 6px solid #66e6ff;
			right: -6px;
			bottom: -6px;
		}
		&.bottom-left {
			border-left: 6px solid #66e6ff;
			border-bottom: 6px solid #66e6ff;
			left: -6px;
			bottom: -6px;
		}
	}
}
.camera-warp {
	width: 100%;
	height: 100%;
	background-color: #252525;
	position: relative;
	overflow: hidden;
	.camera {
		width: calc(100% + 1px);
		height: calc(100% + 1px);
		position: absolute;
		left: 0;
		top: 0;
		margin-left: 0;
	}
	img {
		height: 100%;
		width: 100%;
		position: absolute;
		left: 0;
		top: 0;
	}
	.mark {
		width: 100%;
		color: #333;
		font-size: 32px;
		position: absolute;
		left: 0;
	}
	canvas {
		width: 100%;
		height: 100%;
		position: absolute;
		left: 0;
		top: 0;
	}
	.countdown {
		position: absolute;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		> span {
			font-size: 240px;
			font-weight: bold;
			color: #fff;
		}
	}
}
.shot-warp {
	height: 180px;
	display: flex;
	justify-content: flex-start;
	margin-top: 20px;
	display: flex;
	flex-direction: column;
	align-items: center;
	> span {
		display: inline-block;
		width: 180px;
		height: 180px;
		background-size: 100% 100%;
		background-image: url('@{assets}/shot-default-cion.png');
		flex-shrink: 0;
		&.replay {
			background-image: url('@{assets}/shot-replay-cion.png');
		}
	}
}
</style>
