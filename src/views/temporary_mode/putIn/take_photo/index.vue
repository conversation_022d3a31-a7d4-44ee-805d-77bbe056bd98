<template>
    <div class="take-photo-warp">
        <Step :current-step="2" @backHome="handleClearCamera"></Step>
        <div class="take-photo-content">
            <Carousel v-model="value" dots="none" arrow="never">
                <CarouselItem v-for="(item, key) in wplist" :key="key">
                    <div class="carousel">
                        <div class="carousel-head">
                            <span class="arrow left" @click="handleArrow('left')"></span>
                            <div class="wp-info">
                                <span class="index">{{ key + 1 > 9 ? key + 1 : '0' + (key + 1) }}</span>
                                <div class="info">
                                    <p>{{ item.wpbh }}</p>
                                    <p>{{ item.wpmc }}</p>
                                </div>
                            </div>
                            <span class="arrow right" @click="handleArrow('right')"></span>
                        </div>
                        <Camera ref="Camera" :sort="key" :url="item.url" @returnImg="handleReturnImg"></Camera>
                    </div>
                </CarouselItem>
            </Carousel>
            <div class="tip-warp">
                <p v-show="showError" class="tip">
                    <img src="@/assets/images/temp/error.png" alt="" />
                    有物品未拍摄入库照片，请左右切换页面确认
                </p>
            </div>
            <div class="tool">
                <span class="prev" @click="handlePrev">上一步</span>
                <span class="next" @click="handleNext">下一步</span>
            </div>
        </div>
    </div>
</template>

<script>
import Step from '_c/step.vue'
import Camera from './camera.vue'
import { getNewToken, base64ToFile } from '@/libs/utils'
import { voiceBroadcast } from '@/libs/utils'
export default {
    components: {
        Step,
        Camera
    },
    beforeRouteLeave(to, form, next) {
        voiceBroadcast(false)
        next()
    },
    data() {
        return {
            showError: false,
            wplist: [],
            wpPhotos: {},
            value: 0
        }
    },
    mounted() {
        this.wplist = this.$store.getters.getWplist
        this.wplist.forEach((wp) => {
            if (wp.wpxxPhotoData) {
                this.wpPhotos[wp.id] = wp.wpxxPhotoData[0]?.url
                wp.url = wp.wpxxPhotoData[0]?.url
            } else {
                this.wpPhotos[wp.id] = undefined
            }
        })
        setTimeout(this.voiceBroadcastPlay, 1000)
    },
    methods: {
        // 接收从camera组件返回的base64图片
        handleReturnImg(base64) {
            this.wpPhotos[this.wplist[this.value].id] = base64
        },
        handleArrow(arrow) {
            const length = this.wplist.length - 1
            if (length < 1) return
            if (arrow == 'left') {
                if (this.value === 0) {
                    this.value = length
                } else {
                    this.value--
                }
            }
            if (arrow == 'right') {
                if (this.value === length) {
                    this.value = 0
                } else {
                    this.value++
                }
            }
        },
        handleNext() {
            const shotPhotoNum = this.wplist.filter((wp) => !this.wpPhotos[wp.id]).length
            // isDoneShot 为true 则还有物品未拍照，需展示提示
            this.showError = false
            if (shotPhotoNum != 0) {
                this.showError = true
                return
            }

            // 上传本地照片base64到gofast
            let wpPhotos = this.wplist.map((wp) => {
                if (!this.wpPhotos[wp.id].includes('http://')) {
                    const formData = new FormData()
                    formData.append('file', base64ToFile(this.wpPhotos[wp.id], `${wp.id}.jpg`))
                    formData.append('access_token', getNewToken())
                    return this.$Upload(this.UploadApi.upload, formData)
                }
            })
            wpPhotos = wpPhotos.filter((item) => item)
            if (wpPhotos.length) {
                Promise.all(wpPhotos).then((list) => {
                    list.forEach((item, i) => {
                        if (item && item.url) {
                            const idx = this.wplist.findIndex((wp) => item.url.indexOf(wp.id) != -1)
                            this.$set(this.wplist[idx], 'wpxxPhotoData', [item])
                        } else {
                            this.$GxxMessage({ message: '图片上传失败，请联系管理员！', type: 'error'})
                            return
                        }
                    })
                    this.$store.commit('setWplist', { wplist: this.wplist })
                    this.handleClearCamera()
                    this.$router.push({ name: 'choose_police', params: { fromName: 'take_photo' }})
                }).catch((err) => {
                    this.$GxxMessage({ message: '错误代码【404】，请联系管理员！', type: 'error'})
                    console.log(err)
                })
            } else {
                this.handleClearCamera()
                this.$router.push({ name: 'choose_police', params: { fromName: 'take_photo' }})
            }
        },
        handlePrev() {
            this.handleClearCamera()
            setTimeout(() => {
                this.$router.push({ name: 'choose_wp' })
            }, 500)
        },
        // 关闭摄像头
        handleClearCamera() {
            const cameras = this.$refs['Camera']
            for (let i = 0; i < cameras.length; i++) {
                cameras[i].clearCamera()
            }
        },
        // 拍照语音提示
        voiceBroadcastPlay() {
            const tips = `请将暂存物品对准摄像头，点击按钮将于3秒后拍摄照片`

            voiceBroadcast(true, tips, 'takePhoto')
        }
    }
}
</script>

<style lang="less" scoped>
@assets: '~@/assets/images/temp/';
.take-photo-warp {
	.take-photo-content {
		.tool {
			display: flex;
			justify-content: center;

			span {
				display: inline-block;
				width: 320px;
				height: 96px;
				font-size: 36px;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 8px;
				margin: 10px 60px 40px;
				cursor: pointer;
				&.prev {
					color: #337eff;
					border: 2px solid #337eff;
					background-color: #fff;
				}
				&.next {
					color: #fff;
					background-color: #337eff;
				}
			}
		}
	}
	.tip-warp {
		margin-top: 10px;
		height: 30px;
	}
	.tip {
		display: flex;
		justify-content: center;
		color: #f84558;
		font-size: 24px;
		img {
			width: 32px;
			height: 32px;
			margin-right: 6px;
		}
	}
}
.carousel {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 32px;
	.carousel-head {
		display: flex;
		justify-content: center;
		align-items: center;
		.arrow {
			display: inline-block;
			width: 60px;
			height: 60px;
			background-size: 100%;
			margin: 0 52px;
			cursor: pointer;
			&.left {
				background-image: url('@{assets}/arrow-left-icon.png');
			}
			&.right {
				background-image: url('@{assets}/arrow-right-icon.png');
			}
		}
	}
	.wp-info {
		display: flex;
		align-items: center;
		width: 760px;
		height: 130px;
		border-radius: 16px;
		background-color: #fff;
		box-shadow: 0px 4px 6px 1px #cedcf5;
		.index {
			display: inline-block;
			font-size: 32px;
			color: #191e3b;
			margin-left: 32px;
		}
		.info {
			margin-left: 32px;
			p:nth-child(1) {
				font-size: 32px;
				color: #3663b3;
			}
			p:nth-child(2) {
				font-size: 28px;
				color: #191e3b;
			}
		}
	}
}
/deep/.ivu-carousel-arrow.left {
	display: none;
	// display: inline-block;
	width: 60px;
	height: 60px;
	background-size: 100%;
	left: 50%;
	top: 67px;
	transform: translateX(-490px);
	cursor: pointer;
	background-image: url('@{assets}/arrow-left-icon.png');
	.ivu-icon {
		display: none;
	}
}

/deep/.ivu-carousel-arrow.right {
	display: none;
	// display: inline-block;
	width: 60px;
	height: 60px;
	background-size: 100%;
	right: 50%;
	top: 67px;
	transform: translateX(490px);
	cursor: pointer;
	background-image: url('@{assets}/arrow-right-icon.png');
	.ivu-icon {
		display: none;
	}
}
</style>
