<template>
    <div class="confirm-wrap">
        <div class="content-wrap">
            <h2>请确认存入物品信息</h2>
            <div class="info">
                <span class="label">取货人:</span>
                &nbsp;
                <span class="name">{{ userInfo.name }}</span>
                <span class="label">暂存柜位:</span>
                &nbsp;
                <span class="gh">{{ currentCabinet.num }}</span>
            </div>
            <WpList :list="wplist" :show-image="true" :show-tool="true" @del="handleDel"></WpList>
            <p v-if="!wplist.length" class="not-data">柜位未存放物品，确定后可触发‘有物品未放入’按钮重新选择物品存放。</p>
        </div>
        <div class="foolter">
            <span class="btn btn-confirm" @click="evConfirm">确定</span>
        </div>
    </div>
</template>
<script>
import dealDialog from '@/components/dealDialog'
import WpList from '_c/wp-list'
export default {
    components: {
        WpList
    },
    props: {
        params: {
            type: Array,
            default: () => ([])
        }
    },
    data() {
        return {
            userInfo: {},
            currentCabinet: {},
            wplist: []
        }
    },
    mounted() {
        this.userInfo = this.$store.getters.getPolice
        this.currentCabinet = this.$store.getters.getCabinet
        this.wplist = [].concat(this.params)
    },
    methods: {
        handleDel(item) {
            this.$GxxDialog({
                showHeader: false,
                showHeaderClose: false,
                showFooter: false,
                component: dealDialog,
                componentParams: {
                    iconType: 'warning',
                    duration: 0,
                    text: '是否确定要移除此物品（' + item.wpmc + '）？',
                    cancelText: '取消',
                    okText: '确定'
                }
            }).on.then((ret) => {
                if (ret.type == 'confirm') {
                    const idx = this.wplist.findIndex((item2) => item2.id == item.id)
                    this.wplist.splice(idx, 1)
                }
            })
        },
        evConfirm() {
            // 没有物品存入，直接返回到关柜状态界面
            if (!this.wplist.length) {
                this.$parent.close('cancel')
                return true
            }
            this.wplist.forEach(item => {
                item.cabinetAssignVo = {
                    cabineCode: this.currentCabinet.cabinetCode,
                    sideId: this.currentCabinet.sideId,
                    gh: this.currentCabinet.num
                }
            })
            const params = {
                rkKssj: this.$store.getters.getRkkssj,
                centerId: this.serverConfig.centerId,
                qhrXm: this.userInfo.name,
                qhrIdCard: this.userInfo.idCard,
                rkwpJsonStr: JSON.stringify(this.wplist)
            }
            this.$Post(this.PymApi.save_wpzc_batch, params).then((res) => {
                if (res.success) {
                    const idWpList = this.wplist.map((item) => item.id)
                    const remainingWp = this.$store.getters.getWplist.filter((item) => !idWpList.includes(item.id))
                    this.$store.commit('setWplist', { wplist: remainingWp })
                    this.logger({ title: '存入保存记录', value: res })
                    this.$parent.close('confirm')
                } else {
                    this.$GxxDialog({
                        showHeader: false,
                        showHeaderClose: false,
                        showFooter: false,
                        component: dealDialog,
                        componentParams: {
                            iconType: 'warning',
                            duration: 0,
                            text: '数据上报异常，是否取出物品返回首页？',
                            cancelText: '重新上报',
                            okText: '返回首页'
                        }
                    }).on.then((ret) => {
                        if (ret.type == 'cancel') {
                            this.evConfirm()
                        } else {
                            this.$emit('getReturn', this.currentCabinet.num)
                            this.$parent.close('cancel')
                            this.returnHome()
                        }
                    })
                }
            }).catch((err) => {
                this.$emit('getReturn', this.currentCabinet.num)
                this.$parent.close('cancel')
                this.returnHome()
                this.logger({ title: '存入保存记录Error', value: err })
            })
        }
    }
}
</script>
<style lang="less" scoped>
.confirm-wrap {
    background: #ebf5ff;
    border-radius: 12px;
}
.content-wrap {
    min-height: 400px;
    h2 {
		font-size: 48px;
		color: #3a416e;
		font-weight: bold;
        text-align: center;
		padding: 30px 30px 60px;
		line-height: 1;
	}
	.info {
		padding: 0 30px 40px;
		span {
			font-size: 32px;
			line-height: 1;
		}
		.label {
			color: #64759a;
		}
		.name {
			color: #2e71e5;
			margin-right: 204px;
		}
		.gh {
			color: #191e3b;
		}
	}
    .not-data {
        font-size: 32px;
        color: #f56c6c;
        padding: 0 30px 40px;
    }
    /deep/ .wp-list {
        max-height: 650px;
        max-width: 1000px;
        overflow: auto;
    }
}
.foolter {
    padding: 16px 0 30px;
    text-align: center;
    .btn {
        display: inline-block;
        margin-bottom: 0;
        font-size: 25px;
        font-weight: 400;
        min-width: 135px;
        height: 65px;
        line-height: 65px;
        border-radius: 5px;
        padding: 0 15px;
        text-align: center;
        vertical-align: middle;
        -ms-touch-action: manipulation;
        touch-action: manipulation;
        border: 1px solid transparent;
        white-space: nowrap;
        cursor: pointer;
        &.btn-confirm {
            color: #f2f2f2;
            background-color: #2b85e4;
            border-color: #2b85e4;
        }
        &.btn-cancel {
            background: #f26b6b;
            color: #fff;
            & + .btn-confirm {
                margin-left: 30px;
            }
        }
    }
}
</style>
