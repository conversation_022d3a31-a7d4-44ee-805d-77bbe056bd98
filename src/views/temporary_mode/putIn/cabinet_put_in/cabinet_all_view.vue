<template>
    <div class="cabinet-main" :style="{ 'justify-content': 'space-around' }">
        <div class="cabinet">
            <div class="title">{{ cabinetObj.sideCode == 'A' ? '主柜' : cabinetObj.sideCode + '柜' }}</div>
            <div class="cabinet-body">
                <div v-for="(item1, index) in cabinetObj.positionArr" :key="index + 'col'" class="col" :style="{ width: 100 / cabinetObj.positionArr.length + '%' }">
                    <div v-for="(item2, index2) in item1.row" :key="index2 + 'row'" :class="{ row: true, screen: item2.num == '000', active: item2.open || currentCabinetNum == item2.num, activeAnimation: currentCabinetNum == item2.num }" :style="{ flex: ' 1 ' + ' 0 ' + item2.flexBasis * 100 + '%', border: item1.row.length == 1 ? 'none' : '4px solid #ebf5ff;'}">
                        <div :class="{ active: item2.open || currentCabinetNum == item2.num, activeAnimation: currentCabinetNum == item2.num }">{{ currentCabinetNum == item2.num ? item2.num : '' }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'CabinetMain',
    props: {
        currentCabinetNum: {
            require: true,
            type: String,
            default: ''
        },
        openedCabinetNum: {
            require: true,
            type: Array,
            default: () => []
        },
        cabinetDetail: {
            require: true,
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            cabinetArr: [],
            cabinetObj: {},
            titleSpan: 0
        }
    },
    watch: {
        currentCabinetNum(GH) {
            if (!GH) return
            this.cabinetObj = this.cabinetArr.find((item) => {
                return item.sideCode == GH.slice(0, 1)
            })
        }
    },
    created() {
        this.init()
    },
    methods: {
        init() {
            this.cabinetArr = []
            this.cabinetDetail.map((item) => {
                const obj = {
                    sideCode: item.sideCode,
                    positionArr: []
                }
                obj.positionArr = this.dealCabinetDetail(item)
                this.cabinetArr.push(obj)
            })
            this.cabinetObj = this.cabinetArr.find((item) => {
                return item.sideCode == this.currentCabinetNum.slice(0, 1)
            })
        },
        dealCabinetDetail(data) {
            const colData = []
            data.data?.map((item) => {
                this.openedCabinetNum.indexOf(item.num) > -1 ? (item.open = true) : (item.open = false)
                if (colData.indexOf(item.colPos) == -1) colData.push(item.colPos)
            })
            colData.sort((a, b) => a - b)
            const posData = []
            colData.map((col) => {
                const cabinetData = { col }
                cabinetData.row = data.data.filter((item) => item.colPos == col)
                const totalMerges = cabinetData.row.reduce((newVal, item) => {
                    return Number(newVal) + Number(item.merge)
                }, 0)
                cabinetData.row.forEach((item) => {
                    item.flexBasis = item.merge / totalMerges
                })
                posData.push(cabinetData)
            })
            this.titleSpan = colData.length
            return posData
        }
    }
}
</script>

<style lang="less" scoped>
@assets: '../../../../assets/images';
.cabinet-main {
	width: 960px;
	height: auto;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 0 auto 20px;
	.cabinet {
		width: calc(50% - 16px);
		.title {
			width: 100%;
			height: 60px;
			background: #4d5f80;
			margin-bottom: 4px;
			color: #fff;
			font-size: 28px;
			line-height: 60px;
			text-align: center;
		}
		.cabinet-body {
			width: 100%;
			height: 712px;
			display: flex;
			justify-content: space-between;
			margin: 0 auto;
            line-height: 1;
			.col {
				height: 100%;
				flex: 1;
				margin-right: 4px;
				display: flex;
				justify-content: space-between;
				flex-direction: column;
				&:last-child {
					margin-right: 0;
				}
				.row {
					flex: 1;
					background-color: #5c8ae6;
					// margin-bottom: 4px;
					border-bottom: 4px solid #ebf5ff;
					div {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;
						height: 100%;
						font-size: 32px;
						color: #fff;
						&.active {
							background-color: #e5ac00;
							animation: borderColor 1s infinite alternate;
						}
						// &.activeAnimation {
						//   animation: borderColor 1s infinite  alternate;
						// }
					}
					&.screen {
						display: flex;
						justify-content: center;
						align-items: flex-start;
						div {
							display: block;
							width: 100%;
							height: 50%;
							background: url('@{assets}/zg_icon.png') no-repeat;
							background-size: 100% 100%;
							background-position: 0 0;
							border-bottom: 2px solid #77b8e6;
						}

						background-color: #99d5ff;
					}
					// &.active{
					//   background-color: #E6AC00;
					//   animation: borderColor 1s infinite  alternate;
					// }
				}
			}
		}
	}
}

@keyframes borderColor {
	from {
		border: 4px solid #e6ac00;
	}

	to {
		border: 4px solid #cc8e0f;
	}
}
</style>
