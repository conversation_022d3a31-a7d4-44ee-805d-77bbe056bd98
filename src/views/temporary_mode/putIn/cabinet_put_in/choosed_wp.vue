<template>
    <div class="choosed-wp-warp">
        <div class="content-warp">
            <span class="close" @click="handleClose"></span>
            <p class="title">请选择未放入物品</p>
            <div class="wp-warp">
                <wp-list ref="wpList" :list="wplist" :selection="true" @selectedAll="returnSelectedAll" @select="handleSelected"></wp-list>
            </div>
            <div class="tool">
                <div v-if="selectedAll && isFirst" class="tool-tip">
                    <i class="icon"></i>
                    <div class="txt">您全选了所有物品，点击【继续存入】后，这些物品将重新选择柜格存入，请确认没有物品已存入{{ currentCabinet.num }}柜格</div>
                    <i class="close-txt" @click="isFirst = false"></i>
                </div>
                <div class="tool-content">
                    <div class="selection" :class="{ checked: selectedAll }" @click="handleSelectedAll">
                        <i></i>
                        <span>全选</span>
                    </div>
                    <span class="keepon-btn" @click="handleKeep">继续存入</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import WpList from '_c/wp-list'
export default {
    components: {
        WpList
    },
    props: {
        currentCabinet: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            isFirst: true,
            selectedAll: false,
            selectedWp: [],
            wplist: []
        }
    },
    mounted() {
        this.wplist = this.$store.getters.getWplist
        console.log(this.wplist, '有未存放物品的二次选择物品列表')
        // 遍历把物品柜信息写入柜格信息
        // const wplist = this.$store.getters.getWplist
        // this.wplist = wplist.map((item) => {
        //     delete item.checked
        //     const cabinetAssignVo = {
        //         cabineCode: this.currentCabinet.cabinetCode,
        //         sideId: this.currentCabinet.sideId,
        //         gh: this.currentCabinet.num
        //     }
        //     item.cabinetAssignVo = cabinetAssignVo
        //     return item
        // })
    },
    methods: {
        handleSelected(selected) {
            this.selectedWp = selected
        },
        handleSelectedAll() {
            this.selectedAll = !this.selectedAll
            this.$refs.wpList.selectedAll(this.selectedAll)
        },
        returnSelectedAll(val) {
            this.selectedAll = val
        },
        handleClose() {
            this.$emit('close')
        },
        handleKeep() {
            if (!this.selectedWp.length) {
                this.$GxxMessage({ message: '请选择未入库物品！', type: 'warning'})
                return
            }
            // 选择所有物品未放入，取消之前的选中柜格，点击继续存入返回选择柜格
            // if (this.selectedAll) {
            //     this.$store.commit('removeCabinet')
            // } else {
            //     // 筛选存入柜格物品 保存到store-setCabinetOfWp历史记录
            //     const noSaveWpListId = this.selectedWp.map((item) => item.id)
            //     const hadSaveWpList = this.wplist.filter((item) => !noSaveWpListId.includes(item.id))
            //     this.$store.commit('setCabinetOfWp', { gh: this.currentCabinet.num, wplist: hadSaveWpList })
            //     this.$store.commit('removeCabinet')
            //     // 更新store-未保存物品列表
            //     this.$store.commit('setWplist', { wplist: this.selectedWp })
            // }
            // if (!this.selectedAll) {
            //     // 筛选存入柜格物品 保存到store-setCabinetOfWp历史记录
            //     const noSaveWpListId = this.selectedWp.map((item) => item.id)
            //     const hadSaveWpList = this.wplist.filter((item) => !noSaveWpListId.includes(item.id))
            //     this.$store.commit('setCabinetOfWp', { gh: this.currentCabinet.num, wplist: hadSaveWpList })
            //     this.$store.commit('removeCabinet')
            //     // 更新store-未保存物品列表
            //     this.$store.commit('setWplist', { wplist: this.selectedWp })
            // }
            this.$router.push({ name: 'choose_cabinet', params: { fromName: 'cabinet_put_in', notDeposit: true, getWplist: this.selectedWp }})
        }
    }
}
</script>

<style lang="less">
@assets: '~@/assets/images/temp';
.ivu-drawer {
	width: 100%;
	top: auto;
	bottom: 0;
	background-color: transparent;
	.ivu-drawer-content {
		background-color: transparent;
	}
}
.choosed-wp-warp {
	width: 100vw;
	height: 80%;
	background: #ebf5ff;
	border-radius: 30px 30px 0px 0px;
	position: fixed;
	display: flex;
	flex-direction: column;
	bottom: 0;
	.content-warp {
		width: 100%;
		height: 100%;
		position: relative;
		padding: 40px 20px 0;
		display: flex;
		flex-direction: column;
		.close {
			display: inline-flex;
			width: 96px;
			height: 96px;
			border-radius: 96px;
			align-items: center;
			justify-content: center;
			position: absolute;
			right: 48px;
			top: -136px;
			background-image: url('@{assets}/closeBtnIcon.png');
			background-size: 100% 100%;
		}
		.title {
			font-size: 48px;
			color: #3a416e;
			padding: 0 25px 30px;
		}
		.wp-warp {
			height: calc(100% - 300px);
			overflow-y: auto;
		}
		.tool {
			width: 100%;
			height: 184px;
			padding: 0 32px;
			position: relative;
			.tool-tip {
				height: 114px;
				width: 100%;
				background-color: #fee3e6;
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateY(-100%) translatex(-50%);
				border-radius: 8px;
				display: flex;
				align-items: center;
				.icon {
					display: inline-block;
					width: 48px;
					height: 48px;
					background-image: url('@{assets}/error-icon.png');
					background-size: 100% 100%;
					flex-shrink: 0;
					margin: 0 32px;
				}
				.txt {
					font-size: 24px;
					color: #f84558;
				}
				.close-txt {
					flex-shrink: 0;
					display: inline-block;
					width: 48px;
					height: 48px;
					background-size: 100% 100%;
					background-image: url('@{assets}/del-icon.png');
					margin: 0 24px 0 240px;
					cursor: pointer;
				}
			}
			.tool-content {
				height: 100%;
				width: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.selection {
					display: flex;
					align-items: center;
					i {
						flex-shrink: 0;
						margin-left: 32px;
						display: inline-block;
						width: 48px;
						height: 48px;
						background-size: 100% 100%;
						background-image: url('@{assets}/wp-no-checked.png');
					}
					&.checked {
						i {
							background-image: url('@{assets}/wp-checked.png');
						}
					}
					span {
						color: #333333;
						font-size: 36px;
						margin-left: 24px;
					}
				}
				.keepon-btn {
					width: 320px;
					height: 96px;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 36px;
					color: #fff;
					background-color: #337eff;
					border-radius: 8px;
					cursor: pointer;
					&:active {
						transform: scale(0.98);
					}
				}
			}
		}
	}
}
</style>
