<template>
    <div class="tip-warp">
        <div>
            <img src="@/assets/images/temp/error-icon.png" alt="" />
            <div class="tip-text">
                <p v-if="[0, 1].includes(openCount)">柜门未正常开启？</p>
                <p v-if="[0, 1].includes(openCount)">请点击【重新开柜】</p>
                <p v-if="openCount > 1">柜门多次未正常开启？</p>
                <p v-if="openCount > 1">可点击【重选柜格】换柜子</p>
                <p v-if="openCount > 1">可点击【上报故障】通知管理员</p>
            </div>
            <div class="explain">
                <p class="title">其他问题</p>
                <div class="content">
                    <i>1</i>
                    <div class="content-text">
                        <p>物品太多放不下？</p>
                        <p>可关闭柜门后点击【有物品未放入】将剩余物品放进新柜子</p>
                    </div>
                </div>
            </div>
            <div class="tool">
                <span v-if="[0, 1].includes(openCount)" @click="handleGo">重新开柜</span>
                <span v-if="openCount > 1" class="error" @click="handleReplay">重选柜格</span>
                <span v-if="openCount > 1" class="error" @click="handleError">上报故障</span>
            </div>
        </div>
        <i class="close" @click="handleClose"></i>
    </div>
</template>

<script>
export default {
    props: {
        openCount: {
            type: [Number],
            default: 1
        }
    },
    data() {
        return {
            timer: null
        }
    },
    mounted() {
        console.log('openCount', this.openCount)
    },
    methods: {
        handleGo() {
            this.$emit('keepOn')
        },
        handleClose() {
            this.$emit('close')
        },
        handleReplay() {
            this.$emit('replay')
        },
        handleError() {
            this.ModalConfirm('确定', '取消', 'tipsIcon2', '故障上报后，将保存已存入物品，并返回首页', (val) => {
                if (val) {
                    this.$emit('error')
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
.tip-warp {
	width: 920px;
	height: 1050px;
	text-align: center;
	> div {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	.close {
		position: absolute;
		display: inline-block;
		width: 96px;
		height: 96px;
		background-image: url('~@/assets/images/temp/closeBtnIcon.png');
		background-size: 100% 100%;
		margin: 48px auto;
		cursor: pointer;
	}
	img {
		width: 160px;
		height: 160px;
		margin-top: 108px;
	}
	.tip-text {
		margin-top: 50px;
		p {
			font-size: 48px;
			color: #191e3b;
			font-weight: bold;
			&:nth-child(1) {
				margin-bottom: 30px;
			}
			i {
				font-size: 48px;
				font-weight: bold;
				color: #337eff;
			}
		}
	}
	.explain {
		width: 100%;
		height: 320px;
		background-color: #e5efff;
		border: 40px solid #ebf5ff;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 20px 0 0 26px;
		box-sizing: border-box;
		.title {
			font-size: 28px;
			color: #5779b3;
			font-weight: bold;
		}
		.content {
			display: flex;
			margin-top: 20px;
			i {
				display: inline-block;
				width: 48px;
				height: 48px;
				border-radius: 48px;
				background-color: #fff;
				font-size: 32px;
				display: flex;
				color: #337eff;
				font-weight: bold;
				align-items: center;
				justify-content: center;
				flex-shrink: 0;
				margin-right: 20px;
			}
			.content-text {
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				p:nth-child(1) {
					font-size: 28px;
					color: #191e3b;
				}
				p:nth-child(2) {
					font-size: 28px;
					color: #3a416e;
					text-align: left;
					margin-top: 20px;
				}
			}
		}
	}
	.tool {
		margin-top: 30px;
		display: flex;
		justify-content: center;
		span {
			display: inline-block;
			width: 320px;
			height: 96px;
			border-radius: 8px;
			background-color: #337eff;
			color: #fff;
			font-size: 36px;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			margin: 0 50px;
			&.error {
				color: #f84558;
				background-color: #fff;
				border: 2px solid #f84558;
			}
		}
	}
}
</style>
