<template>
    <div id="temporaryStorageMain">
        <headNav :show-box-index="[true, true, true, true]"></headNav>
        <div class="codeTipBox">
            <div class="codeTipContent">
                <div class="codeTipContentTopBg"></div>
                <div class="codeTipToolBox">
                    <div class="codeTipToolSm">
                        <div class="codeTipToolBoxText">扫描二维码</div>
                        <div class="codeTipToolSmBg"></div>
                    </div>
                    <div class="codeTipToolRFID">
                        <div class="codeTipToolBoxText">感应RFID标签</div>
                        <div class="codeTipToolRFIDBg"></div>
                    </div>
                </div>
                <div class="arrowBox">
                    <div class="arrowOne"></div>
                    <div class="arrowOne"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import headNav from '_c/headNav'
import scanCode from './scanCode'
export default {
    components: {
        headNav
    },
    mixins: [scanCode],
    beforeDestroy() {
        this.$Modal.remove()
    }
}
</script>
