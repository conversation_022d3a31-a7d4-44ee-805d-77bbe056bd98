const serverConfig = JSON.parse(localStorage.getItem('serverConfig'))

export default {
	data() {
		return {
			smsbVal: '',
			caseList_scan: [],
			key: {
				'48': '0',
				'49': '1',
				'50': '2',
				'51': '3',
				'52': '4',
				'53': '5',
				'54': '6',
				'55': '7',
				'56': '8',
				'57': '9',
				'65': 'A',
				'66': 'B',
				'67': 'C',
				'68': 'D',
				'69': 'E',
				'70': 'F',
				'71': 'G',
				'72': 'H',
				'73': 'I',
				'74': 'J',
				'75': 'K',
				'76': 'L',
				'77': 'M',
				'78': 'N',
				'79': 'O',
				'80': 'P',
				'81': 'Q',
				'82': 'R',
				'83': 'S',
				'84': 'T',
				'85': 'U',
				'86': 'V',
				'87': 'W',
				'88': 'X',
				'89': 'Y',
				'90': 'Z',
				'188': ',',
				'219': '{',
				'220': '\\',
				'221': '}',
				'222': '"',
				'186': ':'
			}
		}
	},
	created() {
		this.smsb()
	},
	methods: {
		// 扫码识别
		smsb() {
			this.smsbVal = ''
			this.unBindKeydown()
			const flag = this.GetOSInfo()
			$(document).keydown((e) => {
				const _code = e.keyCode + ''
				const key = this.key
				if (_code != 16 && _code != 13 && key[_code] != undefined && !(e.shiftKey && e.keyCode == 54)) {
					this.smsbVal += key[_code]
				}
				if (e.shiftKey && e.keyCode == 54) {
					this.smsbVal += '^'
				}
				if (flag && e.shiftKey && e.keyCode == 59 && this.smsbVal.substr(this.smsbVal.length - 1, 1) != ':') {
					this.smsbVal += ':'
				}
				if (_code == 13) {
					this.judgeFormat(this.smsbVal)
					this.smsbVal = ''
				}
			})
		},
		// 查询当前的操作系统
		GetOSInfo() {
			return String(navigator.platform).indexOf('Linux') > -1
		},
		judgeFormat(srnr) {
			srnr = srnr.replace(/\\000026/g, '')
			if (srnr != null && srnr != '') {
				// 根据文书ID获取案件信息
				this.getAjxxByWsid(srnr.split(',')[1])
			} else {
				this.ModalInfo('确认', 'tipsIcon2', '暂不支持该二维码格式')
				this.smsb()
			}
		},
		// 根据二维码内容获取案件信息
		getAjxxByWsid(qrContent) {
			if (!qrContent) {
				this.ModalInfo('确定', 'tipsIcon2', '请重新扫码...')
				return
			}
			const params = {
				wpid: qrContent,
				centerId: serverConfig.centerId,
				cabinetCode: serverConfig.cabinetCode
			}
			if (this.$route.path === '/scanCode') {
				this.$Post(this.PymApi.getAssignCabinet, params).then((res) => {
					if (res.success) {
						this.ModalInfo('确定', 'tipsIcon3', '扫码成功，请稍候...')
						setTimeout(() => {
							this.$Modal.remove()
							this.$router.push({
								path: '/cabinetOpenPutIn',
								query: {
									gh: res.data.gh,
									wpid: res.data.id
								}
							})
						}, 1000)
					} else {
						this.ModalInfo('确认', 'tipsIcon2', res.msg || '涉案物品未登记，请先登记！')
						this.smsb()
					}
				})
			}
		},
		unBindKeydown() {
			$(document).unbind('keydown')
		}
	},
	beforeDestroy() {
		this.unBindKeydown()
	}
}
