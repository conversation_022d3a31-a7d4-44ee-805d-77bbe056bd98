<template>
    <div class="self-put-in-file">
        <cabinet-head :cabinet-num="currentCabinetNum" :put-in="putIn" :status-code="statusCode"></cabinet-head>
        <cabinet-main :opened-cabinet-num="openedCabinetNum" :cabinet-detail="cabinetDetail" :current-cabinet-num="currentCabinetNum"></cabinet-main>
        <footer class="footer">
            <cabinet-footer :current-cabinet-num="currentCabinetNum" :put-in="putIn" :status-code="statusCode" :time="time" @clickBackHome="$router.push('/home')" @clickCancel="$router.push('/home')" @clickManual="clickManual" @clickContinue="clickContinue"></cabinet-footer>
        </footer>
    </div>
</template>

<script>
import cabinetFooter from '../components/cabinetFooter.vue'
import cabinetHead from '../components/cabinetHead.vue'
import cabinetMain from '../components/cabinetMain.vue'
import Lock from '@/libs/lock/index'
import { modalConfirm } from '@/libs/modal'
import { getGHEvent } from '@/libs/lock/util'
import { voiceBroadcast, getUserInfo } from '@/libs/utils'
const userinfo = getUserInfo()
export default {
    components: { cabinetMain, cabinetHead, cabinetFooter },
    data() {
        return {
            // 组件参数
            activeGh: '',
            statusCode: 1,
            openedCabinetNum: [],
            currentCabinetNum: '',
            cabinetDetail: [],
            putIn: true,
            wpid: '',
            retryTimes: 3,
            faultCabinetArr: [],
            time: +this.serverConfig.loginOutTime,
            lockObj: null
        }
    },
    watch: {
        currentCabinetNum: {
            async handler(newVal) {
                if (this.openedCabinetNum.findIndex((item) => item == newVal) == -1) {
                    this.openedCabinetNum.push(newVal)
                }
                this.cabinetDetail = await this.renderCabinet()
            }
        }
    },
    mounted() {
        setTimeout(() => {
            this.initParams()
        }, 500)
    },
    beforeDestroy() {
        voiceBroadcast(false)
        this.destroyedLock()
        this.$Modal.remove()
    },
    methods: {
        initLock(callback) {
            if (this.lockObj) {
                callback && callback()
                return
            }
            this.lockObj = new Lock()
            this.lockObj.on('open', (data) => {
                console.log('open', data)
                this.logger({ title: 'open-存入查锁记录-串口', value: data })
                if (data.code == 0) {
                    callback && callback()
                } else {
                    this.destroyedLock()
                    modalConfirm('重新连接', '返回首页', 'tipsIcon2', '连接锁控服务失败！', (flag) => {
                        if (!flag) {
                            this.$router.push('/home')
                            return
                        }
                        setTimeout(() => {
                            this.initLock(callback)
                        }, 1000)
                    })
                }
            })
            this.lockObj.on('error', (data) => {
                console.log('error', data)
                this.logger({ title: 'error-存入查锁记录-串口', value: data })
                this.destroyedLock()
                modalConfirm('重新连接', '返回首页', 'tipsIcon2', data.code == 1 ? '连接锁控服务失败！' : data.msg, (flag) => {
                    if (!flag) {
                        this.$router.push('/home')
                        return
                    }
                    setTimeout(() => {
                        this.initLock(callback)
                    }, 1000)
                })
            })
            this.lockObj.on('message', (data) => {
                console.log('message', data)
                this.logger({ title: 'message-存入查锁记录-串口', value: data })
                const { result, actionType, box, door } = data
                const gh = getGHEvent(box, door)
                if (!actionType) {
                    this.destroyedLock()
                    modalConfirm('重新连接', '返回首页', 'tipsIcon2', '开锁失败，请联系管理员', (flag) => {
                        if (flag) {
                            this.retryTimes = 3
                            this.statusCode = 1
                            setTimeout(() => {
                                this.initLock(this.openLock(gh))
                            }, 1000)
                        } else {
                            this.$router.push('/home')
                        }
                    })
                }
                if (actionType == 'openlock') {
                    // result：1：门开启、0：门关闭
                    if (result == 1) {
                        this.statusCode = 2
                        const index = this.faultCabinetArr.indexOf(gh)
                        if (index > -1) {
                            this.faultCabinetArr.splice(index, 1)
                        }
                        this.voiceBroadcastSuccessOpen(gh)
                        setTimeout(() => {
                            this.checkLock(gh)
                        }, 2000)
                    } else {
                        if (this.retryTimes == 0) {
                            // 重试三次失败
                            if (!this.faultCabinetArr.includes(gh)) {
                                this.faultCabinetArr.push(gh)
                                this.sendFaultInfo(gh)
                                this.voiceBroadcastFailedOpen()
                            }
                            this.destroyedLock()
                            modalConfirm('重新连接', '返回首页', 'tipsIcon2', '开锁失败，请联系管理员', (flag) => {
                                if (flag) {
                                    this.retryTimes = 3
                                    this.statusCode = 1
                                    setTimeout(() => {
                                        this.initLock(callback)
                                    }, 1000)
                                } else {
                                    this.$router.push('/home')
                                }
                            })
                            return
                        }
                        this.retryTimes--
                        this.openLock(gh)
                    }
                }
                if (actionType == 'checklock') {
                    // result：1：门开启、0：门关闭
                    if (result == 1) {
                        // 开门后检查柜门状态
                        setTimeout(() => {
                            this.checkLock(gh)
                        }, 2000)
                    }
                    if (result == 0) {
                        this.statusCode = 3
                        setTimeout(this.savePutInInfo, 1500)
                    }
                }
            })
            this.lockObj.init()
        },
        destroyedLock() {
            this.lockObj && this.lockObj.destroyed()
            this.lockObj = null
        },
        openLock(gh) {
            this.lockObj && this.lockObj.handle('openlock', gh)
        },
        checkLock(gh) {
            this.lockObj && this.lockObj.handle('checklock', gh)
        },
        initParams() {
            const { gh, wpid } = this.$route.query
            this.currentCabinetNum = gh
            this.activeGh = gh
            this.wpid = wpid
            if (this.openedCabinetNum.findIndex((item) => item === this.currentCabinetNum) == -1) {
                this.openedCabinetNum.push(this.currentCabinetNum)
            }
            this.initLock(() => {
                this.openLock(this.currentCabinetNum)
            })
        },
        /**
		 * 柜号
		 * 故障原因
		 * 中心id
		 * 柜子物品柜id
		 */
        sendFaultInfo(curCabNum) {
            const params = {
                cabinetCode: this.serverConfig.cabinetCode,
                centerId: this.serverConfig.centerId,
                gh: curCabNum,
                gzyy: '不能开柜'
            }
            this.$Post(this.PymApi.saveCabinetFault, params)
        },
        /**
		 * 保存存入信息
		 * 1.gh:柜号
		 * 2.wpid:物品ID
		 * 3.sfzh:身份证号
		 * 4.xm:姓名
		 * 5.物品柜名
		 */
        savePutInInfo() {
            const _this = this
            const params = {
                gh: this.currentCabinetNum,
                xm: userinfo.name,
                sfzh: userinfo.idCard,
                wpid: this.wpid,
                centerId: this.serverConfig.centerId,
                cabinetCode: this.serverConfig.cabinetCode
            }
            this.$Post(this.PymApi.saveDepositWpxx, params)
                .then((res) => {
                    if (res.success) {
                        _this.voiceBroadcastClosed(this.currentCabinetNum)
                        if (_this.faultCabinetArr.length > 0) {
                            _this.voiceBroadcastFailedOpen()
                            _this.$Modal.confirm({
                                title: false,
                                width: '450',
                                className: 'tipsWrap',
                                okText: '重试',
                                cancelText: '返回首页',
                                render: (h) => {
                                    return h('div', { class: 'tipsBox' }, [
                                        h(
                                            'i',
                                            {
                                                class: 'tipsIcon2'
                                            },
                                            ''
                                        ),
                                        h(
                                            'p',
                                            {
                                                class: 'tipsText'
                                            },
                                            `开锁失败，请联系管理员!`
                                        )
                                    ])
                                },
                                onOk: () => {
                                    _this.currentCabinetNum = _this.faultCabinetArr[_this.faultCabinetArr.length - 1]

                                    _this.openLock(_this.currentCabinetNum)
                                },
                                onCancel: () => {
                                    _this.$router.push('/home')
                                }
                            })
                        }
                    } else {
                        voiceBroadcast(true, '更新数据失败，请联系管理员', 'updateErr')
                        _this.ModalConfirm('重试', '返回首页', 'tipsIcon2', '更新数据失败，请联系管理员', (val) => {
                            if (val) {
                                setTimeout(() => {
                                    _this.savePutInInfo()
                                }, 1500)
                            } else {
                                _this.$router.replace('/home')
                            }
                        })
                    }
                })
                .catch((rej) => {
                    voiceBroadcast(true, '更新数据失败，请联系管理员', 'updateErr')
                    _this.ModalConfirm('重试', '返回首页', 'tipsIcon2', '更新数据失败，请联系管理员', (val) => {
                        if (val) {
                            setTimeout(() => {
                                _this.savePutInInfo()
                            }, 1500)
                        } else {
                            _this.$router.replace('/home')
                        }
                    })
                })
        },

        // 开门成功
        voiceBroadcastSuccessOpen(curCabNum) {
            const tips = `请将物品存到${curCabNum}号柜子`

            voiceBroadcast(true, tips, 'open_putIn')
        },
        // 开门失败
        voiceBroadcastFailedOpen() {
            const tips = `开锁失败,请联系管理员`
            voiceBroadcast(true, tips, 'openLockFailed')
        },
        // 关门（存入成功）
        voiceBroadcastClosed(curCabNum) {
            const tips = `${curCabNum}号柜子存入物品成功`
            voiceBroadcast(true, tips, 'putInSuccess')
        },
        renderCabinet() {
            return new Promise((resolve, reject) => {
                const cabinetArr = JSON.parse(localStorage.getItem('SIDES'))
                const curRendCabinet = ['A']
                if (cabinetArr.length && cabinetArr.length > 1) {
                    const sideCabinetId = this.activeGh.substring(0, 1) == 'A' ? cabinetArr.find((item) => item.sideCode != 'A').sideCode : this.activeGh.substring(0, 1)
                    curRendCabinet.push(sideCabinetId)
                }

                const result = []
                curRendCabinet.forEach(async(item) => {
                    const sideId = cabinetArr.find((item2) => item2.sideCode === item).id
                    const params = {
                        sideId: sideId
                    }
                    const res = await this.$Post(this.PymApi.GET_SIDEDETAIL, params)

                    result.push({
                        ...res,
                        sideCode: item,
                        orderId: item === 'A' ? 1 : 2
                    })
                    result.sort((a, b) => {
                        return a.orderId - b.orderId
                    })
                })
                resolve(result)
            })
        },
        clickContinue() {
            if (this.$route.path == '/cabinetOpenPutIn') {
                this.$router.replace('/scanCode')
            } else if (this.$route.path == '/cabinetOpenTakeOut') {
                this.$router.replace('/selfbatchTakeOut')
            }
        },
        clickManual() {
            this.openLock(this.currentCabinetNum)
        }
    }
}
</script>

<style lang="less" scoped>
.footer {
	height: 250px;
	width: 940px;
	margin: 110px auto 0 auto;
}
</style>
