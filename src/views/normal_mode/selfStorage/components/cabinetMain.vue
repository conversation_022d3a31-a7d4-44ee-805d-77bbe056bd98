<template>
    <div class="cabinet-main" :style="{ 'justify-content': cabinetArr.length > 1 ? 'space-between' : 'space-around' }">
        <div v-for="item in cabinetArr" :key="item.sideCode" class="cabinet">
            <div class="title">{{ item.sideCode == 'A' ? '主柜' : item.sideCode + '柜' }}</div>
            <div class="cabinet-body">
                <div v-for="(item1, index) in item.positionArr" :key="index + 'col'" class="col" :style="{ width: 100 / item.positionArr.length + '%' }">
                    <div v-for="(item2, index2) in item1.row" :key="index2 + 'row'" :class="{ row: true, screen: item2.num === '000', active: item2.open, activeAnimation: currentCabinetNum == item2.num }" :style="{ flex: ' 1 ' + ' 0 ' + item2.flexBasis * 100 + '%' }">
                        <div :class="{ active: item2.open, activeAnimation: currentCabinetNum == item2.num }">{{ item2.open ? item2.num : '' }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'CabinetMain',
    props: {
        currentCabinetNum: {
            require: true,
            type: String
        },
        openedCabinetNum: {
            require: true,
            type: Array
        },
        cabinetDetail: {
            require: true,
            type: Array
        }
    },
    data() {
        return {
            cabinetArr: [],
            titleSpan: 0
        }
    },
    watch: {
        cabinetDetail: {
            handler(newVal) {
                this.init()
            }
        }
    },
    methods: {
        init() {
            this.cabinetArr = []
            this.cabinetDetail.map((item) => {
                const obj = {
                    sideCode: item.sideCode,
                    positionArr: []
                }
                obj.positionArr = this.dealCabinetDetail(item)
                this.cabinetArr.push(obj)
                console.log(this.cabinetArr)
            })
        },
        dealCabinetDetail(data) {
            const colData = []
            data.details?.map((item) => {
                this.openedCabinetNum.indexOf(item.num) > -1 ? (item.open = true) : (item.open = false)
                if (colData.indexOf(item.colPos) == -1) colData.push(item.colPos)
            })
            colData.sort((a, b) => a - b)
            const posData = []

            colData.map((col) => {
                const cabinetData = {
                    col
                }
                cabinetData.row = data.details.filter((item) => item.colPos == col)
                const totalMerges = cabinetData.row.reduce((newVal, item) => {
                    return Number(newVal) + Number(item.merge)
                }, 0)
                console.log('totalMerges', totalMerges)
                cabinetData.row.forEach((item) => {
                    item.flexBasis = item.merge / totalMerges
                })
                posData.push(cabinetData)
            })
            this.titleSpan = colData.length
            return posData
        }
    }
}
</script>

<style lang="less" scoped>
@assets: '../../../../assets/images';
.cabinet-main {
	width: 840px;
	height: 712px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 40px auto;
	.cabinet {
		width: calc(50% - 16px);
		.title {
			width: 100%;
			height: 60px;
			background: #4d5f80;
			margin-bottom: 4px;
			color: #fff;
			font-size: 28px;
			line-height: 60px;
			text-align: center;
		}
		.cabinet-body {
			width: 100%;
			height: 712px;
			display: flex;
			justify-content: space-between;
			margin: 0 auto;
			.col {
				height: 100%;
				flex: 1;
				margin-right: 4px;
				display: flex;
				justify-content: space-between;
				flex-direction: column;
				&:last-child {
					margin-right: 0;
				}
				.row {
					flex: 1;
					background-color: #5c8ae6;
					// margin-bottom: 4px;
					border-bottom: 4px solid #fff;
					div {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;
						height: 100%;
						font-size: 32px;
						color: #fff;
						&.active {
							background-color: #e6ac00;
							animation: borderColor 1s infinite alternate;
                            line-height: 1;
                            box-sizing: border-box;
						}
						// &.activeAnimation {
						//   animation: borderColor 1s infinite  alternate;
						// }
					}
					&.screen {
						display: flex;
						justify-content: center;
						align-items: flex-start;
						background-color: #99d5ff;
						div {
							display: block;
							width: 100%;
							height: 50%;
							background: url('@{assets}/zg_icon.png') no-repeat;
							background-size: 100% 100%;
							background-position: 0 0;
							border-bottom: 2px solid #77b8e6;
						}
					}
                    &:last-of-type.screen {
                            border: none;
                        }
					// &.active{
					//   background-color: #E6AC00;
					//   animation: borderColor 1s infinite  alternate;
					// }
				}
			}
		}
	}
}

@keyframes borderColor {
	from {
		border: 4px solid #e6ac00;
	}

	to {
		border: 4px solid #cc8e0f;
	}
}
</style>
