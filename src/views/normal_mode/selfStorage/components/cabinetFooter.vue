<template>
    <div class="cabinet-footer clearfix">
        <div class="opened-text">
            <template v-if="statusCode === 2 && !putIn">
                <span class="current-cabinet-num">{{ currentCabinetNum }}</span>
                号柜门已打开，请您留意！
                <button class="cancel" @click="clickCancel">取消</button>
            </template>
            <template v-if="statusCode === 3 && showMoreBtn">
                <button class="manual-open" @click="clickManual">手动开柜</button>
                <button class="continue" @click="clickContinue">继续{{ putIn ? '存入' : '取出' }}</button>
                <button class="back-home" @click="clickBackHome">返回首页({{ restTime }}秒)</button>
            </template>
            <template v-if="showBackHome">
                <button class="back-home back-home-replay" @click="clickBackHome">返回首页({{ restTime }}秒)</button>
            </template>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        /**
		 * 当前正在操作柜号
		 */
        currentCabinetNum: {
            require: true,
            type: String
        },
        /**
		 * 门开关
		 * 1：正在开门
		 * 2：门已打开
		 * 3：门已关闭
		 */
        statusCode: {
            require: true,
            type: Number
        },
        /**
		 * 存入或取出
		 * true:存入
		 * false:取出
		 */
        putIn: {
            require: true,
            type: Boolean
        },
        time: {
            require: true,
            type: Number
        },
        showMoreBtn: {
            require: true,
            type: Boolean,
            default() {
                return true
            }
        },
        showBackHome: {
            require: false,
            type: Boolean,
            default() {
                return false
            }
        }
        /**
		 * 状态字
		 * 1：未取完
		 * 2：已取完
		 */
        // stepCode: {
        //   require: true,
        //   type: Number,
        // },
    },
    data() {
        return {
            restTime: this.time,
            timer: null
        }
    },
    watch: {
        statusCode: {
            handler(newVal) {
                this.timer && clearInterval(this.timer)
                this.timer = null
                this.restTime = this.time
                if (newVal === 3) {
                    this.timeOut()
                }
            },
            immediate: true
        }
    },
    beforeDestroy() {
        this.timer && clearInterval(this.timer)
        this.timer = null
    },
    methods: {
        clickCancel() {
            this.$emit('clickCancel')
        },
        clickManual() {
            this.$emit('clickManual')
        },
        clickContinue() {
            this.$emit('clickContinue')
        },
        clickBackHome() {
            this.$emit('clickBackHome')
        },
        timeOut() {
            this.timer = setInterval(() => {
                this.restTime--
                if (!this.restTime) {
                    clearInterval(this.timer)
                    this.timer = null
                    this.$emit('clickBackHome')
                }
            }, 1000)
        }
    }
}
</script>

<style lang="less" scoped>
.cabinet-footer {
	width: 100%;
	height: 250px;
	text-align: center;
	button {
		border: none;
		outline: none;
		color: #fff;
	}
	.opened-text {
		font-size: 32px;
		line-height: 60px;
		.current-cabinet-num {
			font-size: inherit;
			line-height: inherit;
		}
	}
	.manual-open,
	.cancel {
		margin-top: 20px;
		background-color: #e6ac00;
		width: 264px;
		height: 64px;
		border-radius: 32px;
		font-size: 24px;
		line-height: 30px;
		display: block;
		margin-left: 50%;
		transform: translateX(-50%);
	}
	.continue {
		border: 1px solid #337eff;
		background-color: #fff;
		margin-top: 30px;
		width: 320px;
		height: 96px;
		font-size: 36px;
		line-height: 40px;
		color: #337eff;
		border-radius: 8px;
		float: left;
	}
	.back-home {
		margin-top: 30px;
		float: right;
		width: 320px;
		height: 96px;
		border: 1px solid #fff;
		background-color: #337eff;
		font-size: 36px;
		line-height: 40px;
		color: #fff;
		border-radius: 8px;
        &.back-home-replay {
            float: none;
        }
	}
}
</style>
