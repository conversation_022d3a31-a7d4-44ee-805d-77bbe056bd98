<template>
    <div class="cabinet-open-head">
        <div
            :class="{
                'status-icon': true,
                'opening-icon': statusCode === 1,
                'opened-icon': statusCode === 2,
                'closed-icon': statusCode === 3
            }"
        ></div>
        <div
            :class="{
                'status-text': true,
                'opening-text': statusCode === 1,
                'opened-text': statusCode === 2,
                'closed-text': statusCode === 3
            }"
        >
            {{ statusCode === 1 ? '正在打开柜门' : statusCode === 2 ? '柜门已开启' : '柜门已关闭' }}
        </div>
        <div class="tips-text">
            <span class="cabinet-num">{{ cabinetNum }}</span>
            号柜子{{ statusCode === 1 ? '正在开启，请稍作等待……' : statusCode === 2 ? '已开启，请' + (putIn ? '存入物品并关闭柜门。' : '取出物品并关闭柜门。') : putIn ? '存入物品成功。' : '取出物品成功。' }}
        </div>
    </div>
</template>

<script>
export default {
    props: {
        statusCode: {
            require: true,
            type: Number
        },
        cabinetNum: {
            require: true,
            type: String
        },
        putIn: {
            require: true,
            type: Boolean
        }
    }
}
</script>

<style lang="less" scoped>
@assets: '../../../../assets/images';
.status-icon {
	display: inline-block;
	margin-left: 50%;
	transform: translateX(-50%);
	height: 120px;
	width: 120px;
	margin: 50px auto 50px 50%;
}
.opening-icon {
	background: url('@{assets}/loading1.png') no-repeat;
	background-size: 100% 100%;
	background-position: 0 0;
}
.opened-icon {
	background: url('@{assets}/laoding2.png') no-repeat;
	background-size: 100% 100%;
	background-position: 0 0;
}
.closed-icon {
	background: url('@{assets}/closeIcon.png') no-repeat;
	background-size: 100% 100%;
	background-position: 0 0;
}

.status-text {
	width: 100%;
	font-size: 36px;
	font-weight: 700;
	text-align: center;
	color: #56cd45;
	line-height: 27px;
	margin-bottom: 64px;
	&.opening-text {
		color: #33a0ff;
	}
	// &.opened-text {

	// }
	&.closed-text {
		color: #33a0ff;
	}
}

.tips-text {
	text-align: center;
	font-size: 40px;
	.cabinet-num {
		font-size: 40px;
		font-weight: 700;
		line-height: 27px;
		color: #4f5eff;
	}
}
</style>
