<template>
	<div class="config-wrap">
		<network-config ref="networkConfigRef" :params="{ isWeb: false }"></network-config>
		<div class="x-dialog-footer">
			<gxx-button type="info" @click="cancel">取消</gxx-button>
			<gxx-button @click="confirm">确定</gxx-button>
		</div>
	</div>
</template>

<script>
import NetworkConfig from '@/components/config/network'
export default {
	name: 'Net',
	components: {
		NetworkConfig
	},
	methods: {
		cancel() {
			this.$parent.close('cancel')
		},
		confirm() {
			const networkConfigRef = this.$refs.networkConfigRef
			const networkValid = networkConfigRef.validate('confirm')
			if (!networkValid) {
				return
			}
			this.$GxxMessage({ message: '配置成功！', type: 'success' })
			this.$emit('getReturn', networkConfigRef.configParams)
			this.$parent.close('confirm')
		}
	}
}
</script>

<style lang="less" scoped>
.config-wrap {
	width: 700px;
	/deep/ .config-module {
		margin-top: 20px;
		.module-content {
			.content-row {
				.row-label {
					width: 105px;
				}
			}
		}
	}
	.x-dialog-footer {
		margin: 20px 0;
		padding: 0;
		button {
			font-size: 16px;
			height: 40px;
			width: auto;
			line-height: 1;
		}
	}
}
</style>
