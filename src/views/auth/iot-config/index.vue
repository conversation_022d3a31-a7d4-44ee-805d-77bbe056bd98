<template>
	<div class="config-wrap">
		<iot-config ref="iotConfigRef" :params="{ isWeb: false }"></iot-config>
		<div class="x-dialog-footer">
			<gxx-button type="info" @click="cancel">取消({{ time }}s)</gxx-button>
			<gxx-button @click="confirm">确定</gxx-button>
		</div>
	</div>
</template>

<script>
import IotConfig from '@/components/config/iot'
export default {
	name: 'Iot',
	components: {
		IotConfig
	},
	data() {
		return {
			timer: null,
			time: 120
		}
	},
	created() {
		this.createdTimer()
	},
	methods: {
		createdTimer() {
			this.timer = setInterval(() => {
				if (this.time > 0) {
					this.time--
				} else {
					this.cancel()
				}
			}, 1000)
		},
		clearTimer() {
			this.timer && clearInterval(this.timer)
			this.timer = null
		},
		cancel() {
			this.clearTimer()
			this.$parent.close('cancel')
		},
		confirm() {
			const iotConfigRef = this.$refs.iotConfigRef
			const iotValid = iotConfigRef.validate('confirm')
			if (!iotValid) {
				return
			}
			this.$GxxMessage({ message: '配置成功！', type: 'success' })
			this.clearTimer()
			this.$emit('getReturn', iotConfigRef.configParams)
			this.$parent.close('confirm')
		}
	},
	beforeDestroy() {
		this.clearTimer()
	}
}
</script>

<style lang="less" scoped>
.config-wrap {
	width: 450px;
	/deep/ .config-module {
		margin-top: 20px;
		.module-content {
			.content-row {
				min-width: 100%;
				margin-left: 0 !important;
				.row-label {
					width: 105px;
				}
			}
		}
	}
	.x-dialog-footer {
		margin: 20px 0;
		padding: 0;
		button {
			font-size: 16px;
			height: 40px;
			width: auto;
			line-height: 1;
		}
	}
}
</style>
