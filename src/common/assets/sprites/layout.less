
@sprite-layout-out_hover: 0px 0px 18px 18px;
@sprite-layout-out_normal: -28px 0px 18px 18px;
@sprite-layout-user_hover: 0px -28px 18px 18px;
@sprite-layout-user_normal: -28px -28px 18px 18px;
@sprite-layout-version_hover: -56px 0px 18px 18px;
@sprite-layout-version_normal: -56px -28px 18px 18px;

@sprite-layout-width: 74px;
@sprite-layout-height: 46px;
@sprite-layout-image: '~@/common/assets/sprites/layout.png';

.sprite-width(@sprite) {
  width: extract(@sprite, 3);
}

.sprite-height(@sprite) {
  height: extract(@sprite, 4);
}

.sprite-position(@sprite) {
  @sprite-offset-x: extract(@sprite, 1);
  @sprite-offset-y: extract(@sprite, 2);
  background-position: @sprite-offset-x @sprite-offset-y;
}

.sprite-layout-image() {
  background-image: e(%('url(%a)', e(@sprite-layout-image)));
  background-repeat: no-repeat;
  display: inline-block;
  vertical-align: middle;
}

.sprite-layout(@sprite) {
  .sprite-layout-image();
  .sprite-position(@sprite);
  .sprite-width(@sprite);
  .sprite-height(@sprite);
}

.sprite-layout-out_hover() {
	.sprite-layout(@sprite-layout-out_hover)
}
.sprite-layout-out_normal() {
	.sprite-layout(@sprite-layout-out_normal)
}
.sprite-layout-user_hover() {
	.sprite-layout(@sprite-layout-user_hover)
}
.sprite-layout-user_normal() {
	.sprite-layout(@sprite-layout-user_normal)
}
.sprite-layout-version_hover() {
	.sprite-layout(@sprite-layout-version_hover)
}
.sprite-layout-version_normal() {
	.sprite-layout(@sprite-layout-version_normal)
}
