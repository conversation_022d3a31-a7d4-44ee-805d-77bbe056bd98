
@sprite-menu-leftbar_config: 0px 0px 18px 18px;
@sprite-menu-leftbar_device: -28px 0px 18px 18px;
@sprite-menu-leftbar_info: 0px -28px 18px 18px;
@sprite-menu-leftbar_maintain: -28px -28px 18px 18px;
@sprite-menu-leftbar_network: -56px 0px 18px 18px;

@sprite-menu-width: 74px;
@sprite-menu-height: 46px;
@sprite-menu-image: '~@/common/assets/sprites/menu.png';

.sprite-width(@sprite) {
  width: extract(@sprite, 3);
}

.sprite-height(@sprite) {
  height: extract(@sprite, 4);
}

.sprite-position(@sprite) {
  @sprite-offset-x: extract(@sprite, 1);
  @sprite-offset-y: extract(@sprite, 2);
  background-position: @sprite-offset-x @sprite-offset-y;
}

.sprite-menu-image() {
  background-image: e(%('url(%a)', e(@sprite-menu-image)));
  background-repeat: no-repeat;
  display: inline-block;
  vertical-align: middle;
}

.sprite-menu(@sprite) {
  .sprite-menu-image();
  .sprite-position(@sprite);
  .sprite-width(@sprite);
  .sprite-height(@sprite);
}

.sprite-menu-leftbar_config() {
	.sprite-menu(@sprite-menu-leftbar_config)
}
.sprite-menu-leftbar_device() {
	.sprite-menu(@sprite-menu-leftbar_device)
}
.sprite-menu-leftbar_info() {
	.sprite-menu(@sprite-menu-leftbar_info)
}
.sprite-menu-leftbar_maintain() {
	.sprite-menu(@sprite-menu-leftbar_maintain)
}
.sprite-menu-leftbar_network() {
	.sprite-menu(@sprite-menu-leftbar_network)
}
