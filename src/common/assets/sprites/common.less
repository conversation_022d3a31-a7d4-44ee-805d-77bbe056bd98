
@sprite-common-add: 0px 0px 18px 18px;

@sprite-common-width: 18px;
@sprite-common-height: 18px;
@sprite-common-image: '~@/common/assets/sprites/common.png';

.sprite-width(@sprite) {
  width: extract(@sprite, 3);
}

.sprite-height(@sprite) {
  height: extract(@sprite, 4);
}

.sprite-position(@sprite) {
  @sprite-offset-x: extract(@sprite, 1);
  @sprite-offset-y: extract(@sprite, 2);
  background-position: @sprite-offset-x @sprite-offset-y;
}

.sprite-common-image() {
  background-image: e(%('url(%a)', e(@sprite-common-image)));
  background-repeat: no-repeat;
  display: inline-block;
  vertical-align: middle;
}

.sprite-common(@sprite) {
  .sprite-common-image();
  .sprite-position(@sprite);
  .sprite-width(@sprite);
  .sprite-height(@sprite);
}

.sprite-common-add() {
	.sprite-common(@sprite-common-add)
}
