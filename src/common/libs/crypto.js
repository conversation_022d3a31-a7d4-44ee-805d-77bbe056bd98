import CryptoJS from 'crypto-js'
import { aesSecretKey, aesSecretIv } from '@/config'
const key = CryptoJS.enc.Utf8.parse(aesSecretKey)
const iv = CryptoJS.enc.Utf8.parse(aesSecretIv)

export default {
	/**
	 * AES加密算法
	 * @param {String} str
	 * @returns {String}
	 */
	aesEncrypt(str) {
		const srcs = CryptoJS.enc.Utf8.parse(str)
		const encrypted = CryptoJS.AES.encrypt(srcs, key, { iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
		return encrypted.ciphertext.toString().toUpperCase()
	},
	/**
	 * AES解密算法
	 * @param {String} str
	 * @returns {String}
	 */
	aesDecrypt(str) {
		const encryptedHexStr = CryptoJS.enc.Hex.parse(str)
		const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
		const decrypt = CryptoJS.AES.decrypt(srcs, key, { iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
		const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
		return decryptedStr.toString()
	}
}
