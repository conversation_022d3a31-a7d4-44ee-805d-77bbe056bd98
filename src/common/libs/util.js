// 创建柜号列表
export function createCabinetList() {
	return Array.from({ length: 26 }, (_, i) => {
		const letter = String.fromCharCode(65 + i)
		return { value: letter, label: letter }
	})
}

/**
 * <AUTHOR> @description 判断是否为数字
 * @param value
 * @returns {boolean}
 */
export function isNumber(value) {
	const reg = /^[0-9]*$/
	return reg.test(value)
}

/**
 * <AUTHOR> @description 判断是否是身份证号(第二代)
 * @param str
 * @returns {boolean}
 */
export function isIdCard(str) {
	const reg = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
	return reg.test(str)
}

/**
 * <AUTHOR> @description 判断是否为IP
 * @param ip
 * @returns {boolean}
 */
export function isIP(ip) {
	const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
	return reg.test(ip)
}

/**
 * <AUTHOR> @description 判断是否是端口号
 * @param str
 * @returns {boolean}
 */
export function isPort(str) {
	const reg = /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/
	return reg.test(str)
}

/**
 * <AUTHOR> @description 判断是否是IP:端口号
 * @param str
 * @returns {boolean}
 */
export function isPortAndPort(str) {
	const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/
	return reg.test(str)
}

/**
 * <AUTHOR> @description 判断是否为空
 * @param str
 * @returns {boolean}
 */
export function isBlank(str) {
	return str == null || false || str === '' || str.trim() === '' || str.toLocaleLowerCase().trim() === 'null'
}

/**
 * 语音播报方法
 * @param {string}} text
 */
export function voiceBroadcast(text) {
	const msg = new SpeechSynthesisUtterance(text)
	msg.volume = 1
	msg.rate = 1
	msg.pitch = 1.5
	window.speechSynthesis.speak(msg)
}

/**
 * 深度拷贝数据
 * @param {Object} obj
 */
export function deepClone(obj) {
	return new Promise((resolve) => {
		const { port1, port2 } = new MessageChannel()
		port1.postMessage(obj)
		port2.onmessage = (msg) => {
			resolve(msg.data)
		}
	})
}

/**
 * @description: 时间格式化方法
 * @param {Date} date
 * @param {String} format
 * @returns {String}
 */
export function formatDate(date = new Date(), format = 'YYYY-MM-DD HH:mm:ss') {
	const map = {
		YYYY: date.getFullYear(),
		MM: date.getMonth() + 1,
		DD: date.getDate(),
		HH: date.getHours(),
		mm: date.getMinutes(),
		ss: date.getSeconds()
	}

	return format.replace(/(YYYY|MM|DD|HH|mm|ss)/g, function (match) {
		return String(map[match]).length >= 2 ? map[match] : `0${map[match]}`.slice(-2)
	})
}

/**
 * 处理摄像人旋转
 * @param {Object} videoStyle video样式，容器宽高
 * @param {String} videoType 摄像头选择类型
 * @param {String} canvasDom canvas dom元素
 * @param {String}  videoDom 摄像头流dom元素
 */
export function handleCameraCof(videoStyle, videoType, canvasDom, videoDom) {
	const canvas = document.getElementById(canvasDom)
	if (canvas === null) {
		return canvas
	}
	const context = canvas.getContext('2d')
	canvas.width = videoStyle.width
	canvas.height = videoStyle.height
	if (videoType.rotateY && videoType.rotateX) {
		context.scale(-1, 1)
		context.rotate((-90 * Math.PI) / 180)
		context.drawImage(document.getElementById(videoDom), -videoStyle.height, -videoStyle.width, videoStyle.height, videoStyle.width)
	} else if (videoType.rotateY && !videoType.rotateX) {
		context.scale(-1, 1)
		context.drawImage(document.getElementById(videoDom), -videoStyle.width, 0, videoStyle.width, videoStyle.height)
	} else if (!videoType.rotateY && videoType.rotateX) {
		context.translate(canvas.width * 0.5, canvas.height * 0.5)
		context.rotate(videoType.rotateX * Math.PI)
		context.drawImage(document.getElementById(videoDom), -canvas.height / 2, -canvas.width / 2, canvas.height, canvas.width)
	} else {
		context.drawImage(document.getElementById(videoDom), 0, 0, videoStyle.width, videoStyle.height)
	}
	return canvas
}

/**
 * 获取设备信息
 */
export function getMediaDevices(handle) {
	return new Promise((resolve) => {
		if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
			resolve([])
		} else {
			navigator.mediaDevices
				.enumerateDevices()
				.then((devices) => {
					let deviceList = []
					if (handle) {
						devices.forEach((item) => {
							if (handle(item)) {
								deviceList.push(item)
							}
						})
					} else {
						deviceList = devices.filter((item) => {
							return item.kind == 'videoinput'
						})
					}
					resolve(deviceList)
				})
				.catch((err) => {
					console.log('获取摄像头设备失败，err:', err)
					resolve([])
				})
		}
	})
}
/**
 * 延时器
 */
export function delay(time = 1000) {
	return new Promise((resolve) => {
		let timer = setTimeout(() => {
			clearTimeout(timer)
			timer = null
			resolve()
		}, time)
	})
}
/**
 * 防抖函数
 * @param {Function} fn
 * @param {Number} delay
 * @returns
 */
export function throttle(fn, delay = 1000) {
	let timer = null
	return function (...args) {
		if (timer) return
		timer = setTimeout(() => {
			clearTimeout(timer)
			timer = null
			fn.apply(this, args)
		}, delay)
	}
}
/**
 * 节流函数
 */
export function debounce(fn, delay = 1000) {
	let timer = null
	return function (...args) {
		if (timer) clearTimeout(timer)
		timer = setTimeout(() => {
			clearTimeout(timer)
			timer = null
			fn.apply(this, args)
		}, delay)
	}
}

/**
 * 比较版本号
 * @param {String} currentVersion 当前版本号
 * @param {String} upgradeVersion 更新版本号
 * @returns {Number} 大于0 升级
 */
export function versionCompare(currentVersion, upgradeVersion) {
	const regexp = /^((\d\.+){3,})+((\d{4}\.\d{2}\.\d{2}\.\d{4})$)/
	if (!regexp.test(currentVersion) || !regexp.test(upgradeVersion)) {
		return 0
	}
	const lenDiff = replaceDot(upgradeVersion).length - replaceDot(currentVersion).length
	function handleVersion(version) {
		const versionArr = version.split('.')
		for (let i = 0; i < lenDiff; i++) {
			versionArr.splice(i + 3, 0, '0')
		}
		version = versionArr.join('.')
		return version
	}
	if (lenDiff != 0) {
		if (lenDiff > 0) {
			currentVersion = handleVersion(currentVersion)
		} else {
			upgradeVersion = handleVersion(upgradeVersion)
		}
	}
	let prev = replaceDot(currentVersion)
	let last = replaceDot(upgradeVersion)
	const maxLen = Math.max(prev.length, last.length)
	if (prev.length < maxLen) {
		prev += '0'
	}
	if (last.length < maxLen) {
		last += '0'
	}
	return Number(last) - Number(prev)
}
function replaceDot(str) {
	return str.replace(/\./g, '')
}
//改写柜号累加器
export function convertCabinetCodesToIndices(flag, input, lockInfo) {
	if (!flag) {
		return input
	}
	if (!input) {
		return null
	}
	if (typeof input === 'string' && input.includes(',')) {
		return input
			.split(',')
			.map((item) => convertCabinetCodeToIndex(item.trim(), lockInfo))
			.filter((result) => result !== null)
			.join(',')
	}
	return convertCabinetCodeToIndex(input, lockInfo)
}
function convertCabinetCodeToIndex(input, lockInfo) {
	const prefix = input[0]
	const suffix = parseInt(input.slice(1), 10)
	let cumulativeSum = 0
	for (const key in lockInfo) {
		const lock = lockInfo[key]
		const maxNumArr = lock.maxNum.split(',').map((num) => parseInt(num, 10))

		if (key === prefix) {
			const result = cumulativeSum + suffix
			return padZero(result)
		}
		cumulativeSum += maxNumArr.reduce((sum, num) => sum + num, 0)
	}
	return null
}
function padZero(number) {
	return number < 10 ? `0${number}` : `${number}`
}
