import { systemPlatformStr } from '@/config'

// 波特率配置
export const baudRateOptions = [
	{
		label: 110,
		value: 110
	},
	{
		label: 300,
		value: 300
	},
	{
		label: 600,
		value: 600
	},
	{
		label: 1200,
		value: 1200
	},
	{
		label: 2400,
		value: 2400
	},
	{
		label: 4800,
		value: 4800
	},
	{
		label: 9600,
		value: 9600
	},
	{
		label: 14400,
		value: 14400
	},
	{
		label: 19200,
		value: 19200
	},
	{
		label: 38400,
		value: 38400
	},
	{
		label: 56000,
		value: 56000
	},
	{
		label: 57600,
		value: 57600
	},
	{
		label: 115200,
		value: 115200
	},
	{
		label: 128000,
		value: 128000
	},
	{
		label: 256000,
		value: 256000
	}
]
// 二维码配置
export const qrCodeManufacturerDic = {
	USB: 1,
	COM: 2
}
export const qrCodeManufacturerOptions = [
	{
		label: 'USB',
		value: qrCodeManufacturerDic.USB
	},
	{
		label: '串口',
		value: qrCodeManufacturerDic.COM,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'COM11' : '',
			baudRate: 9600
		}
	}
]
// 锁控配置
export const lockManufacturerDic = {
	K32_S: 1,
	J88_S: 2,
	D99_S: 3,
	K32_W: 4
}
export const lockManufacturerOptions = [
	{
		label: 'GXX-K32-1-s',
		value: lockManufacturerDic.K32_S,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'COM3' : '/dev/ttyS3',
			baudRate: 9600
		}
	},
	{
		label: 'GXX-J88-1-s',
		value: lockManufacturerDic.J88_S,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'COM3' : '/dev/ttyS0',
			baudRate: 57600
		}
	},
	{
		label: 'GXX-D99-1-s',
		value: lockManufacturerDic.D99_S,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'COM1' : '/dev/ttysWK0',
			baudRate: 9600
		}
	},
	{
		label: 'GXX-K32-1-w',
		value: lockManufacturerDic.K32_W,
		config: {
			// eslint-disable-next-line no-undef
			path: `ws://localhost:${SYSTEM_PLATFORM != systemPlatformStr.linux ? 24245 : 8540}/locker`
		}
	}
]
export function createCabinetList() {
	return Array.from({ length: 26 }, (_, i) => {
		const letter = String.fromCharCode(65 + i)
		return { value: letter, label: letter }
	})
}
export function createNumberList(num = 0) {
	return Array.from({ length: num }, (_, i) => {
		return { value: i + 1, label: i + 1 }
	})
}
// RFID配置
export const rfidManufacturerDic = {
	K32_1: 1,
	K32_2: 2,
	D99_1: 3,
	D99_2: 4
}
export const rfidManufacturerOptions = [
	{
		label: 'GXX-K32-1-w',
		value: rfidManufacturerDic.K32_1,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'ws://127.0.0.1:21233/rfid' : 'ws://127.0.0.1:21233/Fre'
		}
	},
	{
		label: 'GXX-K32-2-w',
		value: rfidManufacturerDic.K32_2,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'ws://127.0.0.1:21232/highrfid' : 'ws://localhost:21232/highFre',
			address: '127.0.0.1:4001'
		}
	},
	{
		label: 'GXX-D99-1-s',
		value: rfidManufacturerDic.D99_1,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'COM4' : '/dev/ttyS1',
			baudRate: 19200,
			address: 0
		}
	},
	{
		label: 'GXX-D99-2-s',
		value: rfidManufacturerDic.D99_2,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'COM4' : '/dev/ttyS1',
			baudRate: 115200,
			address: 0
		}
	}
]
export const rfidManufacturerK32Light = {
	GREEN: 'Green',
	RED: 'Red',
	ORANGE: 'Orange'
}
export const k32LightColorOptions = [
	{
		label: rfidManufacturerK32Light.GREEN,
		value: rfidManufacturerK32Light.GREEN
	},
	{
		label: rfidManufacturerK32Light.RED,
		value: rfidManufacturerK32Light.RED
	},
	{
		label: rfidManufacturerK32Light.ORANGE,
		value: rfidManufacturerK32Light.ORANGE
	}
]
export const rfidManufacturerD99Light = {
	NONE: '00',
	GREEN: '01',
	RED: '02',
	GREEN_TWINKLE: '03',
	RED_TWINKLE: '04',
	YELLOW: '05',
	YELLOW_TWINKLE: '06'
}
export const d99LightColorOptions = [
	{
		label: '不亮灯',
		value: rfidManufacturerD99Light.NONE
	},
	{
		label: '绿灯',
		value: rfidManufacturerD99Light.GREEN
	},
	{
		label: '红灯',
		value: rfidManufacturerD99Light.RED
	},
	{
		label: '绿灯闪烁',
		value: rfidManufacturerD99Light.GREEN_TWINKLE
	},
	{
		label: '红灯闪烁',
		value: rfidManufacturerD99Light.RED_TWINKLE
	},
	{
		label: '黄灯常亮',
		value: rfidManufacturerD99Light.YELLOW
	},
	{
		label: '黄灯闪烁',
		value: rfidManufacturerD99Light.YELLOW_TWINKLE
	}
]
export function createColorList() {
	const colorList = []
	for (let i = 0; i <= 255; i++) {
		colorList.push({ value: i, label: String(i) })
	}
	return colorList
}
export function createChannelsList() {
	const channelsList = []
	for (let i = 0; i <= 50; i = i + 2) {
		channelsList.push({ value: i, label: i })
	}
	return channelsList
}
// 补光灯配置
export const lightManufacturerDic = {
	K32_S: 1,
	SELF_DEVELOP: 2,
	D99_S: 3,
	K32_W: 4
}
export const lightManufacturerOptions = [
	{
		label: 'GXX-K32-s',
		value: lightManufacturerDic.K32_S,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'COM3' : '/dev/ttyS3',
			baudRate: 9600
		}
	},
	{
		label: '自研',
		value: lightManufacturerDic.SELF_DEVELOP,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'COM3' : '/dev/ttyS0',
			baudRate: 57600
		}
	},
	{
		label: 'GXX-D99-s',
		value: lightManufacturerDic.D99_S,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'COM1' : '/dev/ttysWK0',
			baudRate: 9600
		}
	},
	{
		label: 'GXX-K32-w',
		value: lightManufacturerDic.K32_W,
		config: {
			// eslint-disable-next-line no-undef
			path: `ws://localhost:${SYSTEM_PLATFORM != systemPlatformStr.linux ? 24246 : 8540}/light`
		}
	}
]
// 人脸算法配置
export const photoshopModeDic = {
	NONE: 0,
	FACE_1: 1
}
export const photoshopModeOptions = [
	{
		value: photoshopModeDic.NONE,
		label: '无'
	},
	{
		value: photoshopModeDic.FACE_1,
		label: 'GXX-FACE-1'
	}
]
// 摄像头旋转角度配置
export const cameraRotateDic = {
	ROTATE_0: 0,
	ROTATE_90: 0.5,
	ROTATE_180: 1,
	ROTATE_270: 1.5
}
export const cameraRotateOptions = [
	{
		value: cameraRotateDic.ROTATE_0,
		label: '0度'
	},
	{
		value: cameraRotateDic.ROTATE_90,
		label: '右90度'
	},
	{
		value: cameraRotateDic.ROTATE_180,
		label: '右180度'
	},
	{
		value: cameraRotateDic.ROTATE_270,
		label: '右270度'
	}
]
// 签名板配置
export const signBoardManufacturerDic = {
	SELF_DEVELOP: 1
}
export const signBoardManufacturerOptions = [
	{
		label: '自研',
		value: signBoardManufacturerDic.SELF_DEVELOP
	}
]
export const signBoardTypeDic = {
	NET: '0', // 网络模式（GXX-B10款）
	SCREEN: '1', // 投屏模式（GXX-A10款）
	MULTI_USB: '2', // 多模态设备USB模式（GXX-D10款）
	MULTI_NET: '3', // 多模态设备网络模式（GXX-D10款）
	HW: '4' // 汉王 签名捺印
}
export const signBoardPath = 'ws://localhost:8899'
export const signBoardTypeOptions = [
	{
		label: '网络模式（GXX-B10款）',
		value: signBoardTypeDic.NET,
		config: {
			path: signBoardPath,
			ip: '127.0.0.1',
			port: 45008
		}
	},
	{
		label: '投屏模式（GXX-A10款）',
		value: signBoardTypeDic.SCREEN,
		config: {
			path: signBoardPath
		}
	},
	{
		label: '多模态设备USB模式（GXX-D10款）',
		value: signBoardTypeDic.MULTI_USB,
		config: {
			path: signBoardPath
		}
	},
	{
		label: '多模态设备网络模式（GXX-D10款）',
		value: signBoardTypeDic.MULTI_NET,
		config: {
			path: signBoardPath,
			ip: '127.0.0.1',
			port: 45008
		}
	},
	{
		label: 'HW',
		value: signBoardTypeDic.HW,
		config: {
			path: signBoardPath
		}
	}
]
// 接触式体征检测配置
export const contactVitalSignsManufacturerDic = {
	TYPE_1: 1
}
export const contactVitalSignsManufacturerOptions = [
	{
		label: '接触式1',
		value: contactVitalSignsManufacturerDic.TYPE_1,
		config: {
			path: '',
			baudRate: 38400
		}
	}
]
// 非接触式体征检测配置
export const nonContactVitalSignsManufacturerDic = {
	TYPE_1: 1
}
export const nonContactVitalSignsManufacturerOptions = [
	{
		label: '非接触式1',
		value: nonContactVitalSignsManufacturerDic.TYPE_1,
		config: {
			path: '127.0.0.1:7382'
		}
	}
]
// 多频读卡器配置
export const frequencyCardReaderManufacturerDic = {
	USB: 1
}
export const frequencyCardReaderManufacturerOptions = [
	{
		label: 'USB',
		value: frequencyCardReaderManufacturerDic.USB
	}
]
// 打印机配置
export const printTypeDic = {
	COLOR: 'color',
	BLACK: 'black'
}
export const printTypeOptions = [
	{
		label: '彩色打印',
		value: printTypeDic.COLOR
	},
	{
		label: '黑白打印',
		value: printTypeDic.BLACK
	}
]
// 手环读卡器配置
export const wristbandManufacturerDic = {
	USB: 1
}
export const wristbandManufacturerOptions = [
	{
		label: 'USB',
		value: wristbandManufacturerDic.USB
	}
]
// 证件读卡器配置
export const idCardManufacturerDic = {
	USB: 1
}
export const idCardManufacturerOptions = [
	{
		label: 'USB',
		value: idCardManufacturerDic.USB
	}
]
// 手环抽屉柜配置
export const wristbandDrawerManufacturerDic = {
	COM: 1,
	D99_S: 2
}
export const wristbandDrawerManufacturerOptions = [
	{
		label: '自研',
		value: wristbandDrawerManufacturerDic.COM,
		config: {
			// eslint-disable-next-line no-undef
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'COM3' : '/dev/ttyS5',
			baudRate: 9600
		}
	},
	{
		label: 'GXX-D99-s',
		value: wristbandDrawerManufacturerDic.D99_S,
		config: {
			path: SYSTEM_PLATFORM != systemPlatformStr.linux ? 'COM3' : '/dev/ttyS5',
			baudRate: 9600
		}
	}
]
// 授权状态配置
export const authStateDic = {
	UNAUTHORIZED: 0,
	AUTHORIZED: 1
}
export const authStateOptions = [
	{
		label: '未授权',
		value: authStateDic.UNAUTHORIZED
	},
	{
		label: '已授权',
		value: authStateDic.AUTHORIZED
	}
]
// 授权对象配置
export const authObjDic = {
	APP: 'app',
	THIRD: 'third'
}
export const authObjOptions = [
	{
		value: authObjDic.APP,
		label: '高新兴'
	},
	{
		value: authObjDic.THIRD,
		label: '第三方'
	}
]

export const mainCabinetList = [
	{ value: '4', label: '智能柜19主柜', gf: 19, maxNum: '19', lockManufacturer: lockManufacturerDic.D99_S, lightManufacturer: lightManufacturerDic.D99_S },
	{ value: '3', label: '智能柜18主柜', gf: 18, maxNum: '18', lockManufacturer: lockManufacturerDic.D99_S, lightManufacturer: lightManufacturerDic.D99_S },
	{ value: '2', label: '智能柜21主柜', gf: 21, maxNum: '21', lockManufacturer: lockManufacturerDic.D99_S, lightManufacturer: lightManufacturerDic.D99_S },
	{ value: '1', label: '智能柜2主柜', gf: 2, maxNum: '2', lockManufacturer: lockManufacturerDic.D99_S, lightManufacturer: lightManufacturerDic.D99_S, lightDoor: '5' }
]
export const secondaryCabinetList = [
	{ value: '4', label: '智能柜26副柜', gf: 26, maxNum: '18', lockManufacturer: lockManufacturerDic.D99_S },
	{ value: '3', label: '智能柜36副柜', gf: 36, maxNum: '21', lockManufacturer: lockManufacturerDic.D99_S },
	{ value: '2', label: '智能柜27副柜', gf: 27, maxNum: '21', lockManufacturer: lockManufacturerDic.D99_S },
	{ value: '1', label: '智能柜16副柜', gf: 16, maxNum: '16', lockManufacturer: lockManufacturerDic.D99_S }
]

// 通讯模式
export const communicationModeDic = {
	ONLINE: '1',
	OFFLINE: '2'
}
// 通讯模式
export const communicationModeOptions = [
	{
		value: communicationModeDic.ONLINE,
		label: '在线模式'
	},
	{
		value: communicationModeDic.OFFLINE,
		label: '离线模式'
	}
]
