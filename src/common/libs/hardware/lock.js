import CommonWs from './common_ws'

class Lock extends CommonWs {
	constructor(options) {
		super(options)
	}
	// 初始化连接
	init() {
		this.initWs({ type: 'lock' })
	}
	// 初始化锁控服务
	initLock(params) {
		const msg = {
			action: 'init',
			...params
		}
		this.sendMessage(msg)
	}
	// 开柜
	openLock(params) {
		const msg = {
			action: 'openLock',
			...params
		}
		this.sendMessage(msg)
	}
	// 获取柜门的锁控状态
	getLockStatus(params) {
		const msg = {
			action: 'checkLock',
			...params
		}
		this.sendMessage(msg)
	}
	// 销毁锁控
	destroyLock() {
		const msg = {
			action: 'close'
		}
		this.sendMessage(msg)
	}
	// 关闭连接
	close() {
		this.closeWs()
	}
}

export default Lock
