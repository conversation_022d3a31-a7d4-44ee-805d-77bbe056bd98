import CommonWs from './common_ws'

class Light extends CommonWs {
	constructor(options) {
		super(options)
	}
	// 初始化连接
	init() {
		this.initWs({ type: 'light' })
	}
	// 初始化补光灯服务
	initLight(params) {
		const msg = {
			action: 'init',
			...params
		}
		this.sendMessage(msg)
	}
	// 打开补光灯
	openLight(params) {
		const msg = {
			action: 'openLight',
			...params
		}
		this.sendMessage(msg)
	}
	// 关闭补光灯
	closeLight(params) {
		const msg = {
			action: 'closeLight',
			...params
		}
		this.sendMessage(msg)
	}
	// 销毁补光灯
	destroyLight() {
		const msg = {
			action: 'close'
		}
		this.sendMessage(msg)
	}
	// 关闭连接
	close() {
		this.closeWs()
	}
}

export default Light
