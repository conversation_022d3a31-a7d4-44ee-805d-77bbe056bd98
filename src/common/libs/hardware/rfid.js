import CommonWs from './common_ws'

class RFID extends CommonWs {
	constructor(options) {
		super(options)
	}
	// 初始化连接
	init() {
		this.initWs({ type: 'rfid' })
	}
	// 初始化rfid服务
	initRfid(params) {
		const msg = {
			action: 'init',
			...params
		}
		this.sendMessage(msg)
	}
	// 获取柜门中所有rfid
	checkRfid(params) {
		const msg = {
			action: 'checkRfid',
			...params
		}
		this.sendMessage(msg)
	}
	// 设置灯光
	setLight(params) {
		const msg = {
			action: 'setDoorLight',
			...params
		}
		this.sendMessage(msg)
	}
	// 设置轮询通道数
	setPollingChannels(params) {
		const msg = {
			action: 'setPollingChannels',
			...params
		}
		this.sendMessage(msg)
	}
	// 设置黄灯
	setOrangeColor(params) {
		const msg = {
			action: 'setOrangeColor',
			...params
		}
		this.sendMessage(msg)
	}
	// 销毁rfid
	destroyRfid() {
		const msg = {
			action: 'close'
		}
		this.sendMessage(msg)
	}
	// 关闭连接
	close() {
		this.closeWs()
	}
}

export default RFID
