import CommonWs from './common_ws'

class VitalSigns extends CommonWs {
	constructor(options) {
		super(options)
	}
	// 初始化连接
	init() {
		this.initWs({ type: 'nonContactVitalSigns' })
	}
	// 初始化服务
	initNonContactVitalSigns(params) {
		const msg = {
			action: 'init',
			...params
		}
		this.sendMessage(msg)
	}
	// 关闭服务
	closeNonContactVitalSigns() {
		const msg = {
			action: 'close'
		}
		this.sendMessage(msg)
	}
	// 关闭连接
	close() {
		this.closeWs()
	}
}

export default VitalSigns
