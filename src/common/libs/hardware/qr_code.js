import CommonWs from './common_ws'

class QrCode extends CommonWs {
	constructor(options) {
		super(options)
	}
	// 初始化连接
	init() {
		this.initWs({ type: 'qrCode' })
	}
	// 初始化qrCode
	initQrCode(params) {
		const msg = {
			action: 'init',
			...params
		}
		this.sendMessage(msg)
	}
	// 销毁qrCode
	destroyQrCode() {
		const msg = {
			action: 'close'
		}
		this.sendMessage(msg)
	}
	// 添加监听事件
	addKeyDownListener() {
		const msg = {
			action: 'addKeyDownListener'
		}
		this.sendMessage(msg)
	}
	// 移除监听事件
	removeKeyDownListener() {
		const msg = {
			action: 'removeKeyDownListener'
		}
		this.sendMessage(msg)
	}
	// 销毁usb
	destroyUsb() {
		const msg = {
			action: 'close'
		}
		this.sendMessage(msg)
	}
	// 关闭连接
	close() {
		this.closeWs()
	}
}

export default QrCode
