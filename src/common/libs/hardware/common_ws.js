import { EventEmitter } from 'events'

class CommonWs extends EventEmitter {
	constructor(options) {
		super(options)
		this.ip = options.ip || 'localhost'
		this.port = options.port || 9529
		this.ws = null // ws连接对象
	}
	// 初始化websocket
	initWs(options) {
		const wsUrl = `ws://${this.ip}:${this.port}/${options.type}`
		this.ws = new WebSocket(wsUrl)
		// 监听连接打开
		this.ws.onopen = () => {
			this.callback({ type: 'open', msg: '服务连接成功' })
		}
		// 接收返回消息
		this.ws.onmessage = (msg) => {
			let data = {}
			msg.data ? (data = JSON.parse(msg.data)) : (data = {})
			this.callback({ type: 'message', data })
		}
		// 监听连接关闭
		this.ws.onclose = () => {
			this.ws = null
			this.callback({ type: 'close', msg: '服务连接关闭' })
		}
		// 监听连接错误
		this.ws.onerror = (error) => {
			console.log(error)
			this.callback({ type: 'error', msg: `服务连接异常` })
		}
	}
	// 发送消息
	sendMessage(msg) {
		this.ws && this.ws.send(JSON.stringify(msg))
	}
	// 断开连接
	closeWs() {
		this.ws && this.ws.close()
	}
	// 返回操作信息
	callback(info) {
		this.emit('handleBack', info)
	}
}

export default CommonWs
