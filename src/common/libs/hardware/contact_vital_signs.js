import CommonWs from './common_ws'

class ContactVitalSigns extends CommonWs {
	constructor(options) {
		super(options)
	}
	// 初始化连接
	init() {
		this.initWs({ type: 'contactVitalSigns' })
	}
	// 初始化服务
	initContactVitalSigns(params) {
		const msg = {
			action: 'init',
			...params
		}
		this.sendMessage(msg)
	}
	// 开启采集指令
	startCollection() {
		const msg = {
			action: 'startCollection'
		}
		this.sendMessage(msg)
	}
	// 结束采集指令
	endCollection() {
		const msg = {
			action: 'endCollection'
		}
		this.sendMessage(msg)
	}
	// 关闭服务
	closeContactVitalSigns() {
		const msg = {
			action: 'close'
		}
		this.sendMessage(msg)
	}
	// 关闭连接
	close() {
		this.closeWs()
	}
}

export default ContactVitalSigns
