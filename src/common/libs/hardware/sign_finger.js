import CommonWs from './common_ws'

class SignFinger extends CommonWs {
	constructor(options) {
		super(options)
	}
	// 初始化连接
	init() {
		this.initWs({ type: 'signFinger' })
	}
	// 初始化服务
	initSignFinger(params) {
		const msg = {
			action: 'init',
			...params
		}
		this.sendMessage(msg)
	}
	// 仅签名
	onlySign(params) {
		const msg = {
			action: 'onlySign',
			...params
		}
		this.sendMessage(msg)
	}
	// 仅捺印
	onlyFinger(params) {
		const msg = {
			action: 'onlyFinger',
			...params
		}
		this.sendMessage(msg)
	}
	// 签名捺印
	signFinger(params) {
		const msg = {
			action: 'signFinger',
			...params
		}
		this.sendMessage(msg)
	}
	// 清除掉捺印
	closeDevices() {
		const msg = {
			action: 'closeDevices'
		}
		this.sendMessage(msg)
	}
	/**
	 * PDF仅预览
	 * @param {Object} params
	 * @param {String} params.srcPdfUrl 待签名源PDF文件路径（支持本地路径、Http路径）
	 * @param {String} params.srcPdfPath 待签名源PDF文件BASE64格式
	 * srcPdfUrl和srcPdfPath二选一， 优先使用srcPdfUrl字段
	 */
	onlyPreview(params) {
		const msg = {
			action: 'onlyPreview',
			...params
		}
		this.sendMessage(msg)
	}
	/**
	 * PDF签名预览
	 * @param {Object} params
	 * @param {String} params.srcPdfUrl 待签名源PDF文件路径（支持本地路径、Http路径）
	 * @param {String} params.srcPdfPath 待签名源PDF文件BASE64格式
	 * srcPdfUrl和srcPdfPath二选一， 优先使用srcPdfUrl字段
	 * @param {String} params.dstPdfFilename 签名完成后pdf文件名（带后缀名）, 不传时，取当前时间戳作为文件名
	 * @param {Boolean} params.forceReadAll 是否强制看完PDF才能签名捺印（如果没有浏览到PDF最后一页，签名捺印按钮不可用）, 默认false
	 * @param {Boolean} params.enableRealityScene 是否启用实景签名, 默认true
	 */
	clickSignEx(params) {
		const msg = {
			action: 'clickSignEx',
			...params
		}
		this.sendMessage(msg)
	}
	/**
	 * 查询签名板状态
	 * @param {Object} params
	 */
	devStatus(params) {
		const msg = {
			action: 'devStatus',
			...params
		}
		this.sendMessage(msg)
	}
	/**
	 * 启动视频预览
	 * @param {Object} params
	 */
	startVideo(params) {
		const msg = {
			action: 'startVideo',
			...params
		}
		this.sendMessage(msg)
	}
	/**
	 * 关闭视频预览
	 * @param {Object} params
	 */
	stopVideo(params) {
		const msg = {
			action: 'stopVideo',
			...params
		}
		this.sendMessage(msg)
	}
	/**
	 * 启动录制
	 * @param {Object} params
	 * @param {String} params.recordName0 第一路摄像头录像文件名
	 * @param {String} params.recordName1 第二路摄像头录像文件名
	 * @param {String} params.delay none/pdf_opening/signing/fingering/signing_or_fingering, 默认none
	 */
	startRecord(params) {
		const msg = {
			action: 'startRecord',
			...params
		}
		this.sendMessage(msg)
	}
	/**
	 * 停止录制
	 * @param {Object} params
	 * @param {Boolean} params.save 是否保存录像文件到本地, 默认false
	 * @param {Object} params.upload 录像文件上传服务器参数
	 */
	stopRecord(params) {
		const msg = {
			action: 'stopRecord',
			...params
		}
		this.sendMessage(msg)
	}
	/**
	 * 设置本地存储路径
	 * @param {Object} params
	 * @param {Boolean} params.boardType 签名板类型
	 * @param {String} params.dir 本地绝对路径（用于存储签名捺印后文件，比如录像文件、签名捺印图片、PDF等）
	 */
	setLocalDir(params) {
		const msg = {
			action: 'setLocalDir',
			...params
		}
		this.sendMessage(msg)
	}
	/**
	 * 查询本地存储路径
	 * @param {Object} params
	 * @param {Boolean} params.boardType 签名板类型
	 */
	localDir(params) {
		const msg = {
			action: 'localDir',
			...params
		}
		this.sendMessage(msg)
	}
	// 关闭签名捺印
	destroySignFinger() {
		const msg = {
			action: 'close'
		}
		this.sendMessage(msg)
	}
	// 关闭连接
	close() {
		this.closeWs()
	}
}

export default SignFinger
