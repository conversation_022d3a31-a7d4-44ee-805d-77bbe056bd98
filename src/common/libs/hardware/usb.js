import CommonWs from './common_ws'

class Usb extends CommonWs {
	constructor(options) {
		super(options)
	}
	// 初始化连接
	init() {
		this.initWs({ type: 'usb' })
	}
	// 初始化usb
	initUsb() {
		const msg = {
			action: 'init'
		}
		this.sendMessage(msg)
	}
	// 获取摄像头列表
	getCameraList() {
		const msg = {
			action: 'getVideoDevice'
		}
		this.sendMessage(msg)
	}
	// 打开摄像头
	openCamera(deviceId = '', width = 600, height = 600) {
		const msg = {
			action: 'openCamera',
			width,
			height,
			deviceId
		}
		this.sendMessage(msg)
	}
	// 关闭摄像头
	closeCamera() {
		const msg = {
			action: 'closeCamera'
		}
		this.sendMessage(msg)
	}
	// 捕获图片
	captureImage() {
		const msg = {
			action: 'captureImage'
		}
		this.sendMessage(msg)
	}
	// 旋转摄像头  0, 0.5, 1, 1.5, 2 分别为 90 180 270 360
	rotateCamera(rotateX = 0, rotateY = 0) {
		const msg = {
			action: 'rotateCamera',
			rotateX,
			rotateY
		}
		this.sendMessage(msg)
	}
	// 添加监听事件
	addKeyDownListener() {
		const msg = {
			action: 'addKeyDownListener'
		}
		this.sendMessage(msg)
	}
	// 移除监听事件
	removeKeyDownListener() {
		const msg = {
			action: 'removeKeyDownListener'
		}
		this.sendMessage(msg)
	}
	// 获取打印机列表
	getPrinterList() {
		const msg = {
			action: 'getPrinterList'
		}
		this.sendMessage(msg)
	}
	// 打印文件
	printFile(param) {
		const msg = {
			action: 'print',
			...param
		}
		this.sendMessage(msg)
	}
	// 获取身份证信息
	getIdCard() {
		const msg = {
			action: 'readIdCard'
		}
		this.sendMessage(msg)
	}
	// 获取手环
	getWristband() {
		const msg = {
			action: 'readWristband'
		}
		this.sendMessage(msg)
	}
	// 销毁usb
	destroyUsb() {
		const msg = {
			action: 'close'
		}
		this.sendMessage(msg)
	}
	// 关闭连接
	close() {
		this.closeWs()
	}
}

export default Usb
