export default {
	菜单: {
		存入物品: '存入物品',
		取出物品: '取出物品',
		临时借出: '临时借出',
		借出归还: '借出归还'
	},
	统计: {
		总量: '总量',
		空闲: '空闲',
		占用: '占用',
		不可用: '不可用'
	},
	人脸识别: {
		未获取到人脸: '未获取到人脸',
		未查询到人脸信息: '未查询到人脸信息',
		请继续识别: '请继续识别'
	},
	验证: {
		请保持面部放在取景框内: '请保持面部放在取景框内',
		没有嫌疑人信息: '没有嫌疑人信息',
		点击: '点击',
		立即注册: '立即注册',
		认证出现问题: '认证出现问题',
		试试其他认证方式: '试试其他认证方式',
		退出认证: '退出认证',
		账号认证: '账号认证',
		刷脸认证: '刷脸认证',
		刷卡认证: '刷卡认证',
		指纹认证: '指纹认证',
		手势认证: '手势认证',
		手环认证: '手环认证',
		请工作人员识别: '请工作人员识别',
		验证成功: '验证成功',
		对比人员不一致: '对比人员不一致',
		第一次验证成功: '第一次验证成功',
		请关联民警识别: '请关联民警识别',
		请管理员识别: '请管理员识别',
		双重验证成功: '双重验证成功',
		识别人员非关联民警: '识别人员非关联民警',
		账号所属人员非关联嫌疑人: '账号所属人员非关联嫌疑人',
		识别人员需为管理员: '识别人员需为管理员',
		请将IC卡放在感应区域: '请将IC卡放在感应区域',
		//账号独有
		请输入账号: '请输入账号',
		请输入密码: '请输入密码',
		立即认证: '立即认证',
		请工作人员验证: '请工作人员验证',
		请关联民警验证: '请关联民警验证',
		请管理员或关联人员验证: '请管理员或关联人员验证',
		请管理员验证: '请管理员验证',
		账号所属人员非关联民警: '账号所属人员非关联民警',
		账号所属人员需为管理员: '账号所属人员需为管理员',
		删除: '删除',
		符: '符',
		数: '数'
	},
	选择: {
		选取关联人员: '选取关联人员',
		必填: '必填',
		选填: '选填',
		关联民警选择: '关联民警选择',
		确认: '确认',
		取消: '取消'
	},
	嫌疑人注册: {
		姓名: '姓名',
		身份证号: '身份证号',
		关联民警: '关联民警',
		账号: '账号',
		密码: '密码',
		修改人脸: '修改人脸',
		录入人脸: '录入人脸',
		人员注册成功: '人员注册成功',
		后: '后',
		即将返回首页: '即将返回首页',
		取消: '取消',
		下一步: '下一步',
		请保持面部放在取景框内: '请保持面部放在取景框内',
		重拍: '重拍',
		确认: '确认',
		基本信息: '基本信息',
		绑定柜格: '绑定柜格',
		完成注册: '完成注册',
		获取成功: '获取成功',
		按确认继续: '按确认继续'
	},
	头部: {
		返回: '返回',
		首页: '首页',
		嫌疑人注册: '嫌疑人注册',
		完善信息: '完善信息',
		人员管理: '人员管理',
		人员注册: '人员注册',
		系统配置: '系统配置',
		存取记录: '存取记录',
		选择物品: '选择物品'
	},
	开关柜: {
		柜门开启中: '柜门开启中',
		请稍后: '请稍后',
		物品后: '物品后',
		请签名捺印确认: '请签名捺印确认',
		请关好柜门: '请关好柜门',
		取出: '取出',
		借出: '借出',
		归还: '归还',
		柜门已关闭: '柜门已关闭',
		柜门已开启: '柜门已开启',
		存放物品后请签名捺印确认: '存放物品后请签名捺印确认',
		存放物品后请关好柜门: '存放物品后请关好柜门'
	},
	设置: {
		人员注册: '人员注册',
		人员管理: '人员管理',
		系统配置: '系统配置',
		存取记录: '存取记录'
	},
	存取记录: {
		输入姓名查询: '输入姓名查询',
		日期: '日期',
		类型: '类型',
		存取类型: '存取类型',
		开柜: '开柜',
		关柜: '关柜',
		操作人: '操作人',
		存入物品: '存入物品',
		取出物品: '取出物品',
		临时借出: '临时借出',
		借出归还: '借出归还',
		全部: '全部',
		管理员: '管理员',
		工作人员: '工作人员',
		非工作人员: '非工作人员'
	},
	人员管理: {
		输入姓名查询: '输入姓名查询',
		查询: '查询',
		同步人员信息: '同步人员信息',
		注册时间: '注册时间',
		完善信息: '完善信息',
		全选: '全选',
		已选: '已选',
		件: '件',
		删除: '删除',
		是否主柜: '是否主柜',
		是: '是',
		否: '否',
		主柜IP: '主柜IP',
		端口: '端口',
		同步: '同步',
		全部: '全部',
		管理员: '管理员',
		工作人员: '工作人员',
		非工作人员: '非工作人员',
		删除成功: '删除成功'
	}
}
