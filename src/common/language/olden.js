export default {
	菜单: {
		存入物品: 'Deposit',
		取出物品: 'Take out',
		临时借出: 'Temporary loans',
		借出归还: 'Lend and return'
	},
	统计: {
		总量: 'Total',
		空闲: 'Idle',
		占用: 'Occupancy',
		不可用: 'Breakdown'
	},
	人脸识别: {
		未获取到人脸: 'Face not detected',
		未查询到人脸信息: 'Face information not found',
		请继续识别: 'Please continue recognition'
	},
	验证: {
		请保持面部放在取景框内: 'Please keep your face within the viewfinder',
		没有嫌疑人信息: 'No suspect information available',
		点击: 'Click',
		立即注册: 'Register',
		认证出现问题: 'Authentication issue occurred',
		试试其他认证方式: 'Try another authentication method',
		退出认证: 'Exit authentication',
		账号认证: 'Account',
		刷脸认证: 'Face',
		刷卡认证: 'Card',
		指纹认证: 'Fingerprint',
		手势认证: 'Gesture',
		请工作人员识别: 'Please ask staff for recognition',
		验证成功: 'Authentication successful',
		对比人员不一致: 'Mismatched person during comparison',
		第一次验证成功: 'First authentication successful',
		请关联民警识别: 'Please ask the associated policeman for recognition',
		请管理员识别: 'Please ask the administrator for recognition',
		双重验证成功: 'Dual authentication successful',
		识别人员非关联民警: 'The recognized person is not the associated policeman',
		账号所属人员非关联嫌疑人: 'The account holder is not the associated suspect',
		识别人员需为管理员: 'The recognized person needs to be an administrator',
		请将IC卡放在感应区域: 'Please place the IC card in the sensing area',
		//账号独有
		请输入账号: 'Please enter your account',
		请输入密码: 'Please enter your password',
		立即认证: 'Authenticate immediately',
		请工作人员验证: 'Please ask staff for verification',
		请关联民警验证: 'Please ask the associated policeman for verification',
		请管理员或关联人员验证: 'Please ask the administrator or associated personnel for verification',
		请管理员验证: 'Please ask the administrator for verification',
		账号所属人员非关联民警: 'The account holder is not an associated policeman',
		账号所属人员需为管理员: 'The account holder needs to be an administrator',
		删除: 'Del',
		符: '#+/',
		数: '123'
	},
	选择: {
		选取关联人员: 'Select Associated Personnel',
		必填: 'Required',
		选填: 'Optional',
		关联民警选择: 'Select Associated Police Officer',
		确认: 'Confirm',
		取消: 'Cancel'
	},
	嫌疑人注册: {
		姓名: 'Name',
		身份证号: 'ID',
		关联民警: 'Linked',
		账号: 'Account',
		密码: 'Pwd',
		修改人脸: 'Modify',
		录入人脸: 'Capture',
		人员注册成功: 'Personnel Registration Successful',
		后: 'After',
		即将返回首页: 'Will Return to Homepage Soon',
		取消: 'Cancel',
		下一步: 'Next Step',
		请保持面部放在取景框内: 'Please Keep Your Face Inside the Frame',
		重拍: 'Retake',
		确认: 'Confirm',
		基本信息: 'Information',
		绑定柜格: 'Bind Locker',
		完成注册: 'Complete',
		获取成功: 'Successfully Obtained',
		按确认继续: 'Press Confirm to Continue'
	},
	头部: {
		返回: 'Back',
		首页: 'Home',
		嫌疑人注册: 'Suspect Registration',
		完善信息: 'Complete Information',
		人员管理: 'Personnel Management',
		人员注册: 'Personnel Registration',
		系统配置: 'System Configuration',
		存取记录: 'Access Records',
		选择物品: 'Select Item'
	},
	开关柜: {
		柜门开启中: 'Cabinet Door Opening',
		请稍后: 'Please Wait',
		物品后: 'After Items',
		请签名捺印确认: 'Please Sign and Fingerprint for Confirmation',
		请关好柜门: 'Please Close the Cabinet Door Properly',
		取出: 'Retrieve',
		借出: 'Borrow',
		归还: 'Return',
		柜门已关闭: 'Cabinet Door Closed',
		柜门已开启: 'Cabinet Door Opened',
		存放物品后请签名捺印确认: 'Please Sign and Fingerprint for Confirmation After Storing Items',
		存放物品后请关好柜门: 'Please Close the Cabinet Door Properly After Storing Items'
	},
	设置: {
		人员注册: 'User Registration',
		人员管理: 'User Management',
		系统配置: 'System Configuration',
		存取记录: 'Access Records'
	},
	存取记录: {
		输入姓名查询: 'Name Search', //缩写，不然会导致样式问题
		日期: 'Date',
		类型: 'Type',
		存取类型: 'Access Type',
		开柜: 'Open', //缩写，不然会导致样式问题
		关柜: 'Close', //缩写，不然会导致样式问题
		操作人: 'Op', //缩写，不然会导致样式问题
		存入物品: 'Items Stored',
		取出物品: 'Items Taken Out',
		临时借出: 'Temporary Loan',
		借出归还: 'Loan Returned',
		全部: 'All',
		管理员: 'Administrator',
		工作人员: 'Staff',
		非工作人员: 'Non-staff'
	},
	人员管理: {
		输入姓名查询: 'Name Search', //缩写，不然会导致样式问题
		查询: 'Search',
		同步人员信息: 'Synchronize Personnel Information',
		注册时间: 'Registration',
		完善信息: 'Complete',
		全选: 'Select All',
		已选: 'Selected',
		件: 'Item(s)',
		删除: 'Delete',
		是否主柜: 'Is Main Cabinet',
		是: 'Yes',
		否: 'No',
		主柜IP: 'Main Cabinet IP',
		端口: 'Port',
		同步: 'Synchronize',
		全部: 'All',
		管理员: 'Administrator',
		工作人员: 'Staff',
		非工作人员: 'Non-staff',
		删除成功: 'Deletion Successful'
	}
}
