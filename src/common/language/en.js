export default {
	菜单: {
		存入物品: 'Store',
		取出物品: 'Retrieve',
		临时借出: 'Temporary Retrieve',
		借出归还: 'Return'
	},
	统计: {
		总量: 'Total',
		空闲: 'Available',
		占用: 'Occupied',
		不可用: 'Faulty'
	},
	人脸识别: {
		未获取到人脸: 'No face found',
		未查询到人脸信息: 'No face information found',
		请继续识别: 'Please continue scanning'
	},
	验证: {
		请保持面部放在取景框内: 'Please keep your face within the frame',
		没有嫌疑人信息: 'No suspect information found',
		点击: 'Click',
		立即注册: 'Register',
		认证出现问题: 'Authentication issue occurred',
		试试其他认证方式: 'Try other authentication methods',
		退出认证: 'Exit authentication',
		账号认证: 'Account',
		刷脸认证: 'Face',
		刷卡认证: 'Card',
		指纹认证: 'Fingerprint',
		手势认证: 'Pattern',
		请工作人员识别: 'Please ask staff for Authentication',
		验证成功: 'Authenticate successful',
		对比人员不一致: 'Mismatched staff',
		第一次验证成功: 'First authentication successful',
		请关联民警识别: 'Please ask the associated police officer for authentication',
		请管理员识别: 'Please ask the administrator for authentication',
		双重验证成功: 'Dual authentication successful',
		识别人员非关联民警: 'The recognized person is not the associated police officer',
		账号所属人员非关联嫌疑人: 'The account holder is not the associated suspect',
		识别人员需为管理员: 'The recognized person must to be administrator',
		请将IC卡放在感应区域: 'Please place the IC card in the sensing area',
		//账号独有
		请输入账号: 'Please enter your account',
		请输入密码: 'Please enter your password',
		立即认证: 'Authenticate now',
		请工作人员验证: 'Please ask staff for verification',
		请关联民警验证: 'Please ask the associated police officer for verification',
		请管理员或关联人员验证: 'Please ask the administrator or associated person for verification',
		请管理员验证: 'Please ask the administrator for verification',
		账号所属人员非关联民警: 'The account holder is not an associated police officer',
		账号所属人员需为管理员: 'The account holder must to be an administrator',
		删除: 'Del',
		符: '#+/',
		数: '123'
	},
	选择: {
		选取关联人员: 'Select associated person',
		必填: 'Required',
		选填: 'Optional',
		关联民警选择: 'Select associated police officer',
		确认: 'Confirm',
		取消: 'Cancel'
	},
	嫌疑人注册: {
		姓名: 'Name',
		密码: 'Password',
		身份证号: 'ID',
		关联民警: 'Link',
		账号: 'Account',
		修改人脸: 'Change face',
		录入人脸: 'Capture face',
		人员注册成功: 'Person Registration Successful',
		后: 'After',
		即将返回首页: 'Return to homepage',
		取消: 'Cancel',
		下一步: 'Next Step',
		请保持面部放在取景框内: 'Please keep your face within the frame',
		重拍: 'Retake',
		确认: 'Confirm',
		基本信息: 'Information',
		绑定柜格: 'Bind Locker',
		完成注册: 'Completed',
		获取成功: 'Capture successful',
		按确认继续: 'Press confirm to Continue'
	},
	头部: {
		返回: 'Back',
		首页: 'Home',
		嫌疑人注册: 'Suspect Registration',
		完善信息: 'Complete Information',
		人员管理: 'Staff Management',
		人员注册: 'Staff Registration',
		系统配置: 'System Configuration',
		存取记录: 'Access Records',
		选择物品: 'Select Item'
	},
	开关柜: {
		柜门开启中: 'Cabinet door opening',
		请稍后: 'Please wait',
		物品后: 'completed',
		请签名捺印确认: 'Please sign and fingerprint for confirmation',
		请关好柜门: 'Please close the cabinet door',
		取出: 'Retrieve',
		借出: 'Temporary retrieve',
		归还: 'Return',
		柜门已关闭: 'Cabinet door closed',
		柜门已开启: 'Cabinet door cpened',
		存放物品后请签名捺印确认: 'Please sign and fingerprint for confirmation after storing items',
		存放物品后请关好柜门: 'Please close the cabinet door after storing items'
	},
	设置: {
		人员注册: 'Staff Registration',
		人员管理: 'Staff Management',
		系统配置: 'System Configuration',
		存取记录: 'Access Records'
	},
	存取记录: {
		输入姓名查询: 'Name Search', //缩写，不然会导致样式问题
		日期: 'Date',
		类型: 'Type',
		存取类型: 'Access Type',
		开柜: 'Open', //缩写，不然会导致样式问题
		关柜: 'Close', //缩写，不然会导致样式问题
		操作人: 'Op.', //缩写，不然会导致样式问题
		存入物品: 'Store',
		取出物品: 'Retrieve',
		临时借出: 'Temporary Retrieve',
		借出归还: 'Return',
		全部: 'All',
		管理员: 'Administrator',
		工作人员: 'Staff',
		非工作人员: 'Non-staff'
	},
	人员管理: {
		输入姓名查询: 'Name Search', //缩写，不然会导致样式问题
		查询: 'Search',
		同步人员信息: 'Synch staff',
		注册时间: 'RegDate',
		完善信息: 'Complete',
		全选: 'Select all',
		已选: 'Selected',
		件: 'Item(s)',
		删除: 'Delete',
		是否主柜: 'Is Main Cabinet',
		是: 'Yes',
		否: 'No',
		主柜IP: 'Main Cabinet IP',
		端口: 'Port',
		同步: 'Synchronize',
		全部: 'All',
		管理员: 'Administrator',
		工作人员: 'Staff',
		非工作人员: 'Non-staff',
		删除成功: 'Delete Successful'
	}
}
