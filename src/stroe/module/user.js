import { getNewToken } from '@/libs/utils'
const state = {
    // 终端本地配置
    userInfo: JSON.parse(localStorage.getItem('userInfo') || 'null')
}
const getters = {
    getUser(state) {
        return state.userInfo
    },
    isLogin(state) {
        if (!state.userInfo || state.userInfo == 'undefined' || !getNewToken()) {
            return false
        }
        return true
    }
}
const mutations = {
    setUser(state, payload) {
        state.userInfo = payload
        localStorage.setItem('userInfo', JSON.stringify(payload))
    },
    removeUser(state) {
        localStorage.removeItem('userInfo')
        state.userInfo = null
    }
}
const actions = {}
export default {
    state,
    getters,
    mutations,
    actions
}
