const state = {
    // 物品信息
    wplist: [],
    // 取货人
    police: null,
    // 柜子信息
    cabinet: null,
    // 柜格保存相关的物品
    cabinetOfWp: [],
    ckkssj: '',
    rkKssj: ''
}
const getters = {
    getWplist(state) {
        return state.wplist
    },
    getPolice(state) {
        return state.police
    },
    getCabinet(state) {
        return state.cabinet
    },
    getCabinetOfWp(state) {
        return state.cabinetOfWp
    },
    getRkkssj(state) {
        return state.rkKssj
    },
    getCkkssj(state) {
        return state.ckkssj
    }
}
const mutations = {
    setRkkssj(state, payload) {
        state.rkKssj = payload
    },
    setCkkssj(state, payload) {
        state.ckkssj = payload
    },
    removeProcessKssj(state) {
        state.rkKssj = ''
        state.ckkssj = ''
    },
    // store物品操作
    setWplist(state, payload) {
        state.wplist = [...payload.wplist]
    },
    removeWplist(state, payload) {
        state.wplist = []
    },
    // store取货人
    setPolice(state, payload) {
        state.police = Object.assign({}, payload.police)
    },
    removePolice(state, payload) {
        state.police = {}
    },
    // store柜子信息
    setCabinet(state, payload) {
        state.cabinet = payload.cabinet
    },
    removeCabinet(state, payload) {
        state.cabinet = null
    },
    // 柜子和物品绑定一起
    setCabinetOfWp(state, wplist) {
        // const { gh, wplist } = payload
        state.cabinetOfWp = wplist
    },
    removeCabinetOfWp(state, payload) {
        state.cabinetOfWp = []
    }
}
const actions = {}
export default {
    state,
    getters,
    mutations,
    actions
}
