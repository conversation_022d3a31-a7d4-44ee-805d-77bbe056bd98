const state = {
	token: '',
	userInfo: {}
}
const getters = {
	token: (state) => state.token,
	userInfo: (state) => state.userInfo
}
const mutations = {
	setToken(state, token) {
		state.token = token
	},
	removeToken(state) {
		state.token = ''
	},
	setUserInfo(state, info) {
		state.userInfo = info
	},
	removeUserInfo(state) {
		state.userInfo = null
	}
}
const actions = {
	setToken({ commit }, token) {
		commit('setToken', token)
	},
	removeToken({ commit }) {
		commit('removeToken')
	},
	setUserInfo({ commit }, data) {
		commit('setUserInfo', data)
	},
	removeUserInfo({ commit }) {
		commit('removeUserInfo')
	}
}

export default { state, getters, actions, mutations }
