const state = {
	nextRouter: '',
	nextUser: {},
	config: localStorage.getItem('configInfo') ? JSON.parse(localStorage.getItem('configInfo')) : {},
	useType: '',
	propertyData: {},
	allConfig: {},
	goodsList: [],
	onlineConfig: {},
	isRemote: false
}
const getters = {
	nextRouter: (state) => state.nextRouter,
	nextUser: (state) => state.nextUser,
	config: (state) => state.config,
	propertyData: (state) => state.propertyData,
	useType: (state) => state.useType,
	allConfig: (state) => state.allConfig,
	goodsList: (state) => state.goodsList,
	onlineConfig: (state) => state.onlineConfig,
	isRemote: (state) => state.isRemote
}
const mutations = {
	setNextRouter(state, nextRouter) {
		state.nextRouter = nextRouter
	},
	setNextUser(state, nextUser) {
		state.nextUser = nextUser
	},
	setConfig(state, config) {
		state.config = config
		localStorage.setItem('configInfo', JSON.stringify(config))
	},
	setPropertyData(state, propertyData) {
		state.propertyData = propertyData
		localStorage.setItem('propertyData', JSON.stringify(propertyData))
	},
	setUseType(state, useType) {
		state.useType = useType
	},
	setAllConfig(state, allConfig) {
		state.allConfig = allConfig
	},
	setGoodsList(state, goodsList) {
		state.goodsList = goodsList
	},
	setOnlineConfig(state, onlineConfig) {
		state.onlineConfig = onlineConfig
	},
	setIsRemote(state, isRemote) {
		state.isRemote = isRemote
	}
}
const actions = {
	setNextRouter({ commit }, nextRouter) {
		commit('setNextRouter', nextRouter)
	},
	setNextUser({ commit }, nextUser) {
		commit('setNextUser', nextUser)
	},
	setConfig({ commit }, config) {
		commit('setConfig', config)
	},
	setPropertyData({ commit }, propertyData) {
		commit('setPropertyData', propertyData)
	},
	setUseType({ commit }, useType) {
		commit('setUseType', useType)
	},
	setAllConfig({ commit }, allConfig) {
		commit('setAllConfig', allConfig)
	},
	setGoodsList({ commit }, goodsList) {
		commit('setGoodsList', goodsList)
	},
	setOnlineConfig({ commit }, onlineConfig) {
		commit('setOnlineConfig', onlineConfig)
	},
	setIsRemote({ commit }, isRemote) {
		commit('setIsRemote', isRemote)
	}
}
export default { state, getters, mutations, actions }
