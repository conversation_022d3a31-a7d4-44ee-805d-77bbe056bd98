<template>
	<div class="ui-message">
		<span class="ui-message-content">
			<span :class="['ui-message-icon', 'form-msg-' + msgType]"></span>
			<span class="ui-message-text">
				<render-cell :render="render"></render-cell>
			</span>
		</span>
	</div>
</template>
<script>
import renderCell from './render'
export default {
	components: {
		renderCell
	},
	props: {
		duration: {
			type: Number,
			default: 1.5
		},
		content: {
			type: String,
			default: ''
		},
		render: {
			type: Function,
			required: true
		},
		onClose: {
			type: Function,
			default: () => {}
		},
		msgId: {
			type: String,
			required: true
		},
		msgType: {
			type: String,
			default: 'info'
		}
	},
	data() {
		return {
			closeTimer: null
		}
	},
	methods: {
		clearCloseTimer() {
			if (this.closeTimer) {
				clearTimeout(this.closeTimer)
				this.closeTimer = null
			}
		},
		close() {
			this.clearCloseTimer()
			this.onClose()
			this.$parent.close(this.msgId)
		}
	},
	mounted() {
		this.clearCloseTimer()
		if (this.duration !== 0) {
			this.closeTimer = setTimeout(() => {
				this.close()
			}, this.duration * 1000)
		}
	},
	beforeDestroy() {
		this.clearCloseTimer()
	}
}
</script>
<style lang="less" scoped>
.ui-message {
	position: absolute;
	top: 50%;
	width: 100%;
	transform: translate(0, -50%);
	max-width: none;
	text-align: center;
	.ui-message-content {
		max-width: 878px;
		min-height: 130px;
		display: inline-block;
		padding: 35px 70px 35px 138px;
		border-radius: 4px;
		background: rgba(0, 0, 0, 0.7);
		box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
		color: #ffffff;
		text-align: center;
		position: relative;
	}
	.ui-message-text {
		font-size: 40px;
		line-height: 60px;
	}
	.ui-message-icon {
		position: absolute;
		top: 41px;
		left: 70px;
		width: 48px;
		height: 48px;
		background: url(./images/error.png) no-repeat;
		background-size: 100% 100%;
		&.form-msg-info {
			background: url(./images/info.png) no-repeat;
			background-size: 100% 100%;
		}
		&.form-msg-error {
			background: url(./images/error.png) no-repeat;
			background-size: 100% 100%;
		}
		&.form-msg-success {
			background: url(./images/success.png) no-repeat;
			background-size: 100% 100%;
		}
	}
}
</style>
