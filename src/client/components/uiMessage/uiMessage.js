import uiWrapper from './uiWrapper.vue'
import Vue from 'vue'
let messageInstance = null
let msgId = 1
const defaults = {
	top: 0,
	duration: 2.5
}
function getType(value, type) {
	const typeArr = Array.isArray(type) ? type : type.split()
	let flag = false
	typeArr.forEach((type) => {
		if (Object.prototype.toString.call(value) === `[object ${type}]`) {
			flag = true
		}
	})
	return flag
}
const newInstance = (messageProp) => {
	const Instance = new Vue({
		render(h) {
			return h(uiWrapper, {
				props: messageProp
			})
		}
	})
	const component = Instance.$mount()
	document.body.appendChild(component.$el)
	const notification = component.$children[0]
	return {
		add(noticeOption) {
			notification.add(noticeOption)
		},
		remove(name) {
			notification.close(name)
		},
		destroy() {
			notification.closeAll()
			setTimeout(() => {
				document.body.removeChild(document.getElementsByClassName('ui-message-wrapper')[0])
			}, 500)
		}
	}
}
const getMessageInstance = () => {
	messageInstance =
		messageInstance ||
		newInstance({
			styles: {
				top: `${defaults.top}px`
			}
		})
	return messageInstance
}
const getMessageType = (type, options) => {
	options = getType(options, 'Object') ? options : { content: options || '' }
	const instance = getMessageInstance()
	const noticeProps = Object.assign(
		{},
		{
			msgId: `ui-message${msgId}`,
			msgType: type,
			top: defaults.top,
			duration: defaults.duration,
			render: (h) => h('span', options.content || '')
		},
		options
	)
	instance.add(noticeProps)
	return (() => {
		const target = msgId++
		return function () {
			instance.remove(`ui-message${target}`)
		}
	})()
}
export default {
	name: 'uiMessage',
	info(options) {
		return getMessageType('info', options)
	},
	success(options) {
		return getMessageType('success', options)
	},
	warning(options) {
		return getMessageType('warning', options)
	},
	error(options) {
		return getMessageType('error', options)
	},
	config(options) {
		if (options.top || options.top === 0) {
			defaults.top = options.top
		}
		if (options.duration || options.duration === 0) {
			defaults.duration = options.duration
		}
	},
	destroy() {
		const instance = getMessageInstance()
		instance.destroy()
		messageInstance = null
	}
}
