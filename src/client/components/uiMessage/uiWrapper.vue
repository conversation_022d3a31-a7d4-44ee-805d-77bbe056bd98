<template>
	<div class="ui-message-wrapper" :style="wrapStyles">
		<ui-message v-for="notice in messageList" :key="notice.msgId" :styles="notice.styles" :content="notice.content" :duration="notice.duration" :render="notice.render" :closable="notice.closable" :background="notice.background" :msg-id="notice.msgId" :msg-type="notice.msgType" :on-close="notice.onClose"></ui-message>
	</div>
</template>
<script>
import uiMessage from './uiMessage.vue'
export default {
	components: { uiMessage },
	props: {
		styles: {
			type: Object,
			default: () => {
				return {
					top: '65px'
				}
			}
		}
	},
	data() {
		return {
			messageList: [],
			zIndex: 4000
		}
	},
	computed: {
		wrapStyles() {
			const defaultStyle = { zIndex: this.zIndex }
			return Object.assign({}, this.styles, defaultStyle)
		}
	},
	methods: {
		add(notice) {
			this.zIndex = 4000
			this.messageList.push(notice)
		},
		close(msgId) {
			for (let i = 0; i < this.messageList.length; i++) {
				if (this.messageList[i].msgId === msgId) {
					this.messageList.splice(i, 1)
					break
				}
			}
		},
		closeAll() {
			this.messageList = []
		}
	}
}
</script>
<style lang="less" scoped>
.ui-message-wrapper {
	position: fixed;
	width: 100%;
	height: 100%;
	left: 0;
	pointer-events: none;
}
</style>
