<template>
	<div class="ui-date-picker" v-if="isCalendarOpen">
		<!-- 遮罩层 -->
		<div class="overlay" @click="cancelSelection"></div>
		<div class="date-picker">
			<div class="date-picker-title">日期选择</div>
			<div class="calendar">
				<div class="calendar-header">
					<div class="prev">
						<div class="year" @click="prevYear"></div>
						<div class="month" @click="prevMonth"></div>
					</div>
					<span>{{ currentYear }}年{{ currentMonth + 1 }}月</span>
					<div class="next">
						<div class="month" @click="nextMonth"></div>
						<div class="year" @click="nextYear"></div>
					</div>
				</div>
				<div class="calendar-days">
					<div class="day" v-for="day in days" :key="day">{{ day }}</div>
					<div
						class="date"
						v-for="(date, idx) in monthDates"
						:key="idx"
						:class="{
							'other-month': date.otherMonth,
							'in-range': isInRange(date),
							'selected-left-data': isStartDate(date),
							'selected-right-data': isEndDate(date)
						}"
						@click="selectDate(date)"
					>
						<div :class="{ selected: isSelected(date) }">{{ date.date }}</div>
					</div>
				</div>
			</div>
			<div class="date-picker-bottom">
				<div class="bottom-value">
					<span class="label">开始日期:</span>
					<span class="value">{{ formattedStartDate }}</span>
					<span class="line"></span>
					<span class="label">结束日期:</span>
					<span class="value">{{ formattedEndDate }}</span>
				</div>
				<div class="btn-box">
					<div class="cancel-btn" @click="cancelSelection">取消</div>
					<div class="reset-btn" @click="resetSelection">重置</div>
					<div class="confirm-btn" @click="confirmSelection">确定</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			currentMonth: new Date().getMonth(),
			currentYear: new Date().getFullYear(),
			isCalendarOpen: false,
			selectedStartDate: null,
			selectedEndDate: null
		}
	},
	props: { value: { type: Array, default: () => ['', ''] } },
	watch: {
		value(val) {
			console.log(val)
			this.selectedStartDate = val[0] ? new Date(val[0]) : ''
			this.selectedEndDate = val[1] ? new Date(val[1]) : ''
		}
	},

	computed: {
		days() {
			return ['一', '二', '三', '四', '五', '六', '日']
		},
		monthDates() {
			const dates = []
			const firstDay = new Date(this.currentYear, this.currentMonth, 1).getDay()
			const totalDays = new Date(this.currentYear, this.currentMonth + 1, 0).getDate()
			const adjustedFirstDay = firstDay === 0 ? 6 : firstDay - 1 // 调整第一天的索引

			// 上个月的日期
			const prevMonthDays = new Date(this.currentYear, this.currentMonth, 0).getDate()
			for (let i = adjustedFirstDay - 1; i >= 0; i--) {
				dates.push({ date: prevMonthDays - i, otherMonth: true, monthOffset: -1 })
			}

			// 当前月的日期
			for (let date = 1; date <= totalDays; date++) {
				dates.push({ date, otherMonth: false, monthOffset: 0 })
			}

			// 下个月的日期
			const nextMonthDays = 42 - dates.length // 确保日历显示6行
			for (let i = 1; i <= nextMonthDays; i++) {
				dates.push({ date: i, otherMonth: true, monthOffset: 1 })
			}

			return dates
		},
		formattedStartDate() {
			return this.selectedStartDate ? `${String(this.selectedStartDate.getMonth() + 1).padStart(2, '0')}-${String(this.selectedStartDate.getDate()).padStart(2, '0')}` : ''
		},
		formattedEndDate() {
			return this.selectedEndDate ? `${String(this.selectedEndDate.getMonth() + 1).padStart(2, '0')}-${String(this.selectedEndDate.getDate()).padStart(2, '0')}` : ''
		}
	},
	methods: {
		isStartDate(date) {
			if (!this.selectedStartDate || !this.selectedEndDate || this.selectedStartDate.getTime() == this.selectedEndDate.getTime()) {
				return false
			}

			const selectedDate = new Date(this.currentYear, this.currentMonth + date.monthOffset, date.date)

			// 检查是否为开始日期
			return selectedDate.toDateString() === this.selectedStartDate.toDateString()
		},
		isEndDate(date) {
			if (!this.selectedEndDate || this.selectedStartDate.getTime() == this.selectedEndDate.getTime()) {
				return false
			}

			const selectedDate = new Date(this.currentYear, this.currentMonth + date.monthOffset, date.date)

			// 检查是否为结束日期
			return selectedDate.toDateString() === this.selectedEndDate.toDateString()
		},
		selectDate(date) {
			if (!date) return
			const selectedDate = new Date(this.currentYear, this.currentMonth + date.monthOffset, date.date)
			if (!this.selectedStartDate || (this.selectedStartDate && this.selectedEndDate)) {
				this.selectedStartDate = selectedDate
				this.selectedEndDate = null
			} else if (selectedDate < this.selectedStartDate) {
				this.selectedEndDate = this.selectedStartDate
				this.selectedStartDate = selectedDate
			} else {
				this.selectedEndDate = selectedDate
			}
		},
		isSelected(date) {
			if (!this.selectedStartDate && !this.selectedEndDate) {
				return false
			}

			const selectedDate = new Date(this.currentYear, this.currentMonth + date.monthOffset, date.date)

			// 比较 selectedStartDate 和 selectedEndDate 的完整日期
			const isStart = this.selectedStartDate && selectedDate.toDateString() === this.selectedStartDate.toDateString()
			const isEnd = this.selectedEndDate && selectedDate.toDateString() === this.selectedEndDate.toDateString()

			return isStart || isEnd
		},
		isInRange(date) {
			if (!this.selectedStartDate || !this.selectedEndDate || this.selectedStartDate.getTime() == this.selectedEndDate.getTime()) {
				return false
			}

			const selectedDate = new Date(this.currentYear, this.currentMonth + date.monthOffset, date.date)

			// 检查是否在范围内，包括开始和结束日期
			return selectedDate >= this.selectedStartDate && selectedDate <= this.selectedEndDate
		},
		prevMonth() {
			if (this.currentMonth === 0) {
				this.currentMonth = 11
				this.currentYear--
			} else {
				this.currentMonth--
			}
		},
		nextMonth() {
			if (this.currentMonth === 11) {
				this.currentMonth = 0
				this.currentYear++
			} else {
				this.currentMonth++
			}
		},
		prevYear() {
			this.currentYear--
		},
		nextYear() {
			this.currentYear++
		},
		openCalendar() {
			this.isCalendarOpen = true
		},
		cancelSelection() {
			this.selectedStartDate = this.value[0] ? new Date(this.value[0]) : ''
			this.selectedEndDate = this.value[1] ? new Date(this.value[1]) : ''
			this.isCalendarOpen = false
		},
		confirmSelection() {
			const startDate = this.selectedStartDate ? `${this.selectedStartDate.getFullYear()}-${String(this.selectedStartDate.getMonth() + 1).padStart(2, '0')}-${String(this.selectedStartDate.getDate()).padStart(2, '0')}` : null
			const endDate = this.selectedEndDate ? `${this.selectedEndDate.getFullYear()}-${String(this.selectedEndDate.getMonth() + 1).padStart(2, '0')}-${String(this.selectedEndDate.getDate()).padStart(2, '0')}` : startDate
			this.$emit('input', [startDate, endDate])
			this.isCalendarOpen = false
		},
		resetSelection() {
			this.selectedStartDate = ''
			this.selectedEndDate = ''
			this.$emit('input', ['', ''])
			this.isCalendarOpen = false
		}
	}
}
</script>

<style scoped lang="less">
.ui-date-picker {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1000;
	/* 遮罩层 */
	.overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0);
		z-index: 1;
	}
}
.date-picker {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	top: auto;
	height: 1060px;
	width: 100%;
	font-size: 32px;
	z-index: 2;
	background: white;
	border-radius: 8px 8px 0px 0px;
	.date-picker-title {
		height: 130px;
		line-height: 130px;
		text-align: center;
		font-weight: bold;
		font-size: 40px;
		color: #2b3346;
	}
	.calendar {
		z-index: 10;
		background: rgba(235, 245, 255, 0.7);
		padding: 0 14px 16px 14px;
	}
	.calendar-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 96px;
		font-weight: 400;
		font-size: 36px;
		color: #334266;
		.prev {
			display: flex;
			.year {
				width: 34px;
				height: 34px;
				background: url('./images/year_left.png') no-repeat;
				background-size: 100% 100%;
				margin-right: 70px;
			}
			.month {
				width: 34px;
				height: 34px;
				background: url('./images/month_left.png') no-repeat;
				background-size: 100% 100%;
			}
		}
		.next {
			display: flex;
			.year {
				width: 34px;
				height: 34px;
				background: url('./images/year_right.png') no-repeat;
				background-size: 100% 100%;
				margin-left: 70px;
			}
			.month {
				width: 34px;
				height: 34px;
				background: url('./images/month_right.png') no-repeat;
				background-size: 100% 100%;
			}
		}
	}
	.calendar-days {
		background: white;
		display: grid;
		grid-template-columns: repeat(7, 1fr);
		line-height: 74px;
		.day {
			font-weight: bold;
			height: 74px;
			text-align: center;
		}
		.date {
			cursor: pointer;
			margin-bottom: 8px;
			text-align: center;
			color: #2f3648;
			height: 74px;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.other-month {
			color: #ccc;
		}
		.selected {
			width: 74px;
			height: 74px;
			background: #20dbe9;
			color: white;
			border-radius: 50%;
		}
		.in-range {
			background: rgba(32, 219, 233, 0.15);
		}
		.selected-left-data {
			background: linear-gradient(to left, rgba(32, 219, 233, 0.15) ; 50%, transparent 50%);
		}
		.selected-right-data {
			background: linear-gradient(to right, rgba(32, 219, 233, 0.15) ; 50%, transparent 50%);
		}
	}
	.date-picker-bottom {
		height: 253px;
		background: white;
		.bottom-value {
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 36px;
			height: 92px;
			color: #b6bed0;
			border: 1px solid #e3eeff;
			.label {
				margin-right: 20px;
			}
			.value {
				color: #334266;
			}
			.line {
				display: inline-block;
				flex-shrink: 0;
				width: 32px;
				height: 1px;
				background: none;
				position: relative;
				margin: 0 20px;
				&::before {
					content: '.........';
					position: absolute;
					left: -14px;
					top: -15px;
					text-align: center;
					color: #b6bed0;
					font-size: 20px;
				}
			}
		}
		.btn-box {
			height: 160px;
			display: flex;
			justify-content: center;
			align-items: center;
			.cancel-btn {
				width: 280px;
				height: 96px;
				background: #ffffff;
				border: 3px solid #2b5fda;
				font-size: 36px;
				color: #2b5fda;
				text-align: center;
				line-height: 96px;
				border-radius: 8px;
			}
			.confirm-btn {
				width: 280px;
				height: 96px;
				background: #2b5fda;
				border-radius: 8px 8px 8px 8px;
				font-size: 36px;
				color: #ffffff;
				line-height: 96px;
				text-align: center;
			}
			.reset-btn {
				width: 280px;
				height: 96px;
				background: #2b5fda;
				border-radius: 8px;
				font-size: 36px;
				color: #ffffff;
				line-height: 96px;
				text-align: center;
				margin: 0 40px;
				background: #ffffff;
				border: 3px solid #2b5fda;
				color: #2b5fda;
			}
		}
	}
}
</style>
