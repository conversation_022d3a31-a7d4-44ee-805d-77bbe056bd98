<template>
	<div class="ui-carousel-box" @touchstart="touchStart" @touchend="touchEnd" @touchmove="touchMove">
		<div class="carousel" :style="carouselStyle">
			<div v-for="(item, index) in newData" :key="index" class="carousel-item" :class="{ active: currentIndex === index }">
				<slot :data="item"></slot>
			</div>
		</div>
		<div class="carousel-condition" :style="{ width: contentWidth + 'px', marginTop: conditionTop + 'px' }" v-if="data.length > 1">
			<div :class="['condition-item', currentIndex === index && 'current']" v-for="(page, index) in paginatedPages" :key="index" @click="setCurrentPage(index)">
				{{ page }}
			</div>
		</div>
	</div>
</template>
<script>
export default {
	name: 'uiCarouselBox',
	props: {
		data: {
			type: Array,
			default: () => []
		},
		contentWidth: {
			type: Number,
			default: 850
		},
		conditionTop: {
			type: Number,
			default: 26
		},
		isReverse: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			currentIndex: 0,
			startX: 0,
			endX: 0,
			transitionEnabled: false
		}
	},
	computed: {
		newData() {
			if (this.isReverse) {
				const arr = [...this.data].reverse()
				return arr
			}
			return [...this.data]
		},
		carouselStyle() {
			return {
				width: `${this.newData.length * this.contentWidth}px`,
				transform: `translateX(-${this.currentIndex * this.contentWidth}px)`,
				transition: this.transitionEnabled ? 'transform 0.5s ease-in-out' : 'none'
			}
		},
		totalPages() {
			return Math.ceil(this.newData.length)
		},
		paginatedPages() {
			const a = Array.from({ length: this.totalPages }, (_, i) => this.getLetterFromIndex(i))
			if (this.isReverse) {
				return a.reverse()
			}
			return a
		}
	},
	watch: {
		newData: {
			handler(val) {
				if (val.length === 0) return
				this.transitionEnabled = false
				this.currentIndex = this.isReverse ? val.length - 1 : 0
				this.$nextTick(() => {
					setTimeout(() => {
						this.transitionEnabled = true
					}, 0)
				})
			},
			immediate: true
		}
	},
	methods: {
		touchStart(e) {
			this.startX = e.touches[0].screenX
			this.endX = e.touches[0].screenX
		},
		changePage(flag) {
			if (flag) {
				this.currentIndex = this.currentIndex === 0 ? 0 : this.currentIndex - 1
			} else {
				this.currentIndex = this.currentIndex + 1 === this.newData.length ? this.currentIndex : this.currentIndex + 1
			}
		},
		touchEnd() {
			if (Math.abs(this.endX - this.startX) < 80) return
			this.changePage(this.endX - this.startX > 80)
		},
		touchMove(e) {
			this.endX = e.touches[0].screenX
		},
		getLetterFromIndex(index) {
			return String.fromCharCode(65 + index)
		},
		setCurrentPage(index) {
			this.currentIndex = index
		},
		prev() {
			this.currentIndex = this.currentIndex > 0 ? this.currentIndex - 1 : this.newData.length - 1
		},
		next() {
			this.currentIndex = this.currentIndex < this.newData.length - 1 ? this.currentIndex + 1 : 0
		},
		setActiveItem(index) {
			this.currentIndex = index
		}
	}
}
</script>
<style lang="less" scoped>
.ui-carousel-box {
	position: relative;
	width: 100%;
	overflow: hidden;
	padding: 0 104px;
	.carousel {
		display: flex;
		flex-direction: row;
		.carousel-item {
			flex-shrink: 0;
			opacity: 0.5;
			transform: scale(0.9);
			cursor: pointer;
			transition: transform 0.5s ease, opacity 0.5s ease;
			&.active {
				opacity: 1;
				transform: scale(1);
				-webkit-tap-highlight-color: transparent; /* 禁用触摸高亮 */
				user-select: none; /* 禁止选中 */
				touch-action: manipulation; /* 优化触摸行为 */
			}
		}
	}
	.carousel-condition {
		display: flex;
		height: 64px;
		align-items: center;
		justify-content: center;
		.condition-item {
			width: 48px;
			height: 48px;
			background: #cce6ff;
			color: #5a90ff;
			border-radius: 50%;
			margin: 0 15px;
			text-align: center;
			line-height: 48px;
			font-size: 26px;
			&.current {
				width: 64px;
				height: 64px;
				background: #2b5fda;
				color: #fff;
				line-height: 64px;
			}
		}
	}
}
</style>
