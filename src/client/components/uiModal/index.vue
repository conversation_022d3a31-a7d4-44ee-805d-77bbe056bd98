<template>
	<div class="ui-modal" v-if="value" @click="clickableOutside ? hidd() : null">
		<div class="ui-modal-container" @click.stop>
			<div class="title">{{ title }}</div>
			<slot></slot>
		</div>
	</div>
</template>

<script>
export default {
	name: 'uiModal',
	props: {
		value: {
			type: Boolean,
			required: false
		},
		title: {
			type: String,
			default: '提示'
		},
		clickableOutside: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {}
	},
	methods: {
		hidd() {
			this.$emit('input', false)
		}
	}
}
</script>

<style lang="less" scoped>
.ui-modal {
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background: rgba(8, 5, 44, 0.6);
	padding: 300px 80px 40px;
	z-index: 1;
	.ui-modal-container {
		width: 920px;
		background: #ebf5ff;
		border-radius: 16px;
		padding: 44px;
		.title {
			font-size: 40px;
			color: #2b3346;
			line-height: 92px;
			text-align: center;
			font-weight: bold;
			margin-bottom: 14px;
		}
	}
}
</style>
