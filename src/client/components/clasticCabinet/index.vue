<template>
	<div class="clastic-cabinet"></div>
</template>
<script>
import WebsocketMixin from '@/client/mixins/websocket'
import { mapGetters } from 'vuex'
export default {
	name: 'clasticCabinet',
	computed: {
		...mapGetters({
			allConfig: 'app/allConfig'
		})
	},
	mixins: [WebsocketMixin],
	methods: {
		handleOpen() {
			const wristbandDrawerInfoObj = this.allConfig.wristbandDrawerInfo
			const params = {
				manufacturer: Number(wristbandDrawerInfoObj.manufacturer) || 2,
				path: wristbandDrawerInfoObj.path || '/dev/ttysWK1',
				baudRate: Number(wristbandDrawerInfoObj.baudRate) || 9600
			}
			this.ws.initLock(params)
		},
		// 打开柜门
		openLock() {
			this.ws.openLock()
		},
		// 关闭柜门
		closeLock() {
			this.ws.closeLock()
		}
	},
	mounted() {
		this.initConnect('controlLock')
	}
}
</script>
