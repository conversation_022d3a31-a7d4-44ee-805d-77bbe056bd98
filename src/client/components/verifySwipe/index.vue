<template>
	<div class="verify-swipe"></div>
</template>
<script>
import WebsocketMixin from '@/client/mixins/websocket'
import { mapGetters } from 'vuex'
export default {
	name: 'verifySwipe',
	data() {
		return {
			loading: false
		}
	},
	computed: {
		...mapGetters({
			allConfig: 'app/allConfig'
		})
	},
	mixins: [WebsocketMixin],
	methods: {
		handleOpen() {
			this.ws.initUsb()
		},
		// 返回数据处理
		handleMessage(message) {
			const { action, code, msg, data } = message
			switch (action) {
				case 'init':
					if (data.flag === 0) {
						this.ws.addKeyDownListener()
					}
					break
				case 'keyDownValue':
					this.loginSwipe(data.result)
					break
				default:
					break
			}
		},
		loginSwipe(id) {
			if (this.loading) {
				return
			}
			const braceletId = id.slice(-6)
			this.loading = true
			this.$http.webApi.swipeLogin({ braceletId }).then((res) => {
				const { data, code } = res
				if (code != 200 || Object.keys(data).length === 0) {
					this.loading = false
					this.$emit('error', `未查到数据!`)
				} else {
					data.tagId = braceletId
					this.$emit('changeValue', data)
				}
			})
		}
	},
	mounted() {
		this.initConnect('usb')
	}
}
</script>

<style lang="less" scoped>
.verify-swipe {
	position: relative;
	width: 724px;
	height: 724px;
	background: url('../../assets/images/verifyHome/swipe_bg.png') no-repeat;
	background-size: 100% 100%;
}
</style>
