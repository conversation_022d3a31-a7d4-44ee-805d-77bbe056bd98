<template>
	<div class="verify-gesture">
		<canvas ref="canvas" class="canvas" />
		<div class="grid" @mousedown="startSwipe" @mousemove="onSwipe" @mouseup="endSwipe" @mouseleave="endSwipe" @touchstart="startSwipe" @touchmove.prevent="onSwipe" @touchend="endSwipe">
			<div v-for="(item, index) in grid" :key="index" class="cell" :class="isActive(index)">
				<div class="cell-content"></div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	name: 'verifyGesture',
	props: {
		gestureType: {
			type: String,
			default: 'verify'
		}
	},
	data() {
		return {
			grid: Array(9).fill(0),
			activeCells: [],
			isDrawing: false,
			lineColor: '#20dbe9',
			highlightTimeout: null
		}
	},
	methods: {
		startSwipe(event) {
			this.isDrawing = true
			this.activeCells = []
			this.updateActiveCells(event)
			this.drawLine()
		},
		onSwipe(event) {
			if (this.isDrawing) {
				this.updateActiveCells(event)
				this.drawLine()
			}
		},
		updateActiveCells(event) {
			const cellIndex = this.getCellIndex(event)
			if (cellIndex !== null && !this.activeCells.includes(cellIndex)) {
				this.activeCells.push(cellIndex)
			}
		},
		getCellIndex(event) {
			const rect = this.$el.getBoundingClientRect()
			const clientX = event.touches ? event.touches[0].clientX : event.clientX
			const clientY = event.touches ? event.touches[0].clientY : event.clientY
			let x
			let y
			const a = Math.floor(clientX - rect.left)
			const b = Math.floor(clientY - rect.top)
			if (a > 64 && a < 104) {
				x = 0
			} else if (a > 340 && a < 380) {
				x = 1
			} else if (a > 616 && a < 656) {
				x = 2
			}
			if (b > 64 && b < 104) {
				y = 0
			} else if (b > 340 && b < 380) {
				y = 1
			} else if (b > 616 && b < 656) {
				y = 2
			}
			const index = y * 3 + x
			return index >= 0 && index < 9 ? index : null
		},
		isActive(index) {
			if (this.activeCells.includes(index)) {
				if (this.isDrawing) {
					return { active: true }
				} else {
					return this.lineColor == '#20dbe9' ? { active: true, success: true } : { active: true, error: true }
				}
			}
			return {}
		},
		drawLine() {
			const canvas = this.$refs.canvas
			const ctx = canvas.getContext('2d')
			this.clearCanvas()
			ctx.strokeStyle = '#20dbe9'
			ctx.lineWidth = 5
			ctx.beginPath()
			if (this.activeCells.length > 0) {
				const start = this.getCellPosition(this.activeCells[0])
				ctx.moveTo(start.x, start.y)
				this.activeCells.forEach((index) => {
					const position = this.getCellPosition(index)
					ctx.lineTo(position.x, position.y)
				})
				ctx.stroke()
			}
		},
		drawResultLine() {
			const canvas = this.$refs.canvas
			const ctx = canvas.getContext('2d')
			this.clearCanvas()
			ctx.strokeStyle = this.lineColor
			ctx.lineWidth = 5
			ctx.beginPath()
			if (this.activeCells.length > 0) {
				const start = this.getCellPosition(this.activeCells[0])
				ctx.moveTo(start.x, start.y)
				this.activeCells.forEach((index) => {
					const position = this.getCellPosition(index)
					ctx.lineTo(position.x, position.y)
				})
				ctx.stroke()
			}
		},
		async endSwipe() {
			if (this.activeCells.length < 2) {
				this.activeCells = []
				return
			}
			this.isDrawing = false
			const isSuccess = await this.verifyPattern()
			this.lineColor = isSuccess ? '#20dbe9' : '#FF5376'
			this.drawResultLine()
		},
		async verifyPattern() {
			if (this.gestureType === 'verify') {
				try {
					const res = await this.$http.webApi.gestureLogin({ gesture: this.activeCells.toString() })
					console.log(res)
					if (res.code == 200) {
						this.$emit('changeValue', res.data)
						// this.$store.commit('user/setUserInfo', res.data)
						this.resetActiveCellsAndCanvas(true)
						return true
					} else {
						this.$emit('error', `未查询到手势信息!`)
						this.resetActiveCellsAndCanvas()
						return false
					}
				} catch (error) {
					this.resetActiveCellsAndCanvas()
					return false
				}
			} else {
				this.$emit('gestureValue', this.activeCells.toString())
				return Promise.resolve(true)
			}
		},
		resetActiveCellsAndCanvas(flag) {
			if (this.highlightTimeout) {
				clearTimeout(this.highlightTimeout)
			}
			this.highlightTimeout = setTimeout(() => {
				this.activeCells = []
				this.clearCanvas()
				this.lineColor = '#20dbe9'
				if (!flag) {
					this.$emit('error', `输入手势密码!`)
				}
			}, 3000)
		},
		getCellPosition(index) {
			const cellWidth = 276
			const cellHeight = 276
			const x = (index % 3) * cellWidth + 84
			const y = Math.floor(index / 3) * cellHeight + 84
			return { x, y }
		},
		clearCanvas() {
			const canvas = this.$refs.canvas
			const ctx = canvas && canvas.getContext('2d')
			ctx && ctx.clearRect(0, 0, canvas.width, canvas.height)
		}
	},
	mounted() {
		const canvas = this.$refs.canvas
		canvas.width = 720
		canvas.height = 720
	}
}
</script>

<style lang="less" scoped>
.verify-gesture {
	position: relative;
	width: 724px;
	height: 724px;
	.canvas {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		pointer-events: none;
	}
	.grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 108px;
		width: 100%;
		height: 100%;
	}
	.cell {
		width: 100%;
		height: 100%;
		cursor: pointer;
		border-radius: 50%;
		background: #ffffff;
		border: 3px solid #d6e2f8;
		display: flex;
		justify-content: center;
		align-items: center;
		.cell-content {
			width: 40px;
			height: 40px;
			border-radius: 50%;
			background: #d6e2f8;
		}
		&.active {
			border: 3px solid #20dbe9;
			.cell-content {
				background: #20dbe9;
			}
		}
		&.success {
			border: 3px solid #20dbe9;
			.cell-content {
				background: #20dbe9;
			}
		}
		&.error {
			border: 3px solid #ff5376;
			.cell-content {
				background: #ff5376;
			}
		}
	}
}
</style>
