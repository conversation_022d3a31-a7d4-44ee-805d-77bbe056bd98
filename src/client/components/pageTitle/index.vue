<template>
	<div class="page-title" v-if="this.$route.meta.showTitle">
		<div class="title-left" @click="goBack()" v-if="showLeft">
			<img src="./images/back.png" alt="" />
			{{ $t(`头部.返回`) }}
		</div>
		<div class="title-text">{{ text && $t(`头部.${text}`) }}</div>
		<div class="title-right" @click="goHome()">
			{{ $t(`头部.首页`) }}
			<img src="./images/home.png" alt="" />
		</div>
	</div>
</template>

<script>
export default {
	name: 'pageTitle',
	data() {
		return {}
	},
	computed: {
		text() {
			return this.$route.meta.title ? this.$route.meta.title : ''
		},
		showLeft() {
			const routeList = ['/settings', '/storageSituation', '/selectBack', '/storageSituationDetail', '/selectItems', '/keepLocater']
			return !routeList.includes(this.$route.path)
		}
	},
	methods: {
		goBack() {
			this.$router.go(-1)
		},
		goHome() {
			this.$router.push('/home')
		}
	}
}
</script>

<style lang="less" scoped>
.page-title {
	height: 140px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding: 0 40px;
	.title-left,
	.title-right {
		width: 170px;
		height: 60px;
		background: #ffffff;
		border-radius: 40px;
		opacity: 0.7;
		font-size: 26px;
		color: #5f709a;
		text-align: center;
		line-height: 60px;
		img {
			width: 36px;
			height: 36px;
			vertical-align: middle;
		}
	}
	.title-text {
		font-size: 48px;
		color: #2b3346;
		line-height: 140px;
		font-weight: bold;
	}
}
</style>
