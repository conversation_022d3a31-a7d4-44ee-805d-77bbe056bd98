import pageHeader from './pageHeader'
import pageTitle from './pageTitle'
import uiSelect from './uiSelect'
import uiInput from './uiInput'
import uiSwitch from './uiSwitch'
import uiRadio from './uiRadio'
import uiNoData from './uiNoData'
import uiDatePicker from './uiDatePicker'
import uiSteps from './uiSteps'
import cabinetTable from './cabinetTable'
import uiCarouselBox from './uiCarouselBox'
import verifyGesture from './verifyGesture'
import uiModal from './uiModal'
import uiMessage from './uiMessage'
import faceBox from './faceBox'
import uiButton from './uiButton'
import verifySwipe from './verifySwipe'
import verifyCriminal from './verifyCriminal'
import fingerprintBox from './fingerprintBox'
import keyboardSelect from './keyboardSelect'
import uiGoVideo from './uiGoVideo'
import uiLoading from './uiLoading'
import signatureModal from './signatureModal'
import clasticCabinet from './clasticCabinet'
export { uiMessage }
export default {
	install(Vue) {
		Vue.component('uiInput', uiInput)
		Vue.component('uiSelect', uiSelect)
		Vue.component('pageTitle', pageTitle)
		Vue.component('pageHeader', pageHeader)
		Vue.component('uiSwitch', uiSwitch)
		Vue.component('uiRadio', uiRadio)
		Vue.component('uiNoData', uiNoData)
		Vue.component('uiDatePicker', uiDatePicker)
		Vue.component('uiSteps', uiSteps)
		Vue.component('cabinetTable', cabinetTable)
		Vue.component('uiCarouselBox', uiCarouselBox)
		Vue.component('verifyGesture', verifyGesture)
		Vue.component('uiModal', uiModal)
		Vue.component('faceBox', faceBox)
		Vue.component('uiButton', uiButton)
		Vue.component('verifySwipe', verifySwipe)
		Vue.component('verifyCriminal', verifyCriminal)
		Vue.component('fingerprintBox', fingerprintBox)
		Vue.component('keyboardSelect', keyboardSelect)
		Vue.component('uiGoVideo', uiGoVideo)
		Vue.component('uiLoading', uiLoading)
		Vue.component('signatureModal', signatureModal)
		Vue.component('clasticCabinet', clasticCabinet)
		Vue.prototype.$baseTip = uiMessage
	}
}
