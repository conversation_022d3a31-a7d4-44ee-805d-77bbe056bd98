<template>
	<div class="fingerprint-box">
		<div v-if="type != 'verify'" class="register-finger" :class="{ 'show-border': fingerImage }">
			<img :src="fingerImage || (fingerprintShowStatus ? fingerprintBg : fingerprintIng)" alt="" />
		</div>
		<div v-else class="register-verify">
			<img :src="fingerprintShowStatus ? fingerprintBg : fingerprintIng" alt="" />
		</div>
	</div>
</template>
<script>
import WebsocketMixin from '@/client/mixins/websocket'
import { mapGetters } from 'vuex'
const BASE64_PREFIX = 'data:image/jpeg;base64,'
export default {
	name: 'fingerprintBox',
	props: {
		type: {
			type: String,
			default: 'verify'
		}
	},
	data() {
		return {
			isDestroy: false,
			fingerprintShowStatus: true,
			fingerprintBg: require('../../assets/images/verifyHome/fingerprint_bg.png'),
			fingerprintIng: require('../../assets/images/verifyHome/fingerprint_ing.png'),
			fingerImage: '',
			fingerStatusing: false
		}
	},
	computed: {
		...mapGetters({
			allConfig: 'app/allConfig'
		})
	},
	mixins: [WebsocketMixin],
	methods: {
		handleOpen() {
			this.initSignFinger()
		},
		initSignFinger() {
			const info = this.allConfig.signFingerInfo
			const params = {
				manufacturer: info.manufacturer,
				boardType: info.boardType,
				path: info.path
			}
			this.ws.initSignFinger(params)
		},
		// 返回数据处理
		handleMessage(message) {
			const { code, msg, data, action } = message
			if (code != 0) {
				if (['init', 'close', 'error'].includes(action)) {
					this.linkStatus = -1
				}
				return this.$baseTip.info(data?.message || msg || '数据异常！')
			}
			const { signFingerData } = data
			switch (action) {
				case 'init':
					if (data.flag === 0) {
						this.againCapture()
					}
					break
				case 'startFinger':
					this.fingerprintShowStatus = false
					break
				case 'fingerOK':
					this.$emit('takeStatus')
					this.handleFingerImage(signFingerData)
					break
				case 'closeDevices':
					this.againCapture()
					break
				default:
					break
			}
		},
		handle(type) {
			if (this.isDestroy) {
				return
			}
			if (!this.ws) {
				return this.connect()
			}
			const info = this.allConfig.signFingerInfo
			const params = {
				manufacturer: info.manufacturer,
				boardType: info.boardType,
				path: info.path
			}
			this.ws[type](params)
		},
		handleFingerImage(signFingerData) {
			if (this.fingerStatusing) return
			this.fingerStatusing = true
			const { fingerData, fingerFeature } = signFingerData[0]?.fingerInfo || {}
			this.fingerImage = fingerData ? BASE64_PREFIX + fingerData : ''
			if (this.type === 'verify') {
				this.fingerCompareManyByFingerFeature(fingerFeature)
			} else {
				this.fingerStatusing = false
				this.$emit('fingerValue', fingerFeature)
			}
		},
		fingerCompareManyByFingerFeature(fingerFeature) {
			this.$http.webApi
				.fingerCompareManyByFingerFeature({ fingerFeature })
				.then((res) => {
					const { code, data, msg } = res
					if (code != 200 || !data) {
						this.$emit('error', msg)
						// this.fingerStatusing = false
						// this.againCapture()
						return
					}
					const person = data[0]
					if (person) {
						this.$emit('fingerValue', person)
					} else {
						this.$emit('error', `未查询到指纹信息,请继续识别!`)
						// this.fingerStatusing = false
						// this.againCapture()
					}
				})
				.catch(() => {
					this.fingerStatusing = false
					this.againCapture()
				})
		},
		againCapture() {
			this.handle('onlyFinger')
		},
		closeDevices() {
			this.handle('closeDevices')
		}
	},
	mounted() {
		this.initConnect('signFinger')
	},
	beforeDestroy() {
		this.isDestroy = true
	}
}
</script>

<style lang="less" scoped>
.fingerprint-box {
	position: relative;
	width: 724px;
	height: 724px;
	.register-finger {
		width: 724px;
		height: 724px;
		border-radius: 50%;

		// padding: 100px;
		&.show-border {
			border: 8px solid #9ec5ff;
			padding: 100px;
		}
	}
	img {
		width: 100%;
		height: 100%;
	}
}
</style>
