<template>
	<div class="ui-input-box">
		<input class="ui-input" :value="value" @input="updateValue" :placeholder="placeholder" :disabled="disabled" :maxlength="maxLength || null" />
		<i v-show="value && !disabled" style="font-size: 40px" :class="['iconfont', 'form-modal-close', 'del-icon']" @click="del"></i>
	</div>
</template>

<script>
export default {
	name: 'uiInput',
	props: {
		value: {
			type: String
		},
		placeholder: {
			type: String,
			default: ''
		},
		disabled: {
			type: Boolean,
			default: false
		},
		maxLength: {
			type: Number,
			default: null
		},
		numeric: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		del() {
			this.$emit('input', '')
		},
		updateValue(e) {
			let inputValue = e.target.value
			if (this.numeric) {
				inputValue = this.filterNumericInput(inputValue)
				e.target.value = inputValue
			}
			this.$emit('input', inputValue)
		},
		filterNumericInput(value) {
			return value.replace(/[^\d]/g, '')
		}
	}
}
</script>

<style lang="less" scoped>
.ui-input-box {
	width: 100%;
	height: 96px;
	background: #ffffff;
	position: relative;
	.del-icon {
		position: absolute;
		top: 28px;
		right: 10px;
		font-size: 40px;
	}
}
.ui-input {
	width: 100%;
	height: 96px;
	background: #ffffff;
	border-radius: 8px;
	border: 1px solid #bfd7ff;
	padding-left: 57px;
	padding-right: 50px;
	font-size: 40px;
	color: #3663b3;
	line-height: 40px;
	&::placeholder {
		color: #a2acc6;
	}
	&:focus {
		border: 4px solid #20dbe9;
	}
	&:disabled {
		background: #d6e2f8;
		color: #3663b3;
	}
}
</style>
