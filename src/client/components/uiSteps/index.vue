<template>
	<div class="ui-steps" :class="`steps-${current}`">
		<div v-for="(item, idx) in stepsData" :key="idx" class="step" :class="current >= idx && 'active'">{{ item }}</div>
	</div>
</template>
<script>
export default {
	name: 'uiSteps',
	props: {
		current: {
			type: Number,
			default: 0
		},
		stepsData: {
			type: Array,
			default: () => []
		}
	}
}
</script>
<style lang="less" scoped>
.ui-steps {
	height: 74px;
	display: flex;
	background: url('./images/first.png') no-repeat;
	background-size: 100% 100%;
	&.steps-1 {
		background: url('./images/second.png') no-repeat;
		background-size: 100% 100%;
	}
	&.steps-2 {
		background: url('./images/third.png') no-repeat;
		background-size: 100% 100%;
	}
	.step {
		flex: 1;
		text-align: center;
		font-size: 36px;
		color: #3663b3;
		line-height: 74px;
		&.active {
			color: #fff;
		}
	}
}
</style>
