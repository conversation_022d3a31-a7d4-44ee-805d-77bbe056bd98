<template>
	<div class="ipc-renderer" v-show="false">
		<video class="video-box" :style="videoStyleComputed" :id="videoId" autoplay playsinline></video>
		<canvas :style="canvasStyleComputed" :id="canvasId"></canvas>
	</div>
</template>

<script>
import { getMediaDevices, handleCameraCof } from '@/common/libs/util'
import { mapGetters } from 'vuex'
import { communicationModeDic } from '@/common/libs/options'
const electronAPI = window.electronAPI || null
export default {
	name: 'electronAPI',
	computed: {
		...mapGetters({
			allConfig: 'app/allConfig',
			config: 'app/config'
		}),
		videoStyleComputed() {
			const rotateY = this.videoClass.rotateY / 2
			const rotateX = this.videoClass.rotateX / 2
			let transform = ''
			if (rotateY != 0 && rotateX == 0) {
				transform = `rotateY(${rotateY}turn)`
			} else if (rotateY == 0 && rotateX != 0) {
				transform = `rotate(${rotateX}turn)`
			} else {
				transform = `rotate(${rotateX}turn) rotateX(${rotateY}turn)`
			}
			return {
				height: `${this.videoStyle.height}px`,
				width: `${this.videoStyle.width}px`,
				transform
			}
		},
		canvasStyleComputed() {
			return {
				height: `${this.videoStyle.height}px`,
				width: `${this.videoStyle.width}px`
			}
		}
	},
	data() {
		return {
			videoStream: null,
			videoStyle: {
				width: 500,
				height: 500
			},
			videoClass: {
				rotateY: 0,
				rotateX: 0
			},
			videoId: '#videoId',
			canvasId: '#canvasId',
			currentCameraDeviceId: ''
		}
	},
	mounted() {
		this.initIpc()
	},
	methods: {
		onlyOpenParseStringToArray(input) {
			const lockInfo = this.allConfig.lockInfo
			let oldNum = 0
			let door = input
			let address = '1'
			end: for (const key in lockInfo) {
				const lock = lockInfo[key]
				const maxNumArr = lock.maxNum.split(',') || []
				const lockPlateArr = lock.lockPlate.split(',') || []
				for (let i = 0; i < maxNumArr.length; i++) {
					if (door > Number(maxNumArr[i]) + oldNum) {
						oldNum += maxNumArr[i]
					} else {
						door = door - oldNum
						address = lockPlateArr[i]
						break end
					}
				}
			}
			const result = { address, door }
			return result
		},
		parseStringToArray(input) {
			const lockInfo = this.allConfig.lockInfo
			let name = 'A'
			let door = input
			let oldNum = 0
			for (const key in lockInfo) {
				const lock = lockInfo[key]
				const sum = lock.maxNum.split(',').reduce((accumulator, currentValue) => {
					return accumulator + parseInt(currentValue, 10)
				}, 0)
				if (door > sum + oldNum) {
					oldNum += sum
				} else {
					door = door - oldNum
					name = key
					break
				}
			}
			const result = name + this.padZero(door)
			console.log(result)
			return result
		},
		padZero(numberStr) {
			return numberStr < 10 ? `0${numberStr}` : numberStr
		},
		initIpc() {
			if (!electronAPI) {
				return
			}
			electronAPI.on('usb-device', (arg) => {
				const { action } = arg
				switch (action) {
					case 'getVideoDevice':
						this.getVideoDevices(arg)
						break
					case 'openCamera':
						this.openCamera(arg)
						break
					case 'rotateCamera':
						this.rotateCamera(arg)
						break
					case 'captureImage':
						this.captureImage(arg)
						break
					case 'closeCamera':
						this.closeCamera(true)
						break
					default:
						break
				}
			})
			electronAPI.on('remote-open-lock', async (arg) => {
				console.log('arg', arg)
				if (this.config.communicationMode != communicationModeDic.ONLINE) {
					return electronAPI.send('remote-open-lock-result', { code: 500, data: false, msg: '应用处于离线版本无法操作' })
				}
				if (arg.onlyOpen) {
					const arr = []
					const ids = []
					if (arg.openAllLock) {
						const { data } = await this.$http.appApi.getSidesAllList()
						let result = []
						let maxGf = 0
						data.forEach((item) => {
							maxGf += Number(item.gf)
						})
						result = Array.from({ length: maxGf }, (_, i) => i + 1)
						result.forEach((item, index) => {
							arr[index] = this.parseStringToArray(item)
						})
					} else {
						arg.onlyLockList.forEach((item, index) => {
							arr[index] = this.parseStringToArray(item.inboxNum)
							ids[index] = item.inboxNum
						})
					}
					const lockParams = {
						openLockList: arr,
						policeIdCard: arg.policeIdCard,
						ids
					}
					electronAPI.send('remote-open-lock-result', { data: true, msg: '成功' })
					this.$router.push({ name: 'onlyOpenAnimationTips', query: lockParams })
				} else {
					const flag = arg && arg.openList && arg.personMagicId
					const { openList, status } = arg
					if (flag) {
						let useType = null
						switch (status) {
							case '1':
								useType = 'crwp'
								break
							case '2':
								useType = 'qcwp'
								break
							case '3':
								useType = 'lsjc'
								break
							default:
								useType = 'jcgh'
								break
						}
						const params = openList.map((item) => {
							return {
								id: item.inboxNum,
								userId: arg.personMagicId,
								num: this.parseStringToArray(item.inboxNum),
								policeIdCard: arg.policeIdCard,
								policeName: arg.policeName,
								manageIdCard: arg.manageIdCard,
								manageName: arg.manageName,
								personMagicId: arg.personMagicId,
								personIdCard: arg.personIdCard,
								personName: arg.personName
							}
						})
						if (this.$route.name == 'home') {
							this.$store.commit('app/setUseType', useType)
							this.$store.commit('app/setNextUser', params)
							this.$store.commit('app/setIsRemote', true)
							this.$router.push('/animationTips')
							electronAPI.send('remote-open-lock-result', { data: true, msg: '成功' })
						} else {
							electronAPI.send('remote-open-lock-result', { code: 500, data: false, msg: '应用正在使用中，无法操作' })
						}
					} else {
						electronAPI.send('remote-open-lock-result', { code: 500, data: false, msg: '操作失败，参数有误！' })
					}
				}
			})
		},
		async getVideoDevices(options) {
			const videoList = await getMediaDevices()
			const devices = videoList.map((item) => {
				return {
					label: item.label,
					deviceId: item.deviceId
				}
			})
			this.ipcResult({ data: devices, type: options.action, success: true })
		},
		async openCamera(options) {
			this.videoStyle.width = options.width || this.videoStyle.width
			this.videoStyle.height = options.height || this.videoStyle.height
			if (this.videoStream) {
				if (options.deviceId == this.currentCameraDeviceId) {
					return this.ipcResult({ data: null, type: options.action, success: true })
				} else {
					this.closeCamera(false)
				}
			}
			this.currentCameraDeviceId = options.deviceId
			const devices = await getMediaDevices((device) => {
				return device.kind == 'videoinput' && device.deviceId == this.currentCameraDeviceId
			})
			if (devices.length == 0) {
				return this.ipcResult({ data: null, type: options.action, success: false })
			}
			const device = devices[0]
			const constraints = {
				video: {
					width: this.videoStyle.width,
					height: this.videoStyle.height,
					// frameRate: 15, // 帧率 最大60
					deviceId: { exact: device.deviceId }
				},
				audio: false
			}
			navigator.mediaDevices
				.getUserMedia(constraints)
				.then((stream) => {
					this.videoStream = stream
					const videoDom = document.getElementById(this.videoId)
					try {
						videoDom.srcObject = this.videoStream
					} catch (error) {
						videoDom.src = URL.createObjectURL(this.videoStream)
					}
					this.ipcResult({ data: null, type: options.action, success: true })
				})
				.catch(() => {
					this.ipcResult({ data: null, type: options.action, success: false })
				})
		},
		rotateCamera(options) {
			this.videoClass.rotateY = options.rotateY
			this.videoClass.rotateX = options.rotateX
			this.ipcResult({ data: null, type: options.action, success: true })
		},
		captureImage(options) {
			if (!this.videoStream) {
				return this.ipcResult({ data: null, type: options.action, success: false })
			}
			const canvas = handleCameraCof(this.videoStyle, this.videoClass, this.canvasId, this.videoId)
			if (canvas) {
				this.ipcResult({ data: canvas.toDataURL('image/png'), type: options.action, success: true })
			} else {
				this.ipcResult({ data: '人脸抓拍失败！', type: options.action, success: false })
			}
		},
		closeCamera(isResult = true) {
			if (!this.videoStream) {
				return isResult && this.ipcResult({ data: null, type: 'closeCamera', success: true })
			}
			this.videoStream.getTracks().forEach(function (track) {
				track.stop()
			})
			this.videoStream = null
			this.currentCameraDeviceId = ''
			isResult && this.ipcResult({ data: null, type: 'closeCamera', success: true })
		},
		ipcResult(result) {
			if (!electronAPI) {
				return
			}
			electronAPI.send('usb-device-result', result)
		}
	},
	beforeDestroy() {
		this.closeCamera(false)
		if (electronAPI) {
			electronAPI.removeAllListeners('usb-device')
			electronAPI.removeAllListeners('usb-device-result')
		}
	}
}
</script>

<style lang="less" scoped>
.ipc-renderer {
	.video-box {
		object-fit: cover;
		transform-origin: center;
	}
}
</style>
