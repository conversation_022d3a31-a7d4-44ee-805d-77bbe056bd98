<template>
	<div class="face-box">
		<div class="circle-container">
			<div class="gradient-circle">
				<div class="inner-circle"></div>
			</div>
		</div>
		<div class="face-content">
			<div v-show="!cameraState" class="empty"></div>
			<video v-show="cameraState && (type == 'verify' || !photoImg)" :style="videoStyleComputed" :id="videoId" autoplay playsinline></video>
			<canvas v-show="false" :style="canvasStyleComputed" :id="canvasId"></canvas>
			<img v-show="photoImg && type != 'verify'" :src="photoImg" />
			<div class="count-down" v-if="countDown > 0 && type != 'verify'">{{ countDown }}S</div>
		</div>
	</div>
</template>
<script>
import WebsocketMixin from '@/client/mixins/websocket'
import { mapGetters } from 'vuex'
import { getMediaDevices, handleCameraCof } from '@/common/libs/util'
export default {
	name: 'faceBox',
	mixins: [WebsocketMixin],
	props: {
		videoId: {
			type: String,
			default: 'faceVideoId'
		},
		type: {
			type: String,
			default: 'verify'
		}
	},
	data() {
		return {
			canvasId: 'faceCanvasId',
			cameraState: false,
			canvasStyleComputed: {
				height: `660px`,
				width: `660px`
			},
			photoImg: '',
			deviceId: '',
			videoStream: null,
			photoTimer: null,
			countDown: 0,
			openStatus: true
		}
	},
	computed: {
		...mapGetters({
			allConfig: 'app/allConfig'
		}),
		videoStyleComputed() {
			const { rotateY, rotateX } = this.allConfig.colorCameraInfo
			const colorRotateY = rotateY / 2
			const colorRotateX = rotateX / 2
			let transform = ''
			if (colorRotateY != 0 && colorRotateX == 0) {
				transform = `rotateY(${colorRotateY}turn)`
			} else if (colorRotateY == 0 && colorRotateX != 0) {
				transform = `rotate(${colorRotateX}turn)`
			} else {
				transform = `rotate(${colorRotateX}turn) rotateX(${colorRotateY}turn)`
			}
			const offsetX = -200 // 向左偏移的像素值
			const offsetY = -200 // 向上偏移的像素值
			transform += ` translate(${offsetX}px, ${offsetY}px)`
			return {
				height: `1060px`,
				width: `1060px`,
				transform
			}
		}
	},
	methods: {
		async getDevice() {
			const cameraList = await getMediaDevices()
			const flag = cameraList.findIndex((item) => {
				return item.deviceId === this.allConfig.colorCameraInfo.deviceId
			})
			if (flag != -1) {
				this.deviceId = this.allConfig.colorCameraInfo.deviceId
			} else {
				this.deviceId = cameraList[0].deviceId || 'onlyOne'
			}
			await this.openCamera()
		},
		async openCamera() {
			const constraints = {
				video: {
					width: 1080,
					height: 1080,
					deviceId: this.deviceId == 'onlyOne' ? '' : { exact: this.deviceId }
				},
				audio: false
			}
			navigator.mediaDevices
				.getUserMedia(constraints)
				.then((stream) => {
					this.$nextTick(() => {
						this.videoStream = stream
						const videoDom = document.getElementById(this.videoId)
						if (!this.openStatus || !videoDom) {
							this.closeCamera()
							return
						}
						try {
							videoDom.srcObject = stream
						} catch (error) {
							videoDom.src = URL.createObjectURL(stream)
						}
						this.cameraState = true
						this.takePhoto()
					})
				})
				.catch(() => {
					this.cameraState = false
				})
		},
		takePhoto() {
			this.$emit('takeStatus')
			this.photoImg = ''
			this.photoTimer && clearInterval(this.photoTimer)
			this.countDown = 3
			this.photoTimer = setInterval(() => {
				this.countDown--
				if (this.countDown <= 0) {
					this.faceRecognition()
					this.photoTimer && clearInterval(this.photoTimer)
				}
			}, 1000)
		},
		//识别人脸
		faceRecognition() {
			if (!this.videoStream) {
				return
			}
			const { rotateY, rotateX } = this.allConfig.colorCameraInfo
			const canvas = handleCameraCof(
				{
					width: 1060,
					height: 1060
				},
				{ rotateX: Number(rotateX), rotateY: Number(rotateY) },
				this.canvasId,
				this.videoId
			)
			if (canvas) {
				// 创建一个新的 canvas，用于裁剪中间部分
				const cropCanvas = document.createElement('canvas')
				const cropSize = 660 // 目标裁剪区域的宽高
				cropCanvas.width = cropSize
				cropCanvas.height = cropSize
				const ctx = cropCanvas.getContext('2d')
				const offsetX = 200
				const offsetY = 200
				ctx.drawImage(canvas, offsetX, offsetY, cropSize, cropSize, 0, 0, cropSize, cropSize)

				// 从裁剪后的 canvas 中生成 base64 数据
				const base64 = cropCanvas.toDataURL('image/jpeg')
				const params = {
					type: 1,
					base64
				}
				this.$http.webApi.getFaceDetectInfo(params).then((res) => {
					const { code, data } = res
					if (code != 200 || !data) {
						this.$emit('error', `${this.$t(`人脸识别.未获取到人脸`)}!`)
						this.continue()
						return
					}
					if (this.type === 'verify') {
						this.photoImg = params.base64
						this.faceCompareManyByBase64(params, data) // 数据库对比
					} else {
						const canvas = document.createElement('canvas')
						canvas.width = data.width + 100 < 660 ? data.width + 100 : 660
						canvas.height = data.height + 60 < 660 ? data.height + 60 : 660
						const x = data.x - 60 > 0 ? data.x - 60 + 200 : 200
						const y = data.y - 60 > 0 ? data.y - 60 + 200 : 200
						canvas.getContext('2d').drawImage(document.getElementById(this.canvasId), x, y, canvas.width, canvas.height, 0, 0, canvas.width, canvas.height)
						const minBase64 = canvas.toDataURL('image/jpeg')
						this.loadImage({
							...data,
							base64: params.base64,
							callback: (base64) => {
								this.photoImg = base64
							}
						})
						this.$emit('faceValue', minBase64)
						this.photoTimer && clearInterval(this.photoTimer)
					}
				})
			}
		},
		//增加人脸框
		loadImage(options) {
			const { base64, height, width, x, y } = options
			let image = new Image()
			image.src = base64
			image.onload = () => {
				// 在canvas上绘制人脸框
				const newCanvas = document.createElement('canvas')
				newCanvas.width = 660
				newCanvas.height = 660
				const ctx = newCanvas.getContext('2d')
				ctx.drawImage(image, 0, 0, newCanvas.width, newCanvas.height)
				ctx.strokeStyle = '#ff0000'
				ctx.lineWidth = 4
				ctx.strokeRect(x, y, width, height)
				options.callback && options.callback(newCanvas.toDataURL('image/jpeg'))
				image = null
			}
		},
		//根据抓拍去对比
		faceCompareManyByBase64(params) {
			this.$http.webApi
				.faceCompareManyByBase64(params)
				.then((res) => {
					const { code, data, msg } = res
					if (code != 200 || !data) {
						this.$emit('error', msg)
						this.continue()
						return
					}
					const person = data[0]
					if (person) {
						this.photoTimer && clearInterval(this.photoTimer)
						this.$emit('faceValue', person)
					} else {
						this.$emit('error', `${this.$t(`人脸识别.未查询到人脸信息`)},${this.$t(`人脸识别.请继续识别`)}!`)
						this.continue()
					}
				})
				.catch(() => {
					this.continue()
				})
		},
		//自动继续识别  做一个延迟效果
		continue() {
			setTimeout(() => {
				this.takePhoto()
			}, 2000)
		},
		closeCamera() {
			if (!this.videoStream || !(this.videoStream instanceof MediaStream)) {
				this.videoStream = null
				this.cameraState = false
				this.openStatus = false
				return
			}
			try {
				this.videoStream.getTracks().forEach((track) => {
					track.stop()
				})
			} catch (error) {
				console.error('Failed to stop camera tracks:', error)
			} finally {
				this.videoStream = null
				this.cameraState = false
				this.openStatus = false
			}
		},
		handleOpen() {
			this.initLight()
		},
		// 初始化补光灯
		initLight() {
			const info = this.allConfig.supplementLightInfo
			const params = {
				manufacturer: Number(info.manufacturer),
				path: info.path,
				baudRate: Number(info.baudRate)
			}
			this.ws.initLight(params)
		},
		// 返回数据处理
		handleMessage(message) {
			const { code, msg, data, action } = message
			switch (action) {
				case 'init':
					if (data.flag === 0) {
						this.ws.openLight()
					}
					break
				case 'closeLight':
					this.ws && this.ws.close()
					break
				default:
					break
			}
		}
	},
	beforeDestroy() {
		this.photoTimer && clearInterval(this.photoTimer)
		this.closeCamera()
		this.ws && this.ws.closeLight()
	},
	created() {
		this.getDevice()
		this.initConnect('light')
	}
}
</script>
<style lang="less" scoped>
.face-box {
	height: 724px;
	width: 724px;
	border-radius: 50%;
	position: relative;
	overflow: hidden;
	.circle-container {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 724px;
		width: 724px;
		.gradient-circle {
			width: 724px;
			height: 724px;
			border-radius: 50%;
			background: conic-gradient(rgba(38, 103, 255, 1), rgba(121, 100, 244, 1), rgba(94, 244, 255, 0));
			padding: 10px;
			display: flex;
			justify-content: center;
			align-items: center;
			animation: rotate 3s linear infinite;
			.inner-circle {
				width: 100%;
				height: 100%;
				background-color: #f0f0f0;
				border-radius: 50%;
			}
		}
	}
	.face-content {
		position: absolute;
		top: 32px;
		left: 32px;
		height: 660px;
		width: 660px;
		border-radius: 50%;
		overflow: hidden;
		.empty {
			width: 100%;
			height: 100%;
			background: url('../../assets/images/verifyHome/face_bg.png') no-repeat;
			background-size: 100% 100%;
		}
		img {
			width: 100%;
			height: 100%;
		}
		.count-down {
			position: absolute;
			top: 0;
			width: 100%;
			height: 74px;
			line-height: 74px;
			font-size: 40px;
			color: #ffffff;
			text-align: center;
			background: #80afff;
		}
	}
}
@keyframes rotate {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
</style>
