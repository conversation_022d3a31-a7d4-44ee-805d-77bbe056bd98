<template>
	<div class="no-data">
		<img src="./images/no_data.png" alt="" />
		<div>{{ text }}</div>
	</div>
</template>

<script>
export default {
	name: 'uiNoData',
	props: {
		text: {
			type: String,
			default: '暂无数据'
		}
	}
}
</script>

<style scoped lang="less">
.no-data {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	padding-top: 200px;
	img {
		width: 475px;
		height: 234px;
		margin-bottom: 30px;
	}
	font-weight: 400;
	font-size: 38px;
	color: #a2acc6;
}
</style>
