<template>
	<div :class="['ui-radio', value === label && 'selected']" :style="{ width: width }" @click="changeRadio()">
		<div class="icon"></div>
		<slot></slot>
	</div>
</template>
<script>
export default {
	name: 'uiRadio',
	props: {
		value: {
			type: String,
			default: ''
		},
		label: {
			type: String,
			default: ''
		},
		width: {
			type: String,
			default: '282px'
		}
	},
	methods: {
		changeRadio() {
			this.$emit('input', this.label)
		}
	}
}
</script>
<style lang="less" scoped>
.ui-radio {
	display: inline-flex;
	width: 282px;
	height: 100px;
	background: #ffffff;
	border-radius: 8px;
	border: 1px solid #bfd7ff;
	align-items: center;
	justify-content: center;
	font-size: 40px;
	color: #3663b3;
	& + & {
		margin-left: 50px;
	}
	.icon {
		width: 44px;
		height: 44px;
		border: 2px solid #20dbe9;
		border-radius: 50%;
		margin-right: 16px;
		position: relative;
	}
	&.selected {
		border: 4px solid #20dbe9;
		.icon::after {
			content: '';
			width: 24px;
			height: 24px;
			background: #20dbe9;
			position: absolute;
			left: 8px;
			top: 8px;
			border-radius: 100%;
		}
	}
}
</style>
