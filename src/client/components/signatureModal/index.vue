<template>
	<ui-modal :value="true" title="请在签名区签名">
		<div class="signature-modal">
			<div class="container" :class="{ 'no-data': !signboardData }">
				<div class="signboard-wrap">
					<canvas ref="canvasRef" width="800" height="360"></canvas>
				</div>
			</div>
			<div class="ui-modal-footer">
				<button class="btn cancel-btn" @click="cancel">取消</button>
				<button class="btn cancel-btn" @click="clearSignboard">重签</button>
				<button class="btn" @click="confirm">确定</button>
			</div>
		</div>
	</ui-modal>
</template>

<script>
export default {
	name: 'signatureModal',
	data() {
		return {
			modalShow: false,
			signboardData: '',
			isDrawing: false,
			lastPosition: { x: 0, y: 0 },
			canvasRef: null,
			context: null
		}
	},
	mounted() {
		this.addSignEventListener()
	},
	methods: {
		addSignEventListener() {
			this.canvasRef = this.$refs.canvasRef
			if (!this.canvasRef) {
				return
			}
			this.context = this.canvasRef.getContext('2d')
			this.canvasRef.addEventListener('touchstart', this.startDrawing)
			this.canvasRef.addEventListener('touchmove', this.draw)
			this.canvasRef.addEventListener('touchend', this.stopDrawing)
			this.canvasRef.addEventListener('mousedown', this.startDrawing)
			this.canvasRef.addEventListener('mousemove', this.draw)
			this.canvasRef.addEventListener('mouseup', this.stopDrawing)
			this.canvasRef.addEventListener('mouseout', this.stopDrawing)
		},

		removeSignEventListener() {
			if (!this.canvasRef) {
				return
			}
			this.canvasRef.removeEventListener('touchstart', this.startDrawing)
			this.canvasRef.removeEventListener('touchmove', this.draw)
			this.canvasRef.removeEventListener('touchend', this.stopDrawing)
			this.canvasRef.removeEventListener('mousedown', this.startDrawing)
			this.canvasRef.removeEventListener('mousemove', this.draw)
			this.canvasRef.removeEventListener('mouseup', this.stopDrawing)
			this.canvasRef.removeEventListener('mouseout', this.stopDrawing)
		},

		startDrawing(e) {
			e.preventDefault()
			this.isDrawing = true
			this.lastPosition = this.getMousePosition(e)
		},

		draw(e) {
			e.preventDefault()
			if (!this.isDrawing) return
			const currentPosition = this.getMousePosition(e)
			this.context.beginPath()
			this.context.moveTo(this.lastPosition.x, this.lastPosition.y)
			this.context.lineTo(currentPosition.x, currentPosition.y)
			this.context.stroke()
			this.lastPosition = currentPosition
			this.signboardData = this.canvasRef.toDataURL()
		},
		stopDrawing() {
			this.isDrawing = false
		},
		getMousePosition(e) {
			const rect = this.canvasRef.getBoundingClientRect()
			const { clientX, clientY } = e.touches ? e.touches[0] : e
			return {
				x: clientX - rect.left,
				y: clientY - rect.top
			}
		},

		clearSignboard() {
			this.context.clearRect(0, 0, this.canvasRef.width, this.canvasRef.height)
			this.signboardData = ''
		},
		cancel() {
			this.$emit('cancel')
		},
		async confirm() {
			if (!this.signboardData) {
				this.$baseTip.info('请完成签名')
				return
			}
			this.$emit('confirm', this.signboardData)
			// this.cancel()
			this.clearSignboard()
		}
	},

	beforeDestroy() {
		this.removeSignEventListener()
	}
}
</script>

<style lang="less" scoped>
.signature-modal {
	.container {
		display: flex;
		margin: 32px 0 48px;
		background: #f5faff;
		border: 1px solid #c0e0fe;
		position: relative;
		&.no-data::before {
			content: '签名区';
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 360px;
			font-size: 120px;
			color: #e1eefa;
		}
		.signboard-wrap {
			flex: 1;
			margin: 16px;
			background: transparent;
			border: 2px dashed #c0e0fe;
			border-radius: 4px;
			position: relative;
			display: flex;
			.btn-common {
				width: 112px;
				height: 36px;
				line-height: 36px;
				text-align: center;
				border-radius: 100px;
				font-size: 20px;
				cursor: pointer;
				position: absolute;
				right: 8px;
			}
		}
	}
}
</style>
