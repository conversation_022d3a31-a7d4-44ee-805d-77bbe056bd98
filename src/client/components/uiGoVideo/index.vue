<template>
  <div  class="ui-go-video-box">
    <div class="ui-go-video" :id="windowUniqueId"></div>
    <div class="btn" @click="getImg()">
      <img src="./images/take.png" alt="">
    </div>
  </div>
    
  </template>
  <script>
  import { mapGetters } from 'vuex'
  import GoVideoWebPlayer from "./lib/GoVideoWebPlayer.js";
  import { nanoid } from "nanoid";
  export default {
    name:"uiGoVideo",
    props: {
      initConfig: {
        type: Object,
        default: () => { return {}; }
      },
      playList: {
        type: Array,
        default: () => [],
      },
      getBase64: {
        type: Function
      },
      autoPlay: {
        type: Boolean,
        default: false,
      }
    },
    computed: {
      ...mapGetters({
        config: 'app/config'
      }),
    },
    watch: {
      playList: {
        deep: true,
        handler() {
          this.setWndNumByList();
          this.playPlayList();
        },
      },
    },
    data() {
      return {
        windowUniqueId: nanoid(),
        player: null,
        defaultConfiguration: {
          reconnect: true,
          isPlayback: false,
          wndNums: 1,
          maxSplit: 1,
          draggable: false,
          showBackgroundImage: true,
          keepLastFrame: false,
          topMenuKeys: ["Capture", "FullScreen", "Voice"],
          rightMenuKeys: [],
          timeLineMenuKeys: ["fullScreen"],
          defaultVolume: 100,
        },
      };
    },
    methods: {
      getImg(){
        let imgList=this.player.capture()
        this.$emit('getImgInfo',imgList[0])
      },
      setWndNumByList() {
        let list = this.playList.map(item => item && Object.keys(item).length !== 0);
        let len = this.player.wndNums;
        if (list.length !== 0) len = list.length === 1 ? 1 : list.length <= 4 ? 4 :  9;
        if (len !== this.player.wndNums) {
          this.player.setWndNums(len, true);
        }
      },
      playPlayList() {
        if (this.autoPlay && this.initConfig.isPlayback) {
          return this.recordVideoAuto(0);
        }
        this.playList.forEach((element, index) => {
          if (this.initConfig.isPlayback) {
            this.recordVideo({...element, wndNo: index });
          } else {
            this.playVideo({ ...element, wndNo: index });
          }
        });
      },
      switchEnvironment() {
        console.log(this.config)
        return `http://${this.config.outsideTakeHttp}`;
        // return `http://192.168.204.80:99`;
      },
      onCapture(e) {
        if (this.getBase64) {
          this.getBase64(e);
        } else {
          let link = document.createElement("a");
          link.href = e;
          link.setAttribute("download", `${new Date().Format("yyyy-MM-dd hh:mm:ss")}.png`);
          link.style.display = "none";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        };
      },
      initWindow() {
        let defaultConfiguration = { ...this.defaultConfiguration, ...this.initConfig };
        this.player = new GoVideoWebPlayer(this.windowUniqueId, defaultConfiguration);
        this.player.onCapture = this.onCapture;
      },
      initService() {
        const defaultRequest = new GoVideoWebPlayer.DefaultRequest({
          ip: this.switchEnvironment(),
          useCMS: true,
          isARStream: 0,
        });
        this.player.use(defaultRequest);

      },
      recordVideo(item) {
        let { channelId, deviceName, storageType, wndNo, date, config, customInfo = {} } = item;
        let defaultConfiguration = {
          customInfo, // 自定义信息
          focusFreeWnd: false,
          focusWndByKey: false,
          wndNo,
        };
        this.player.defaultPlayback(channelId, deviceName, storageType || 0, date, {
          ...defaultConfiguration,
          ...config,
        });
      },
      recordVideoAuto(idx) {
        if (idx  >= this.playList.length) return;
        let item = this.playList[idx];
        if (item) {
          this.recordVideo({...item, wndNo: idx });
          this.player.seekTime(item.config.startTime);
          this.player.setVolume(100, idx);
          this.player.onVideoSetted = () => {
            let btn = document.getElementsByClassName("gs-timeline-option-button gs-control-play")[0];
            btn.click();
            this.player.selectWnd(idx + 1);
            this.recordVideoAuto(idx + 1);
          };
        }
      },
      playVideo(item) {
        let { channelId, deviceName, wndNo, config, customInfo = {} } = item;
        let defaultConfiguration = {
          customInfo, // 自定义信息
          focusFreeWnd: false,
          focusWndByKey: false,
          wndNo,
        };
        this.player.defaultPlay(channelId, deviceName, 1, {
          ...defaultConfiguration,
          ...config,
        });
      },
      destroyVideo() {
        this.player.destroy();
      }
    },
    mounted() {
      this.initWindow();
      this.initService();
      // this.setWndNumByList()
      // this.playPlayList()
    },
    beforeDestroy() {
      this.destroyVideo();
    }
  };
  </script>
  
  <style src="./lib/assets/index.css" scoped></style>
  <style lang="less" scoped>
  .ui-go-video-box{
    width: 920px;
    height: 685px;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    .ui-go-video {
      width: 860px;
      height: 521px;
      background: #FFFFFF;
      border-radius: 8px 8px 8px 8px;
    }
    .btn{
      width: 180px;
      height: 74px;
      background: #00DCA7;
      border-radius: 80px;
      text-align: center;
      img{
        height: 100%;
      }
    }
  }
 
  </style>
  