.gs-display-none {
  display: none;
}

.gs-display-block {
  display: block;
}

.gs-background-image-none {
  background-image: '';
}

.gs-wrapper-div {
  position: relative;
  display: grid;
  min-height: 0;
  height: 100%;
  width: 100%;
}

.gs-playerdiv {
  position: relative;
  background-color: black;
  background-repeat: no-repeat;
  background-position: center center;
  display: inline-block;
  min-height: 0;
}

.gs-win-selected {
  outline: rgb(49, 236, 255) solid 3px;
}

.gs-background-no-video {
  background-image: url(../source/common/NoVideo.png);
}

.gs-background-no-video-only {
  background-image: url(../source/common/NoVideoOnly.png);
}

.gs-top-menu {
  width: 100%;
  height: 22px;
  background: rgba(35, 35, 36, 0.5);
  position: absolute;
  top: 0;
  color: white;
  z-index: 2;
  display: none;
  align-items: center;
  justify-content: space-between;
}
.gs-top-menu-ext {
  display: block !important;
  height: 40px;
  line-height: 20px;
}

.gs-top-menu-title {
  max-width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gs-mobile-top-menu {
  width: 100%;
  height: 34px;
  padding: 0 12px;
  box-sizing: border-box;
  display: none;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 0;
  color: white;
  z-index: 1;
}

.gs-mobile-bottom-menu {
  width: 100%;
  height: 34px;
  padding: 0 12px;
  box-sizing: border-box;
  display: none;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 0;
  z-index: 1;
}

.gs-mobile-top-menu-left {
  display: flex;
}

.gs-mobile-top-menu-left > div {
  margin-right: 5px;
}

.gs-mobile-bottom-menu-left {
  flex: 1;
  flex-direction: row;
  display: flex;
}

.gs-mobile-top-menu-left > div,
.gs-mobile-top-menu-right > div,
.gs-mobile-bottom-menu-left > div,
.gs-mobile-bottom-menu-right > div {
  width: 24px;
  height: 24px;
  background-size: contain;
}

.gs-info-div {
  color: white;
  position: absolute;
  height: 100%;
  width: 100%;
  text-align: center;
  background: black;
  display: flex;
  align-items: center;
  justify-content: center;
  background-repeat: no-repeat;
  background-position: center;
  background-position-y: 45%;
  background-size: 5%;
  padding-top: 15%;
  box-sizing: border-box;
  background-color: rgba(255, 255, 255, 0.3);
}

.gs-status-div {
  color: red;
  position: absolute;
  left: 0;
  bottom: 0;
  text-align: left;
  background: transparent;
  display: none;
  z-index: 1;
}

.gs-right-menu {
  position: absolute;
  background: white;
  z-index: 3;
  display: none;
  padding: 3px;
  width: 168px;
}

.gs-right-menu-item {
  border: none;
  background-color: white;
  font-size: 12px;
  color: black;
  padding-left: 30px;
  width: 100%;
  text-align: left;
  cursor: default;
}

.gs-right-menu-item:disabled {
  cursor: not-allowed;
}

.gs-right-menu-item:hover {
  background-color: #91c9f7;
}

.gs-top-button {
  cursor: pointer;
  margin-right: 10px;
}

.gs-top-icon {
  height: 18px;
  width: 18px;
  display: inline-block;
  background-size: contain;
  position: relative;
}

.gs-top-button-list {
  position: absolute;
  width: max-content;
  background: white;
  color: black;
  right: 4px;
  top: 20px;
  text-align: center;
  list-style: none;
  margin: 0px;
  padding: 0px;
}

.gs-top-button-list-item {
  padding: 4px 13px;
  font-size: 14px;
}

.gs-top-button-list-item:hover {
  color: #fff;
  background-color: #7b9bf2;
}

.playerClass {
  position: absolute;
  top: 0;
  left: 0;
}

.player-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: transparent;
}

.gs-rectangle {
  display: none;
  border: 2px solid red;
  position: fixed;
  box-sizing: border-box;
}

.gs-video-lock-wrapper {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  background: rgba(184, 184, 184, 0.7);
  justify-content: center;
  align-items: center;
  overflow: auto;
}

.gs-video-lock-modal {
  width: 264px;
  background: rgba(0, 0, 0, 0.8);
  padding: 12px;
  margin: auto;
}

.gs-video-lock-title-img {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  display: inline-block;
}

.gs-video-lock-title-span {
  font-weight: bold;
  color: #ffffff;
  font-size: 14px;
  display: inline-block;
}

.gs-video-lock-content {
  height: 56px;
  margin: 12px 0 8px;
  color: white;
  font-size: 12px;
  line-height: 20px;
}

.gs-video-lock-input-info {
  color: white;
  font-size: 14px;
  margin-bottom: 8px;
}

.gs-video-lock-input-error {
  color: red;
  font-size: 12px;
}

.gs-video-lock-input {
  display: block;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  border: 1px solid #ffffff;
  margin-bottom: 12px;
  box-sizing: border-box;
  color: white;
  width: 100%;
}

.gs-video-lock-button-wrapper {
  display: flex;
  justify-content: space-between;
}

.gs-video-lock-button-wrapper > button {
  width: 114px;
  height: 32px;
  border-radius: 2px;
  outline: none;
  border: none;
  color: white;
  font-size: 14px;
  justify-content: center;
  align-items: center;
  display: inline-flex;
  cursor: pointer;
}

.gs-video-lock-confirm-button {
  background: #3d84ee;
}

.gs-video-lock-cancel-button {
  background: rgba(255, 255, 255, 0.4);
}

.debug-button {
  display: none; 
  position: absolute;
  top: 80%;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: 12px;
  padding: 2px 8px;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
  z-index: 1;
}