.gs-timeline {
  background: rgb(238, 243, 255);
  position: relative;
}

.gs-timeline-option {
  height: 40px;
  margin-bottom: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: white;
}

.gs-left-wrapper {
  position: absolute;
  left: 0;
  display: flex;
  box-sizing: content-box;
}

.gs-right-wrapper {
  position: absolute;
  right: 0;
  display: flex;
}

.gs-mid-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.gs-rate-span {
  font-size: 14px;
  font-family: 微软雅黑;
  color: #3d84ee;
  background: rgb(236, 243, 253);
  display: inline-flex;
  width: 38px;
  height: 22px;
  justify-content: center;
  align-items: center;
}

.gs-scroll-option {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-sizing: border-box;
  border: none;
  cursor: pointer;
  background-color: #fbfbfb;
  padding: 0;
}

.gs-scroll-option:disabled {
  cursor: not-allowed;
  background-color: #e5e5e5;
}

.gs-scroll-bar {
  width: 10px;
  background: #c9c9c9;
  border-radius: 6px;
  position: relative;
  top: 19px;
  margin: 0 auto;
}

.gs-scroll-div {
  position: absolute;
  right: 0;
  background: #fbfbfb;
  display: none;
  border: 1px solid #d9d9d9;
  border-right: none;
  user-select: none;
  box-sizing: border-box;
}

.gs-seek-button {
  width: 23px;
  height: 14px;
  padding: 0;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  line-height: 10px;
  box-sizing: border-box;
  background-color: white;
  background-repeat: no-repeat;
  background-position: center center;
}

.gs-seek-button:hover {
  background-color: #f2f3f4;
}

.gs-seek-span {
  color: #3d84ee;
  font-weight: bold;
  position: relative;
  top: 3px;
}

.gs-seek-span:hover {
  color: #0652c4;
}

.gs-seek-input {
  display: flex;
  width: 100px;
  height: 28px;
  border: 1px solid #d9d9d9;
  padding: 0;
  margin-right: 1px;
  border-radius: 2px;
  line-height: 25px;
  box-sizing: border-box;
}

.gs-custom-seek {
  margin: 5px 0px 0px 7px;
  font-size: 15px;
  text-align: left;
}

.gs-control-seek-hover {
  color: #3d84ee;
  background: url('../source/icon/select.png') no-repeat 145px;
}

.gs-seek-button-wrapper {
  display: flex;
  flex-direction: column;
  margin-right: 5px;
}

.gs-seek-component {
  display: flex;
  font-family: 微软雅黑;
  margin-left: 8px;
}

.gs-seek-option-item {
  list-style: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  margin: 7px 6px;
  font-size: 15px;
  background-color: white;
  background-repeat: no-repeat;
  line-height: normal;
}

.gs-seek-option {
  position: absolute;
  height: 160px;
  width: 200px;
  left: -50px;
  top: 28px;
  padding: 0 12px;
  display: none;
  background-color: white;
  z-index: 1;
  box-sizing: border-box;
}

.gs-seek-option-icon {
  margin-right: 10px;
  width: 24px;
  height: 24px;
  color: #505050;
}

.gs-seek-option-icon:hover {
  color: #3d84ee;
}

.gs-seektime-input {
  border: none;
  min-width: 0;
  text-align: center;
  padding: 0;
}

.gs-seektime-input:focus {
  outline: none;
}

.gs-line {
  border-bottom: 1px solid #eaeaea;
}

.gs-canvas {
  background-color: white;
  user-select: none;
}

.gs-scroll-icon {
  background-repeat: no-repeat;
  background-position: center center;
  height: 17px;
  width: 17px;
}

.gs-timeline-option-button {
  height: 28px;
  width: 28px;
  border: none;
  outline: none;
  background-color: white;
  cursor: pointer;
  background-repeat: no-repeat;
  margin: 0 3px;
}

.gs-timeline-option-button:disabled {
  cursor: not-allowed;
}

.gs-timeline-poptext {
  position: absolute;
  display: none;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  color: #606266;
  line-height: 1.4;
  text-align: justify;
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  word-break: break-all;
  padding: 5px;
}

.gs-timeline-label-poptext {
  transform: translateX(-50%);
  z-index: 1;
}

.poptext-arrow {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 6px;
  filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  left: 50%;
  bottom: -6px;
  margin-right: 3px;
  border-top-color: #ebeef5;
  border-bottom-width: 0;
}

.poptext-arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  content: ' ';
  border-width: 6px;
  bottom: 1px;
  margin-left: -6px;
  border-top-color: #fff;
  border-bottom-width: 0;
}

.gs-timeline-label-wrapper {
  position: absolute;
  top: 45px;
}

.gs-timeline-label {
  position: absolute;
  width: 10px;
  height: 24px;
  cursor: pointer;
}

.gs-timeline-lock-wrapper {
  position: absolute;
  top: 45px;
  pointer-events: none;
}
.gs-timeline-lock-box {
  position: absolute;
  cursor: pointer;
  pointer-events: none;
}

.gs-timeline-lock-area {
  position: absolute;
  cursor: pointer;
  opacity: 0.8;
  background-color: #2d62b2;
}

.gs-timeline-lock-icon {
  position: absolute;
  width: 10px;
  height: 24px;
  cursor: pointer;
  background: url(../source/icon/timelineLock.svg) no-repeat center center;
}
.gs-timeline-lock-info {
  width: 330px;
  height: 155px;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 30px;
  z-index: 1;
  display: none;
}
.gs-timeline-lock-info::after {
  content: '';
  position: absolute;
  background-color: white;
  width: 8px;
  height: 8px;
  left: 149px;
  transform: rotate(45deg);
  top: 150px;
}
.gs-timeline-lock-info-icon {
  float: left;
  width: 34px;
  height: 53px;
  background: url(../source/icon/darkLock.svg) no-repeat center center;
}
.gs-timeline-lock-info-title {
  height: 22px;
  font-size: 15px;
  font-weight: 600;
  color: #262626;
  line-height: 22px;
  margin-top: 4px;
}
.gs-timeline-lock-info-alltime {
  margin: 5px 7px;
}
.gs-timeline-lock-info-endtime {
  margin: 14px 7px 10px;
}
.gs-timeline-lock-info-name {
  margin: 0px 7px;
  display: inline-block;
  width: 150px;
}
.gs-timeline-lock-info-department {
  display: inline-block;
  width: 163px;
}
.gs-timeline-lock-info-remark {
  margin: 3px 0px 3px 7px;
}

.text-ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.gs-timeline-synchronous {
  width: 9px;
  height: 10px;
  position: absolute;
  top: 70px;
  left: 50%;
  transform: translateX(-4.5px);
  display: none;
}

.gs-timeline-synchronous:after {
  border: 1px solid #ff9e33;
  height: 130px;
  display: block;
  content: '';
  width: 0;
  margin: 0 auto;
  border-left: 0;
}

.gs-timeline-simple-icon {
  width: 20px;
  height: 20px;
  position: absolute;
  z-index: 1;
  background-size: contain;
  top: 2px;
  cursor: pointer;
}

.gs-timeline-select-time-ractangle {
  width: 0;
  height: 0;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(29, 76, 187, 0.5);
}

.gs-timeline-select-record {
  position: absolute;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
  background-color: rgba(29, 76, 187, 0.5);
}

.gs-timeline-select-left {
  height: 22px;
  width: 10px;
  background-size: contain;
  position: absolute;
  left: -10px;
}

.gs-timeline-select-left:hover::after {
  display: flex;
}

.gs-timeline-select-left::after {
  height: 18px;
  width: 130px;
  position: relative;
  justify-content: center;
  align-items: center;
  top: 22px;
  right: 55px;
  font-size: 12px;
  border-radius: 3px;
  content: attr(data-time);
  background-color: rgba(255, 255, 255, 1);
  z-index: 2;
}

.gs-timeline-select-right {
  height: 22px;
  width: 10px;
  background-size: contain;
  position: absolute;
  right: -10px;
}

.gs-timeline-select-right:hover::after {
  display: flex;
}

.gs-timeline-select-right::after {
  height: 18px;
  width: 130px;
  position: relative;
  justify-content: center;
  align-items: center;
  top: 22px;
  left: -65px;
  font-size: 12px;
  border-radius: 3px;
  content: attr(data-time);
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 2;
}

.gs-after-hide::after {
  display: none;
}

.gs-after-show::after {
  display: flex;
}

.gs-pointer-events-none {
  pointer-events: none;
}

.gs-select-record-option {
  position: relative;
}

.gs-select-record-option-wrapper {
  width: 62px;
  height: 30px;
  position: absolute;
  justify-content: space-around;
  align-items: center;
  top: 28px;
  left: 0px;
  background-color: white;
}

.gs-select-record-option-img {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-size: contain;
  cursor: pointer;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

input[type='number'] {
  -moz-appearance: textfield;
}

.gs-timeline-range {
  height: 17px;
  width: 25px;
  border: none;
  outline: none;
  background-color: white;
  cursor: pointer;
  background-repeat: no-repeat;
  margin: 0 3px;
  background-size: contain;
}

[type='range'] {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
  outline: 0;
  background-color: transparent;
  width: 100px;
}

/* 定义range控件轨道的样式 */
[type='range']::-webkit-slider-runnable-track {
  height: 4px;
  background: #eee;
}

/* 定义range控件容器的样式 */
[type='range' i]::-webkit-slider-container {
  height: 20px;
  overflow: hidden;
}

/* 定义range控件拇指的样式 */
[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: #409eff;
  border: 1px solid transparent;
  margin-top: -4px;
  /* 使用border-image属性给拇指添加渐变边框 */
  border-image: linear-gradient(#409eff, #409eff) 0 fill / 5 11 5 0 / 0px 0px 0
    2000px;
}

.gs-timeline-record-info {
  font-size: 12px;
  line-height: 7px;
  position: absolute;
  left: 100%;
  width: 195px;
  transform: translateY(-100%);
  background-color: white;
  box-shadow: 0 9px 28px 8px rgb(0 0 0 / 10%), 0 6px 16px 0 rgb(0 0 0 / 10%),
    0 3px 6px -4px rgb(0 0 0 / 16%);
  padding: 2px 7px 8px;
  border-radius: 5px;
}

.gs-more-button {
  position: absolute;
  box-shadow: 0 9px 28px 8px rgb(0 0 0 / 10%), 0 6px 16px 0 rgb(0 0 0 / 10%),
    0 3px 6px -4px rgb(0 0 0 / 16%);
  border-radius: 5px;
  flex-direction: column;
  background: white;
  padding: 5px 10px 5px 0;
}

.gs-more-button-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.gs-more-button-wrapper > span {
  white-space: nowrap;
  font-size: 14px;
}

.gs-sound-control-wrapper {
  position: absolute;
  box-shadow: 0 9px 28px 8px rgb(0 0 0 / 10%), 0 6px 16px 0 rgb(0 0 0 / 10%),
    0 3px 6px -4px rgb(0 0 0 / 16%);
  border-radius: 5px;
  background: white;
  height: 155px;
  width: 34px;
}

.gs-sound-control {
  margin-top: 13px;
  display: flex;
  justify-content: center;
  position: relative;
  height: 100px;
  margin-bottom: 10px;
}

.gs-sound-control-background-bar {
  background-color: #e4e7ed;
  position: absolute;
  border-radius: 3px;
  overflow: hidden;
  height: 100px;
  width: 6px;
}

.gs-sound-control-sound-bar {
  background-color: #409eff;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 50px;
}

.gs-sound-control-point {
  width: 16px;
  height: 16px;
  border: 2px solid #409eff;
  background-color: #fff;
  border-radius: 50%;
  z-index: 1;
  box-sizing: border-box;
  top: 41px;
  position: absolute;
}

.gs-sound-control-point:hover {
  width: 18px;
  height: 18px;
}

.gs-sound-control-volume {
  display: flex;
  border-radius: 3px;
  width: 34px;
  height: 34px;
  justify-content: center;
  align-items: center;
  background: rgba(48, 49, 51, 1);
  color: white;
  font-size: 14px;
}

.gs-sound-control-volume-wrapper {
  position: absolute;
}

.gs-sound-control-point:hover + .gs-sound-control-volume-wrapper {
  display: flex !important;
}

.gs-sound-control-volume-plus {
  height: 0;
  width: 0;
  top: 34px;
  position: absolute;
  box-sizing: border-box;
  left: 12px;
  border: 5px solid;
  border-color: transparent transparent transparent rgba(48, 49, 51, 1);
  transform: rotate(90deg);
}

.gs-control-text {
  width: 70px;
  margin-left: 38px;
  line-height: 28px;
  white-space: nowrap;
}

.gs-timeline-button-content {
  position: absolute;
  bottom: 34px;
  right: -64px;
  background: white;
  padding: 0 6px;
  width: 125px;
}

.gs-timeline-button-content > div {
  width: 110px;
}

.gs-timeline-button-box-icon {
  display: inline-block;
  width: 20px;
  height: 28px;
  background: url(../source/control/backSelect/normal.png) no-repeat center
    center;
  background-size: cover;
  position: relative;
  right: -20px;
  top: 5px;
}
