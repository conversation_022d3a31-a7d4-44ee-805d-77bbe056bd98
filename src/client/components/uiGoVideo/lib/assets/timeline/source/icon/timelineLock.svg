<?xml version="1.0" encoding="UTF-8"?>
<svg width="28px" height="30px" viewBox="0 0 28 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>时间轴锁备份</title>
    <defs>
        <filter x="-133.4%" y="-104.4%" width="366.8%" height="308.8%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.4 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="视频联网" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="录像回放加锁" transform="translate(-1080.000000, -944.000000)" fill="#FFFFFF">
            <g id="编组-25" transform="translate(322.000000, 875.000000)">
                <g id="编组-58备份-2" transform="translate(732.000000, 73.000000)">
                    <g id="时间轴锁备份" filter="url(#filter-1)" transform="translate(35.502798, 5.002798)">
                        <path d="M6.49720178,3 C6.49720178,1.8954305 5.60177128,1 4.49720178,1 C3.44283999,1 2.57903666,1.81587779 2.50268752,2.85073766 L2.49720178,3 L2.49720178,4.5 L1.49720178,4.5 L1.49720178,3 C1.49720178,1.34314575 2.84034753,0 4.49720178,0 C6.09488266,0 7.40086266,1.24891996 7.49210909,2.82372721 L7.49720178,3 L7.49720178,4.5 L7.99440356,4.5 C8.54668831,4.5 8.99440356,4.94771525 8.99440356,5.5 L8.99440356,10.4944036 C8.99440356,11.0466883 8.54668831,11.4944036 7.99440356,11.4944036 L1,11.4944036 C0.44771525,11.4944036 6.76353751e-17,11.0466883 0,10.4944036 L0,5.5 C-6.76353751e-17,4.94771525 0.44771525,4.5 1,4.5 L6.49720178,4.5 L6.49720178,3 Z" id="形状结合"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>