class e{constructor(e){this.name="DefaultRequest",this.ip=e.ajaxUrl||e.ip,this.token=e.token||"",this.useOneForOneCode=e.useOneForOneCode,this.errHandle=e.errHandle,this.useCMS=null!=e.useCMS&&e.useCMS,this.focusFreeWnd=void 0===e.focusFreeWnd||e.focusFreeWnd,this.isARStream=e.isARStream,this.reconnect=e.reconnect,this.focusWndByKey=e.focusWndByKey,this.getStreamUrl=e.getStreamUrl,this.getRecordUrl=e.getRecordUrl,this.getRecordStreamUrl=e.getRecordStreamUrl,this.getOpenUrl=e.getOpenUrl,this.getvidRecordLabelUrl=e.getvidRecordLabelUrl,this.userId=e.userId}}e.recordDownloadConfig={autoDownload:!1};const t=0,i=1,s=2,n=3,r=4;let o=n;class a{constructor(e=o,t=""){this.tag=t,a.level=e}static get level_map(){return{[n]:"debug",[s]:"info",[i]:"warn",[t]:"error",[r]:"log"}}_output(e,t){if(a.log&&a.level>=e){const i=Array.prototype.slice.call(t);this.tag&&i.unshift(`[${this.tag}]`),console[a.level_map[e]].apply(console,t)}}info(){a.log&&this._output(i,arguments)}debug(){a.log&&this._output(n,arguments)}error(){this._output(t,arguments)}warn(){this._output(i,arguments)}log(){a.log&&this._output(r,arguments)}}a.log=!1,a.level=n;const h=new Map;function l(e=""){return h.has(e)||h.set(e,new a(o,e)),h.get(e)}new a;var u=d,c=null;try{c=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(e){}function d(e,t,i){this.low=0|e,this.high=0|t,this.unsigned=!!i}function p(e){return!0===(e&&e.__isLong__)}d.prototype.__isLong__,Object.defineProperty(d.prototype,"__isLong__",{value:!0}),d.isLong=p;var m={},g={};function _(e,t){var i,s,n;return t?(n=0<=(e>>>=0)&&e<256)&&(s=g[e])?s:(i=S(e,(0|e)<0?-1:0,!0),n&&(g[e]=i),i):(n=-128<=(e|=0)&&e<128)&&(s=m[e])?s:(i=S(e,e<0?-1:0,!1),n&&(m[e]=i),i)}function f(e,t){if(isNaN(e))return t?b:D;if(t){if(e<0)return b;if(e>=I)return A}else{if(e<=-E)return O;if(e+1>=E)return C}return e<0?f(-e,t).neg():S(e%T|0,e/T|0,t)}function S(e,t,i){return new d(e,t,i)}d.fromInt=_,d.fromNumber=f,d.fromBits=S;var y=Math.pow;function v(e,t,i){if(0===e.length)throw Error("empty string");if("NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return D;if("number"==typeof t?(i=t,t=!1):t=!!t,(i=i||10)<2||36<i)throw RangeError("radix");var s;if((s=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===s)return v(e.substring(1),t,i).neg();for(var n=f(y(i,8)),r=D,o=0;o<e.length;o+=8){var a=Math.min(8,e.length-o),h=parseInt(e.substring(o,o+a),i);if(a<8){var l=f(y(i,a));r=r.mul(l).add(f(h))}else r=(r=r.mul(n)).add(f(h))}return r.unsigned=t,r}function w(e,t){return"number"==typeof e?f(e,t):"string"==typeof e?v(e,t):S(e.low,e.high,"boolean"==typeof t?t:e.unsigned)}d.fromString=v,d.fromValue=w;var T=4294967296,I=T*T,E=I/2,R=_(1<<24),D=_(0);d.ZERO=D;var b=_(0,!0);d.UZERO=b;var P=_(1);d.ONE=P;var B=_(1,!0);d.UONE=B;var L=_(-1);d.NEG_ONE=L;var C=S(-1,2147483647,!1);d.MAX_VALUE=C;var A=S(-1,-1,!0);d.MAX_UNSIGNED_VALUE=A;var O=S(0,-2147483648,!1);d.MIN_VALUE=O;var x,k,M,G,N,F,U,V,W,K,$,Y,H=d.prototype;H.toInt=function(){return this.unsigned?this.low>>>0:this.low},H.toNumber=function(){return this.unsigned?(this.high>>>0)*T+(this.low>>>0):this.high*T+(this.low>>>0)},H.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(O)){var t=f(e),i=this.div(t),s=i.mul(t).sub(this);return i.toString(e)+s.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var n=f(y(e,6),this.unsigned),r=this,o="";;){var a=r.div(n),h=(r.sub(a.mul(n)).toInt()>>>0).toString(e);if((r=a).isZero())return h+o;for(;h.length<6;)h="0"+h;o=""+h+o}},H.getHighBits=function(){return this.high},H.getHighBitsUnsigned=function(){return this.high>>>0},H.getLowBits=function(){return this.low},H.getLowBitsUnsigned=function(){return this.low>>>0},H.getNumBitsAbs=function(){if(this.isNegative())return this.eq(O)?64:this.neg().getNumBitsAbs();for(var e=0!=this.high?this.high:this.low,t=31;t>0&&0==(e&1<<t);t--);return 0!=this.high?t+33:t+1},H.isZero=function(){return 0===this.high&&0===this.low},H.eqz=H.isZero,H.isNegative=function(){return!this.unsigned&&this.high<0},H.isPositive=function(){return this.unsigned||this.high>=0},H.isOdd=function(){return 1==(1&this.low)},H.isEven=function(){return 0==(1&this.low)},H.equals=function(e){return p(e)||(e=w(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&(this.high===e.high&&this.low===e.low)},H.eq=H.equals,H.notEquals=function(e){return!this.eq(e)},H.neq=H.notEquals,H.ne=H.notEquals,H.lessThan=function(e){return this.comp(e)<0},H.lt=H.lessThan,H.lessThanOrEqual=function(e){return this.comp(e)<=0},H.lte=H.lessThanOrEqual,H.le=H.lessThanOrEqual,H.greaterThan=function(e){return this.comp(e)>0},H.gt=H.greaterThan,H.greaterThanOrEqual=function(e){return this.comp(e)>=0},H.gte=H.greaterThanOrEqual,H.ge=H.greaterThanOrEqual,H.compare=function(e){if(p(e)||(e=w(e)),this.eq(e))return 0;var t=this.isNegative(),i=e.isNegative();return t&&!i?-1:!t&&i?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},H.comp=H.compare,H.negate=function(){return!this.unsigned&&this.eq(O)?O:this.not().add(P)},H.neg=H.negate,H.add=function(e){p(e)||(e=w(e));var t=this.high>>>16,i=65535&this.high,s=this.low>>>16,n=65535&this.low,r=e.high>>>16,o=65535&e.high,a=e.low>>>16,h=0,l=0,u=0,c=0;return u+=(c+=n+(65535&e.low))>>>16,l+=(u+=s+a)>>>16,h+=(l+=i+o)>>>16,h+=t+r,S((u&=65535)<<16|(c&=65535),(h&=65535)<<16|(l&=65535),this.unsigned)},H.subtract=function(e){return p(e)||(e=w(e)),this.add(e.neg())},H.sub=H.subtract,H.multiply=function(e){if(this.isZero())return D;if(p(e)||(e=w(e)),c)return S(c.mul(this.low,this.high,e.low,e.high),c.get_high(),this.unsigned);if(e.isZero())return D;if(this.eq(O))return e.isOdd()?O:D;if(e.eq(O))return this.isOdd()?O:D;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(R)&&e.lt(R))return f(this.toNumber()*e.toNumber(),this.unsigned);var t=this.high>>>16,i=65535&this.high,s=this.low>>>16,n=65535&this.low,r=e.high>>>16,o=65535&e.high,a=e.low>>>16,h=65535&e.low,l=0,u=0,d=0,m=0;return d+=(m+=n*h)>>>16,u+=(d+=s*h)>>>16,d&=65535,u+=(d+=n*a)>>>16,l+=(u+=i*h)>>>16,u&=65535,l+=(u+=s*a)>>>16,u&=65535,l+=(u+=n*o)>>>16,l+=t*h+i*a+s*o+n*r,S((d&=65535)<<16|(m&=65535),(l&=65535)<<16|(u&=65535),this.unsigned)},H.mul=H.multiply,H.divide=function(e){if(p(e)||(e=w(e)),e.isZero())throw Error("division by zero");var t,i,s;if(c)return this.unsigned||-2147483648!==this.high||-1!==e.low||-1!==e.high?S((this.unsigned?c.div_u:c.div_s)(this.low,this.high,e.low,e.high),c.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?b:D;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return b;if(e.gt(this.shru(1)))return B;s=b}else{if(this.eq(O))return e.eq(P)||e.eq(L)?O:e.eq(O)?P:(t=this.shr(1).div(e).shl(1)).eq(D)?e.isNegative()?P:L:(i=this.sub(e.mul(t)),s=t.add(i.div(e)));if(e.eq(O))return this.unsigned?b:D;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();s=D}for(i=this;i.gte(e);){t=Math.max(1,Math.floor(i.toNumber()/e.toNumber()));for(var n=Math.ceil(Math.log(t)/Math.LN2),r=n<=48?1:y(2,n-48),o=f(t),a=o.mul(e);a.isNegative()||a.gt(i);)a=(o=f(t-=r,this.unsigned)).mul(e);o.isZero()&&(o=P),s=s.add(o),i=i.sub(a)}return s},H.div=H.divide,H.modulo=function(e){return p(e)||(e=w(e)),c?S((this.unsigned?c.rem_u:c.rem_s)(this.low,this.high,e.low,e.high),c.get_high(),this.unsigned):this.sub(this.div(e).mul(e))},H.mod=H.modulo,H.rem=H.modulo,H.not=function(){return S(~this.low,~this.high,this.unsigned)},H.and=function(e){return p(e)||(e=w(e)),S(this.low&e.low,this.high&e.high,this.unsigned)},H.or=function(e){return p(e)||(e=w(e)),S(this.low|e.low,this.high|e.high,this.unsigned)},H.xor=function(e){return p(e)||(e=w(e)),S(this.low^e.low,this.high^e.high,this.unsigned)},H.shiftLeft=function(e){return p(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?S(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):S(0,this.low<<e-32,this.unsigned)},H.shl=H.shiftLeft,H.shiftRight=function(e){return p(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?S(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):S(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},H.shr=H.shiftRight,H.shiftRightUnsigned=function(e){if(p(e)&&(e=e.toInt()),0===(e&=63))return this;var t=this.high;return e<32?S(this.low>>>e|t<<32-e,t>>>e,this.unsigned):S(32===e?t:t>>>e-32,0,this.unsigned)},H.shru=H.shiftRightUnsigned,H.shr_u=H.shiftRightUnsigned,H.toSigned=function(){return this.unsigned?S(this.low,this.high,!1):this},H.toUnsigned=function(){return this.unsigned?this:S(this.low,this.high,!0)},H.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},H.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&e,e>>>8&255,e>>>16&255,e>>>24]},H.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24,e>>>16&255,e>>>8&255,255&e,t>>>24,t>>>16&255,t>>>8&255,255&t]},d.fromBytes=function(e,t,i){return i?d.fromBytesLE(e,t):d.fromBytesBE(e,t)},d.fromBytesLE=function(e,t){return new d(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24,t)},d.fromBytesBE=function(e,t){return new d(e[4]<<24|e[5]<<16|e[6]<<8|e[7],e[0]<<24|e[1]<<16|e[2]<<8|e[3],t)};class q{constructor(e){this.data=e,this.bytesAvailable=this.data.byteLength,this.word=0,this.bitsAvailable=0}destroy(){this.data=null}loadWord(){var e=this.data.byteLength-this.bytesAvailable,t=new Uint8Array(4),i=Math.min(4,this.bytesAvailable);if(0===i)throw new Error("no bytes available");t.set(this.data.subarray(e,e+i)),this.word=new DataView(t.buffer,t.byteOffset,t.byteLength).getUint32(0),this.bitsAvailable=8*i,this.bytesAvailable-=i}skipBits(e){var t;this.bitsAvailable>e?(this.word<<=e,this.bitsAvailable-=e):(e-=this.bitsAvailable,e-=(t=e>>3)<<3,this.bytesAvailable-=t,this.loadWord(),this.word<<=e,this.bitsAvailable-=e)}readBits(e,t=0){let i=Math.min(this.bitsAvailable,e),s=this.word>>>32-i;return(e>32||t>6)&&console.error("***Cannot read more than 32 bits at a time"),this.bitsAvailable-=i,this.bitsAvailable>0?this.word<<=i:this.bytesAvailable>0&&this.loadWord(),i=e-i,i>0?s<<i|this.readBits(i,t+1):s}skipLZ(){var e;for(e=0;e<this.bitsAvailable;++e)if(0!=(this.word&2147483648>>>e))return this.word<<=e,this.bitsAvailable-=e,e;return this.loadWord(),e+this.skipLZ()}skipUEG(){this.skipBits(1+this.skipLZ())}skipEG(){this.skipBits(1+this.skipLZ())}readUEG(){var e=this.skipLZ();return this.readBits(e+1)-1}readEG(){var e=this.readUEG();return 1&e?1+e>>>1:-1*(e>>>1)}readBoolean(){return 1===this.readBits(1)}readUByte(){return this.readBits(8)}readUShort(){return this.readBits(16)}readUInt(){return this.readBits(32)}}class j{static getLevelString(e){return(e/10).toFixed(1)}static getProfileString(e){switch(e){case 0:return"None";case 1:return"Main";case 2:return"Main10";case 3:return"Mainstillpicture";case 4:return"Mainrext";case 5:return"Highthroughputext";default:return"Unknown"}}static _ebsp2rbsp(e){let t=e,i=t.byteLength,s=new Uint8Array(i),n=0;for(let e=0;e<i;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(s[n]=t[e],n++);return new Uint8Array(s.buffer,0,n)}static profileTierLevel(e,t){e.readBits(2),e.readBits(1),e.readBits(5),e.readBits(32),e.readBits(1),e.readBits(1),e.readBits(1),e.readBits(1),e.skipBits(40),e.readBits(3),e.readBits(1),e.readBits(8);let i,s=[],n=[];for(i=0;i<t;i++)s[i]=e.readBits(1),n[i]=e.readBits(1);if(t>0)for(i=t;i<8;i++)e.readBits(2);for(i=0;i<t;i++)s[i]&&(e.skipBits(40),e.readBits(4),e.skipBits(40),e.readBits(3),e.readBits(1)),n[i]&&e.skipBits(8)}static decimalToHex(e,t){let i=Number(e).toString(16);for(t=null==t?t=2:t;i.length<t;)i="0"+i;return i}static parseSPS(e){let t,i,s=j._ebsp2rbsp(e),n=new q(s);n.readBits(16),n.readBits(4);let r=n.readBits(3);n.readBits(1);let o=n.readBits(2),a=n.readBits(1),h=n.readBits(5),l=n.readBits(32),u=[0,0,0,0,0,0];for(t=0;t<6;t++)u[t]=n.readBits(8);let c=n.readBits(8),d=".";switch(o){case 0:d+="";break;case 1:d+="A";break;case 2:d+="B";break;case 3:d+="C"}d+=h,d+=".";let p=l,m=0;for(t=0;t<32&&(m|=1&p,31!==t);t++)m<<=1,p>>=1;d+=this.decimalToHex(m,0),d+=".",d+=0===a?"L":"H",d+=c;let g=!1,_="";for(t=5;t>=0;t--)(u[t]||g)&&(_="."+this.decimalToHex(u[t],0)+_,g=!0);d+=_;let f=[],S=[];for(t=0;t<r;t++)f[t]=n.readBits(1),S[t]=n.readBits(1);if(r>0)for(t=r;t<8;t++)n.readBits(2);for(t=0;t<r;t++)f[t]&&(n.skipBits(40),n.readBits(4),n.skipBits(40),n.readBits(3),n.readBits(1)),S[t]&&n.skipBits(8);n.readUEG();let y=n.readUEG(),v=[0,420,422,444][y];3===y&&n.readBits(1);let w=n.readUEG(),T=n.readUEG();n.readBits(1)&&(n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG());let I=n.readUEG(),E=n.readUEG(),R=n.readUEG();for(t=n.readBits(1)?0:r;t<=r;t++)n.readUEG(),n.readUEG(),n.readUEG();if(n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG(),n.readBits(1)){if(n.readBits(1)){let e,i=[];for(let s=0;s<4;s++)for(let r=0;r<6;r+=3===s?3:1)if(void 0===i[s]&&(i[s]=[]),i[s][r]=n.readUEG(),i[s][r])for(e=Math.min(64,1<<4+(s<<1)),s>1&&n.readUEG(),t=0;t<e;t++)n.readUEG();else n.readUEG()}}n.readBits(1),n.readBits(1),n.readBits(1)&&(n.readBits(4),n.readBits(4),n.readUEG(),n.readUEG(),n.readBits(1));let D,b=n.readUEG(),P=[],B=[],L=[];for(t=0;t<b;t++)if(D=0,t&&(D=n.readBits(1)),D){let e=0,s=0,r=0,o=0;if(t===b&&(e=n.readUEG()),e>t-1||e<0)throw"HEVCStream.readSPS(): st_ref_pic_set error.";let a=t-1-e,h=(1-(n.readBits(1)<<1))*(n.readUEG()+1),l=P[a]+B[a];for(i=0;i<=l;i++){let e=n.readBits(2),u=e?1:0;if(!e){u=n.readBits(1)<<1}if(1===u||2===u){let e=h;i<l&&(e+=L[a][i]),void 0===L[t]&&(L[t]=[]),L[t][s]=e,e<0?r++:o++,s++}}P[t]=r,B[t]=o}else{let e,s,r=0,o=0;for(P[t]=n.readUEG(),B[t]=n.readUEG(),L[t]=[],i=0;i<P[t];i++)e=n.readUEG(),o=r-e-1,r=o,L[t][i]=o,n.readBits(1);for(i=0;i<B[t];i++)s=n.readUEG(),o=r-s-1,r=o,L[t][i]=o,n.readBits(1)}if(n.readBits(1)){let e=n.readUEG();for(t=0;t<e;t++)n.readBits(R+4),n.readBits(1)}n.readBits(1),n.readBits(1);let C=0;if(n.readBits(1)){if(n.readBits(1)){255===n.readBits(8)&&(n.readBits(16),n.readBits(16))}if(n.readBits(1)&&n.readBits(1),n.readBits(1)){n.readBits(4),n.readBits(1)&&n.skipBits(24)}if(n.readBits(1)&&(n.readUEG(),n.readUEG()),n.readBits(3),n.readBits(1)&&(n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG()),n.readBits(1)){if(n.readBits(32),n.readBits(32),n.readBits(1)&&n.readUEG(),n.readBits(1)){let e,s,o,a,h,l=n.readBits(1),u=n.readBits(1);for((l||u)&&(e=n.readBits(1),e&&n.readBits(19),n.skipBits(8),e&&n.readBits(4),n.readBits(15)),t=0;t<=r;t++){if(o=1,a=0,h=0,s=n.readBits(1),s||(o=n.readBits(1)),o?n.readUEG():a=n.readBits(1),a||(h=n.readUEG()),l)for(i=0;i<=h;i++)n.readUEG(),n.readUEG(),e&&(n.readUEG(),n.readUEG()),n.readBits(1);if(u)for(i=0;i<=h;i++)n.readUEG(),n.readUEG(),e&&(n.readUEG(),n.readUEG()),n.readBits(1)}}}n.readBits(1)&&(n.readBits(3),C=n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG())}let A=0;n.readBits(1)&&(A=n.readBits(1),n.readBits(1),n.readBits(6)),A&&(n.readBits(1),n.readBits(1),n.readBits(1),n.readBits(1),n.readBits(1),n.readBits(1),n.readBits(1),n.readBits(1),n.readBits(1));let O=j.getProfileString(h),x=j.getLevelString(c),k=I+8;return n.destroy(),n=null,{profile_string:O,level_string:x,bit_depth:k,ref_frames:1,chroma_format:v,mimeCodec:"hvc1"+d,profileIdc:h,levelIdc:c,profileCompatibility:l,width:w,height:T,general_constraint_indicator:u,min_spatial_segmentation_idc:C,parallelismType:0,chroma_format_idc:y,bit_depth_luma_minus8:I,bit_depth_chroma_minus8:E,avgFrameRate:0,lengthSizeMinusOne:3}}static parseMimeCode(e){let t,i=j._ebsp2rbsp(e),s=new q(i);s.readBits(16),s.readBits(4),s.readBits(3),s.readBits(1);let n=s.readBits(2),r=s.readBits(1),o=s.readBits(5),a=s.readBits(32),h=[0,0,0,0,0,0];for(t=0;t<6;t++)h[t]=s.readBits(8);let l=s.readBits(8),u=".";switch(n){case 0:u+="";break;case 1:u+="A";break;case 2:u+="B";break;case 3:u+="C"}u+=o,u+=".";let c=a,d=0;for(t=0;t<32&&(d|=1&c,31!==t);t++)d<<=1,c>>=1;u+=this.decimalToHex(d,0),u+=".",u+=0===r?"L":"H",u+=l;let p=!1,m="";for(t=5;t>=0;t--)(h[t]||p)&&(m="."+this.decimalToHex(h[t],0)+m,p=!0);return u+=m,s.destroy(),s=null,u}}class X{static _ebsp2rbsp(e){let t=e,i=t.byteLength,s=new Uint8Array(i),n=0;for(let e=0;e<i;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(s[n]=t[e],n++);return new Uint8Array(s.buffer,0,n)}static parseSPS(e){let t=e.subarray(1,4),i="avc1.";for(let e=0;e<3;e++){let s=t[e].toString(16);s.length<2&&(s="0"+s),i+=s}let s=X._ebsp2rbsp(e),n=new q(s);n.readUByte();let r=n.readUByte();n.readUByte();let o=n.readUByte();n.readUEG();let a=X.getProfileString(r),h=X.getLevelString(o),l=1,u=420,c=[0,420,422,444],d=8;if((100===r||110===r||122===r||244===r||44===r||83===r||86===r||118===r||128===r||138===r||144===r)&&(l=n.readUEG(),3===l&&n.readBits(1),l<=3&&(u=c[l]),d=n.readUEG()+8,n.readUEG(),n.readBits(1),n.readBool())){let e=3!==l?8:12;for(let t=0;t<e;t++)n.readBool()&&(t<6?X._skipScalingList(n,16):X._skipScalingList(n,64))}n.readUEG();let p=n.readUEG();if(0===p)n.readUEG();else if(1===p){n.readBits(1),n.readUEG(),n.readUEG();let e=n.readUEG();for(let t=0;t<e;t++)n.readUEG()}let m=n.readUEG();n.readBits(1);let g=n.readUEG(),_=n.readUEG(),f=n.readBits(1);0===f&&n.readBits(1),n.readBits(1);let S=0,y=0,v=0,w=0;if(n.readBoolean()&&(S=n.readUEG(),y=n.readUEG(),v=n.readUEG(),w=n.readUEG()),n.readBoolean()){if(n.readBoolean()){let e=n.readUByte();e>0&&e<16||255===e&&(n.readUByte(),n.readUByte(),n.readUByte(),n.readUByte())}n.readBoolean()&&n.readBoolean(),n.readBoolean()&&(n.readBits(4),n.readBoolean()&&n.readBits(24)),n.readBoolean()&&(n.readUEG(),n.readUEG()),n.readBoolean()&&(n.readBits(32),n.readBits(32),n.readBoolean())}let T=0,I=0;if(0===l)T=1,I=2-f;else{T=3===l?1:2,I=(1===l?2:1)*(2-f)}let E=16*(g+1),R=16*(_+1)*(2-f);return E-=(S+y)*T,R-=(v+w)*I,n.destroy(),n=null,{profile_string:a,level_string:h,bit_depth:d,ref_frames:m,chroma_format:u,chroma_format_string:X.getChromaFormatString(u),mimeCodec:i,width:E,height:R}}static parseMimeCode(e){let t=e.subarray(1,4),i="avc1.";for(let e=0;e<3;e++){let s=t[e].toString(16);s.length<2&&(s="0"+s),i+=s}return i}static _skipScalingList(e,t){let i=8,s=8,n=0;for(let r=0;r<t;r++)0!==s&&(n=e.readUEG(),s=(i+n+256)%256),i=0===s?i:s}static getProfileString(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}}static getLevelString(e){return(e/10).toFixed(1)}static getChromaFormatString(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}}}class z{static init(){var e;for(e in z.types={hvc1:[],hvcC:[],avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]},z.types)z.types.hasOwnProperty(e)&&(z.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);var t=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),i=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);z.HDLR_TYPES={video:t,audio:i};var s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),n=new Uint8Array([0,0,0,0,0,0,0,0]);z.STTS=z.STSC=z.STCO=n,z.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),z.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),z.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),z.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);var r=new Uint8Array([105,115,111,109]),o=new Uint8Array([97,118,99,49]),a=new Uint8Array([0,0,0,1]);z.FTYP=z.box(z.types.ftyp,r,a,r,o),z.DINF=z.box(z.types.dinf,z.box(z.types.dref,s))}static box(e,...t){for(var i,s=8,n=t.length,r=n;n--;)s+=t[n].byteLength;for((i=new Uint8Array(s))[0]=s>>24&255,i[1]=s>>16&255,i[2]=s>>8&255,i[3]=255&s,i.set(e,4),n=0,s=8;n<r;++n)i.set(t[n],s),s+=t[n].byteLength;return i}static hdlr(e){return z.box(z.types.hdlr,z.HDLR_TYPES[e])}static mdat(e){return z.box(z.types.mdat,e)}static mdhd(e,t){return 0,z.box(z.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,2,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,255&e,0,0,0,0,85,196,0,0]))}static mdia(e){return z.box(z.types.mdia,z.mdhd(e.timescale,e.duration),z.hdlr(e.type),z.minf(e))}static mfhd(e){return z.box(z.types.mfhd,new Uint8Array([0,0,0,0,e>>24,e>>16&255,e>>8&255,255&e]))}static minf(e){return"audio"===e.type?z.box(z.types.minf,z.box(z.types.smhd,z.SMHD),z.DINF,z.stbl(e)):z.box(z.types.minf,z.box(z.types.vmhd,z.VMHD),z.DINF,z.stbl(e))}static moof(e,t,i){return z.box(z.types.moof,z.mfhd(e),z.traf(i,t))}static moov(e,t,i){for(var s=e.length,n=[];s--;)n[s]=z.trak(e[s]);return z.box.apply(null,[z.types.moov,z.mvhd(i,t)].concat(n).concat(z.mvex(e)))}static mvex(e){for(var t=e.length,i=[];t--;)i[t]=z.trex(e[t]);return z.box.apply(null,[z.types.mvex].concat(i))}static mvhd(e,t){var i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,2,e>>24&255,e>>16&255,e>>8&255,255&e,t>>24&255,t>>16&255,t>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return z.box(z.types.mvhd,i)}static sdtp(e){var t,i,s=e.samples||[],n=new Uint8Array(4+s.length);for(i=0;i<s.length;i++)t=s[i].flags,n[i+4]=t.dependsOn<<4|t.isDependedOn<<2|t.hasRedundancy;return z.box(z.types.sdtp,n)}static stbl(e){return z.box(z.types.stbl,z.stsd(e),z.box(z.types.stts,z.STTS),z.box(z.types.stsc,z.STSC),z.box(z.types.stsz,z.STSZ),z.box(z.types.stco,z.STCO))}static avc1(e){var t,i,s,n=[],r=[];for(t=0;t<e.sps.length;t++)s=(i=e.sps[t]).byteLength,n.push(s>>>8&255),n.push(255&s),n=n.concat(Array.prototype.slice.call(i));for(t=0;t<e.pps.length;t++)s=(i=e.pps[t]).byteLength,r.push(s>>>8&255),r.push(255&s),r=r.concat(Array.prototype.slice.call(i));var o=z.box(z.types.avcC,new Uint8Array([1,n[3],n[4],n[5],255,224|e.sps.length].concat(n).concat([e.pps.length]).concat(r))),a=e.width,h=e.height;return z.box(z.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,a>>8&255,255&a,h>>8&255,255&h,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,98,105,110,101,108,112,114,111,46,114,117,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),o,z.box(z.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])))}static esds(e){var t=e.config.byteLength;let i=new Uint8Array(26+t+3);return i.set([0,0,0,0,3,23+t,0,1,0,4,15+t,64,21,0,0,0,0,0,0,0,0,0,0,0,5,t]),i.set(e.config,26),i.set([6,1,2],26+t),i}static mp4a(e){var t=e.audiosamplerate;return z.box(z.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount,0,16,0,0,0,0,t>>8&255,255&t,0,0]),z.box(z.types.esds,z.esds(e)))}static stsd(e){return"audio"===e.type?z.box(z.types.stsd,z.STSD,z.mp4a(e)):17==e.ecodeId?z.box(z.types.stsd,z.STSD,z.avc1(e)):z.box(z.types.stsd,z.STSD,z.hvc1(e))}static tkhd(e){var t=e.id,i=e.duration,s=e.width,n=e.height,r=e.volume;return z.box(z.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>24&255,t>>16&255,t>>8&255,255&t,0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i,0,0,0,0,0,0,0,0,0,0,0,0,r>>0&255,r%1*10>>0&255,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,s>>8&255,255&s,0,0,n>>8&255,255&n,0,0]))}static traf(e,t){var i=z.sdtp(e),s=e.id;return z.box(z.types.traf,z.box(z.types.tfhd,new Uint8Array([0,0,0,0,s>>24,s>>16&255,s>>8&255,255&s])),z.box(z.types.tfdt,new Uint8Array([1,0,0,0,t.high>>24,t.high>>16&255,t.high>>8&255,255&t.high,t.low>>24,t.low>>16&255,t.low>>8&255,255&t.low])),z.trun(e,i.length+16+20+8+16+8+8),i)}static trak(e){return e.duration=e.duration||4294967295,z.box(z.types.trak,z.tkhd(e),z.mdia(e))}static trex(e){var t=e.id;return z.box(z.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))}static trun(e,t){var i,s,n,r,o,a,h=e.samples||[],l=h.length,u=12+16*l,c=new Uint8Array(u);for(t+=8+u,c.set([0,0,15,1,l>>>24&255,l>>>16&255,l>>>8&255,255&l,t>>>24&255,t>>>16&255,t>>>8&255,255&t],0),i=0;i<l;i++)n=(s=h[i]).duration,r=s.size,o=s.flags,a=s.cts,c.set([n>>>24&255,n>>>16&255,n>>>8&255,255&n,r>>>24&255,r>>>16&255,r>>>8&255,255&r,o.isLeading<<2|o.dependsOn,o.isDependedOn<<6|o.hasRedundancy<<4|o.paddingValue<<1|o.isNonSync,61440&o.degradPrio,15&o.degradPrio,a>>>24&255,a>>>16&255,a>>>8&255,255&a],12+16*i);return z.box(z.types.trun,c)}static initSegment(e,t,i){z.types||z.init();var s,n=z.moov(e,t,i);return(s=new Uint8Array(z.FTYP.byteLength+n.byteLength)).set(z.FTYP),s.set(n,z.FTYP.byteLength),s}static hvc1(e){let t,i,s,n=[],r=[],o=[];for(t=0;t<e.sps.length;t++)i=e.sps[t],s=i.byteLength,n.push(s>>>8&255),n.push(255&s),n=n.concat(Array.prototype.slice.call(i));for(t=0;t<e.pps.length;t++)i=e.pps[t],s=i.byteLength,r.push(s>>>8&255),r.push(255&s),r=r.concat(Array.prototype.slice.call(i));for(t=0;t<e.vps.length;t++)i=e.vps[t],s=i.byteLength,o.push(s>>>8&255),o.push(255&s),o=o.concat(Array.prototype.slice.call(i));let a=j.parseSPS(e.sps[0]),h=new Uint8Array([1,1,a.profileCompatibility>>24&255,a.profileCompatibility>>16&255,a.profileCompatibility>>8&255,255&a.profileCompatibility,a.general_constraint_indicator[0],a.general_constraint_indicator[1],a.general_constraint_indicator[2],a.general_constraint_indicator[3],a.general_constraint_indicator[4],a.general_constraint_indicator[5],a.levelIdc,a.min_spatial_segmentation_idc>>8&255,255&a.min_spatial_segmentation_idc,a.parallelismType,a.chroma_format_idc,a.bit_depth_luma_minus8,a.bit_depth_chroma_minus8,0,0,15,3].concat(160,0,1,o,161,0,1,n,162,0,1,r));const l=z.box(z.types.hvcC,h),u=a.width,c=a.height;return z.box(z.types.hvc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,u>>8&255,255&u,c>>8&255,255&c,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),l,z.box(z.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])))}static parseMimeCode(e,t){return"h265"==t?j.parseMimeCode(e):"h264"==t?X.parseMimeCode(e):void 0}}!function(e){e.CONNECT_ABORT="WebSocketConnectAbort",e.TEST_EVENT="test_event",e.RTSP_TIMEOUT="rtspTimeout",e.RECONNECT="reconnect"}(x||(x={}));class Z{constructor(){this.eventMap=new Map}listen(e,t){if(this.eventMap){let i=this.eventMap.get(e);i?i.push(t):this.eventMap.set(e,[t])}}addEventListener(e,t){this.listen(e,t)}removeEventListener(e,t){if(this.eventMap&&this.eventMap.has(e)){let t=this.eventMap.get(e);t&&(t.length>1||this.eventMap.delete(e))}}clearEventListener(){this.eventMap&&this.eventMap.clear(),this.eventMap=null}trigger(e,...t){if(this.eventMap){let i=this.eventMap.get(e);if(i)for(let e of i)e(...t)}}emit(e,...t){this.trigger(e,...t)}}!function(e){e[e.GS_AV_TYPE_NONE=0]="GS_AV_TYPE_NONE",e[e.GS_AV_TYPE_VIDEO=1]="GS_AV_TYPE_VIDEO",e[e.GS_AV_TYPE_AUDIO=2]="GS_AV_TYPE_AUDIO",e[e.GS_AV_TYPE_COMPLEX=15]="GS_AV_TYPE_COMPLEX",e[e.GS_AV_TYPE_DATA=255]="GS_AV_TYPE_DATA",e[e.GS_AV_TYPE_NUMS=256]="GS_AV_TYPE_NUMS"}(k||(k={})),function(e){e[e.GS_AV_FRAME_TYPE_NONE=0]="GS_AV_FRAME_TYPE_NONE",e[e.GS_AV_FRAME_VIDEO_I=1]="GS_AV_FRAME_VIDEO_I",e[e.GS_AV_FRAME_VIDEO_P=2]="GS_AV_FRAME_VIDEO_P",e[e.GS_AV_FRAME_VIDEO_B=3]="GS_AV_FRAME_VIDEO_B",e[e.GS_AV_FRAME_VIDEO_CONFIG=4]="GS_AV_FRAME_VIDEO_CONFIG",e[e.GS_AV_FRAME_VIDEO_AUD=5]="GS_AV_FRAME_VIDEO_AUD",e[e.GS_AV_FRAME_VIDEO_SPS=6]="GS_AV_FRAME_VIDEO_SPS",e[e.GS_AV_FRAME_VIDEO_PPS=7]="GS_AV_FRAME_VIDEO_PPS",e[e.GS_AV_FRAME_VIDEO_VPS=8]="GS_AV_FRAME_VIDEO_VPS",e[e.GS_AV_FRAME_VIDEO_SEI=9]="GS_AV_FRAME_VIDEO_SEI",e[e.GS_AV_FRAME_AUDIO=10]="GS_AV_FRAME_AUDIO",e[e.GS_AV_FRAME_AUDIO_CONFIG=11]="GS_AV_FRAME_AUDIO_CONFIG",e[e.GS_AV_FRAME_SYSTEM_HEADER=12]="GS_AV_FRAME_SYSTEM_HEADER",e[e.GS_AV_FRAME_PRIVATE=13]="GS_AV_FRAME_PRIVATE",e[e.GS_AV_FRAME_NUMS=14]="GS_AV_FRAME_NUMS"}(M||(M={})),function(e){e[e.GS_CODEID_NONE=0]="GS_CODEID_NONE",e[e.GS_CODEID_ST_MP4=16]="GS_CODEID_ST_MP4",e[e.GS_CODEID_ST_H264=17]="GS_CODEID_ST_H264",e[e.GS_CODEID_ST_SVAC=18]="GS_CODEID_ST_SVAC",e[e.GS_CODEID_ST_H265=19]="GS_CODEID_ST_H265",e[e.GS_CODEID_ST_H263=20]="GS_CODEID_ST_H263",e[e.GS_CODEID_ST_MPEG2=21]="GS_CODEID_ST_MPEG2",e[e.GS_CODEID_ST_MPEG1=22]="GS_CODEID_ST_MPEG1",e[e.GS_CODEID_ST_MJPG=23]="GS_CODEID_ST_MJPG",e[e.GS_CODEID_FCC_MPEG4=24]="GS_CODEID_FCC_MPEG4",e[e.GS_CODEID_MS_MPEG4=25]="GS_CODEID_MS_MPEG4",e[e.GS_CODEID_ST_FLV1=26]="GS_CODEID_ST_FLV1",e[e.GS_CODEID_ST_WMV1=27]="GS_CODEID_ST_WMV1",e[e.GS_CODEID_ST_WMV2=28]="GS_CODEID_ST_WMV2",e[e.GS_CODEID_ST_WMV3=29]="GS_CODEID_ST_WMV3",e[e.GS_CODEID_AUDIO_ST_MP3=768]="GS_CODEID_AUDIO_ST_MP3",e[e.GS_CODEID_AUDIO_ST_PCM=769]="GS_CODEID_AUDIO_ST_PCM",e[e.GS_CODEID_AUDIO_ST_G711A=770]="GS_CODEID_AUDIO_ST_G711A",e[e.GS_CODEID_AUDIO_ST_G711U=771]="GS_CODEID_AUDIO_ST_G711U",e[e.GS_CODEID_AUDIO_ST_G721=772]="GS_CODEID_AUDIO_ST_G721",e[e.GS_CODEID_AUDIO_ST_G722=773]="GS_CODEID_AUDIO_ST_G722",e[e.GS_CODEID_AUDIO_ST_G723=774]="GS_CODEID_AUDIO_ST_G723",e[e.GS_CODEID_AUDIO_ST_G726=775]="GS_CODEID_AUDIO_ST_G726",e[e.GS_CODEID_AUDIO_ST_G729=776]="GS_CODEID_AUDIO_ST_G729",e[e.GS_CODEID_AUDIO_ST_H263=777]="GS_CODEID_AUDIO_ST_H263",e[e.GS_CODEID_AUDIO_ST_SVAC=778]="GS_CODEID_AUDIO_ST_SVAC",e[e.GS_CODEID_AUDIO_ST_AAC=779]="GS_CODEID_AUDIO_ST_AAC",e[e.GS_CODEID_AUDIO_ST_G726_16=780]="GS_CODEID_AUDIO_ST_G726_16",e[e.GS_CODEID_AUDIO_ST_G726_24=781]="GS_CODEID_AUDIO_ST_G726_24",e[e.GS_CODEID_AUDIO_ST_G726_32=782]="GS_CODEID_AUDIO_ST_G726_32",e[e.GS_CODEID_AUDIO_ST_G726_40=783]="GS_CODEID_AUDIO_ST_G726_40",e[e.GS_CODEID_AUDIO_ST_OGGVORBIS=784]="GS_CODEID_AUDIO_ST_OGGVORBIS",e[e.GS_CODEID_AUDIO_ST_AMR=785]="GS_CODEID_AUDIO_ST_AMR",e[e.GS_CODEID_AUDIO_ST_WMA1=786]="GS_CODEID_AUDIO_ST_WMA1",e[e.GS_CODEID_AUDIO_ST_WMA2=787]="GS_CODEID_AUDIO_ST_WMA2",e[e.GS_CODEID_AUDIO_ST_G722_1=788]="GS_CODEID_AUDIO_ST_G722_1",e[e.GS_CODEID_AUDIO_ST_OPUS=789]="GS_CODEID_AUDIO_ST_OPUS",e[e.GS_CODEID_NONE_NUMS=790]="GS_CODEID_NONE_NUMS"}(G||(G={})),function(e){e[e.GS_AV_SAMPLE_FMT_NONE=-1]="GS_AV_SAMPLE_FMT_NONE",e[e.GS_AV_SAMPLE_FMT_U8=0]="GS_AV_SAMPLE_FMT_U8",e[e.GS_AV_SAMPLE_FMT_S20=1]="GS_AV_SAMPLE_FMT_S20",e[e.GS_AV_SAMPLE_FMT_S16=2]="GS_AV_SAMPLE_FMT_S16",e[e.GS_AV_SAMPLE_FMT_S32=3]="GS_AV_SAMPLE_FMT_S32",e[e.GS_AV_SAMPLE_FMT_FLT=4]="GS_AV_SAMPLE_FMT_FLT",e[e.GS_AV_SAMPLE_FMT_DBL=5]="GS_AV_SAMPLE_FMT_DBL",e[e.GS_AV_SAMPLE_FMT_U8P=6]="GS_AV_SAMPLE_FMT_U8P",e[e.GS_AV_SAMPLE_FMT_S16P=7]="GS_AV_SAMPLE_FMT_S16P",e[e.GS_AV_SAMPLE_FMT_S32P=8]="GS_AV_SAMPLE_FMT_S32P",e[e.GS_AV_SAMPLE_FMT_FLTP=9]="GS_AV_SAMPLE_FMT_FLTP",e[e.GS_AV_SAMPLE_FMT_DBLP=10]="GS_AV_SAMPLE_FMT_DBLP",e[e.GS_AV_SAMPLE_FMT_S64=11]="GS_AV_SAMPLE_FMT_S64",e[e.GS_AV_SAMPLE_FMT_S64P=12]="GS_AV_SAMPLE_FMT_S64P",e[e.GS_AV_SAMPLE_FMT_NUMs=13]="GS_AV_SAMPLE_FMT_NUMs"}(N||(N={})),function(e){e[e.GS_AV_PACKET_TYPE_NONE=0]="GS_AV_PACKET_TYPE_NONE",e[e.GS_AV_PACKET_TYPE_RAW=1]="GS_AV_PACKET_TYPE_RAW",e[e.GS_AV_PACKET_TYPE_ES=2]="GS_AV_PACKET_TYPE_ES",e[e.GS_AV_PACKET_TYPE_PS=4]="GS_AV_PACKET_TYPE_PS",e[e.GS_AV_PACKET_TYPE_TS=8]="GS_AV_PACKET_TYPE_TS",e[e.GS_AV_PACKET_TYPE_FLV=16]="GS_AV_PACKET_TYPE_FLV",e[e.GS_AV_PACKET_TYPE_MP4=32]="GS_AV_PACKET_TYPE_MP4",e[e.GS_AV_PACKET_TYPE_MKV=64]="GS_AV_PACKET_TYPE_MKV",e[e.GS_AV_PACKET_TYPE_RTP=256]="GS_AV_PACKET_TYPE_RTP"}(F||(F={})),function(e){e[e.GSMD_FLAG_ISKEY=1]="GSMD_FLAG_ISKEY",e[e.GSMD_FLAG_IS_PLAY_START=4]="GSMD_FLAG_IS_PLAY_START",e[e.GSMD_FLAG_IS_PLAY_END=8]="GSMD_FLAG_IS_PLAY_END"}(U||(U={}));class J{constructor(){this.eMediaType=k.GS_AV_TYPE_NONE,this.eCodeID=G.GS_CODEID_NONE,this.ePacketType=F.GS_AV_PACKET_TYPE_NONE,this.sinkPipeName="",this.nTrackIndex=0,this.strTrackName="0",this.strTrackSourcePath="/",this.isEnable=!0,this.nTimesacle=0,this.aConfigBuffer=new Uint8Array(0),this.mParams=new Map,this.nWidth=0,this.nHeight=0,this.nSamplerate=0,this.nSampleBits=0,this.nChannels=0}}class Q extends J{constructor(){super(),this.nWidth=0,this.nHeight=0,this.nFramerate=0,this.nBitrate=0,this.eMediaType=k.GS_AV_TYPE_VIDEO}}class ee extends J{constructor(){super(),this.nSamplerate=0,this.nSampleBits=0,this.nChannels=0,this.eSampleFormat=N.GS_AV_SAMPLE_FMT_NONE,this.eMediaType=k.GS_AV_TYPE_AUDIO}}class te extends Q{constructor(){super(),this.eCodeID=G.GS_CODEID_ST_H264}}class ie extends Q{constructor(){super(),this.eCodeID=G.GS_CODEID_ST_H265}}class se extends ee{constructor(){super(),this.eCodeID=G.GS_CODEID_AUDIO_ST_AAC}}class ne{constructor(){this._trackInfos=new Array(0),this.trackInfos=new Array(0),this._activeTrackFlag=4294967295}getTotalDuration(){return 0}isCompleted(){return!0}getTrackInfo(){return 0==this._trackInfos.length||this._activeTrackFlag,null}}class re{constructor(){this.nFlag=0,this.nSeq=0,this.nPTS=new u(0),this.nDTS=new u(0),this.nSTS=new u(0),this.nDuration=0,this.nTimestamp=0,this.eFrameType=M.GS_AV_FRAME_TYPE_NONE}isKey(){return 0!=(this.nFlag&U.GSMD_FLAG_ISKEY)}isStart(){return 0!=(this.nFlag&U.GSMD_FLAG_IS_PLAY_START)}isEnd(){return 0!=(this.nFlag&U.GSMD_FLAG_IS_PLAY_END)}setKeyFlag(){this.nFlag|=U.GSMD_FLAG_ISKEY}setStartFlag(){this.nFlag|=U.GSMD_FLAG_IS_PLAY_START}setEndFlag(){this.nFlag|=U.GSMD_FLAG_IS_PLAY_END}}class oe extends re{constructor(){super(...arguments),this.eFrameType=M.GS_AV_FRAME_TYPE_NONE,this.nBlockId=0,this.nBlockMaxNum=0}}class ae extends re{}!function(e){e.NONE="NONE",e.READY="READY",e.PLAY="PLAY",e.PAUSE="PAUSE",e.CLOSE="CLOSE"}(V||(V={})),function(e){e.CURRENT_TIME="current_time",e.RANGE_START="range_start",e.RANGE_END="range_end",e.RATE="speed"}(W||(W={}));class he{constructor(e,t){this.key="",this.value=null,this.key=e,this.value=t}}!function(e){e[e.OK=0]="OK",e[e.CONTINUE=1]="CONTINUE",e[e.ERROR=2]="ERROR",e[e.FINISH=3]="FINISH"}(K||(K={})),function(e){e[e.OK=0]="OK",e[e.FULL=1]="FULL",e[e.OVERFLOW=2]="OVERFLOW",e[e.ERROR=3]="ERROR"}($||($={}));class le{constructor(e,t,i){this._OnMediaInfoChanged=null,this._outBinders=new Array,this._inBinders=new Array,this._currentStatus=V.NONE,this._targetStatus=V.NONE,this._rate=1,this._currentTime=0,this._eventBus=new Z,this._pipeName=e,this._eventBus=t,this.session=i}get pipeName(){return this._pipeName}get currentStatus(){return this._currentStatus}get targetStatus(){return this._targetStatus}get isStatusChanging(){return this._currentStatus!=this._targetStatus}get outBinders(){return this._outBinders}get inBinders(){return this._inBinders}async onStatusChangeRequest(e){if(e==V.READY){let e=[];for(let t of this.inBinders)e.push(t.sdpInfo);await this.updateOutputSdpInfo(e)}return K.OK}onIncomingPipeRequest(e,t){return K.OK}async onIncomingDataFromPrev(e,t){return $.OK}onAttributeChanged(e){return K.OK}async _setStatus(e){if(this.isStatusChanging)return this._targetStatus=e,K.CONTINUE;this._targetStatus=e,this.log(`==> begin change ${this.pipeName} Status to ${e}`);let t=await this.onStatusChangeRequest(e);return this.log(`==> end change ${this.pipeName} Status to ${e},ret = ${t}`),t==K.OK?this._currentStatus=e:this._targetStatus=this._currentStatus,t}_setAttribute(e){return this.onAttributeChanged(e)}_bindIncomingPipe(e){let t=this.onIncomingPipeRequest(e.sdpInfo,e.sourcePipe);return t==K.OK&&this._inBinders.push(e),t}async updateOutputSdpInfo(e){if(0!=this._outBinders.length)return K.ERROR;for(let t of e){let e=new ce(this,t);this._outBinders.push(e)}return this._OnMediaInfoChanged&&await this._OnMediaInfoChanged(this),K.OK}async sendDataToNextPipe(e,t,i){for(let s of this._outBinders)if(s==t&&null!=s.sdpInfo&&s.sinkPipe){s.sinkPipe.currentStatus!=V.PLAY&&s.sinkPipe.currentStatus!=V.PAUSE&&s.sinkPipe.currentStatus!=V.READY&&console.warn(`SendData To ${s.sinkPipe.pipeName} error: status is ${s.sinkPipe.currentStatus}`),i&&(s.sdpInfo.nHeight=i.nHeight,s.sdpInfo.nWidth=i.nWidth,s.sdpInfo.aConfigBuffer=i.aConfigBuffer);let t=await s.sinkPipe.onIncomingDataFromPrev(s.sdpInfo,e);return t!=$.OK&&console.debug("["+this._pipeName+"] send data to ["+s.sinkPipe._pipeName+"], ret: "+t+"; date: "+(new Date).toString()),t}return $.ERROR}reset(){this._outBinders=[],this._inBinders=[]}onReset(){this.reset()}log(e){var t;null===(t=this._eventBus)||void 0===t||t.trigger("log",e)}}class ue extends le{constructor(e,t,i,s){super(e,i,s),this._headPipes=new Array,this._tailPipes=new Array,this._headPipes=t.slice(),this._tailPipes=t.slice();for(let e of this._headPipes)this._waitSDPToBindNextPipe(e)}_clearPipe(e){if(e)if(e.outBinders.length<=0)e.onReset();else{for(let t=0;t<e.outBinders.length;++t)e.outBinders[t]&&e.outBinders[t].sinkPipe&&this._clearPipe(e.outBinders[t].sinkPipe);e.onReset()}}reset(){super.reset();for(let e=0;e<this._headPipes.length;++e)this._clearPipe(this._headPipes[e]);this._headPipes=[],this._tailPipes=[],console.debug("Clear GSWebPlayerPipeline compeleted.")}async setStatus(e){let t=K.OK;console.debug("SetStatus Pipeline[%s] to %s",this.pipeName,e),this._targetStatus=e;for(let i of this._headPipes)if(t=await this._setPipeStatus(i,e),t!=K.OK)return t;return this._currentStatus=this._targetStatus,console.debug("SetStatus Pipeline[%s] to %s compeleted",this.pipeName,e),K.OK}async setAttribute(e){for(let t of this._tailPipes)await this._setPipeAttribute(t,e);return K.OK}async _setPipeAttribute(e,t){let i=e._setAttribute(t);if(i==K.OK){let i=this._getPrevPipes(e);for(let e of i)await this._setPipeAttribute(e,t),console.debug("SetAttribte Pipe[%s].%s to %s completed",e.pipeName,t.key,t.value)}else console.error("SetAttribute Pipe[%s.%s] Fail:%d",this.pipeName,e.pipeName,i);return K.OK}async _setPipeStatus(e,t){var i,s;let n=t;for(;;){if(e.currentStatus==n)break;this.log(`==> Pipe[${e.pipeName}] ${e.currentStatus} -> ${n}`);let t=this._getNextStatus(e.currentStatus,n);if(t==V.NONE)break;console.debug(`==>Seting Pipe[${e.pipeName}].status = ${t}`);let r=await e._setStatus(t);if(this.log(`==> Pipe[${e.pipeName}] -> ${n} ${r}`),ue.disabledH265&&"H265Analyzer"===e.pipeName&&n===V.READY){r=K.ERROR;let s=setTimeout((()=>{var e;null===(e=this._eventBus)||void 0===e||e.trigger("close"),clearTimeout(s)}),1e3);return null===(i=this._eventBus)||void 0===i||i.trigger("error",1),console.error(`   !!Set Pipe[${e.pipeName}].status = ${t} Fail:${r}`),r}if(r!=K.OK){if(r==K.CONTINUE)break;if("H265Analyzer"===e.pipeName&&n===V.READY){let e=setTimeout((()=>{var t;null===(t=this._eventBus)||void 0===t||t.trigger("close"),clearTimeout(e)}),1e3);null===(s=this._eventBus)||void 0===s||s.trigger("error",1)}return console.error(`   !!Set Pipe[${e.pipeName}].status = ${t} Fail:${r}`),r}{console.debug(`    --\x3ePipe[${e.pipeName}].status is ${t}`);let i=this._getNextPipes(e);for(let e of i)r=await this._setPipeStatus(e,t),r==K.OK&&(console.debug("SetStatus Pipe[%s.%s] to %s completed",this.pipeName,e.pipeName,t),this._printCurPipelineStatus())}}return K.OK}async _handleNewSDP(e){let t=this._bindNextPipe(e);for(let i of t)i.sinkPipe&&await this._setPipeStatus(i.sinkPipe,e.targetStatus)}_waitSDPToBindNextPipe(e){e._OnMediaInfoChanged=async()=>{await this._handleNewSDP(e)}}_getNextStatus(e,t){switch(e){case V.NONE:switch(t){case V.READY:case V.PLAY:case V.PAUSE:return V.READY;case V.NONE:case V.CLOSE:return V.CLOSE}case V.READY:switch(t){case V.READY:return V.NONE;case V.PLAY:case V.PAUSE:return t;case V.NONE:case V.CLOSE:return V.CLOSE}case V.PLAY:switch(t){case V.READY:case V.PLAY:return V.NONE;case V.PAUSE:return t;case V.NONE:case V.CLOSE:return V.CLOSE}case V.PAUSE:switch(t){case V.READY:case V.PAUSE:return V.NONE;case V.PLAY:return t;case V.NONE:case V.CLOSE:return V.CLOSE}case V.CLOSE:return V.NONE}}_getNextPipes(e){let t=Array(),i=e.outBinders;for(let e of i)e.status==Y.BINDED&&null!=e.sinkPipe&&t.push(e.sinkPipe);return t}_getPrevPipes(e){let t=Array();for(let i of e.inBinders)t.push(i.sourcePipe);return t}_bindNextPipe(e){var t;let i=new Array;e._OnMediaInfoChanged=null;let s=e.outBinders;for(let n of s)if(n.status==Y.NONE&&null!=n.sdpInfo){let s=this.makeNextPipe(e,n.sdpInfo);if(null==s)n.status=Y.NO_TARGET;else{this._waitSDPToBindNextPipe(s),n.sinkPipe=s,this._updateTailPipe(n.sourcePipe,n.sinkPipe),s._bindIncomingPipe(n)==K.OK?(n.status=Y.BINDED,i.push(n),this.log(`+++Bind [${this.pipeName}].[${e.pipeName} --\x3e ${s.pipeName}]`)):(null===(t=this._eventBus)||void 0===t||t.trigger("log",`+++Bind [${this.pipeName}].[${e.pipeName} --\x3e ${s.pipeName}] error`),console.error("+++Bind [%s].[%s --\x3e %s] error",this.pipeName,e.pipeName,s.pipeName))}}else console.error("Bind Pipeline[%s] NextPipe Error",this.pipeName);return i}_updateTailPipe(e,t){let i=!1;for(let s=0;s<this._tailPipes.length;s++){if(this._tailPipes[s]==e){this._tailPipes.splice(s,1,t),i=!0;break}}i||this._tailPipes.push(t)}_findExistPipeByName(e,t){if(!e.outBinders.length)return null;if(e.pipeName==t)return e;let i=null;for(let s of this._getNextPipes(e)){if(s.pipeName==t){i=s;break}if(i=this._findExistPipeByName(s,t),i&&i.pipeName==t)break}return i}findExistPipe(e){let t=null;for(let i of this._headPipes)if(t=this._findExistPipeByName(i,e),t)break;return t}_findNextPipeToString(e,t){if(!e.outBinders.length){if(0==t.length)return;let e="Current PipeLine Status: "+t[0];for(let i of t.slice(1,t.length))e+=" ---\x3e "+i;return e+="; date: "+(new Date).toString(),void console.debug(e)}for(let i of this._getNextPipes(e))i&&(t.push(this._pipeStatusToString(i)),this._findNextPipeToString(i,t),t.pop())}_pipeStatusToString(e){return"["+e.pipeName+": "+e.currentStatus+"]"}_printCurPipelineStatus(){let e=new Array;for(let t of this._headPipes)e.push(this._pipeStatusToString(t)),this._findNextPipeToString(t,e),e.pop()}log(e){var t;null===(t=this._eventBus)||void 0===t||t.trigger("log",e)}}ue.disabledH265=!1,function(e){e[e.NONE=0]="NONE",e[e.BINDED=1]="BINDED",e[e.NO_TARGET=2]="NO_TARGET"}(Y||(Y={}));class ce{constructor(e,t){this.sinkPipe=null,this.sourcePipe=e,this.sdpInfo=t,this.status=Y.NONE}}class de extends le{constructor(e){super(de.PipeName,e),this._sdp=new J,this._mp4TrackInfos=new Map,this._spsInit=!1}async onStatusChangeRequest(e){switch(e){case V.READY:return this._open();case V.PLAY:case V.PAUSE:break;case V.CLOSE:return this._close()}return K.OK}async onIncomingDataFromPrev(e,t){if(!t.aBuffer)return $.ERROR;if(!this._mp4TrackInfos)return $.ERROR;if(e.eMediaType==k.GS_AV_TYPE_VIDEO){let i=this._mp4TrackInfos.get(e.eMediaType);this._spsInit||(i=new _e(e),this._mp4TrackInfos.set(e.eMediaType,i),e.aConfigBuffer=z.initSegment(Array.from(this._mp4TrackInfos.values()),0,1e3),this._spsInit=!0);let s=new me(i,t);i.samples=[s];let n=z.moof(t.nSeq,s.pts,i),r=z.mdat(t.aBuffer),o=new re;o.nFlag=t.nFlag,o.nPTS=t.nPTS,o.nDTS=t.nDTS,o.nSTS=t.nSTS,o.aBuffer=new Uint8Array(n.length+r.length),o.aBuffer.set(n),o.aBuffer.set(r,n.length),this.outBinders[1]&&await this.sendDataToNextPipe(o,this.outBinders[1],e),await this.sendDataToNextPipe(o,this.outBinders[0],e)!=$.OK&&console.error("pipeName:["+de.PipeName+"]: sendDate error; date: "+(new Date).toString()),o.aBuffer=void 0,o=void 0,n=null,r=null,i.samples=void 0,s=void 0}return $.OK}onIncomingPipeRequest(e,t){if(e.eMediaType==k.GS_AV_TYPE_DATA)return K.OK;let i;return this._sdp&&(Object.assign(this._sdp,e),this._sdp.eMediaType=k.GS_AV_TYPE_COMPLEX,this._sdp.ePacketType=F.GS_AV_PACKET_TYPE_MP4,this._sdp.nTrackIndex=1,this._sdp.strTrackName="fmp4_track"),e.eMediaType==k.GS_AV_TYPE_VIDEO?i=new _e(e):e.eMediaType==k.GS_AV_TYPE_AUDIO&&(i=new fe(e)),i&&this._mp4TrackInfos&&(i.id=this._mp4TrackInfos.size+1,this._mp4TrackInfos.set(e.eMediaType,i)),K.OK}async _open(){let e=K.OK;if(this._sdp&&this._mp4TrackInfos){this._sdp.aConfigBuffer=z.initSegment(Array.from(this._mp4TrackInfos.values()),0,1e3);const t=Object.assign({},this._sdp);t.sinkPipeName="RecordPipe",e=await this.updateOutputSdpInfo([this._sdp,t])}return e}async _close(){let e=K.OK;return this._sdp&&(this._sdp.aConfigBuffer=new Uint8Array(0)),this._sdp=void 0,this._mp4TrackInfos=void 0,e}}de.PipeName="FMP4Remux",de.isDev=!1,de.record=!1;class pe{constructor(){this.isLeading=0,this.isDependedOn=0,this.hasRedundancy=0,this.degradPrio=0,this.isNonSync=0,this.dependsOn=0,this.paddingValue=0}}class me{constructor(e,t){this.duration=0,this.size=0,this.flags=new pe,this.cts=new u(0),this.pts=new u(0),this.dts=new u(0),this.duration=t.nDuration,t.aBuffer&&(this.size=t.aBuffer.byteLength),this.pts=t.nPTS,this.dts=t.nDTS,this.cts=this.pts.sub(this.dts),t.isKey()?(this.flags.dependsOn=2,this.flags.isNonSync=0):(this.flags.dependsOn=1,this.flags.isNonSync=1)}}class ge{constructor(){this.id=1,this.type="video",this.duration=4294967295,this.timescale=0,this.width=0,this.height=0}}class _e extends ge{constructor(e){if(super(),this.sps=new Array,this.pps=new Array,this.vps=new Array,this.type="video",this.timescale=e.nTimesacle,this.width=e.nWidth,this.height=e.nHeight,this.ecodeId=e.eCodeID,e.eCodeID==G.GS_CODEID_ST_H264){let t=e;t.aSPS&&t.aPPS&&(this.sps.push(t.aSPS),this.pps.push(t.aPPS))}else if(e.eCodeID==G.GS_CODEID_ST_H265){let t=e;t.aSPS&&t.aPPS&&t.aVPS&&(this.sps.push(t.aSPS),this.pps.push(t.aPPS),this.vps.push(t.aVPS))}}}class fe extends ge{constructor(e){super(),this.volume=0,this.channelCount=1,this.audiosamplerate=1,this.config=new Uint8Array(0),this.type="audio",this.channelCount=e.nChannels,this.audiosamplerate=e.nSamplerate,this.timescale=e.nSamplerate}}const Se=(e,t,i,s)=>(e.addEventListener(t,i,s),()=>e.removeEventListener(t,i,s));window.log="";class ye extends le{constructor(e,t){var i,s;super("MSEPlayer",t),this._breakCallback=()=>{var e;null===(e=this._eventBus)||void 0===e||e.trigger("breaking")},this._startTime=-1,this._hasDone=!1,this._inSDP=null,this._targetDiv=null,this._mediaSource=null,this._mseMinCacheSize=5,this._mseMaxCacheSize=320,this._lastStatus=V.NONE,this._allowPause=!1,this._mediaSourceObjectURL=null,this._removeEventList=[],this._isPlayback=!1,this._mimeCodec="",this._waitSync=!1,this._acceptFirstFrame=!1,this._rebuild=!1,this._waitChangeRate=!1,this._videoRate=1,this.lastFrame="",this._spsInit=!1;const{divToken:n,isPlayback:r,objectFit:o,videoId:a}=e;if(this._targetDiv=document.getElementById(n),!this._targetDiv)throw new Error("MSEPipe find targetDiv failed.");this.lastFrame=e.lastFrame;const h=document.createElement("img");let l;this._img=h,h.style.display="none",h.style.width="100%",h.style.height="100%",h.style.position="absolute",h.style.top="0",h.style.left="0",this._targetDiv.innerHTML="",this._video=document.createElement("video"),this._video.id=a,this._video.muted=!0,this._video.style.width="100%",this._video.style.height="100%",this._video.style.objectFit=o||"contain",this._video.setAttribute("class","playerClass"),this.lastFrame&&(h.src=this.lastFrame,h.style.display="block",h.style.objectFit=o||"contain",this._video.style.display="hidden");let u=0;this._video.onerror=()=>{var e;if(this._video&&this._video.error){if(console.error(`Error ${this._video.error.code}; details: ${this._video.error.message}`),!ye.reconnect)return;if(u+=1,clearTimeout(l),u>=10)return void(null===(e=this._eventBus)||void 0===e||e.emit("onMSEError"));l=setTimeout((()=>{u=0;const e=document.createElement("canvas"),t=e.getContext("2d");e.width=this._video.videoWidth,e.height=this._video.videoHeight,null==t||t.drawImage(this._video,0,0,e.width,e.height);const i=e.toDataURL("image/jpeg");this._video.style.display="hidden",h.src=i,h.style.display="block",this._video&&!this._rebuild&&(this._rebuild=!0,this._video.src="",this._startTime=-1,this._video.currentTime=0,this.openPromise().then((e=>{this._rebuild=!1,this._video.oncanplaythrough=()=>{var e;const{videoWidth:t,videoHeight:i}=this._video;null===(e=this._eventBus)||void 0===e||e.trigger("onUpdateResolution",`${t}X${i}`),this._video.play(),setTimeout((()=>{this._video.style.display="block",h.style.display="none"}),1e3)},this._video.oncomplete=()=>{setTimeout((()=>{this._video.style.display="block",h.style.display="none"}),1e3)}})))}),2e3)}},this._attachMediaElement(),this._targetDiv.appendChild(this._video),this._targetDiv.appendChild(h),this._isPlayback=r,null==t||t.listen("updateURL",(()=>{this._startTime=-1})),null===(i=this._eventBus)||void 0===i||i.listen("setVolume",(e=>{e&&this._video&&(this._video.muted=!1)})),null===(s=this._eventBus)||void 0===s||s.listen("setRate",(e=>{var t;this._video&&(this._videoRate=e,this._waitChangeRate=!0,null===(t=this._eventBus)||void 0===t||t.trigger("onRateChange")),console.error("set mse playrate: "+e)})),this._video&&(this._video.oncanplaythrough=()=>{var e;const{videoWidth:t,videoHeight:i}=this._video;null===(e=this._eventBus)||void 0===e||e.trigger("onUpdateResolution",`${t}X${i}`),setTimeout((()=>{this._video.style.display="block",h.style.display="none"}),1e3)})}_detachEventListener(){this._removeEventList.forEach((e=>e())),this._removeEventList=[]}_attachEventListener(){if(this._video){return[Se(this._video,"timeupdate",(()=>{if(this._video&&this._eventBus&&this._targetDiv){if(this.setRate(),this.resetCurrentTime(),!this._video.currentTime)return;if(this._eventBus.trigger("PlayTimeUpdate",1e3*this._video.currentTime),ye.isDev){const e=this._video.buffered.length;let t=[];for(let i=0;i<e;i++)t.push([this._video.buffered.start(i),this._video.buffered.end(i)]);window.log+=`${this._video.currentTime} ${t}\n`}}})),Se(this._video,"canplaythrough",(()=>{})),Se(this._video,"waiting",(()=>{}))]}return[]}_attachMediaElement(){this._removeEventList=this._attachEventListener()}_detachMediaElement(){if(this._mediaSource){let e=this._mediaSource,t=this._sourceBuffer;if(t){if("closed"!=e.readyState)try{e.removeSourceBuffer(t)}catch(e){console.error("mse removeSourceBuffer failed.")}this._sourceBuffer=void 0}if("open"==e.readyState)try{e.endOfStream()}catch(e){console.error("mse mediaSource end of stream failed.")}this._mediaSource=null}this._detachEventListener(),this._video&&(this._video.src="",this._video.removeAttribute("src")),this._mediaSourceObjectURL&&(window.URL.revokeObjectURL(this._mediaSourceObjectURL),this._mediaSourceObjectURL=null)}async onStatusChangeRequest(e){switch(e){case V.READY:return await this._open();case V.PLAY:return await this._play();case V.PAUSE:return await this._pause();case V.CLOSE:return await this._close()}return K.OK}onIncomingPipeRequest(e,t){return this._inSDP=e,K.OK}setRate(){this._waitChangeRate&&(this._waitChangeRate=!1,this._video.paused?(this._video.playbackRate=this._videoRate,16===this._videoRate&&(this._waitSync=!0,this.trySeekToPlayNewest())):(this._video.pause(),this._video.playbackRate=this._videoRate,16===this._videoRate&&(this._waitSync=!0,this.trySeekToPlayNewest()),setTimeout((()=>{this._allowPause&&this._video.play()}),500)),console.log("set rate",this._videoRate))}async onIncomingDataFromPrev(e,t){var i,s,n,r,o,a;if(this._inSDP=e,!this._spsInit&&this._sourceBuffer&&this._allowPause&&(this._sourceBuffer.appendBuffer(this._inSDP.aConfigBuffer),this._video.play(),this._spsInit=!0),(null===(i=this._video)||void 0===i?void 0:i.error)||!(null===(s=this._video)||void 0===s?void 0:s.src))return $.ERROR;if(!t.aBuffer||!this._inSDP)return console.log("buffer or sdp error"),$.ERROR;if(this._acceptFirstFrame||(this._acceptFirstFrame=!0,null===(n=this._eventBus)||void 0===n||n.trigger("onAcceptFirstFrame")),this._inSDP&&this._startTime<0&&this._video&&(this._startTime=t.nPTS.div(this._inSDP.nTimesacle).toNumber(),this._hasDone=this._startTime==this._video.currentTime,this._startTime+=t.nSTS.add(-t.nPTS).toNumber()/this._inSDP.nTimesacle),ye.isDev){const e=this._video.buffered.length;let i=[];for(let t=0;t<e;t++)i.push([this._video.buffered.start(t),this._video.buffered.end(t)]);window.log+=`${new Date} ${t.nSTS.add(-t.nPTS).toNumber()/this._inSDP.nTimesacle} ${this._video.currentTime} ${i}\n`}this.tryClearMseBuffer(),await this._canInputData();try{this._sourceBuffer&&!this._sourceBuffer.updating&&t.aBuffer&&(t.isKey()&&this.setRate(),this._sourceBuffer.appendBuffer(t.aBuffer),t.aBuffer=void 0)}catch(e){const t=(null===(r=this._video.error)||void 0===r?void 0:r.message)||"";t.indexOf("video decode error")>=0||t.indexOf("PIPELINE_ERROR_DECODE")>=0||t.indexOf("DECODER_ERROR")>=0?null===(o=this._eventBus)||void 0===o||o.trigger("onDecodeError","视频数据解码失败，暂不支持该视频源"):null===(a=this._eventBus)||void 0===a||a.trigger("proxy","getReconnectURL"),console.error("InputFrame Error:",e)}return $.OK}_printMseBufferMsg(){let e=this._sourceBuffer;if(e&&this._video&&e.buffered&&e.buffered.length>0)for(let t=0;t<e.buffered.length;++t)console.error("sourcebuffer["+t+"]: start="+e.buffered.start(t)+", end="+e.buffered.end(t)+", curplay="+this._video.currentTime)}tryClearMseBuffer(){let e=this._sourceBuffer,t=[];try{if(e&&e.buffered){let i=e.buffered.length;if(i>0&&this._video){let s=e.buffered.start(i-1);e.buffered.end(i-1);this._isPlayback&&!this._waitSync||this.trySeekToPlayNewest();let n=this._video.currentTime;if(n-s>this._mseMaxCacheSize&&t.push({start:s,end:n-this._mseMinCacheSize}),i>1)for(let s=0;s<i-1;++s){let i=e.buffered.start(s),n=e.buffered.end(s);t.push({start:i,end:n})}}for(;t.length&&!e.updating;){let i=t.shift();i&&e.remove(i.start,i.end)}this.resetCurrentTime()}}catch(e){console.error(e)}}resetCurrentTime(){const e=this._video.currentTime;let t=this._video.buffered.length;if(t>0){const i=this._video.buffered.start(0),s=this._video.buffered.end(0);e<this._video.buffered.start(0)?this._video.currentTime=this._video.buffered.start(0)+.5:e>s&&1==t?this._video.pause():e>i&&e<s-5&&1==t&&this._video.paused?this._allowPause&&this._video.play():e>s-.5&&e<s&&t>1?(this._video.currentTime=this._video.buffered.start(1)+.5,this._video.paused&&this._allowPause&&this._video.play()):this._allowPause&&this._video.paused&&this._video.play()}}trySeekToPlayNewest(){let e=this._sourceBuffer;if(this._video&&e&&e.buffered&&e.buffered.length){let t=this._video.currentTime,i=e.buffered.end(e.buffered.length-1);this._waitSync?i-t>=1&&!e.updating&&(this._video.currentTime=i-.5,this._waitSync=!1):i-t>=3&&!e.updating&&(this._video.currentTime=i-.5)}}tryGetNewestPlayTimeStamp(){var e;let t=this._sourceBuffer,i=null===(e=this._video)||void 0===e?void 0:e.currentTime;return t&&t.buffered&&t.buffered.length&&(i=t.buffered.end(t.buffered.length-1)),i}onAttributeChanged(e){var t;if(e.key==W.CURRENT_TIME){if(!this._inSDP)return K.ERROR;if(this._video&&this._lastStatus!=V.PAUSE){this._onClearMseBuffer();let t=e.value;this._startTime=t/1e3,this._video.currentTime=this._startTime,this._hasDone=this._startTime==this._video.currentTime}}else e.key==W.RATE&&(this._video&&this._videoRate!==e.value&&(this._videoRate=e.value,this._waitChangeRate=!0,null===(t=this._eventBus)||void 0===t||t.trigger("onRateChange")),console.error("set mse playrate: "+e.value));return K.OK}async _open(){return console.debug("MSEPipe Begin Open!"),null!=this._inSDP&&this._video?this.openPromise():K.ERROR}openPromise(){return new Promise((e=>{var t,i,s;let n="";(null===(t=this._inSDP)||void 0===t?void 0:t.eCodeID)==G.GS_CODEID_ST_H264?n='video/mp4; codecs="avc1.4d401f"':(null===(i=this._inSDP)||void 0===i?void 0:i.eCodeID)==G.GS_CODEID_ST_H265?n='video/mp4; codecs="hev1.1.6.L93.B0"':console.error("unknow codeid: "+(null===(s=this._inSDP)||void 0===s?void 0:s.eCodeID)),this._mimeCodec=n,"MediaSource"in window&&MediaSource.isTypeSupported(n)?(this._mediaSource=new MediaSource,this._mediaSourceObjectURL=URL.createObjectURL(this._mediaSource),this._video&&this._mediaSourceObjectURL&&(this._video.src=this._mediaSourceObjectURL),this._mediaSource.addEventListener("sourceopen",(()=>{try{if(!this._mediaSource||!this._inSDP||!this._inSDP.aConfigBuffer)return console.error("MSE Source Open param error."),void e(K.ERROR);this._sourceBuffer=this._mediaSource.addSourceBuffer(n),this._sourceBuffer.addEventListener("updateend",(()=>{this._onSourceBufferUpdateEnd()})),console.debug("MSEPipe Open sourcebuffer success!"),e(K.OK)}catch(t){console.error("MSE Source Open error "+t),e(K.ERROR)}}))):(console.error("Unsupported MIME type or codec: ",n),e(K.ERROR))}))}async _pause(){if(this._targetDiv&&this._video){if(!this._allowPause)return console.error("mse player not allow pause when play command is not finish!"),K.OK;this._video.pause(),this._allowPause=!1,this._lastStatus=V.PAUSE}return K.OK}async _close(){if(this._mediaSource&&this._sourceBuffer&&"open"==this._mediaSource.readyState)try{this._sourceBuffer.abort()}catch(e){console.error("_close::sourceBuffer::abort failed. reason: "+e)}try{this._detachMediaElement(),this._targetDiv&&this._video&&(this._targetDiv.removeChild(this._video),this._video=document.createElement("video"),this._targetDiv=null)}catch(e){console.error(e)}return K.OK}async _play(){try{this._video&&(this._allowPause=!0)}catch(e){console.error("MSEPipe Play error",e)}return this._lastStatus=V.PLAY,K.OK}_onSourceBufferUpdateEnd(){var e;!this._hasDone&&this._video&&this._video.buffered.length>=1&&(this._video.currentTime=this._startTime,this._video.currentTime<this._video.buffered.start(0)?(this._video.currentTime=this._video.buffered.start(0),this._startTime=this._video.currentTime):this._video.currentTime>this._video.buffered.end(0)&&(this._video.currentTime=this._video.buffered.end(0),this._startTime=this._video.currentTime),this._hasDone=Math.abs(this._startTime-this._video.currentTime)<.1),this._canInputEvent&&!(null===(e=this._sourceBuffer)||void 0===e?void 0:e.updating)&&this._canInputEvent()}_onClearMseBuffer(){let e=this._sourceBuffer,t=[];try{if(e&&e.buffered){let i=e.buffered.length;if(i>0&&this._video)for(let s=0;s<i;++s){let i=e.buffered.start(s),n=e.buffered.end(s);t.push({start:i,end:n})}for(this._canInputData();t.length&&!e.updating;){let i=t.shift();i&&e.remove(i.start,i.end)}}this._video&&(this._video.currentTime=0),console.debug("_onClearMseBuffer end")}catch(e){console.error(e)}}_onWaitingData(){}async _canInputData(){return this._sourceBuffer?(this._sourceBuffer.updating&&await new Promise((e=>{this._canInputEvent=e})),$.OK):$.ERROR}}ye.reconnect=!0,ye.isDev=!1;class ve{constructor(){this.eventMap=new Map}addEventListener(e,t){const i=this.eventMap.get(e);i?i.push(t):this.eventMap.set(e,[t])}removeEventListener(e,t){const i=this.eventMap.get(e);if(i){const e=i.indexOf(t);i.splice(e,1)}}listen(e,t){this.addEventListener(e,t)}emit(e,...t){const i=this.eventMap.get(e);if(i)for(let e of i)e(...t)}trigger(e,...t){const i=this.eventMap.get(e);if(i)for(let e of i)e(...t)}clear(e){this.eventMap.set(e,[])}}class we{static parse(e){let t={},i=/^([^:]+):\/\/([^\/]+)(.*)$/.exec(e);if(!i)throw new Error("bad url");t.full=e,t.protocol=i[1],t.urlpath=i[3];let s=t.urlpath.split("/");t.basename=(s.pop()||"").split(/\?|#/)[0],t.basepath=s.join("/");let n=i[2].split("@"),r=n[0].split(":"),o=[];return 2===n.length&&(o=n[0].split(":"),r=n[1].split(":")),t.user=o[0],t.pass=o[1],t.host=r[0],t.auth=t.user&&t.pass?`${t.user}:${t.pass}`:"",t.port=null==r[1]?we.protocolDefaultPort(t.protocol):r[1],t.portDefined=null!=r[1],t.location=`${t.host}:${t.port}`,"unix"==t.protocol&&(t.socket=t.port,t.port=void 0),t}static full(e){return`${e.protocol}://${e.location}/${e.urlpath}`}static base(e){return`${e.protocol}://${e.location}/${e.basename}`}static isAbsolute(e){return/^[^:]+:\/\//.test(e)}static protocolDefaultPort(e){switch(e){case"rtsp":return 554;case"ws":case"http":return 80;case"https":return 443}return 0}}function Te(e,t){if("video"==e.toLowerCase())switch(t.toLowerCase()){case"h264":return new te;case"h265":return new ie;case"mpeg4-generic":return new Q}if("audio"==e.toLowerCase())switch(t.toLowerCase()){case"l16":{let e=new ee;return e.eCodeID=G.GS_CODEID_AUDIO_ST_PCM,e.eSampleFormat=N.GS_AV_SAMPLE_FMT_S16,e.nSampleBits=16,e}case"l20":{let e=new ee;return e.eCodeID=G.GS_CODEID_AUDIO_ST_PCM,e.eSampleFormat=N.GS_AV_SAMPLE_FMT_S20,e.nSampleBits=20,e}case"l24":{let e=new ee;return e.eCodeID=G.GS_CODEID_AUDIO_ST_PCM,e.eSampleFormat=N.GS_AV_SAMPLE_FMT_S32,e.nSampleBits=32,e}case"MPEG4-GENERIC":return new se}return null}function Ie(e,t){let i=new Uint8Array((0|e.byteLength)+(0|t.byteLength));return i.set(e,0),i.set(t,0|e.byteLength),i}function Ee(e=[]){let t=0;for(let i of e)t+=0|i.byteLength;let i=new Uint8Array(t),s=0;for(let t of e)i.set(t,s),s+=t.byteLength;return i}function Re(e){for(var t=window.atob(e),i=t.length,s=new Uint8Array(i),n=0;n<i;n++)s[n]=t.charCodeAt(n);return s.buffer}function De(e){let t=e.length>>1;for(var i=new Uint8Array(t),s=0;s<t;s++)i[s]=parseInt(e.substr(s<<1,2),16);return i}function be(e,t=0,i=8*e.byteLength){let s=Math.ceil((i-t)/8),n=new Uint8Array(s),r=t>>>3,o=(i>>>3)-1,a=7&t,h=8-a,l=8-i&7;for(let t=0;t<s;++t){let i=0;t<o&&(i=e[r+t+1]>>h,t==o-1&&l<8&&(i>>=l,i<<=l)),n[t]=e[r+t]<<a|i}return n}class Pe{constructor(e){this.src=new DataView(e.buffer,e.byteOffset,e.byteLength),this.bitpos=0,this.byte=this.src.getUint8(0),this.bytepos=0}readBits(e){if(32<(0|e)||0==(0|e))throw new Error("too big");let t=0;for(let i=e;i>0;--i)t=(0|t)<<1|(0|this.byte)>>8-++this.bitpos&1,(0|this.bitpos)>=8&&(this.byte=this.src.getUint8(++this.bytepos),this.bitpos&=7);return t}skipBits(e){return this.bitpos+=7&(0|e),this.bytepos+=(0|e)>>>3,this.bitpos>7&&(this.bitpos&=7,++this.bytepos),this.finished()?this.bytepos-this.src.byteLength-this.bitpos:(this.byte=this.src.getUint8(this.bytepos),0)}finished(){return this.bytepos>=this.src.byteLength}}class Be{static get SampleRates(){return[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350]}static parseAudioSpecificConfig(e){let t;t=e instanceof Uint8Array?new Pe(e):e;let i=t.bitpos+8*(t.src.byteOffset+t.bytepos),s=t.readBits(5);this.codec=`mp4a.40.${s}`;let n=t.readBits(4);15==n&&t.skipBits(24);let r=t.readBits(4);return{config:be(new Uint8Array(t.src.buffer),i,i+16),codec:`mp4a.40.${s}`,samplerate:Be.SampleRates[n],channels:r}}static parseStreamMuxConfig(e){let t=new Pe(e);if(!t.readBits(1))return t.skipBits(14),Be.parseAudioSpecificConfig(t)}}const Le=l("parser:sdp");class Ce{constructor(){this.has_config=!1,this.version=-1,this.origin=null,this.sessionName=null,this.timing=null,this.media={}}getAudioSDPInfo(e){let t=new ne,i=new ee;return i&&(i.eMediaType=k.GS_AV_TYPE_AUDIO,i.eCodeID=G.GS_CODEID_AUDIO_ST_PCM,i.ePacketType=F.GS_AV_PACKET_TYPE_RTP,i.nTrackIndex=e,i.nChannels=2,i.nSampleBits=16,i.nSamplerate=8e3,i.strTrackName="TrackID="+i.nTrackIndex,i.nTimesacle=8e3,i.mParams.rtp_payload_type=10,t.trackInfos.push(i)),t}getAudioSDPInfoString(e){let t=this.getAudioSDPInfo(e),i="v=0\r\no=- 0 0 IN IP4 127.0.0.1\r\ns=No Name\r\nc=IN IP4 127.0.0.1\r\nt=0 0\r\na=tool:GSStreamer Streaming Media.V2020.09.21\r\n";return i+="m=audio 0 RTP/AVP "+t.trackInfos[e].mParams.rtp_payload_type+"\r\n",i+="b=AS:69\r\n",i+="a=rtpmap:"+t.trackInfos[e].mParams.rtp_payload_type+" L"+t.trackInfos[e].nSampleBits+"/"+t.trackInfos[e].nSamplerate+"/"+t.trackInfos[e].nChannels+"\r\n",i+="a=control:"+t.trackInfos[e].strTrackName+"\r\n",i}getSDPInfo(){let e=new ne,t=0;for(let i in this.media){let s=this.media[i];for(let i of s.fmt){let n=s.rtpmap[i];if(!n)continue;let r=Te(s.type,n.name);if(!r)continue;let o=r.eCodeID;if(o==G.GS_CODEID_ST_H264){if(r=new te,s.fmtp&&s.fmtp["sprop-parameter-sets"]){let e=s.fmtp["sprop-parameter-sets"].split(",");r.aSPS=Re(e[0]),r.aPPS=Re(e[1])}}else if(o==G.GS_CODEID_ST_H265)r=new ie;else if(o==G.GS_CODEID_AUDIO_ST_AAC){if(s.fmtp&&s.fmtp.config){let e,t=s.fmtp.config;this.has_config="0"!=s.fmtp.cpresent,"MPEG4-GENERIC"==s.rtpmap[i].name?(e={config:Be.parseAudioSpecificConfig(De(t))},n._rtp_parser.setConfig(e.config)):t&&(e={config:Be.parseStreamMuxConfig(De(t))},n._rtp_parser.aacparser.setConfig(e.config))}}else o==G.GS_CODEID_AUDIO_ST_PCM&&(r.nSamplerate=parseInt(n.clock),r.nChannels=parseInt(n.encparams));r&&(r.ePacketType=F.GS_AV_PACKET_TYPE_ES,r.nTrackIndex=t++,r.strTrackName=s.control,r.nTimesacle=parseInt(n.clock),r.mParams.rtp_payload_type=i,e.trackInfos.push(r))}}return e}parse(e){return new Promise(((t,i)=>{try{var s=e,n=!0,r=null;for(let e of s.split("\n"))if(e=e.replace(/\r/,""),0!==e.length){switch(e.charAt(0)){case"v":if(-1!==this.version)return Le.log("Version present multiple times in SDP"),i(),!1;n=n&&this._parseVersion(e);break;case"o":if(null!==this.origin)return Le.log("Origin present multiple times in SDP"),i(),!1;n=n&&this._parseOrigin(e);break;case"s":if(null!==this.sessionName)return Le.log("Session Name present multiple times in SDP"),i(),!1;n=n&&this._parseSessionName(e);break;case"t":if(null!==this.timing)return Le.log("Timing present multiple times in SDP"),i(),!1;n=n&&this._parseTiming(e);break;case"m":null!==r&&(this.media[r.type]=r),r={type:"",rtpmap:{}},this._parseMediaDescription(e,r);break;case"a":this._parseAttribute(e,r);break;default:Le.log("Ignored unknown SDP directive: "+e)}if(!n)return void i()}(null==r?void 0:r.type)&&(this.media[null==r?void 0:r.type]=r),n?t(!0):i()}catch(e){console.error(e),i()}}))}_parseVersion(e){let t=e.match(/^v=([0-9]+)$/);return t&&t.length?(this.version=t[1],0==+this.version||(Le.log("Unsupported SDP version:"+this.version),!1)):(Le.log("'v=' (Version) formatted incorrectly: "+e),!1)}_parseOrigin(e){let t=e.match(/^o=([^ ]+) (-?[0-9]+) (-?[0-9]+) (IN) (IP4|IP6) ([^ ]+)$/);return t&&t.length?(this.origin={username:t[1],sessionid:t[2],sessionversion:t[3],nettype:t[4],addresstype:t[5],unicastaddress:t[6]},!0):(Le.log("'o=' (Origin) formatted incorrectly: "+e),!1)}_parseSessionName(e){let t=e.match(/^s=([^\r\n]+)$/);return t&&t.length?(this.sessionName=t[1],!0):(Le.log("'s=' (Session Name) formatted incorrectly: "+e),!1)}_parseTiming(e){let t=e.match(/^t=([0-9]+) ([0-9]+)$/);return t&&t.length?(this.timing={start:t[1],stop:t[2]},!0):(Le.log("'t=' (Timing) formatted incorrectly: "+e),!1)}_parseMediaDescription(e,t){let i=e.match(/^m=([^ ]+) ([^ ]+) ([^ ]+)[ ]/);return i&&i.length?(t.type=i[1],t.port=i[2],t.proto=i[3],t.fmt=e.substr(i[0].length).split(" ").map((function(e,t,i){return parseInt(e)})),!0):(Le.log("'m=' (Media) formatted incorrectly: "+e),!1)}_parseAttribute(e,t){if(null===t)return!0;var i=e.indexOf(":");let s;switch(e.substr(0,-1===i?2147483647:i)){case"a=recvonly":case"a=sendrecv":case"a=sendonly":case"a=inactive":t.mode=e.substr(2);break;case"a=range":s=e.match(/^a=range:\s*([a-zA-Z-]+)=([0-9.]+|now)\s*-\s*([0-9.]*)$/),t.range=[Number("now"==s[2]?-1:s[2]),Number(s[3]),s[1]];break;case"a=control":t.control=e.substr(10);break;case"a=rtpmap":if(s=e.match(/^a=rtpmap:(\d+) (.*)$/),null===s)return Le.log("Could not parse 'rtpmap' of 'a='"),!1;var n=parseInt(s[1]);t.rtpmap[n]={};var r=s[2].split("/");t.rtpmap[n].name=r[0].toUpperCase(),t.rtpmap[n].clock=r[1],void 0!==r[2]&&(t.rtpmap[n].encparams=r[2]),t.ptype=r[0].toUpperCase();break;case"a=fmtp":if(s=e.match(/^a=fmtp:(\d+) (.*)$/),0===s.length)return Le.log("Could not parse 'fmtp'  of 'a='"),!1;for(var o of(t.fmtp={},s[2].split(";"))){var a=o.indexOf("=");t.fmtp[o.substr(0,a).toLowerCase().trim()]=o.substr(a+1).trim()}}return!0}hasMedia(e){return null!=this.media[e]}getMediaBlock(e){return this.media[e]}getMediaBlockList(){var e=[];for(var t in this.media)e.push(t);return e}}class Ae{constructor(e,t,i,s,n,r){this.data=null,this.ntype=e,this.nri=t,this.forbidden_zero_bit=i,this.dts=n,this.pts=r||this.dts,this.sliceType=null,this._size=0,s?(this._size+=s.byteLength,this._slices=[s]):this._slices=[]}static get NDR(){return 1}static get SLICE_PART_A(){return 2}static get SLICE_PART_B(){return 3}static get SLICE_PART_C(){return 4}static get IDR(){return 5}static get SEI(){return 6}static get SPS(){return 7}static get PPS(){return 8}static get DELIMITER(){return 9}static get EOSEQ(){return 10}static get EOSTR(){return 11}static get FILTER(){return 12}static get STAP_A(){return 24}static get STAP_B(){return 25}static get FU_A(){return 28}static get FU_B(){return 29}static get TYPES(){return{[Ae.IDR]:"IDR",[Ae.SEI]:"SEI",[Ae.SPS]:"SPS",[Ae.PPS]:"PPS",[Ae.NDR]:"NDR"}}static type(e){return e.ntype in Ae.TYPES?Ae.TYPES[e.ntype]:"UNKNOWN"}appendData(e){this._size+=e.byteLength,this._slices.push(e)}finish(){null==this.data?(this.data=Ee(this._slices),this._slices=[]):(this.data=Ie(this.data,Ee(this._slices)),this._slices=[])}toString(){var e;return`${Ae.type(this)}(${null===(e=this.data)||void 0===e?void 0:e.byteLength}): NRI: ${this.getNri()}, PTS: ${this.pts}, DTS: ${this.dts}`}getNri(){return this.nri>>5}type(){return this.ntype}isKeyframe(){return this.ntype===Ae.IDR||7===this.sliceType}getSize(){return this._size+4+1}getData(){if(this._slices.length>0&&this.finish(),!this.data)return new Uint8Array;let e=new Uint8Array(5+this.data.byteLength),t=new DataView(e.buffer);return t.setUint32(0,1),t.setUint8(4,128&this.forbidden_zero_bit|96&this.nri|31&this.ntype),e.set(this.data,5),e}getFrameType(){return this.type()==Ae.IDR?M.GS_AV_FRAME_VIDEO_I:this.type()==Ae.NDR?M.GS_AV_FRAME_VIDEO_P:this.type()==Ae.PPS?M.GS_AV_FRAME_VIDEO_PPS:this.type()==Ae.SPS?M.GS_AV_FRAME_VIDEO_SPS:this.type()==Ae.SEI?M.GS_AV_FRAME_VIDEO_SEI:M.GS_AV_FRAME_TYPE_NONE}}const Oe=l("client:rtsp");class xe{constructor(){this.fragmented_nalu=null}static parseNALHeader(e){return{forbidden_zero_bit:128&e,nri:96&e,type:31&e}}parseSingleNALUPacket(e,t,i,s){let n=new Ae(t.type,t.nri,t.forbidden_zero_bit,e.subarray(0),i,s);return n.finish(),n}parseAggregationPacket(e,t,i,s){let n=new DataView(e.buffer,e.byteOffset,e.byteLength),r=0;Ae.STAP_B===t.type&&(n.getUint16(r),r+=2);let o=[];for(;r<n.byteLength;){let t=n.getUint16(r);r+=2;let a=xe.parseNALHeader(n.getUint8(r));r++;let h=this.parseSingleNALUPacket(e.subarray(r,r+t-1),a,i,s);null!==h&&(h.finish(),o.push(h)),r+=t-1}return o}parseFragmentationUnit(e,t,i,s){let n=new DataView(e.buffer,e.byteOffset,e.byteLength),r=0,o=n.getUint8(r),a=(128&o)>>>7,h=(64&o)>>>6,l=31&o,u=null;return r++,Ae.FU_B===t.type&&(n.getUint16(r),r+=2),a&&(this.fragmented_nalu=new Ae(l,t.nri,t.forbidden_zero_bit,e.subarray(r),i,s)),this.fragmented_nalu&&this.fragmented_nalu.ntype===l&&(a||this.fragmented_nalu.appendData(e.subarray(r)),h)?(u=this.fragmented_nalu,this.fragmented_nalu=null,u.finish(),u):null}onNALUFragment(e,t,i){let s=new DataView(e.buffer,e.byteOffset,e.byteLength),n=xe.parseNALHeader(s.getUint8(0)),r=null;if(n.type>0&&n.type<24)r=this.parseSingleNALUPacket(e.subarray(1),n,t,i);else{if(Ae.FU_A!==n.type&&Ae.FU_B!==n.type)return Ae.STAP_A===n.type||Ae.STAP_B===n.type?this.parseAggregationPacket(e.subarray(1),n,t,i):(Oe.log("Undefined NAL unit, type: "+n.type),null);r=this.parseFragmentationUnit(e.subarray(1),n,t,i)}return r?[r]:null}}class ke{constructor(e,t,i){this.dts=t,this.pts=i||this.dts,this.data=e}getData(){return this.data}getSize(){return this.data.byteLength}isKeyframe(){return!0}getFrameType(){return M.GS_AV_FRAME_AUDIO}}class Me{constructor(){this.config={samplerate:44100}}onAACFragment(e){let t=e.getPayload();if(!e.media)return null;let i,s,n,r=new DataView(t.buffer,t.byteOffset,t.byteLength),o=0,a=0,h=0,l=0,u=0;if(e.media.fmtp&&e.media.fmtp.sizelength){a=Number(e.media.fmtp.sizelength||0),h=Number(e.media.fmtp.indexlength||0),l=Number(e.media.fmtp.indexdeltalength||0),u=Number(e.media.fmtp.ctsdeltalength||0),i=Number(e.media.fmtp.dtsdeltalength||0),s=Number(e.media.fmtp.randomaccessindication||0),n=Number(e.media.fmtp.streamstateindication||0);let t=Number(e.media.fmtp.auxiliarydatasizelength||0);o=a+Math.max(h,l)+u+i+s+n+t}let c=0,d=0,p=9e4*(Math.round(e.getTimestampMS()/1024)<<10)/this.config.samplerate;if(0!==o){let e=r.getUint16(0);c=2+(e>>>3)+(7&e?1:0);let o=[],m=0,g=new Pe(t.subarray(2+d)),_=0,f=0;for(let r=0;r<e;){let e=g.readBits(a);g.readBits(r?l:h),r+=a+(r?l:h),u&&(g.readBits(1),_=g.readBits(u),r+=u),i&&(g.readBits(1),f=g.readBits(i),r+=u),s&&(g.skipBits(1),r+=1),n&&(g.skipBits(n),r+=n),o.push(new ke(t.subarray(c+m,c+m+e),p+f,p+_)),m+=e}return o}{let e=t.subarray(c);for(;255==e[d];)++d;return++d,[new ke(t.subarray(c+d),p)]}}}class Ge{constructor(e,t,i,s,n,r,o){this.data=null,this.ntype=e,this.layerid=t,this.forbidden_zero_bit=i,this.tid=s,this.dts=r,this.pts=o||this.dts,this._size=0,n?(this._size+=n.byteLength,this._slices=[n]):this._slices=[]}static get TRAIL_N(){return 0}static get NDR(){return 1}static get TSA_N(){return 2}static get TSA_R(){return 3}static get STSA_N(){return 4}static get STSA_R(){return 5}static get RADL_N(){return 6}static get RADL_R(){return 7}static get RASL_N(){return 8}static get RASL_R(){return 9}static get BLA_W_LP(){return 16}static get BLA_W_RADL(){return 17}static get BLA_N_LP(){return 18}static get IDR(){return 19}static get DIR_N_LP(){return 20}static get CRA(){return 21}static get VPS(){return 32}static get SPS(){return 33}static get PPS(){return 34}static get AUD(){return 35}static get EOS(){return 36}static get EOB(){return 37}static get FD(){return 38}static get SEI(){return 39}static get FU_A(){return 49}static get AP_A(){return 48}static get TYPES(){return{[Ge.IDR]:"IDR",[Ge.NDR]:"NDR",[Ge.VPS]:"VPS",[Ge.SPS]:"SPS",[Ge.PPS]:"PPS",[Ge.SEI]:"SEI"}}static type(e){return e.ntype in Ge.TYPES?Ge.TYPES[e.ntype]:"UNKNOWN"}appendData(e){this._size+=e.byteLength,this._slices.push(e)}finish(){null==this.data?(this.data=Ee(this._slices),this._slices=[]):(this.data=Ie(this.data,Ee(this._slices)),this._slices=[])}toString(){var e;return`${Ge.type(this)}(${null===(e=this.data)||void 0===e?void 0:e.byteLength}): NAL: ${this.ntype}, PTS: ${this.pts}, DTS: ${this.dts}`}type(){return this.ntype}isKeyframe(){return this.ntype===Ge.IDR}getSize(){return this._size+4+2}getData(){if(this._slices.length>0&&this.finish(),!this.data)return new Uint8Array;let e=new Uint8Array(6+this.data.byteLength),t=new DataView(e.buffer);return t.setUint32(0,1),t.setUint8(4,128&this.forbidden_zero_bit|this.ntype<<1&126|(32&this.layerid)>>5),t.setUint8(5,(31&this.layerid)<<3|7&this.tid),e.set(this.data,6),e}getFrameType(){return this.type()==Ge.IDR?M.GS_AV_FRAME_VIDEO_I:this.type()==Ge.NDR?M.GS_AV_FRAME_VIDEO_P:this.type()==Ge.PPS?M.GS_AV_FRAME_VIDEO_PPS:this.type()==Ge.SPS?M.GS_AV_FRAME_VIDEO_SPS:this.type()==Ge.VPS?M.GS_AV_FRAME_VIDEO_VPS:this.type()==Ge.SEI?M.GS_AV_FRAME_VIDEO_SEI:M.GS_AV_FRAME_TYPE_NONE}}const Ne=l("client:rtsp");class Fe{constructor(){this.fragmented_nalu=null}static parseNALHeader(e){let t=e.getUint8(0),i=e.getUint8(1);return{type:(126&t)>>1,layerid:(1&t)<<5|(248&i)>>3,forbidden_zero_bit:128&t,tid:7&i}}parseSingleNALUPacket(e,t,i,s){let n=new Ge(t.type,t.layerid,t.forbidden_zero_bit,t.tid,e.subarray(0),i,s);return n.finish(),n}parseAggregationPacket(e,t,i,s){let n=new DataView(e.buffer,e.byteOffset,e.byteLength),r=0,o=[];for(;r<n.byteLength;){let t=n.getUint16(r);r+=2;let a=Fe.parseNALHeader(n);r+=1;let h=this.parseSingleNALUPacket(e.subarray(r,r+t-1),a,i,s);null!==h&&(h.finish(),o.push(h)),r+=t-1}return o}parseFragmentationUnit(e,t,i,s){let n=0,r=new DataView(e.buffer,e.byteOffset,e.byteLength).getUint8(n),o=(128&r)>>>7,a=(64&r)>>>6,h=63&r,l=null;return n++,1==o&&(this.fragmented_nalu=new Ge(h,t.layerid,t.forbidden_zero_bit,t.tid,e.subarray(n),i,s)),this.fragmented_nalu&&this.fragmented_nalu.ntype===h&&(o||this.fragmented_nalu.appendData(e.subarray(n)),1==a)?(l=this.fragmented_nalu,this.fragmented_nalu=null,l.finish(),l):null}onNALUFragment(e,t,i){let s=new DataView(e.buffer,e.byteOffset,e.byteLength),n=Fe.parseNALHeader(s),r=null;if(n.type==Ge.FU_A)r=this.parseFragmentationUnit(e.subarray(2),n,t,i);else if(n.type==Ge.VPS||n.type==Ge.SPS||n.type==Ge.PPS||n.type==Ge.SEI)r=this.parseSingleNALUPacket(e.subarray(2),n,t,i);else{if(n.type==Ge.AP_A)return this.parseAggregationPacket(e.subarray(2),n,t,i);if(50==n.type)return Ne.log("Undefined NAL unit, type: "+n.type),null;r=this.parseSingleNALUPacket(e.subarray(2),n,t,i)}return r?[r]:null}}l("rtp:parser");class Ue{constructor(e){this.csrcs=[];let t=new DataView(e.buffer,e.byteOffset,e.byteLength);this._version=t.getUint8(0)>>>6,this._padding=(32&t.getUint8(0))>>>5,this._has_extension=(16&t.getUint8(0))>>>4,this._csrc=15&t.getUint8(0),this._marker=t.getUint8(1)>>>7,this._pt=127&t.getUint8(1),this._sequence=t.getUint16(2),this._timestamp=t.getUint32(4),this._ssrc=t.getUint32(8),this._csrcs=[];let i=12;this._csrc>0&&(this.csrcs.push(t.getUint32(i)),i+=4),1==this._has_extension&&(this._extension=t.getUint16(i),this._ehl=t.getUint16(i+2),i+=4,this.parseExtendtion(e.subarray(i),4*this._ehl),i+=4*this._ehl),this._headerLength=i,this._padding&&t.getUint8(e.byteLength-1),this._data=e.subarray(i),this._byteLength=this._data.byteLength}getPayload(){return this._data}getTimestampMS(){return this._timestamp}toStringEx(){return"RTP(version:"+this._version+", padding:"+this._padding+", has_extension:"+this._has_extension+", csrc:"+this._csrc+", marker:"+this._marker+", pt:"+this._pt+", sequence:"+this._sequence+", timestamp:"+this._timestamp+", ssrc:"+this._ssrc+")"}toString(){return"RTP(pt:"+this._pt+", sequence:"+this._sequence+", timestamp:"+this._timestamp+",len:"+this._byteLength+")"}parseExtendtion(e,t){let i={},s=new DataView(e.buffer,e.byteOffset,e.byteLength),n=0;for(;n<t;){let e=s.getInt8(n),r=s.getInt8(n+1);if(0!=r){if(n+=2,n>=t)break;switch(e){case 1:{let e={bIsKeyFrame:s.getUint16(n+0),nFrameNum:s.getInt32(n+2),nPTS:s.getBigInt64(n+6),nDTS:s.getBigInt64(n+12),nSTS:s.getBigInt64(n+20)};i.GSMediaDataHeader=e;break}case 2:{let e={eFrameType:s.getInt32(n+0),nBlockId:s.getInt16(n+4),nBlockMaxNum:s.getInt16(n+6)};i.GSVideoDataHeader=e;break}}n+=r,r%2!=0&&(n+=r%2)}else n+=1}this.media_info=i}}class Ve{constructor(e){this._rtp=e}getData(){return this._rtp._data}getSize(){return this._rtp._data.byteLength}isKeyframe(){return!0}getFrameType(){return M.GS_AV_FRAME_AUDIO}}class We{constructor(){this.name=""}parse(e){return[new Ve(e)]}setConfig(e){}static createParser(e){return e==G.GS_CODEID_ST_H264?new Ke:e==G.GS_CODEID_ST_H265?new $e:e==G.GS_CODEID_AUDIO_ST_AAC?new Ye:new We}}class Ke extends We{constructor(){super(),this.naluasm=new xe}parse(e){return this.naluasm.onNALUFragment(e.getPayload(),e.getTimestampMS())}}class $e extends We{constructor(){super(),this.naluasm=new Fe}parse(e){return this.naluasm.onNALUFragment(e.getPayload(),e.getTimestampMS())}}class Ye extends We{constructor(){super(),this.scale=1,this.asm=new Me}setConfig(e){this.asm.config=e}parse(e){return this.asm.onAACFragment(e)}}const He=l("rtsp:stream"),qe=4294967295;class je{constructor(e,t){this._rtp_parser=null,this._client=e,this._trackInfo=t,this._payloadType=t.mParams.rtp_payload_type,this._rtpChannel=2*t.nTrackIndex,this._rtcpChannel=this._rtpChannel+1,this._lastPkt=null,this._frameSeq=1,this._lastPTS=new u(0),this._baseNtpInfo=null,this._targetNtpInfo=null,this._playing=!1,this._packets=[],this._startPts=new u(-1)}clearPackets(){for(console.debug("clear rtp_stream packet: payloadType: "+this._payloadType+"; packets num: "+this._packets.length);this._packets.length>0;)this._packets.shift()}init(){return this._rtp_parser=We.createParser(this._trackInfo.eCodeID),!!this._rtp_parser}pause(){this._playing=!1}play(e,t,i){this._playing=!0,this._targetNtpInfo={baseNtpTime:e.mul(this._trackInfo.nTimesacle/1e3),seq:t,rtpTime:i,loopTimes:0,maxRtpTime:qe},this._baseNtpInfo=null,He.debug(`Play! seek to ${new Date(e.toNumber())} npt: ${JSON.stringify(this._targetNtpInfo)}`),this.clearOldPackets(this._targetNtpInfo),this.buildFrameFromRtpPacket()}stop(){this._playing=!1}getSetupParam(){return{Transport:`RTP/AVP/TCP;unicast;interleaved=${this._rtpChannel}-${this._rtcpChannel}`,Date:(new Date).toUTCString()}}getSetupURL(e){let t=this._trackInfo.strTrackName;return we.isAbsolute(t)?this._trackInfo.control:we.isAbsolute(`${e}/${t}`)?`${e}/${t}`:t}getPayloadType(){return this._payloadType}onRtpPacketMessage(e){var t;let i=new Ue(e);if(i._pt==this._payloadType){if(null==this._lastPkt)this._lastPkt=i,He.debug("*** rcv rtp begin : "+this._lastPkt.toStringEx()+" Parser:"+(null===(t=this._rtp_parser)||void 0===t?void 0:t.name));else{let e=i._sequence-this._lastPkt._sequence|0;e>1?He.error("rtp pkt lose : "+this._lastPkt.toString()+" => "+i.toString()):e<0&&He.debug("rcv rtp loop : "+this._lastPkt.toStringEx());let t=i._timestamp-this._lastPkt._timestamp;if(t<0&&this._baseNtpInfo){this._baseNtpInfo.loopTimes++,Math.abs(Math.abs(t)-qe)>1e3&&(this._baseNtpInfo.maxRtpTime=this._lastPkt._timestamp),He.info("rcv rtp ts loop : "+this._lastPkt.toString()+" => "+i.toString())}this._lastPkt=i}this._packets.push(i),this.isTargetPacketArrived(this._targetNtpInfo)&&(this._baseNtpInfo=this._targetNtpInfo,this._targetNtpInfo=null,He.info(`Receive target npt packet! npt: ${JSON.stringify(this._baseNtpInfo)}`)),this._playing&&this.buildFrameFromRtpPacket()}else He.log(`Media description for payload type: ${this.pt} not provided.`)}onRtcpPacketMessage(e){}isTargetPacketArrived(e){if(!e)return!1;let t=0;do{if(this._packets[t]._sequence==e.seq)return!0;t++}while(t<this._packets.length)}clearOldPackets(e){for(;this._packets.length>0;){if(this._packets[0]._sequence==e.seq)break;this._packets.shift()}}buildFrameFromRtpPacket(){if(this._baseNtpInfo)for(;this._packets.length>0;){this._packets.length;let e=this._packets.shift();if(!this._rtp_parser||!e)return;let t=this._rtp_parser.parse(e);if(t&&e)try{for(let i of t){let t=null;if(this._trackInfo.eMediaType==k.GS_AV_TYPE_VIDEO)t=new oe;else{if(this._trackInfo.eMediaType!=k.GS_AV_TYPE_AUDIO)continue;t=new ae}t.aBuffer=i.getData(),t.aBuffer||console.debug("frame aBuffer is null"),t.eFrameType=i.getFrameType(),t.nSeq=this._frameSeq++,this._startPts.compare(new u(-1))||(this._startPts=this._baseNtpInfo.baseNtpTime.add(e.getTimestampMS()));let s=this._startPts;t.nPTS=this._startPts.add(e.getTimestampMS()-this._baseNtpInfo.rtpTime+this._baseNtpInfo.loopTimes*this._baseNtpInfo.maxRtpTime),t.nSTS=s,t.nDTS=t.nPTS,t.nTimestamp=e.getTimestampMS(),i.isKeyframe()&&t.setKeyFlag(),this._lastPTS.compare(new u(0))?(t.nDuration=t.nPTS.sub(this._lastPTS).toNumber(),0==t.nDuration&&(t.nPTS=t.nPTS.add(1),t.nDTS=t.nPTS,t.nDuration=1)):t.nDuration=3600,t.eFrameType>=M.GS_AV_FRAME_VIDEO_CONFIG&&t.eFrameType<=M.GS_AV_FRAME_VIDEO_SEI&&(t.nDuration=0),t.eFrameType!=M.GS_AV_FRAME_VIDEO_I&&t.eFrameType!=M.GS_AV_FRAME_VIDEO_P||(this._lastPTS=t.nPTS),this._client._fnFrameData(this._trackInfo,t)}t=[]}catch(e){console.error("RtpStream Error: ",e)}}}}const Xe=l("rtsp:parser");class ze{constructor(){this.cseq=0}static parseRtspData(e){let t=new DataView(e.buffer,e.byteOffset,e.byteLength),i={channel:0,len:0,data:new Uint8Array},s=0;return 36!=t.getUint8(s)?(Xe.error("parseRtspData error: StartCode is not 0x24!\n"),null):(s+=1,i.channel=t.getUint8(s),s+=1,i.len=t.getUint16(s),s+=2,i.data=e.subarray(s),i)}static parseRtspMessge(e){let t=e.split("\r\n\r\n");if(t.length<1)return Xe.error("parseRtspMessge split fail.\n"),null;let i=t[0].split("\r\n"),s={headers:{},code:-1,cseq:-1,statusLine:"",body:""};if(i.length<1)return Xe.error("parseRtspMessge header split fail."),null;let n=null;const r=i[0].match(new RegExp("RTSP/1.0[ ]+([0-9]{3})[ ]+(.*)"));if(n=r[0],s.code=+r[1],s.statusLine=r[2],!n)return Xe.error("parseRtspMessge match fail."),null;s.code=Number(s.code);let o=1;for(;i[o];){let[e,t]=i[o].split(/:(.+)/);s.headers[e.toLowerCase()]=t.trim(),o++}if(s.headers.hasOwnProperty("content-length")){Number(s.headers["content-length"])&&t.length>1?s.body=t[1]:s.body=""}else s.body="";if(s.headers.hasOwnProperty("cseq")){let e=Number(s.headers.cseq);null!=e&&(s.cseq=e)}return Xe.debug(`Parser End CSeq: ${s.cseq}`),s}static parserRtspRtpInfo(e){let t={};try{let i=e.split(",");for(let e of i){let i="",s=e.split(";");for(let e of s){let[s,n]=e.split("="),r=s.toLowerCase().trim();"url"==r&&(i=e.slice(e.indexOf("=")+1),t[i]={seq:1/0,rtptime:1/0}),"seq"==r&&""!=i&&(t[i].seq=parseInt(n.trim())),"rtptime"==r&&""!=i&&(t[i].rtptime=parseInt(n.trim()))}}}catch(e){}return t}static parseRtspRange(e){let t={startTime:new u(0),endTime:new u(0)};try{if(!e)return t;let i=e.split("=");if(2!=i.length&&"clock"!=i[0].toLowerCase()&&"npt"!=i[0].toLowerCase())return t;let s=i[1].split("-");if(2!=s.length)return t;""!=s[0]&&(t.startTime=u.fromString(s[0])),""!=s[1]&&(t.endTime=u.fromString(s[1]))}catch(e){}return t}}const Ze=l("rtsp:ws");let Je=window.WebSocket;try{Je=window.WebSocket}catch(e){Je=require("ws")}class Qe extends Error{constructor(e,t,i,s){super(`${e} ${t} ${i}`),this.code=0,this.cseq=0,this.body="",this.headers={},this._data=s,this._errname=e,this._errcmd=t,this._errmsg=i}}class et{constructor(e){this._ws=null,this._wsURL="",this._rtspCSeq=0,this._rtspVer="RTSP/1.0",this._awaitingResPromises=[],this._eventBus=e,this._isOpended=!1,this._isConnected=!1,this._fnConnect=function(){},this._fnDisconnect=function(){},this._fnRtpData=function(e){},this._fnError=function(e){}}arrayBufferToString(e){return(new TextDecoder).decode(new Uint8Array(e))}async connect(e,t){this._wsURL=e,this._ws=null,this._ws=new Je(this._wsURL),this._ws.binaryType="arraybuffer",this._isOpended=!0;let i,s=0;this._ws.onopen=()=>{Ze.debug("WebSocket onopen!"),this._isConnected=!0,this._fnConnect(),i=setInterval((()=>{s&&(this._eventBus.trigger("onUpdateCoderate",(s/262144).toFixed(1)+"Mbps"),s=0)}),2e3)},this._ws.onmessage=e=>{if(!this._isConnected)return;let t=new Uint8Array(e.data);if(s+=t.byteLength,36==t[0])this._fnRtpData(t);else{let t=this.arrayBufferToString(e.data),i=ze.parseRtspMessge(t);i?this.onRtspMessage(i):(Ze.error(`Parser rtsp messge fail:\n${t}`),this._fnError(new Qe("RtspParseErr","RtspParser","Parser ws data to rtsp messgae fail",e.data)))}},this._ws.onerror=e=>{console.log(e),clearInterval(i),Ze.error(`[ws error] ${e}`),this._isConnected&&this._fnDisconnect(),this.disconnect(),this._eventBus.trigger("log",`[ws error] ${JSON.stringify(e)}`),this._fnError("websocket on Error"),this._eventBus.trigger("reconnect")},this._ws.onclose=e=>{if(console.log(e),clearInterval(i),this._eventBus.trigger("log",`[ws close] ${JSON.stringify(e)}`),1e3==e.code)this._isConnected&&this._fnDisconnect();else if(1006==e.code)this._eventBus.trigger("reconnect");else{const t=`[ws close] ${e.type}. code: ${e.code} ${e.reason||"unknown reason"}; date: `+(new Date).toUTCString();this._eventBus.trigger("log",t),Ze.error(t),this._fnError("websocket close abnormal. code: "+e.code)}this.disconnect()}}createLog(e,t){var i=document.createElement("a");i.setAttribute("href","data:text/plain;charset=utf-8,"+encodeURIComponent(t)),i.setAttribute("download",e),i.style.display="none",document.body.appendChild(i),i.click(),document.body.removeChild(i)}disconnect(e){console.log(e),this._isConnected=!1,this._ws&&(this._isOpended=!1,this._ws.close(1e3,e)),this._eventBus.trigger("stopHeartbeat"),this._ws=null}async sendRequest(e,t={},i="",s,n,r,o=1e4){let a=this._rtspCSeq++,h=new Promise(((t,i)=>{setTimeout((()=>{let t=new Qe("timeout",e,`Wait Respone Timeout ${o} ${e} CSeq:${a}`);i(t)}),o)})),l=this.send(a,e,t,i,r);Promise.race([h,l]).then((t=>{this.deleteWaitingResponse(a),t.code<300?(Ze.debug(`Command ${e}  CSeq:${a} response success!!`),s(t)):(n(new Qe("response",e,`Rcv fail response  CSeq:${a} rtsp res code:${t.code}`),t),this._eventBus.trigger("log",`${e} Rcv fail response  CSeq:${a} rtsp res code:${t.code}`))})).catch((t=>{this._eventBus.trigger("stopHeartbeat"),Ze.debug(`ASend fail ${t}`),this._eventBus.trigger("log",`ASend fail ${t}`),this.deleteWaitingResponse(a),n(null==t?new Qe("response",e,`Rcv fail response  CSeq:${a}`):t)}))}async sendRtpPacket(e,t=1e4){this._ws&&this._ws.send(this.encrypt(e))}encrypt(e){return e}send(e,t,i={},s,n){i?i.CSeq=e:i={CSeq:e},i["User-Agent"]="GXXRTSP 1.0";let r=this.buildRtspMessage(t,n,i,s);return new Promise(((t,i)=>{this._awaitingResPromises[e]={resolve:t,reject:i},this._ws&&this._ws.send(this.encrypt(r))}))}onRtspMessage(e){this._awaitingResPromises.hasOwnProperty(e.cseq)?(Ze.debug(`Rtsp res CSeq: ${e.cseq} resolve waiting`),this._awaitingResPromises[e.cseq].resolve(e)):Ze.warn(`Rtsp res CSeq: ${e.cseq} not found waiting`)}deleteWaitingResponse(e){this._awaitingResPromises.hasOwnProperty(e)&&delete this._awaitingResPromises[e]}buildRtspMessage(e,t,i={},s=""){let n=`${e} ${t} ${this._rtspVer}\r\n`;for(let e in i)n+=`${e}: ${i[e]}\r\n`;return s&&(n+=`Content-Length: ${s.length}\r\n`),n+="\r\n",s&&(n+=s),n}}l("rtp:packer");class tt{constructor(e){this.csrcs=[];let t=new DataView(e.buffer,e.byteOffset,e.byteLength);this._version=t.getUint8(0)>>>6,this._padding=(32&t.getUint8(0))>>>5,this._has_extension=(16&t.getUint8(0))>>>4,this._csrc=15&t.getUint8(0),this._marker=t.getUint8(1)>>>7,this._pt=127&t.getUint8(1),this._sequence=t.getUint16(2),this._timestamp=t.getUint32(4),this._ssrc=t.getUint32(8),this._csrcs=[];let i=new Uint8Array(12);new DataView(i,0,i.length).setUint8(0,128),e.data.byteLength;let s=12;this._csrc>0&&(this.csrcs.push(t.getUint32(s)),s+=4),1==this._has_extension&&(this._extension=t.getUint16(s),this._ehl=t.getUint16(s+2),s+=4,this.parseExtendtion(e.subarray(s),4*this._ehl),s+=4*this._ehl),this._headerLength=s,this._padding&&t.getUint8(e.byteLength-1),this._data=e.subarray(s)}static createRtpPackage(e){let t=new ArrayBuffer(12),i=new DataView(t,0,t.byteLength),s=e.length+12,n=0;i.setUint8(n,128),n+=1,i.setUint8(n,e.marker<<7&1|127&e.payload),n+=1,i.setUint8(n,e.seqNum>>>8&255),n+=1,i.setUint8(n,255&e.seqNum),n+=1,i.setUint8(n,e.timestamp>>24&255),n+=1,i.setUint8(n,e.timestamp>>16&255),n+=1,i.setUint8(n,e.timestamp>>8&255),n+=1,i.setUint8(n,255&e.timestamp),n+=1,i.setUint8(n,e.ssrc>>24&255),n+=1,i.setUint8(n,e.ssrc>>16&255),n+=1,i.setUint8(n,e.ssrc>>8&255),n+=1,i.setUint8(n,255&e.ssrc),n+=1;let r=new Uint8Array(s);return r.set(new Uint8Array(t),0),r.set(e.data,12),r}getPayload(){return this._data}getTimestampMS(){return this._timestamp}toStringEx(){return"RTP(version:"+this._version+", padding:"+this._padding+", has_extension:"+this._has_extension+", csrc:"+this._csrc+", marker:"+this._marker+", pt:"+this._pt+", sequence:"+this._sequence+", timestamp:"+this._timestamp+", ssrc:"+this._ssrc+")"}toString(){return"RTP(pt:"+this._pt+", sequence:"+this._sequence+", timestamp:"+this._timestamp+",len:"+this._data.byteLength+")"}parseExtendtion(e,t){let i={},s=new DataView(e.buffer,e.byteOffset,e.byteLength),n=0;for(;n<t;){let e=s.getInt8(n),r=s.getInt8(n+1);if(0!=r){if(n+=2,n>=t)break;switch(e){case 1:{let e={bIsKeyFrame:s.getUint16(n+0),nFrameNum:s.getInt32(n+2),nPTS:s.getBigInt64(n+6),nDTS:s.getBigInt64(n+12),nSTS:s.getBigInt64(n+20)};i.GSMediaDataHeader=e;break}case 2:{let e={eFrameType:s.getInt32(n+0),nBlockId:s.getInt16(n+4),nBlockMaxNum:s.getInt16(n+6)};i.GSVideoDataHeader=e;break}}n+=r,r%2!=0&&(n+=r%2)}else n+=1}this.media_info=i}}const it=l("client:rtsp");class st{constructor(e={streamTimeout:30,session:"",playMode:""},t){this._reconnectNums=0,this._isReconnectOpen=!1,this._reconnecting=!1,this._rtpChannel=0,this._reconnectCallback={openSuccess:()=>{},openFailed:()=>{},playSuccess:()=>{},playFailed:()=>{}},this._fnFrameData=(e,t)=>{},this._fnReady=()=>{},this._fnPushPlay=()=>{},this._fnDisconnected=e=>{},this._fnPlayEnd=()=>{},this._fnTearDown=()=>{},this._fnConnectFail=e=>{},this._fnConnectError=e=>{},this._fnSendOption=()=>{},this._fnSendDescribe=()=>{},this._fnSendSetup=()=>{},this._fnSendAnnounce=()=>{},this._options=e,this._rtspSock=new et(t),this._wsStrURL="",this._rtspStrURL="",this._isTearDown=!0,this._isReady=!1,this._sdpParser=null,this._streamSDP=null,this._rtp_streams={},this._sessionID=null,this._fontSession=e.session,this._seqNum=0,this._reconnectNums=0,this._eventBus=t,this._isReconnectOpen=!1,this._reconnecting=!1,this._scale=1,this._heartBeatInterval=null,this._reconnectCallback={openSuccess:()=>{},openFailed:()=>{},playSuccess:()=>{},playFailed:()=>{}},t.listen("rtspReconnect",((e=!1)=>{this._reconnecting||(this._reconnecting=!0,this._isReconnectOpen=e,t.trigger("proxy","getReconnectURL"))})),t.listen("startHeartbeat",(()=>{this._heartBeatInterval=setInterval((()=>{this.sendOptions().catch((e=>{clearInterval(this._heartBeatInterval)})),console.log("heartbeat")}),3e4)})),t.listen("stopHeartbeat",(()=>{clearInterval(this._heartBeatInterval),this._heartBeatInterval=null})),this._fnFrameData=e=>{},this._fnReady=()=>{},this._fnPushPlay=()=>{},this._fnDisconnected=e=>{},this._fnPlayEnd=()=>{},this._fnTearDown=()=>{},this._fnConnectFail=e=>{},this._fnConnectError=e=>{},this._fnSendOption=()=>{},this._fnSendDescribe=()=>{},this._fnSendSetup=()=>{},this._fnSendAnnounce=()=>{},this._rtspSock._fnConnect=()=>{this.onConnected()},this._rtspSock._fnDisconnect=()=>{this.onDisconnect()},this._rtspSock._fnRtpData=e=>{this.onRtpData(e)},this._rtspSock._fnError=e=>{this.onConnectError(e)}}static get streamProtocol(){return"rtsp"}get scale(){return this._scale}async openURL(e,t=!1){return t||(this._reconnectNums=0,this._eventBus.trigger("disReconnect")),await this.openRealPlayURL(e)}async openRealPlayURL(e){this._wsStrURL=String(e);let t=we.parse(e),i=we.parse(this._wsStrURL.replace(t.protocol,"rtsp"));return this._rtspStrURL=i.full,this._isTearDown=!1,this._fnSendOption=()=>this.sendRtspDescribe(),this._fnSendDescribe=()=>this.sendSetup(),await new Promise(((e,t)=>{this._rtspSock&&(this._rtspSock._fnConnect=()=>{this.onConnected(),this.sendOptions().then((()=>this._fnSendOption())).then((()=>this._fnSendDescribe())).then((()=>{this._reconnectNums&&this._reconnectCallback.openSuccess(),e([!0])})).catch((t=>{this._eventBus.trigger("stopHeartbeat"),this._reconnectNums&&this._reconnectCallback.openFailed(),this._eventBus.trigger("reconnect",!0),e([!1,t])}))},this._rtspSock.connect(this._wsStrURL,this._rtspStrURL))}))}async openAudioPlayURL(e){this._wsStrURL=String(e);let t=we.parse(e),i=we.parse(this._wsStrURL.replace(t.protocol,"rtsp"));if(this._rtspStrURL=i.full,this._isTearDown=!1,this._rtspSock)return this._rtspSock.connect(this._wsStrURL),this._fnSendOption=()=>{this.sendAnnounce()},this._fnSendAnnounce=()=>{this.sendPushSetup(this._streamSDP.trackInfos[0].nTrackIndex)},await new Promise(((e,t)=>{this._rtspSock&&(this._rtspSock._fnConnect=()=>{this.sendOptions().then((()=>this._fnSendOption())).then((()=>this._fnSendAnnounce())).then((()=>{e(!0)})).catch((()=>{e(!1)}))})}))}streamSDP(){return this._streamSDP}async play(e,t=!1){if(t||(this._reconnectNums=0,this._eventBus.trigger("disReconnect")),this._eventBus.trigger(""),!this._isReady)return it.warn("play status error:"+this._wsStrURL),!1;let i={FontSession:this._fontSession,Session:this._fontSession||this._sessionID};if(e){if(e.startTime||e.endTime){let t="clock=";t+=e.startTime?e.startTime:"0.0",t+="-",e.endTime&&(t+=e.endTime),i.Range=`${t}`}e.speedScale&&(i.Scale=e.speedScale,this._scale=e.speedScale)}for(let e in this._rtp_streams){this._rtp_streams[e].pause()}this.log("Begin PLAY");return await new Promise(((e,t)=>{this._rtspSock&&this._rtspSock.sendRequest("PLAY",i,this._fontSession,(t=>{try{this.log(`End PLAY ${JSON.stringify(t)}`);let i=ze.parserRtspRtpInfo(t.headers["rtp-info"]),s=ze.parseRtspRange(t.headers.range);for(let e in this._rtp_streams){let t=this._rtp_streams[e],n=t.getSetupURL(this._rtspStrURL);if(i.hasOwnProperty(n)){let e=i[n];t.play(s.startTime,e.seq,e.rtptime)}else it.error(`Play response error: track url err, track:${n}`),console.error(i)}this._reconnectNums&&this._reconnectCallback.playSuccess(),it.debug("Play OK:"+this._wsStrURL),e(!0),this._reconnectNums=0}catch(t){e(!1)}}),(t=>{try{this._reconnectNums&&this._reconnectCallback.playFailed(),this._eventBus.trigger("reconnect"),it.error("Play fail:"+this._wsStrURL+"\nerror:"+t),this.log("Play fail:"+this._wsStrURL+"\nerror:"+t),e(!1)}catch(t){e(!1)}}),this._rtspStrURL)}))}playPushAudio(e){if(!this._isReady)return it.warn("play status error:"+this._wsStrURL),!1;let t={FontSession:this._fontSession,Session:this._fontSession||this._sessionID};if(e){if(e.startTime||e.endTime){let i="clock=";i+=e.startTime?e.startTime:"0.0",i+="-",e.endTime&&(i+=e.endTime),t.Range=`${i}`}e.speedScale&&(t.Scale=e.speedScale)}for(let e in this._rtp_streams){this._rtp_streams[e].pause()}return this._rtspSock?(this._rtspSock.sendRequest("PLAY",t,void 0,(e=>{this._fnPushPlay(),it.debug("Play OK:"+this._wsStrURL)}),(e=>{it.error("Play fail:"+this._wsStrURL+"\nerror:"+e)}),this._rtspStrURL),!0):void 0}pause(){let e={};if(this._sessionID&&(e.Session=this._sessionID),this._rtspSock)return this._rtspSock.sendRequest("PAUSE",e,"",(e=>{}),(e=>{this._rtspSock&&this._rtspSock.disconnect()}),this._rtspStrURL),!1}tearDown(e=!1){this._eventBus.trigger("stopHeartbeat"),e||this._eventBus.trigger("disReconnect"),this._isReady&&!this._isTearDown||this._fnTearDown();let t={FontSession:this._fontSession,Session:this._fontSession||this._sessionID};return this.log("Begin teardown"),new Promise(((e,i)=>{this._rtspSock&&this._rtspSock.sendRequest("TEARDOWN",t,this._fontSession,(t=>{try{if(this.log(`End teardown ${JSON.stringify(t)}`),this._isTearDown||this._fnTearDown(),!this._rtspSock)return;this._rtspSock.disconnect(),this._isTearDown=!0,this.clearResource(),e(!0)}catch(e){i()}}),(e=>{if(this._rtspSock)try{it.error(`failed teardown ${JSON.stringify(e)}`),this.log(`failed teardown ${JSON.stringify(e)}`),this._isTearDown||this._fnTearDown(),this._rtspSock.disconnect(),this._isTearDown=!0,this.clearResource(),i()}catch(e){this._rtspSock.disconnect(),i()}}),this._rtspStrURL)}))}onConnected(){it.info("Connect success:"+this._wsStrURL),this._eventBus.trigger("startHeartbeat")}onDisconnect(){this._eventBus.trigger("stopHeartbeat"),this._isReady?(this._isReady=!1,this._fnDisconnected()):(this._fnConnectFail(),it.error("Connect fail:"+this._wsStrURL))}sendOptions(){it.debug(`Begin Option: ${this._wsStrURL}`),this.log(`Begin Option: ${this._wsStrURL}`);const e={FontSession:this._fontSession,Session:this._fontSession||this._sessionID};return new Promise(((t,i)=>{this._rtspSock&&this._rtspSock._ws||this._eventBus.trigger("stopHeartbeat"),console.log("send Option",new Date),this._rtspSock&&this._rtspSock.sendRequest("OPTIONS",e,this._fontSession,(e=>{try{if(it.debug(`End Send Option: ${JSON.stringify(e)}`),!e.headers.public)return void i("OPTIONS PUBLIC IS NONE");this.methods=e.headers.public.split(",").map((e=>e.trim())),it.debug(`End Send Option: ${this._wsStrURL}`),t(!0)}catch(e){i()}}),(e=>{if(this._rtspSock)try{this.log(`OPTIONS err ${JSON.stringify(e)} ${this._wsStrURL}`),it.error(`OPTIONS err ${e} ${this._wsStrURL}`),this._rtspSock.disconnect(),this._fnConnectFail(e),i(e)}catch(e){i(e)}}),this._rtspStrURL)}))}sendAnnounce(){this._sdpParser=new Ce;let e=this._sdpParser.getAudioSDPInfo(0),t=this._sdpParser.getAudioSDPInfoString(0),i=e.trackInfos[0];i.nTrackIndex=0;let s=new je(this,i);return this._rtp_streams[0]=s,this._streamSDP=e,it.debug(`Begin Announce: ${this._wsStrURL}`),new Promise(((e,i)=>{this._rtspSock&&this._rtspSock.sendRequest("ANNOUNCE",{"Content-type":"application/sdp"},t,(t=>{it.debug(`End Announce: ${this._wsStrURL}`),e(!0)}),(e=>{it.error(`OPTIONS err ${e} ${this._wsStrURL}`),this._rtspSock&&(this._rtspSock.disconnect(),this._fnConnectFail(e),i(e))}),this._rtspStrURL)}))}sendPushSetup(e=0){let t=this._streamSDP,i=this._rtp_streams[e],s=t.trackInfos[e];return it.debug(`Begin Setup ${this._wsStrURL}`),new Promise(((n,r)=>{this._rtspSock&&this._rtspSock.sendRequest("SETUP",i.getSetupParam(),"",(i=>{try{if(this.onSetupReady(i),this._fnSendSetup(),it.debug(`Track  ${s.strTrackName} Setup complete.`),!(++e>=t.trackInfos.length))return this.sendPushSetup(e);it.info(`Setup Completed ${this._wsStrURL}`),this._isReady=!0,this._fnReady(),n(!0)}catch(e){r()}}),(e=>{if(this._rtspSock)try{it.error(`Setup stream err ${e}  trackID: ${s.strTrackName} `),this._rtspSock.disconnect(),this._fnConnectFail(e),r()}catch(e){r()}}),i.getSetupURL(this._rtspStrURL))}))}sendRtspDescribe(){return it.debug(`Begin describe: ${this._wsStrURL}`),this.log(`Begin Send DESCRIBE: ${this._wsStrURL}`),new Promise(((e,t)=>{this._rtspSock&&this._rtspSock.sendRequest("DESCRIBE",{Accept:"application/sdp",FontSession:this._fontSession,Session:this._fontSession||this._sessionID},this._fontSession,(i=>{try{this.log(`End Send DESCRIBE: ${JSON.stringify(i)}`),this._sdpParser=new Ce,this._sdpParser.parse(i.body).then((()=>{it.debug(`Rcv SDP: ${i.body} ${this._wsStrURL}`),this.onDescribeReady(),e(!0)})).catch((e=>{this._rtspSock&&(it.error(`Parser fail SDP: ${i.body} ${this._wsStrURL} error:${e}`),this._rtspSock.disconnect(),this._fnConnectFail(new Qe("InvalidSDP","ParserSDP","Parser sdp fail",i)),t())}))}catch(e){t()}}),(e=>{try{if(this.log(`Get describe fail err ${JSON.stringify(e)} ${this._wsStrURL}`),it.error(`Get describe fail err ${e} ${this._wsStrURL}`),!this._rtspSock)return;this._rtspSock.disconnect(),this._fnConnectFail(e),t(e)}catch(e){t(e)}}),this._rtspStrURL)}))}onDescribeReady(){it.info("Rtsp "+this._wsStrURL+" ready");try{if(this._streamSDP=this._sdpParser.getSDPInfo(),!this._streamSDP)throw new Qe("DESCRIBE",`Describe parse sdp fail: ${this._streamSDP}`)}catch(e){if(it.error(`Setup fail ${this._wsStrURL}`),!this._rtspSock)return;this._rtspSock.disconnect(),this._fnConnectFail(new Qe("SETUP","Send setup command fail.",""))}}sendSetup(e=0){let t=this._sdpParser.getSDPInfo(),i=t.trackInfos[e];if(!i)throw"获取音视频信息异常，请检查该设备是否能正常播放！";i.nTrackIndex=e;let s=new je(this,i);if(!s.init())throw new Qe("SETUP","Send setup command fail.","");return this._rtp_streams[e]=s,this.log("Begin Send SETUP"),new Promise(((n,r)=>{this._rtspSock&&this._rtspSock.sendRequest("SETUP",{...s.getSetupParam(),FontSession:this._fontSession,Session:this._fontSession||this._sessionID},this._fontSession,(async s=>{this.log(`End SETUP ${JSON.stringify(s)}`);try{this.onSetupReady(s),it.debug(`Track  ${i.strTrackName} Setup complete.`),++e>=t.trackInfos.length?(it.info(`Setup Completed ${this._wsStrURL}`),this._isReady=!0,this._fnReady(),n(!0)):n(await this.sendSetup(e))}catch(e){this.log(`Setup stream err ${e} `),r()}}),(e=>{try{if(it.error(`Setup stream err ${e}  trackID: ${i.strTrackName} `),this.log(`Setup stream err ${e}  trackID: ${i.strTrackName} `),!this._rtspSock)return;this._rtspSock.disconnect(),this._fnConnectFail(e),r()}catch(e){r()}}),s.getSetupURL(this._rtspStrURL))}))}onSetupReady(e){console.debug("Setup Ready: "+e);let t=e.headers.session;this._eventBus.trigger("session",t);let i=e.headers.transport;if(i){let e=i.match(/interleaved=([0-9]+)-([0-9]+)/)[1];e&&(this._rtpChannel=Number(e))}if(t){let e=t.split(";").slice(1),i={};for(let t of e){let e=t.split("=");i[e[0]]=e[1]}i.timeout&&(this._keepaliveInterval=1e3*Number(i.timeout));let s=t.split(";")[0];t&&(this._sessionID=s)}}pushData(e){if(e){let t=this._streamSDP.trackInfos[0].nTrackIndex,i=e.data.byteLength,s=new Uint8Array(e.data.buffer),n=0,r=1400,o=!1;for(;n<=i;){let a=n+r;n+r>i&&(a=i,o=!0);let h=s.subarray(n,a);this._seqNum+=1;let l={marker:o?1:0,data:h,length:h.byteLength,payload:this._rtp_streams[t].getPayloadType(),timestamp:1*e.duration/e.sampleRate,seqNum:this._seqNum,ssrc:0},u=tt.createRtpPackage(l),c=u.byteLength,d=new ArrayBuffer(4),p=new DataView(d,0,d.byteLength);p.setUint8(0,255&"$".charCodeAt(0)),p.setUint8(1,0),p.setUint8(2,c>>8&255),p.setUint8(3,255&c);let m=new Uint8Array(c+4);if(m.set(new Uint8Array(d),0),m.set(u,4),!this._rtspSock)return;this._rtspSock.sendRtpPacket(m),n+=r}}}onRtpData(e){let t=0|e[1],i=t/2|0;t%2==0?this._rtp_streams[i].onRtpPacketMessage(e.subarray(4)):this._rtp_streams[i].onRtcpPacketMessage(e.subarray(4))}onConnectError(e){this._rtspSock&&(this._rtspSock.disconnect(e),this._fnConnectError(e))}clearResource(){for(let e in this._rtp_streams)this._rtp_streams[e]&&this._rtp_streams[e].clearPackets();this._rtp_streams={},this._rtspSock=null,this._streamSDP=null,this._eventBus.trigger("stopHeartbeat")}log(e){this._eventBus.trigger("log",e)}}class nt extends le{constructor(e,t=new Z){var i;super(nt.PipeName,t,e.session),this._videoOutputPin=null,this._audioOutputPin=null,this._rtspClient=void 0,this._sourceURL=void 0,this._startTime=-1,this._lastStatus=V.NONE,this._timeout=0;let s={streamTimeout:30,playMode:"realplay",session:this.session||""};this._rtspClient=new st(s,t),this._sourceURL=e.sourceURL,this._timeout=e.timeout||5,null===(i=this._eventBus)||void 0===i||i.listen("reconnectSuccess",(()=>{this.startListenTimeout("reconnect")}))}async onStatusChangeRequest(e){switch(e){case V.READY:return await new Promise((e=>{this.open(e)}));case V.PLAY:return await new Promise((e=>{this.play(e)}));case V.PAUSE:this._rtspClient&&this._rtspClient.pause(),this._lastStatus=V.PAUSE,this.stopListenTimeout();break;case V.CLOSE:return new Promise((e=>{this.teardown(e),this.stopListenTimeout()}))}return K.OK}stopListenTimeout(){this._timeoutObj&&clearTimeout(this._timeoutObj),this._timeoutObj=null}timeoutFunc(e){var t;this.currentStatus==V.PLAY&&(null===(t=this._eventBus)||void 0===t||t.trigger("rtspTimeout",e)),this.stopListenTimeout()}startListenTimeout(e){this.stopListenTimeout(),this._timeoutObj=setTimeout(this.timeoutFunc.bind(this,e),1e3*this._timeout)}onAttributeChanged(e){if(console.log(e.key),e.key==W.CURRENT_TIME){let t=e.value;if(this.currentStatus==V.PLAY)return this._rtspClient&&this._rtspClient.play({startTime:t})?K.OK:K.ERROR;this._startTime=t}else if(e.key==W.RATE){let t=e.value;return console.log("set rtsp client rate: "+t),this._rtspClient&&this._rtspClient.scale===t||this._rtspClient&&this._rtspClient.play({speedScale:t})?K.OK:K.ERROR}return K.OK}async open(e){if(this._rtspClient&&(this._rtspClient._fnReady=()=>{var t;let i=null===(t=this._rtspClient)||void 0===t?void 0:t.streamSDP();this.updateOutputSdpInfo(i.trackInfos).then((t=>{for(let e of this.outBinders)e.sdpInfo.eMediaType==k.GS_AV_TYPE_VIDEO&&(this._videoOutputPin=e),e.sdpInfo.eMediaType==k.GS_AV_TYPE_AUDIO&&(this._audioOutputPin=e);e(t)}))}),this._sourceURL&&this._rtspClient){const[t,i]=await this._rtspClient.openURL(this._sourceURL);t||(console.warn(`rtsp client openURL fail: ${this._sourceURL}`),e(i||K.ERROR))}}async play(e){if(this._lastStatus==V.PAUSE)return this.resumePlay(e);if(this._rtspClient){let t;this._rtspClient._fnFrameData=(e,t)=>{this.onReceiveFrame(e,t)},this._rtspClient._fnConnectError=e=>{var t;null===(t=this._eventBus)||void 0===t||t.trigger(x.CONNECT_ABORT,e+", "+(new Date).toString())},-1!=this._startTime&&(t={startTime:this._startTime},this._startTime=-1);await this._rtspClient.play(t)?(this.startListenTimeout("play"),e(K.OK),this._lastStatus=V.PLAY):e(K.ERROR)}}resumePlay(e){let t;return this._rtspClient&&this._rtspClient.play(t)?(this.startListenTimeout("resumePlay"),e(K.OK)):e(K.ERROR)}teardown(e){let t=new Promise((e=>{this._rtspClient&&(this._rtspClient._fnTearDown=()=>{e(K.OK)})})),i=new Promise((e=>{this._rtspClient&&(this._rtspClient._fnDisconnected=()=>{e(K.OK)})}));return Promise.race([t,i]).then((()=>{this._rtspClient=void 0,this._videoOutputPin=null,this._audioOutputPin=null})),this._rtspClient&&this._rtspClient.tearDown(),this._sourceURL=void 0,this._lastStatus=V.NONE,this._startTime=-1,e(K.OK),K.OK}onReceiveFrame(e,t){this.startListenTimeout("receiveFrame"),t&&t.aBuffer&&0!=this.outBinders.length&&(e.eMediaType==k.GS_AV_TYPE_VIDEO&&this._videoOutputPin&&this.sendDataToNextPipe(t,this._videoOutputPin),e.eMediaType==k.GS_AV_TYPE_AUDIO&&this._audioOutputPin&&this.sendDataToNextPipe(t,this._audioOutputPin))}}function rt(e,t){let i=0;for(var s=0;s<e;s++)i<<=1,t.data[Math.floor(t.index/8)]&128>>t.index%8&&(i+=1),t.index++;return i}function ot(e,t){let i=0;for(;e.index<8*t&&!(e.data[Math.floor(e.index/8)]&128>>e.index%8);)i++,e.index++;e.index++;let s=0;for(let t=0;t<i;t++)s<<=1,e.data[Math.floor(e.index/8)]&128>>e.index%8&&(s+=1),e.index++;return(1<<i)-1+s}function at(e,t){let i=ot(e,t),s=(n=i/2,Math.ceil(n));var n;return i%2==0&&(s=-s),s}function ht(e){let t,i=(e=e.slice(4)).length,s={data:e,index:0};if(rt(1,s),rt(2,s),7==rt(5,s)){let e=rt(8,s);if(rt(1,s),rt(1,s),rt(1,s),rt(1,s),rt(1,s),rt(1,s),rt(2,s),rt(8,s),ot(s,i),100==e||110==e||122==e||144==e){t=ot(s,i),3==t&&rt(1,s),ot(s,i),ot(s,i),rt(1,s);let e=rt(1,s),r=new Uint8Array(8);if(e)for(var n=0;n<8;n++)r[n]=rt(1,s)}ot(s,i);let r=ot(s,i);if(0==r)ot(s,i);else if(1==r){rt(1,s),at(s,i),at(s,i);let e=ot(s,i),t=new Uint8Array[e];for(n=0;n<e;n++)t[n]=at(s,i)}ot(s,i),rt(1,s);let o=16*(ot(s,i)+1),a=16*(ot(s,i)+1),h=rt(1,s);if(h||rt(1,s),rt(1,s),rt(1,s)){let e=1,n=2-h;1==t?(e=2,n=2*(2-h)):2==t&&(e=2,n=2-h),o-=e*(ot(s,i)+ot(s,i)),a-=n*(ot(s,i)+ot(s,i))}return{width:o,height:a}}}var lt,ut,ct,dt,pt,mt,gt,_t,ft;nt.PipeName="RTSPClientPipe",function(e){e[e.SLICE=1]="SLICE",e[e.DPA=2]="DPA",e[e.DPB=3]="DPB",e[e.DPC=4]="DPC",e[e.IDR=5]="IDR",e[e.SEI=6]="SEI",e[e.SPS=7]="SPS",e[e.PPS=8]="PPS",e[e.AUD=9]="AUD",e[e.EOSEQ=10]="EOSEQ",e[e.EOSTREAM=11]="EOSTREAM",e[e.FILL=12]="FILL",e[e.MAX=12]="MAX"}(lt||(lt={}));class St{constructor(e){this.forbidden_zero_bit=128&e,this.nal_ref_idc=96&e,this.nal_unit_type=31&e}}class yt extends le{constructor(e){super(yt.PipeName,e),this._sdpInfo=new te,this._initFlags=0,this._spsData=void 0,this._ppsData=void 0,this._spsInit=!1,this._lastFrame=null,this._firstDuration=0,this._spsData=new oe,this._spsData.setKeyFlag(),this._spsData.eFrameType=M.GS_AV_FRAME_VIDEO_SPS,this._ppsData=new oe,this._ppsData.setKeyFlag(),this._ppsData.eFrameType=M.GS_AV_FRAME_VIDEO_PPS}async onStatusChangeRequest(e){switch(e){case V.READY:case V.PLAY:case V.PAUSE:break;case V.CLOSE:return await this._close()}return K.OK}async _close(){return this._ppsData=void 0,this._spsData=void 0,this._sdpInfo=void 0,this._initFlags=0,K.OK}onIncomingPipeRequest(e,t){return e.eMediaType!=k.GS_AV_TYPE_VIDEO||e.eCodeID!=G.GS_CODEID_ST_H264?K.ERROR:(this._sdpInfo=e,this._sdpInfo.nWidth=1920,this._sdpInfo.nHeight=1080,K.OK)}async onIncomingDataFromPrev(e,t){if(e.eMediaType!=k.GS_AV_TYPE_VIDEO)return $.ERROR;if(e.eCodeID!=G.GS_CODEID_ST_H264||null==t.aBuffer)return $.ERROR;let i,s=$.OK,n=new DataView(t.aBuffer.buffer,t.aBuffer.byteOffset),r=0,o=0,a=0,h=new Array;for(;;){if(r>=n.byteLength-yt.STARTCODE_MAX_SIZE){i&&(i.aBuffer=t.aBuffer.subarray(o,n.byteLength),this._eventBus&&i.eFrameType==M.GS_AV_FRAME_VIDEO_SEI&&this._eventBus.trigger("sei",i),await this.updateSdp(i),h.push(i));break}let e=n.getUint32(r,!1);if(e==yt.H264_START_CODE||e>>8==yt.H264_START_CODE){a=e==yt.H264_START_CODE?4:3,i&&(i.aBuffer=t.aBuffer.subarray(o,r),this._eventBus&&i.eFrameType==M.GS_AV_FRAME_VIDEO_SEI&&this._eventBus.trigger("sei",i),await this.updateSdp(i),h.push(i)),r+=a,o=r;let l=n.getUint8(r);r+=1;let u=new St(l);if(0!=u.forbidden_zero_bit||u.nal_unit_type>lt.MAX){console.error("h264 data error: nalu "+l),s=$.ERROR;break}switch(i=new oe,u.nal_unit_type){case lt.SPS:i.eFrameType=M.GS_AV_FRAME_VIDEO_SPS,i.setKeyFlag(),this._spsData=i;break;case lt.PPS:i.eFrameType=M.GS_AV_FRAME_VIDEO_PPS,i.setKeyFlag(),this._ppsData=i;break;case lt.IDR:i.eFrameType=M.GS_AV_FRAME_VIDEO_I,i.setKeyFlag();break;case lt.SLICE:i.eFrameType=M.GS_AV_FRAME_VIDEO_P;break;case lt.SEI:i.eFrameType=M.GS_AV_FRAME_VIDEO_SEI}if(i){if(i.nSeq=t.nSeq,i.nPTS=t.nPTS,i.nDTS=t.nDTS,i.nSTS=t.nSTS,i.nDuration=t.nDuration,i.nTimestamp=t.nTimestamp,u.nal_unit_type==lt.IDR){if(i.aBuffer=t.aBuffer.subarray(o,t.aBuffer.length),this._sdpInfo&&!this._spsInit&&this._spsData&&this._spsData.aBuffer){const{width:e,height:t}=ht(Array.prototype.slice.call(new Uint8Array(this._spsData.aBuffer.buffer)));this._sdpInfo.nWidth=e,this._sdpInfo.nHeight=t}await this.updateSdp(i),h.push(i);break}if(u.nal_unit_type==lt.SLICE){i.aBuffer=t.aBuffer.subarray(o,t.aBuffer.length),h.push(i);break}}}else r+=1}return s!=$.OK&&console.error("pipeName:["+yt.PipeName+"]: sendDate error; date: "+(new Date).toString()),s=await this.sendData(h),s!=$.OK&&console.error("pipeName:["+yt.PipeName+"]: sendDate error; date: "+(new Date).toString()),i=void 0,h=[],n=void 0,s}setLengthByte(e,t,i){const s=e-4;i[0+t]=s>>24&255,i[1+t]=s>>16&255,i[2+t]=s>>8&255,i[3+t]=255&s}createMdatBuffer(e){let t=0,i=0,s=e.byteLength+4;this._spsData&&this._spsData.aBuffer&&(t=this._spsData.aBuffer.byteLength+4),this._ppsData&&this._ppsData.aBuffer&&(i=this._ppsData.aBuffer.byteLength+4);const n=new Uint8Array(t+i+s);return this.setLengthByte(t,0,n),this.setLengthByte(i,t,n),this.setLengthByte(s,t+i,n),this._spsData&&this._spsData.aBuffer&&n.set(this._spsData.aBuffer,4),this._ppsData&&this._ppsData.aBuffer&&n.set(this._ppsData.aBuffer,t+4),n.set(e,t+i+4),n}async sendData(e){let t=$.OK;if(this.hasReceiveSPSPPS())for(let i of e)if(i.eFrameType==M.GS_AV_FRAME_VIDEO_I||i.eFrameType==M.GS_AV_FRAME_VIDEO_P)if(null!=i.aBuffer){if(i.eFrameType==M.GS_AV_FRAME_VIDEO_I)i.aBuffer=this.createMdatBuffer(i.aBuffer);else{let e=new Uint8Array(i.aBuffer.byteLength+4),t=i.aBuffer.byteLength;e[0]=t>>24&255,e[1]=t>>16&255,e[2]=t>>8&255,e[3]=255&t,e.set(i.aBuffer,4),i.aBuffer=e}for(let e of this.outBinders)e.sdpInfo.isEnable&&(this._lastFrame&&(this._lastFrame.nTimestamp?this._lastFrame.nDuration=i.nTimestamp-this._lastFrame.nTimestamp:this._lastFrame.nDuration=this._firstDuration||i.nTimestamp,this._firstDuration=this._lastFrame.nDuration,this._spsInit?t=await this.sendDataToNextPipe(this._lastFrame,e):(this._spsInit=!0,t=await this.sendDataToNextPipe(this._lastFrame,e,this._sdpInfo))),this._lastFrame=i)}else t=$.ERROR,console.error("pipeName:["+yt.PipeName+"]: sendDate frame.aBuffer is null; date: "+(new Date).toString());return t}async updateSdp(e){return!this.hasReceiveSPSPPS()&&this._sdpInfo&&(e.eFrameType==M.GS_AV_FRAME_VIDEO_SPS&&(this._sdpInfo.aSPS=e.aBuffer),e.eFrameType==M.GS_AV_FRAME_VIDEO_PPS&&(this._sdpInfo.aPPS=e.aBuffer),this._initFlags|=1<<e.eFrameType,this.hasReceiveSPSPPS())?this.updateOutputSdpInfo([this._sdpInfo]):K.OK}hasReceiveSPSPPS(){return(this._initFlags&yt.INIT_FLAG)==yt.INIT_FLAG}}yt.PipeName="H264Analyzer",yt.STARTCODE_MAX_SIZE=4,yt.H264_START_CODE=1,yt.INIT_FLAG=1<<M.GS_AV_FRAME_VIDEO_SPS|1<<M.GS_AV_FRAME_VIDEO_PPS;class vt extends le{constructor(e){super(vt.PipeName,e),this._isSending=!1,this._dataArray=new Array,this._lastState=V.NONE,this._sdpInfo=null}onAttributeChanged(e){return e.key==W.CURRENT_TIME&&this._lastState!=V.PAUSE&&(console.debug("gsp_data_cache::clearBuffer::dataLength: "+this._dataArray.length+"; lastStatus: "+this._lastState),this._clearBuffer()),K.OK}onIncomingPipeRequest(e,t){this._sdpInfo=e;const i=Object.assign({},this._sdpInfo);return i.sinkPipeName="RecordPipe",this.updateOutputSdpInfo([e,i]),K.OK}async onStatusChangeRequest(e){switch(e){case V.READY:return super.onStatusChangeRequest(e);case V.PAUSE:this._lastState=V.PAUSE;break;case V.PLAY:this._lastState=V.PLAY;break;case V.CLOSE:return this._clearBuffer()}return K.OK}async onIncomingDataFromPrev(e,t){if(this._dataArray.unshift(t),!this._isSending&&this.currentStatus==V.PLAY){for(this._isSending=!0;this._dataArray.length>0;){let e=this._dataArray.pop();if(e)for(let t of this.outBinders)if(t.sdpInfo.isEnable){await this.sendDataToNextPipe(e,t)!=$.OK&&console.error("pipeName:["+vt.PipeName+"]: sendDate error; date: "+(new Date).toUTCString())}}this._isSending=!1}return $.OK}_clearBuffer(){return this._dataArray=[],this._isSending=!1,K.OK}}vt.PipeName="DataCache";class wt extends le{constructor(e){super(wt.PipeName,e)}async onStatusChangeRequest(e){return K.OK}onIncomingPipeRequest(e,t){return K.OK}async onIncomingDataFromPrev(e,t){return $.OK}}wt.PipeName="MSENull";class Tt extends le{constructor(e,t){var i,s;super(Tt.PipeName,t),this._inSDP=null,this._audioCtx=void 0,this._gainNode=void 0,this._targetInput=null,this._startTime=0,this._maxValue=0,this._targetInputId=void 0,this._throwDataLen=3,this._isThrowData=!1,this._notifyThrowDataTime=0,this.sign=!1,this._samples=new Float32Array,this.sign=!1,e&&(this._targetInput=document.createElement("INPUT"),this._targetInput.type="range",this._targetInput.min="0",this._targetInput.max="1",this._targetInput.step="0.01",this._targetInput.defaultValue="0",this._targetInput.onchange=()=>{this._gainNode&&(this._gainNode.gain.value=+this._targetInput.value)},null===(i=document.getElementById(e))||void 0===i||i.appendChild(this._targetInput),this._targetInputId=e),null===(s=this._eventBus)||void 0===s||s.listen("setVolume",(e=>{void 0!==e&&(console.log(`set volume ${e}`),this._gainNode&&this._gainNode.gain.setValueAtTime(e/100,this._audioCtx.currentTime+1))})),this._close()}async onStatusChangeRequest(e){switch(e){case V.READY:return await this._open();case V.PLAY:break;case V.CLOSE:return await this._close()}return K.OK}onIncomingPipeRequest(e,t){return e.eMediaType==k.GS_AV_TYPE_AUDIO&&(this._inSDP=e,this._maxValue=this._getMaxValue(),console.debug("SDP: channels: "+this._inSDP.nChannels,"Samplerate: "+this._inSDP.nSamplerate,"sampleBits: "+this._inSDP.nSampleBits,"sampleformat: "+this._inSDP.eSampleFormat)),K.OK}onAttributeChanged(e){return K.OK}async onIncomingDataFromPrev(e,t){if(!t||!t.aBuffer||0==t.aBuffer.byteLength||this._maxValue<=0)return $.ERROR;if(this._isThrowData){if(!(this._audioCtx&&this._startTime<this._audioCtx.currentTime+.1))return $.OK;this._isThrowData=!1}let i=this._createTypedArray(t.aBuffer.buffer,t.aBuffer.byteOffset);if(!i)return $.ERROR;let s=new Float32Array(i.length);for(let e=0;e<i.length;e++)s[e]=i[e]/this._maxValue;let n=new Float32Array(this._samples.length+s.length);return this._samples.length>0&&n.set(this._samples,0),n.set(s,this._samples.length),this._samples=n,this._samples.length>=640&&this._flush(),i=null,n=null,s=null,$.OK}async _open(){return this.log("--------------------------------开启音频------------------------------"),this._audioCtx=new window.AudioContext,this._gainNode=this._audioCtx.createGain(),this._targetInput?this._gainNode.gain.value=+this._targetInput.value:this._gainNode.gain.value=0,this._gainNode.connect(this._audioCtx.destination),this._startTime=this._audioCtx.currentTime,K.OK}async _close(){var e;return console.log("close"),this._gainNode&&(this._gainNode.disconnect(),this._gainNode=void 0),this._audioCtx&&(this._audioCtx.close(),this._audioCtx=void 0),this._samples=new Float32Array(0),this._inSDP=null,this._maxValue=0,this._startTime=0,this._targetInputId&&this._targetInput&&(null===(e=document.getElementById(this._targetInputId))||void 0===e||e.removeChild(this._targetInput)),this._targetInputId=void 0,this._targetInput=null,K.OK}_flush(){if(!(this._inSDP&&this._samples.length&&this._audioCtx&&this._gainNode))return;let e=this._audioCtx.createBufferSource(),t=this._samples.length/this._inSDP.nChannels,i=this._audioCtx.createBuffer(this._inSDP.nChannels,t,this._inSDP.nSamplerate),s=null,n=0;for(let e=0;e<this._inSDP.nChannels;e++){s=i.getChannelData(e),n=e;for(let e=0;e<t;e++)s[e]=this._samples[n],n+=this._inSDP.nChannels}this._startTime<this._audioCtx.currentTime&&(this._startTime=this._audioCtx.currentTime),e.onended=e=>{let t=e.currentTarget;t&&(t.buffer=null,t.stop(),t.disconnect(),t=null)},e.buffer=i,e.connect(this._gainNode),e.start(this._startTime),this._startTime-this._audioCtx.currentTime>this._throwDataLen&&(this._isThrowData=!0,this._notifyThrowDataTime++%10==0&&console.error("audio data cache for "+this._throwDataLen+"s,please check for audio data, starttime: "+this._startTime,"current play time: "+this._audioCtx.currentTime,"diff: "+(this._startTime-this._audioCtx.currentTime),"duration: "+i.duration)),this._startTime+=i.duration,this._samples=new Float32Array,n=0,s=null,i=null,e=null}_createTypedArray(e,t){return this._inSDP?this._inSDP.eSampleFormat==N.GS_AV_SAMPLE_FMT_U8?new Int8Array(e,t):this._inSDP.eSampleFormat==N.GS_AV_SAMPLE_FMT_S16?new Int16Array(e,t):this._inSDP.eSampleFormat==N.GS_AV_SAMPLE_FMT_S32?new Int32Array(e,t):this._inSDP.eSampleFormat==N.GS_AV_SAMPLE_FMT_FLT?new Float32Array(e,t):null:null}_getMaxValue(){return this._inSDP?this._inSDP.eSampleFormat==N.GS_AV_SAMPLE_FMT_U8?128:this._inSDP.eSampleFormat==N.GS_AV_SAMPLE_FMT_S16?32768:this._inSDP.eSampleFormat==N.GS_AV_SAMPLE_FMT_S32?2147483648:this._inSDP.eSampleFormat==N.GS_AV_SAMPLE_FMT_FLT?1:-1:-1}}Tt.PipeName="PCMPlayerPipe";class It extends le{constructor(e,t){super(It.PipeName,t),this._dataBufs=null,this._offset=0,this._seq=0,this._frameSize=1400,this._startTS=0,this._totalReadSize=0,this._audioSdp=new ee,this._fileURL=e,""==this._fileURL&&(this._fileURL="../data/L16.pcm")}async onStatusChangeRequest(e){switch(e){case V.READY:return new Promise((e=>{this.openFile(this._fileURL,e)}));case V.PLAY:if(null==this._dataBufs)return K.ERROR;this._startTS=(new Date).getTime(),this.beginSendFrame();case V.CLOSE:}return K.OK}onIncomingPipeRequest(e,t){return K.ERROR}openFile(e,t){let i=new XMLHttpRequest;i.open("GET",e),i.responseType="arraybuffer",i.onload=s=>{200!=i.status&&(console.warn("Unexpected status code "+i.status+" for "+e),t(K.ERROR)),this._dataBufs=new Uint8Array(i.response),console.log("load local [%s] file done.",e);let n=this.initSDP(),r=this.updateOutputSdpInfo(n);t(r)},i.send()}beginSendFrame(){if(!this._dataBufs)return;let e=new ae;e.nSeq=this._seq++,e.aBuffer=this._dataBufs.slice(this._offset,this._offset+this._frameSize),this._offset+=this._frameSize,this._offset>=this._dataBufs.length&&(this._offset=0),this._totalReadSize=this._totalReadSize+this._frameSize;let t=1e3*this._totalReadSize/(this._audioSdp.nSamplerate*this._audioSdp.nChannels*this._audioSdp.nSampleBits/8),i=this._startTS+t-(new Date).getTime();i<0&&(i=0),setTimeout((()=>{this.sendDataToNextPipe(e,this.outBinders[0]),this.beginSendFrame()}),i)}initSDP(){return this._audioSdp=new ee,this._audioSdp.eMediaType=k.GS_AV_TYPE_AUDIO,this._audioSdp.eCodeID=G.GS_CODEID_AUDIO_ST_PCM,this._audioSdp.ePacketType=F.GS_AV_PACKET_TYPE_ES,this._audioSdp.nTrackIndex=0,this._audioSdp.strTrackName="audio_track1",this._audioSdp.nSampleBits=16,this._audioSdp.nSamplerate=8e3,this._audioSdp.nChannels=1,this._audioSdp.eSampleFormat=N.GS_AV_SAMPLE_FMT_S16,[this._audioSdp]}}It.PipeName="PCMFileSourcePipe",function(e){e[e.TRAIL_N=0]="TRAIL_N",e[e.TRAIL_R=1]="TRAIL_R",e[e.TSA_N=2]="TSA_N",e[e.TSA_R=3]="TSA_R",e[e.STSA_N=4]="STSA_N",e[e.STSA_R=5]="STSA_R",e[e.RADL_N=6]="RADL_N",e[e.RADL_R=7]="RADL_R",e[e.RASL_N=8]="RASL_N",e[e.RASL_R=9]="RASL_R",e[e.BLA_W_LP=16]="BLA_W_LP",e[e.BLA_W_RADL=17]="BLA_W_RADL",e[e.BLA_N_LP=18]="BLA_N_LP",e[e.IDR_W_RADL=19]="IDR_W_RADL",e[e.IDR_N_LP=20]="IDR_N_LP",e[e.CRA=21]="CRA",e[e.VPS=32]="VPS",e[e.SPS=33]="SPS",e[e.PPS=34]="PPS",e[e.AUD=35]="AUD",e[e.EOS=36]="EOS",e[e.EOB=37]="EOB",e[e.FD=38]="FD",e[e.SEI=39]="SEI",e[e.SEI_1=40]="SEI_1",e[e.MAX=41]="MAX"}(ut||(ut={}));class Et{constructor(e){this.forbidden_zero_bit=128&e,this.nal_unit_type=(126&e)>>1}}class Rt extends le{constructor(e){super(Rt.PipeName,e),this._sdpInfo=new ie,this._initFlags=0,this._vpsData=void 0,this._spsData=void 0,this._ppsData=void 0,this._vpsData=new oe,this._vpsData.setKeyFlag(),this._vpsData.eFrameType=M.GS_AV_FRAME_VIDEO_VPS,this._spsData=new oe,this._spsData.setKeyFlag(),this._spsData.eFrameType=M.GS_AV_FRAME_VIDEO_SPS,this._ppsData=new oe,this._ppsData.setKeyFlag(),this._ppsData.eFrameType=M.GS_AV_FRAME_VIDEO_PPS}async onStatusChangeRequest(e){switch(e){case V.READY:return await this._open();case V.PLAY:case V.PAUSE:break;case V.CLOSE:return await this._close()}return K.OK}async _close(){return this._vpsData=void 0,this._ppsData=void 0,this._spsData=void 0,this._sdpInfo=void 0,this._initFlags=0,K.OK}async _open(){return new Promise((e=>{"MediaSource"in window&&MediaSource.isTypeSupported('video/mp4; codecs="hev1.1.6.L93.B0"')?e(K.OK):e(K.ERROR)}))}onIncomingPipeRequest(e,t){return e.eMediaType!=k.GS_AV_TYPE_VIDEO||e.eCodeID!=G.GS_CODEID_ST_H265?K.ERROR:(this._sdpInfo=e,K.OK)}async onIncomingDataFromPrev(e,t){if(e.eMediaType!=k.GS_AV_TYPE_VIDEO)return $.ERROR;if(e.eCodeID!=G.GS_CODEID_ST_H265||null==t.aBuffer)return $.ERROR;let i,s=$.OK,n=new DataView(t.aBuffer.buffer,t.aBuffer.byteOffset),r=0,o=0,a=0,h=new Array;for(;;){if(r>=n.byteLength-Rt.STARTCODE_MAX_SIZE){i&&(i.aBuffer=t.aBuffer.subarray(o,n.byteLength),this._eventBus&&i.eFrameType==M.GS_AV_FRAME_VIDEO_SEI&&this._eventBus.trigger("sei",i),await this.updateSdp(i),h.push(i));break}let e=n.getUint32(r,!1);if(e==Rt.H264_START_CODE||e>>8==Rt.H264_START_CODE){a=e==Rt.H264_START_CODE?4:3,i&&(i.aBuffer=t.aBuffer.subarray(o,r),this._eventBus&&i.eFrameType==M.GS_AV_FRAME_VIDEO_SEI&&this._eventBus.trigger("sei",i),await this.updateSdp(i),h.push(i)),r+=a,o=r;let l=n.getUint8(r);r+=2;let u=new Et(l);if(0!=u.forbidden_zero_bit||u.nal_unit_type>ut.MAX){console.error("h265 data error: nalu "+l),s=$.ERROR;break}switch(i=new oe,u.nal_unit_type){case ut.SPS:i=this._spsData;break;case ut.PPS:i=this._ppsData;break;case ut.VPS:i=this._vpsData;break;case ut.BLA_W_LP:case ut.BLA_W_RADL:case ut.BLA_N_LP:case ut.IDR_W_RADL:case ut.IDR_N_LP:case ut.CRA:i.eFrameType=M.GS_AV_FRAME_VIDEO_I,i.setKeyFlag();break;case ut.TRAIL_N:case ut.TRAIL_R:case ut.RADL_N:case ut.RADL_R:case ut.RASL_N:case ut.RASL_R:i.eFrameType=M.GS_AV_FRAME_VIDEO_P;break;case ut.SEI:case ut.SEI_1:i.eFrameType=M.GS_AV_FRAME_VIDEO_SEI}if(i){if(i.nSeq=t.nSeq,i.nPTS=t.nPTS,i.nDTS=t.nDTS,i.nSTS=t.nSTS,i.nDuration=t.nDuration,u.nal_unit_type==ut.IDR_W_RADL){i.aBuffer=t.aBuffer.subarray(o,t.aBuffer.length),await this.updateSdp(i),h.push(i);break}if(u.nal_unit_type==ut.TRAIL_R){i.aBuffer=t.aBuffer.subarray(o,t.aBuffer.length),h.push(i);break}}}else r+=1}return s=await this.sendData(h),s}async sendData(e){let t=$.OK;if(this.hasReceiveSPSPPSVPS())for(let i of e)if(i.eFrameType==M.GS_AV_FRAME_VIDEO_I||i.eFrameType==M.GS_AV_FRAME_VIDEO_P)if(null!=i.aBuffer){let e=new Uint8Array(i.aBuffer.byteLength+4),s=i.aBuffer.byteLength;e[0]=s>>24&255,e[1]=s>>16&255,e[2]=s>>8&255,e[3]=255&s,e.set(i.aBuffer,4),i.aBuffer=e;for(let e of this.outBinders)e.sdpInfo.isEnable&&(t=await this.sendDataToNextPipe(i,e))}else t=$.ERROR;return t}async updateSdp(e){return!this.hasReceiveSPSPPSVPS()&&this._sdpInfo&&(e.eFrameType==M.GS_AV_FRAME_VIDEO_SPS&&(this._sdpInfo.aSPS=e.aBuffer),e.eFrameType==M.GS_AV_FRAME_VIDEO_PPS&&(this._sdpInfo.aPPS=e.aBuffer),e.eFrameType==M.GS_AV_FRAME_VIDEO_VPS&&(this._sdpInfo.aVPS=e.aBuffer),this._initFlags|=1<<e.eFrameType,this.hasReceiveSPSPPSVPS())?this.updateOutputSdpInfo([this._sdpInfo]):K.OK}hasReceiveSPSPPSVPS(){return(this._initFlags&Rt.INIT_FLAG)==Rt.INIT_FLAG}}Rt.PipeName="H265Analyzer",Rt.STARTCODE_MAX_SIZE=4,Rt.H264_START_CODE=1,Rt.INIT_FLAG=1<<M.GS_AV_FRAME_VIDEO_SPS|1<<M.GS_AV_FRAME_VIDEO_PPS|1<<M.GS_AV_FRAME_VIDEO_VPS;class Dt extends le{constructor(e){super(Dt.PipeName,e)}async onStatusChangeRequest(e){return e===V.READY?super.onStatusChangeRequest(e):K.OK}onIncomingPipeRequest(e,t){return K.OK}async onIncomingDataFromPrev(e,t){var i,s,n;e.eCodeID==G.GS_CODEID_ST_H264?console.debug("MultiOutPipe: h264 data: datalen="+(null===(i=t.aBuffer)||void 0===i?void 0:i.length)):e.eCodeID==G.GS_CODEID_ST_H265?console.debug("MultiOutPipe: h265 data: datalen="+(null===(s=t.aBuffer)||void 0===s?void 0:s.length)):e.eCodeID==G.GS_CODEID_AUDIO_ST_PCM&&console.debug("MultiOutPipe: pcm data: datalen="+(null===(n=t.aBuffer)||void 0===n?void 0:n.length));for(let e of this.outBinders)e.sdpInfo.isEnable&&await this.sendDataToNextPipe(t,e);return $.OK}}Dt.PipeName="TestMultiOutPipe";class bt extends le{constructor(e){super(bt.PipeName,e),this._btnDumpFile=null,this._bCanDumpFile=!1,this._firstKeyFrame=!1,this._dumpGopSum=10,this._dumpGopCur=0,this._dumpCacheArr=new Array,this._dumpCacheLen=0,this._btnDumpFile=document.getElementById("btnDumpFile"),this._btnDumpFile&&this._btnDumpFile.addEventListener("click",(e=>{this._ChangeDumpFileFlag(e)}),!1)}_ChangeDumpFileFlag(e){this._bCanDumpFile||(this._bCanDumpFile=!0,console.debug("start Dump File"))}async onStatusChangeRequest(e){switch(e){case V.READY:case V.PAUSE:case V.PLAY:break;case V.CLOSE:this._resetDumpState(),this._btnDumpFile&&(this._btnDumpFile.removeEventListener("click",this._ChangeDumpFileFlag),this._btnDumpFile=null)}return K.OK}onIncomingPipeRequest(e,t){return this._sdpInfo=e,this.updateOutputSdpInfo([this._sdpInfo]),K.OK}async onIncomingDataFromPrev(e,t){return this._bCanDumpFile&&t.aBuffer&&(t.isKey()&&(this._firstKeyFrame||(this._firstKeyFrame=!0),this._dumpGopCur+=1),this._firstKeyFrame&&(this._dumpCacheArr.unshift(t.aBuffer),this._dumpCacheLen+=t.aBuffer.byteLength),this._dumpGopCur>=this._dumpGopSum&&(this._bCanDumpFile=!1,this._dumpFile())),await this.sendDataToNextPipe(t,this.outBinders[0]),$.OK}_resetDumpState(){this._dumpCacheLen=0,this._bCanDumpFile=!1,this._firstKeyFrame=!1,this._dumpCacheArr=new Array,this._dumpGopCur=0}_dumpFile(){var e,t;if(this._sdpInfo&&this._dumpCacheArr.length>0){let i=new Uint8Array(this._dumpCacheLen+this._sdpInfo.aConfigBuffer.byteLength),s=0;for(i.set(null===(e=this._sdpInfo)||void 0===e?void 0:e.aConfigBuffer,s),s+=null===(t=this._sdpInfo)||void 0===t?void 0:t.aConfigBuffer.byteLength;this._dumpCacheArr.length>0;){let e=this._dumpCacheArr.pop();e&&(i.set(e,s),s+=e.byteLength)}!function(e,t){let i=new Blob([e]);var s=(window.URL||window.webkitURL).createObjectURL(i),n=document.createElement("li"),r=document.createElement("a");r.href=s,r.download=(new Date).toString()+"."+t,r.innerHTML=r.download,n.appendChild(r);let o=document.getElementById("FileDumpList");o&&o.appendChild(n)}(i,"mp4"),console.debug("Dump File Success."),this._resetDumpState()}}}bt.PipeName="FileDataCachePipe",function(e){e[e.OK=0]="OK",e[e.ERR_UNIMPLEMENT=1]="ERR_UNIMPLEMENT",e[e.ERR_INVALID_OPT=2]="ERR_INVALID_OPT",e[e.ERR_INVAILD_PARAMS=3]="ERR_INVAILD_PARAMS",e[e.ERR_INVALID_STATUS=4]="ERR_INVALID_STATUS",e[e.ERR_STATUS_CHANGE_FAILED=5]="ERR_STATUS_CHANGE_FAILED"}(ct||(ct={})),function(e){e[e.GSMP=0]="GSMP",e[e.HLS=1]="HLS",e[e.FLV=2]="FLV",e[e.LOCAL=3]="LOCAL",e[e.FMP4=4]="FMP4"}(dt||(dt={})),function(e){e[e.HIGH_PERF=0]="HIGH_PERF",e[e.FULL_FEAT=1]="FULL_FEAT"}(pt||(pt={})),function(e){e.Error="Error",e.Aborted="Aborted",e.PlayEnd="PlayEnd",e.PlayTimeUpdate="PlayTimeUpdate",e.PlayStateChange="PlayStateChange"}(mt||(mt={}));class Pt{constructor(e){this.name="AuthControl",this.option=e||{live:{Capture:"video_patrol_snap_ctrl",MultipleCapture:"video_patrol_snap_ctrl",Record:"video_patrol_snap_ctrl",Stream:"stream_change",Voice:"patrol_voice_control",alarm:"real_hand_alarm",Share:"video_patrol_share",ManualAlarm:"real_hand_alarm"},record:{Capture:"video_playback_snap",MultipleCapture:"video_playback_snap",Record:"video_playback_snap",SliceVideo:"video_playback_section",Voice:"video_voice_control",Share:"video_playback_share",Download:"video_playback_download",export:"video_playback_export",Lock:"video_playback_lock",Label:"video_playback_label_call",LabelCall:"video_playback_label_call",LabelManage:"video_playback_label_manage"}}}}function Bt(e,t){e.classList.toggle(t,!1)}function Lt(e,t){e.classList.add(t)}function Ct(e,t){if(e instanceof Date){var i={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};for(var s in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length))),i)new RegExp("("+s+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?i[s]:("00"+i[s]).substr((""+i[s]).length)));return t}return e}function At(e,t,i){if(0===arguments.length)return null;const s=t||"{y}-{m}-{d}-{h}-{i}-{s}";let n;if("object"==typeof e)n=e;else{if(!e)return e;10===(""+e).length&&(e=1e3*parseInt(e+"")),n=new Date(e)}const r={y:n.getFullYear(),m:n.getMonth()+1,d:n.getDate(),h:n.getHours(),i:n.getMinutes(),s:n.getSeconds(),a:n.getDay()};return s.replace(/{(y|m|d|h|i|s|a)+}/g,((e,t)=>{let s=r[t];return"a"===t?["一","二","三","四","五","六","日"][s-1]:(!i&&e.length>0&&s<10&&(s="0"+s),s||0)}))}function Ot(e){const t=new Date(e);return Ct(new Date(t.getTime()+864e5),"yyyy-MM-dd")}function xt(e,t,i){let s="";s="boolean"==typeof e?e?"record":"live":e;let n=!0;const r=Pt.authOption;if(!r||!r[s]||!r[s][t])return n;n=!1;const o=r[s][t];return i.indexOf(o)>-1&&(n=!0),n}Pt.authOption=null;class kt extends le{constructor(e,t,i,s){super(kt.PipeName,e),this._recording=!1,this._initKey=!1,this._inSDP=null,this._recordBuffer=[],this._recordName="",this._recordStartTime=new Date,this._recordEndTime=new Date,this._isDownloadSource=!1,this._recordProxy=[],this._recordDataCacheOut=!1,this._prevPipeName=t;let n=512;s.maxRecordSize&&(n="function"==typeof s.maxRecordSize?s.maxRecordSize():s.maxRecordSize),this._eventBus.listen("startRecordSource",(()=>{this.startRecordSource()})),this._eventBus.listen("stopRecordSource",(()=>{this.stopRecordSource()}));const r=t===vt.PipeName;this._isH264=i.eCodeID==G.GS_CODEID_ST_H264,this._isH265=i.eCodeID==G.GS_CODEID_ST_H265;const o=t===de.PipeName,a=r&&(this._isH264||this._isH265);this._recordDataCacheOut=a,kt.record&&e.trigger("startRecord","record");let h=0;e.listen("startRecord",(async t=>{h+=1;let i=0;this._recordBuffer=[],this._recordProxy=new Proxy(this._recordBuffer,{set(t,s,r){t[s]=r;if("symbol"!==typeof s&&Number(s)+""!="NaN"){let t=new Blob([r]);i+=t.size,t=void 0,i>=1024*n*1024&&e.trigger("stopRecord",!0)}return!0}}),this._recordName=t,this._recordStartTime=new Date,(kt.isDev&&a||o)&&(o&&this._inSDP&&this._recordBuffer.push(this._inSDP.aConfigBuffer),this._recording=!0)})),e.listen("stopRecord",(async(t=!1)=>{if(this._recording=!1,this._recordEndTime=new Date,!this._recordBuffer.length)return;const i=new Blob(this._recordBuffer),s=window.URL.createObjectURL(i),n=document.createElement("a"),r=At(this._recordStartTime,"{y}{m}{d}T{h}{i}{s}"),a=At(this._recordEndTime,"{y}{m}{d}T{h}{i}{s}"),l=`${this._recordName}-${r}-${a}-${String(h).padStart(2,"0")}`;o?n.download=`${l}.mp4`:this._isH264?n.download=`${l}.h264`:this._isH265&&(n.download=`${l}.h265`),n.style.display="none",n.href=s,document.body.appendChild(n),n.click(),document.body.removeChild(n),this._recordBuffer=[],t&&e.trigger("startRecord",this._recordName)}))}async onStatusChangeRequest(e){switch(e){case V.READY:case V.PLAY:case V.PAUSE:return this._eventBus.trigger("stopRecord"),K.OK;case V.CLOSE:return this.stopRecordSource(),K.OK}return K.OK}onIncomingPipeRequest(e,t){return this._inSDP=e,K.OK}async onIncomingDataFromPrev(e,t){return t.aBuffer&&this._recording?(this._prevPipeName===vt.PipeName&&e.eMediaType!=k.GS_AV_TYPE_VIDEO||(this._isDownloadSource?t.isKey()&&!this._initKey?(this._eventBus.trigger("onSourceRecording",this.uint2string(t.aBuffer)),this._initKey=!0):this._initKey&&this._eventBus.trigger("onSourceRecording",this.uint2string(t.aBuffer)):this._recordDataCacheOut?this._recordProxy.push(t.aBuffer):t.isKey()&&!this._initKey?(this._recordProxy.push(t.aBuffer),this._initKey=!0):this._initKey&&this._recordProxy.push(t.aBuffer)),$.OK):$.OK}startRecordSource(){!this._recording&&this._inSDP&&(this._eventBus.trigger("onStartRecordSource",this.uint2string(this._inSDP.aConfigBuffer)),this._recording=!0,this._isDownloadSource=!0)}stopRecordSource(){this._recording=!1,this._initKey=!1,this._eventBus.trigger("onStopRecordSource")}uint2string(e){return e.join(",")}}kt.PipeName="RecordPipe",kt.isDev=!1,kt.record=!1;class Mt{constructor(e,t){this._wsUrl="",this._ws=null,this._eventBus=e,this._onMessage=t}open(e){return this._wsUrl=e,new Promise((t=>{this._ws=new WebSocket(e),this._ws.onopen=()=>{t(K.OK)},this._ws.onmessage=e=>{const t=new Uint8Array(e.data);this._onMessage(t)},this._ws.onerror=()=>{var e;t(K.ERROR),null===(e=this._ws)||void 0===e||e.close()},this._ws.onclose=()=>{}}))}}class Gt{constructor(e,t,i){this._sourceURL=e.sourceURL,this._eventBus=t,this._fmp4Connect=new Mt(t,i)}async open(){return this._fmp4Connect.open(this._sourceURL)}async play(){return K.OK}async pause(){return K.OK}async close(){return K.OK}}class Nt extends le{constructor(e,t=new Z){super(Nt.PipeName,t,e.session),this._fmp4Client=new Gt(e,t,this.onMessage)}async onStatusChangeRequest(e){switch(e){case V.READY:return this.open();case V.PLAY:return this.play();case V.PAUSE:return this.pause();case V.CLOSE:return this.close()}return K.OK}async open(){return this._fmp4Client.open()}async play(){return this._fmp4Client.play()}async pause(){return this._fmp4Client.pause()}async close(){return this._fmp4Client.close()}onMessage(e){const t=new re;t.aBuffer=e;for(let e of this.outBinders)this.sendDataToNextPipe(t,e)}}Nt.PipeName="RTSPClientPipe";class Ft extends ue{constructor(e,t){let i;switch(e.sourceType){case dt.FMP4:i=new Nt(e,t);break;case dt.GSMP:default:i=new nt(e,t)}super("GSWebPlayerPipeline",new Array(i),t,e.session),this.sourcePipe_=i,this.openInfo_=e}getSourceSDP(){return this.sourcePipe_,null}makeNextPipe(e,t){let i=null;if("RecordPipe"===t.sinkPipeName)i=new kt(this._eventBus,e.pipeName,t,this.openInfo_);else if(e.pipeName==nt.PipeName)i=new vt(this._eventBus);else if(e.pipeName==Nt.PipeName)i=new bt(this._eventBus);else if(this.openInfo_.divToken)if(e.pipeName==vt.PipeName){if(t.eMediaType==k.GS_AV_TYPE_DATA)i=new Dt(this._eventBus);else if(t.eMediaType==k.GS_AV_TYPE_VIDEO)t.eCodeID==G.GS_CODEID_ST_H264?i=new yt(this._eventBus):t.eCodeID==G.GS_CODEID_ST_H265&&(i=new Rt(this._eventBus));else if(t.eMediaType==k.GS_AV_TYPE_AUDIO)try{i=new Tt(this.openInfo_.volumeToken,this._eventBus)}catch(e){console.log(e),i=new wt(this._eventBus)}}else e.pipeName==yt.PipeName||e.pipeName==Rt.PipeName?i=new de(this._eventBus):this.openInfo_.isDebug?e.pipeName==de.PipeName?i=new bt(this._eventBus):e.pipeName==bt.PipeName&&(i=new ye(this.openInfo_,this._eventBus)):e.pipeName==de.PipeName&&(i=new ye(this.openInfo_,this._eventBus));else i=new wt(this._eventBus);return i||console.error(`GSPL_WebPlayer route next pipe error: ${e.pipeName}, ${JSON.stringify(t)}`),i}static GetSourcePipe(e,t,i){}}class Ut extends ue{constructor(e,t){let i=new It(e.sourceURL,t);super("PCMFileSourcePipe",new Array(i),t),this.sourcePipe_=i,this.openInfo_=e}makeNextPipe(e,t){return e.pipeName==It.PipeName?new Tt(this.openInfo_.volumeToken,this._eventBus):null}}class Vt{static init(e=!1){this.funList=[],this.funPulsList=[],this.emptyFun=function(){};for(var t=0;t<this.funNames.length;t++){let e=console[this.funNames[t]];this.funList.push(e)}for(t=0;t<this.funNames.length;t++)this.createPlusFun(this.funList[t],this.funNames[t]);this.setConsoleMode(e)}static setLevel(e){this.$level=e,this.openConsole()}static setConsoleMode(e){this.isPlusMode=e,e?this.openConsolePlus():this.openConsole()}static openConsole(){if(this.closeConsole(),this.isPlusMode)this.openConsolePlus();else for(var e=this.$level;e<this.funNames.length;e++)console[this.funNames[e]]=this.funList[e]}static closeConsole(){for(var e=0;e<this.funNames.length;e++)console[this.funNames[e]]=this.emptyFun}static openConsolePlus(){this.isPlusMode=!0;for(var e=this.$level;e<this.funNames.length;e++)console[this.funNames[e]]=this.funPulsList[e]}static closeConsolePlus(){this.isPlusMode=!1,this.openConsole()}static createPlusFun(e,t){this.funPulsList.push((function(i,...s){console.group("["+t+"]["+new Date+"]"),e.apply(console,arguments),console.groupEnd()}))}}Vt.TRACE=0,Vt.DEBUG=1,Vt.LOG=1,Vt.INFO=2,Vt.WARN=3,Vt.ERROR=4,Vt.$level=Vt.LOG,Vt.funNames=["trace","debug","log","info","warn","error"],function(e){e[e.OK=0]="OK",e[e.ERR_UNIMPLEMENT=1]="ERR_UNIMPLEMENT",e[e.ERR_INVALID_OPT=2]="ERR_INVALID_OPT",e[e.ERR_INVAILD_PARAMS=3]="ERR_INVAILD_PARAMS",e[e.ERR_INVALID_STATUS=4]="ERR_INVALID_STATUS",e[e.ERR_STATUS_CHANGE_FAILED=5]="ERR_STATUS_CHANGE_FAILED"}(gt||(gt={})),function(e){e[e.HIGH_PERF=0]="HIGH_PERF",e[e.FULL_FEAT=1]="FULL_FEAT"}(_t||(_t={})),function(e){e.Error="Error",e.Aborted="Aborted",e.PlayEnd="PlayEnd",e.PlayTimeUpdate="PlayTimeUpdate",e.PlayStateChange="PlayStateChange"}(ft||(ft={}));class Wt{constructor(){this.divToken="",this.videoId="",this.volumeToken="",this.sourceURL="",this.sourceType=dt.GSMP,this.mode=_t.HIGH_PERF,this.isPlayback=!1,this.isDebug=!1,this.objectFit="contain",this.timeout=10}}class Kt{constructor(e){this.playerPipeline_=null,this._eventBus=new Z,this._error=0,this._option=new Wt,this.isRecording=!1,this.index=e||0,this._eventBus.listen("close",(async e=>{await this.stop.call(this),await this.close.call(this)})),this._eventBus.listen("error",(e=>{this._error=e})),this._eventBus.listen(x.CONNECT_ABORT,(e=>{this._eventBus.trigger(ft.Error,{type:x.CONNECT_ABORT,reason:e})})),this._eventBus.listen(x.TEST_EVENT,(e=>{console.debug("on event: "+x.TEST_EVENT+"; reason: "+e)})),this._eventBus.listen("proxy",((e,...t)=>{null!==this.playerPipeline_&&("getReconnectURL"===e?this._option.reconnect&&this._eventBus.trigger("getReconnectURL"):this._eventBus.trigger(e,...t))})),this._eventBus.listen(x.RTSP_TIMEOUT,(()=>{console.log("RTSP_TIMEOUT"),this.trigger("reconnect")})),this._eventBus.listen("reconnect",(()=>{this.trigger("timeout"),this._option.reconnect&&this.trigger("proxy","rtspReconnect")})),this._eventBus.listen("startRecord",(()=>{this.isRecording=!0})),this._eventBus.listen("stopRecord",(()=>{this.isRecording=!1})),this._eventBus.listen("startRecordSource",(()=>{this.isRecording=!0})),this._eventBus.listen("stopRecordSource",(()=>{this.isRecording=!1}))}static init(){Vt.init(),Vt.setLevel(Vt.DEBUG)}get sourceInfo(){return null}get isPlaying(){return!1}get videoId(){return this._option.videoId}addEventListener(e,t){this._eventBus.addEventListener(e,t)}removeEventListener(e,t){this._eventBus.removeEventListener(e,t)}async open(e){if(this._eventBus.trigger("error",0),this._option=e,this._option.videoId=Math.random()+`_${this.index}`,null!=this.playerPipeline_)return gt.ERR_INVALID_OPT;e.sourceType==dt.LOCAL?this.playerPipeline_=new Ut(e,this._eventBus):this.playerPipeline_=new Ft(e,this._eventBus);const t=await this.playerPipeline_.setStatus(V.READY);return new Promise(((e,i)=>{t==K.OK?e(t):i(t)}))}async close(){if(null==this.playerPipeline_)return gt.ERR_INVALID_OPT;return await this.playerPipeline_.setStatus(V.CLOSE)!=K.OK?gt.ERR_INVALID_OPT:(this.playerPipeline_&&this.playerPipeline_.reset(),this.playerPipeline_=null,this._eventBus.clearEventListener(),gt.OK)}async play(){if(null==this.playerPipeline_)return gt.ERR_INVALID_OPT;return await this.playerPipeline_.setStatus(V.PLAY)!=K.OK?gt.ERR_INVALID_OPT:gt.OK}async trigger(e,...t){this._eventBus.trigger(e,...t)}async stop(){if(null==this.playerPipeline_)return gt.ERR_INVALID_OPT;return await this.playerPipeline_.setStatus(V.PAUSE)!=K.OK?gt.ERR_INVALID_OPT:gt.OK}async seek(e){if(null==this.playerPipeline_)return gt.ERR_INVALID_OPT;if(this.playerPipeline_.currentStatus==V.NONE||this.playerPipeline_.currentStatus==V.CLOSE)return gt.ERR_INVALID_STATUS;let t=K.OK,i=new he(W.CURRENT_TIME,Math.floor(e));return t=await this.playerPipeline_.setAttribute(i),t!=K.OK?gt.ERR_INVALID_OPT:gt.OK}async setSpeed(e){if(null==this.playerPipeline_)return gt.ERR_INVALID_OPT;if(this.playerPipeline_.currentStatus==V.NONE||this.playerPipeline_.currentStatus==V.CLOSE)return gt.ERR_INVALID_STATUS;let t=K.OK,i=new he(W.RATE,e);return t=await this.playerPipeline_.setAttribute(i),t!=K.OK?gt.ERR_INVALID_OPT:gt.OK}async setVolume(e){return this._eventBus.trigger("setVolume",e),gt.OK}async pause(){if(null==this.playerPipeline_)return gt.ERR_INVALID_OPT;return await this.playerPipeline_.setStatus(V.PAUSE)!=K.OK?gt.ERR_INVALID_OPT:gt.OK}capturePic(){return gt.ERR_UNIMPLEMENT}reconnect(){this.trigger("reconnect")}startRecord(e){this.isRecording=!0,this._eventBus.trigger("startRecord",e)}stopRecord(){this.isRecording=!1,this._eventBus.trigger("stopRecord")}startRecordSource(){this.isRecording=!0,this._eventBus.trigger("startRecordSource")}stopRecordSource(){this.isRecording=!1,this._eventBus.trigger("stopRecordSource")}}function $t(e,t=!1){const i=e||new Date,s=i.getFullYear(),n=String(i.getMonth()+1).padStart(2,"0"),r=String(i.getDate()).padStart(2,"0");let o="00",a="00",h="00";return e&&!t&&(o=String(i.getHours()).padStart(2,"0"),a=String(i.getMinutes()).padStart(2,"0"),h=String(i.getSeconds()).padStart(2,"0")),`${s}-${n}-${r} ${o}:${a}:${h}`}function Yt(e,t,i,s){return(new Date(e).getTime()-new Date(t).getTime())/(6e4*i)*s}function Ht(e,t){let i=0;for(let[s,n]of t){if(e>=s&&e<=n)return i;i++}return-1}function qt(e,t){let i="",s="",n="",r=t*e;const o=Math.floor(r/60)%24;i=String(o>=0?0:24),i=+i+o+"",i=String(i).padStart(2,"0"),r%=60,s=String(r<0?60+r:r),s=String(s).padStart(2,"0");const a=Math.round(60*r)%60;return n=String(a>=0?0:60),n=+n+a+"",n=String(n).padStart(2,"0"),`${i}:${s}:${n}`}function jt(e,t,i,s,n){e&&(e.fillStyle="white",e.fillRect(t,i,s,n))}function Xt(e,t,{x:i,y:s,bold:n=1,font:r,align:o,color:a,maxLength:h}){if(e){e.fillStyle=a,e.font=r,e.textAlign=o;for(let r=0;r<n;r++)e.fillText(t,i,s,h)}}function zt(){return!!window.navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)}class Zt{constructor(e,t="div"){this.targetDiv=null,this.eventBus=e,this.element=document.createElement(t||"div")}appendTo(e){e&&this.element&&(this.targetDiv=e,e.append(this.element))}styleInit(e){Object.entries(e).forEach((e=>{const[t,i]=e;this.element.style[t]=i}))}isValify(){}paramsInit(e){Object.entries(e).forEach((e=>{const[t,i]=e;this.element[t]=i}))}addClass(e){this.element.classList.add(e)}removeClass(e){this.element.classList.toggle(e,!1)}clearClass(){this.element.className=""}}class Jt{constructor(e,t="div",i=!1){this.targetDiv=null,this.eventMap=new Map,this.isFlex=!1,this._visible=!0,this.eventBus=e,this.element=document.createElement(t||"div"),i&&this.afterHide()}get visible(){return this._visible}get dataset(){return this.element.dataset}set visible(e){this._visible=e,this.styleInit({display:e?this.isFlex?"flex":"inline-block":"none"})}afterShow(){this.removeClass("gs-after-hide"),this.addClass("gs-after-show")}afterHide(){this.removeClass("gs-after-show"),this.addClass("gs-after-hide")}append(e){e instanceof Jt?this.element.append(e.element):e&&this.element.append(e)}appendTo(e,t){e instanceof Jt?(this.targetDiv=e.element,e.element.append(this.element)):e&&(this.targetDiv=e,e.append(this.element)),this.parent=t}appendBefore(e,t){if(e instanceof Jt){this.targetDiv=e.element;const t=e.element.childNodes[0];t?this.element.before(t):e.element.append(this.element)}else if(e){this.targetDiv=e;const t=e.childNodes[0];t?this.element.before(t):e.append(this.element)}this.parent=t}styleInit(e){Object.entries(e).forEach((e=>{const[t,i]=e;this.element.style[t]=i}))}paramsInit(e){Object.entries(e).forEach((e=>{const[t,i]=e;this.element[t]=i}))}attrInit(e){Object.entries(e).forEach((e=>{const[t,i]=e;this.element.attributes[t]=i}))}addClass(e){this.element.classList.add(e)}removeClass(e){this.element.classList.toggle(e,!1)}clearClass(){this.element.className=""}listen(e,t){this.element.addEventListener(e,t);(this.eventMap.get(e)||[]).push(t)}removeListen(e,t){const i=this.eventMap.get(e)||[];if(t){this.element.removeEventListener(e,t);const s=i.indexOf(t);s>=0&&i.splice(s,1)}else i.forEach((t=>{this.element.removeEventListener(e,t)}))}}class Qt extends Jt{constructor(e,t="div",i=!1){super(e,t,i),this.eventBus=e}}class ei extends Jt{constructor(e,{offsetY:t,height:i,nameWidth:s,getTimeByOffsetX:n}){super(e),this.left=0,this.width=0,this.leftCache=0,this.widthCache=0,this.leftMouseDown=!1,this.rightMouseDown=!1,this.startClientX=0,this.startTime="",this.endTime="",this.dragover=e=>{if(e instanceof DragEvent&&e.preventDefault(),!this.leftMouseDown&&!this.rightMouseDown)return;const t=e.clientX-this.startClientX,i=this.leftMouseDown?-t:t,s=this.leftMouseDown?t>this.width?this.width:t:0,n=this.left+s,r=this.width+i;this.leftCache=n<this.nameWidth?this.nameWidth:n,this.widthCache=n<this.nameWidth?n+r-this.nameWidth:r,this.widthCache<=0&&(this.widthCache=0),this.leftMouseDown?this.leftFlag.dataset.time=this.getTimeByOffsetX(this.leftCache):this.rightFlag.dataset.time=this.getTimeByOffsetX(this.leftCache+this.widthCache),this.styleInit({left:this.leftCache+"px",width:this.widthCache+"px"})},this.dragend=e=>{(this.leftMouseDown||this.rightMouseDown)&&(this.rightMouseDown=!1,this.leftMouseDown=!1,this.left=this.leftCache,this.width=this.widthCache,this.styleInit({left:this.leftCache+"px",width:this.widthCache+"px"}),this.setTime(),this.removeClass("gs-pointer-events-none"),this.leftFlag.removeClass("gs-pointer-events-none"),this.rightFlag.removeClass("gs-pointer-events-none"),this.leftFlag.afterHide(),this.rightFlag.afterHide())},this.leftFlag=new Jt(e,"div",!0),this.rightFlag=new Jt(e,"div",!0),this._offsetY=t,this.height=i,this.visible=!1,this.nameWidth=s,this.getTimeByOffsetX=n,this.initStyle(),this.eventInit()}set offsetY(e){this._offsetY=e,this.styleInit({top:this.offsetY+45+"px"})}get offsetY(){return this._offsetY}initStyle(){this.leftFlag.addClass("gs-icon-left"),this.leftFlag.addClass("gs-timeline-select-left"),this.rightFlag.addClass("gs-icon-right"),this.rightFlag.addClass("gs-timeline-select-right"),this.addClass("gs-timeline-select-record"),this.styleInit({top:this.offsetY+45+"px",height:this.height+"px"})}appendTo(e){super.appendTo(e),this.leftFlag.appendTo(this),this.rightFlag.appendTo(this)}showSelected(e,t){this.left=e,this.width=t,this.styleInit({left:e+"px",width:t+"px"}),this.visible=!0,this.setTime()}eventInit(){const e=e=>{this.startClientX=e.clientX,this.addClass("gs-pointer-events-none"),this.leftFlag.addClass("gs-pointer-events-none"),this.rightFlag.addClass("gs-pointer-events-none")};this.leftFlag.element.onmousedown=t=>{console.log("left mousedown"),this.leftFlag.afterShow(),this.rightMouseDown=!1,this.leftMouseDown=!0,e(t)},this.rightFlag.element.onmousedown=t=>{console.log("right mousedown"),this.rightFlag.afterShow(),this.leftMouseDown=!1,this.rightMouseDown=!0,e(t)}}setTime(){this.visible&&(this.startTime=this.getTimeByOffsetX(this.left),this.endTime=this.getTimeByOffsetX(this.left+this.width),this.leftFlag.dataset.time=this.startTime,this.rightFlag.dataset.time=this.endTime)}}class ti{constructor(e,t,i,{gap:s=4,height:n=22,nameWidth:r=130,offsetY:o=50,timeGap:a=90,autoGetRecord:h=!1}={}){this.ctx=null,this.width=0,this.gap=4,this.height=22,this.nameWidth=130,this.offsetY=50,this.midX=0,this.currentIndex=0,this.rangeList=[],this.rangeTimeList=[],this.rangeTimeListInitPromise=null,this.isSelected=!1,this.showTime="",this._currentTime="",this.timeRange=0,this.timeGap=90,this.title="",this.rate=0,this.status="none",this.labels=[],this.updateTimeLine=e=>{},this.supportH265=!0,this.pending=!1,this.dateMap=new Map,this.autoGetRecord=!1,this.lastDate="",this._authOption=[],this._scrollIndex=0,this._index=0,this._labelObj=new Map,this.getTimeByOffsetX=e=>{const t=(e-this.midX)*this._timelineGroup.gapMinute/this.timeGap;return $t(new Date(new Date(this._timelineGroup.currentTime).getTime()+60*t*1e3))},this._timelineGroup=e;const l=e.element;this.ctx=l.getContext("2d"),this.width=l.width,this.gap=s,this.height=n,this.nameWidth=r,this.offsetY=o,this.timeGap=a,this.index=t,this.midX=this.width/2,this.eventBus=e.eventBus,this._selectRecordDiv=new ei(this.eventBus,{offsetY:this.offset,height:this.height,nameWidth:r,getTimeByOffsetX:this.getTimeByOffsetX}),this.autoGetRecord=h,this._selectRecordDiv.appendTo(e.targetDiv),this.updateTimeLine=i,this.clearHeader(),this.eventInit()}set index(e){this._index=e,this._selectRecordDiv&&(this._selectRecordDiv.offsetY=this.offset)}get index(){return this._index}set currentTime(e){this._currentTime=e;const t=new Date(e).getTime(),i=this.rangeTimeList[this.rangeTimeList.length-1];this.rangeTimeList.length?t>=i[1]&&this.eventBus.emit("videoEnd",this.index):this.eventBus.emit("videoEnd",this.index)}get currentTime(){return this._currentTime}set scrollIndex(e){this._scrollIndex=e,this._selectRecordDiv&&(this._selectRecordDiv.offsetY=this.offset)}get synchronous(){return this._timelineGroup.synchronous}get offset(){return this.offsetY+(this.index-this._scrollIndex)*(this.gap+this.height)}get visible(){return this.index>=this._scrollIndex&&this.index<this._scrollIndex+4}get selectRecordVisible(){return this._selectRecordDiv.visible}get selectRecordTimeRange(){return{startTime:this._selectRecordDiv.startTime,endTime:this._selectRecordDiv.endTime}}set authOption(e){this._authOption=e}get authOption(){return this._authOption}eventInit(){this.eventBus.listen("getNextRecordCallback",((e,t)=>{t===this.index&&(e.forEach((e=>{e.startStamp=new Date(e.rcdStartTime).getTime(),e.endStamp=new Date(e.rcdEndTime).getTime()})),e=e.sort(((e,t)=>e.startStamp-t.startStamp)),e=this.mergeRangeList(e),this.recordDateMap(e),this.rangeList.push(...e),this.rangeTimeList.push(...e.map((e=>[e.startStamp,e.endStamp]))),e.length&&this.eventBus.emit("seek",this.index))}))}checkPoint(e){if(!this.ctx)return;this.ctx.beginPath(),this.ctx.rect(0,this.offset,this.width,this.height);const t=this.ctx.isPointInPath(e.offsetX,e.offsetY);return this.currentIndex=t?this.index:-1,t}clearCurrentFlag(){jt(this.ctx,this.midX-1,this.offset-2,3,26)}drawFlag(){this.ctx&&this.rangeList.length&&this.isSelected&&!this.synchronous&&(this.ctx.fillStyle="#ff9e33",this.ctx.fillRect(this.midX-1,this.offset-2,3,26))}recordDateMap(e){if(!e.length)return;const t=e[0].rcdStartTime.split(" ")[0],i=e[e.length-1].rcdEndTime.split(" ")[0];let s=[],n=t;for(;new Date(n)<=new Date(i);)s.push(n),n=Ot(n);s.forEach((e=>{this.dateMap.set(e,!0)}))}mergeRangeList(e){let t=0;for(;e[t+1];){const i=e[t],s=e[t+1];this.dateMap.set(i.rcdStartTime.split(" ")[0],!0),s.startStamp>i.endStamp+1e3||i.relevantReason!==s.relevantReason?t++:(s.endStamp<=i.endStamp||(e[t].rcdEndTime=e[t+1].rcdEndTime,e[t].endStamp=e[t+1].endStamp),e.splice(t+1,1))}return this.recordDateMap(e),e}setVideoRange({rangeList:e,currentTime:t,timeRange:i}={}){this.rangeList=e||this.rangeList,this.showTime=t||this.showTime,this.timeRange=i||this.timeRange,e&&e.length&&(this.supportH265=!0,this.rangeList=this.mergeRangeList(this.rangeList),this.status="ready",this.rangeTimeList=this.rangeList.map((e=>[e.startStamp,e.endStamp])),this.currentTime=e[0].rcdStartTime)}getTimeRange(e,t){return Yt(e,t,this.timeRange,this.timeGap)}drawVideoRange(){for(let e of this.rangeList){let t=this.getTimeRange(e.rcdStartTime,this.showTime)+this.midX,i=this.getTimeRange(e.rcdEndTime,e.rcdStartTime);if(!this.ctx)return;switch(e.relevantReason){case-1:case 1:this.ctx.fillStyle="#3d84ee";break;case 0:this.ctx.fillStyle="#ea4f4f";break;case 2:this.ctx.fillStyle="#4fcf37";break;case 3:case 4:case 5:this.ctx.fillStyle="#ffa500";break;default:this.ctx.fillStyle="#3d84ee"}i=t<this.nameWidth?i-(this.nameWidth-t):i,t=t<this.nameWidth?this.nameWidth:t,this.ctx.fillRect(t,this.offset,i<0?0:i,this.height)}}drawLine(){this.ctx&&(this.ctx.fillStyle=this.isSelected?"#d4e4ff":"#eef3ff",this.ctx.fillRect(this.nameWidth,this.offset,this.width,this.height))}draw(e){this.isSelected&&this.eventBus.emit("statusChange",this.status),this.clearCurrentFlag(),this.drawLine(),this.drawVideoRange(),this.drawFlag(),this.drawLabels(),e&&(this.clearHeader(),this.setVideoImg(),this.setTitle())}currentChange(e){this.currentIndex=void 0!==e?e:this.currentIndex,this.isSelected=this.index==this.currentIndex,this.isSelected&&this.eventBus.emit("rateChange",this.rate),this.index>=this._scrollIndex&&this.index<this._scrollIndex+4&&this.draw(!0)}play(){this.rangeList.length&&"play"!=this.status&&(this.status="play",this.isSelected&&this.eventBus.emit("statusChange","play"))}stop(){this.status="stop",this.isSelected&&this.eventBus.emit("statusChange","stop")}checkSeekable(e){if(!this.supportH265||this.pending)return!1;return Ht(new Date(e).getTime(),this.rangeTimeList)>-1}seek(e,t=!0){if(!this.supportH265||this.pending)return!1;const i=Ht(new Date(e).getTime(),this.rangeTimeList)>-1;return i&&(this.currentTime=e,t&&this.play()),i}close(e=!0){this.stop(),e&&(this.rate=0,this.clearTitle(),this.title="",this.rangeList=[],this.labels=[],this.dateMap=new Map,this.supportH265=!0),this.pending=!1,this.status=this.rangeList.length?"ready":"none",this.isSelected&&this.eventBus.emit("statusChange",this.status),console.log("close"),this.draw()}clearHeader(){this.ctx&&(this.ctx.fillStyle="rgb(246,249,255)",this.ctx.fillRect(0,this.offset,this.nameWidth,this.height))}clearTitle(){this.ctx&&(this.ctx.fillStyle="rgb(246,249,255)",this.ctx.fillRect(34,this.offset,this.nameWidth-10-16-8,this.height))}drawTitle(){if(!this.ctx)return;let e=this.title;e.length>12&&(e=this.title.slice(0,12)+"..."),Xt(this.ctx,e,{x:34,y:this.offset+16,color:"#3d84ee",font:"bold 14px Microsoft JhengHei",align:"left",bold:3,maxLength:this.nameWidth-10-16-8})}drawLabels(){const e={},t=e=>{const t=this._labelObj.get(e),i=null==t?void 0:t.div;if(!i||!t)return;const s=this.getTimeRange(t.labelPostionTime||t.positionTime,this.showTime)+this.midX-5;i.styleInit({top:this.offset-1+"px",left:s+"px",display:s>this.nameWidth?"block":"none"})};this.labels.forEach((i=>{e[i.id]=i;const s=this._labelObj.get(i.id);if(s)return Object.assign(s,i),void t(i.id);this._labelObj.set(i.id,i);const n=new Jt(this.eventBus);this._labelObj.get(i.id).div=n,n.addClass("gs-icon-label"),n.addClass("gs-timeline-label"),t(i.id),n.appendTo(this._timelineGroup.labelWrapper.element),n.element.addEventListener("mouseover",(e=>{const t=e.target;if(!t)return;const s=(this.index-1)*(this.height+this.gap)+this.offsetY+45-9,n=(t.style.left.match(/\d+/g)||[0])[0];this.eventBus.emit("showLabelPoptext",s,i.title,n)})),n.element.addEventListener("mouseout",(e=>{this.eventBus.emit("hideLabelPoptext")})),n.element.addEventListener("click",(e=>{this.eventBus.emit("labelClick",i)}))})),this._labelObj.forEach(((t,i)=>{var s;e[i]||(null===(s=t.div)||void 0===s||s.element.remove(),this._labelObj.delete(i))}))}setVideoImg(){if(this.index>=this._timelineGroup.groupLength)return;const e=new Image;e.src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAMRJREFUOE9jZKAQMFKon4E6Bti2vj/F8P+/KUHXMDKePlwtaIasDuwC25Z3/wlqhio4XCPEaNX63oD5H4PC4VrBDSgGxNlwMLjrsjHMPvCD4cD1X9jNZGRMZPj/v5/hP8PFw7VCDigGLM3kY5AVYmbYdeUXQ8vGr/gd9Z/hIIYBDppsDDZqrAwrTvxguPPyL+kGEBsOYHXYXEAfAyCBOIHhP8MFjDAgxgWwaGT6xyBwpFbwACQWKE1IxNiMSw118gIlLgAAdrdcEQaGTMwAAAAASUVORK5CYII=",e.onload=()=>{this.ctx&&(this.index>=this._timelineGroup.groupLength||this.ctx.drawImage(e,10,this.offset+3,16,16))}}setTitle(e){this.title=null==e?this.title:e,this.visible&&(this.title?(this.clearTitle(),this.drawTitle()):this.clearTitle())}getRecord(e){const t=Ht(new Date(e||this.currentTime).getTime(),this.rangeTimeList);return this.rangeList[t]}getNextTimePoint(e){const t=function(e,t){for(let i in t){const[s,n]=t[i],r=t[+i+1];if(0==+i&&e<=s)return+i;if(!r)return-1;const[o,a]=r;if(e>n&&e<o)return+i+1}return-1}(new Date(e).getTime(),this.rangeTimeList);return t>=0?this.rangeList[t].rcdStartTime:e}drawSelectedRecord(e,t){this._selectRecordDiv.showSelected(e,t)}isRecordSelected(e,t){return!(!this.rangeList||!this.rangeList.length)&&!(this.offset>t||this.offset+this.height<e)}dragover(e){this._selectRecordDiv.dragover(e)}dragend(e){this._selectRecordDiv.dragend(e)}setSelectRecordTime(){this._selectRecordDiv.setTime()}hideSelectRecord(){this._selectRecordDiv.visible=!1}getNextRecord(e){if(!this.autoGetRecord)return;const t=(e||this.currentTime).split(" ")[0];if(t!==this.lastDate&&!this.dateMap.get(Ot(t))){this.lastDate=t;const e=Ot(t);this.dateMap.set(e,!0);const i=Ct(new Date,"yyyy-MM-dd");if(new Date(e)>new Date(i))return;this.eventBus.emit("stop",this.index),this.eventBus.emit("getNextRecord",e,this.index)}}}class ii{constructor(e){this.mouseDown=!1,this.mouseDownX=0,this.currentOffsetX=0,this.ractangleOffsetX=0,this.ractangleOffsetY=0,this.ractangleClientX=0,this.ractangleClientY=0,this._onDblClick=e=>{e.preventDefault(),e.stopPropagation();const t=this.timeLineGroup;let i=null;t.groupItems.forEach(((s,n)=>{s.checkPoint(e)&&(console.log(s.currentTime),t.painter.drawTimePointByTime(s.currentTime),i=n)}));const s=t.currentIndex;if(t.currentIndex=null==i?t.currentIndex:i,null!=i)if(t.currentIndex!==s)t.eventBus&&t.eventBus.emit("currentChange",t.currentIndex);else{if(e.offsetX<=t.nameWidth)return;{const i=t.gapMinute,s=(e.offsetX-t.midX)/t.timeGap*i*60*1e3,n=$t(new Date(new Date(t.currentTime).getTime()+s));t.groupItems[t.currentIndex].seek(n)&&(t.painter.drawTimePointByTime(n),t.eventBus.emit("seek"))}}},this._onMouseWheel=e=>{const t=this.timeLineGroup;let i=e.deltaY;i=i<0?100*Math.ceil(i/100):100*Math.floor(i/100),t.showTimeRangeButton&&t.adjustTimeRangeDiv&&t.adjustTimeRangeDiv.timeRangeIndex!==t.timeGapRangeIndex&&(t.timeGapRangeIndex=t.adjustTimeRangeDiv.timeRangeIndex);const s=t.timeGapRange[t.timeGapRangeIndex];t.timeGapRangeIndex+=i/100;const n=t.timeGapRange.length-1;if(t.timeGapRangeIndex<0)return void(t.timeGapRangeIndex=0);if(t.timeGapRangeIndex>n)return void(t.timeGapRangeIndex=n);const r=t.timeGap;if(t.showTimeRangeButton&&t.adjustTimeRangeDiv){i<0?t.adjustTimeRangeDiv.changeRange(1,!1):i>0&&t.adjustTimeRangeDiv.changeRange(2,!1);const e=(t.width-t.nameWidth)/(t.timeRangeArr[t.timeGapRangeIndex]/t.timeGapRange[t.timeGapRangeIndex]);t.timeGap=e<55?55:e}t.offsetX=t.offsetX*(t.timeGap/r)*(s/t.timeGapRange[t.timeGapRangeIndex]),t.selectRecord&&t.groupItems.forEach((e=>{e.setSelectRecordTime()})),t.painter.drawTimePointByTime()},this._onMouseDown=e=>{const t=this.timeLineGroup;this.mouseDown=!0,this.mouseDownX=e.screenX,this.ractangleOffsetX=e.offsetX,this.ractangleOffsetY=e.offsetY,this.ractangleClientX=e.clientX,this.ractangleClientY=e.clientY,this.currentOffsetX=t.offsetX},this._onMouseMove=e=>{const t=this.timeLineGroup;t.groupItems.forEach((t=>{t.dragover(e)}));const{offsetX:i,offsetY:s}=e,n=this.timeLineGroup.offsetY,{height:r,gap:o,scrollIndex:a}=this.timeLineGroup,h=Math.floor((s-n)/(r+o)),l=s-n-h*(r+o);if(i<this.timeLineGroup.nameWidth&&h>=0&&l<=r){if(this.timeLineGroup.groupItems[h+a]){const e=this.timeLineGroup.groupItems[h+a].title,t=(h-1)*(r+o)+n+45-9;this.timeLineGroup.eventBus.emit("showPoptext",t,e)}}else this.timeLineGroup.eventBus.emit("hidePoptext");this.mouseDown&&(this.selectRecord?this._onRantangleMouseMove(e,!1):(t.offsetX=e.screenX-this.mouseDownX+this.currentOffsetX,t.painter.clearTime(),t.painter.drawTimePoint(),t.painter.clearCurrentTime(),t.painter.drawCurrentTime(),t.painter.drawVideo()))},this._onRantangleMouseMove=(e,t=!0)=>{this.mouseDown&&this.timeLineGroup.painter.drawTimeSelectRectangle({startOffsetX:this.ractangleOffsetX,startOffsetY:this.ractangleOffsetY,startClientX:this.ractangleClientX,startClientY:this.ractangleClientY,endOffsetX:e.offsetX,endOffsetY:e.offsetY,endClientX:e.clientX,endClientY:e.clientY},t)},this._onRantangleMouseUp=e=>{this.mouseDown=!1,this.timeLineGroup.selectTimeRectangleDiv.styleInit({width:0,height:0,left:0,top:0,border:"none"}),this.timeLineGroup.painter.drawTimeLineSelectComponents(),this.timeLineGroup.eventBus.emit("showSelectRecordOption",this.timeLineGroup.selectRecordVisible)},this._onMouseUp=e=>{e.target&&"CANVAS"===e.target.tagName&&(e.preventDefault(),e.stopPropagation());const t=this.timeLineGroup;this.selectRecord&&!this.mouseDown&&t.groupItems.forEach((t=>{t.dragend(e)})),this.selectRecord&&this.mouseDown?this._onRantangleMouseUp(e):this.mouseDown&&(t.offsetX-this.currentOffsetX?(this.timeLineGroup.autoGetRecord?this.timeLineGroup.getNextRecord():t.eventBus.emit("seek"),this.mouseDown=!1):this.mouseDown=!1)},this._scrollTo=e=>{this.timeLineGroup.scrollIndex=e},this.onResize=()=>{const e=this.timeLineGroup;if(e.targetDiv){if(e.targetDiv.offsetWidth&&e.paramsInit({width:e.targetDiv.offsetWidth}),e.element&&e.element instanceof HTMLCanvasElement&&(e.width=e.element.width),e.showTimeRangeButton&&e.adjustTimeRangeDiv){const t=e.timeGap,i=(e.width-e.nameWidth)/(e.timeRangeArr[e.timeGapRangeIndex]/e.timeGapRange[e.timeGapRangeIndex]);e.timeGap=i<55?55:i,e.offsetX=e.offsetX*(e.timeGap/t)}e.midX=e.width/2,e.groupItems.forEach((t=>{t.width=e.width,t.midX=e.midX,t.setTitle()})),e.painter.timePonitInit()}},this.timeLineGroup=e}get selectRecord(){return this.timeLineGroup.selectRecord}eventInit(){const e=this.timeLineGroup;e.element.addEventListener("dblclick",this._onDblClick),e.element.addEventListener("mousewheel",this._onMouseWheel),e.element.addEventListener("mousedown",this._onMouseDown),e.element.addEventListener("mousemove",this._onMouseMove),e.element.addEventListener("mouseup",this._onMouseUp,!1),document.body.addEventListener("mouseup",this._onMouseUp,!1),e.selectTimeRectangleDiv.element.addEventListener("mousemove",this._onRantangleMouseMove),e.selectTimeRectangleDiv.element.addEventListener("mouseup",this._onRantangleMouseUp);const t=this.onResize;let i;window.addEventListener("resize",(function(){i&&clearTimeout(i),i=setTimeout((function(){t(),i=null}),100)})),e.eventBus.addEventListener("scrollTo",this._scrollTo)}}class si{constructor(e){this._scrollIndex=0,this._selectRecordParams={width:0,height:0,left:0,top:0},this.drawTimePointByTime=e=>{var t;const i=this.timeLineGroup;if(null===(t=i.groupEvent)||void 0===t?void 0:t.mouseDown)return;const s=Yt(i.currentTime,e||i.currentTime,i.timeGapRange[i.timeGapRangeIndex],i.timeGap);i.offsetX=s+i.offsetX,i.currentTime=e||i.currentTime,this.clearCurrentTime(),this.drawCurrentTime(),this.clearTime(),this.drawTimePoint(),this.drawVideo()},this.timeLineGroup=e}set scrollIndex(e){this._scrollIndex=e}clearTime(){const e=this.timeLineGroup;jt(e.ctx,0,35,e.width,13)}drawVideo(e){const t=this.timeLineGroup;t.groupItems.slice(this._scrollIndex,this._scrollIndex+4).forEach((i=>{i.setVideoRange({currentTime:t.currentTime,timeRange:t.timeGapRange[t.timeGapRangeIndex]}),i.draw(e)}))}clearCurrentTime(){const e=this.timeLineGroup;jt(e.ctx,e.midX-100,1,200,16)}getCurrentTime(){const e=this.timeLineGroup,t=new Date($t()).getTime();return $t(new Date(t+-e.offsetX/e.timeGap*e.timeGapRange[e.timeGapRangeIndex]*60*1e3))}drawCurrentTime(){const e=this.timeLineGroup;e.currentTime=this.getCurrentTime(),Xt(e.ctx,e.currentTime,{x:e.midX,y:14,color:"#3d84ee",font:"bold 14px Microsoft JhengHei",align:"center",bold:3})}drawTimePoint(){const e=this.timeLineGroup;if(!e.ctx)return;const t=e.midX+e.offsetX,i=e.timeGapRange[e.timeGapRangeIndex];e.ctx.fillStyle="black",e.ctx.font="12px Microsoft YaHei",e.ctx.textAlign="center";const[s,n]=function(e,t,i){i=i||$t();const s=e?new Date(e):new Date(i),n=$t(s,!0),r=new Date(n).getTime(),o=new Date(i).getTime()-r;return[Math.floor(o/(6e4*t))||0,Math.floor((s.getTime()-r)/(6e4*t))]}(e.currentTime,i);for(let r=n-24;r<n+25;r++){const n=t+e.timeGap*(-s+r);if(n){const t=qt(r,i);this.drawLine(n,e.offsetY-3),e.ctx.fillText(t,n,e.offsetY-3-3)}}e.ctx.clearRect(0,0,e.nameWidth,e.offsetY)}drawLine(e,t,i=.5,s="black"){const n=this.timeLineGroup;n.ctx&&(n.ctx.beginPath(),n.ctx.moveTo(e,t),n.ctx.lineTo(e,t-3),n.ctx.closePath(),n.ctx.strokeStyle=s,n.ctx.lineWidth=i,n.ctx.stroke())}drawTimeSelectRectangle({startOffsetX:e,startOffsetY:t,startClientX:i,startClientY:s,endOffsetX:n,endOffsetY:r,endClientX:o,endClientY:a},h=!0){const l=Math.abs(o-i),u=Math.abs(a-s);let c=Math.min(n,e),d=Math.min(t,r);h&&(o>=i?(c=e,d=a>=s?t:t-u):(c=e-l,d=a>=s?t:t-u)),this._selectRecordParams={width:l,height:u,left:c,top:d},this.timeLineGroup.selectTimeRectangleDiv.styleInit({width:l+"px",height:u+"px",left:c+"px",top:d+45+"px",border:"1px solid rgb(29, 76, 187)"})}drawTimeLineSelectComponents(){const{top:e,left:t,width:i,height:s}=this._selectRecordParams;this.timeLineGroup.groupItems.forEach((n=>{n.isRecordSelected(e,e+s)&&n.drawSelectedRecord(t,i)}))}drawSynchronousFlag(){this.timeLineGroup.synchronousFlag.styleInit({display:this.timeLineGroup.synchronous?"block":"none"})}timePonitInit(){this.clearTime(),this.drawTimePoint(),this.clearCurrentTime(),this.drawCurrentTime(),this.drawVideo(!0),this.drawSynchronousFlag()}}class ni extends Zt{constructor(e,t,i=18){super(e,"button"),this._index=0,this._groupLength=4,this._disabled=!1,this._height=i,this._type=t,this._init(),this._initListener()}set groupLength(e){this._groupLength=e,this.index=this._index}set disabled(e){this._disabled=e,this.paramsInit({disabled:e})}set index(e){this._index=e,0!=this._index||this._type?this._index>=this._groupLength-4&&this._type?this.disabled=!0:this.disabled=!1:this.disabled=!0}_init(){this.addClass("gs-scroll-option"),this.styleInit({borderBottom:this._type?void 0:"1px solid #D9D9D9",borderTop:this._type?"1px solid #D9D9D9":void 0,height:this._height-1+"px",width:this._height-1+"px",position:"absolute",top:this._type?void 0:0,bottom:this._type?0:void 0}),this.index=this._index;const e=new Zt(this.eventBus);e.addClass("gs-scroll-icon"),this._type?e.addClass("gs-icon-scrollDown"):e.addClass("gs-icon-scrollUp"),e.appendTo(this.element)}_initListener(){var e;null===(e=this.element)||void 0===e||e.addEventListener("click",(()=>{var e;this._index<=0&&!this._type||this._index>=this._groupLength-4&&this._type||null===(e=this.eventBus)||void 0===e||e.emit("scroll",this._type?1:-1)}))}}class ri extends Zt{constructor(e,t,i,s,n){super(e),this._groupLength=4,this._index=0,this._scrollHeight=0,this._groupItemHeight=t,this._gap=i,this._height=s,this._optionHeight=n,this._init()}set groupLength(e){this._groupLength=e,this.index=0;const t=this._groupItemHeight*this._groupLength+this._gap*(this._groupLength-1);this._scrollHeight=this._height/t*(this._height-2*this._optionHeight),this.styleInit({height:this._scrollHeight+"px"})}set index(e){this._index=e,this.styleInit({top:this._optionHeight+e*(this._height-2*this._optionHeight-this._scrollHeight)/(this._groupLength-4)+"px"})}_init(){this.addClass("gs-scroll-bar")}}class oi extends Zt{constructor(e,t,i,s,n=18){super(e),this._groupLength=4,this._scorllHeight=0,this._optionHeight=18,this._index=0,this._offetY=45+t,this._gap=s,this._scrollWidth=n,this._groupItemHeight=i,this._height=4*this._groupItemHeight+3*this._gap,this._topOption=new ni(this.eventBus,0),this._bottomOption=new ni(this.eventBus,1),this._topOption.appendTo(this.element),this._bottomOption.appendTo(this.element),this._scrollbar=new ri(this.eventBus,i,s,this._height,this._optionHeight),this._scrollbar.appendTo(this.element),this._init(),this._eventInit()}set groupLength(e){this._groupLength=e,this.index=0,this._groupLength<=4?this.styleInit({display:"none"}):(this.styleInit({display:"block"}),this._bottomOption.groupLength=e,this._topOption.groupLength=e,this._scrollbar.groupLength=e)}get index(){return this._index}set index(e){this._index=e,this._topOption.index=this._index,this._bottomOption.index=this._index,this._scrollbar.index=this._index}_init(){this.styleInit({width:this._scrollWidth+"px",height:this._height+4+"px",top:this._offetY+"px"}),this.addClass("gs-scroll-div")}_eventInit(){var e;null===(e=this.eventBus)||void 0===e||e.addEventListener("scroll",(e=>{var t;this.index+=e,null===(t=this.eventBus)||void 0===t||t.emit("scrollTo",this.index)}))}}class ai extends Jt{constructor(e,t,i){super(e),this.addClass("gs-timeline-poptext"),t&&this.addClass("gs-timeline-label-poptext"),this.eventInit(t,i),this.textDiv=new Jt(e),this.textDiv.appendTo(this.element);const s=new Jt(e);s.appendTo(this.element),s.addClass("poptext-arrow")}set show(e){this.styleInit({display:e?"inline-block":"none"})}set value(e){this.textDiv.element.innerText=e}eventInit(e,t){this.eventBus&&(this.eventBus.addEventListener(t||"hidePoptext",(()=>{this.show=!1})),this.eventBus.addEventListener(e||"showPoptext",((e,t,i=0)=>{this.value=t,t?(t.length>12||i)&&(this.show=!0,this.styleInit({top:e+"px",left:i+"px"})):this.show=!1})))}}class hi extends Zt{constructor(e,t){super(e),this.timeRangeTextArr=["30分钟","1小时","2小时","10小时","24小时","48小时+"],this.timeRangeIndex=4,this.timeLineGroup=t,this.styleInit({position:"absolute",top:"48px",right:"14px"}),this.reduceOption=new Zt(this.eventBus),this.reduceOption.addClass("gs-control-timeRangeReduce"),this.reduceOption.addClass("gs-timeline-range"),this.reduceOption.styleInit({display:"inline-block"}),this.reduceOption.appendTo(this.element),this.rangeOption=new Zt(this.eventBus,"input"),this.rangeOption.paramsInit({type:"range",id:"rangeOptionId",value:100/(this.timeRangeTextArr.length-1)*this.timeRangeIndex}),this.rangeOption.appendTo(this.element),this.rangeText=new Zt(this.eventBus,"span"),this.rangeText.styleInit({position:"relative",top:"-4px",fontSize:"12px",width:"50px",display:"inline-block",textAlign:"center"}),this.rangeText.element.innerHTML=this.timeRangeTextArr[this.timeRangeIndex],this.rangeText.appendTo(this.element),this.addOption=new Zt(this.eventBus),this.addOption.addClass("gs-control-timeRangeAdd"),this.addOption.addClass("gs-timeline-range"),this.addOption.styleInit({display:"inline-block"}),this.addOption.appendTo(this.element),this.eventInit()}eventInit(){this.reduceOption.element.addEventListener("click",(e=>{this.changeRange(1)})),this.rangeOption.element.addEventListener("mouseup",(e=>{const t=e.target;this.timeRangeIndex=Math.round(t.value/(100/(this.timeRangeTextArr.length-1))),t.value=Math.round(this.timeRangeIndex*(100/(this.timeRangeTextArr.length-1))),this.rangeText.element.innerHTML=this.timeRangeTextArr[this.timeRangeIndex],this.redrawTimeLine()})),this.addOption.element.addEventListener("click",(e=>{this.changeRange(2)}))}changeRange(e,t=!0){if(1===e){if(!this.timeRangeIndex)return;this.timeRangeIndex--}else{if(5==this.timeRangeIndex)return;this.timeRangeIndex++}t||(this.timeRangeIndex=this.timeLineGroup.timeGapRangeIndex),this.rangeOption.element.value=Math.round(20*this.timeRangeIndex),this.rangeText.element.innerHTML=this.timeRangeTextArr[this.timeRangeIndex],t&&this.redrawTimeLine()}redrawTimeLine(){const e=this.timeLineGroup,t=e.timeGapRange[e.timeGapRangeIndex];e.timeGapRangeIndex=this.timeRangeIndex;const i=e.timeGapRange.length-1;if(e.timeGapRangeIndex<0)return void(e.timeGapRangeIndex=0);if(e.timeGapRangeIndex>i)return void(e.timeGapRangeIndex=i);const s=e.timeGap,n=(e.width-e.nameWidth)/(e.timeRangeArr[e.timeGapRangeIndex]/e.timeGapRange[e.timeGapRangeIndex]);e.timeGap=n<55?55:n,e.offsetX=e.offsetX*(e.timeGap/s)*(t/e.timeGapRange[e.timeGapRangeIndex]),e.selectRecord&&e.groupItems.forEach((e=>{e.setSelectRecordTime()})),e.painter.drawTimePointByTime()}}class li extends Qt{constructor(e,t=0,i={}){if(super(e,"canvas"),this._groupLength=4,this.width=0,this.height=22,this.nameWidth=130,this.offsetY=50,this.offsetX=0,this.gap=4,this.currentIndex=0,this.currentTime="",this.groupItems=[],this.timeGapRange=[3,5,10,30,60,120],this.timeRangeArr=[30,60,120,600,1440,2880,5760],this.initTimeGap=60,this.timeGapRangeIndex=4,this.ctx=null,this.midX=0,this._timeGap=90,this.groupEvent=null,this._synchronous=!1,this._scrollIndex=0,this._selectRecord=!1,this.play=e=>{var t;null===(t=this.groupItems[null==e?this.currentIndex:e])||void 0===t||t.play()},this.stop=e=>{var t;null===(t=this.groupItems[null==e?this.currentIndex:e])||void 0===t||t.stop(),this.setPending(!1,e)},this.close=(e,t=!0)=>{var i;null===(i=this.groupItems[null==e?this.currentIndex:e])||void 0===i||i.close(t)},this.currentIndex=t,this._groupLength=i.groupLength||4,this.nameWidth=i.nameWidth||130,this.width=i.canvasWidth||0,this.showTimeRangeButton=i.showTimeRangeButton,this.timeGap=i.timeGap||90,this.offsetY=i.offsetY||50,this.timeGapRange=i.timeGapRange||this.timeGapRange,this.autoGetRecord=i.autoGetRecord,i.initTimeGap){const e=this.timeGapRange.indexOf(i.initTimeGap);this.timeGapRangeIndex=e>=0?e:0}else if(i.timeGapRange){const e=this.timeGapRange.indexOf(60);this.timeGapRangeIndex=e>=0?e:0}if(this.initTimeGap=this.timeGapRange[this.timeGapRangeIndex],this.showTimeRangeButton){const t=(this.width-this.nameWidth)/(this.timeRangeArr[this.timeGapRangeIndex]/this.initTimeGap);this.timeGap=t<55?55:t,this.adjustTimeRangeDiv=new hi(e,this)}this.selectTimeRectangleDiv=new Jt(e),this.addClass("gs-canvas"),this.element&&this.element instanceof HTMLCanvasElement&&(this.ctx=this.element.getContext("2d")),this.groupEvent=new ii(this),this.groupEvent.eventInit(),this.painter=new si(this),this._scroll=new oi(e,this.offsetY,this.height,this.gap),1==i.maxSplit&&this.paramsInit({height:this.offsetY+this.gap+this.height}),this._poptext=new ai(e),this.labelWrapper=new Jt(e),this.synchronousFlag=new Jt(e)}set timeGap(e){this._timeGap=e,this.groupItems.forEach((t=>{t.timeGap=e}))}get timeGap(){return this._timeGap}get currentTimeLine(){return this.groupItems[this.currentIndex]}get gapMinute(){return this.timeGapRange[this.timeGapRangeIndex]}set groupLength(e){if(e===this._groupLength)return;const t=this._groupLength;this._groupLength=e,this._scroll.groupLength=e,this._scrollIndex=0,this.painter.scrollIndex=0,this._groupLength>t?(this.groupItemsInit(t),this.groupItems.forEach((e=>e.scrollIndex=0))):(this.groupItems.forEach((e=>e.scrollIndex=0)),this.groupItems.splice(e,t),this.painter.timePonitInit()),1===this._groupLength?this.paramsInit({height:this.offsetY+this.gap+this.height}):this.paramsInit({height:this.offsetY+4*(this.gap+this.height)})}get groupLength(){return this._groupLength}set scrollIndex(e){this._scrollIndex=e,this.painter.scrollIndex=e,this.groupItems.forEach((t=>t.scrollIndex=e)),this.painter.timePonitInit()}get scrollIndex(){return this._scrollIndex}set synchronous(e){if(this._synchronous=e,e){const e=this.currentTime;this.groupItems.forEach((t=>{t.currentTime=e,t.showTime=e,t.rate=0,t.stop()}))}this.painter.drawSynchronousFlag(),this.painter.drawTimePointByTime()}get synchronous(){return this._synchronous}get synchronousStatus(){let e="none";return this.groupItems.forEach((t=>{e="play"==e||"play"==t.status?"play":"stop"==e||"stop"==t.status?"stop":"ready"==e||"ready"==t.status?"ready":"none"})),e}get firstPlayItem(){let e;return this.groupItems.forEach((t=>{"play"===t.status&&(void 0===e||t.index<e)&&(e=t.index)})),null==e?void 0:this.groupItems[e]}get selectRecord(){return this._selectRecord}set selectRecord(e){this._selectRecord=e,e||this.hideSelectRecord()}get selectRecordVisible(){let e=!1;return this.groupItems.forEach((t=>{e=e||t.selectRecordVisible})),e}get selectRecordMap(){const e=new Map;return this.groupItems.forEach((t=>{t.selectRecordVisible&&e.set(t.index,t.selectRecordTimeRange)})),e}appendTo(e){super.appendTo(e),this.paramsInit({width:e.offsetWidth}),this.element&&this.element instanceof HTMLCanvasElement&&(this.width=this.element.width),this.midX=this.width/2,this.groupItemsInit(),this._scroll.appendTo(e),this._poptext.appendTo(e),this.labelWrapper.addClass("gs-timeline-label-wrapper"),this.labelWrapper.appendTo(e);new ai(this.eventBus,"showLabelPoptext","hideLabelPoptext").appendTo(e),this.synchronousFlag.addClass("gs-icon-synchronous"),this.synchronousFlag.addClass("gs-timeline-synchronous"),this.synchronousFlag.appendTo(e),this.selectTimeRectangleDiv.appendTo(e),this.selectTimeRectangleDiv.addClass("gs-timeline-select-time-ractangle"),this.showTimeRangeButton&&this.adjustTimeRangeDiv&&this.adjustTimeRangeDiv.appendTo(e)}groupItemsInit(e,t){for(let i=e||0;i<(t||this._groupLength);i++){if(!(this.element instanceof HTMLCanvasElement))return;const e=new ti(this,i,this.painter.drawTimePointByTime,{height:this.height,nameWidth:this.nameWidth,offsetY:this.offsetY,gap:this.gap,autoGetRecord:this.autoGetRecord,timeGap:this.timeGap});e.currentChange(this.currentIndex),this.groupItems.push(e)}}setVideoRange(e,t){const i=this.groupItems[null==t?this.currentIndex:t];i.setVideoRange({rangeList:e,currentTime:this.currentTime,timeRange:this.timeGapRange[this.timeGapRangeIndex]}),this.synchronous&&"play"==this.synchronousStatus&&this.eventBus.emit("seek",t),this.painter.drawTimePointByTime(i.currentTime)}selectChange(e){this.currentIndex=e,this.groupItems.forEach((e=>e.currentChange(this.currentIndex)))}playAll(e){this.groupItems.forEach((t=>{"play"===e.get(t.index)&&t.play()}))}stopAll(){this.groupItems.forEach((e=>{e.stop()}))}closeAll(e=!0){this.groupItems.forEach((t=>{t.close(e)}))}setTitle(e,t){this.groupItems[null==t?this.currentIndex:t].setTitle(e)}getRecord(e,t){return this.groupItems[null==e?this.currentIndex:e].getRecord(t)}getGroupItem(e){return this.groupItems[null==e?this.currentIndex:e]}updateTime(e,t){const i=this.firstPlayItem,s=this.groupItems[t].getNextTimePoint(e);if(this.groupItems[t].getNextRecord(e),this.synchronous){if(i&&(this.groupItems[t].currentTime=e,i.index==t)){let t;if(this.painter.drawTimePointByTime(e),this.groupItems.forEach((s=>{if(s.showTime=e,"ready"==s.status||"stop"==s.status){s.checkSeekable(e)&&(s.currentTime=e,this.eventBus.emit("seek",s.index))}s.index!=i.index&&(t="play"==t||"play"==s.status?"play":"stop"==t||"stop"==s.status?"stop":"ready"==t||"ready"==s.status?"ready":"none")})),"play"!==t&&s!==e){const t=this.getMinNextTimePoint(e);if(t!==e)return this.currentTime=t,void this.eventBus.emit("seek")}}}else this.groupItems[t].currentTime=s,this.currentIndex==t&&this.painter.drawTimePointByTime(s),s!==e&&this.eventBus.emit("seek",t)}getMinNextTimePoint(e){let t;return this.groupItems.forEach((i=>{const s=i.getNextTimePoint(e);s!=e&&(t?new Date(t)>new Date(s)&&(t=s):t=s)})),t||e}wndChange(e,t){const i=this.groupItems[e],s=this.groupItems[t];this.groupItems[t]=i,this.groupItems[e]=s,i.index=t,s.index=e,i.setTitle(i.title),s.setTitle(s.title),this.currentIndex===e?this.selectChange(t):this.currentIndex===t&&this.selectChange(e)}setLabels(e,t){t=null==t?this.currentIndex:t;const i=this.groupItems[t];i.labels=e,i.drawLabels()}syncSeek(e){this.groupItems.forEach((t=>{t.seek(e)}))}getPending(e){return e=null==e?this.currentIndex:e,this.groupItems[e].pending}setPending(e,t){t=null==t?this.currentIndex:t,this.groupItems[t].pending=e}hideSelectRecord(){this.groupItems.forEach((e=>{e.hideSelectRecord()}))}getNextRecord(){const e=this.getGroupItem(),t=Ot(this.currentTime);if(e.dateMap.get(t))this.eventBus.trigger("seek");else{const i=Ct(new Date,"yyyy-MM-dd");if(new Date(t)>new Date(i))return void this.eventBus.trigger("seek");e.getNextRecord(this.currentTime)}}}class ui{constructor(e){this.dom=e}appendTo(e){e.append(this.dom)}}class ci extends Jt{constructor(e,t,i,{show:s=!0,disabled:n=!1,selected:r=!1,closeTitle:o="",closeName:a="",isSwitch:h=!1}={},l){super(e,"button"),this._show=!1,this._disabled=!0,this._selected=!1,this._closeTitle="",this._closeName="",this._isSwitch=!1,this.title="",this.name="play",this._show=s,this._disabled=n,this._selected=r,this._closeTitle=o,this._closeName=a,this._isSwitch=h,this.name=t,this.title=i,this.params=l,this.paramsInit({title:i,disabled:n}),this.styleReset(),this.eventInit()}set disabled(e){this._disabled=e,this.paramsInit({disabled:e})}get disabled(){return this._disabled}set show(e){this._show=e,this.styleReset()}get show(){return this._show}get selected(){return this._selected}set selected(e){this._selected=e,e&&this._closeTitle?this.paramsInit({title:this._closeTitle}):this.paramsInit({title:this.title}),this.styleReset()}removeBg(){this.removeClass(`gs-control-${this.name}-hover`),this.removeClass(`gs-control-${this.name}`)}styleReset(){this.removeBg(),this.addClass(this._selected?`gs-control-${this.name}-hover`:`gs-control-${this.name}`),this.addClass("gs-timeline-option-button"),this.styleInit({display:this._show?"inline-block":"none"})}eventInit(){var e;null===(e=this.element)||void 0===e||e.addEventListener("click",(()=>{var e,t,i;if(this.disabled)return;let s,n;this.selected&&this._closeName?[s,...n]=this._closeName.split("-"):[s,...n]=this.name.split("-");const r=null===(e=this.params)||void 0===e?void 0:e.map((e=>e instanceof Function?e():e));r?null===(t=this.eventBus)||void 0===t||t.emit(s,...n,...r):null===(i=this.eventBus)||void 0===i||i.emit(s,...n),this._isSwitch&&(this.selected=!this.selected)}))}}class di extends Zt{constructor(e,t,i){super(e,"button"),this.imgName=t,this.click=i,this.addClass("gs-seek-button"),this.addClass(`gs-control-${this.imgName}`),this.eventInit()}eventInit(){var e;null===(e=this.element)||void 0===e||e.addEventListener("click",(()=>{this.click()}))}}class pi extends Zt{constructor(e,t,i,s,n){super(e,"input"),this._value=0,this.max=t,this.min=i,this.flag=s,this.clickCallback=n,this.addClass("gs-seektime-input"),this.value=0,this.eventInit()}set value(e){this._value=e,this.paramsInit({value:String(e).padStart(2,"0"),type:"number",max:this.max,min:this.min})}get value(){return String(this._value).padStart(2,"0")}eventInit(){this.element.addEventListener("keydown",(e=>{})),this.element.addEventListener("input",(e=>{let t=e.target.value;const i=t.length;i>2&&(t=t.slice(i-2,i)),t>this.max&&(t=this.max),this.value=Number(t)})),this.element.addEventListener("click",(()=>{this.clickCallback(this.flag)}))}}class mi extends Zt{constructor(e){super(e),this.inputMap=new Map,this.flag=0,this.timeUp=()=>{const{max:e,min:t,value:i}=this.selectedInput;Number(i)+1>e?this.selectedInput.value=t:this.selectedInput.value=Number(i)+1},this.timeDown=()=>{const{max:e,min:t,value:i}=this.selectedInput;this.selectedInput.value=i-1<t?e:i-1},this.inputClick=e=>{this.flag=e},this.addClass("gs-seek-component"),this.secondInput=new pi(this.eventBus,59,0,0,this.inputClick),this.minuteInput=new pi(this.eventBus,59,0,1,this.inputClick),this.hourInput=new pi(this.eventBus,23,0,2,this.inputClick),this.init(),this.inputStyleInit(),this.buttonStyleInit()}get selectedInput(){return this.inputMap.get(this.flag)}get time(){return`${this.hourInput.value}:${this.minuteInput.value}:${this.secondInput.value}`}set showSeek(e){}init(){var e,t,i,s;this.inputWrapper=new Zt(this.eventBus),this.inputMap.set(2,this.hourInput),this.inputMap.set(1,this.minuteInput),this.inputMap.set(0,this.secondInput),this.hourInput.appendTo(this.inputWrapper.element),null===(t=null===(e=this.inputWrapper)||void 0===e?void 0:e.element)||void 0===t||t.append(":"),this.minuteInput.appendTo(this.inputWrapper.element),null===(s=null===(i=this.inputWrapper)||void 0===i?void 0:i.element)||void 0===s||s.append(":"),this.secondInput.appendTo(this.inputWrapper.element),this.upButton=new di(this.eventBus,"up",this.timeUp),this.downButton=new di(this.eventBus,"down",this.timeDown),this.buttonWrapper=new Zt(this.eventBus),this.upButton.appendTo(this.buttonWrapper.element),this.downButton.appendTo(this.buttonWrapper.element),this.seekOptionButton=new ci(this.eventBus,"showSeek","跳转到定位时间"),this.inputWrapper.appendTo(this.element),this.buttonWrapper.appendTo(this.element),this.seekOptionButton.appendTo(this.element)}inputStyleInit(){var e;null===(e=this.inputWrapper)||void 0===e||e.addClass("gs-seek-input")}buttonStyleInit(){var e;null===(e=this.buttonWrapper)||void 0===e||e.addClass("gs-seek-button-wrapper")}}class gi{constructor(e,t,i){this.show=!0,this.enable=!0,this.isSwitch=!1,this.value=e,this.label=t,this.isSwitch=!!i}}const _i={bookmark:new gi("bookmark","标签管理"),recordSearch:new gi("recordSearch","录像检索"),addBookmark:new gi("addBookmark","添加标签"),segmentedPlay:new gi("segmentedPlay","分段播放"),capture:new gi("capture","抓图"),customSplit:new gi("customSplit","自定义分屏"),synchronous:new gi("synchronous","同步播放",!0),selectRecord:new gi("selectRecord","框选时间轴",!0),seek:new gi("seek","时间点定位"),fullScreen:new gi("fullScreen","全屏",!0),closeAll:new gi("closeAll","关闭全部")};class fi{constructor(e=[]){const t=[];(e=Array.from(new Set(e))).forEach((e=>{"string"==typeof e?"string"==typeof(e=e[0].toLowerCase()+e.substr(1))&&t.push(_i[e]):("function"==typeof e||(e.isCustom=!0),t.push(e))})),this.menu=t}hasButton(e){for(let t of this.menu)if("function"!=typeof t&&t.value.toLocaleLowerCase()==e.toLocaleLowerCase())return!0;return!1}}class Si extends ci{constructor(e,t,i,s){super(e,t,i,s)}eventInit(){this.element.addEventListener("click",(()=>{var e,t;if(this.disabled)return;let i;i=this.selected&&this._closeName?this._closeName:this.name,this._isSwitch?(this.selected=!this.selected,null===(e=this.eventBus)||void 0===e||e.emit(i,this.selected)):null===(t=this.eventBus)||void 0===t||t.emit(i)}))}}class yi{constructor(e,t,i){this.splitList=[1,4,9,,16,25,36],this.eventBus=e,this.maxSplit=t,this.customSplit=i,this.oneSplitButton=new ci(this.eventBus,"split-1","一分屏",{show:this.maxSplit>1}),this.fourSplitButton=new ci(this.eventBus,"split-4","四分屏",{selected:!0,show:this.maxSplit>=4}),this.nineSplitButton=new ci(this.eventBus,"split-9","九分屏",{show:this.maxSplit>=9}),this.sixteenSplitButton=new ci(this.eventBus,"split-16","16分屏",{show:this.maxSplit>=16}),this.twentyFiveSplitButton=new ci(this.eventBus,"split-25","25分屏",{show:this.maxSplit>=25}),this.thirtySixSplitButton=new ci(this.eventBus,"split-36","36分屏",{show:this.maxSplit>=36}),this.customSplitButtom=new ci(this.eventBus,"customSplit","自定义分屏",{show:i}),this.buttonList=[this.oneSplitButton,this.fourSplitButton,this.nineSplitButton,this.sixteenSplitButton,this.twentyFiveSplitButton,this.thirtySixSplitButton]}set wndNums(e){this.buttonList.forEach(((t,i)=>{t.selected=Math.pow(i+1,2)===e})),!this.splitList.includes(e)&&this.customSplit?this.customSplitButtom.selected=!0:this.customSplit&&(this.customSplitButtom.selected=!1)}appendTo(e){e.push(...this.buttonList),this.customSplit&&e.push(this.customSplitButtom)}}class vi extends Zt{constructor(e,t,i,s,n){var r;super(e,"li"),this.imgName=t,this.icon=new Zt(e),this.diff=s,this.seekElement=n,this.addClass("gs-seek-option-item"),this.iconInit(),this.eventInit(),null===(r=this.element)||void 0===r||r.append(i)}removeBg(){this.removeClass(`gs-control-${this.imgName}-hover`),this.removeClass(`gs-control-${this.imgName}-normal`),this.removeClass("gs-control-seek-hover")}iconInit(){this.icon.appendTo(this.element)}eventInit(){var e;null===(e=this.element)||void 0===e||e.addEventListener("click",(()=>{var e,t,i;null===(e=this.eventBus)||void 0===e||e.emit("hideSeek"),null===(t=this.eventBus)||void 0===t||t.emit("seek",void 0,this.diff);let s=Math.abs(this.diff),n=s<1?`${Math.round(60*s)}s`:`${s}m`;(null===(i=this.seekElement.previousSibling)||void 0===i?void 0:i.childNodes[0]).innerHTML=n,this.seekElement.childNodes[0].childNodes.forEach((e=>{e.classList.remove("gs-control-seek-hover")})),this.addClass("gs-control-seek-hover")}))}}class wi extends Zt{constructor(e,t,i=!0){super(e),this._show=!1,this.inputValue=5,this.isBackSeek=!0,this.isCustomSeek=!1,this.customNeedSeek=!1,this.back5sLi=new vi(e,"back5s","倒退5秒",-1/12,t),this.back10sLi=new vi(e,"back10s","倒退10秒",-1/6,t),this.back5mLi=new vi(e,"back5m","倒退5分钟",-5,t),this.back10mLi=new vi(e,"back10m","倒退10分钟",-10,t),this.next5sLi=new vi(e,"back5s","前进5秒",1/12,t),this.next10sLi=new vi(e,"back10s","前进10秒",1/6,t),this.next5mLi=new vi(e,"back5m","前进5分钟",5,t),this.next10mLi=new vi(e,"back10m","前进10分钟",10,t),this.seekElement=t,this.isBackSeek=i,this.initCustomSeek(),i?this.backButtonInit():this.nextButtonInit(),this.addClass("gs-seek-option"),this.back5sLi.addClass("gs-control-seek-hover"),this.next5sLi.addClass("gs-control-seek-hover"),this.eventInit()}set show(e){e&&(this.customNeedSeek=!0),this._show=!0,this.styleInit({display:e?"block":"none"})}get show(){return this._show}backButtonInit(){const e=new Zt(this.eventBus,"div");e.addClass("gs-line"),this.back5sLi.appendTo(this.element),this.back10sLi.appendTo(this.element),this.back5mLi.appendTo(this.element),this.back10mLi.appendTo(this.element),e.appendTo(this.element),this.customSeekDiv.appendTo(this.element)}nextButtonInit(){const e=new Zt(this.eventBus,"div");e.addClass("gs-line"),this.next5sLi.appendTo(this.element),this.next10sLi.appendTo(this.element),this.next5mLi.appendTo(this.element),this.next10mLi.appendTo(this.element),e.appendTo(this.element),this.customSeekDiv.appendTo(this.element)}removeBackSeekBg(){this.back5sLi.removeBg(),this.back10sLi.removeBg(),this.back5mLi.removeBg(),this.back10mLi.removeBg()}removeNextSeekBg(){this.next5sLi.removeBg(),this.next10sLi.removeBg(),this.next5mLi.removeBg(),this.next10mLi.removeBg()}initCustomSeek(){this.customSeekDiv=new Zt(this.eventBus,"div"),this.customSeekDiv.addClass("gs-custom-seek");let e=new Zt(this.eventBus,"span");e.element.innerHTML=this.isBackSeek?"倒退":"前进",e.appendTo(this.customSeekDiv.element),this.inputButton=new Zt(this.eventBus,"input"),this.inputButton.paramsInit({type:"number",value:5}),this.inputButton.addClass("gs-seek-input"),this.inputButton.styleInit({display:"inline",width:"60px",margin:"0 5px"}),this.inputButton.appendTo(this.customSeekDiv.element);let t=new Zt(this.eventBus,"span");t.element.innerHTML="秒",t.appendTo(this.customSeekDiv.element)}eventInit(){document.addEventListener("click",(e=>{var t,i;if(e.target!=this.element&&e.target!=this.seekElement&&e.target!=this.inputButton.element){if(this.seekElement.childNodes[0].childNodes.forEach((e=>{let t=[...e.classList];t.includes("gs-control-seek-hover")&&!t.includes("gs-custom-seek")&&(this.isCustomSeek=!1)})),this.isCustomSeek&&this.customNeedSeek){this.customNeedSeek=!1;let e=this.inputValue,s=e<60?`${e}s`:`${(e/60).toFixed(1)}m`;(null===(t=this.seekElement.previousSibling)||void 0===t?void 0:t.childNodes[0]).innerHTML=s,null===(i=this.eventBus)||void 0===i||i.emit("seek",void 0,(this.isBackSeek?-e:e)/60)}this.show=!1}})),this.inputButton.element.addEventListener("keypress",(e=>{var t,i;if("NumpadEnter"===e.code){this.customNeedSeek=!1;let s=e.target.value,n=s<60?`${s}s`:`${(s/60).toFixed(1)}m`;(null===(t=this.seekElement.previousSibling)||void 0===t?void 0:t.childNodes[0]).innerHTML=n,this.show=!1,null===(i=this.eventBus)||void 0===i||i.emit("seek",void 0,(this.isBackSeek?-s:s)/60)}})),this.inputButton.element.addEventListener("input",(e=>{e.target.value<0?e.target.value=1:e.target.value>1e4&&(e.target.value=1e4),this.inputValue=e.target.value})),this.customSeekDiv.element.addEventListener("click",(e=>{this.isCustomSeek=!0,this.isBackSeek?this.removeBackSeekBg():this.removeNextSeekBg(),this.customSeekDiv.addClass("gs-control-seek-hover")}))}}class Ti{constructor(e){this.backTimeText="5s",this.nextTimeText="5s",this._rate=0,this._status="none",this._enable=!1,this.eventBus=e,this.playButton=new Si(e,"play","播放",{disabled:!0}),this.pauseButton=new Si(e,"stop","暂停",{show:!1}),this.fastButton=new Si(e,"fast","快放",{disabled:!0}),this.slowButton=new Si(e,"slow","慢放按钮",{disabled:!0}),this.stopButton=new Si(e,"close","停止",{disabled:!0}),this.framePreviousButton=new Si(e,"framePrevious","帧退",{disabled:!0,show:!1}),this.frameNextButton=new Si(e,"frameNext","帧退",{disabled:!0,show:!1}),this.resetButton=new Si(e,"reset","重置倍速",{disabled:!0}),this.initSeekButton(),this.rateSpan=new Zt(e,"span"),this.rateSpan.addClass("gs-rate-span"),this.rateSpan.styleInit({display:0===this._rate?"none":"inline"}),this.rateSpan.element.innerHTML=`${Math.pow(2,this._rate)}.0X`,this.playButtonList=[this.stopButton,this.backButton,this.backSelectButton,this.slowButton,this.framePreviousButton,this.playButton,this.pauseButton,this.frameNextButton,this.fastButton,this.nextButton,this.nextSelectButton,this.resetButton,this.rateSpan]}get rate(){return this._rate}get status(){return this._status}set rate(e){var t;this._rate=e,null===(t=this.rateSpan)||void 0===t||t.styleInit({display:0===this._rate?"none":"inline"}),this.rateSpan.element.innerHTML=e>=0?`${Math.pow(2,this._rate)}.0X`:`1/${Math.pow(2,Math.abs(e))}X`,this.enableRate=this._enable}set status(e){switch(this._status=e,e){case"none":this.enableRate=!1,this.playButton.show=!0,this.pauseButton.show=!1,this.playButton.disabled=!0,this.frameNextButton.disabled=!0,this.framePreviousButton.disabled=!0,this.fastButton.disabled=!1,this.slowButton.disabled=!1,this.stopButton.disabled=!0,this.resetButton.disabled=!0,this.rate=0;break;case"ready":case"stop":this.enableRate=!1,this.playButton.show=!0,this.pauseButton.show=!1,this.playButton.disabled=!1,this.stopButton.disabled=!1,this.fastButton.disabled=!1,this.slowButton.disabled=!1;break;case"play":this.enableRate=!0,this.playButton.show=!1,this.pauseButton.show=!0,this.pauseButton.disabled=!1,this.fastButton.disabled=!1,this.slowButton.disabled=!1,this.stopButton.disabled=!1,this.frameNextButton.disabled=!1,this.framePreviousButton.disabled=!1}}set enableRate(e){this._enable=e,this.fastButton&&(this.fastButton.disabled=!e||6==this.rate),this.slowButton&&(this.slowButton.disabled=!e||-6==this.rate),this.resetButton&&(this.resetButton.disabled=!e||0==this.rate)}appendTo(e){e.unshift(...this.playButtonList)}initSeekButton(){this.backButton=new Si(this.eventBus,"back","倒退",{disabled:!1}),this.backButton.styleInit({marginRight:"-1px"}),this.backSelectButton=new Si(this.eventBus,"backSelect","倒退时间选择",{disabled:!1}),this.backSelectButton.styleInit({width:"20px",margin:0});let e=new Zt(this.eventBus,"span");e.addClass("gs-seek-span"),e.element.innerHTML=this.backTimeText,e.appendTo(this.backButton.element),this.backSeekOption=new wi(this.eventBus,this.backSelectButton.element),this.backSeekOption.appendTo(this.backSelectButton.element),this.nextButton=new Si(this.eventBus,"next","前进",{disabled:!1}),this.nextButton.styleInit({marginRight:"-1px"}),this.nextSelectButton=new Si(this.eventBus,"nextSelect","前进时间选择",{disabled:!1}),this.nextSelectButton.styleInit({width:"20px",margin:0});let t=new Zt(this.eventBus,"span");t.addClass("gs-seek-span"),t.element.innerHTML=this.nextTimeText,t.styleInit({width:"20px"}),t.appendTo(this.nextButton.element),this.nextSeekOption=new wi(this.eventBus,this.nextSelectButton.element,!1),this.nextSeekOption.styleInit({left:"210px"}),this.nextSeekOption.appendTo(this.nextSelectButton.element)}quickSeek(e=!0){var t;const i=(e?this.backButton.element.childNodes[0]:this.nextButton.element.childNodes[0]).innerText,s=i.includes("s")?+i.split("s")[0]/60:i.split("m")[0];null===(t=this.eventBus)||void 0===t||t.emit("seek",void 0,e?-s:+s)}set showBackSeek(e){this.backSeekOption.show=e}set showNextSeek(e){this.nextSeekOption.show=e}}class Ii extends Jt{constructor(e){super(e),this.buttonDiv=null,this.optionWrapper=new Jt(e),this.downloadOption=new Jt(e),this.lockOption=new Jt(e),this.optionWrapper.isFlex=!0,this.initStyle(),this.hideOption(),this.append(this.optionWrapper),this.optionWrapper.append(this.downloadOption),this.optionWrapper.append(this.lockOption),this.eventInit()}initStyle(){this.addClass("gs-select-record-option"),this.downloadOption.addClass("gs-control-download"),this.lockOption.addClass("gs-control-lock"),this.optionWrapper.addClass("gs-select-record-option-wrapper"),this.downloadOption.addClass("gs-select-record-option-img"),this.lockOption.addClass("gs-select-record-option-img"),this.downloadOption.paramsInit({title:"下载录像"}),this.lockOption.paramsInit({title:"锁定录像"})}showOption(){this.optionWrapper.visible=!0}hideOption(){this.optionWrapper.visible=!1}eventInit(){this.downloadOption.listen("click",(()=>{console.log("download click"),this.eventBus.emit("selectRecordDownload")})),this.lockOption.listen("click",(()=>{console.log("lock click"),this.eventBus.emit("selectRecordLock")})),this.eventBus.listen("showSelectRecordOption",(e=>{e?this.showOption():this.hideOption()}))}before(e){this.buttonDiv=e,this.optionWrapper.element.before(e.element)}updateByAuthOption(e=[]){const t=xt("record","Download",e),i=xt("record","Lock",e);t||i||!this.buttonDiv?this.buttonDiv&&(this.buttonDiv.show=!0,this.downloadOption.visible=t,this.lockOption.visible=i):this.buttonDiv.show=!1}}class Ei extends Zt{constructor(e,t,i){super(e),this.maxSplit=9,this._synchronous=!1,this.leftGroup=[],this.rightGroup=[],this.midGroup=[],this.elementMap={},this.selectRecordOption=null,this.addClass("gs-timeline-option"),this.timeLineGroup=t;let s=!1;if(i){this.maxSplit=i.maxSplit||this.maxSplit,this._synchronous=!!i.synchronous;const e=i.timeLineMenuKeys;if(e){const t=new fi(e);this.menuArr=t.menu,s=t.hasButton("customSplit")}}this.childrenInit(),this.seekComponent=new mi(this.eventBus),this._keys=null==i?void 0:i.timeLineMenuKeys,this.splitButtonGroup=new yi(e,this.maxSplit,s),this.playButtonGroup=new Ti(e),this.buttonInit(),this.eventInit()}set rate(e){this.playButtonGroup.rate=e}get rate(){return this.playButtonGroup.rate}set showSeek(e){var t;null===(t=this.eventBus)||void 0===t||t.emit("seek",void 0,0)}set showBackSeek(e){this.playButtonGroup.showBackSeek=e}set showNextSeek(e){this.playButtonGroup.showNextSeek=e}get positionTime(){return this.seekComponent.time}set enableRate(e){this.playButtonGroup.enableRate=e}set status(e){this.playButtonGroup.status=e}get status(){return this.playButtonGroup.status}get synchronous(){var e;return!!(null===(e=this.synchronousButton)||void 0===e?void 0:e.selected)}set fullScreen(e){this.fullScreenButton&&(this.fullScreenButton.selected=e)}set wndNums(e){this.splitButtonGroup.wndNums=e}get selectRecord(){var e;return null===(e=this.selectRecordButton)||void 0===e?void 0:e.selected}quickSeek(e=!0){this.playButtonGroup.quickSeek(e)}childrenInit(){this.midWrapperDiv=new Zt(this.eventBus,"div"),this.leftWrapperDiv=new Zt(this.eventBus,"div"),this.rightWrapperDiv=new Zt(this.eventBus,"div"),this.leftWrapperDiv.addClass("gs-left-wrapper"),this.rightWrapperDiv.addClass("gs-right-wrapper"),this.midWrapperDiv.addClass("gs-mid-wrapper"),this.midWrapperDiv.appendTo(this.element),this.leftWrapperDiv.appendTo(this.element),this.rightWrapperDiv.appendTo(this.element)}buttonInit(){this.splitButtonGroup.appendTo(this.rightGroup),this.menuArr&&this.menuArr.forEach((e=>{if("function"==typeof e){if(!Zt.Vue)return;const t=(new(Zt.Vue.extend({render:e}))).$mount().$el;this.leftGroup.push(new ui(t))}else{const t=e.value.toLocaleLowerCase();if("synchronous"===t)this.synchronousButton=new Si(this.eventBus,e.value,e.label,{selected:this._synchronous,isSwitch:e.isSwitch}),this.midGroup.push(this.synchronousButton);else if("selectrecord"===t){this.selectRecordButton=new Si(this.eventBus,e.value,e.label,{isSwitch:e.isSwitch});const t=new Ii(this.eventBus);this.selectRecordOption=t,t.before(this.selectRecordButton),this.leftGroup.push(t)}else if("customsplit"===t);else if("seek"===t)this._keys.includes("capture")||this.seekComponent.appendTo(this.leftWrapperDiv.element);else if("fullscreen"===t)this.fullScreenButton=new ci(this.eventBus,"fullScreen","全屏",{closeName:"closeFullScreen",closeTitle:"关闭全屏"}),this.rightGroup.push(this.fullScreenButton);else if("closeall"===t){const e=new ci(this.eventBus,"closeAll","关闭所有");this.rightGroup.push(e)}else{const t=new Si(this.eventBus,e.value,e.label,{isSwitch:e.isSwitch});this.leftGroup.push(t),["addBookmark"].indexOf(e.value)>-1&&(this.elementMap[e.value]=t)}}})),this.playButtonGroup.appendTo(this.midGroup),this.leftGroup.forEach((e=>{e.appendTo(this.leftWrapperDiv.element)})),this.rightGroup.forEach((e=>{e.appendTo(this.rightWrapperDiv.element)})),this.midGroup.forEach((e=>{e.appendTo(this.midWrapperDiv.element)}))}updateByAuthOption(e){Object.keys(this.elementMap).forEach((t=>{if("addBookmark"==t){const i=xt("record","LabelManage",e);this.elementMap[t].show=i}})),this.selectRecordOption&&this.selectRecordOption.updateByAuthOption(e)}eventInit(){this.eventBus.addEventListener("statusChange",(e=>{if(this.synchronous){const e=this.timeLineGroup.synchronousStatus;this.status!==e&&(this.status=e)}else this.status!==e&&(this.status=e)})),this.eventBus.addEventListener("rateChange",(e=>this.rate=e))}}class Ri extends Zt{constructor(e){super(e,"canvas"),this.ctx=null,this.startTime="1970-01-01 00:00:00",this.endTime="1970-01-01 00:00:00",this.processTime="1970-01-01 00:00:00",this.timePerPixel=0,this.width=0,this.isMousedown=!1,this.onResize=()=>{this.targetDiv&&(this.targetDiv.offsetWidth&&this.paramsInit({width:this.targetDiv.offsetWidth}),this.element&&this.element instanceof HTMLCanvasElement&&(this.width=this.element.width),this.draw(0))},this.eventBus=e,this.addClass("timeline-simple"),this.styleInit({backgroundColor:"white",position:"relative",bottom:"-4px"}),this.paramsInit({height:20}),this.initIcon(e),this.timelineSimpleEventInit(),this.element&&this.element instanceof HTMLCanvasElement&&(this.ctx=this.element.getContext("2d"))}initTime({startTime:e,endTime:t}){this.startTime=e||this.startTime,this.endTime=t||this.endTime,this.processTime=this.startTime,this.timePerPixel=(new Date(`${this.endTime}`).valueOf()-new Date(`${this.startTime}`).valueOf())/(this.width-145),this.draw(0)}initIcon(e){this.rewindImg=new Zt(e,"i"),this.rewindImg.paramsInit({title:"快退30秒"}),this.rewindImg.addClass("gs-control-slow"),this.rewindImg.addClass("gs-timeline-simple-icon"),this.forwardImg=new Zt(e,"i"),this.forwardImg.paramsInit({title:"快进30秒"}),this.forwardImg.addClass("gs-control-fast"),this.forwardImg.addClass("gs-timeline-simple-icon"),this.forwardImg.styleInit({right:0}),this.iconEventInit()}timelineSimpleEventInit(){this.element.onmousedown=e=>{let t;this.isMousedown=!0,this.dragTime(e),document.onmousemove=e=>{clearTimeout(t),t=window.setTimeout((()=>{this.dragTime(e)}))},document.onmouseup=()=>{var e;document.onmousemove=null,document.onmouseup=null,null===(e=this.eventBus)||void 0===e||e.emit("simpleSeek"),this.isMousedown=!1}};const e=this.onResize;let t;window.addEventListener("resize",(function(){t&&clearTimeout(t),t=setTimeout((function(){e(),t=null}),100)}))}dragTime(e){let t=e.offsetX-20;if(t<0||t>this.width-145)return;let i=new Date(new Date(`${this.startTime}`).valueOf()+t*this.timePerPixel);this.processTime=this.dateFormat(i,"yyyy-MM-dd hh:mm:ss"),this.draw(t)}iconEventInit(){this.rewindImg.element.onclick=()=>{var e;if(this.processTime==this.startTime)return;const t=new Date(new Date(`${this.processTime}`).valueOf()-3e4);let i=this.dateFormat(t,"yyyy-MM-dd hh:mm:ss"),s=0;i<=this.startTime?this.processTime=this.startTime:(this.processTime=i,s=(new Date(`${i}`).valueOf()-new Date(`${this.startTime}`).valueOf())/this.timePerPixel),null===(e=this.eventBus)||void 0===e||e.emit("simpleSeek"),this.draw(s)},this.forwardImg.element.onclick=()=>{var e;if(this.processTime==this.endTime)return;let t=new Date(new Date(`${this.processTime}`).valueOf()+3e4),i=this.dateFormat(t,"yyyy-MM-dd hh:mm:ss"),s=this.width-145;i>=this.endTime?this.processTime=this.endTime:(this.processTime=i,s=(new Date(`${i}`).valueOf()-new Date(`${this.startTime}`).valueOf())/this.timePerPixel),null===(e=this.eventBus)||void 0===e||e.emit("simpleSeek"),this.draw(s)}}appendTo(e){this.targetDiv=e,this.rewindImg.appendTo(e),super.appendTo(e),this.forwardImg.appendTo(e),this.paramsInit({width:e.offsetWidth}),this.element&&this.element instanceof HTMLCanvasElement&&(this.width=this.element.width),this.draw(0)}draw(e){this.clear(),this.drawArea(this.width-145,"#dcdfe6"),this.drawArea(e,"#3d84ee"),this.drawText()}updateTime(e){if(!this.isMousedown)if(e>=this.endTime)this.processTime=this.endTime,this.draw(this.width-145);else{this.processTime=e;let t=(new Date(`${e}`).valueOf()-new Date(`${this.startTime}`).valueOf())/this.timePerPixel;this.draw(t)}}clear(){this.ctx&&this.ctx.clearRect(0,0,this.element.offsetWidth,this.element.offsetHeight)}drawArea(e,t){this.ctx&&(this.ctx.beginPath(),this.ctx.rect(20,5,e,7),this.ctx.fillStyle=t,this.ctx.fill())}drawText(){this.ctx&&(this.ctx.clearRect(this.width-70,13,this.element.offsetWidth,this.element.offsetHeight),this.ctx.beginPath(),this.ctx.font="12px Arial",this.ctx.fillStyle="black",this.ctx.textAlign="center",this.ctx.textBaseline="alphabetic",this.ctx.fillText(`${this.processTime.split(" ")[1]} /${this.endTime.split(" ")[1]}`,this.width-70,13))}dateFormat(e,t){if(e instanceof Date){var i={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};for(var s in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length))),i)new RegExp("("+s+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?i[s]:("00"+i[s]).substr((""+i[s]).length)));return t}return e}}var Di;!function(e){e.UNSUPPORTH265="浏览器不支持h265视频"}(Di||(Di={}));class bi extends Map{getFunc(e){return this.get(e)||(()=>{})}}class Pi{constructor(e,t,i,s,n){this.eventMap=new bi,this.eventBus=e,this.eventMap=t,this.timeLineGroup=i,this.timeLineOption=s,this.timeLineSimple=n}get synchronous(){return this.timeLineGroup.synchronous}get groupItems(){return this.timeLineGroup.groupItems}eventInit(){this.listenPlay(),this.listenStop(),this.listenClose(),this.listenChange(),this.listenSeek(),this.listenSimpleSeek(),this.listenSplit(),this.listenFast(),this.listenSlow(),this.listenReset(),this.listenShowSeek(),this.listenQuickSeek(),this.listenSeekSelect(),this.listenVideoEnd(),this.listenLabelClick(),this.listenSynchronous(),this.listenDefaultEvent(),this.listenSelectRecord(),this.listenSelectRecordDownload(),this.listenSelectRecordLock(),this.listenGetNextRecord()}listenEvent(e,t,i=(e=>{})){this.eventBus.addEventListener(e,(()=>{console.log(e,t);const s=this.eventMap.get(t);s&&s().then((e=>{i(e)}))}))}listenPlay(){this.eventBus.addEventListener("play",(()=>{console.log("play");const e=this.timeLineGroup.currentTime,t=t=>{t=null==t?this.timeLineGroup.currentIndex:t;const i=this.timeLineGroup.getRecord(t,this.timeLineGroup.currentTime),s=this.timeLineGroup.groupItems[t].rangeList.slice(-1)[0];if(i&&s&&s.rcdEndTime){const n=s.rcdEndTime;if(this.timeLineGroup.getPending(t))return;this.timeLineGroup.setPending(!0,t);const r=this.eventMap.get("onPlay");r&&r(i,e,n,t).then((()=>{this.timeLineGroup.play(t)})).finally((()=>{this.timeLineGroup.setPending(!1,t)}))}else{const e=this.eventMap.get("onPause");e&&e(t).then((()=>{this.timeLineGroup.stop(t)}))}};this.synchronous?this.timeLineGroup.groupItems.forEach((e=>{t(e.index)})):t()}))}listenStop(){this.eventBus.addEventListener("stop",(e=>{console.log("stop");const t=e=>{const t=this.eventMap.get("onPause");t&&(this.timeLineGroup.stop(e),t(e))};this.synchronous?this.timeLineGroup.groupItems.forEach((e=>{t(e.index)})):t(e)}))}listenChange(){this.eventBus.addEventListener("currentChange",(e=>{console.log("change");const t=this.eventMap.get("onChange");t&&t(e)}))}listenSeek(){this.eventBus.addEventListener("seek",((e,t,i=!1)=>{console.log("seek",e);const s=this.synchronous,n=this.eventMap.get("onSeekError"),r=this.eventMap.get("onVideoEnd"),o=this.eventMap.get("onSeek"),a=this.timeLineGroup.currentTime,h=e=>{const h=this.timeLineGroup.getGroupItem(e);if(!h.supportH265||h.pending)return!1;let l=a,u=h.checkSeekable(l);(s||u)&&(h.currentTime=l,h.showTime=l);const c=h.getRecord();if(void 0!==t){const e=this.timeLineOption.positionTime;l=0==t?(null==l?void 0:l.split(" ")[0])+" "+e:$t(new Date(new Date(l).getTime()+60*t*1e3))}if(!u&&this.timeLineSimple.targetDiv&&(l=h.rangeList[0].rcdStartTime,u=h.checkSeekable(l)),!u&&!s)return void n(l);if(!u&&s)return h.stop(),n(l),void r(e);console.log(l);if(this.timeLineGroup.getPending(e))return;this.timeLineGroup.setPending(!0,e);const d=(h.rangeList.slice(-1)[0]||{}).rcdEndTime;o(c,l,d,Math.pow(2,h.rate),e,i).then((()=>{h.play()})).catch((e=>{h.close(!1),e==Di.UNSUPPORTH265&&(h.supportH265=!1)})).finally((()=>{this.timeLineGroup.setPending(!1,e)}))};s&&void 0===e?this.timeLineGroup.groupItems.forEach((e=>{e.supportH265&&e.rangeList.length&&!e.pending?h(e.index):(e.currentTime=a,e.showTime=a)})):h(e)}))}listenSimpleSeek(){this.eventBus.addEventListener("simpleSeek",(()=>{console.log("simpleSeek");const e=this.timeLineGroup.getGroupItem(0),t=e.getRecord();let i=this.timeLineSimple.processTime;const s=(e.rangeList.slice(-1)[0]||{}).rcdEndTime,n=this.eventMap.get("onSeek"),r=this.eventMap.get("onSeekError");if(!e.checkSeekable(i))return r(i);n&&n(t,`${i}`,s,Math.pow(2,e.rate),0).then((()=>{e.play()})).catch((()=>{r(i)}))}))}listenSplit(){this.eventBus.addEventListener("split",(e=>{console.log("split");const t=this.eventMap.getFunc("onSplit");t&&t(e).then((()=>{this.timeLineOption.wndNums=+e,this.timeLineGroup.groupLength=+e}))}))}listenFast(){const e=e=>{const t=this.eventMap.get("onFast");let i=this.timeLineOption.rate;if(i>=-6&&i<6&&(i+=1),6==i||-6==this.timeLineOption.rate){const t=this.timeLineGroup.getGroupItem(e);return this.timeLineOption.rate=i,t.rate=i,void(this.timeLineOption.enableRate=!0)}if(t){const s=this.timeLineGroup.getGroupItem(e);this.timeLineOption.rate=i,s.rate=i,t(Math.pow(2,i),e).then((()=>{this.timeLineOption.enableRate=!0})).catch((()=>{this.timeLineOption.enableRate=!0}))}};this.eventBus.addEventListener("fast",(()=>{console.log("fast"),this.timeLineOption.enableRate=!1,this.synchronous?this.groupItems.forEach((t=>{e(t.index)})):e()}))}listenSlow(){const e=e=>{const t=this.eventMap.get("onSlow");let i=this.timeLineOption.rate;if(i>-6&&i<=6&&(i-=1),-6==i||6==this.timeLineOption.rate){const t=this.timeLineGroup.getGroupItem(e);return this.timeLineOption.rate=i,t.rate=i,void(this.timeLineOption.enableRate=!0)}if(t){const s=this.timeLineGroup.getGroupItem(e);this.timeLineOption.rate=i,s.rate=i,t(Math.pow(2,i),e).then((()=>{this.timeLineOption.enableRate=!0})).catch((()=>{this.timeLineOption.enableRate=!0}))}};this.eventBus.addEventListener("slow",(()=>{console.log("slow"),this.timeLineOption.enableRate=!1,this.synchronous?this.groupItems.forEach((t=>{e(t.index)})):e()}))}listenReset(){const e=e=>{const t=this.eventMap.get("onReset"),i=this.timeLineGroup.getGroupItem(e);t&&(i.rate=0,t(e))};this.eventBus.addEventListener("reset",(()=>{this.synchronous?this.groupItems.forEach((t=>{e(t.index)})):e(),this.timeLineOption.rate=0}))}listenShowSeek(){this.eventBus.addEventListener("showSeek",(()=>{console.log("showSeek"),this.timeLineOption.showSeek=!0}))}listenQuickSeek(){this.eventBus.addEventListener("back",(()=>{this.timeLineOption.quickSeek()})),this.eventBus.addEventListener("next",(()=>{this.timeLineOption.quickSeek(!1)}))}listenSeekSelect(){this.eventBus.addEventListener("backSelect",(()=>{this.timeLineOption.showBackSeek=!0})),this.eventBus.addEventListener("nextSelect",(()=>{this.timeLineOption.showNextSeek=!0}))}listenVideoEnd(){this.eventBus.addEventListener("videoEnd",(e=>{console.log("videoEnd");const t=this.eventMap.get("onVideoEnd");t&&t(e).then((()=>{this.timeLineGroup.stop(e)}))}))}listenLabelClick(){this.eventBus.addEventListener("labelClick",(e=>{console.log("onLabelClick");const t=this.eventMap.get("onLabelClick");t&&t(e)}))}listenSynchronous(){this.eventBus.addEventListener("synchronous",(e=>{const t=this.timeLineOption.status;if(this.timeLineGroup.synchronous=e,e){this.timeLineOption.rate=0;this.eventMap.get("onPauseAll")().then((()=>{"play"===t&&this.eventBus.emit("seek")}))}}))}listenSelectRecord(){this.eventBus.addEventListener("selectRecord",(e=>{console.log(e),this.timeLineGroup.selectRecord=e,e||this.eventBus.emit("showSelectRecordOption",!1)}))}listenSelectRecordDownload(){this.eventBus.addEventListener("selectRecordDownload",(()=>{const e=this.eventMap.get("onSelectRecordDownload");if(e){e(this.timeLineGroup.selectRecordMap)}}))}listenSelectRecordLock(){this.eventBus.addEventListener("selectRecordLock",(()=>{const e=this.eventMap.get("onSelectRecordLock");if(e){e(this.timeLineGroup.selectRecordMap)}}))}listenDefaultEvent(){const e=this.timeLineOption.menuArr;e&&e.forEach((e=>{if("function"==typeof e)return;if(["seek","fullScreen","closeAll"].includes(e.value))return;const t="on"+(e.value[0].toUpperCase()+e.value.substr(1));this.listenEvent(e.value,t)})),this.listenEvent("fullScreen","onFullScreen"),this.listenEvent("closeAll","onCloseAll"),this.listenEvent("closeFullScreen","onCloseFullScreen"),this.listenEvent("customSplit","onCustomSplit")}listenClose(){this.eventBus.addEventListener("close",(()=>{const e=this.eventMap.get("onClose"),t=this.eventMap.get("onCloseAll");this.synchronous?t():e(this.timeLineGroup.currentTimeLine.index,!0)}))}listenGetNextRecord(){this.eventBus.addEventListener("getNextRecord",((e,t)=>{this.eventMap.get("onGetNextRecord")(e,t)}))}}class Bi extends Zt{constructor(e,t){super(new ve),this.playerDiv=null,this.currentIndex=0,this.eventMap=new bi,this.titleWidth=130,this.playerDiv=document.getElementById(e),t&&(this.titleWidth=t.titleWidth||this.titleWidth),this.timeLineGroup=new li(this.eventBus,this.currentIndex,{nameWidth:this.titleWidth,maxSplit:t.maxSplit,timeGapRange:t.timeGapRange,initTimeGap:t.initTimeGap,offsetY:t.offsetY,timeGap:t.timeGap,autoGetRecord:t.autoGetRecord,canvasWidth:this.playerDiv&&this.playerDiv.offsetWidth||0,showTimeRangeButton:t.showTimeRangeButton}),this.timeLineInit(t),this.timeLineSimple=new Ri(this.eventBus),this.isSimpleTimeLine=null==t?void 0:t.isSimpleTimeLine,this.isSimpleTimeLine&&this.element instanceof HTMLDivElement&&(this.styleInit({backgroundColor:"white"}),this.timeLineGroup.styleInit({display:"none"}),this.timeLineSimple.appendTo(this.element)),this.timeLineOption=new Ei(this.eventBus,this.timeLineGroup,t),this.timeLineOption.appendTo(this.element),this.element instanceof HTMLDivElement&&this.timeLineGroup.appendTo(this.element),this.callbackInit()}set groupLength(e){this.timeLineGroup.groupLength!==e&&(this.timeLineGroup.groupLength=e,this.timeLineOption.wndNums=e)}set fullScreen(e){this.timeLineOption.fullScreen=e}get rate(){return Math.pow(2,this.timeLineOption.rate)}get currentTime(){return this.timeLineGroup.currentTime}get synchronous(){return this.timeLineOption.synchronous}get status(){return this.timeLineOption.status}get statusList(){return this.timeLineGroup.groupItems.map((e=>e.status))}get statusMap(){const e=new Map;return this.timeLineGroup.groupItems.forEach((t=>{e.set(t.index,t.status)})),e}get positionTime(){return this.timeLineOption.positionTime}setCallbackFunc(e,t){this.eventMap.set(e,t)}callbackInit(){this.on("currentChange",(e=>{this.currentIndex=e}));new Pi(this.eventBus,this.eventMap,this.timeLineGroup,this.timeLineOption,this.timeLineSimple).eventInit()}timeLineInit(e){this.timeLineGroup._synchronous=!!(null==e?void 0:e.synchronous),this.appendTo(this.playerDiv),this.addClass("gs-timeline")}selectChange(e){this.currentIndex=e,this.timeLineGroup.selectChange(e)}on(e,t){this.eventBus&&this.eventBus.addEventListener(e,t)}setVideo(e,t,i){var s;this.timeLineGroup.setVideoRange(e,t),null===(s=this.timeLineGroup.painter)||void 0===s||s.drawVideo(),this.isSimpleTimeLine&&i&&this.timeLineSimple.initTime({startTime:i.startTime,endTime:i.endTime}),this.refresh(),this.seekTime(null==i?void 0:i.seekTime)}setLabels(e,t){this.timeLineGroup.setLabels(e,t)}play(e){this.timeLineGroup.play(e)}stop(e){this.timeLineGroup.stop(e)}close(e,t=!0){this.timeLineGroup.close(e,t)}playAll(e){this.timeLineGroup.playAll(e)}stopAll(){this.timeLineGroup.stopAll()}closeAll(e=!0){this.timeLineGroup.closeAll(e)}setTitle(e,t){this.timeLineGroup.setTitle(e,t)}refresh(){var e;null===(e=this.timeLineGroup.groupEvent)||void 0===e||e.onResize(),this.timeLineSimple.onResize()}updateTime(e,t){this.timeLineOption.selectRecord||(this.timeLineSimple.updateTime(e),this.timeLineGroup.updateTime(e,t))}wndChange(e,t){this.timeLineGroup.wndChange(e,t)}enableRate(){this.timeLineOption.enableRate=!0}seekTime(e,t=!1,i,s=!1){const n=this.timeLineGroup.getGroupItem(i);e&&((void 0===i||this.synchronous)&&this.timeLineGroup.painter.drawTimePointByTime(e),n.seek(e,t));const r=n.status;!t||this.synchronous&&"play"===r||this.eventBus.emit("seek",i,void 0,s)}getGroupItem(e){return this.timeLineGroup.getGroupItem(e)}getCurrentTimeByWndNo(e){return this.timeLineGroup.getGroupItem(e).currentTime}checkSeekable(e,t){return this.timeLineGroup.getGroupItem(t).checkSeekable(e)}getNextTimePoint(e,t){return this.timeLineGroup.getGroupItem(t).getNextTimePoint(e)}setAuthOption(e,t=[]){this.timeLineGroup.getGroupItem(e).authOption=t,this.updateTimelineOption(e)}updateTimelineOption(e){const t=this.timeLineGroup.getGroupItem(e);this.timeLineOption.updateByAuthOption(t.authOption)}}class Li{constructor(e,t=!1){this.value=e,this.status=t}}class Ci extends Jt{constructor(e,t,i){super(t),this._value=e.value,this._status=e.status,this.appendTo(i),this.eventInit(),this.classInit()}set status(e){this._status=e,this.clearClass(),this.addClass("gs-operation-box-button"),e?this.addClass(`gs-control-${this._value}-hover`):this.classInit()}eventInit(){this.element.onclick=()=>{this.eventBus.trigger(this._value,this._status)}}classInit(){this.addClass("gs-operation-box-button"),this.addClass(`gs-control-${this._value}`)}}class Ai extends Jt{constructor(e=4,t=9,i=["split-1","split-4","split-9","fullScreen","closeAll"]){super(new ve),this.maxSplit=t,this.addClass("gs-operation-box"),this.buttonInit(e,i)}buttonInit(e,t){const i=this.eventBus,s=this.element,n=new Map;t.forEach((e=>{let t,r;if("string"==typeof e){if(e.indexOf("split")>-1){if(+e.split("-")[1]>this.maxSplit)return}r=e,t=new Ci(new Li(e),i,s)}else t=new Ci(e,i,s),r=e.value;if(0===r.indexOf("split")){const e=+r.split("-")[1];n.set(e,t)}})),this.eventBus.listen("Split",((e,t)=>{n.forEach((e=>{e.status=!1}));const i=n.get(+t);i&&(i.status=!0)})),this.eventBus.trigger("Split",!1,e)}}class Oi{constructor(e,t={}){this._length=1,this.rightMenuKeys=["Close","FullScreen"],this.topMenuKeys=["FullScreen","Close"],this.timeLineMenuKeys=["seek","fullScreen","closeAll"],this._isPlayback=!1,this._playerMap=new Map,this._currentId=0,this.playerIndexMap=new Map,this.gridGap="3px",this.reconnect=!1,this.titleWidth=130,this.maxSplit=9,this.playerTemplate={col:2,row:2},this.playerItemTemplates=new Array(4).fill({col:2,row:2}),this.fixScreenNo=!1,this._showOpeartionBox=!1,this.synchronous=!1,this.wndPlayRulesUpdate=void 0,this.selectById=(e,t,i=!1)=>{if(e)return void 0!==t&&this._timeLine&&this._timeLine.selectChange(t),this._playerMap.forEach((t=>t.selected(e))),this._currentId=e,this.currentPlayer=this._playerMap.get(e),this._timeLine&&this._timeLine.updateTimelineOption(this.currentPlayer.index),e},this.option=t,this.controlEventBus=new ve,this.callbackMap=new Map;const i=document.getElementById(e),s=new Jt(this.controlEventBus);s.element.id=`player-wrapper${Math.random()}`,this._divId=s.element.id,s.appendTo(i),this.wrapperDiv=s.element,t.customScreenObj&&(this.playerTemplate=t.customScreenObj.playerTemplate,this.playerItemTemplates=t.customScreenObj.playerItemTemplates,this.playerItemScreenNos=t.customScreenObj.playerItemScreenNos,this.fixScreenNo=!!t.customScreenObj.playerItemScreenNos),this.optionInit(t),this.timeLineInit(t),this.operationBoxInit(t),this.createPlayer();const n=Math.sqrt(this._length),r=Math.floor(n);this.styleInit(n===r?n:0,this._length),this.currentPlayer=this._playerMap.get(this._currentId)}get playerOption(){const{maxRecordSize:e,wndDraggable:t,magnifyOnDblClick:i,singleOnDblClickFullscreen:s,draggable:n,showBackgroundImage:r,defaultVolume:o}=this.option;return{topMenuKeys:this.topMenuKeys,rightMenuKeys:this.rightMenuKeys,isPlayback:this._isPlayback,id:this._divId,timeline:this._timeLine,reconnect:this.reconnect,controlEventBus:this.controlEventBus,showBackgroundImage:null==r||r,operationBox:this._operationBox,keepLastFrame:this._keepLastFrame,draggable:n,callbackMap:this.callbackMap,maxRecordSize:e||512,topForever:this.option.topForever,wndDraggable:null==t||t,wndPlayRules:null==this.wndPlayRulesUpdate?this.option.wndPlayRules:this.wndPlayRulesUpdate,magnifyOnDblClick:null==i||i,singleOnDblClickFullscreen:null==s||s,defaultVolume:o}}get keepLastFrame(){return this._keepLastFrame}get isRecording(){let e=!1;return this._playerMap.forEach((t=>{e=e||t.isRecording})),e}set keepLastFrame(e){this._keepLastFrame=e,this.playerIndexMap.forEach((t=>{t.keepLastFrame=e}))}get gridStyle(){return[this.playerTemplate,this.playerItemTemplates]}optionInit(e){e&&(this._showOpeartionBox=Boolean(e.showOperationBox&&!e.isPlayback),this._length=e.wndNums||this._length,this.rightMenuKeys=e.rightMenuKeys||this.rightMenuKeys,this.topMenuKeys=e.topMenuKeys||this.topMenuKeys,this.timeLineMenuKeys=e.timeLineMenuKeys||this.timeLineMenuKeys,this.maxSplit=e.maxSplit||this.maxSplit,this._isPlayback=null==e.isPlayback?this._isPlayback:e.isPlayback,this.reconnect=null==e.reconnect?this.reconnect:e.reconnect,this.titleWidth=null==e.titleWidth?this.titleWidth:e.titleWidth,this.keepLastFrame=e.keepLastFrame,this.synchronous=!!e.synchronous,this._length>1&&!e.rightMenuKeys&&this.rightMenuKeys.push("CloseAll"))}timeLineInit(e){this._isPlayback&&(this._timeLine=new Bi(this._divId,{titleWidth:this.titleWidth,maxSplit:this.maxSplit,timeLineMenuKeys:this.timeLineMenuKeys,synchronous:this.synchronous,isSimpleTimeLine:e.isSimpleTimeLine,timeGapRange:e.timeGapRange,initTimeGap:e.initTimeGap,offsetY:e.timelineTopBlank,timeGap:e.timeGapWidth,autoGetRecord:e.autoGetRecord,showTimeRangeButton:e.showTimeRangeButton}),this._timeLine.styleInit({gridColumn:"1 / 3"}),this._timeLine.groupLength=(null==e?void 0:e.wndNums)||4)}operationBoxInit(e){this._showOpeartionBox&&(this._operationBox=new Ai(e.wndNums,e.maxSplit),this._operationBox.appendTo(this.wrapperDiv))}updateStaticConfig(){this._playerMap.forEach((e=>{this.wndPlayRulesUpdate=Oi.config.wndPlayRules,e.updateStaticConfig()}))}createPlayer(){for(let e=0;e<this._length;e++){let t=e;this.fixScreenNo&&(t=this.playerItemScreenNos[e]);const i=this.playerOption,s=new ls(i,this,t),{id:n}=s;s.index=t,this.playerIndexMap.set(t,s),this._playerMap.set(n,s),this._currentId=this._currentId||n}this.selectById(this._currentId+"")}styleInit(e,t){const i=document.getElementById(this._divId);if(i){this.wrapperDiv=i,Lt(i,"gs-wrapper-div"),i.style.gridGap=this.gridGap;const s=e||Math.sqrt(this._length),n=Math.floor(s),r=t||this.playerTemplate.col*this.playerTemplate.row;1===s?this.setGridStyle({col:2,row:2},new Array(this._length).fill({col:2,row:2}),!0):n==s&&this._length==r?this.setGridStyle({col:s,row:s},[]):this.setGridStyle(this.playerTemplate,this.playerItemTemplates)}}setGridStyle(e,t,i){i||(this.playerTemplate=e,this.playerItemTemplates=t);const s=document.getElementById(this._divId),{row:n,col:r}=e;s&&(s.style.gridTemplateColumns=r?`repeat(${r},1fr) `:"",s.style.gridTemplateRows=n?`repeat(${n},1fr)`:"");let o=0;this.playerIndexMap.forEach(((e,i)=>{if(null==i)return;const{row:s,col:n}=t[o]||{};e.playerDiv.styleInit({gridRow:`auto/span ${s||1}`}),e.playerDiv.styleInit({gridColumn:`auto/span ${n||1}`}),t.length||e.playerDiv.styleInit({display:"inline-block"}),o++})),this._timeLine&&(this._timeLine.styleInit({gridColumn:`auto/span ${e.col||1}`}),this._timeLine.refresh()),this._operationBox&&this._operationBox.styleInit({gridColumn:`auto/span ${e.col||1}`})}getPlayerByKey(e){let t;return this.playerIndexMap.forEach((i=>{i.key===e&&(t=i)})),t}getPlayerByIndex(e){return null==e?this.currentPlayer:this.playerIndexMap.get(e)}getIndexByKey(e){let t;return this.playerIndexMap.forEach(((i,s)=>{i.key===e&&(t=s)})),t}selectByIndex(e,t=!0){const i=this.playerIndexMap.get(e)||this.currentPlayer;return t&&this.selectById(i.id,i.index),i}getFreeWnd(e=!1,t=!0,i=!0){if(!this.currentPlayer.title)return this.currentPlayer.index;for(let i of this._playerMap){const[s,n]=i;if(!n.title){const i=n.index;return t&&this.selectById(s,i,e),i}}return i?this.currentPlayer.index:void 0}getWndByConfig(e=!1,t=!0,i=["freePriority"]){const s={manualPriority:()=>{if(this.currentPlayer.playerDiv.clickWnd)return this.currentPlayer.playerDiv.clickWnd=!1,this.currentPlayer.index},freePriority:()=>{if(!this.currentPlayer.title)return this.currentPlayer.index;for(let i of this._playerMap){const[s,n]=i;if(!n.title){const i=n.index;return t&&this.selectById(s,i,e),i}}},nextPriority:()=>{const e=this.currentPlayer.index+1==this._length?0:this.currentPlayer.index+1;return this.selectByIndex(e),e}};let n;return i.some((e=>{if(n=s[e](),null!=n)return!0})),null==n?this.currentPlayer.index:n}getFreeWnds(){let e=[];for(let t of this._playerMap){const[i,s]=t;if(!s.title){const t=s.index;e.push(t)}}return e}focusWndByKey(e){const t=this.getPlayerByKey(e);if(t)return this.selectByIndex(t.index),t.index}closeByIndex(e,t){if(null==e)return this.currentPlayer.close(t);return this.selectByIndex(e,!1).close(t)}destory(){const e=document.getElementById(this._divId);this.playerIndexMap.forEach((e=>{e.destroy()})),e&&e.parentElement&&(e.parentElement.innerHTML="")}getActiveOthers(e){if(e>=this._length)return!1;{let t=!1;return this.playerIndexMap.forEach((i=>{i.index>=e&&i.status==hs.PLAYSUCCESSED&&(t=!0)})),t}}}function xi(e,t=!1){const i=e||new Date,s=i.getFullYear(),n=String(i.getMonth()+1).padStart(2,"0"),r=String(i.getDate()).padStart(2,"0");let o="00",a="00",h="00";return e&&!t&&(o=String(i.getHours()).padStart(2,"0"),a=String(i.getMinutes()).padStart(2,"0"),h=String(i.getSeconds()).padStart(2,"0")),`${s}-${n}-${r} ${o}:${a}:${h}`}Oi.config={isShowCoderate:!1,isShowResolution:!1,keepRatio:!1,wndPlayRules:void 0};const ki=new Map,Mi=new Map;function Gi(e,t,i){!function(e,t,i){e.listen("PlayTimeUpdate",(e=>{i.trigger("updateTime",xi(new Date(e)),t.index),i.trigger("onTimeUpdate",xi(new Date(e)),t.index)}))}(e,t,i),function(e,t,i){e.listen("getReconnectURL",(async()=>{const e=t.wndInfo,s=t.isPlayback,n=t.playerDiv.video;let r=0;if(n&&(r=1e3*n.currentTime),s){const e=t.timeLine;if(e&&"stop"===e.status)return}await t.close(!0,s),i.trigger("reconnect",e,r),i.trigger("defaultReconnect",e,r)}))}(e,t,i),function(e,t,i){e.listen("timeout",(async()=>{i.trigger("timeout",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("info",(e=>{console.log(e),t.setInfo(e)}))}(e,t),function(e,t,i){e.listen("sei",(e=>{i.trigger("onSEI",t.wndInfo,e)}))}(e,t,i),function(e,t,i){e.listen("onRateChange",(()=>{i.trigger("onRateChange")}))}(e,0,i),function(e,t,i){e.listen("onStartRecordSource",(e=>{i.trigger("onStartRecordSource",e,t.wndInfo)})),e.listen("onStopRecordSource",(()=>{i.trigger("onStopRecordSource",t.wndInfo)})),e.listen("onSourceRecording",(e=>{i.trigger("onSourceRecording",e,t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("log",(e=>{i.trigger("onLog",e)}))}(e,0,i),function(e,t,i){let s=!1;e.listen("onDecodeError",(i=>{if(s)return;const n=t.wndInfo.key,r=ki.get(n)||0;if(s=!0,r>=3)ki.set(n,0),t.close(!0).then((()=>(s=!1,t.setInfo(i||"播放器解码失败，请联系管理员"))));else{ki.set(n,r+1);const t=Mi.get(n);t&&clearTimeout(t),Mi.set(n,setTimeout((()=>{ki.set(n,0)}),1e4)),e.trigger("getReconnectURL")}}))}(e,t),function(e,t,i){e.listen("onUpdateCoderate",(e=>{t.playerDiv.coderate=e}))}(e,t),function(e,t,i){e.listen("onUpdateResolution",(e=>{t.playerDiv.resolution=e}))}(e,t)}function Ni(e,t){if(e instanceof Date){var i={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};for(var s in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length))),i)new RegExp("("+s+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?i[s]:("00"+i[s]).substr((""+i[s]).length)));return t}return e}function Fi(e,t,i){!function(e,t){e.listen("Voice",(e=>{t.soundControl(t.volume,!e)}))}(e,t),function(e,t,i){e.listen("Close",(()=>{i.trigger("Close",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("CloseAll",(()=>{i.trigger("CloseAll")}))}(e,0,i),function(e,t,i){e.listen("CloseOthers",(()=>{i.trigger("CloseOthers",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("Stream",(()=>{i.trigger("onChangeStream",t.wndInfo),i.trigger("onDefaultChangeStream",t.wndInfo),e.trigger("Zoom",!1)}))}(e,t,i),function(e,t,i){e.listen("OpenKeepRatio",(()=>{i.trigger("KeepRatio",t.index,!0)})),e.listen("CloseKeepRatio",(()=>{i.trigger("KeepRatio",t.index,!1)})),e.listen("KeepRatio",(e=>{i.trigger("KeepRatio",t.index,e,!1),e?i.trigger("onOpenKeepRatio",t.wndInfo):i.trigger("onCloseKeepRatio",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("Capture",(()=>{var e,s;const n=t.playerDiv.video,r=document.createElement("canvas");r.width=n.videoWidth,r.height=n.videoHeight,null===(e=r.getContext("2d"))||void 0===e||e.drawImage(n,0,0,r.width,r.height);const o=r.toDataURL("image/jpeg"),a=t.title+" "+Ni(new Date,"yyyy-MM-dd hh:mm:ss")+".jpg",h=Object.assign({isPlayback:t.isPlayback,currentTime:null===(s=t.timeLine)||void 0===s?void 0:s.currentTime},t.wndInfo);i.trigger("onCapture",o,a,h)}))}(e,t,i),function(e,t){e.listen("Video",(e=>{e?console.log("结束本地录像！"):console.log("开始本地录像！")}))}(e),function(e,t,i){e.listen("OpenFullScreen",(()=>{i.trigger("OpenFullScreen")})),e.listen("CloseFullScreen",(()=>{i.trigger("CloseFullScreen")})),e.listen("FullScreen",(e=>{e?i.trigger("CloseFullScreen"):i.trigger("OpenFullScreen"),i.trigger("onOpenFullScreen",t.wndInfo),i.trigger("onCloseFullScreen",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("onSelectWnd",(()=>{i.trigger("onSelectWnd",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("Test",(()=>{const e=t.wndInfo.url,s=e.split("&")[1],n=e.match(/(?<=\/\/).*?(?=:)/);let r="";n&&(r=n[0]),i.trigger("onTest",t.wndInfo,s,r)}))}(e,t,i),function(e,t,i){e.listen("MultipleCapture",(()=>{let e=0,s=setInterval((()=>{var n,r;const o=t.playerDiv.video;if(o){const a=document.createElement("canvas");a.width=o.videoWidth,a.height=o.videoHeight,null===(n=a.getContext("2d"))||void 0===n||n.drawImage(o,0,0,a.width,a.height);const h=a.toDataURL("image/jpeg"),l=t.title+" "+Ni(new Date,"yyyy-MM-dd hh:mm:ss")+".jpg",u=Object.assign({isPlayback:t.isPlayback,currentTime:null===(r=t.timeLine)||void 0===r?void 0:r.currentTime},t.wndInfo);i.trigger("onMultipleCapture",e,h,l,u),e++,4===e&&clearInterval(s)}else clearInterval(s)}),1e3)}))}(e,t,i),function(e,t,i){e.listen("Share",(()=>{var e;const s=t.playerDiv.video,n=document.createElement("canvas");n.width=s.videoWidth,n.height=s.videoHeight,null===(e=n.getContext("2d"))||void 0===e||e.drawImage(s,0,0,n.width,n.height);const r=n.toDataURL("image/jpeg");i.trigger("share",r,t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("ManualAlarm",(()=>{var e;const s=t.playerDiv.video,n=document.createElement("canvas");n.width=s.videoWidth,n.height=s.videoHeight,null===(e=n.getContext("2d"))||void 0===e||e.drawImage(s,0,0,n.width,n.height);const r=n.toDataURL("image/jpeg");i.trigger("manualAlarm",r,t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("SliceVideo",(()=>{i.trigger("onSliceVideo",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("on3D",(e=>{i.trigger("on3D",t.wndInfo,e)})),e.listen("ThreeDimensions",(e=>{e?i.trigger("onOpen3D",t.wndInfo):i.trigger("onClose3D",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("Zoom",(e=>{e?i.trigger("onOpenZoom",t.wndInfo):i.trigger("onCloseZoom",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("onError",(e=>{i.trigger("onError",t.wndInfo,e)}))}(e,t,i),function(e,t,i){e.listen("OpenPlatformRecord",(()=>{i.trigger("onOpenPlatformRecord",t.wndInfo)})),e.listen("ClosePlatformRecord",(()=>{i.trigger("onClosePlatformRecord",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("Record",(e=>{e?(i.trigger("OpenRecord",t),console.log("OpenRecord")):(i.trigger("CloseRecord",t),console.log("CloseRecord"))})),e.listen("OpenRecord",(()=>{console.log("OpenRecord"),i.trigger("OpenRecord",t)})),e.listen("CloseRecord",(()=>{console.log("CloseRecord"),i.trigger("CloseRecord",t)}))}(e,t,i),function(e,t,i){e.listen("DownloadByTime",(()=>{i.trigger("onDownloadByTime",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("Download",(()=>{i.trigger("Download",t.wndInfo)}))}(e,t,i),function(e,t,i){e.listen("DblClick",(e=>{i.trigger("onDblClick",t.wndInfo,e)}))}(e,t,i),function(e,t,i){e.listen("Click",(()=>{i.trigger("onClick",t.wndInfo)}))}(e,t,i)}class Ui extends Jt{constructor(e){super(e),this.infoStyleInit()}set info(e){this.paramsInit({innerText:e}),this.styleInit({display:e?"flex":"none"})}infoStyleInit(){this.styleInit({display:"none"}),this.addClass("gs-info-div"),this.addClass("gs-common-Exclamation")}}class Vi extends Jt{constructor(e,t=!1){super(e),this._info="",this._isRecordInfo=t,this.infoStyleInit()}set info(e){this._info=e,this.paramsInit({innerText:e}),this.styleInit({display:e?"flex":"none"})}get info(){return this._info}infoStyleInit(){this.styleInit({display:"none"}),this.addClass("gs-status-div"),this._isRecordInfo&&this.styleInit({justifyContent:"end",width:"100%"})}}const Wi={Close:{value:"Close",label:"关闭视频",show:0,enable:1,isSwitch:0},CloseAll:{value:"CloseAll",label:"关闭所有视频",show:0,enable:1,isSwitch:0},CloseOthers:{value:"CloseOthers",label:"关闭其他视频",show:0,enable:1,isSwitch:0},Capture:{value:"Capture",label:"抓图",show:0,enable:1,isSwitch:0},SecondaryStream:{value:"SecondaryStream",label:"辅码流",show:0,enable:0,isSwitch:0},MainStream:{value:"MainStream",label:"主码流",show:0,enable:0,isSwitch:0},Test:{value:"Test",label:"调试工具",show:0,enable:1,isSwitch:0,playActive:1},DownloadByTime:{value:"DownloadByTime",label:"按时间下载",show:0,enable:1,isSwitch:0,playActive:1,readyActive:1},KeepRatio:{value:"KeepRatio",label:"画面比例切换",show:0,enable:1,isSwitch:1,openTip:"打开",closeTip:"关闭",playActive:1},Voice:{value:"Voice",label:"声音",show:0,enable:1,isSwitch:1,openTip:"打开",closeTip:"关闭"},FullScreen:{value:"FullScreen",label:"全屏",show:0,enable:1,isSwitch:1,openTip:"打开",closeTip:"关闭"},Share:{value:"Share",label:"视频分享",show:0,enable:1,isSwitch:0,playActive:1},Record:{value:"Record",label:"本地录像",show:0,enable:1,isSwitch:1,playActive:1,openTip:"打开",closeTip:"关闭"},ManualAlarm:{value:"ManualAlarm",label:"手动告警",show:0,enable:1,isSwitch:0,playActive:1}};class Ki{constructor(e=[]){const t=JSON.parse(JSON.stringify(Wi)),i=[];e.forEach((e=>{if("string"==typeof e){if(!t[e])return;i.push(t[e]),t[e].show=1}else"function"!=typeof e&&e.value?(e.show=!0,e.enable=!0,e.isCustom=!0,i.push(e)):i.push(e)})),this.menu=i}}class $i{constructor(e,t,i){this.targetDiv=null,this.playStatus=!1,this.element=t,this._index=i,this.playActive=e.playActive,this.readyActive=e.readyActive,this._show=e.show,this._enable=e.enable,this.enableKeys=e.enableKeys,this.onShow=e.onShow,this.itemStyleInit(),this.addClass("gs-right-menu-item")}set index(e){this._index=e}get index(){return this._index}set show(e){this._show=e,this.enable?this.element.style.display=e?"block":"none":this.element.style.display=this.enable?"block":"none"}get show(){return this._show}set enable(e){this._enable=e,this.element.style.display=e?"block":"none"}get enable(){return this._enable}appendTo(e){this.targetDiv=e,e.append(this.element)}itemStyleInit(){this.enable?this.styleInit({display:this.show?"block":"none"}):this.styleInit({display:"none"})}reset(){this.playActive&&!this.playStatus&&(this.show=!1),this.itemStyleInit()}styleInit(e){Object.entries(e).forEach((e=>{const[t,i]=e;this.element.style[t]=i}))}addClass(e){this.element.classList.add(e)}active(e){this.enable?this.playActive&&(this.playStatus=e,this.show=e,this.styleInit({display:this.show?"block":"none"})):this.styleInit({display:"none"})}setReadyActive(e){this.enable?this.readyActive&&(this.playStatus=e,this.show=e,this.styleInit({display:this.show?"block":"none"})):this.styleInit({display:"none"})}}class Yi extends Qt{constructor(e,t,i,s,n){super(t,"button"),this.rightMenuItem=null,this.value="",this.label="",this.show=!1,this.enable=!0,this.status=!1,this.isSwitch=!1,this.openTip="",this.closeTip="",this.targetDiv=null,this.playActive=!1,this.readyActive=!1,this.playStatus=!1,this._isCustom=!1,this._menuItem=e,this._playerDiv=i,this.controlEventBus=s,this._index=n,this.addClass("gs-right-menu-item"),this.reset(),this.eventInit()}set index(e){this._index=e}get index(){return this._index}itemStyleInit(){this.enable?(this.styleInit({display:this.show?"block":"none"}),this.paramsInit({innerText:((this.status?this.closeTip:this.openTip)||"")+this.label,disabled:!this.enable})):this.styleInit({display:"none"})}eventInit(){this.value&&(this.element.onclick=()=>{let e=this.value;this.isSwitch&&(e=(this.status?"Close":"Open")+e),console.log(`trigger ${e}`),this.targetDiv&&(this.targetDiv.style.display="none"),this.status=!this.status,this.itemStyleInit(),this.eventBus.trigger(e),this._isCustom&&this.controlEventBus.trigger("on"+e,this._index)},"FullScreen"===this.value?(this.controlEventBus.listen("fullScreenExit",(()=>{this.status=!1,this.itemStyleInit()})),this.controlEventBus.listen("OpenFullScreen",(()=>{this.status=!0,this.itemStyleInit()})),this.controlEventBus.listen("CloseFullScreen",(()=>{this.status=!1,this.itemStyleInit()}))):this.isSwitch&&(this.controlEventBus.listen("Open"+this.value,(()=>{this.status=!0,this.itemStyleInit()})),this.controlEventBus.listen("Close"+this.value,(()=>{this.status=!1,this.itemStyleInit()}))))}reset(){this.value=this._menuItem.value,this.label=this._menuItem.label,this.show=this._menuItem.show,this.enable=this._menuItem.enable,this.status=this._menuItem.status||!1,this.isSwitch=this._menuItem.isSwitch,this.openTip=this._menuItem.openTip||"",this.closeTip=this._menuItem.closeTip||"",this.playActive=this._menuItem.playActive||!1,this.readyActive=this._menuItem.readyActive||!1,this._isCustom=this._menuItem.isCustom||!1,this.playActive&&!this.playStatus&&(this.show=!1),this.itemStyleInit()}active(e){this.enable?(this.playActive&&(this.show=e,this.playStatus=e,this.styleInit({display:this.show?"block":"none"})),this.isSwitch&&(this.status=!1,this.paramsInit({innerText:((this.status?this.closeTip:this.openTip)||"")+this.label}))):this.styleInit({display:"none"})}setReadyActive(e){this.enable?this.readyActive&&(this.show=e,this.playStatus=e,this.styleInit({display:this.show?"block":"none"})):this.styleInit({display:"none"})}appendTo(e){this.targetDiv=e,e.append(this.element)}}class Hi extends Qt{constructor(e=[],t,i,s,n){super(t),this.rightMenuList=[],this.targetDiv=null,this.authOption=[],this.controlEventBus=s,this.id=i.id,this._index=n,this.keys=e,this.rightMenuStyleInit(),this.menuItemInit(e,i),this.addListenerOutSideClick()}set index(e){this._index=e,this.rightMenuList.forEach((t=>{t.index=e}))}get index(){return this._index}set show(e){this.keys.length?this.styleInit({display:e?"block":"none"}):this.styleInit({display:"none"})}setAuthOption(e,t){this.isPlayBack=t,this.authOption=e;const i=t?"record":"live";this.rightMenuList.forEach((t=>{t instanceof $i?this.checkAuthEnable(t):(t.enable=xt(i,t.value,e),t.itemStyleInit())}))}rightMenuStyleInit(){this.addClass("gs-right-menu"),this.element.classList.add("gs-right-panel")}menuItemInit(e,t){new Ki(e).menu.forEach((e=>{if("function"!=typeof e){if(e.value){const i=new Yi(e,this.eventBus,t,this.controlEventBus,this.index);i.appendTo(this.element),this.rightMenuList.push(i)}else if(e.render&&Jt.Vue){const t=e.render,i=(new(Jt.Vue.extend({render:e=>t(e,(()=>this.index))}))).$mount().$el,s=new $i(e,i,this.index);this.rightMenuList.push(s),s.appendTo(this.element)}}else if(Jt.Vue){const t=(new(Jt.Vue.extend({render:t=>e(t,(()=>this.index))}))).$mount().$el;this.element.append(t)}}))}appendTo(e){this.targetDiv=e,e.append(this.element),e.oncontextmenu=t=>{t.preventDefault(),this.controlEventBus.trigger("hideAllPanel");const i=e.getElementsByTagName("video")[0];let s=0,n=0;i&&(s=+(i.style.left.match(/-?\d+/g)||[0])[0],n=+(i.style.top.match(/-?\d+/g)||[0])[0]);const r=t.target;this.styleInit({left:t.offsetX+s+"px",top:t.offsetY+r.offsetTop+n+"px"}),this.show=!0,this.controlEventBus.trigger("selectById",this.id,this.index)}}hideAllPanel(){}addListenerOutSideClick(){document.addEventListener("click",(e=>{e.stopPropagation(),e.target!==this.element&&(this.show=!1)})),this.controlEventBus.listen("fullScreenExit",(()=>{this.show=!1})),this.controlEventBus.listen("hideAllPanel",(()=>{this.show=!1}))}resetMenu(){this.rightMenuList.forEach((e=>e.reset()))}removeMenu(){this.targetDiv&&(this.targetDiv.oncontextmenu=()=>{})}checkAuthEnable(e){if(e instanceof $i){if(!e.enableKeys||!e.enableKeys.length)return void(e.enable=!0);const t=e.enableKeys.reduce(((e,t)=>e||this.authOption.indexOf(t)>-1),!1);if(e.enable=t,!e.enable)return;if(e.onShow){const t={};e.enableKeys.forEach((e=>{t[e]=this.authOption.indexOf(e)>-1})),e.onShow(this.index,t)}}}active(e){this.rightMenuList.forEach((t=>{t.active(e)}))}readyActive(e){this.rightMenuList.forEach((t=>{t.setReadyActive(e)}))}}const qi=["Zoom","ThreeDimensions"],ji={Close:{value:"Close",label:"关闭视频",show:0,enable:1,isSwitch:0,status:0},Capture:{value:"Capture",label:"抓图",show:0,enable:1,isSwitch:0,status:0},MultipleCapture:{value:"MultipleCapture",label:"连续抓图",show:0,enable:1,isSwitch:0,status:0},Stream:{value:"Stream",label:"码流",show:0,enable:1,isSwitch:1,openTip:"当前为辅码流,点击切换为主码流",closeTip:"当前为主码流,点击切换为辅码流",status:1},Zoom:{value:"Zoom",label:"数字缩放",show:0,enable:1,isSwitch:1,openTip:"打开数字缩放",closeTip:"打开数字缩放",status:0},KeepRatio:{value:"KeepRatio",label:"画面比例切换",show:0,enable:1,isSwitch:1,openTip:"打开画面比例切换",closeTip:"关闭画面比例切换",status:0},"3D":{value:"ThreeDimensions",label:"3D缩放",show:0,enable:1,isSwitch:1,openTip:"打开3D缩放",closeTip:"关闭3D缩放",status:0},Voice:{value:"Voice",label:"声音",show:0,enable:1,isSwitch:1,openTip:"打开声音",closeTip:"关闭声音",status:0},PlatformRecord:{value:"PlatformRecord",label:"平台录像",show:0,enable:1,isSwitch:1,openTip:"打开",closeTip:"关闭",status:0},FullScreen:{value:"FullScreen",label:"全屏",show:0,enable:1,isSwitch:1,openTip:"打开全屏",closeTip:"关闭全屏",status:0},Record:{value:"Record",label:"本地录像",show:0,enable:1,isSwitch:1,openTip:"打开本地录像",closeTip:"关闭本地录像",status:0},Download:{value:"Download",label:"下载录像",show:0,enable:1,isSwitch:0,status:0},SliceVideo:{value:"SliceVideo",label:"切片",show:0,enable:1,isSwitch:0,status:0}};class Xi{constructor(e=[]){const t=JSON.parse(JSON.stringify(ji)),i=[];(e=Array.from(new Set(e.concat([])))).forEach((e=>{"string"==typeof e?i.push(t[e]):(e.enable=!0,e.isCustom=!0,i.push(e))})),this.menu=i}}class zi extends Qt{constructor(e,t,i,s){super(e),this._show=!0,this._status=!1,this._closeTip="",this._openTip="",this._isSwitch=!1,this._enable=!0,this._isCutom=!1,this.label="",this.value="",this._menuItem=t,this.controlEventBus=i,this._icon=new Jt(e),this._index=s,this.element&&this._icon.appendTo(this.element),this.reset(),this.buttonInit(),this.eventInit()}set status(e){this._status=e,this.buttonReset()}get status(){return this._status}set show(e){this._show=e,this.styleInit({display:this._show?"inline-flex":"none"})}set index(e){this._index=e}get index(){return this._index}buttonInit(){this.addClass("gs-top-button"),this.element&&(this.element.onclick=()=>{console.log(`trigger ${this.value} ${this._status}`),this._isSwitch&&(qi.includes(this.value)&&this.parent&&this.parent.checkPlayerStatusWithout(this.value),"FullScreen"!==this.value&&(this.status=!this._status)),this.eventBus.trigger(this.value,this._status),this._isCutom&&this.controlEventBus.trigger("on"+this.value,this._index)}),this.buttonReset()}reset(){this._show=this._menuItem.show,this._status=this._menuItem.status,this.label=this._menuItem.label,this._closeTip=this._menuItem.closeTip||"",this._openTip=this._menuItem.openTip||"",this._isSwitch=this._menuItem.isSwitch,this._enable=this._menuItem.enable,this._isCutom=this._menuItem.isCustom||!1,this.value=this._menuItem.value,this.buttonReset()}buttonReset(){this._enable?(this.styleInit({display:this._show?"inline-flex":"none"}),this.paramsInit({title:this._isSwitch?this._status?this._closeTip:this._openTip:this.label}),this.resetIcon()):this.styleInit({display:"none"})}resetIcon(){let e,t="";t="Stream"===this.value?this._status?"MainStream":"SecondaryStream":(this._isSwitch?this._status?"Open":"Close":"")+this.value,e="Stream"!==this.value&&this._isSwitch?`gs-switch-${this._status?"open":"close"}-${this.value}`:`gs-button-${t}`;const i=e+"-click";this._icon.clearClass(),this._icon.addClass(e),this._icon.addClass("gs-top-icon");const s=()=>{this._icon.removeClass(i),this._isSwitch&&(this._icon.removeClass(`gs-switch-open-${this.value}-click`),this._icon.removeClass(`gs-switch-close-${this.value}-click`))};this._icon.element.onmousedown=()=>{this._icon.addClass(i)},this._icon.element.onmouseup=()=>{s()}}eventInit(){"FullScreen"===this.value&&(this.controlEventBus.listen("OpenFullScreen",(()=>{this.status=!0})),this.controlEventBus.listen("CloseFullScreen",(()=>{this.status=!1})),this.controlEventBus.listen("fullScreenExit",(()=>{this.status=!1})))}remove(){this.show=!1}}class Zi extends Qt{constructor(e,t,i,s){super(t),this._topMap=new Map,this._buttonList=[],this._title="",this._show=!1,this._isShowCoderate=!1,this._isShowResolution=!1,this._coderate="",this._resolution="",this._topMenu=new Xi(e.topMenuKeys),this._nameDiv=new Qt(this.eventBus),this._panelDiv=new Qt(this.eventBus),this._index=s,this._topForever=e.topForever,this.controlEventBus=i,this.isShowCoderate=e.isShowCoderate,this.isShowResolution=e.isShowResolution,this.option=e,this.topMenuStyleInit(),e.fixScreenNo&&(this._screenNoDiv=new Qt(this.eventBus),this.initScreenNoDiv(s)),this.initNameDiv(),this.initPanelDiv(),this.topMenuInit()}get isShowCoderate(){return this._isShowCoderate}set isShowCoderate(e){e=null==e?Oi.config.isShowCoderate:e,this._isShowCoderate=e,this.refreshTitle()}get isShowResolution(){return this._isShowResolution}set isShowResolution(e){e=null==e?Oi.config.isShowResolution:e,this._isShowResolution=e,this.refreshTitle()}set show(e){if(this._topForever)e=!0;else if(e===this._show)return;this._show=e,this.styleInit({display:!e||e&&!this._title?"none":"flex"})}set title(e){this._title=e,this.refreshTitle(),this.show=!0}set index(e){this._index=e,this._buttonList.forEach((t=>{t.index=e}))}get index(){return this._index}get zoom(){var e;return null===(e=this._topMap.get("Zoom"))||void 0===e?void 0:e.status}get threeDimensions(){var e;return null===(e=this._topMap.get("ThreeDimensions"))||void 0===e?void 0:e.status}get coderate(){return this._coderate}set coderate(e){this._coderate=e,this.refreshTitle()}get resolution(){return this._resolution}set resolution(e){this._resolution=e,this.refreshTitle()}setAuthOption(e,t){const i=t?"record":"live";this._topMenu.menu.forEach((t=>{t.enable=xt(i,t.value,e)})),this.resetTopMenu()}updateStaticConfig(){this.isShowCoderate=this.option.isShowCoderate,this.isShowResolution=this.option.isShowResolution}refreshTitle(){let e="";this.isShowCoderate&&this.isShowResolution?e=this.resolution?`(${this.coderate||"0.0Mbps"}，${this.resolution})`:"":this.isShowCoderate?e=`(${this.coderate||"0.0Mbps"})`:this.isShowResolution&&(e=this.resolution?`(${this.resolution})`:"");const t=this._title+e;this._nameDiv.paramsInit({innerText:t,title:t})}topMenuStyleInit(){this.addClass("gs-top-menu"),this._nameDiv.addClass("gs-top-menu-title")}initScreenNoDiv(e){this._screenNoDiv&&(this.addClass("gs-top-menu-ext"),this._nameDiv.styleInit({maxWidth:"100%"}),this._screenNoDiv.paramsInit({innerText:`屏${e}`}),this.append(this._screenNoDiv))}initNameDiv(){this._nameDiv.styleInit({float:"left"}),this.append(this._nameDiv)}initPanelDiv(){this.append(this._panelDiv)}topMenuInit(){this._topMenu.menu.forEach((e=>{const t=new zi(this.eventBus,e,this.controlEventBus,this._index);t.appendTo(this._panelDiv,this),this._buttonList.push(t),this._topMap.set(t.value,t)}))}setTopMenu(e){this._topMenu.menu.forEach((t=>{"Stream"===t.value&&(t.status=1===e),t.show=!0})),this.resetTopMenu()}switchTopStatus(e,t){this._buttonList.forEach((i=>{i.value===e&&(i.status=null==t?!i.status:t)}))}resetTopMenu(){this._buttonList.forEach((e=>e.reset()))}removeTopMenu(){this.show=!1,this._buttonList.forEach((e=>{switch(e.show=!1,e.value){case"KeepRatio":this.eventBus.emit(e.value,Oi.config.keepRatio),e.status=Oi.config.keepRatio;break;case"Zoom":case"ThreeDimensions":this.eventBus.emit(e.value,!1),e.status=!1}}))}checkPlayerStatusWithout(e){qi.forEach((t=>{if(t==e)return;const i=this._topMap.get(t);if(console.log(i),i&&i.status){const e=`请先关闭${i.label}`;throw this.eventBus.trigger("onError",e),e}}))}}const Ji=e=>{let t=e.offsetLeft;return null!=e.offsetParent&&(t+=Ji(e.offsetParent)),t},Qi=e=>{let t=e.offsetTop;return null!=e.offsetParent&&(t+=Qi(e.offsetParent)),t};function es(){let e=0,t=0,i=0,s=0,n=0,r=!1,o=0,a=0,h=1,l=0,u=0,c=!1;const d=()=>{n=0,s=0,e=0,t=0,i=0,h=1;const r=this.video;r&&(r.style.width="100%",r.style.height="100%",r.style.top="0",r.style.left="0")};this.eventBus.listen("Zoom",(e=>{c=e,e?this.paramsInit({draggable:""}):(d(),this.paramsInit({draggable:this.draggable?"true":""}))})),this.element.addEventListener("wheel",(t=>{if(!c)return;let i=-t.deltaY;i=i<0?100*Math.ceil(i/100):100*Math.floor(i/100);const r=t.target;if("VIDEO"!==r.tagName)return;if(i<0&&e<=0||e>=40&&i>0)return;const o=(e+10)/10;e+=i/100,e=e<=0?0:e,e=e>=40?40:e;const a=t.target,{clientWidth:d,clientHeight:p}=r.parentNode,{clientX:m,clientY:g,offsetX:_,offsetY:f}=t;h=(e+10)/10,l=(1-h)*d,u=(1-h)*p,a.style.width=d*h+"px",a.style.height=p*h+"px";const S=_*h/o,y=f*h/o,v=r.parentNode;n=-(S-m+Ji(v)),s=-(y-g+Qi(v)),h<=1&&(n=0,s=0),n>0&&(n=0),s>0&&(s=0),n<l&&(n=l),s<u&&(s=u),a.style.left=n+"px",a.style.top=s+"px"})),this.element.addEventListener("mousedown",(e=>{c&&(r=!0,o=e.clientX,a=e.clientY)})),this.element.addEventListener("mousemove",(e=>{if(!c)return;if("VIDEO"!==e.target.tagName)return;if(!r||1==h)return;const d=e.target,p=e.clientX-o,m=e.clientY-a;i=p+n,t=m+s,i>0&&(o=e.clientX,i=0,n=0),t>0&&(a=e.clientY,t=0,s=0),i<l&&(o=e.clientX,i=l,n=l),t<u&&(a=e.clientY,t=u,s=u),d.style.left=i+"px",d.style.top=t+"px"})),this.element.addEventListener("mouseup",(e=>{c&&(r=!1,n=i,s=t)})),this.element.addEventListener("mouseout",(e=>{c&&(r=!1,n=i,s=t)})),this.element.addEventListener("dblclick",(e=>{d()})),this.controlEventBus.listen("CloseFullScreen",(()=>{d()})),this.controlEventBus.listen("OpenFullScreen",(()=>{d()})),this.eventBus.listen("FullScreen",(()=>{d()}))}function ts(e=!1,t=!1){(e||t)&&(this.element.ondragover=e=>{e.preventDefault()},this.element.ondragstart=()=>{this.controlEventBus.emit("dragStart",this.index)},this.element.ondrop=()=>{this.controlEventBus.emit("drop",this.index)},this.element.onmouseup=()=>{this.controlEventBus.emit("mouseup",this.index)}),e&&this.paramsInit({draggable:"true"})}function is(){let e=!1,t=!1,i=0,s=0,n=0,r=0,o=0,a=0;const h=new Jt(this.eventBus);h.addClass("gs-rectangle"),h.appendTo(this.element);const l=l=>{const u=Math.floor(Math.abs(n-i)),c=Math.floor(Math.abs(r-s)),d=!e||!t;if(t=!1,h.styleInit({display:"none"}),d)return;const p={clientWidth:Math.floor(this.element.clientWidth),clientHeight:Math.floor(this.element.clientHeight),midX:Math.floor((n+i)/2),midY:Math.floor((r+s)/2),rectangleWidth:u,rectangleHeight:c,startOffsetX:i,startOffsetY:s,endOffsetX:n,endOffsetY:r};this.eventBus.emit("on3D",p),i=0,s=0,o=0,a=0,n=0,r=0,h.styleInit({width:0,height:0,left:0,top:0})};this.eventBus.listen("ThreeDimensions",(t=>{e=t,e?this.paramsInit({draggable:""}):this.paramsInit({draggable:this.draggable?"true":""})})),this.element.addEventListener("mousedown",(l=>{if(l.target){if("VIDEO"!==l.target.tagName)return}e&&(t=!0,i=l.offsetX,s=l.offsetY,o=l.clientX,a=l.clientY,n=l.offsetX,r=l.offsetY,h.styleInit({display:"block"}))})),this.element.addEventListener("mousemove",(i=>{if(!e||!t)return;const{clientX:s,clientY:l,offsetX:u,offsetY:c}=i;n=u,r=c,h.styleInit({width:Math.abs(s-o)+"px",height:Math.abs(l-a)+"px",left:Math.min(s,o)+"px",top:Math.min(l,a)+"px"})})),this.element.addEventListener("mouseup",(e=>{l()}))}class ss extends Qt{constructor(e){super(e),this._resolution="",this.visible=!1,this.addClass("gs-video-watermark")}set resolution(e){this._resolution=e,this.setWatermark()}showWatermark(e){e?this.setWatermark():(this.visible=!1,this.watermarkInfo=void 0)}setWatermark(){if(this._resolution&&!this.visible){ss.setVideoWaterMarkInfo();const e=ss.videoWaterMarkInfo;if(!e.content)return;const t=this.getWatermarkContent();this.watermarkInfo={pointerEvents:"none",top:"0",left:"0",opacity:e.opacity+"",position:"absolute",zIndex:"1",...t},this.styleInit(this.watermarkInfo),this.visible=!0}}refreshWatermark(){if(!this.visible)return;const e=Object.assign({},this.watermarkInfo),t=this.getWatermarkContent();Object.assign(e,t),this.styleInit(e)}getWatermarkContent(){const e=this.targetDiv;if(!e)return{};const t=ss.videoWaterMarkInfo,i=document.createElement("canvas"),s=+t.fontSize*e.clientWidth/1920;i.width=e.clientWidth/(t.density/10),i.height=e.clientHeight/(t.density/10);const n=i.getContext("2d");return n.font=`${s}px Vedana`,n.fillStyle=t.fontColor,t.content.split("\n").forEach(((e,t)=>{n.fillText(e,0,(t+1)*s)})),{width:`${e.clientWidth}px`,height:`${e.clientHeight}px`,background:`url(${i.toDataURL("image/png")}) left top repeat`}}}ss.videoWaterMarkInfoFunc=void 0,ss.videoWaterMarkInfo={fontSize:18,opacity:100,density:0,fontColor:"",content:""},ss.setVideoWaterMarkInfo=async e=>{if(ss.videoWaterMarkInfoFunc=e||ss.videoWaterMarkInfo,e=ss.videoWaterMarkInfoFunc)if("function"==typeof e){let t=await e();Object.assign(ss.videoWaterMarkInfo,t)}else Object.assign(ss.videoWaterMarkInfo,e)};class ns extends Qt{constructor(e,t,i,s,n,r){super(t),this.id="",this.isFull=!1,this.targetDiv=null,this._info="",this.backImgSrc="",this.isSelected=!1,this._lastFrame="",this.draggable=!1,this._index=0,this.clickWnd=!1,this.fixScreenNo=!1,this.id=e,this.element.id=e,this.controlEventBus=i,this._index=s,this.draggable=n.draggable||!1,this.backImgSrc=!1===n.showBackgroundImage?"gs-background-no-video-only":"gs-background-no-video",this.playerDivStyleInit(),this.infoDiv=new Ui(this.eventBus),this.cornerInfo=new Vi(this.eventBus),this.recordInfo=new Vi(this.eventBus,!0),r.customScreenObj&&(this.fixScreenNo=!!r.customScreenObj.playerItemScreenNos);const o=Object.assign(n,{isShowCoderate:r.isShowCoderate,isShowResolution:r.isShowResolution,fixScreenNo:this.fixScreenNo});this.topMenuDiv=new Zi(o,t,this.controlEventBus,this._index),this.rightMenuDiv=new Hi(n.rightMenuKeys,t,this.element,this.controlEventBus,this._index),this.playerWrapper=new Jt(this.eventBus),this.isPlayBack=r.isPlayback,this.videoWatermark=new ss(t),this.singleOnDblClickFullscreen=n.singleOnDblClickFullscreen,this.magnifyOnDblClick=n.magnifyOnDblClick,this.append(),this.eventInit(n.draggable,r.nodeDraggable)}set info(e){this._info=e,e?(Bt(this.element,this.backImgSrc),this._lastFrame?this.styleInit({background:`url(${this._lastFrame})`,backgroundSize:"cover"}):this.styleInit({background:"",backgroundSize:""}),Bt(this.element,"gs-display-block"),Lt(this.element,"gs-display-none")):(this.styleInit({background:"",backgroundSize:""}),Lt(this.element,this.backImgSrc),Bt(this.element,"gs-display-none"),Lt(this.element,"gs-display-block"))}get info(){return this._info}set lastFrame(e){this._lastFrame=e,e||this.styleInit({background:"",backgroundSize:""})}get lastFrame(){return this._lastFrame}set index(e){this._index=e,this.topMenuDiv.index=e,this.rightMenuDiv.index=e}get index(){return this._index}get playerWrapperId(){return this.playerWrapper.element.id}get video(){return this.playerWrapper.element.getElementsByTagName("video")[0]}get zoom(){return this.topMenuDiv.zoom}get threeDimensions(){return this.topMenuDiv.threeDimensions}set authOption(e){this.topMenuDiv.setAuthOption(e,this.isPlayBack),this.rightMenuDiv.setAuthOption(e,this.isPlayBack)}set coderate(e){this.topMenuDiv.coderate=e}set resolution(e){this.topMenuDiv.resolution=e,this.videoWatermark.resolution=e}updateStaticConfig(){this.topMenuDiv.updateStaticConfig()}append(){this.infoDiv.appendTo(this.element),this.cornerInfo.appendTo(this.element),this.recordInfo.appendTo(this.element),this.topMenuDiv.appendTo(this.element),this.rightMenuDiv.appendTo(this.element),this.videoWatermark.appendTo(this.element),this.playerWrapper.element.id="playerWrapper"+Math.random(),this.playerWrapper.addClass("player-wrapper"),this.playerWrapper.appendTo(this.element)}switchTopStatus(e,t){this.topMenuDiv.switchTopStatus(e,t)}playerDivStyleInit(){Lt(this.element,"gs-playerdiv"),Lt(this.element,this.backImgSrc)}setInfo(e){this.info=e,this.infoDiv.info=e}setCornerInfo(e){this.cornerInfo.info=e}removeTopMenu(){this.topMenuDiv.removeTopMenu()}setTitle(e){this.topMenuDiv.title=e}setTopMenu(e){this.topMenuDiv.setTopMenu(e)}before(e,t){this.targetDiv=t,e.before(this.element)}eventInit(e=!1,t=!1){document.addEventListener("fullscreenchange",(()=>{this.videoWatermark.refreshWatermark()})),this.element.onclick=()=>{this.clickWnd=!0,this.controlEventBus.trigger("selectById",this.id,this.index),this.eventBus.trigger("Click")},this.element.ondblclick=()=>{var e;const t=Array.from((null===(e=this.element.parentElement)||void 0===e?void 0:e.children)||[]);this.magnifyOnDblClick?(this.isFull?(t.forEach((e=>{e.classList.value.split(" ").includes("gs-watermark")||(e.style.display="inline-block")})),this.controlEventBus.trigger("windowChange")):(t.forEach((e=>{e.classList.value.split(" ").includes("gs-watermark")||(e.style.display="none")})),this.element.style.display="inline-block",this.controlEventBus.trigger("showTimeLine"),this.controlEventBus.trigger("windowChange",1)),this.eventBus.trigger("DblClick",this.isFull),this.isFull=!this.isFull,this.videoWatermark.refreshWatermark()):this.eventBus.trigger("DblClick",this.isFull)},this.element.onmouseover=()=>{this.topMenuDiv.show=!0},this.element.onmouseout=()=>{this.topMenuDiv.show=!1},es.call(this),ts.call(this,e,t),is.call(this)}startRecord(){this.recordInfo.info="●正在本地录像"}stopRecord(){this.recordInfo.info=""}selected(e){e===this.id?(this.eventBus.trigger("onSelectWnd"),this.element.classList.contains("gs-win-selected")||Lt(this.element,"gs-win-selected")):Bt(this.element,"gs-win-selected")}capture(){var e;if(this.video){const t=document.createElement("canvas");t.width=this.video.videoWidth,t.height=this.video.videoHeight,null===(e=t.getContext("2d"))||void 0===e||e.drawImage(this.video,0,0,t.width,t.height);return t.toDataURL("image/jpeg")}return""}active(e,t){this.videoWatermark.showWatermark(e),this.lastFrame=t||this.lastFrame,e?this.styleInit({background:"",backgroundSize:""}):(this.styleInit({background:"",backgroundSize:""}),this.setCornerInfo(""),this.recordInfo.info=""),this.rightMenuDiv.active(e)}readyActive(e){this.rightMenuDiv.readyActive(e)}destroy(){this.observer&&(this.observer.disconnect(),this.observer.takeRecords(),this.observer=void 0)}}class rs extends Qt{constructor({eventBus:e,controlEventBus:t,index:i,wndNums:s=4,mobileMenuKeys:n=[]}={}){super(e),this.zoom=!1,this.threeDimensions=!1,this.coderate="",this.resolution="",this._show=!1,this._isFull=!1,this.isFullScreen=!1,this.controlEventBus=t,this.index=i,this.isFlex=!0,this.wndNums=s,this.mobileMenuKeys=n,this.leftDiv=new Qt(e),this.rightDiv=new Qt(e),this.leftDiv.appendTo(this.element),this.rightDiv.appendTo(this.element),this.eventInit(),this.classInit(),this.leftInit(),this.rightInit()}set show(e){this._show&&e&&(e=!1),e?(this.visible=!0,this._show=!0,this.timer&&clearTimeout(this.timer),this.timer=setTimeout((()=>{this.show=!1,this._show=!1}),3e4)):(this.visible=!1,this._show=!1)}set isFull(e){this._isFull=e,this.customWrapper&&(e&&this.isFullScreen?this.customWrapper.visible=!0:this.customWrapper.visible=!1)}get isFull(){return this._isFull}eventInit(){this.controlEventBus.listen("showMobileBottomMenu",(e=>{e!==this.index&&(this.show=!1)}))}classInit(){this.addClass("gs-mobile-bottom-menu"),this.leftDiv.addClass("gs-mobile-bottom-menu-left"),this.rightDiv.addClass("gs-mobile-bottom-menu-right")}leftInit(){const e=new Qt(this.eventBus);e.visible=!1,e.appendTo(this.leftDiv),e.addClass("gs-mobile-mobilePortrait");const t=new Qt(this.eventBus);t.visible=!1,t.appendTo(this.leftDiv),this.customWrapper=t,t.addClass("gs-mobile-customWrapper"),this.controlEventBus.listen("fullScreenChange",(i=>{this.isFullScreen=i,e.visible=i,i?(1===this.wndNums||this.isFull)&&(t.visible=!0):t.visible=i})),e.element.addEventListener("click",(()=>{e.visible=!1,t.visible=!1,this.controlEventBus.trigger("CloseFullScreen")})),this.controlEventBus.listen("CloseFullScreen",(()=>{this.isFullScreen=!1,e.visible=!1,t.visible=!1}));new Ki(this.mobileMenuKeys).menu.forEach((e=>{if("function"!=typeof e){if(e.value);else if(e.render&&Jt.Vue){const i=e.render,s=(new(Jt.Vue.extend({render:e=>i(e,(()=>this.index))}))).$mount().$el;new $i(e,s,this.index).appendTo(t.element)}}else if(Jt.Vue){const i=(new(Jt.Vue.extend({render:t=>e(t,(()=>this.index))}))).$mount().$el;t.element.append(i)}}))}rightInit(){const e=new Qt(this.eventBus);e.appendTo(this.rightDiv),e.addClass("gs-mobile-mobileMagnify"),1===this.wndNums&&(e.visible=!1),this.isFull=!1;const t=t=>{var i;t&&t.preventDefault();const s=this.element.parentElement,n=Array.from((null===(i=null==s?void 0:s.parentElement)||void 0===i?void 0:i.children)||[]);!this.isFull&&n.length&&s?(n.forEach((e=>{e.classList.value.split(" ").includes("gs-watermark")||(e.style.display="none")})),s.style.display="inline-block",this.controlEventBus.trigger("showTimeLine"),this.controlEventBus.trigger("windowChange",1),e.removeClass("gs-mobile-mobileMagnify"),e.addClass("gs-mobile-mobileExitMagnify")):(n.forEach((e=>{e.classList.value.split(" ").includes("gs-watermark")||(e.style.display="inline-block")})),this.controlEventBus.trigger("windowChange"),e.removeClass("gs-mobile-mobileExitMagnify"),e.addClass("gs-mobile-mobileMagnify")),this.isFull=!this.isFull,this.eventBus.trigger("onMobileMagnify",this.isFull)};e.element.addEventListener("click",t,!0),this.controlEventBus.addEventListener("onMobileMagnify",(e=>{this.index===e&&t()})),this.controlEventBus.listen("splitChange",(t=>{this.wndNums=t,1==t?(e.visible=!1,this.isFullScreen&&this.customWrapper&&(this.customWrapper.visible=!0)):(e.visible=!0,this.isFull&&this.customWrapper?this.customWrapper.visible=!0:this.customWrapper&&(this.customWrapper.visible=!1))}))}setAuthOption(e,t){}updateStaticConfig(){}switchTopStatus(e,t){}removeTopMenu(){}setTopMenu(e){}}class os extends Qt{constructor(e,t,i){super(e),this._title="",this._show=!1,this.isFlex=!0,this.leftDiv=new Qt(e),this.rightDiv=new Qt(e),this.leftDiv.appendTo(this.element),this.rightDiv.appendTo(this.element),this.titleDiv=new Qt(e,"span"),this.index=i,this.controlEventBus=t,this.eventInit(),this.classInit(),this.leftInit(),this.rightInit()}set show(e){this._show&&e&&(e=!1),e?(this.visible=!0,this._show=!0,this.timer&&clearTimeout(this.timer),this.timer=setTimeout((()=>{this.show=!1,this._show=!1}),3e4)):(this.visible=!1,this._show=!1)}set title(e){this._title=e,this.titleDiv.element.innerText=e}eventInit(){this.controlEventBus.listen("showMobileTopMenu",(e=>{e!==this.index&&(this.show=!1)}))}classInit(){this.addClass("gs-mobile-top-menu"),this.leftDiv.addClass("gs-mobile-top-menu-left"),this.rightDiv.addClass("gs-mobile-top-menu-right")}leftInit(){const e=new Qt(this.eventBus);e.visible=!1,e.appendTo(this.leftDiv),e.addClass("gs-mobile-mobileBack"),this.eventBus.listen("onMobileMagnify",(t=>{e.visible=t})),this.controlEventBus.listen("webkitOpenFullScreen",(t=>{1===t&&(e.visible=!0)})),this.controlEventBus.listen("CloseFullScreen",(()=>{e.visible=!1})),e.element.addEventListener("click",(()=>{this.controlEventBus.trigger("onMobileBack")})),this.titleDiv.appendTo(this.leftDiv)}rightInit(){const e=new Qt(this.eventBus);e.appendTo(this.rightDiv),e.addClass("gs-mobile-mobileClose"),e.element.addEventListener("click",(()=>{this.eventBus.emit("Close")}))}}class as extends Qt{constructor(e,t,i,s,n,r){super(t),this.id="",this.isFull=!1,this.targetDiv=null,this._info="",this.backImgSrc="",this.isSelected=!1,this.draggable=!1,this._index=0,this.clickWnd=!1,this.id=e,this.element.id=e,this.controlEventBus=i,this._index=s,this.draggable=n.draggable||!1,this.backImgSrc=!1===n.showBackgroundImage?"gs-background-no-video-only":"gs-background-no-video",this.playerDivStyleInit(),this.infoDiv=new Ui(this.eventBus),this.cornerInfo=new Vi(this.eventBus),this.recordInfo=new Vi(this.eventBus,!0),this.playerWrapper=new Jt(this.eventBus),this.isPlayBack=r.isPlayback,this.mobileTopMenu=new os(t,i,s),this.mobileBottomMenu=new rs({eventBus:t,controlEventBus:i,index:s,wndNums:r.wndNums,mobileMenuKeys:r.mobileMenuKeys}),this.append(),this.eventInit(n.draggable)}set info(e){this._info=e,e?(Bt(this.element,this.backImgSrc),Bt(this.element,"gs-display-block"),Lt(this.element,"gs-display-none")):(Lt(this.element,this.backImgSrc),Bt(this.element,"gs-display-none"),Lt(this.element,"gs-display-block"))}get info(){return this._info}set index(e){this._index=e,this.mobileTopMenu.index=e,this.mobileBottomMenu.index=e}get index(){return this._index}get playerWrapperId(){return this.playerWrapper.element.id}get video(){return this.playerWrapper.element.getElementsByTagName("video")[0]}get zoom(){return this.mobileBottomMenu.zoom}get threeDimensions(){return this.mobileBottomMenu.threeDimensions}set authOption(e){this.mobileBottomMenu.setAuthOption(e,this.isPlayBack)}set coderate(e){}set resolution(e){}updateStaticConfig(){this.mobileBottomMenu.updateStaticConfig()}append(){this.infoDiv.appendTo(this.element),this.cornerInfo.appendTo(this.element),this.recordInfo.appendTo(this.element),this.mobileTopMenu.appendTo(this.element),this.mobileBottomMenu.appendTo(this.element),this.playerWrapper.element.id="playerWrapper"+Math.random(),this.playerWrapper.addClass("player-wrapper"),this.playerWrapper.appendTo(this.element)}switchTopStatus(e,t){this.mobileBottomMenu.switchTopStatus(e,t)}playerDivStyleInit(){Lt(this.element,"gs-playerdiv"),Lt(this.element,this.backImgSrc)}setInfo(e){this.info=e,this.infoDiv.info=e}setCornerInfo(e){this.cornerInfo.info=e}removeTopMenu(){this.mobileBottomMenu.removeTopMenu()}setTitle(e){this.mobileTopMenu.title=e}setTopMenu(e){this.mobileBottomMenu.setTopMenu(e)}before(e,t){this.targetDiv=t,e.before(this.element)}eventInit(e=!1){this.element.onclick=e=>{this.clickWnd=!0,this.controlEventBus.trigger("selectById",this.id,this.index),this.eventBus.trigger("Click");const t=e.target;t&&"VIDEO"===t.tagName?(this.mobileTopMenu.show=!0,this.mobileBottomMenu.show=!0,this.controlEventBus.emit("showMobileTopMenu",this.index),this.controlEventBus.emit("showMobileBottomMenu",this.index)):t&&Array.from(t.classList).includes("player-wrapper")&&(this.mobileTopMenu.show=!1,this.mobileBottomMenu.show=!0,this.controlEventBus.emit("showMobileTopMenu",this.index),this.controlEventBus.emit("showMobileBottomMenu",this.index))},es.call(this),ts.call(this,e),is.call(this)}startRecord(){this.recordInfo.info="●正在本地录像"}stopRecord(){this.recordInfo.info=""}selected(e){e===this.id?(this.eventBus.trigger("onSelectWnd"),this.element.classList.contains("gs-win-selected")||Lt(this.element,"gs-win-selected")):Bt(this.element,"gs-win-selected")}capture(){var e;if(this.video){const t=document.createElement("canvas");t.width=this.video.videoWidth,t.height=this.video.videoHeight,null===(e=t.getContext("2d"))||void 0===e||e.drawImage(this.video,0,0,t.width,t.height);return t.toDataURL("image/jpeg")}return""}active(e,t){this.lastFrame=t||this.lastFrame,e?this.styleInit({background:"",backgroundSize:""}):(this.mobileTopMenu.show=!1,this.styleInit({background:"",backgroundSize:""}),this.setCornerInfo(""),this.recordInfo.info="")}readyActive(e){}destroy(){}}var hs;!function(e){e[e.BEFORECREATE=-1]="BEFORECREATE",e[e.CREATED=0]="CREATED",e[e.BEFOREOPEN=1]="BEFOREOPEN",e[e.OPENSUCCESSED=2]="OPENSUCCESSED",e[e.OPENFAILED=3]="OPENFAILED",e[e.BEFOREPLAY=4]="BEFOREPLAY",e[e.PLAYSUCCESSED=5]="PLAYSUCCESSED",e[e.PLAYFAILED=6]="PLAYFAILED",e[e.BEFORESTOP=7]="BEFORESTOP",e[e.STOPSUCCESSED=8]="STOPSUCCESSED",e[e.STOPFAILED=9]="STOPFAILED",e[e.BEFORECLOSE=10]="BEFORECLOSE",e[e.CLOSESUCCESSED=11]="CLOSESUCCESSED",e[e.CLOSEFAILED=12]="CLOSEFAILED",e[e.OTHER=13]="OTHER",e[e.SETVIDEOED=14]="SETVIDEOED"}(hs||(hs={}));class ls{constructor(e,t,i){this.id="",this.status=hs.BEFORECREATE,this.isPlayback=!1,this.title="",this.storageType="",this.streamType=1,this._index=0,this.info="",this.volume=50,this.isMute=!0,this.customInfo={},this.key="",this.speed=1,this.playList=[],this.playerIndex=0,this.supportH265=!0,this.lastFrame="",this.record=!1,this.sourceType=0,this.option=e,this._index=i,this.player=new Kt,this.status=hs.CREATED,this.isPlayback=e.isPlayback,this.reconnect=e.reconnect,this.controlEventBus=e.controlEventBus,this.keepLastFrame=e.keepLastFrame,this.callbackMap=e.callbackMap,this.control=t,this._keepRatio=Oi.config.keepRatio,this.volume=null==e.defaultVolume?this.volume:e.defaultVolume,this.id="gswebplayer"+Math.random();const s=new ve;Fi(s,this,this.controlEventBus),Gi(this.player._eventBus,this,this.controlEventBus);const n=zt()?as:ns;this.playerDiv=new n(this.id,s,this.controlEventBus,this._index,{rightMenuKeys:e.rightMenuKeys,topMenuKeys:e.topMenuKeys,showBackgroundImage:e.showBackgroundImage,draggable:e.draggable,divId:e.id,topForever:e.topForever,magnifyOnDblClick:e.magnifyOnDblClick,singleOnDblClickFullscreen:e.singleOnDblClickFullscreen},t.option);const r=document.getElementById(e.id);r&&e.timeline?this.playerDiv.before(e.timeline.element,r):r&&e.operationBox?this.playerDiv.before(e.operationBox.element,r):this.playerDiv.appendTo(r),this.openInfo=new Wt,this.openInfo.divToken=this.playerDiv.playerWrapperId}get isStop(){var e;return this.status==hs.STOPSUCCESSED||"stop"===(null===(e=this.timeLine)||void 0===e?void 0:e.status)}set keepRatio(e){this.changeKeepRatio(e),this._keepRatio=e,this.switchTopStatus("KeepRatio",e)}get keepRatio(){return this._keepRatio}set index(e){this._index=e,this.playerDiv.index=e}get index(){return this._index}get isRecording(){return this.player.isRecording}get wndInfo(){return{key:this.key,title:this.title,wndNo:this.index,mute:this.isMute,keepRatio:this.keepRatio,volume:this.volume,customInfo:this.customInfo,streamType:this.streamType,storageType:this.streamType,speed:this.speed,status:this.status,targetSpeed:null,chanId:this.key,url:this.openInfo.sourceURL,startTime:this.startTime,endTime:this.endTime,supportH265:this.supportH265,isRecording:this.isRecording}}get video(){const e=document.getElementById(this.id);return e?e.getElementsByTagName("video")[0]:null}get timeLine(){var e;return null===(e=this.control._timeLine)||void 0===e?void 0:e.getGroupItem(this.index)}set authOption(e){this._authOption=e,this.playerDiv.authOption=e}updateStaticConfig(){this.playerDiv.updateStaticConfig(),this.keepRatio=Oi.config.keepRatio}changeKeepRatio(e){const t=document.getElementById(this.id);if(t){const i=t.getElementsByTagName("video")[0],s=this.playerDiv.playerWrapper.element.getElementsByTagName("img")[0];if(s&&(s.style.objectFit=e?"":"fill"),i)return i.style.objectFit=e?"":"fill",!0}return!1}switchTopStatus(e,t){this.playerDiv.switchTopStatus(e,t)}playerReset(){this.playList.push(this.player),this.player=new Kt(this.playerIndex);const e=this.player;let t=this.playerIndex;Object.defineProperty(e,"index",{get:()=>t,set:e=>{t=e,this.removeVideo()}}),this.playerIndex++,this.removeVideo(),Gi(this.player._eventBus,this,this.controlEventBus)}seek(e){return this.player.seek(new Date(e).getTime())}stop(){return this.status=hs.BEFORESTOP,this.player.stop().then((()=>{this.status=hs.STOPSUCCESSED,this.controlEventBus.trigger("onPauseSuccess",this.wndInfo)})).catch((e=>{throw console.log(e),this.status=hs.STOPFAILED,this.controlEventBus.trigger("onPauseError",this.wndInfo,e),"pause error"}))}pause(){this.stop()}removeVideo(){clearTimeout(this.videoTimeout),this.videoTimeout=setTimeout((()=>{const e=this.playerDiv.element.getElementsByTagName("video");let t=this.player.index;t=void 0===t?1/0:t;for(let i=0;i<e.length;i++){const s=+e[i].id.split("_")[1];this.player.videoId!==e[i].id&&t>s&&e[i].remove()}}),1e3)}async close(e=!1,t=!1,i=!0){console.log(this.title,"player close start"),this.status=hs.BEFORECLOSE;for(let e in this.playList){let t=this.playList[e];t&&await t.close().then((t=>{this.playList[e]=null}))}let s;return this.keepLastFrame&&e?(s=this.playerDiv.capture(),this.lastFrame=s||this.lastFrame,this.playerDiv.lastFrame=s):this.playerDiv.lastFrame="",this.openInfo.sourceURL="",this.player.close().then((()=>{console.log(this.title,"player close success"),this.status=hs.CLOSESUCCESSED,this.playerReset(),t||(this.setInfo(""),e||(this.setTitle(""),this.keepRatio=Oi.config.keepRatio,this.key="",this.speed=1,this.customInfo={}),this.removeTopMenu(),i&&(this.startTime="",this.endTime="",this.supportH265=!0,this.record=!1),this.controlEventBus.trigger("closeTimeLine",this._index,i),this.status=hs.CREATED,this.playerDiv.active(!1,s),this.controlEventBus.trigger("onCloseSuccess",this.wndInfo,this._index))})).catch((e=>{this.playerReset(),console.log(this.title,"player close failed"),console.log(e),this.status=hs.CLOSEFAILED,this.controlEventBus.trigger("onCloseError",this.wndInfo,e)}))}setInfo(e,t){t&&t!==this.player||(console.log(this.title,e),this.info=e,this.playerDiv.setInfo(e))}setTitle(e){this.title=e,this.playerDiv.setTitle(e)}setStorageType(e){this.streamType=e}removeTopMenu(){this.playerDiv.removeTopMenu()}setTopMenu(){console.log("streamType",this.streamType),this.playerDiv.setTopMenu(this.streamType)}selected(e){this.playerDiv.selected(e)}openUrl(e){console.log(this.title,"player openUrl start"),this.setInfo("正在连接..."),this.openInfo.sourceURL=e,this.openInfo.isPlayback=this.isPlayback,this.openInfo.objectFit=this.keepRatio?"":"fill",this.openInfo.reconnect=this.reconnect,this.openInfo.maxRecordSize=this.option.maxRecordSize,this.openInfo.lastFrame=this.lastFrame,this.openInfo.sourceType=this.sourceType,this.status=hs.BEFOREOPEN;const t=this.player;return this.controlEventBus.trigger("defaultResetGetUrlErrorUrlError",this.key),this.player.open(this.openInfo).then((e=>{if(this.removeVideo(),1===this.player._error)throw"浏览器不支持h265视频";if(e!==gt.OK)throw e;if(this.status!=hs.BEFOREOPEN)throw"视频播放对象已被关闭";console.log(this.title,"player openUrl success"),this.status=hs.OPENSUCCESSED,this.controlEventBus.trigger("onOpenSuccess",this.wndInfo),this.playerDiv.active(!0)})).catch((e=>{console.log(this.title,"player openUrl failed",e),this.player.stop(),this.status==hs.BEFOREOPEN&&(this.status=hs.OPENFAILED);let i="";throw 1===this.player._error?(this.setInfo("浏览器不支持h265视频",t),this.supportH265=!1,i="浏览器不支持h265视频"):"player 已被关闭"==e?i="player 已被关闭":2==e?(this.setInfo("连接超时",t),i="连接超时"):((e+="").toLowerCase().indexOf("option")>=0&&(e="没有接收到视频数据"),e.toLowerCase().indexOf("describe")>=0&&(e="没有接收到视频数据"),this.status!==hs.CLOSESUCCESSED&&this.setInfo(e||"连接失败",t),i=e||"连接失败"),this.controlEventBus.trigger("onOpenError",this.wndInfo,i),i}))}playUrl(){return this.status=hs.BEFOREPLAY,this.player.play().then((e=>{if(0!==e)throw"连接超时";this.status=hs.PLAYSUCCESSED,this.setTopMenu(),this.setInfo(""),this.switchTopStatus("KeepRatio",this.keepRatio),setTimeout((()=>{this.setSpeed(this.speed)}),1e3),this.controlEventBus.trigger("onPlaySuccess",this.wndInfo),this.record&&(this.switchTopStatus("Record",!0),this.playerDiv.startRecord())})).catch((e=>{console.log(e),this.controlEventBus.trigger("onPlayError",this.wndInfo,e),this.setInfo("播放失败"),this.status=hs.PLAYFAILED}))}async play(e,t=0){if(this.sourceType=t,e){const t="object"==typeof this.customInfo?Object.assign({},this.customInfo):this.customInfo;return await this.close(!0),this.customInfo=t,this.openUrl(e).then((()=>this.playUrl()))}return this.playUrl()}soundControl(e,t){this.volume=e,this.isMute=0==e||!!t,this.player.setVolume(t?0:e),this.playerDiv.switchTopStatus("Voice",!t)}setSpeed(e){console.log("setSpeed",e),e>16&&(e=16),clearTimeout(this.timer);return new Promise(((t,i)=>{clearTimeout(this.timer),this.timer=setTimeout((()=>{const s={...this.wndInfo,targetSpeed:e};5!==this.status?(t(!0),this.speed=e,this.controlEventBus.trigger("onSetSpeedSuccess",s)):this.speed===e?(t(!0),this.controlEventBus.trigger("onSetSpeedSuccess",s)):this.player.setSpeed(e).then((()=>{t(!0),this.speed=e,this.controlEventBus.trigger("onSetSpeedSuccess",s)})).catch((e=>{i(!1),this.controlEventBus.trigger("onSetSpeedError",s,e)}))}),200)}))}setCornerInfo(e){this.playerDiv.setCornerInfo(e)}startRecord(e){this.status!==hs.PLAYSUCCESSED&&this.status!==hs.OPENSUCCESSED||(this.player.startRecord(e||this.key),this.switchTopStatus("Record",!0),this.playerDiv.startRecord())}stopRecord(){this.player.stopRecord(),this.switchTopStatus("Record",!1),this.playerDiv.stopRecord()}destroy(){this.playerDiv.destroy()}}class us{constructor(){this.onPlay=()=>{},this.onSeek=()=>{},this.onPause=()=>{},this.onFast=()=>{},this.onSlow=()=>{},this.onReset=()=>{},this.onSplit=()=>{},this.onSeekError=()=>{},this.onBookmark=()=>{},this.onAddBookmark=()=>{},this.onSegmentedPlay=()=>{},this.onVideoSetted=()=>{},this.onLabelClick=()=>{},this.onRecordSearch=()=>{},this.onSelectRecordDownload=()=>{},this.onSelectRecordLock=()=>{},this.onCustomSplit=()=>{},this.onTimeUpdate=()=>{}}}class cs extends us{constructor(){super(...arguments),this.onCloseSuccess=()=>{},this.onCloseError=()=>{},this.onPlaySuccess=()=>{},this.onPlayError=()=>{},this.onPauseSuccess=()=>{},this.onPauseError=()=>{},this.onOpenSuccess=()=>{},this.onOpenError=()=>{},this.onSetSpeedSuccess=()=>{},this.onSetSpeedError=()=>{},this.onSEI=()=>{},this.onBeforeClose=()=>!0,this.onBeforeCloseAll=()=>!0,this.onBeforeCloseOthers=()=>!0,this.onTimeout=()=>{},this.on3D=()=>{},this.onReconnect=()=>{},this.onDownloadByTime=()=>{},this.onDownload=()=>{},this.onGetUrlSuccess=()=>{},this.onGetUrlError=()=>{},this.onGetVideoSuccess=()=>{},this.onGetVideoError=()=>{},this.onDblClick=()=>{},this.onClick=()=>{}}}class ds extends cs{constructor(){super(...arguments),this.onClose=()=>{},this.onCloseAll=()=>{},this.onSelectWnd=()=>{},this.onCapture=()=>{},this.onMultipleCapture=()=>{},this.onShare=()=>{},this.onManualAlarm=()=>{},this.onVolumeChange=()=>{},this.onOpenKeepRatio=()=>{},this.onCloseKeepRatio=()=>{},this.onOpenFullScreen=()=>{},this.onCloseFullScreen=()=>{},this.onOpenMute=()=>{},this.onCloseMute=()=>{},this.onTest=()=>{},this.onCloseOthers=()=>{},this.onDrop=()=>{},this.onWndChange=()=>{},this.onMouseUp=()=>{},this.onChangeStream=()=>{},this.onOpenZoom=()=>{},this.onCloseZoom=()=>{},this.onOpen3D=()=>{},this.onClose3D=()=>{},this.onSliceVideo=()=>{},this.onError=()=>{},this.onRevertPlayer=()=>{},this.onOpenPlatformRecord=()=>{},this.onClosePlatformRecord=()=>{},this.onOpenRecord=()=>{},this.onOpenRecordSuccess=()=>{},this.onOpenRecordError=()=>{},this.onCloseRecord=()=>{},this.onCloseRecordSuccess=()=>{},this.onStartRecord=()=>{},this.onStartRecordSuccess=()=>{},this.onStartRecordError=()=>{},this.onStopRecord=()=>{},this.onStopRecordSuccess=()=>{},this.onStopRecordError=()=>{},this.onStartRecordSource=()=>{},this.onStopRecordSource=()=>{},this.onSourceRecording=()=>{},this.onDefaultDownloadSuccess=()=>{},this.onDefaultDownloadError=()=>{},this.onChangeStreamSuccess=()=>{},this.onChangeStreamError=()=>{},this.onLog=()=>{},this.onResumePlay=()=>{},this.onBeforeSplit=()=>!0}}const ps=new ds;function ms(e,t){return e.callbackMap.get(t)||ps[t]}function gs(e,t,i){!0===e?t():!1!==e&&e?e.then((e=>{if(!1!==e)return t();i&&i()})):i&&i()}function _s(e,t){!function(e,t){t.listen("selectById",((t,i)=>{e.selectById(t,i)}))}(e,t),function(e,t){t.listen("windowChange",(t=>{e.styleInit(t)}))}(e,t),function(e,t){t.listen("Close",((i,s)=>{gs(ms(e,"onBeforeClose")(i),(()=>{var n;const r=e.getPlayerByIndex(i.wndNo);null==r||r.close(!1,!1,s);const o=Object.assign({isPlayback:r.isPlayback,currentTime:null===(n=r.timeLine)||void 0===n?void 0:n.currentTime},i);t.trigger("onClose",o),t.trigger("defaultClose",i)}))}))}(e,t),function(e,t){const i=e.wrapperDiv;let s=!1,n="",r="",o="",a=0,h=0;const l=document;i&&(l.addEventListener("fullscreenchange",(()=>{console.log("fullscreenchange",s),s=!s,s?t.trigger("onOpenFullScreen"):(t.trigger("fullScreenExit",!1),t.trigger("onCloseFullScreen"),e.timeLineFullScreen=!1)})),l.onwebkitfullscreenchange=()=>{var l,u;if(s=!s,console.log("onwebkitfullscreenchange",s),s){t.trigger("onOpenFullScreen");const s=window.screen.height,l=window.screen.width,c=Math.min(s,l),d=Math.max(s,l);n=i.style.transform,r=i.style.width,o=i.style.height,a>h&&s>l&&(i.style.transform=`translateX(${(c-d)/2}px) translateY(${(d-c)/2}px) rotate(90deg)`),i.style.width=d+"px",i.style.height=c+"px",null===(u=e._timeLine)||void 0===u||u.refresh()}else t.trigger("fullScreenExit",!1),t.trigger("onCloseFullScreen"),e.timeLineFullScreen=!1,i.style.transform=n,console.log(r,o),i.style.width=r,i.style.height=o,null===(l=e._timeLine)||void 0===l||l.refresh()}),t.listen("OpenFullScreen",(()=>{const s=i.parentElement;s&&(zt()&&s.webkitRequestFullscreen?(a=window.screen.height,h=window.screen.width,s.webkitRequestFullscreen(),t.trigger("fullScreenChange",!0),t.trigger("webkitOpenFullScreen",e.wndNums)):s.requestFullscreen?(s.requestFullscreen(),t.trigger("fullScreenChange",!0)):s.mozRequestFullScreen?(s.mozRequestFullScreen(),t.trigger("fullScreenChange",!0)):s.webkitRequestFullscreen?(a=window.screen.height,h=window.screen.width,s.webkitRequestFullscreen(),t.trigger("fullScreenChange",!0)):s.msRequestFullscreen&&(s.msRequestFullscreen(),t.trigger("fullScreenChange",!0)),e.timeLineFullScreen=!0,e.showWatermark(!0))})),t.listen("CloseFullScreen",(()=>{const i=document;i.exitFullScreen?i.exitFullScreen():i.mozCancelFullScreen?i.mozCancelFullScreen():i.webkitExitFullscreen?(i.webkitExitFullscreen(),t.trigger("webkitExitFullscreen")):i.msExitFullscreen&&i.msExitFullscreen(),e.timeLineFullScreen=!1,e.showWatermark(!1)}))}(e,t),function(e,t){t.listen("CloseAll",((i,s=(()=>{}),n=(()=>{}))=>{gs(ms(e,"onBeforeCloseAll")(e.wndInfoList),(()=>{e.closeAll(i).then(s).catch(n),t.trigger("onCloseAll")}))}))}(e,t),function(e,t){t.listen("CloseOthers",(i=>{const s=ms(e,"onBeforeCloseOthers"),n=[];e.wndInfoList.forEach((e=>{e.wndNo!==i.wndNo&&n.push(e)})),gs(s(i,n),(()=>{e.closeOthers(),t.trigger("onCloseOthers",i)}))}))}(e,t),function(e,t){t.listen("splitChange",((t,i,s)=>{gs(ms(e,"onBeforeSplit")(t),(()=>{e.wndNums=t,i&&i()}),s)}))}(e,t),function(e,t){t.listen("updateTime",((t,i)=>{e.updateTime(t,i)}))}(e,t),function(e,t){t.listen("Speed",((t,i)=>{e.setSpeed(t,i)}))}(e,t),function(e,t){t.listen("reconnect",((e,i)=>{t.trigger("onReconnect",e,i)}))}(0,t),function(e,t){t.listen("timeout",(e=>{t.trigger("onTimeout",e)}))}(0,t),function(e,t){t.listen("closeTimeLine",((t,i=!0)=>{e.closeTimeLine(t,i)}))}(e,t),function(e,t){t.listen("KeepRatio",((t,i,s)=>{e.setKeepRatio(i,s,t)}))}(e,t),function(e,t){t.listen("play",((t,i,s,n,r)=>{e.play(t,i,s,n,r)}))}(e,t),function(e,t){t.listen("pause",(t=>{e.pause(t)}))}(e,t),function(e,t){t.listen("PauseAll",((i=(()=>{}),s=(()=>{}))=>{e.pauseAll().then(i).catch(s),t.trigger("onPauseAll")}))}(e,t),function(e,t){t.listen("setInfo",((t,i)=>{e.setInfoByIndex(t,i)}))}(e,t),function(e,t){t.listen("setTitle",((t,i)=>{e.setTitle(t,i)}))}(e,t),function(e,t){t.listen("setCustomInfo",((t,i)=>{e.setCustomInfo(t,i)}))}(e,t),function(e,t){t.listen("setKey",((t,i)=>{e.setKey(t,i)}))}(e,t),function(e,t){t.listen("setVideo",((t,i,s,n)=>{e.setVideo(t,i,s,n)}))}(e,t),function(e,t){t.listen("focusFreeWnd",(t=>{const i=e.getWndByConfig(void 0,void 0,e.playerOption.wndPlayRules);t&&t(i)}))}(e,t),function(e,t){let i;t.listen("dragStart",(e=>{i=e})),t.listen("drop",(s=>{var n;if(void 0!==i)t.emit("onWndChange",i,s),t.emit("onDefaultWndChange",i,s),t.emit("onWndExchange",i,s),e.playerOption.wndDraggable&&e.wndChange(i,s);else{const i=null===(n=e.playerIndexMap.get(s))||void 0===n?void 0:n.wndInfo;t.emit("onDrop",s,i)}i=void 0}))}(e,t),function(e,t){t.listen("share",((i,s)=>{var n;const r=(null===(n=e._timeLine)||void 0===n?void 0:n.currentTime)||$t();t.trigger("onShare",i,r,s)}))}(e,t),function(e,t){t.listen("manualAlarm",((e,i)=>{t.trigger("onManualAlarm",e,i)}))}(0,t),function(e,t){t.listen("mouseup",(e=>{t.trigger("onMouseUp",e)}))}(0,t),function(e,t){t.listen("onRateChange",(()=>{e._timeLine&&e._timeLine.enableRate()}))}(e,t),function(e,t){const i=new ds;Object.keys(i).forEach((s=>{t.listen(s,((...t)=>{const n=e.callbackMap.get(s)||i[s];n&&n(...t)}))}));const s=i=>{t.listen(i,(t=>{var s;const n=e.callbackMap.get(i);n&&n(null===(s=e.playerIndexMap.get(t))||void 0===s?void 0:s.wndInfo)}))};e.topMenuKeys.forEach((e=>{if("string"!=typeof e){const t="on"+e.value;s(t)}})),e.rightMenuKeys.forEach((e=>{if("string"!=typeof e&&"function"!=typeof e&&e.value)if(e.isSwitch){const t="onOpen"+e.value,i="onClose"+e.value;s(t),s(i)}else{const t="on"+e.value;s(t)}})),e.timeLineMenuKeys.forEach((e=>{if("string"!=typeof e&&"function"!=typeof e&&e.value)if(e.isSwitch){const t="onOpen"+e.value,i="onClose"+e.value;s(t),s(i)}else{const t="on"+e.value;s(t)}}))}(e,t),function(e,t){t.listen("SliceVideo",(()=>{t.trigger("onSliceVideo")}))}(0,t),function(e,t){t.listen("OpenRecord",((e,i)=>{const s=e.wndInfo;t.trigger("onStartRecord",s),e.isRecording?t.trigger("onStartRecordError",s):(e.startRecord(i),t.trigger("onStartRecordSuccess",s))})),t.listen("CloseRecord",(e=>{t.trigger("onStopRecord",e.wndInfo),e.stopRecord(),t.trigger("onStopRecordSuccess",e.wndInfo)})),t.listen("StartRecordSource",(t=>{const i=e.getPlayerByIndex(t);i.playerDiv.startRecord(),i.player.startRecordSource()})),t.listen("StopRecordSource",(t=>{const i=e.getPlayerByIndex(t);i.playerDiv.stopRecord(),i.player.stopRecordSource()}))}(e,t),function(e,t){t.listen("Download",(i=>{const s={...i};e._timeLine&&(s.currentTime=e._timeLine.getCurrentTimeByWndNo(i.wndNo)),t.trigger("onDownload",s)}))}(e,t),function(e,t){t.listen("showTimeLine",(()=>{e._timeLine&&e._timeLine.styleInit({display:"inline-block"})}))}(e,t),function(e,t){t.listen("setWndInfo",((t,i,s)=>{e.setTitle(t,s),e.setInfoByIndex(i,s)}))}(e,t),function(e,t){t.listen("onDblClick",(()=>{e.playerOption.singleOnDblClickFullscreen&&1===e.wndNums&&e.fullScreen(!e.timeLineFullScreen)}))}(e,t),function(e,t){t.listen("onMobileBack",(()=>{1===e.wndNums?e.fullScreen(!1):t.trigger("onMobileMagnify",e.currentPlayer.index)}))}(e,t)}function fs(e,t,i){!function(e,t,i){let s,n,r;function o(n){s(n,(()=>new Promise((s=>{let r={currentTime:e.currentTime,...t.currentPlayer.wndInfo};i.trigger(n,r),s("")}))))}s=e.setCallbackFunc.bind(e),function(){const e=e=>"on"+e[0].toUpperCase()+e.substr(1);t.timeLineMenuKeys.forEach((t=>{if("string"==typeof t){if(["capture","seek","fullScreen","closeAll"].includes(t))return;o(e(t))}else"function"!=typeof t&&o(e(t.value))}))}(),s("onPause",(e=>new Promise((s=>{const n=t.getPlayerByIndex(e),r=n.wndInfo,o=n.pause();i.trigger("onPause",r),s(o)})))),s("onPauseAll",(()=>new Promise(((e,t)=>{i.trigger("PauseAll",e,t)})))),n=(e,i,s,n,r=!1)=>new Promise((async(o,a)=>{const h=t.playerIndexMap.get(n)||t.currentPlayer,l=t._timeLine.timeLineGroup.getGroupItem(n).rate;let u=Math.pow(2,l);if(h.speed=u,r)await h.close(!0,!0);else{if(h.isStop&&h.wndInfo.url){const e=h.play().then((()=>(console.log("play success"),h.index)));return void o(e)}await h.close(!0,!0)}const c=At(new Date(i));let d=c+","+c+","+At(s);const p=h.wndInfo;p.chanId=e.chanId+"",p.storageType=e.storageType,p.streamType=e.storageType,h.setInfo("正在获取视频地址..."),t.callbackMap.get("onPlay")(d,p,(e=>{const t=h.openUrl(e).then((()=>h.seek(i))).then((()=>h.playUrl())).then((()=>h.index));o(t)}),(e=>{h.setInfo(e),a(e)}))})),s("onPlay",n),s("onChange",(e=>new Promise((i=>{t.selectByIndex(e)})))),r=(e,i)=>(t.playerIndexMap.get(i)||t.currentPlayer,new Promise(((s,n)=>{s(t.setSpeed(e,i))}))),s("onSeek",((e,s,o,a,h,l=!1)=>{let u=t.playerIndexMap.get(h)||t.currentPlayer;const c=u.wndInfo;return i.trigger("onSeek",c,s),c.supportH265?u.stop().then((()=>n(e,s,o,h,!0))).then((()=>r(a,h))).then((()=>{l&&t.pauseByIndex(h)})):new Promise(((e,t)=>{t(Di.UNSUPPORTH265)}))})),s("onSeekError",(e=>new Promise((s=>{console.error("该时间点无录像"),i.trigger("onSeekError",t.currentPlayer.wndInfo,e,"该时间点无录像")})))),s("onFullScreen",(()=>new Promise((e=>{i.trigger("OpenFullScreen")})))),s("onCloseFullScreen",(()=>new Promise((e=>{i.trigger("CloseFullScreen")})))),s("onSplit",(e=>new Promise(((s,n)=>{i.trigger("onSplit",t.wndNums,+e),i.trigger("splitChange",+e,s,n)})))),s("onCloseAll",(e=>new Promise((t=>{i.trigger("CloseAll",e,t)})))),s("onClose",((e,s)=>new Promise((n=>{const r=t.getPlayerByIndex(e);i.trigger("Close",r.wndInfo,s)})))),s("onFast",((e,s)=>new Promise((n=>{const o=t.currentPlayer.wndInfo;o.targetSpeed=[32,64].includes(e)?16:e,i.trigger("onFast",o),n(r(o.targetSpeed,s))})))),s("onSlow",((e,s)=>new Promise((n=>{const o=t.currentPlayer.wndInfo;o.targetSpeed=[1/32,1/64].includes(e)?1/16:e,i.trigger("onSlow",o),n(r(o.targetSpeed,s))})))),s("onReset",(e=>new Promise((s=>{const n=t.currentPlayer.wndInfo;n.targetSpeed=1,i.trigger("onReset",n),s(r(1,e))})))),s("onVideoEnd",(e=>new Promise((i=>{const s=t.getPlayerByIndex(e);i(s.stop().then((()=>s.close(!0,!0,!1))))})))),s("onLabelClick",(e=>new Promise((s=>{const n=xt("record","LabelManage",t.currentPlayer.authOption);i.trigger("onLabelClick",e,n),s("")})))),s("onCapture",(()=>new Promise((e=>{var s;const n=t.currentPlayer,r=n.playerDiv.video,o=document.createElement("canvas");o.width=r.videoWidth,o.height=r.videoHeight,null===(s=o.getContext("2d"))||void 0===s||s.drawImage(r,0,0,o.width,o.height);const a=o.toDataURL("image/jpeg"),h=n.title+" "+Ct(new Date,"yyyy-MM-dd hh:mm:ss")+".jpg";i.trigger("onCapture",a,h,n.wndInfo),e("")})))),s("onSelectRecordDownload",(e=>new Promise((s=>{const n=new Map;e.forEach(((e,i)=>{const s=t.getPlayerByIndex(i).wndInfo;s.startTime=e.startTime,s.endTime=e.endTime,n.set(i,s)})),i.trigger("onSelectRecordDownload",n),s("")})))),s("onSelectRecordLock",(e=>new Promise((s=>{const n=new Map;e.forEach(((e,i)=>{const s=t.getPlayerByIndex(i).wndInfo;s.startTime=e.startTime,s.endTime=e.endTime,n.set(i,s)})),i.trigger("onSelectRecordLock",n),s("")})))),s("onGetNextRecord",((e,t)=>new Promise((s=>{i.trigger("onGetNextRecord",e,t),i.trigger("onDefaultGetNextRecord",e,t),s("")}))))}(e,t,i)}class Ss{constructor(e,t,i,s){this.ajaxUrl=e,this.token=t,this.response=(n,r,o,a="")=>{let h={action:n.split("/").pop(),message:r,sequenceId:"2",token:"",type:"request"},l="";if("POSTFORM"==o||"GET"==o){Object.entries(r).forEach((e=>{if(l+=e[0],l+="=","object"==typeof e[1])l+=JSON.stringify(e[1]);else{let t=String(e[1]);l+="GET"==o?t.replace(" ","+"):t}l+="&"}))}const u="string"==typeof Ss.viewCode?Ss.viewCode:Ss.viewCode(),c="string"==typeof Ss.operateName?Ss.operateName:Ss.operateName(),d="string"==typeof Ss.operateTxt?Ss.operateTxt:Ss.operateTxt(),p="function"==typeof Ss.headers?Ss.headers(n):Ss.headers,m=n.indexOf("http://")>=0,g={};let _={ViewCode:u,OperateName:encodeURI(c),OperateTxt:encodeURI(d),OperateType:"1"};s||m||Object.assign(g,_),m||(Object.assign(g,{"Content-Type":"POSTFORM"==o?"application/x-www-form-urlencoded":"application/json",Authorization:"string"==typeof t?t:t()}),Object.assign(g,p)),Object.keys(g).forEach((e=>{g[e]||delete g[e]}));let f,S,y=m?n:e+n+("GET"==o?`?${l}`:"");switch("POSTFORM"===o?f=l:"POST"===o?f=JSON.stringify(h):"POSTBODY"===o&&(f=JSON.stringify(r)),o){case"POSTFORM":case"POSTBODY":case"POST":S="POST";break;case"GET":S="GET";break;default:S=o}return fetch(y,{method:S,mode:"cors",cache:"no-cache",credentials:"same-origin",headers:g,redirect:"follow",referrerPolicy:"no-referrer",body:f}).then((e=>{if(200!==e.status)throw i&&i(e),a+"接口网络错误";return e.json()})).catch((e=>{throw console.error(e),"网络异常"}))}}post(e,t,i=""){return this.response(e,t,"POST",i)}postForm(e,t,i=""){return this.response(e,t,"POSTFORM",i)}postBody(e,t,i=""){return this.response(e,t,"POSTBODY",i)}get(e,t,i=""){return this.response(e,t,"GET",i)}}Ss.viewCode="",Ss.operateName="",Ss.operateTxt="",Ss.headers={};class ys{constructor(e){this.getGoVideoStream=(e,t,i,s)=>i?this.ajax.post(this.defaultRequest.getStreamUrl||"/govideo-service/GoCloud-Base/GoVideo/GetStreamRequest",{chanId:e,clientType:107,streamType:t,streamAgentType:10,ipAddr:"",isARStream:i},"获取点流地址").then((e=>{if(e.message.streamURI)return e.message.streamURI;throw e.message.operDescri})):this.ajax.postForm(this.defaultRequest.getStreamUrl||"/vid-video-deal-service/realTimeStream/getRealTimeStreamForWeb",{channelId:e,streamType:t,streamAgentType:10},"获取点流地址").then((e=>{if(void 0!==e.data.innerErrorCode&&0==+e.data.innerErrorCode&&e.data.url)return e.data.url;if(null==e.data.innerErrorCode&&e.data.url)return e.data.url;if(e.data.userName){throw`设备${s?`【${s}】`:""}已被【${e.data.userName}】锁定，请先解锁后再进行操作`}throw e.data.innerErrorMsg||e.returnMsg})),this.getStream=(e,t,i,s,n=10)=>this.ajax.post(this.defaultRequest.getStreamUrl||"/GoCloud-Base/GoVideo/GetStreamRequest",{chanId:e,clientType:107,streamType:t,streamAgentType:n,ipAddr:"",isARStream:i},"获取点流地址").then((e=>{if(e.message.streamURI)return e.message.streamURI;throw e.message.operDescri})),this.getGoVideoOpenUrl=(e,t=10)=>this.ajax.postForm(this.defaultRequest.getOpenUrl||"/vid-video-deal-service/govideoStream/OpenUrlRequest",{message:{url:e,streamAgentType:t,ipAddr:""}},"获取点流地址").then((e=>{if(e.data.message.url)return e.data.message.url;throw e.data.message.operDescri})),this.getOpenUrl=e=>this.ajax.post(this.defaultRequest.getOpenUrl||"/GoCloud-Base/GoVideo/OpenUrlRequest",{url:e,streamAgentType:10,ipAddr:""},"获取点流地址").then((e=>{if(e.message.url)return e.message.url;throw e.message.operDescri})),this.getGoVideoRecord=(e,t,i,s,n,r=-1)=>{const o=s||`${t} 00:00:00`,a=n||`${t} 23:59:59`;return this.ajax.postForm(this.defaultRequest.getRecordUrl||"/vid-video-deal-service/govideoRecordDeal/getGoVideoOrGoVlinkRecordRequest",{channelIds:e,startTime:o,endTime:a,pageStart:1,pageNum:500,storageType:i,operType:2,relevantReason:r},"获取录像数据").then((e=>{if(0!==e.message.operResult)throw e.message.operDescri||e.message.returnMsg;return e.message.rcdInfoList}))},this.getRecord=(e,t,i,s,n,r=-1)=>{const o=s||`${t} 00:00:00`,a=n||`${t} 23:59:59`;return this.ajax.post(this.defaultRequest.getRecordUrl||"/GoCloud-Base/GoVideo/RecordFileRetrievalRequest",{storageType:i,mediaType:0,relevantReason:r,sortWay:0,startRowIndex:-1,wantedRcdNum:1e3,rcdStartTime:o,rcdEndTime:a,chanIdList:[e]},"获取录像数据").then((e=>{if(0!==e.message.operResult)throw e.message.operDescri;return e.message.rcdInfoList}))},this.getGoVideoRecordStream=(e,t,i,s=2,n=10,r=[])=>this.ajax.postForm(this.defaultRequest.getRecordStreamUrl||"/vid-video-deal-service/govideoRecordDeal/recordFileOperRequestTransfer",{operType:s,rcdFilename:e,id:i,storageType:t,relevantReason:-1,channelId:i,chanId:i,streamAgentType:n,watermarkInfo:r,wmCount:r.length},"获取录像点流地址").then((e=>{if(0!==e.message.operResult)throw e.message.operDescri||e.message.returnMsg;return e.message.url})),this.getRecordStream=(e,t,i,s=2,n=10,r=[])=>{const[o,a,h]=e.split(",");let[l,u,c]=o.split("-"),[d,p,m]=a.split("-").slice(3),[g,_,f,S,y,v]=h.split("-");return this.ajax.post(this.defaultRequest.getRecordStreamUrl||"/GoCloud-Base/GoVideo/RecordFileOperRequest",{chanId:i,ipAddr:"",offset:`${l}-${u}-${c} ${d}:${p}:${m}`,operType:s,rcdStartTime:`${l}-${u}-${c} ${d}:${p}:${m}`,rcdEndTime:`${g}-${_}-${f} ${S}:${y}:${v}`,relevantReason:-1,storageType:t,streamAgentType:n,watermarkInfo:r},"获取录像点流地址").then((e=>{if(0!==e.message.operResult)throw e.message.operDescri;return e.message.url}))},this.getStreamByOneForOneCode=(e,t,i)=>this.ajax.postForm(this.defaultRequest.getStreamUrl||"/vid-video-deal-service/openRealTimeInterface/getOtherRealTimeStreamByOneForOneCode",{oneForOneCode:e,streamType:t,streamAgentType:10,ip:"",isARStream:i},"获取一机一档点流地址").then((e=>{if(0!==e.returnCode)throw e.returnMsg;return e.data})),this.getRecordByOneForOneCodes=(e,t,i,s,n,r=-1)=>{const o=s||`${t} 00:00:00`,a=n||`${t} 23:59:59`;return this.ajax.postForm(this.defaultRequest.getRecordUrl||"/vid-video-deal-service/openRecordInterface/getGoVideoOrGoVlinkRecordRequestByOneForOneCodes",{oneForOneCodes:e,startTime:o,endTime:a,pageStart:1,pageNum:500,storageType:i,operType:2,relevantReason:r},"获取一机一档录像数据").then((e=>{if(0!==e.message.operResult)throw e.message.returnMsg;return e.message.rcdInfoList}))},this.getRecordStreamByOneForOneCode=(e,t,i)=>this.ajax.postForm(this.defaultRequest.getRecordStreamUrl||"/vid-video-deal-service/openRecordInterface/recordFileOperRequestByOneForOneCode",{oneForOneCode:i,rcdFilename:e,storageType:t,operType:2,relevantReason:-1,streamAgentType:10},"获取一机一档录像点流地址").then((e=>{if(0!==e.operResult)throw e.operDescri;return e.url})),this.getvidRecordLabel=(e,t,i)=>(e=e.split("-").map((e=>e.padStart(2,"0"))).join("-"),t=t.split("-").map((e=>e.padStart(2,"0"))).join("-"),this.ajax.get(this.defaultRequest.getvidRecordLabelUrl||"/vid-video-deal-service/v2/rcd_label/list",{page:1,size:999,channelId:i,positionStart:e,positionEnd:t},"获取录像标签").then((e=>{if(!e.data.data)throw"v2/rcd_label/list 接口异常";return e.data.data}))),this.getDownloadState=e=>this.ajax.get(e+"&getstate=1",{}).then((e=>(console.log(e),e))),this.getAuthOption=(e,t="")=>{let i=t;return t instanceof Function&&(i=t()),this.ajax.postBody("/vid-video-deal-service/permit/getChanAccessMenu",{userId:i,chanIds:[e]})};const{token:t,errHandle:i,useCMS:s}=e,n=e.ip;this.ajax=new Ss(n,t,i,s),this.defaultRequest=e}}var vs=({defaultRequestApi:t,controlUse:i})=>{const{getRecordStream:s,getOpenUrl:n,getRecord:r,getDownloadState:o}=t;return({chanId:t,storageType:a,startTime:h,endTime:l,name:u,speed:c,watermarkInfo:d=[],autoDownload:p})=>{const m=h instanceof Date?h:new Date(h),g=l instanceof Date?l:new Date(l),_=At(m)||"",f=At(m,"{y}-{m}-{d} {h}:{i}:{s}")||"",S=At(g)||"",y=At(g,"{y}-{m}-{d} {h}:{i}:{s}")||"",v=_+","+_+","+S,w=u+"-"+At(m,"{y}{m}{d}T{h}{i}{s}")+"-"+At(g,"{y}{m}{d}T{h}{i}{s}");p=void 0===p?e.recordDownloadConfig.autoDownload:p;let T=JSON.parse(sessionStorage.getItem("downloadUrl")||"[]");const I=d.length?12:10;return r(String(t),"",a,String(f),String(y)).then((e=>{if(!e.length)throw"该时间段无录像"})).then((()=>Promise.all(T.map((e=>o(e)))).then((e=>{if(e.forEach(((e,t)=>{+e.state||(T[t]="")})),T=T.filter((e=>e)),T.length>=5)throw"已达到同时下载上限，请等待当前下载任务完成！";sessionStorage.setItem("downloadUrl",JSON.stringify(T))})))).then((()=>s(v,a,t,3,I,d))).then((e=>"rtsp"===e.split("://")[0]?n(e,12):e)).then((e=>{const t=document.createElement("a");return t.href=e.replace(".mp4",`&speed=${c||1}&filename=${w}.mp4`),T.push(t.href),sessionStorage.setItem("downloadUrl",JSON.stringify(T)),p&&(t.target="_blank",t.click()),i.controlEventBus.trigger("onDefaultDownloadSuccess",w+".mp4"),t.href})).catch((e=>{i.controlEventBus.trigger("onDefaultDownloadError",w+e)}))}},ws=({focusFreeWnd:e,focusWndByKey:t,controlUse:i,keyMap:s,defaultRequestApi:n})=>{const{getRecord:r,getAuthOption:o}=n;const a=async({wndNo:e,chanId:t,title:n,date:a,storageType:h,remakeOption:l,isFocusWndByKey:u,key:c,getTimelineRecord:d})=>{var p;if(u){const s=i.focusWndByKey(t);void 0!==s&&(e=s)}await i.closeByIndex(e),i.controlEventBus.listen("onWndChange",((t,i)=>{e==t?e=i:e==i&&(e=t)})),s.set(e,c),l.wndNo=e;if(s.get(e)===c){if(l.customInfo?(l.customInfo.key=c,l.customInfo.date=a):l.customInfo={key:c,date:a},i.controlEventBus.trigger("setCustomInfo",l.customInfo,e),i.controlEventBus.trigger("setKey",t,e),!r)return void i.controlEventBus.trigger("setTitle",n,e);l.seekTime=l.seekTime||l.startTime,l.startTime||l.endTime?(l.startTime=l.startTime||`${a} 00:00:00`,l.endTime=l.endTime||`${a} 23:59:59`):(null===(p=i.option)||void 0===p?void 0:p.getAroundRecord)?(l.startTime=`${function(e){const t=new Date(e);return Ct(new Date(t.getTime()-864e5),"yyyy-MM-dd")}(a)} 00:00:00`,l.endTime=`${Ot(a)} 23:59:59`):(l.startTime=`${a} 00:00:00`,l.endTime=`${a} 23:59:59`),function(e,t){i.controlEventBus.trigger("setWndInfo",e,"正在获取录像...",t)}(n,e)}let m;return m=Pt.authOption?o(t).then((e=>(l.authOption=Object.values(e.data.chanMenuMap)[0],r(t,a,h,l.startTime,l.endTime,l.relevantReason)))):r(t,a,h,l.startTime,l.endTime,l.relevantReason),m.then((r=>{var o;if(s.get(e)===c){l.wndNo=e;const s=null===(o=i.playerIndexMap.get(e))||void 0===o?void 0:o.wndInfo;return i.controlEventBus.trigger("setKey","",e),l.storageType=h,i.controlEventBus.trigger("setVideo",t,n,r,l),i.controlEventBus.trigger("onGetVideoSuccess",s,r),function(e,t,i){if(!e.length)return[];e.forEach((e=>{e.startStamp=new Date(e.rcdStartTime).getTime(),e.endStamp=new Date(e.rcdEndTime).getTime()}));const s=(e=e.sort(((e,t)=>e.startStamp-t.startStamp)))[0].rcdStartTime,n=e[e.length-1].rcdEndTime;if(t||i){t=t&&new Date(t).getTime()>new Date(s).getTime()?t:s,i=i&&new Date(i).getTime()<new Date(n).getTime()?i:n;const r=new Date(t),o=new Date(i),a=JSON.parse(JSON.stringify(e)).filter((e=>{const t=new Date(e.rcdStartTime),i=new Date(e.rcdEndTime);return t<o&&i>r}));return a.length?(r>new Date(a[0].rcdStartTime)&&(a[0].rcdStartTime=t),o<new Date(a[a.length-1].rcdEndTime)&&(a[a.length-1].rcdEndTime=i),a):[]}return t=s,i=n,e}(r,l.startTime,l.endTime)}})).catch((t=>{var n;if(s.get(e)===c){const s=null===(n=i.playerIndexMap.get(e))||void 0===n?void 0:n.wndInfo;i.controlEventBus.trigger("setInfo",t,e),i.controlEventBus.trigger("onGetVideoError",s,t)}}))};return(s,n,r,o,h,l=!0)=>new Promise(((u,c)=>{const d=Object.assign({},h||{});let p,m=null==h?void 0:h.wndNo,g=Math.random(),_=void 0!==(null==h?void 0:h.focusFreeWnd)?null==h?void 0:h.focusFreeWnd:e,f=void 0!==(null==h?void 0:h.focusWndByKey)?null==h?void 0:h.focusWndByKey:t;void 0!==m?p=a({wndNo:m,chanId:s,title:n,date:o,storageType:r,remakeOption:d,isFocusWndByKey:f,key:g,getTimelineRecord:l}):_&&f?void 0!==i.focusWndByKey(s)?p=a({wndNo:m,chanId:s,title:n,date:o,storageType:r,remakeOption:d,isFocusWndByKey:f,key:g,getTimelineRecord:l}):i.controlEventBus.trigger("focusFreeWnd",(e=>{m=e,p=a({wndNo:m,chanId:s,title:n,date:o,storageType:r,remakeOption:d,isFocusWndByKey:f,key:g,getTimelineRecord:l})})):_?i.controlEventBus.trigger("focusFreeWnd",(e=>{m=e,p=a({wndNo:m,chanId:s,title:n,date:o,storageType:r,remakeOption:d,isFocusWndByKey:f,key:g,getTimelineRecord:l})})):(m=i.currentPlayer.index,p=a({wndNo:m,chanId:s,title:n,date:o,storageType:r,remakeOption:d,isFocusWndByKey:f,key:g,getTimelineRecord:l})),u(p)}))};class Ts{constructor(e){const{useCMS:t,useOneForOneCode:i,errHandle:s}=e,n=new ys(e);this.getStream=i?n.getStreamByOneForOneCode:t?n.getStream:n.getGoVideoStream,this.getRecord=i?n.getRecordByOneForOneCodes:t?n.getRecord:n.getGoVideoRecord,this.getRecordStream=i?n.getGoVideoRecordStream:t?n.getRecordStream:n.getGoVideoRecordStream,this.getOpenUrl=i?n.getGoVideoOpenUrl:t?n.getOpenUrl:n.getGoVideoOpenUrl,this.getvidRecordLabel=t?void 0:n.getvidRecordLabel,this.getDownloadState=n.getDownloadState;this.getAuthOption=t=>n.getAuthOption(t,e.userId)}}const Is=(e,t)=>{const{focusFreeWnd:i,isARStream:s,focusWndByKey:n,reconnect:r}=t,o=new Ts(t),a=new Map,h=t=>{const{startTime:i,endTime:s,chanId:n,wndNo:r}=t,a=o.getvidRecordLabel;if(a&&i&&s&&n)return a(i,s,n).then((t=>{var i;null===(i=e._timeLine)||void 0===i||i.setLabels(t,r)}))};!function({controlUse:e,keyMap:t,setLabels:i,defaultRequest:s,defaultRequestApi:n,reconnect:r}){const{isARStream:o}=s,{getStream:a,getOpenUrl:h,getRecordStream:l,getRecord:u}=n;function c(t){e.controlEventBus.clear(t)}if(c("onDefaultWndChange"),e.controlEventBus.listen("onDefaultWndChange",((e,i)=>{const s=t.get(e),n=t.get(i);t.set(e,n),t.set(i,s)})),c("onDefaultChangeStream"),e.controlEventBus.listen("onDefaultChangeStream",(async t=>{const{chanId:i,streamType:s,title:n,wndNo:r}=t,l=1===s?2:1,u=e.getPlayerByKey(i);u&&(await u.close(!0),u.setInfo("正在获取链接地址。。。"),a(i,l,o,n).then((e=>"rtsp"===e.split("://")[0]?h(e):e)).then((t=>{u&&(e.controlEventBus.trigger("onChangeStreamSuccess",u.wndInfo),u.streamType=l,u.setInfo("正在连接..."),u.play(t))})).catch((t=>{u.streamType=l,e.controlEventBus.trigger("setInfo",t,r)})))})),c("onDefaultVideoSetted"),e.controlEventBus.listen("onDefaultVideoSetted",(async e=>{i(e)})),c("onDefaultRevertPlayer"),e.controlEventBus.listen("onDefaultRevertPlayer",(t=>{console.log("onDefaultRevertPlayer",t);const i=e.defaultPlay;i&&t.forEach((e=>{const{chanId:t,title:s,streamType:n,wndNo:r,customInfo:o}=e;i(t,s,n,{wndNo:r,customInfo:o})}))})),e.callbackMap.set("onPlay",((i,s,n,r)=>{const o=s.chanId,a=s.streamType;l(i,a,o).then((e=>h(e))).then((e=>{n(e)})).catch((i=>{if("string"==typeof i&&t.get(s.wndNo)===s.customInfo.key){const t=e.playerIndexMap.get(s.wndNo);null==t||t.setInfo(i),e.controlEventBus.trigger("onOpenError",s,i),e.controlEventBus.trigger("onPlayError",s,i),e.controlEventBus.trigger("onGetUrlError",s,i),e.controlEventBus.trigger("defaultGetUrlError",s,i)}r(i)}))})),c("defaultCloseAll"),e.controlEventBus.listen("defaultCloseAll",(()=>{t.clear()})),c("defaultCloseOthers"),e.controlEventBus.listen("defaultCloseOthers",(()=>{const i=e.currentPlayer.index;t.forEach(((e,s)=>{s!==i&&t.delete(s)}))})),c("defaultSplitChange"),e.controlEventBus.listen("defaultSplitChange",(e=>{t.forEach(((i,s)=>{s>=e&&t.delete(s)}))})),c("defaultClose"),e.controlEventBus.listen("defaultClose",(e=>{t.set(e.wndNo,"")})),c("onResumePlay"),e.controlEventBus.listen("onResumePlay",(t=>{const{chanId:i,title:s,customInfo:n,streamType:r,storageType:o,wndNo:a}=t;!e._isPlayback&&e.defaultPlay&&e.defaultPlay(i,s,r,{wndNo:a,customInfo:n})})),c("onDefaultGetNextRecord"),e.controlEventBus.listen("onDefaultGetNextRecord",((t,i)=>{const s=e.getPlayerByIndex(i).wndInfo,{chanId:n,storageType:r}=s;u(n,t,r).then((t=>{var s;null===(s=e._timeLine)||void 0===s||s.eventBus.trigger("getNextRecordCallback",t,i)}))})),e.reconnect){c("defaultReconnect"),e.controlEventBus.listen("defaultReconnect",(async(t,i)=>{var s;const{key:n,title:r,customInfo:o,streamType:a,storageType:h,wndNo:l}=t,u=e.getPlayerByKey(n);if(n&&u){{const n=t.endTime;if(e._isPlayback&&n&&i&&i>=new Date(n).getTime())return void(null===(s=e._timeLine)||void 0===s||s.stop(l));u.setInfo("正在重连。。。")}if(!e._isPlayback&&e.defaultPlay)u.key="",e.defaultPlay(n,r,a,{isReconnect:!0,wndNo:l,customInfo:o});else if(e._isPlayback&&e.defaultPlayback&&e._timeLine){const t=e._timeLine.getCurrentTimeByWndNo(l),i=$t(new Date(new Date(t).getTime()+1e3));e._timeLine.seekTime(i,!0,l)}}}));const i=e=>{const t=Math.floor(e/10);return Math.min(300,5*Math.pow(2,t))};c("defaultGetUrlError");const s=new Map;e.controlEventBus.listen("defaultGetUrlError",((n,r)=>{const{key:o,wndNo:a}=n,h=e.getPlayerByKey(o),l=s.get(o),u=(null==l?void 0:l.times)||0,c=t.get(a);l&&(clearInterval(l.interval),clearTimeout(l.timeout));const d=i(u),p=setInterval((()=>{h&&(t.get(h.index)==c?h.setInfo(r+`\n第${u+1}次重连中，请稍等...`):(null==h||h.setInfo(""),e.controlEventBus.trigger("defaultResetGetUrlErrorUrlError")))}),1e3),m=setTimeout((()=>{e.controlEventBus.trigger("defaultReconnect",n),clearInterval(p),clearTimeout(m)}),1e3*d);s.set(o,{interval:p,timeout:m,times:u+1})})),c("defaultResetGetUrlError"),e.controlEventBus.listen("defaultResetGetUrlError",(t=>{if(!t)return;e.getPlayerByKey(t);const i=s.get(t);i&&(clearInterval(i.interval),clearTimeout(i.timeout)),s.set(t,{interval:null,timeout:null,times:0})}))}c("defaultRateReconnect"),e.controlEventBus.listen("defaultRateReconnect",(t=>{const{chanId:i,title:s,customInfo:n,streamType:r,storageType:o,wndNo:a}=t;if(e._isPlayback&&e.defaultPlayback&&e._timeLine){const t=e._timeLine.getCurrentTimeByWndNo(a),i=$t(new Date(new Date(t).getTime()+1e3));e._timeLine.seekTime(i,!0)}}))}({defaultRequest:t,defaultRequestApi:o,setLabels:h,keyMap:a,controlUse:e,reconnect:r}),e.defaultPlay=(({controlUse:e,focusFreeWnd:t,focusWndByKey:i,keyMap:s,isARStream:n,defaultRequestApi:r})=>{const{getStream:o,getOpenUrl:a,getAuthOption:h}=r;function l(t,i){e.controlEventBus.trigger("setWndInfo",t,"正在获取播放连接。。。",i)}const u=async({wndNo:t,chanId:i,streamType:r,title:u,remakeOption:c,key:d,isFocusWndByKey:p})=>{const m=e.getPlayerByKey(i);if(p){if(m&&m.streamType==r)return m.index<e._length&&e.selectByIndex(m.index),void(t=m.index);if(m){if(m.index>=e._length)return void(m.streamType=r);e.selectByIndex(m.index),t=m.index,s.set(t,d)}else await e.closeByIndex(t,!0),l(u,t)}else await e.closeByIndex(t,c.isReconnect),l(u,t);if(s.set(t,d),c.wndNo=t,s.get(t)!==d)return;let g;return e.controlEventBus.trigger("setCustomInfo",c.customInfo,t),e.controlEventBus.trigger("setKey",i,t),e.controlEventBus.listen("onWndChange",((e,i)=>{t==e?t=i:t==i&&(t=e)})),g=Pt.authOption?h(i).then((e=>(c.authOption=Object.values(e.data.chanMenuMap)[0],o(String(i),r,n,u)))):o(String(i),r,n,u),g.then((e=>"rtsp"===e.split("://")[0]?a(e):e)).then((n=>{var o;if(s.get(t)===d){c.wndNo=t;const s=null===(o=e.playerIndexMap.get(t))||void 0===o?void 0:o.wndInfo;e.controlEventBus.trigger("onGetUrlSuccess",s,n),e.controlEventBus.trigger("setKey","",t),e.controlEventBus.trigger("play",i,u,n,r,c)}})).catch((async i=>{var n;if(s.get(t)===d){m&&await m.close(m.index==t),e.controlEventBus.trigger("setInfo",i,t);const s=null===(n=e.playerIndexMap.get(t))||void 0===n?void 0:n.wndInfo;e.controlEventBus.trigger("onOpenError",s,i),e.controlEventBus.trigger("onPlayError",s,i),e.controlEventBus.trigger("onGetUrlError",s,i),e.controlEventBus.trigger("defaultGetUrlError",s,i)}}))};return(s,n,r,o)=>new Promise(((a,h)=>{const l=Object.assign({},o||{});let c,d=null==o?void 0:o.wndNo,p=Math.random(),m=void 0!==(null==o?void 0:o.focusFreeWnd)?null==o?void 0:o.focusFreeWnd:t,g=void 0!==(null==o?void 0:o.focusWndByKey)?null==o?void 0:o.focusWndByKey:i;void 0!==d?(c=u({wndNo:d,chanId:s,streamType:r,title:n,remakeOption:l,key:p,isFocusWndByKey:g}),a(c)):m?e.controlEventBus.trigger("focusFreeWnd",(e=>{d=e,c=u({wndNo:d,chanId:s,streamType:r,title:n,remakeOption:l,key:p,isFocusWndByKey:g}),a(c)})):(d=e.currentPlayer.index,c=u({wndNo:d,chanId:s,streamType:r,title:n,remakeOption:l,key:p,isFocusWndByKey:g}),a(c))}))})({controlUse:e,focusFreeWnd:i,focusWndByKey:n,keyMap:a,isARStream:s,defaultRequestApi:o}),e.defaultPlayback=ws({controlUse:e,focusFreeWnd:i,focusWndByKey:n,keyMap:a,defaultRequestApi:o}),e.defaultDownload=vs({defaultRequestApi:o,controlUse:e}),e.defaultUpdateLabels=(({controlUse:e,setLabels:t})=>i=>{if(void 0!==i){const s=e.playerIndexMap.get(i)||e.currentPlayer;if(!xt(s.isPlayback,"Label",s.authOption))return Promise.all([]);const n=s.wndInfo;return t(n)||Promise.all([])}const s=[];return e.playerIndexMap.forEach((e=>{if(!xt(e.isPlayback,"Label",e.authOption))return;const i=e.wndInfo,n=t(i);n&&s.push(n)})),Promise.all(s)})({controlUse:e,setLabels:h})};class Es extends Oi{constructor(e,t){super(e,t),this.defaultRequest=!1}use(t){t instanceof e?(this.defaultRequest=!0,Is(this,t)):t instanceof Pt&&(Pt.authOption=t.option)}}class Rs extends Es{constructor(e,t){super(e,t),this._fullScreen=!1}get wndNums(){return this._length}set wndNums(e){this.setWndNums(e,!0)}get wndInfoList(){const e=[];return this._playerMap.forEach((t=>{e.push(t.wndInfo)})),e}set storageType(e){this.currentPlayer.storageType=e}set timeLineFullScreen(e){this._fullScreen=e,this._timeLine&&(this._timeLine.fullScreen=e)}get timeLineFullScreen(){return this._fullScreen}setInfo(e,t){var i;null===(i=this._playerMap.get(t||this._currentId))||void 0===i||i.setInfo(e)}setInfoByIndex(e,t){(this.playerIndexMap.get(t)||this.currentPlayer).setInfo(e)}setStatus(e){this.currentPlayer.status=e}setSpeed(e,t){return this.getPlayerByIndex(t).setSpeed(e)}setTitle(e,t){var i;if(null==t||t===this.currentPlayer.index)null===(i=this.currentPlayer)||void 0===i||i.setTitle(e);else{const i=this.selectByIndex(t,!1);null==i||i.setTitle(e)}this._timeLine&&this._timeLine.setTitle(e,t)}setKey(e,t){if(null==t)this.currentPlayer.key=e;else{this.selectByIndex(t,!1).key=e}}setCustomInfo(e,t){if(null==t)this.currentPlayer.customInfo=e;else{this.selectByIndex(t,!1).customInfo=e}}setKeepRatio(e,t=!0,i=-1){e=null==e?!this.currentPlayer.keepRatio:e;const s=-1===i?this.currentPlayer:this.playerIndexMap.get(i);s&&(s.keepRatio=e,t&&s.switchTopStatus("KeepRatio",e))}setAllKeepRatio(e){this.playerIndexMap.forEach((t=>{t.keepRatio=e,t.switchTopStatus("KeepRatio",e)}))}setCornerInfo(e,t){(this.playerIndexMap.get(t)||this.currentPlayer).setCornerInfo(e)}setWndNums(e,t=!0,i=!0){this.controlEventBus.trigger("defaultSplitChange",e);const s=this._length;this._length=e;const n=[];if(e>=s)for(let t=s;t<e;t++){const e=this.playerIndexMap.get(t);e&&e.wndInfo.chanId&&n.push(e.wndInfo);const i=new ls(this.playerOption,this,t),{id:s}=i;i.index=t,this.playerIndexMap.set(t,i),this._playerMap.set(s,i)}else{for(let i of this._playerMap.values())i.index>=e&&(i.close(!t),i.playerDiv.element.remove(),t&&(this._playerMap.delete(i.id),this.playerIndexMap.delete(i.index)));this.selectByIndex(0)}if(this._timeLine&&(this._timeLine.groupLength=e),i){const e=Math.sqrt(this._length);e===Math.floor(e)&&(this.playerTemplate={row:e,col:e}),this.styleInit()}this.controlEventBus.emit("onRevertPlayer",n),this.controlEventBus.emit("onDefaultRevertPlayer",n)}}class Ds extends Rs{constructor(e,t){super(e,t),this.resumeMap=new Map,_s(this,this.controlEventBus),this._timeLine&&fs(this._timeLine,this,this.controlEventBus),this.watermark=this.watermarkInit()}watermarkInit(){const e=new Jt(this.controlEventBus);e.visible=!1;const t=document.getElementById(this._divId);return e.appendTo(t),e.addClass("gs-watermark"),e}showWatermark(e){const t=this.watermark;if(e){const e=Ds.waterMarkInfo;if(!e.content)return;const i=document.createElement("canvas"),s=e.fontSize;i.width=e.width,i.height=e.height;const n=i.getContext("2d");n.rotate(-20*Math.PI/180),n.font=`${s}px Vedana`,n.fillStyle="#666666",n.textAlign="center",n.textBaseline="middle",e.content.split("/n").forEach(((e,t)=>{n.fillText(e,i.width/2,i.height+t*s)})),t.styleInit({pointerEvents:"none",top:"40px",left:"0px",opacity:e.opacity/100+"",position:"fixed",zIndex:"100000",width:`${document.documentElement.clientWidth}px`,height:`${document.documentElement.clientHeight}px`,background:`url(${i.toDataURL("image/png")}) left top repeat`}),t.visible=!0}else t.visible=!1}changeStream(e,t){const i=this.getPlayerByKey(e);if(!i)return;t!==i.wndInfo.streamType&&(i.playerDiv.eventBus.trigger("Zoom",!1),this.controlEventBus.trigger("onChangeStream",i.wndInfo),this.controlEventBus.trigger("onDefaultChangeStream",i.wndInfo))}fullScreen(e){this.controlEventBus.trigger(e?"OpenFullScreen":"CloseFullScreen"),this.timeLineFullScreen=e}async play(e,t,i,s,n){let{wndNo:r,customInfo:o,keepRatio:a,focusWndByKey:h,sourceType:l}=n||{},u=this.getPlayerByKey(e);if(u){if(h&&s==u.streamType)return void(u.index<this._length&&this.selectByIndex(u.index));if(h){if(r=u.index,u.index>=this._length)return void(u.streamType=s);await u.close()}else await u.close()}let c=this.selectByIndex(r,!1);return c.streamType=s,c.key=e,c.keepRatio=Boolean(a),c.customInfo=o,c.setTitle(t),c.record=(null==n?void 0:n.record)||!1,c.authOption=(null==n?void 0:n.authOption)||[],c.play(i,l)}pause(e){var t;this._timeLine&&(console.log("pause"),null===(t=this._timeLine.eventBus)||void 0===t||t.emit("stop"))}pauseByIndex(e){var t;this._timeLine&&(console.log("pause"),null===(t=this._timeLine.eventBus)||void 0===t||t.emit("stop",e))}pauseAll(){const e=[];return this._isPlayback&&this._timeLine?(this.resumeMap=this._timeLine.statusMap,this._playerMap.forEach((t=>{e.push(t.stop())})),this._timeLine.stopAll()):this._playerMap.forEach((t=>{e.push(t.stop())})),Promise.all(e)}resumePlay(){this._isPlayback&&this._timeLine?(this._playerMap.forEach((e=>{"play"===this.resumeMap.get(e.index)&&e.play()})),this._timeLine.playAll(this.resumeMap)):this._playerMap.forEach((e=>{e.wndInfo.url&&e.status===hs.STOPSUCCESSED&&e.play()}))}playAll(){this._isPlayback&&this._timeLine?this._playerMap.forEach((e=>{var t,i;const s=e.wndInfo.url,n=e.index;s?(e.play(),null===(t=this._timeLine)||void 0===t||t.play(n)):null===(i=this._timeLine)||void 0===i||i.seekTime("",!0,n)})):this._playerMap.forEach((e=>{e.wndInfo.url&&e.status===hs.STOPSUCCESSED&&e.play()}))}resumePlayByKey(e){var t,i;const s=void 0===e?this.getPlayerByIndex():this.getPlayerByKey(e)||this.getPlayerByIndex(+e);if(!s)return;const n=s.wndInfo.url;if(this._isPlayback){const e=s.index;n?(s.play(),null===(t=this._timeLine)||void 0===t||t.play(e)):null===(i=this._timeLine)||void 0===i||i.seekTime("",!0,e)}else n&&s.status===hs.STOPSUCCESSED&&s.play()}async setVideo(e,t,i,s){var n,r,o,a,h,l,u;let{wndNo:c,startTime:d,endTime:p,keepRatio:m,customInfo:g,focusWndByKey:_,seekTime:f}=s||{},S=this.getPlayerByKey(e);if(S){if(_)return void this.selectByIndex(S.index);await S.close()}let y=this.selectByIndex(c,!1);if(y.key=e,y.keepRatio=Boolean(null==m?Oi.config.keepRatio:m),y.customInfo=g,y.setTitle(t),y.setStorageType((null==s?void 0:s.storageType)||0),y.record=(null==s?void 0:s.record)||!1,!i.length)return void y.setInfo("无录像信息！");i.forEach((e=>{e.startStamp=new Date(e.rcdStartTime).getTime(),e.endStamp=new Date(e.rcdEndTime).getTime()})),i=i.sort(((e,t)=>e.startStamp-t.startStamp)),y.setInfo("查询成功！"),null===(n=this._timeLine)||void 0===n||n.setTitle(t,c);const v=i[0].rcdStartTime,w=i[i.length-1].rcdEndTime;if(d||p){if(d||p){d=d&&new Date(d).getTime()>new Date(v).getTime()?d:v,p=p&&new Date(p).getTime()<new Date(w).getTime()?p:w;const e=new Date(d),t=new Date(p);const s=JSON.parse(JSON.stringify(i)).filter((i=>{const s=new Date(i.rcdStartTime),n=new Date(i.rcdEndTime);return s<t&&n>e}));if(!s.length)return y.setInfo("无录像信息！");e>new Date(s[0].rcdStartTime)&&(s[0].rcdStartTime=d),t<new Date(s[s.length-1].rcdEndTime)&&(s[s.length-1].rcdEndTime=p),null===(o=this._timeLine)||void 0===o||o.setVideo(s,c,{startTime:d,endTime:p,seekTime:f})}}else null===(r=this._timeLine)||void 0===r||r.setVideo(i,c,{seekTime:f}),d=v,p=w;if(f){(null===(a=this._timeLine)||void 0===a?void 0:a.checkSeekable(f))||(f=null===(h=this._timeLine)||void 0===h?void 0:h.getNextTimePoint(f)),null===(l=this._timeLine)||void 0===l||l.seekTime(f,!1)}(null==s?void 0:s.immediatePlay)&&this._timeLine&&(!this._timeLine.synchronous||this._timeLine.synchronous&&"play"!==this._timeLine.status)&&(null===(u=this._timeLine)||void 0===u||u.seekTime(void 0,!0,c)),y.startTime=d,y.endTime=p,this.controlEventBus.emit("onVideoSetted",y.wndInfo),this.controlEventBus.emit("onDefaultVideoSetted",y.wndInfo),y.status=hs.SETVIDEOED,y.playerDiv.readyActive(!0),y.authOption=(null==s?void 0:s.authOption)||[],this._timeLine&&this._timeLine.setAuthOption(y.index,null==s?void 0:s.authOption)}updateTime(e,t){if(this._timeLine){5==(this.playerIndexMap.get(t)||this.currentPlayer).status&&this._timeLine.updateTime(e,t)}}closeTimeLine(e,t=!0){this._timeLine&&this._timeLine.close(e,t)}soundControl(e,t){var i;null===(i=this.currentPlayer)||void 0===i||i.soundControl(e,t)}capture(e){var t;const i=this._isPlayback?"record":"live",s=this.playerIndexMap.get(e)||this.currentPlayer,n=s.playerDiv.video;if(!xt(i,"Capture",s.authOption)||!n)return["","",s.wndInfo];const r=document.createElement("canvas");r.width=n.videoWidth,r.height=n.videoHeight,null===(t=r.getContext("2d"))||void 0===t||t.drawImage(n,0,0,r.width,r.height);return[r.toDataURL("image/jpeg"),s.title+" "+Ni(new Date,"yyyy-MM-dd hh:mm:ss")+".jpg",s.wndInfo]}wndChange(e,t,i=!1){const s=this.playerIndexMap.get(e),n=this.playerIndexMap.get(t),r=s.playerDiv.element,o=n.playerDiv.element,a=t-e;if(1===a)o.after(r);else if(-1===a)o.before(r);else if(a>0){const i=this.playerIndexMap.get(t-1),s=this.playerIndexMap.get(e+1);if(!i||!s)return;i.playerDiv.element.after(r),s.playerDiv.element.before(o)}else{const i=this.playerIndexMap.get(t+1),s=this.playerIndexMap.get(e-1);if(!i||!s)return;i.playerDiv.element.before(r),s.playerDiv.element.after(o)}this.playerIndexMap.set(e,n),this.playerIndexMap.set(t,s),s.index=t,n.index=e,this._timeLine&&this._timeLine.wndChange(e,t),i&&(this.controlEventBus.emit("onWndChange",e,t),this.controlEventBus.emit("onDefaultWndChange",e,t),this.controlEventBus.emit("onWndExchange",e,t)),this.styleInit()}close(e){const t=this._playerMap.get(e||this._currentId);return this.controlEventBus.trigger("defaultClose",null==t?void 0:t.wndInfo),null==t?void 0:t.close()}closeAll(e){this.controlEventBus.trigger("defaultCloseAll");const t=[];let i=!1,s=!1,n=!0;return"boolean"==typeof e?n=e:void 0===e?n=!0:(i=!!e.isChangeStream,s=!!e.isSeek,n=!!e.clearRangeList),this._playerMap.forEach((e=>{t.push(e.close(i,s,n))})),Promise.all(t).then((e=>{this.selectByIndex(0)}))}closeOthers(){this.controlEventBus.trigger("defaultCloseOthers");const e=[];return this._playerMap.forEach((t=>{t.index!==this.currentPlayer.index&&e.push(t.close())})),Promise.all(e)}setWndNumsProxy(e,t){let i=e=>{},s=()=>{},n=this.getActiveOthers(e);return void 0!==t?this.setWndNums(e,t):new Promise(((e,t)=>{i=e,s=t})).then((t=>{if("boolean"==typeof t)this.setWndNums(e,t);else if(t){const i=t(),s=null==i||i;this.setWndNums(e,s,!1)}})),[n,i,s]}requestPictureInPicture(e){const t=this.getPlayerByIndex(e),i=t.video;return document.pictureInPictureEnabled?i?(i.requestPictureInPicture(),!0):t.playerDiv.info:"浏览器不支持画中画"}exitPictureInPicture(){document.pictureInPictureEnabled&&document.exitPictureInPicture()}}Ds.waterMarkInfo={fontSize:18,width:300,height:300,opacity:100,content:""};class bs{constructor(e,t){this.control=new Ds(e,t),this.eventBus=new ve,this._callbackMap=this.control.callbackMap;Object.keys(new ds).forEach((e=>{this.setCallbackProperty(e),this.control.controlEventBus.addEventListener(e,((...t)=>this.eventBus.emit(e,...t)))})),t&&t.topMenuKeys&&t.topMenuKeys.forEach((e=>{if("string"!=typeof e){const t="on"+e.value;this.setCallbackProperty(t)}})),t&&t.rightMenuKeys&&t.rightMenuKeys.forEach((e=>{if("string"!=typeof e&&"function"!=typeof e&&e.value)if(e.isSwitch){const t="onOpen"+e.value,i="onClose"+e.value;this.setCallbackProperty(t),this.setCallbackProperty(i)}else{const t="on"+e.value;this.setCallbackProperty(t)}})),this._callbackMap.set("onTest",((e,t,i)=>{window.open(`./assets/streamTestTool/index.html?urlId=${t}&ip=${i}`)}))}setCallbackProperty(e){Object.defineProperty(this,e,{get(){return this._callbackMap.get(e)||(()=>{})},set(t){this._callbackMap.set(e,t)}})}}class Ps extends bs{constructor(e,t){super(e,t),Ps.defaultRequest&&this.use(Ps.defaultRequest),Ps.authControl&&this.use(Ps.authControl),Ps.controlList.push(this.control)}static bindVue(e){Zt.Vue=e,Jt.Vue=e}static setViewCode(e=""){Ss.viewCode=e}static setOperateName(e=""){Ss.operateName=e}static setOperateTxt(e=""){Ss.operateTxt=e}static setHeaders(e={}){Ss.headers=e}static use(t){t instanceof e?Ps.defaultRequest=t:t instanceof Pt&&(Ps.authControl=t)}static setStaticConfig(e){Oi.config=Object.assign(Oi.config,e),Ps.controlList.length&&Ps.controlList.forEach((e=>{try{e.updateStaticConfig()}catch(e){}}))}static setRecordDownloadConfig(t){e.recordDownloadConfig=t}refresh(){var e;null===(e=this.control._timeLine)||void 0===e||e.refresh()}setTitle(e,t){this.control.setTitle(e,t)}setKey(e,t){this.control.setKey(e,t)}setCustomInfo(e,t){this.control.setCustomInfo(e,t)}setInfo(e,t){this.control.setInfoByIndex(e,t)}setWndInfo(e,t,i){this.setTitle(e,i),this.setInfo(t,i)}setKeepRatio(e,t){this.control.setKeepRatio(e,!0,t)}setAllKeepRatio(e){this.control.setAllKeepRatio(e)}selectWnd(e){this.control.selectByIndex(e)}pause(e){this.control.pause(e)}pauseAll(){this.control.pauseAll()}resumePlay(){this.control.resumePlay()}playAll(){this.control.playAll()}clearTimeLine(e){var t,i;null===(i=null===(t=this.control)||void 0===t?void 0:t._timeLine)||void 0===i||i.setVideo([],e)}setTimelineRange(e,t,i,s){this.control.setVideo(e,t,i,s)}play(e,t,i,s,n){return arguments.length<=1?this.control.resumePlayByKey(e):t&&i&&s?this.control.play(e?"":String(e),t,i,s,n):void 0}defaultPlay(e,t,i,s){if(this.control&&this.control.defaultPlay)return this.control.defaultPlay(e,t,i,s)}defaultPlayback(e,t,i,s,n,r=!0){if(this.control&&this.control.defaultPlayback)return this.control.defaultPlayback(e,t,i,s,n,r)}defaultDownload(e){if(this.control&&this.control.defaultDownload)return this.control.defaultDownload(e)}defaultUpdateLabels(e){this.control.defaultUpdateLabels&&this.control.defaultUpdateLabels(e)}setGridStyle(e,t){this.control.setGridStyle(e,t)}async closeAll(){return await this.control.closeAll()}async close(e){return await this.control.closeByIndex(e)}async closeOthers(){return await this.control.closeOthers()}getFirstFreeWnd(e=!0,t=!0){return this.control.getFreeWnd(!1,e,t)}getFreeWnds(){return this.control.getFreeWnds()}getWndNoById(e){return this.control.getIndexByKey(e)}getWndNoByKey(e){return this.control.getIndexByKey(e)}destory(){this.destroy()}async destroy(){await this.control.closeAll(),this.control.destory();for(let e in Ps.controlList)if(Ps.controlList[e]==this.control)return void Ps.controlList.splice(+e,1)}use(e){this.control.use(e)}fullScreen(e){this.control.fullScreen(e)}wndChange(e,t){this.control.wndChange(e,t,!0)}wndExchange(e,t){this.control.wndChange(e,t,!0)}seekTime(e,t=!1,i=!1){this.control._timeLine&&this.control._timeLine.seekTime(e,t,void 0,i)}changeStream(e,t){this.control.changeStream(e,t)}capture(e){return this.control.capture(e)}setCornerInfo(e,t){this.control.setCornerInfo(e,t)}setLabels(e,t){var i;null===(i=this.control._timeLine)||void 0===i||i.setLabels(e,t)}getWndInfo(e){const t=this.control.getPlayerByIndex(e);return t?t.wndInfo:{}}setWndNums(e,t){return this.control.setWndNumsProxy(e,t)}getActiveOthers(e){return this.control.getActiveOthers(e)}startRecord(e,t){const i=this.control.getPlayerByIndex(e);xt(this.control._isPlayback?"record":"live","Record",i.authOption)&&this.control.controlEventBus.trigger("OpenRecord",i,t)}startRecordSource(e){this.control.controlEventBus.trigger("StartRecordSource",e)}stopRecordSource(e){this.control.controlEventBus.trigger("StopRecordSource",e)}stopRecord(e){const t=this.control.getPlayerByIndex(e);xt(this.control._isPlayback?"record":"live","Record",t.authOption)&&this.control.controlEventBus.trigger("CloseRecord",t)}requestPictureInPicture(e){return this.control.requestPictureInPicture(e)}exitPictureInPicture(){this.control.exitPictureInPicture()}setRate(e,t){return this.control.getPlayerByIndex(t).setSpeed(e)}setVolume(e,t){this.control.getPlayerByIndex(t).soundControl(e)}reconnect(e){console.log("reconnect");const t=this.control.getPlayerByIndex(e);this.control.controlEventBus.trigger("defaultReconnect",t.wndInfo)}}Ps.defaultRequest=null,Ps.authControl=null,Ps.controlList=[],Ps.setWaterMarkInfo=e=>{"function"==typeof e?Object.assign(Ds.waterMarkInfo,e()):Object.assign(Ds.waterMarkInfo,e)},Ps.setVideoWaterMarkInfo=e=>{ss.setVideoWaterMarkInfo(e)},window.GoVideoWebPlayerVersion="2.4.2.202311170900";class Bs extends Ps{constructor(e,t){super(e,t),this.playerInfo={}}static get isDev(){return Bs._isDev}static set isDev(e){Bs._isDev=e,kt.isDev=e,de.isDev=e,ye.isDev=e}static get record(){return Bs._record}static set record(e){Bs._record=e,kt.record=e,de.record=e}static set disabledH265(e){ue.disabledH265=e}static set reconnect(e){ye.reconnect=e}static set log(e){a.log=e}static get log(){return a.log}static set logLevel(e){a.level=e}static get logLevel(){return a.level}get version(){return window.GoVideoWebPlayerVersion}get wndNums(){return this.control.wndNums}set wndNums(e){this.control.wndNums=e}get wndNo(){return this.control.currentPlayer.index}get wndInfo(){return this.control.currentPlayer.wndInfo}get wndInfoList(){return this.control.wndInfoList}get keepLastFrame(){return!!this.control.keepLastFrame}set keepLastFrame(e){this.control.keepLastFrame=e}get players(){return this.control.playerIndexMap}get isRecording(){return this.control.isRecording}get gridStyle(){return this.control.gridStyle}get synchronous(){var e;return null===(e=this.control._timeLine)||void 0===e?void 0:e.synchronous}get positionTime(){var e;return null===(e=this.control._timeLine)||void 0===e?void 0:e.positionTime}get timelineStatus(){var e;return null===(e=this.control._timeLine)||void 0===e?void 0:e.status}get rate(){return this.control.currentPlayer.speed}set rate(e){this.control.currentPlayer.setSpeed(e)}get volume(){return this.control.currentPlayer.volume}set volume(e){this.control.currentPlayer.soundControl(e)}get currentTime(){var e;return null===(e=this.control._timeLine)||void 0===e?void 0:e.currentTime}}Bs.DefaultRequest=e,Bs.AuthControl=Pt,Bs._isDev=!1,Bs._record=!1,Bs.isMobile=zt();export{Bs as default};
