<template>
	<div class="ui-select" :style="{ height: !showSelect && '0px' }">
		<div @click="toggleDropdown" class="select-button" :class="{ open: isOpen, disabled: disabled }" v-show="showSelect">
			<div :class="['select-label', !selectedLabel && 'placeholder']">{{ selectedLabel || placeholder }}</div>
			<div class="arrow"></div>
		</div>
		<div v-if="isOpen" class="dropdown-options">
			<div class="title">{{ selectTitle }}</div>
			<ul>
				<li v-for="(option, index) in options" :key="index" @click="toggleSelection(option)">
					{{ option[labelKey] }}
					<div class="icon" :class="[isSelected(option) ? (multiple ? 'multiple-selected' : 'selected') : '']"></div>
				</li>
			</ul>
			<div class="btn-box">
				<button class="close-btn" @click="close()">{{ $t(`选择.取消`) }}</button>
				<button class="sub-btn" @click="submit()">{{ $t(`选择.确认`) }}</button>
			</div>
		</div>
		<div v-if="isOpen" class="overlay" @click="close"></div>
	</div>
</template>
<script>
export default {
	name: 'uiSelect',
	props: {
		options: {
			type: Array,
			required: true
		},
		selectTitle: {
			type: String,
			default: ''
		},
		value: {
			type: [Number, String, Array],
			default: ''
		},
		labelKey: {
			type: String,
			default: 'label'
		},
		valueKey: {
			type: String,
			default: 'value'
		},
		multiple: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: String,
			default: '请选择'
		},
		showSelect: {
			type: Boolean,
			default: true
		},
		disabled: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			isOpen: false,
			selected: this.multiple ? [...this.value] : this.value,
			selectedLabel: ''
		}
	},
	mounted() {
		this.setSelectedLabel()
	},
	methods: {
		close() {
			this.isOpen = false
			this.selected = this.multiple ? [...this.value] : this.value
		},
		submit() {
			this.$emit('input', this.selected)
			this.setSelectedLabel()
			const selectedOptions = this.options.filter((option) => (this.multiple ? this.selected.includes(option[this.valueKey]) : option[this.valueKey] === this.selected))
			this.$emit('change', selectedOptions)
			this.isOpen = false
		},
		toggleDropdown() {
			if (this.disabled) return
			this.isOpen = !this.isOpen
		},
		toggleSelection(option) {
			if (this.multiple) {
				if (this.isSelected(option)) {
					this.selected = this.selected.filter((val) => val !== option[this.valueKey])
				} else {
					this.selected.push(option[this.valueKey])
				}
			} else {
				this.selected = option[this.valueKey]
			}
		},
		isSelected(option) {
			return this.multiple ? this.selected.includes(option[this.valueKey]) : this.selected === option[this.valueKey]
		},
		setSelectedLabel() {
			if (this.multiple) {
				const selectedOptions = this.options.filter((option) => this.selected.includes(option[this.valueKey]))
				this.selectedLabel = selectedOptions.map((option) => option[this.labelKey]).join(', ')
			} else {
				const selectedOption = this.options.find((option) => option[this.valueKey] === this.selected)
				this.selectedLabel = selectedOption ? selectedOption[this.labelKey] : ''
			}
		}
	},
	watch: {
		value() {
			this.selected = this.multiple ? [...this.value] : this.value
			this.setSelectedLabel()
		}
	}
}
</script>
<style lang="less" scoped>
.ui-select {
	position: relative;
	display: inline-block;
	width: 100%;
	height: 100px;
	.select-button {
		width: 100%;
		height: 100%;
		padding: 10px;
		background-color: #fff;
		cursor: pointer;
		border-radius: 8px;
		border: 1px solid #bfd7ff;
		padding-left: 57px;
		font-size: 40px;
		color: #3663b3;
		line-height: 40px;
		text-align: left;
		display: flex;
		align-items: center;
		justify-content: space-between;
		&::placeholder {
			color: #a2acc6;
		}
		.arrow {
			background: url('./images/icon_arrow.png') no-repeat;
			background-size: 100% 100%;
			width: 36px;
			height: 36px;
			transition: transform 0.2s ease-in-out;
			margin-right: 40px;
		}
		&.open {
			border: 4px solid #20dbe9;
			.arrow {
				transform: rotate(180deg);
			}
		}
		.placeholder {
			color: #a2acc6;
		}
		.select-label {
			max-width: calc(100% - 80px);
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
		&.disabled {
			background: #d6e2f8;
			color: #3663b3;
		}
	}
	.dropdown-options {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 0 40px;
		background-color: #fff;
		border-radius: 8px 8px 0px 0px;
		border: 1px solid #bfd7ff;
		z-index: 1000;
		.title {
			height: 117px;
			font-weight: bold;
			font-size: 40px;
			color: #2b3346;
			line-height: 117px;
			text-align: center;
			border-bottom: 1px solid rgba(164, 198, 255, 0.3);
		}
		ul {
			list-style: none;
			margin: 0;
			min-height: 358px;
			max-height: 570px;
			overflow-y: auto;
			li {
				padding: 10px;
				cursor: pointer;
				border-bottom: 1px solid rgba(164, 198, 255, 0.3);
				height: 128px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 40px;
				color: #2b3346;
				.icon {
					width: 54px;
					height: 54px;
					border: 2px solid #20dbe9;
					border-radius: 50%;
					position: relative;
					&.selected::after {
						content: '';
						width: 30px;
						height: 30px;
						background: #20dbe9;
						position: absolute;
						left: 10px;
						top: 10px;
						border-radius: 100%;
					}
					&.multiple-selected {
						background-image: url('./images/icon_selected.png');
						background-size: cover;
						background-position: center;
						background-repeat: no-repeat;
					}
				}
			}
		}
		.btn-box {
			height: 164px;
			display: flex;
			align-items: center;
			justify-content: center;
			.close-btn {
				width: 480px;
				height: 96px;
				background: #ffffff;
				border: 3px solid #2b5fda;
				font-size: 36px;
				color: #2b5fda;
			}
			.sub-btn {
				width: 480px;
				height: 96px;
				background: #2b5fda;
				border-radius: 8px 8px 8px 8px;
				font-size: 36px;
				color: #ffffff;
				margin-left: 40px;
			}
		}
	}
	.overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0);
		z-index: 999;
	}
}
</style>
