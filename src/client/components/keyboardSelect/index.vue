<template>
	<div class="keyboard-select">
		<div class="dropdown-options">
			<div class="title">{{ title }}</div>
			<div class="input-custom focus" @click="handleClick($event)" ref="name">
				<span class="text" v-for="(char, idx) in value" :key="idx" :class="{ 'cursor-after': idx === cursorPosition }">{{ char }}</span>
				<span class="cursor" :style="{ left: cursorLeft + 'px' }"></span>
				<span class="placeholder-text" v-if="!value">{{ placeholder }}</span>
			</div>
			<div class="keyboard">
				<div v-for="key in keyboardLayout[currentType]" :key="key.value" :class="['key', key.type === 'key2' && 'key2', key.value == '删除' && value == '' && 'no-value', key.value == 'aA' && 'select-Aa', (key.value == $t(`验证.符`) || key.value == $t(`验证.数`)) && $i18n.locale == 'en' && !key.type && 'key-english']" @click="inputKey(key.value)">
					<template v-if="key.value == 'Aa'">
						<img class="key-img" src="../../assets/images/verifyHome/up.png" alt="" />
					</template>
					<template v-else-if="key.value == 'aA'">
						<img class="key-img" src="../../assets/images/verifyHome/up_select.png" alt="" />
					</template>
					<template v-else-if="key.value == '删除'">
						<img v-if="value == ''" class="del-img" src="../../assets/images/verifyHome/del_white.png" alt="" />
						<img v-else class="del-img" src="../../assets/images/verifyHome/del.png" alt="" />
					</template>
					<template v-else>
						{{ key.value }}
					</template>
				</div>
			</div>
			<div class="btn-box">
				<ui-button type="white" @click="close">取消</ui-button>
				<ui-button :disabled="!value" @click="submit">确认归还</ui-button>
			</div>
		</div>
		<div class="overlay" @click="close"></div>
	</div>
</template>
<script>
export default {
	name: 'keyboard',
	props: {
		title: {
			type: String,
			default: '请输入民警卡号'
		}
	},
	data() {
		return {
			value: '',
			placeholder: '请输入',
			currentType: '0',
			cursorPosition: null,
			cursorLeft: 32,
			keyboardLayout: [
				[{ value: '1' }, { value: '2' }, { value: '3' }, { value: '4' }, { value: '5' }, { value: '6' }, { value: '7' }, { value: '8' }, { value: '9' }, { value: '0' }, { value: this.$t(`验证.符`) }, { value: '-' }, { value: '/' }, { value: ';' }, { value: '(' }, { value: ')' }, { value: '$' }, { value: '&' }, { value: '@' }, { value: '”' }, { value: 'ABC', type: 'key2' }, { value: '.' }, { value: ',' }, { value: ':' }, { value: '’' }, { value: '`' }, { value: '?' }, { value: '!' }, { value: this.$t(`验证.删除`), type: 'key2' }],
				[{ value: '[' }, { value: ']' }, { value: '{' }, { value: '}' }, { value: '#' }, { value: '%' }, { value: '^' }, { value: '*' }, { value: '+' }, { value: '=' }, { value: this.$t(`验证.数`) }, { value: '_' }, { value: '"' }, { value: '|' }, { value: '~' }, { value: '>' }, { value: '<' }, { value: '€' }, { value: '£' }, { value: '¥' }, { value: 'ABC', type: 'key2' }, { value: '.' }, { value: ',' }, { value: ':' }, { value: '’' }, { value: '`' }, { value: '?' }, { value: '!' }, { value: this.$t(`验证.删除`), type: 'key2' }],
				[{ value: 'q' }, { value: 'w' }, { value: 'e' }, { value: 'r' }, { value: 't' }, { value: 'y' }, { value: 'u' }, { value: 'i' }, { value: 'o' }, { value: 'p' }, { value: 'Aa' }, { value: 'a' }, { value: 's' }, { value: 'd' }, { value: 'f' }, { value: 'g' }, { value: 'h' }, { value: 'j' }, { value: 'k' }, { value: 'l' }, { value: '123', type: 'key2' }, { value: 'z' }, { value: 'x' }, { value: 'c' }, { value: 'v' }, { value: 'b' }, { value: 'n' }, { value: 'm' }, { value: this.$t(`验证.删除`), type: 'key2' }],
				[{ value: 'Q' }, { value: 'W' }, { value: 'E' }, { value: 'R' }, { value: 'T' }, { value: 'Y' }, { value: 'U' }, { value: 'I' }, { value: 'O' }, { value: 'P' }, { value: 'aA' }, { value: 'A' }, { value: 'S' }, { value: 'D' }, { value: 'F' }, { value: 'G' }, { value: 'H' }, { value: 'J' }, { value: 'K' }, { value: 'L' }, { value: '123', type: 'key2' }, { value: 'Z' }, { value: 'X' }, { value: 'C' }, { value: 'V' }, { value: 'B' }, { value: 'N' }, { value: 'M' }, { value: this.$t(`验证.删除`), type: 'key2' }]
			]
		}
	},
	methods: {
		handleClick(event) {
			const rect = this.$refs.name.getBoundingClientRect()
			const x = event.clientX - rect.left - 32 // 获取点击位置相对于输入框的偏移量
			const chars = this.$refs.name.querySelectorAll('.text') // 获取所有字符元素
			let accumulatedWidth = 0
			let position = 0 // 遍历每个字符，计算其实际宽度并确定点击位置
			if (x < 0) {
				this.cursorPosition = 0
				this.cursorLeft = 32
				return
			}
			for (let i = 0; i < chars.length; i++) {
				const charRect = chars[i].getBoundingClientRect()
				accumulatedWidth += charRect.width
				if (x < accumulatedWidth) {
					position = i + 1
					break
				}
				if (x > accumulatedWidth) {
					position = chars.length
				}
			}

			this.cursorPosition = position
			this.cursorLeft = accumulatedWidth + 32
		},

		inputKey(char) {
			if (char === this.$t(`验证.数`) || char === '123') {
				this.currentType = '0'
				return
			}
			if (char === this.$t(`验证.符`)) {
				this.currentType = '1'
				return
			}
			if (char === 'ABC' || char === 'aA') {
				this.currentType = '2'
				return
			}
			if (char === 'Aa') {
				this.currentType = '3'
				return
			}
			if (char === this.$t(`验证.删除`)) {
				if (this.cursorPosition == 0) return
				if (this.value == '') return
				this.value = this.value.slice(0, this.cursorPosition - 1) + this.value.slice(this.cursorPosition)
				this.cursorPosition = this.cursorPosition - 1
			} else {
				this.value = this.value.slice(0, this.cursorPosition) + char + this.value.slice(this.cursorPosition)
				this.cursorPosition = this.cursorPosition + 1
			}
			this.$nextTick(() => {
				this.updateCursorPosition()
			})
		},
		updateCursorPosition() {
			const activeTextRef = this.$refs.name.querySelectorAll('.text')
			let accumulatedWidth = 0
			for (let i = 0; i < this.cursorPosition; i++) {
				if (activeTextRef[i]) {
					const charRect = activeTextRef[i] && activeTextRef[i].getBoundingClientRect()
					accumulatedWidth += charRect.width
				}
			}
			this.cursorLeft = accumulatedWidth + 32
		},
		close() {
			this.$emit('close')
		},
		submit() {
			this.$emit('changeValue', this.value)
		}
	}
}
</script>
<style lang="less" scoped>
.keyboard-select {
	.title {
		height: 130px;
		line-height: 130px;
		font-weight: bold;
		font-size: 40px;
		color: #2b3346;
		text-align: center;
	}
	.input-custom {
		width: calc(100% - 24px);
		height: 100px;
		border: 2px solid #337fff;
		font-size: 40px;
		padding-left: 32px;
		border-radius: 8px;
		line-height: 100px;
		position: relative;
		display: inline-block;
		user-select: none; /* 禁止选择文本 */
		white-space: nowrap;
		// margin-bottom: 38px;
		margin: 0 12px 38px 12px;
		overflow: hidden;
		.text {
			display: inline-block;
			font-size: 40px;
			text-align: center;
		}
		.placeholder-text {
			color: #a2acc6;
		}
		&.focus {
			border-radius: 10px;
			border: 2px solid #20dbe9;
		}
		.cursor {
			position: absolute;
			height: 60px;
			top: 20px;
			width: 2px;
			background-color: rgb(96, 129, 238);
			pointer-events: none;
			animation: blink 1s infinite;
		}
		.icon {
			position: absolute;
			top: 38px;
			left: 22px;
			width: 48px;
			height: 48px;
		}
	}
	.keyboard {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 60px;
		justify-content: center;
		text-align: center;
		.key {
			// box-sizing: border-box;
			width: 96px;
			line-height: 120px;
			cursor: pointer;
			font-size: 60px;
			background: #ffffff;
			border-radius: 12px;
			border: 2px solid #2b5fda;
			color: #2b5fda;
			margin: 10px 5px;
			-webkit-tap-highlight-color: transparent; /* 禁用触摸高亮 */
			touch-action: manipulation; /* 优化触摸行为 */
			&:focus {
				outline: none; /* 移除 outline */
			}
			&.key2 {
				width: 148px;
			}
			&.key-english {
				font-size: 40px;
			}
			&.select-Aa {
				background: #2b5fda;
			}
			&.no-value {
				background: #669eff;
			}
		}
		.key-img {
			width: 48px;
			height: 43px;
		}
		.del-img {
			width: 66px;
			height: 42px;
		}
	}
	.btn-box {
		width: 100%;
		display: flex;
		padding: 0 40px 33px 40px;
		.ui-btn {
			flex: 1;
		}
		.ui-btn + .ui-btn {
			margin-left: 40px;
		}
	}
	.dropdown-options {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		// padding: 0 40px;
		background-color: #fff;
		border-radius: 8px 8px 0px 0px;
		border: 1px solid #bfd7ff;
		z-index: 1000;
	}
	.overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0);
		z-index: 999;
	}
}
@keyframes blink {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0;
	}
}
</style>
