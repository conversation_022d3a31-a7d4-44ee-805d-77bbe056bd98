<template>
	<span :class="classes" @click="toggle">
		<span class="ui-switch-content" v-show="value">
			<slot name="open"></slot>
		</span>
		<span class="ui-switch-circle"></span>
		<span class="ui-switch-content" v-show="!value">
			<slot name="close"></slot>
		</span>
		<input v-model="currentValue" v-show="false" />
	</span>
</template>
<script>
export default {
	name: 'uiSwitch',
	data() {
		return {
			currentValue: ''
		}
	},
	model: {
		prop: 'value',
		event: 'change'
	},
	props: {
		value: {
			type: Boolean,
			default: false
		}
	},
	watch: {
		value: {
			handler() {
				this.currentValue = this.value ? 'hasValue' : ''
			},
			immediate: true
		}
	},
	computed: {
		classes() {
			const classes = []
			classes.push('ui-switch')
			classes.push(this.value ? 'ui-switch-open' : 'ui-switch-close')
			return classes
		}
	},
	methods: {
		toggle() {
			this.$emit('change', !this.value)
		}
	}
}
</script>

<style lang="less" scoped>
.ui-switch {
	display: inline-block;
	vertical-align: middle;
	border-radius: 35px;
	min-width: 180px;
	height: 70px;
	user-select: none;
	line-height: calc(70px - 2px);
	position: relative;
	cursor: pointer;
	background: #cbd4e5;
	transition: all 0.2s ease;
	color: #5f709a;
	font-size: 40px;
	font-weight: normal;
	padding: 0px 20px;

	&.ui-switch-open {
		background: #20dbe9;
		padding-right: 70px;
		color: #fff;
		.ui-switch-circle {
			left: calc(100% - 66px);
		}
	}
	&.ui-switch-close {
		background: #cbd4e5;
		padding-left: 70px;
		.ui-switch-circle {
			left: 4px;
		}
	}
	.ui-switch-circle {
		background: #fff;
		position: absolute;
		display: inline-block;
		border-radius: 50%;
		width: 62px;
		height: 62px;
		transition: left 0.2s ease;
		top: 4px;
	}
	.ui-switch-content {
		text-align: center;
		display: inline-block;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}
}
</style>
