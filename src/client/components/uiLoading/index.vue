<template>
	<div class="ui-loading" v-if="value">
		<div class="ui-loading-container">
			<div class="loading-img"></div>
			<div class="loading-text">
				<slot></slot>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'uiLoading',
	props: {
		value: {
			type: Boolean,
			default: false
		}
	}
}
</script>

<style lang="less" scoped>
.ui-loading {
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background: rgba(8, 5, 44, 0.6);
	z-index: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	.ui-loading-container {
		width: 398px;
		height: 340px;
		background: #ebf5ff;
		border-radius: 16px;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 47px;
		.loading-img {
			width: 172px;
			height: 172px;
			background: url('./images/new-load.png') no-repeat;
			background-size: 100% 100%;
		}
		.loading-text {
			font-size: 32px;
			color: #333333;
		}
	}
}
</style>
