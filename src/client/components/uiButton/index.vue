<template>
	<button :class="classes" @click="btnClick" type="button">
		<slot></slot>
	</button>
</template>

<script>
export default {
	name: 'uiButton',
	data() {
		return {
			btnLoad: false
		}
	},
	props: {
		type: {
			type: String,
			default: 'default'
			//default:蓝底白字;white:白底蓝字
		},
		wait: {
			type: Number,
			required: false
		},
		disabled: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		classes() {
			return ['ui-btn', `ui-btn-${this.type}`, { 'ui-btn-disabled': this.disabled }]
		},
		waitTime() {
			return this.wait !== undefined ? this.wait : 0
		}
	},
	methods: {
		btnClick(e) {
			if (this.disabled || this.btnLoad) return false
			this.$emit('click', e)
			if (this.waitTime > 0) {
				this.btnLoad = true
				const timer = setTimeout(() => {
					this.btnLoad = false
					clearTimeout(timer)
				}, this.waitTime)
			}
		}
	}
}
</script>

<style lang="less" scoped>
.ui-btn {
	user-select: none;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	white-space: nowrap;
	transition: all 0.2s;
	font-size: 36px;
	font-weight: normal;
	height: 96px;
	&.ui-btn-default {
		background: #2b5fda;
		color: #fff;
		border-radius: 8px;
		&.ui-btn-disabled {
			color: rgba(228, 241, 249, 0.6);
			background: #9aafe0;
		}
	}
	&.ui-btn-white {
		background: #ffffff;
		border: 3px solid #2b5fda;
		color: #2b5fda;
		&.ui-btn-disabled {
			border: 3px solid #9aafe0;
			color: #9aafe0;
		}
	}
	&:not(.ui-btn-disabled) {
		&.ui-btn-default {
			&:active {
				background: #356ced;
			}
		}
		&.ui-btn-white {
			&:active {
				border: 3px solid #356ced;
				color: #356ced;
			}
		}
	}
}
</style>
