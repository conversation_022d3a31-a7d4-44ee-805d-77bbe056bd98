<template>
	<div class="head">
		<div class="top-left" style="z-index: 1">
			<template v-if="config.communicationMode == communicationModeDic.ONLINE">
				<div class="positioning"></div>
				<div class="center-name">{{ onlineConfig.centerName }}</div>
			</template>
		</div>
		<div class="top-right">
			<div class="now-time" @dblclick="dblclick()">
				<div class="minute">{{ minuteDate }}</div>
				<div class="date">{{ dayDate }}</div>
			</div>
			<div class="line"></div>
			<template v-if="userInfo?.name">
				{{ userInfo.name }}
			</template>
			<div v-else class="set-up" @click="toPage()"></div>
		</div>
		<ui-modal v-model="showModal">
			<div class="modal-content">版本：{{ allConfig.version }}</div>
			<div class="ui-modal-footer">
				<div class="btn cancel-btn" @click="showModal = false">取消</div>
				<div class="btn" @click="closeApp">退出</div>
				<div v-if="config.communicationMode == communicationModeDic.ONLINE" class="btn" @click="detection()">检测</div>
			</div>
		</ui-modal>
		<ui-modal v-model="upgradeModal">
			<div class="modal-content">检测到新版本，是否立即升级？</div>
			<div class="ui-modal-footer">
				<div class="btn cancel-btn" @click="upgradeModal = false">稍后</div>
				<div class="btn" @click="upgradeApp()">更新</div>
			</div>
		</ui-modal>
		<ui-loading v-model="showLoading">正在下载安装中请稍后！</ui-loading>
	</div>
</template>

<script>
import { communicationModeDic } from '@/common/libs/options'
import { mapGetters } from 'vuex'
import { formatDate, versionCompare } from '@/common/libs/util'
export default {
	name: 'pageHeader',
	data() {
		return {
			upgradeModal: false,
			minuteDate: formatDate(new Date(), 'HH:mm'),
			dayDate: formatDate(new Date(), 'YYYY-MM-DD'),
			timer: null,
			centerName: '',
			communicationModeDic,
			showModal: false,
			upgradeData: {},
			showLoading: false,
			defaultTime: null
		}
	},
	computed: {
		...mapGetters({
			allConfig: 'app/allConfig',
			config: 'app/config',
			propertyData: 'app/propertyData',
			token: 'user/token',
			onlineConfig: 'app/onlineConfig',
			userInfo: 'user/userInfo'
		})
	},
	methods: {
		dblclick() {
			if (this.$route.name == 'home') {
				this.showModal = true
			}
		},
		upgradeApp() {
			this.$http.appApi.upgradeApp({ url: this.upgradeData.fileDir }).then((res) => {
				this.upgradeModal = false
				this.showLoading = true
				setTimeout(() => {
					this.showLoading = false
				}, 60 * 1000)
			})
		},
		detection() {
			this.$http.webApi.getLatestVersion().then((res) => {
				this.upgradeData = res.data || {}
				if (this.upgradeData.versionNumber && this.upgradeData.fileDir) {
					const compareRet = versionCompare(this.allConfig.version, this.upgradeData.versionNumber)
					if (compareRet && compareRet > 0) {
						this.showModal = false
						this.upgradeModal = true
					} else {
						this.$baseTip.info('当前版本为最新版本！')
					}
				} else {
					this.$baseTip.info('获取版本号失败！')
				}
			})
		},
		closeApp() {
			this.$http.appApi.closeApp()
		},
		toPage() {
			if (this.$route.name != 'home') {
				return false
			}
			this.$store.commit('app/setNextRouter', '/settings')
			if (this.config.loginMethod.includes('2')) {
				this.$router.push('verifyHome')
			} else {
				this.$router.push('verifyAccount')
			}
		},
		getCurrentDate() {
			this.minuteDate = formatDate(new Date(this.defaultTime), 'HH:mm')
			this.dayDate = formatDate(new Date(this.defaultTime), 'YYYY-MM-DD')
		}
	},
	async mounted() {
		if (this.config.communicationMode == communicationModeDic.ONLINE) {
			const res = await this.$http.webApi.getLocalDateTime()
			this.defaultTime = new Date(res.data).getTime()
		} else {
			this.defaultTime = new Date().getTime()
		}
		this.timer = setInterval(() => {
			this.defaultTime += 1000
			this.getCurrentDate()
		}, 1 * 1000)
	},
	beforeDestroy() {
		this.timer && clearInterval(this.timer)
	}
}
</script>

<style lang="less" scoped>
.head {
	height: 100px;
	width: 100%;
	position: relative;
	display: flex;
	justify-content: space-between;
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(180deg, #337fff 0%, rgba(153, 199, 252, 0.37) 100%);
		opacity: 0.3;
	}
	.top-left {
		font-size: 32px;
		color: #465c88;
		// line-height: 100px;
		font-weight: bold;
		padding-left: 22px;
		display: flex;
		align-items: center;

		.positioning {
			width: 42px;
			height: 42px;
			background: url('./images/icon_positioning.png') no-repeat;
			background-size: 100% 100%;
			margin-right: 7px;
		}
		.center-name {
			max-width: 650px;
			white-space: nowrap; /* 禁止文本换行 */
			overflow: hidden; /* 隐藏超出容器的部分 */
			text-overflow: ellipsis; /* 使用省略号表示被裁剪的文本 */
		}
	}
	.top-right {
		display: flex;
		align-items: center;
		padding-right: 21px;
		font-size: 28px;
		.now-time {
			color: #5779b3;
			text-align: center;
			z-index: 1;
			.minute {
				font-size: 36px;
				line-height: 43px;
			}
			.date {
				font-size: 20px;
				line-height: 27px;
			}
		}
		.line {
			width: 1px;
			height: 60px;
			background: #77adfd;
			margin: 0 21px;
		}
		.set-up {
			width: 42px;
			height: 44px;
			background: url('./images/icon_set.png') no-repeat;
			background-size: 100% 100%;
			z-index: 1;
		}
	}
}
.modal-content {
	height: 200px;
	margin-bottom: 44px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	font-size: 40px;
}
</style>
