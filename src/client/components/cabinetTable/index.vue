<template>
	<div class="cabinet-table" :style="boxStyle">
		<div class="trapezoid"></div>
		<div class="gradient-border">
			<table v-if="tableData.length > 0">
				<tr v-for="(row, rowIdx) in tableData" :key="'row' + rowIdx">
					<td v-for="(col, colIdx) in row" :key="'col' + colIdx" v-show="Object.keys(col).length > 0" :class="{ 'is-been-used': userId != col.userId && col.userName, disabled: col.isForbidden }" :rowspan="col.merge" :colspan="col.colSpan" :style="{ height: heightType[col.heightType], width: widthType[col.widthType] }" @click="selectData(col)">
						<div v-if="col.num == 'A000' || col.num == '000'" class="home-bg">
							<div class="home-container">
								<div class="camera" v-show="cameraShow"></div>
								<img class="screen" :src="$store.state.app.config.userType === '1' ? policeScreenImg : screenImg" alt="" :style="getStyle()" />
								<img class="sensing" v-show="['3', '4'].includes(tableObj.cabinetId)" src="./images/sensing_area.png" alt="" />
							</div>
							<div class="scan-box">
								<img v-show="['3', '4'].includes(tableObj.cabinetId)" src="./images/scan.png" alt="" />
							</div>
						</div>
						<div v-else class="num">{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, col.num, allConfig.lockInfo) }}</div>
						<div class="text-box" v-if="operateType != 'edit' && col.userName">
							<img v-if="showDetail(col)" :src="col.faceImg || (col.userType === '3' ? personImg : policeImg)" alt="" />
							<div v-if="showDetail(col)">{{ col.userName }}</div>
						</div>
						<div class="status-box" v-if="col.userName && showDetail(col) && col.status == 'lsjc'">借出</div>
						<div class="selected-icon" v-if="isSelected(col)"></div>
					</td>
				</tr>
			</table>
		</div>
	</div>
</template>
<script>
import { mapGetters } from 'vuex'
import { TableModel, TableUtils, WidthType, HeightType, BoxStyle } from './tableUtils'
import { convertCabinetCodesToIndices } from '@/common/libs/util'
export default {
	name: 'cabinetTable',
	props: {
		tableObj: {
			type: Object,
			default: () => {}
		},
		tableUserData: {
			type: Array,
			default: () => []
		},
		size: {
			type: String,
			default: 'min'
		},
		operateType: {
			type: String,
			default: 'detail'
		},
		userId: {
			default: ''
		},
		selectList: {
			type: Array,
			default: () => []
		},
		selectString: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			convertCabinetCodesToIndices,
			formData: {},
			tableMatrix: [],
			tableData: [],
			policeImg: require('./images/police.png'),
			personImg: require('./images/person.png'),
			policeScreenImg: require('./images/police_screen.png'),
			screenImg: require('./images/home_screen.png'),
			heightType: HeightType[this.tableObj.cabinetId][this.size],
			widthType: WidthType[this.tableObj.cabinetId][this.size],
			boxStyle: BoxStyle[this.size]
		}
	},
	computed: {
		...mapGetters({
			userInfo: 'user/userInfo',
			config: 'app/config',
			useType: 'app/useType',
			allConfig: 'app/allConfig'
		}),
		cameraShow() {
			let flag = true
			if (this.config.mainCabinetModel == '1' && this.config.mainScreenType == '2') {
				flag = false
			}
			return flag
		},
		isSelected() {
			return (col) => {
				if (this.config.userType == '1') {
					return this.selectString.includes(col.num)
				} else {
					return this.selectList.some((item) => col.num === item.num)
				}
			}
		}
	},
	watch: {
		tableUserData: {
			handler(val) {
				if (val) {
					this.updateTableData()
				}
			},
			immediate: true
		}
	},
	methods: {
		getStyle() {
			let width = '215px'
			let height = '367px'
			if (this.config.mainCabinetModel == '1' && this.config.mainScreenType == '2') {
				width = '390px'
				height = '629px'
			}
			return { width, height }
		},
		selectData(cell) {
			if (cell.isForbidden === '1' || cell.num === 'A000') {
				return false
			}
			if (this.operateType === 'edit' || this.operateType === 'multiple') {
				if (this.operateType === 'multiple' && this.userId && this.userId != cell.userId && cell.userName) {
					return false
				}
				if (this.operateType === 'edit' && this.userId != cell.userId && cell.userName) {
					return false
				}
				if (this.useType) {
					if (this.useType == 'qcwp' && !cell.userId) {
						return this.$baseTip.info('请选择绑定柜格!')
					}
					if (this.useType == 'jcgh' && cell.status != 'lsjc') {
						return this.$baseTip.info('未查询到存在临时借出记录!')
					}
					if (this.useType == 'lsjc' && !cell.userId) {
						return this.$baseTip.info('请选择绑定柜格!')
					}
					if (this.userInfo.userType == 2 && cell.associatedPoliceId && cell.associatedPoliceId != this.userInfo.id) {
						return this.$baseTip.info('无权限，请重新选择!')
					}

					const flag = this.selectList.some((item) => cell.num === item.num)
					let arr = [...this.selectList] // 创建副本
					if (flag) {
						arr = this.selectList.filter((item) => item.num !== cell.num)
					} else {
						arr.push(cell)
					}
					this.$emit('selectedList', { label: this.tableObj.name, list: arr })
				} else {
					this.$set(cell, 'selected', !cell.selected)
					const selectedList = this.tableData.flatMap((row) => (row ? row.filter((item) => item && item.selected).map((item) => item.num) : [])).join(',')
					this.$emit('selectedList', { label: this.tableObj.name, list: selectedList })
				}
			} else {
				this.$emit('selected', cell)
			}
		},
		showDetail(col) {
			if (!this.userInfo || this.userInfo.userType == 1 || col.userId == this.userInfo.id || col.associatedPoliceId == this.userInfo.id) {
				return true
			}
			return false
		},
		updateTableData() {
			if (this.tableUserData.length == 0) return
			const newData = this.tableData.map((row) =>
				row.map((item) => {
					const match = this.tableUserData.find((a) => a.sidesName === item.num)
					if (match) {
						if (this.operateType === 'edit' && match.userId == this.userId) {
							match.selected = true
						}
						return { ...item, ...match }
					}
					return item
				})
			)
			this.tableData = newData
			if (this.operateType === 'edit') {
				const selectedList = this.tableData.flatMap((row) => (row ? row.filter((item) => item && item.selected).map((item) => item.num) : [])).join(',')
				this.$emit('selectedList', { label: this.tableObj.name, list: selectedList })
			}
		},
		getTableData(obj) {
			if (this.config.mainScreenType == '3') {
				return TableUtils.generateTurnList(TableModel[obj.cabinetId][obj.gf], obj.name)
			}
			return TableUtils.generateList(TableModel[obj.cabinetId][obj.gf], obj.name)
		}
	},
	created() {
		this.tableData = this.getTableData(this.tableObj)
		this.updateTableData()
	}
}
</script>
<style lang="less" scoped>
.trapezoid {
	height: 0;
	border-left: 30px solid transparent;
	border-right: 30px solid transparent;
	border-bottom: 30px solid #6899d5;
}
.gradient-border {
	border-width: 20px;
	border-style: solid;
	border-image: linear-gradient(180deg, #6899d5 0%, #4578b3 100%);
	border-image-slice: 1;
	box-shadow: 0px 30px 30px #6899d5;
	table {
		width: calc(100% + 40px);
		margin: 0 -20px;
		height: 100%;
		border: none;
		border-collapse: separate;
		border-spacing: 20px 0;
		tr {
			width: 100%;
		}
		td {
			padding: 0;
			border-top: 4px solid #3264a5;
			border-left: 4px solid #3264a5;
			border-right: 4px solid #3264a5;
			position: relative;
			.num {
				position: absolute;
				top: 4px;
				left: 7px;
				font-weight: bold;
				font-size: 22px;
				color: #00d18b;
				line-height: 33px;
			}
			.home-bg {
				height: 100%;
				width: 100%;
				display: flex;
				flex-direction: column;
				.home-container {
					flex: 1;
					background: linear-gradient(180deg, #eef4ff 0%, #cce4ff 100%);
					display: flex;
					flex-direction: column;
					align-items: center;
					padding-top: 100px;
					.camera {
						width: 80px;
						height: 24px;
						background: #b2bccb;
						border-radius: 50px;
						position: relative;
						&::before,
						&::after {
							content: '';
							position: absolute;
							border-radius: 50%;
							width: 12px;
							height: 12px;
							background-color: #c5ccd8;
						}
						&::before {
							top: 6px;
							left: 16px;
						}
						&::after {
							top: 6px;
							right: 16px;
						}
					}
					.screen {
						width: 215px;
						height: 367px;
						margin-top: 23px;
						margin-bottom: 53px;
					}
					.sensing {
						width: 270px;
						height: 90px;
						margin-bottom: 53px;
					}
				}
				.scan-box {
					height: 40px;
					background: linear-gradient(197deg, #eef4ff 0%, #cce4ff 100%);
					box-shadow: inset 0px 2px 2px 1px rgba(255, 255, 255, 0.16);
					border-top: 4px solid #3264a5;
					padding-left: 22px;
					padding-top: 10px;
					img {
						width: 48px;
						height: 15px;
					}
				}
			}
			&.disabled {
				.num {
					color: #f20b5b;
				}
			}
			//占用
			&.is-been-used {
				.num {
					color: #445bf3;
				}
				.text-box {
					display: flex;
					align-items: center;
					padding-left: 50px;
					font-weight: 400;
					font-size: 20px;
					color: #2b4d71;
					img {
						width: 44px;
						height: 44px;
						border-radius: 50%;
						margin-right: 8px;
					}
				}
				.status-box {
					position: absolute;
					top: 5px;
					right: 5px;
					width: 48px;
					height: 24px;
					background: #fcead2;
					border-radius: 2px 2px 2px 2px;
					font-size: 14px;
					color: #f79834;
					line-height: 24px;
					padding-left: 7px;
					&::after {
						content: '';
						position: absolute;
						width: 6px;
						height: 6px;
						top: 9px;
						background-color: #f79834;
						border-radius: 50%;
						margin-left: 2px;
					}
				}
			}
			.status-box {
				position: absolute;
				top: 5px;
				right: 5px;
				width: 48px;
				height: 24px;
				background: #fcead2;
				border-radius: 2px 2px 2px 2px;
				font-size: 14px;
				color: #f79834;
				line-height: 24px;
				padding-left: 7px;
				&::after {
					content: '';
					position: absolute;
					width: 6px;
					height: 6px;
					top: 9px;
					background-color: #f79834;
					border-radius: 50%;
					margin-left: 2px;
				}
			}
			.text-box {
				display: flex;
				align-items: center;
				padding-left: 50px;
				font-weight: 400;
				font-size: 20px;
				color: #2b4d71;
				img {
					width: 44px;
					height: 44px;
					border-radius: 50%;
					margin-right: 8px;
				}
			}
			.selected-icon {
				width: 100%;
				height: 100%;
				border: 4px solid #20dbe9;
				position: absolute;
				top: 0;
				background-color: #f0f8ff;
				box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
				z-index: -1;
				&::before {
					content: '✔';
					position: absolute;
					font-size: 30px;
					bottom: 0;
					right: 0;
					width: 54px;
					color: #fff;
					height: 48px;
					background-color: #20dbe9;
					clip-path: polygon(0 100%, 100% 100%, 100% 0);
					display: flex;
					justify-content: flex-end;
					align-items: flex-end;
				}
			}
			&.middle-cell {
				&::before {
					content: '';
					display: inline-block;
					width: 10px;
				}
			}
		}
	}
}
</style>
