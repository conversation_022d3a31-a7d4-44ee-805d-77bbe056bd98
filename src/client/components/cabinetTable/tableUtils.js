const TableModel = {
	4: {
		19: { id: 4, maxRow: 9, maxCol: 3, length: 20, maxRowsPerCol: [9, 2, 9], merge: { 2: [11], 7: [10] }, screen: 10, heightType: { 2: [2, 3, 13, 14], 3: [1, 8, 9, 12, 19, 20] }, widthType: { 2: [10] } },
		26: { id: 4, maxRow: 9, maxCol: 3, length: 26, maxRowsPerCol: [9, 8, 9], merge: { 2: [17] }, heightType: { 2: [2, 3, 11, 12], 3: [1, 8, 9, 10, 18, 25, 26] }, widthType: { 2: [10, 11, 12, 13, 14, 15, 16, 17] } }
	},
	3: {
		18: { id: 3, maxRow: 18, maxCol: 2, maxRowsPerCol: [1, 18], length: 19, merge: { 18: [1] }, screen: 1 },
		36: { id: 3, maxRow: 18, maxCol: 2, maxRowsPerCol: [18, 18], length: 36 }
	},
	2: {
		21: { id: 2, maxRow: 9, maxCol: 3, length: 22, maxRowsPerCol: [9, 4, 9], merge: { 6: [10] }, screen: 10, heightType: { 2: [7, 8, 9, 11, 12, 13, 20, 21, 22] }, widthType: { 2: [10, 11, 12, 13] } },
		27: { id: 2, maxRow: 9, maxCol: 3, length: 27, maxRowsPerCol: [9, 9, 9], heightType: { 2: [7, 8, 9, 16, 17, 18, 25, 26, 27] }, widthType: { 2: [10, 11, 12, 13, 14, 15, 16, 17, 18] } }
	},
	1: {
		2: { id: 1, maxRow: 2, maxCol: 2, length: 3, maxRowsPerCol: [2, 1], horizontalMerge: { 2: [1] }, screen: 1, heightType: { 4: [3, 2], 8: [1] } },
		16: { id: 1, maxRow: 8, maxCol: 2, length: 16, maxRowsPerCol: [8, 8], heightType: { 2: [1, 2, 9, 10], 3: [8, 16] } }
	}
}
const BoxStyle = {
	min: {
		height: '1260px',
		width: '850px'
	},
	big: {
		height: '1387px',
		width: '870px'
	}
}
// 各个比例高度计算规则,例如 id为2的柜子，他的总高度是1731，小一点的是175，高一点的是221，那么就用175/1731得到占比，
// 然后再除以boxheigth-70得到的就是实际的高度.得到他的值175/1731*1317=133px，然后对应的在model与具体的heigthType去关联上  1317||1190
const HeightType = {
	4: {
		min: {
			1: '70px',
			2: '140px',
			3: '210px'
		},
		big: {
			1: '77px',
			2: '154px',
			3: '231px'
		}
	},
	3: {
		min: {
			1: '66px'
		},
		big: {
			1: '73px'
		}
	},
	2: {
		min: {
			1: '120px',
			2: '151px'
		},
		big: {
			1: '133px',
			2: '168px'
		}
	},
	1: {
		min: {
			1: '99px',
			2: '198px',
			3: '297px',
			4: '396px',
			8: '793px'
		},
		big: {
			1: '109px',
			2: '218px',
			3: '328px',
			4: '439px',
			8: '878px'
		}
	}
}
// 宽度按照比例算，需要减去左右加起来40，然后根据分几个就对于的减去（n-1）*20的间隙距离。
const WidthType = {
	4: {
		min: {
			1: '221px',
			2: '327px'
		},
		big: {
			1: '227px',
			2: '336px'
		}
	},
	3: {
		min: {
			1: '395px'
		},
		big: {
			1: '405px'
		}
	},
	2: {
		min: {
			1: '223px',
			2: '324px'
		},
		big: {
			1: '228px',
			2: '333px'
		}
	},
	1: {
		min: {
			1: '395px'
		},
		big: {
			1: '405px'
		}
	}
}
function getTypeValue(obj, num, typeKey) {
	for (const type in obj[typeKey]) {
		if (obj[typeKey][type].includes(num)) {
			return Number(type)
		}
	}
	return 1
}
// 辅助函数
function generateTableData(obj, name, reversed = false) {
	const arr = []
	let num = 1
	const maxRowsPerCol = reversed ? [...obj.maxRowsPerCol].reverse() : obj.maxRowsPerCol
	const maxCol = reversed ? obj.maxCol - 1 : 0
	const colIncrement = reversed ? -1 : 1
	for (let i = maxCol; reversed ? i >= 0 : i < obj.maxCol; i += colIncrement) {
		let addMerge = 0
		for (let j = 0; j < obj.maxRow; j++) {
			const merge = getTypeValue(obj, num, 'merge') // 处理纵向合并
			const heightType = getTypeValue(obj, num, 'heightType') // 处理高度类型
			const widthType = getTypeValue(obj, num, 'widthType') // 处理宽度类型
			const colSpan = getTypeValue(obj, num, 'horizontalMerge') // 处理横向合并
			const filteredItems = arr.filter((item) => item.rowPos == j)
			const lastItem = filteredItems[filteredItems.length - 1]
			const addColSpan = lastItem ? lastItem.colSpan - 1 : 0
			if (maxRowsPerCol[i] > j) {
				const item = {
					num: obj.screen == num ? `${name}000` : name + (num > obj.screen ? (num - 1).toString().padStart(2, '0') : num.toString().padStart(2, '0')),
					rowPos: j + addMerge + addColSpan,
					colPos: i,
					merge,
					colSpan,
					widthType,
					heightType
				}
				arr.push(item)
				num++
			}
			if (merge > 1) {
				addMerge = addMerge + merge - 1
			}
		}
	}
	const tableMatrix = Array.from({ length: obj.maxRow }, () => Array(obj.maxCol).fill({}))
	arr.forEach((item) => {
		const { rowPos, colPos } = item
		tableMatrix[rowPos][colPos] = item
	})
	return tableMatrix
}
// 方法定义
const TableUtils = {
	generateList(obj, name) {
		return generateTableData(obj, name)
	},
	generateTurnList(obj, name) {
		return generateTableData(obj, name, true)
	}
}
// 导出常量和方法
export { TableModel, TableUtils, HeightType, WidthType, BoxStyle }
