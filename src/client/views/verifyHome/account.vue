<template>
	<div class="verify-account">
		<div class="verify-account-top">
			<div class="title">{{ this.config.placeName }}</div>
			<div class="name">{{ this.allConfig?.operationInfo?.terminalName }}</div>
		</div>
		<div class="input-custom" @click="handleClick($event, 'name')" :class="{ focus: activeType === 'name' }" ref="name">
			<img class="icon" src="../../assets/images/verifyHome/icon_name.png" />
			<span class="text" v-for="(char, idx) in user.name" :key="idx" :class="{ 'cursor-after': idx === cursorPosition }">{{ char }}</span>
			<span class="cursor" v-if="activeType === 'name'" :style="{ left: cursorLeft + 'px' }"></span>
			<span class="placeholder-text" v-if="activeType !== 'name' && user.name === ''">{{ $t(`验证.请输入账号`) }}</span>
		</div>
		<div class="input-custom" @click="handleClick($event, 'password')" :class="{ focus: activeType === 'password' }" ref="password">
			<img class="icon" src="../../assets/images/verifyHome/icon_password.png" />
			<span class="text" v-for="(char, idx) in user.password" :key="idx" :class="{ 'cursor-after': idx === cursorPosition }">*</span>
			<span class="cursor" v-if="activeType === 'password'" :style="{ left: cursorLeft + 'px' }"></span>
			<span class="placeholder-text" v-if="activeType !== 'password' && user.password === ''">{{ $t(`验证.请输入密码`) }}</span>
		</div>
		<div class="keyboard">
			<div v-for="key in keyboardLayout[currentType]" :key="key.value" :class="['key', key.type === 'key2' && 'key2', key.value == '删除' && user[activeType] == '' && 'no-value', key.value == 'aA' && 'select-Aa', (key.value == $t(`验证.符`) || key.value == $t(`验证.数`)) && $i18n.locale == 'en' && !key.type && 'key-english']" @click="inputKey(key.value)">
				<template v-if="key.value == 'Aa'">
					<img class="key-img" src="../../assets/images/verifyHome/up.png" alt="" />
				</template>
				<template v-else-if="key.value == 'aA'">
					<img class="key-img" src="../../assets/images/verifyHome/up_select.png" alt="" />
				</template>
				<template v-else-if="key.value == '删除'">
					<img v-if="user[activeType] == ''" class="del-img" src="../../assets/images/verifyHome/del_white.png" alt="" />
					<img v-else class="del-img" src="../../assets/images/verifyHome/del.png" alt="" />
				</template>
				<template v-else>
					{{ key.value }}
				</template>
			</div>
		</div>
		<div class="sub-btn" @click="submit()">{{ $t(`验证.立即认证`) }}</div>
		<div class="tips" @click="changeType()">{{ this.config.loginMethod.includes(',') ? `${$t('验证.认证出现问题')}？${$t('验证.试试其他认证方式')}` : '' }}</div>
		<div class="register-box">
			<template v-if="useType && this.config.communicationMode != communicationModeDic.ONLINE">
				<div class="register-text">{{ $t(`验证.没有嫌疑人信息`) }}？{{ $t(`验证.点击`) }}</div>
				<div class="register-btn" @click="goRegister()">{{ $t(`验证.立即注册`) }}</div>
			</template>
		</div>
		<div class="ui-button-box">
			<ui-button type="white" @click="close()">退出认证{{ countdown > 0 ? `(${countdown})s` : '' }}</ui-button>
		</div>
		<!-- <div class="close-btn" @click="close()">{{ $t(`验证.退出认证`) }}({{ countdown }}s)</div> -->
	</div>
</template>

<script>
import { communicationModeDic } from '@/common/libs/options'
import { mapGetters } from 'vuex'
export default {
	name: 'verifyAccount',
	data() {
		return {
			communicationModeDic,
			timer: null,
			countdown: 60,
			modal: false,
			user: {
				name: '',
				password: ''
			},
			activeType: 'name',
			currentType: '0',
			keyboardLayout: [
				[{ value: '1' }, { value: '2' }, { value: '3' }, { value: '4' }, { value: '5' }, { value: '6' }, { value: '7' }, { value: '8' }, { value: '9' }, { value: '0' }, { value: this.$t(`验证.符`) }, { value: '-' }, { value: '/' }, { value: ';' }, { value: '(' }, { value: ')' }, { value: '$' }, { value: '&' }, { value: '@' }, { value: '”' }, { value: 'ABC', type: 'key2' }, { value: '.' }, { value: ',' }, { value: ':' }, { value: '’' }, { value: '`' }, { value: '?' }, { value: '!' }, { value: this.$t(`验证.删除`), type: 'key2' }],
				[{ value: '[' }, { value: ']' }, { value: '{' }, { value: '}' }, { value: '#' }, { value: '%' }, { value: '^' }, { value: '*' }, { value: '+' }, { value: '=' }, { value: this.$t(`验证.数`) }, { value: '_' }, { value: '"' }, { value: '|' }, { value: '~' }, { value: '>' }, { value: '<' }, { value: '€' }, { value: '£' }, { value: '¥' }, { value: 'ABC', type: 'key2' }, { value: '.' }, { value: ',' }, { value: ':' }, { value: '’' }, { value: '`' }, { value: '?' }, { value: '!' }, { value: this.$t(`验证.删除`), type: 'key2' }],
				[{ value: 'q' }, { value: 'w' }, { value: 'e' }, { value: 'r' }, { value: 't' }, { value: 'y' }, { value: 'u' }, { value: 'i' }, { value: 'o' }, { value: 'p' }, { value: 'Aa' }, { value: 'a' }, { value: 's' }, { value: 'd' }, { value: 'f' }, { value: 'g' }, { value: 'h' }, { value: 'j' }, { value: 'k' }, { value: 'l' }, { value: '123', type: 'key2' }, { value: 'z' }, { value: 'x' }, { value: 'c' }, { value: 'v' }, { value: 'b' }, { value: 'n' }, { value: 'm' }, { value: this.$t(`验证.删除`), type: 'key2' }],
				[{ value: 'Q' }, { value: 'W' }, { value: 'E' }, { value: 'R' }, { value: 'T' }, { value: 'Y' }, { value: 'U' }, { value: 'I' }, { value: 'O' }, { value: 'P' }, { value: 'aA' }, { value: 'A' }, { value: 'S' }, { value: 'D' }, { value: 'F' }, { value: 'G' }, { value: 'H' }, { value: 'J' }, { value: 'K' }, { value: 'L' }, { value: '123', type: 'key2' }, { value: 'Z' }, { value: 'X' }, { value: 'C' }, { value: 'V' }, { value: 'B' }, { value: 'N' }, { value: 'M' }, { value: this.$t(`验证.删除`), type: 'key2' }]
			],
			cursorPosition: null,
			cursorLeft: 82,
			routerName: 'home',
			isLoad: false
		}
	},
	computed: {
		...mapGetters({
			allConfig: 'app/allConfig',
			config: 'app/config',
			nextRouter: 'app/nextRouter',
			userInfo: 'user/userInfo',
			nextUser: 'app/nextUser',
			useType: 'app/useType'
		})
	},
	methods: {
		goRegister() {
			this.$router.push('verifyRegister')
		},
		userFlag(data) {
			if (!this.useType && data.userType === '3') {
				return this.showWarning(`${this.$t(`验证.请工作人员验证`)}!`)
			}
			const isAdmin = data.userType === '1'
			if (isAdmin) {
				this.handleAdmin(data)
				return
			}
			const nextUserId = this.nextUser[0]?.userId
			if (nextUserId) {
				this.handleOfficer(data, nextUserId)
				return
			}
			if (this.config.twoFactorAuthentication === '1') {
				this.handleTwoFactorAuth(data)
			} else {
				this.handleSingleAuth(data)
			}
		},
		handleAdmin(data) {
			if (!this.userInfo) {
				this.$store.commit('user/setUserInfo', data)
			}
			this.showSuccess(`${this.$t(`验证.验证成功`)}!`)
			this.toPage(this.nextRouter)
		},
		handleOfficer(data, nextUserId) {
			if (nextUserId == data.id) {
				this.showSuccess(`${this.$t(`验证.验证成功`)}!`)
				this.$store.commit('user/setUserInfo', data)
				this.toPage(this.nextRouter)
			} else {
				this.showWarning(`${this.$t(`验证.对比人员不一致`)}!`)
			}
		},
		handleTwoFactorAuth(data) {
			if (this.userInfo) {
				this.processTwoFactorAuth(data)
			} else {
				this.$store.commit('user/setUserInfo', data)
				const text = data.userType === '3' ? `第一次验证成功,请关联民警或管理员识别` : !this.useType ? `第一次验证成功,请管理员识别!` : `第一次验证成功,请管理员或关联嫌疑人识别!`
				this.showSuccess(text)
				if (this.config.loginMethod.includes(',')) {
					this.$router.push('verifyHome')
				} else {
					this.user.name = ''
					this.user.password = ''
					this.activeType = 'name'
					this.cursorLeft = '82'
				}
			}
		},
		processTwoFactorAuth(data) {
			if (this.userInfo.userType === '3') {
				this.verifyAssociatedPolice(data)
			} else {
				this.verifyAdminOrOther(data)
			}
		},
		verifyAssociatedPolice(data) {
			if (this.userInfo.associatedPoliceId == data.id || this.userInfo.associatedPoliceId == data.idCard) {
				this.showSuccess(`${this.$t(`验证.双重验证成功`)}!`)
				this.toPage(this.nextRouter)
			} else {
				this.showWarning(`${this.$t(`验证.账号所属人员非关联民警`)}!`)
			}
		},
		verifyAssociatedOther(data) {
			if (this.userInfo.id == data.associatedPoliceId || this.userInfo.idCard == data.associatedPoliceId) {
				this.showSuccess(`${this.$t(`验证.双重验证成功`)}!`)
				this.$store.commit('user/setUserInfo', data)
				this.toPage(this.nextRouter)
			} else {
				this.showWarning(`${this.$t(`验证.账号所属人员非关联嫌疑人`)}!`)
			}
		},
		verifyAdminOrOther(data) {
			if (data.userType === '1') {
				this.showSuccess(`${this.$t(`验证.双重验证成功`)}!`)
				this.toPage(this.nextRouter)
			} else {
				if (this.useType) {
					this.verifyAssociatedOther(data)
				} else {
					this.showWarning(`${this.$t(`验证.账号所属人员需为管理员`)}!`)
				}
			}
		},
		handleSingleAuth(data) {
			this.showSuccess(`${this.$t(`验证.验证成功`)}!`)
			this.$store.commit('user/setUserInfo', data)
			this.toPage(this.nextRouter)
		},
		showSuccess(message) {
			this.$baseTip.success(message)
		},
		showWarning(message) {
			this.$baseTip.info(message)
		},
		toPage(page) {
			this.$router.push(page)
		},
		changeType() {
			if (this.config.loginMethod.includes(',')) {
				this.$router.push('verifyHome')
			}
		},
		async submit() {
			if (!this.user.name) {
				this.$baseTip.info(`${this.$t(`验证.请输入账号`)}!`)
				return false
			} else if (!this.user.password) {
				this.$baseTip.info(`${this.$t(`验证.请输入密码`)}!`)
				return false
			}
			const params = {
				loginId: this.user.name,
				password: this.user.password
			}
			if (this.isLoad) {
				return
			}
			this.isLoad = true
			try {
				const res = await this.$http.webApi.login(params)
				const { data } = res
				this.userFlag(data)
			} catch (error) {
			} finally {
				this.isLoad = false
			}
		},
		handleClick(event, name) {
			this.activeType = name
			const rect = this.$refs[name].getBoundingClientRect()
			const x = event.clientX - rect.left - 82 // 获取点击位置相对于输入框的偏移量
			const chars = this.$refs[name].querySelectorAll('.text') // 获取所有字符元素
			let accumulatedWidth = 0
			let position = 0 // 遍历每个字符，计算其实际宽度并确定点击位置
			if (x < 0) {
				this.cursorPosition = 0
				this.cursorLeft = 82
				return
			}
			for (let i = 0; i < chars.length; i++) {
				const charRect = chars[i].getBoundingClientRect()
				accumulatedWidth += charRect.width
				if (x < accumulatedWidth) {
					position = i + 1
					break
				}
				if (x > accumulatedWidth) {
					position = chars.length
				}
			}

			this.cursorPosition = position
			this.cursorLeft = accumulatedWidth + 82
		},

		inputKey(char) {
			if (char === this.$t(`验证.数`) || char === '123') {
				this.currentType = '0'
				return
			}
			if (char === this.$t(`验证.符`)) {
				this.currentType = '1'
				return
			}
			if (char === 'ABC' || char === 'aA') {
				this.currentType = '2'
				return
			}
			if (char === 'Aa') {
				this.currentType = '3'
				return
			}
			if (char === this.$t(`验证.删除`)) {
				if (this.cursorPosition == 0) return
				if (this.user[this.activeType] == '') return
				this.user[this.activeType] = this.user[this.activeType].slice(0, this.cursorPosition - 1) + this.user[this.activeType].slice(this.cursorPosition)
				this.cursorPosition = this.cursorPosition - 1
			} else {
				this.user[this.activeType] = this.user[this.activeType].slice(0, this.cursorPosition) + char + this.user[this.activeType].slice(this.cursorPosition)
				this.cursorPosition = this.cursorPosition + 1
			}
			this.$nextTick(() => {
				this.updateCursorPosition()
			})
		},
		updateCursorPosition() {
			const activeTextRef = this.$refs[this.activeType].querySelectorAll('.text')
			let accumulatedWidth = 0
			for (let i = 0; i < this.cursorPosition; i++) {
				if (activeTextRef[i]) {
					const charRect = activeTextRef[i] && activeTextRef[i].getBoundingClientRect()
					accumulatedWidth += charRect.width
				}
			}
			this.cursorLeft = accumulatedWidth + 82
		},
		close() {
			this.timer && clearInterval(this.timer)
			this.$router.push('home')
		}
	},
	beforeDestroy() {
		this.timer && clearInterval(this.timer)
		this.timer = null
	},
	created() {
		this.countdown = this.config.operationTime
		if (this.countdown > 0) {
			this.timer = setInterval(() => {
				this.countdown--
				if (this.countdown <= 0) {
					this.close()
				}
			}, 1000)
		}
	}
}
</script>

<style lang="less" scoped>
.verify-account {
	position: fixed;
	top: 100px;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	background: url('../../assets/images/home/<USER>') no-repeat;
	background-size: cover;
	background-position: center -100px;
	flex-direction: column;
	align-items: center;
	.verify-account-top {
		width: 760px;
		padding-top: 77px;
		padding-bottom: 160px;
		.name {
			font-weight: bold;
			font-size: 62px;
			color: #042767;
			line-height: 94px;
			height: 94px;
		}
		.title {
			font-weight: bold;
			font-size: 40px;
			color: #042767;
			line-height: 72px;
			height: 72px;
		}
	}
	.input-custom {
		width: 760px;
		height: 120px;
		border: 2px solid #337fff;
		font-size: 40px;
		padding-left: 82px;
		border-radius: 8px;
		line-height: 120px;
		position: relative;
		display: inline-block;
		user-select: none; /* 禁止选择文本 */
		white-space: nowrap;
		margin-bottom: 58px;
		overflow: hidden;
		.text {
			display: inline-block;
			font-size: 40px;
			text-align: center;
		}
		.placeholder-text {
			color: #a2acc6;
		}
		&.focus {
			border-radius: 10px;
			border: 2px solid #20dbe9;
		}
		.cursor {
			position: absolute;
			height: 60px;
			top: 30px;
			width: 2px;
			background-color: rgb(96, 129, 238);
			pointer-events: none;
			animation: blink 1s infinite;
		}
		.icon {
			position: absolute;
			top: 38px;
			left: 22px;
			width: 48px;
			height: 48px;
		}
	}
	.keyboard {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 50px;
		justify-content: center;
		text-align: center;
		.key {
			// box-sizing: border-box;
			width: 96px;
			line-height: 120px;
			cursor: pointer;
			font-size: 60px;
			background: #ffffff;
			border-radius: 12px;
			border: 2px solid #2b5fda;
			color: #2b5fda;
			margin: 10px 5px;
			-webkit-tap-highlight-color: transparent; /* 禁用触摸高亮 */
			touch-action: manipulation; /* 优化触摸行为 */
			&:focus {
				outline: none; /* 移除 outline */
			}
			&.key2 {
				width: 148px;
			}
			&.key-english {
				font-size: 40px;
			}
			&.select-Aa {
				background: #2b5fda;
			}
			&.no-value {
				background: #669eff;
			}
		}
		.key-img {
			width: 48px;
			height: 43px;
		}
		.del-img {
			width: 66px;
			height: 42px;
		}
	}
	.sub-btn {
		width: calc(100% - 240px);
		margin: 0 120px 80px 120px;
		height: 120px;
		background: #2b5fda;
		font-weight: normal;
		font-size: 48px;
		line-height: 120px;
		color: #ffffff;
		text-align: center;
		font-style: normal;
	}
	.tips {
		font-weight: normal;
		font-size: 28px;
		color: #669eff;
		line-height: 40px;
		height: 40px;
		text-align: center;
		font-style: normal;
		text-transform: none;
		margin-bottom: 30px;
	}
	.register-box {
		height: 130px;
		display: flex;
		justify-content: center;
		align-items: center;
		.register-text {
			font-size: 28px;
			color: #5f709a;
		}
		.register-btn {
			width: 154px;
			height: 48px;
			background: #669eff;
			border-radius: 32px;
			font-size: 28px;
			color: #ffffff;
			text-align: center;
			line-height: 48px;
			margin-left: 24px;
		}
	}
	.close-btn {
		width: 758px;
		height: 96px;
		background: #ffffff;
		border: 3px solid #337eff;
		font-weight: normal;
		font-size: 36px;
		color: #337fff;
		line-height: 96px;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
}
@keyframes blink {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0;
	}
}
</style>
