<template>
	<div class="verify-face">
		<div class="head"></div>
		<div class="verify-content">
			<face-box
				v-if="showType === 'face'"
				ref="faceBox"
				videoId="videoIdSTREE"
				@faceValue="faceValue"
				@error="showWarning"
				@takeStatus="
					() => {
						this.verifyTips = $t(`验证.请保持面部放在取景框内`)
					}
				"
			></face-box>
			<div class="swipe-box" v-if="showType === 'swipe'">
				<verify-swipe ref="swipe" @changeValue="faceValue" @error="showWarning"></verify-swipe>
			</div>
			<div class="criminal-box" v-if="showType === 'criminal'">
				<verify-criminal ref="criminal" @changeValue="faceValue" @error="showWarning"></verify-criminal>
			</div>
			<div class="gesture-box" v-if="showType === 'gesture'">
				<verify-gesture @changeValue="faceValue" @error="showWarning"></verify-gesture>
			</div>
			<div class="fingerprint-box" v-if="showType === 'fingerprint'">
				<fingerprint-box
					ref="fingerprintBox"
					@fingerValue="fingerValue"
					@error="showWarning"
					@takeStatus="
						() => {
							this.verifyTips = '请将指纹放在指纹仪上'
						}
					"
				></fingerprint-box>
			</div>
		</div>
		<div class="arrow-box">
			<img src="../../assets/images/verifyHome/arrow.png" alt="" />
		</div>
		<div :class="['verify-tips', $i18n.locale == 'en' && 'verify-tips-english']">{{ verifyTips }}</div>
		<div class="register-box">
			<template v-if="useType && this.config.communicationMode != communicationModeDic.ONLINE">
				<div class="text">{{ $t(`验证.没有嫌疑人信息`) }}？{{ $t(`验证.点击`) }}</div>
				<div class="btn" @click="goRegister()">{{ $t(`验证.立即注册`) }}</div>
			</template>
		</div>
		<div class="type-box">
			<div class="item" v-for="(item, idx) in filteredTypeList" :key="idx" @click="changeType(item)">
				<img :src="item.icon" alt="" />
				<div class="text">{{ $t(`验证.${item.label}`) }}</div>
			</div>
		</div>
		<div class="register-box">
			<div class="text">{{ $t(`验证.认证出现问题`) }}？{{ $t(`验证.试试其他认证方式`) }}</div>
		</div>
		<div class="ui-button-box">
			<ui-button @click="close()">退出认证{{ countdown > 0 ? `(${countdown})s` : '' }}</ui-button>
		</div>
		<div class="tips-img">
			<img v-if="showTips" :src="tipsImg" />
		</div>
		<div class="ceshi" style="flex: 1; display: flex"></div>
		<!-- 遮罩层 -->
		<div v-if="isMaskVisible" class="mask"></div>
	</div>
</template>
<script>
import { communicationModeDic } from '@/common/libs/options'
import { mapGetters } from 'vuex'
export default {
	name: 'verifyFace',
	data() {
		return {
			isMaskVisible: false,
			showType: '',
			verifyTips: this.$t(`验证.请保持面部放在取景框内`),
			typeList: [
				{
					label: '账号认证',
					id: 'account',
					key: '1',
					icon: require('../../assets/images/verifyHome/account.png')
				},
				{
					label: '刷脸认证',
					key: '2',
					id: 'face',
					icon: require('../../assets/images/verifyHome/face.png')
				},
				{
					label: '刷卡认证',
					id: 'swipe',
					key: '3',
					icon: require('../../assets/images/verifyHome/swipe.png')
				},
				{
					label: '手环认证',
					id: 'criminal',
					key: '6',
					icon: require('../../assets/images/verifyHome/criminal.png')
				},
				{
					label: '指纹认证',
					id: 'fingerprint',
					key: '4',
					icon: require('../../assets/images/verifyHome/fingerprint.png')
				},
				{
					label: '手势认证',
					id: 'gesture',
					key: '5',
					icon: require('../../assets/images/verifyHome/gesture.png')
				}
			],
			countdown: 60,
			timer: null,
			showTips: null,
			faceSuccess: require('../../assets/images/verifyHome/face_success.png'),
			faceError: require('../../assets/images/verifyHome/face_error.png'),
			fingerprintSuccess: require('../../assets/images/verifyHome/fingerprint_success.png'),
			fingerprintError: require('../../assets/images/verifyHome/fingerprint_error.png'),
			fingerprintShowStatus: true,
			fingerprintBg: require('../../assets/images/verifyHome/fingerprint_bg.png'),
			fingerprintIng: require('../../assets/images/verifyHome/fingerprint_ing.png'),
			communicationModeDic,
			audio: null
		}
	},
	computed: {
		...mapGetters({
			config: 'app/config',
			nextRouter: 'app/nextRouter',
			userInfo: 'user/userInfo',
			nextUser: 'app/nextUser',
			useType: 'app/useType'
		}),
		filteredTypeList() {
			return this.typeList.filter((item) => item.id !== this.showType && this.config.loginMethod.includes(item.key))
		},
		tipsImg() {
			if (this.showTips === 'faceSuccess') {
				return this.faceSuccess
			}
			if (this.showTips === 'faceError') {
				return this.faceError
			}
			if (this.showTips === 'fingerprintSuccess') {
				return this.fingerprintSuccess
			}
			if (this.showTips === 'fingerprintError') {
				return this.fingerprintError
			}
			return this.faceSuccess
		}
	},
	created() {
		this.countdown = this.config.operationTime
		if (this.countdown > 0) {
			this.timer = setInterval(() => {
				this.countdown--
				if (this.countdown <= 0) {
					this.close()
				}
			}, 1000)
		}
		for (let i = 1; i < this.typeList.length; i++) {
			if (this.config.loginMethod.includes(this.typeList[i].key)) {
				return (this.showType = this.typeList[i].id)
			}
		}
	},
	methods: {
		userFlag(data) {
			if (!this.useType && data.userType === '3') {
				return this.showWarning(`${this.$t(`验证.请工作人员识别`)}!`)
			}
			const isAdmin = data.userType === '1'
			if (isAdmin) {
				this.handleAdmin(data)
				return
			}
			const nextUserId = this.nextUser[0]?.userId
			if (nextUserId) {
				this.handleOfficer(data, nextUserId)
				return
			}
			if (this.config.twoFactorAuthentication === '1') {
				this.handleTwoFactorAuth(data)
			} else {
				this.handleSingleAuth(data)
			}
		},
		handleAdmin(data) {
			if (!this.userInfo) {
				this.$store.commit('user/setUserInfo', data)
			}
			this.showSuccess(`${this.$t(`验证.验证成功`)}!`)
			this.toPage(this.nextRouter)
		},
		handleOfficer(data, nextUserId) {
			if (nextUserId == data.id) {
				this.showSuccess(`${this.$t(`验证.验证成功`)}!`)
				this.$store.commit('user/setUserInfo', data)
				this.toPage(this.nextRouter)
			} else {
				this.showWarning(`${this.$t(`验证.对比人员不一致`)}!`)
			}
		},
		handleTwoFactorAuth(data) {
			if (this.userInfo) {
				this.processTwoFactorAuth(data)
			} else {
				this.$store.commit('user/setUserInfo', data)
				const text = data.userType === '3' ? `第一次验证成功,请关联民警或管理员识别` : !this.useType ? `第一次验证成功,请管理员识别!` : `第一次验证成功,请管理员或关联嫌疑人识别!`
				this.showSuccess(text)
				this.isMaskVisible = true
				setTimeout(() => {
					this.isMaskVisible = false
					if (this.config.loginMethod.includes('2')) {
						this.changeType({ id: 'face' })
						this.$refs.faceBox && this.$refs.faceBox.continue()
					} else {
						this.changeType({ id: 'account' })
					}
				}, 2000)
			}
		},
		processTwoFactorAuth(data) {
			if (this.userInfo.userType === '3') {
				this.verifyAssociatedPolice(data)
			} else {
				this.verifyAdminOrOther(data)
			}
		},
		verifyAssociatedPolice(data) {
			if (this.userInfo.associatedPoliceId == data.id || this.userInfo.associatedPoliceId == data.idCard) {
				this.showSuccess(`${this.$t(`验证.双重验证成功`)}!`)
				this.toPage(this.nextRouter)
			} else {
				this.showWarning(`${this.$t(`验证.识别人员非关联民警`)}!`)
			}
		},
		verifyAssociatedOther(data) {
			if (this.userInfo.id == data.associatedPoliceId || this.userInfo.idCard == data.associatedPoliceId) {
				this.showSuccess(`${this.$t(`验证.双重验证成功`)}!`)
				this.$store.commit('user/setUserInfo', data)
				this.toPage(this.nextRouter)
			} else {
				this.showWarning(`${this.$t(`验证.账号所属人员非关联嫌疑人`)}!`)
			}
		},
		verifyAdminOrOther(data) {
			if (data.userType === '1') {
				this.showSuccess(`${this.$t(`验证.双重验证成功`)}!`)
				this.toPage(this.nextRouter)
			} else {
				if (this.useType) {
					this.verifyAssociatedOther(data)
				} else {
					this.showWarning(`${this.$t(`验证.识别人员需为管理员`)}!`)
				}
			}
		},
		handleSingleAuth(data) {
			this.showSuccess(`${this.$t(`验证.验证成功`)}!`)
			this.$store.commit('user/setUserInfo', data)
			this.toPage(this.nextRouter)
		},
		showSuccess(message) {
			this.audio = new Audio(require('../../assets/audio/verify_success.mp3'))
			this.audio.play()
			this.verifyTips = message
			this.showType == 'face' && this.veify('faceSuccess')
			this.showType == 'fingerprint' && this.veify('fingerprintSuccess')
		},
		showWarning(message) {
			this.audio = new Audio(require('../../assets/audio/verify_failure.mp3'))
			this.audio.play()
			this.verifyTips = message
			if (this.showType == 'face') {
				this.veify('faceError')
				this.$refs.faceBox && this.$refs.faceBox.continue()
			}
			if (this.showType == 'fingerprint') {
				this.veify('fingerprintError')
				this.$refs.fingerprintBox && (this.$refs.fingerprintBox.fingerStatusing = false)
				this.$refs.fingerprintBox && this.$refs.fingerprintBox.againCapture()
			}
			if (this.showType == 'swipe') {
				this.$refs.swipe && (this.$refs.swipe.loading = false)
			}
			if (this.showType == 'criminal') {
				this.$refs.criminal && (this.$refs.criminal.loading = false)
			}
		},
		toPage(page) {
			// 这里增加遮罩层防止在接口请求过程中触发了跳转导致问题
			this.isMaskVisible = true
			setTimeout(() => {
				this.isMaskVisible = false
				this.$router.push(page)
			}, 3000)
		},
		faceValue(info) {
			this.userFlag(info)
		},
		fingerValue(info) {
			this.userFlag(info)
		},
		veify(type) {
			this.showTips = null
			this.showTips = type
			setTimeout(() => {
				this.showTips = null
			}, 1500)
		},
		goRegister() {
			this.$router.push('verifyRegister')
		},
		changeType(item) {
			if (item.id === 'account') {
				this.$router.push('verifyAccount')
			} else {
				this.showType = item.id
				if (this.showType === 'face') {
					this.verifyTips = this.$t(`验证.请保持面部放在取景框内`)
				} else if (this.showType === 'swipe') {
					this.verifyTips = '请将民警卡放在感应区域'
				} else if (this.showType === 'criminal') {
					this.verifyTips = '请将手环放在感应区域'
				} else if (this.showType === 'fingerprint') {
					this.verifyTips = '请将指纹放在指纹仪上'
				} else if (this.showType === 'gesture') {
					this.verifyTips = '输入手势密码'
				} else {
					this.verifyTips = ''
				}
			}
		},
		close() {
			this.timer && clearInterval(this.timer)
			this.timer = null
			this.$router.push('home')
		}
	},
	beforeDestroy() {
		this.timer && clearInterval(this.timer)
		this.timer = null
	}
}
</script>

<style lang="less" scoped>
.mask {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}
.verify-face {
	position: fixed;
	top: 100px;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	.head {
		width: 100%;
		height: 180px;
		display: flex;
		align-items: center;
		padding-right: 43px;
		justify-content: flex-end;
		.switch {
			width: 200px;
			height: 64px;
			background: #ffffff;
			border-radius: 80px;
			font-size: 28px;
			color: #2668ff;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 42px;
				height: 42px;
				margin-right: 5px;
			}
		}
	}
	.verify-content {
		height: 724px;
		width: 724px;
		margin-bottom: 36px;
	}
	.verify-tips {
		font-size: 42px;
		color: #2b3346;
		line-height: 55px;
		text-align: center;
		font-weight: bold;
		height: 55px;
		&.verify-tips-english {
			font-size: 30px;
		}
	}
	.arrow-box {
		height: 70px;
		display: flex;
		justify-content: center;
		img {
			width: 52px;
			height: 52px;
		}
	}
	.type-box {
		margin-top: 98px;
		margin-bottom: 26px;
		// height: 261px;
		display: flex;
		.item {
			margin: 0 10px;
			width: 188px;
			height: 188px;
			background: #ffffff;
			box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15), inset 0px 3px 3px 1px rgba(48, 116, 218, 0.1);
			border-radius: 8px;
			padding-top: 25px;
			img {
				height: 102px;
				width: 100%;
			}
			.text {
				font-size: 28px;
				color: #2b60da;
				line-height: 40px;
				text-align: center;
			}
		}
	}
	.register-box {
		height: 104px;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 52px;
		.text {
			font-size: 28px;
			color: #5f709a;
		}
		.btn {
			width: 154px;
			height: 48px;
			background: #669eff;
			border-radius: 32px;
			font-size: 28px;
			color: #ffffff;
			text-align: center;
			line-height: 48px;
			margin-left: 24px;
		}
	}
	.tips {
		font-weight: normal;
		font-size: 28px;
		color: #669eff;
		line-height: 40px;
		text-align: center;
		font-style: normal;
		text-transform: none;
		margin-bottom: 130px;
	}
	.tips-img {
		position: absolute;
		top: 20px;
		left: 50%;
		transform: translateX(-50%);
	}
}
</style>
