<template>
	<div class="verify-register">
		<ui-steps :stepsData="stepsData" :current="current"></ui-steps>
		<div class="container">
			<div class="ui-form-box" v-if="current === 0" :class="[$i18n.locale == 'en' && 'label-font-35']">
				<div class="ui-form-item">
					<div class="label">{{ $t(`嫌疑人注册.姓名`) }}</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.name" :placeholder="$t(`选择.必填`)" />
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">{{ $t(`嫌疑人注册.身份证号`) }}</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.certificateNumber" :placeholder="$t(`选择.选填`)" />
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">{{ $t(`嫌疑人注册.关联民警`) }}</div>
					<div class="value">
						<ui-select :options="policeList" valueKey="id" labelKey="name" :placeholder="$t(`选择.必填`)" :select-title="$t(`选择.关联民警选择`)" v-model="formData.associatedPoliceId" @change="changeAssociated"></ui-select>
					</div>
				</div>
				<template v-if="config.loginMethod.includes('1')">
					<div class="ui-form-item">
						<div class="label">{{ $t(`嫌疑人注册.账号`) }}</div>
						<div class="value">
							<ui-input v-keyboard v-model="formData.loginId" :placeholder="config.loginMethod.includes('2') ? $t(`选择.选填`) : $t(`选择.必填`)" />
						</div>
					</div>
					<div class="ui-form-item">
						<div class="label">{{ $t(`嫌疑人注册.密码`) }}</div>
						<div class="value">
							<ui-input v-keyboard v-model="formData.password" :placeholder="config.loginMethod.includes('2') ? $t(`选择.选填`) : $t(`选择.必填`)" />
						</div>
					</div>
				</template>
				<div class="type-box">
					<div class="item" v-if="config.loginMethod.includes('2')" @click="showFaceBox = true">
						<div class="img-box">
							<img :src="formData.faceImg || require('../../assets/images/settings/face.png')" alt="" />
						</div>
						<div class="text" :class="[$i18n.locale == 'en' && 'font-32']">{{ formData.faceImg ? $t(`嫌疑人注册.修改人脸`) : $t(`嫌疑人注册.录入人脸`) }}</div>
					</div>
				</div>
			</div>
			<div v-if="current === 2" class="ui-success-box">
				<img src="../../assets/images/settings/icon_success.png" alt="" />
				<div class="text">{{ $t(`嫌疑人注册.人员注册成功`) }}！</div>
				<div class="tips">{{ countdown }}s{{ $t(`嫌疑人注册.后`) }}，即将返回认证页面</div>
			</div>
		</div>
		<div class="ui-form-submit" v-show="current !== 2">
			<div class="cancel-btn" @click="back()">{{ $t(`嫌疑人注册.取消`) }}</div>
			<div class="confirm-btn" :class="{ disabled: !isFormValid }" @click="isFormValid ? changeSteps() : null">{{ $t(`嫌疑人注册.下一步`) }}</div>
		</div>
		<ui-modal v-model="showFaceBox" :title="$t(`嫌疑人注册.录入人脸`)">
			<div v-if="showFaceBox" class="face-modal">
				<face-box
					ref="faceBox"
					videoId="videoIdfive"
					type="register"
					@faceValue="faceValue"
					@error="
						(msg) => {
							faceTips = msg
						}
					"
					@takeStatus="
						() => {
							faceTips = $t(`嫌疑人注册.请保持面部放在取景框内`)
						}
					"
				></face-box>
				<div class="tips">{{ faceTips }}</div>
			</div>
			<div class="ui-modal-footer" v-if="face">
				<div class="btn cancel-btn" @click="retake">{{ $t(`嫌疑人注册.重拍`) }}</div>
				<div class="btn" @click="submit">{{ $t(`嫌疑人注册.确认`) }}</div>
			</div>
		</ui-modal>
		<ui-loading v-model="showLoading"></ui-loading>
	</div>
</template>

<script>
import Crypto from '@/common/libs/crypto'
import { mapGetters } from 'vuex'
import { isIdCard } from '@/common/libs/util'
export default {
	name: 'verifyRegister',
	data() {
		return {
			current: 0,
			formData: {
				userType: '3'
			},
			tableData: [],
			tableUserData: [],
			policeList: [],
			oldStepsData: [this.$t(`嫌疑人注册.基本信息`), this.$t(`嫌疑人注册.绑定柜格`), this.$t(`嫌疑人注册.完成注册`)],
			countdown: 5,
			associatedIscds: '',
			selectedIscds: {},
			showFaceBox: false,
			face: '',
			faceTips: this.$t(`嫌疑人注册.请保持面部放在取景框内`),
			showLoading: false
		}
	},
	computed: {
		...mapGetters({
			config: 'app/config'
		}),
		isFormValid() {
			if (this.current === 0) {
				const requiredFields = [this.formData.name, this.formData.associatedPoliceId]
				const isValid = this.formData.faceImg || (this.formData.loginId && this.formData.password)
				return requiredFields.every((value) => value) && isValid
				// return requiredFields.every((value) => value)
			}
			return true
		},
		stepsData() {
			const arr = [this.$t(`嫌疑人注册.基本信息`), this.$t(`嫌疑人注册.绑定柜格`), this.$t(`嫌疑人注册.完成注册`)]
			return arr
		}
	},
	methods: {
		retake() {
			this.face = null
			this.faceTips = this.$t(`嫌疑人注册.请保持面部放在取景框内`)
			this.$refs.faceBox.takePhoto()
		},
		submit() {
			this.$set(this.formData, 'faceImg', this.face)
			this.showFaceBox = false
		},
		faceValue(face) {
			this.face = face
			this.faceTips = `${this.$t(`嫌疑人注册.获取成功`)},${this.$t(`嫌疑人注册.按确认继续`)}`
		},
		changeAssociated(item) {
			if (item && item.length > 0) {
				const data = item[0]
				this.formData.associatedPoliceId = data.id
				this.formData.associatedPoliceName = data.name
				this.formData.associatedPoliceNumber = data.policeNumber
			}
		},
		changeType() {
			this.showFaceBox = true
		},
		back() {
			this.timer && clearInterval(this.timer)
			this.timer = null
			this.$router.go(-1)
		},
		changeSteps() {
			// const flag = isIdCard(this.formData.certificateNumber)
			// if (!flag) {
			// 	return this.$baseTip.info('请输入正确身份证格式！')
			// }
			const params = { ...this.formData }
			if (params.password) {
				params.password = Crypto.aesEncrypt(params.password)
			}
			this.showLoading = true
			this.$http.webApi
				.userRegister(params)
				.then(() => {
					this.current = 2
					this.timer = setInterval(() => {
						this.countdown--
						if (this.countdown <= 0) {
							this.back()
						}
					}, 1000)
				})
				.finally(() => {
					this.showLoading = false
				})
		}
	},
	destroyed() {
		this.timer && clearInterval(this.timer)
		this.timer = null
	},
	created() {
		this.$http.webApi.userPageList({ page: 1, size: 999, userType: 2 }).then((res) => {
			const { data } = res.data
			this.policeList = data || []
		})
	}
}
</script>
<style lang="less" scoped>
.verify-register {
	flex: 1;
	padding: 0 40px;
	display: flex;
	flex-direction: column;
	.container {
		flex: 1;
		display: flex;
		.ui-form-box {
			flex: 1;
			padding: 100px 40px 0;
			max-height: 1260px;
			overflow-y: auto;
			.type-box {
				display: flex;
				padding-left: 200px;
				.item {
					width: 209px;
					height: 285px;
					background: #ffffff;
					box-shadow: 0px 4px 12px 1px #cedcf5;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					margin-right: 30px;
					.img-box {
						width: 138px;
						height: 138px;
						background: #f5faff;
						padding: 26px;
						margin-bottom: 30px;
						img {
							width: 80px;
							height: 80px;
						}
					}
					.text {
						font-size: 36px;
						color: #7a98cc;
						line-height: 36px;
						&.font-32 {
							font-size: 32px;
						}
					}
				}
			}
		}
		.table-box {
			flex: 1;
			padding-top: 40px;
		}
	}
}
.face-modal {
	display: flex;
	align-items: center;
	flex-direction: column;
	margin-bottom: 30px;
	.tips {
		font-size: 42px;
		color: #2b3346;
		line-height: 162px;
		text-align: center;
		font-weight: bold;
	}
}
</style>
