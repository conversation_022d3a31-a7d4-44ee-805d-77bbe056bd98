<template>
	<div class="storage-situation-detail">
		<div class="title">
			<div class="title-item">
				{{ $t('统计.总量') }}：
				<span class="value">{{ statisticsObj.allNum }}</span>
			</div>
			<div class="title-item free">{{ $t('统计.空闲') }}：({{ statisticsObj.freeNum }})</div>
			<div class="title-item user">{{ $t('统计.占用') }}：({{ statisticsObj.userNum }})</div>
			<div class="title-item fault">{{ $t('统计.不可用') }}：({{ statisticsObj.faultNum }})</div>
		</div>
		<div class="table-box">
			<ui-carousel-box :data="tableData" :contentWidth="870" :conditionTop="55" :isReverse="config.mainScreenType == '3'">
				<template slot-scope="{ data }">
					<cabinet-table :tableObj="data" :tableUserData="tableUserData" size="big"></cabinet-table>
				</template>
			</ui-carousel-box>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
	name: 'storageSituationDetail',
	data() {
		return {
			statisticsObj: {},
			tableData: [],
			tableUserData: []
		}
	},
	computed: {
		...mapGetters({
			config: 'app/config'
		})
	},
	methods: {
		getData() {
			this.$http.appApi.getSidesAllList().then((res) => {
				this.tableData = res.data
			})
			this.$http.webApi.getIscdsUserList().then((res) => {
				this.tableUserData = res.data
			})
			this.$http.webApi.getIscdsUserNum().then((res) => {
				this.statisticsObj = res.data || {}
			})
		}
	},
	created() {
		this.getData()
	}
}
</script>
<style scoped lang="less">
.storage-situation-detail {
	flex: 1;
	display: flex;
	flex-direction: column;
	.title {
		margin: 0 auto;
		height: 72px;
		width: 822px;
		background: #fff;
		box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15), inset 0px 3px 3px 1px rgba(48, 116, 218, 0.1);
		display: flex;
		align-items: center;
		.title-item {
			flex: 1;
			font-size: 26px;
			color: #64759a;
			display: flex;
			align-items: center;
			justify-content: center;
			&.free {
				color: #00d18b;
			}
			&.user {
				color: #445bf3;
			}
			&.fault {
				color: #e60050;
			}
			.value {
				font-size: 36px;
				color: #2b4d71;
			}
		}
	}
	.table-box {
		flex: 1;
		padding-top: 40px;
	}
}
</style>
