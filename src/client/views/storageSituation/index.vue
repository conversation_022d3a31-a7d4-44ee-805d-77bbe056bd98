<template>
	<div class="storage-situation">
		<div class="title">
			<div class="title-item">
				{{ $t('统计.总量') }}：
				<span class="value">{{ statisticsObj.allNum }}</span>
			</div>
			<div class="title-item free">{{ $t('统计.空闲') }}：({{ statisticsObj.freeNum }})</div>
			<div class="title-item user">{{ $t('统计.占用') }}：({{ statisticsObj.userNum }})</div>
			<div class="title-item fault">{{ $t('统计.不可用') }}：({{ statisticsObj.faultNum }})</div>
		</div>
		<div class="table-box">
			<ui-carousel-box :data="tableData" :contentWidth="870" :conditionTop="55" :isReverse="config.mainScreenType == '3'">
				<template slot-scope="{ data }">
					<cabinet-table :tableObj="data" :tableUserData="tableUserData" @selected="selectIscds" size="big" :operateType="multipleSelect ? 'multiple' : ''" @selectedList="changeList" :userId="userId" :selectList="selectedIscds[data.name] || []"></cabinet-table>
				</template>
			</ui-carousel-box>
		</div>
		<div class="select-box" v-if="userInfo.userType != '1'">
			<div>
				<span class="label">多选:</span>
				<ui-switch v-model="multipleSelect">
					<span slot="open">开启</span>
					<span slot="close">关闭</span>
				</ui-switch>
			</div>
			<div class="btn" @click="multipleSubmit">开柜</div>
		</div>
		<ui-select ref="selectType" :showSelect="false" :options="associatedUserList" :select-title="$t('选择.选取关联人员')" valueKey="id" labelKey="name" v-model="searchType" @change="associatedUser"></ui-select>
	</div>
</template>

<script>
import { communicationModeDic } from '@/common/libs/options'
import { mapGetters } from 'vuex'
export default {
	name: 'storageSituation',
	data() {
		return {
			statisticsObj: {},
			tableData: [],
			tableUserData: [],
			associatedUserList: [],
			searchType: '',
			iscdsInfo: {},
			useTypeObj: {
				crwp: '1',
				qcwp: '2',
				lsjc: '3',
				jcgh: '4'
			},
			multipleSelect: false,
			selectedIscds: {},
			userId: '',
			selectList: [],
			linshi: {}
		}
	},
	computed: {
		...mapGetters({
			useType: 'app/useType',
			userInfo: 'user/userInfo',
			config: 'app/config'
		})
	},
	methods: {
		changeList(list) {
			const result = Object.values(this.selectedIscds)
				.filter((value) => Array.isArray(value) && value.length > 0)
				.flatMap((value) => value)
			if (result.length === 0 && this.userInfo.userType == 2) {
				if (!list.list[0]?.userId) {
					this.$refs.selectType.isOpen = true
					this.linshi = list
				} else {
					if (list.list[0]) {
						const { userId, userName, userType, associatedPoliceId } = list.list[0]
						this.iscdsInfo.going = {
							id: userId,
							name: userName,
							userType,
							associatedPoliceId
						}
						this.userId = list.list[0].userId
					} else {
						this.iscdsInfo.going = {}
						this.userId = ''
					}

					this.$set(this.selectedIscds, list.label, list.list)
				}
			} else {
				this.$set(this.selectedIscds, list.label, list.list)
				const result = Object.values(this.selectedIscds)
					.filter((value) => Array.isArray(value) && value.length > 0)
					.flatMap((value) => value)
				if (result.length === 0 && this.userInfo.userType == 2) {
					this.iscdsInfo.going = {}
					this.userId = ''
				}
			}
		},
		multipleSubmit() {
			const result = Object.values(this.selectedIscds)
				.filter((value) => Array.isArray(value) && value.length > 0)
				.flatMap((value) => value)
			if (result.length === 0) {
				return this.$baseTip.info('请选择柜格!')
			}
			result.forEach((item) => {
				item.going = this.iscdsInfo.going
			})
			this.$store.commit('app/setNextUser', result)
			this.$router.push({ name: 'animationTips', query: this.useType })
		},
		associatedUser(item) {
			if (item && item.length > 0) {
				this.iscdsInfo.going = item[0]
				if (!this.multipleSelect) {
					this.$store.commit('app/setNextUser', [this.iscdsInfo])
					this.$router.push({ name: 'animationTips', query: this.useType })
				} else {
					this.$set(this.selectedIscds, this.linshi.label, this.linshi.list)
					this.userId = item[0].id
				}
			} else {
				this.iscdsInfo.going = {}
				this.userId = ''
			}
		},
		selectIscds(info) {
			if (this.multipleSelect) {
				return
			}
			if (this.userInfo.userType === '3') {
				if (this.useType == 'crwp') {
					if (info.userId && info.userId != this.userInfo.id) {
						return this.$baseTip.info('请选择空闲柜格!')
					}
					this.$store.commit('app/setNextUser', [info])
					this.$router.push({ name: 'animationTips', query: this.useType })
				} else {
					if (info.userId != this.userInfo.id) {
						return this.$baseTip.info('请选择绑定柜格!')
					}
					//加入判断，如果没有临时借出，就不能借出归还start
					if (this.useType == 'jcgh' && info.status != 'lsjc') {
						return this.$baseTip.info('未查询到存在临时借出记录!')
					}
					//end
					this.$store.commit('app/setNextUser', [info])
					// this.$router.push('backTips')
					this.$router.push({ name: 'animationTips', query: this.useType })
				}
			} else if (this.userInfo.userType === '2') {
				if (info.userId) {
					// 警员去操作关联嫌疑人已经关联的柜子
					const obj = this.associatedUserList.find((item) => item.id == info.userId)
					if (!obj) {
						return this.$baseTip.info('无权限，请重新选择!')
					}
					//加入判断，如果没有临时借出，就不能借出归还start
					if (this.useType == 'jcgh' && info.status != 'lsjc') {
						return this.$baseTip.info('未查询到存在临时借出记录!')
					}
					//end
					this.$store.commit('app/setNextUser', [info])
					this.$router.push({ name: 'animationTips', query: this.useType })
				} else {
					if (this.useType == 'crwp') {
						this.iscdsInfo = info
						this.$refs.selectType.isOpen = true
						// 警员去操作空闲柜子
					} else {
						this.$baseTip.info('无权限，请重新选择!')
					}
				}
			} else if (this.userInfo.userType === '1') {
				if (this.config.communicationMode == communicationModeDic.ONLINE) {
					if (!info.userId) {
						return this.$baseTip.info('请选择绑定的柜格!')
					}
					this.onliceOperation(info)
				} else {
					this.$store.commit('app/setNextUser', [info])
					this.$router.push({ name: 'animationTips', query: this.useType })
				}
			} else {
				return false
			}
		},
		async onliceOperation(info) {
			try {
				const { data = [] } = await this.$http.webApi.findPersongoods({ personMagicId: info.userId, status: this.useType == 'jcgh' ? '3' : '' })
				const arr = data.filter((item) => item.boxId === info.id)
				if (!arr.length) {
					throw new Error('未查询到存在临时借出记录!')
				}
				const ids = arr.map((item) => item.magicId)
				this.$store.commit('app/setGoodsList', ids)
				this.$store.commit('app/setNextUser', [info])
				const params = {
					personMagicId: info.userId,
					boxIdList: [info.id],
					status: this.useTypeObj[this.useType]
				}
				await this.isCanOpenDoor(params)
			} catch (err) {
				this.$baseTip.info(err.message)
			}
		},
		async isCanOpenDoor(params) {
			try {
				await this.$http.webApi.isCanOpenDoor(params)
				this.$router.push({ name: 'animationTips', query: this.useType })
			} catch (err) {}
		},
		getData() {
			this.$http.appApi.getSidesAllList().then((res) => {
				this.tableData = res.data
			})
			this.$http.webApi.getIscdsUserList().then((res) => {
				this.tableUserData = res.data
			})
			this.$http.webApi.getIscdsUserNum().then((res) => {
				this.statisticsObj = res.data || {}
			})
			if (this.userInfo.userType === '2') {
				this.$http.webApi.userPageList({ page: 1, size: 99, userType: 3, associatedPoliceId: this.userInfo.id }).then((res) => {
					const { data } = res.data
					this.associatedUserList = data || []
				})
			}
		}
	},
	created() {
		this.userId = this.userInfo.userType == 3 && this.userInfo.id
		this.getData()
	}
}
</script>
<style scoped lang="less">
.storage-situation {
	margin-top: -40px;
	flex: 1;
	display: flex;
	flex-direction: column;
	.title {
		margin: 0 auto;
		height: 72px;
		width: 822px;
		background: #fff;
		box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15), inset 0px 3px 3px 1px rgba(48, 116, 218, 0.1);
		display: flex;
		align-items: center;
		.title-item {
			flex: 1;
			font-size: 26px;
			color: #64759a;
			display: flex;
			align-items: center;
			justify-content: center;
			&.free {
				color: #00d18b;
			}
			&.user {
				color: #445bf3;
			}
			&.fault {
				color: #e60050;
			}
			.value {
				font-size: 36px;
				color: #2b4d71;
			}
		}
	}
	.table-box {
		flex: 1;
		padding-top: 15px;
	}
	.select-box {
		height: 110px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 100px;
		// background: rgba(255, 255, 255, 1);
		.label {
			line-height: 70px;
			vertical-align: top;
			margin-right: 20px;
			font-size: 40px;
		}
		.btn {
			width: 200px;
			height: 70px;
			background: #2b5fda;
			border-radius: 8px;
			font-size: 40px;
			color: #fff;
			text-align: center;
			line-height: 70px;
		}
	}
}
</style>
