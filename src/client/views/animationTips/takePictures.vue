<template>
	<div class="take-pictures">
		<div class="page-title">
			<div class="title-left"></div>
			<div class="title-text">高拍仪拍摄</div>
			<div class="title-right" @click="goHome()">
				首页
				<img src="../../components/pageTitle/images/home.png" alt="" />
			</div>
		</div>
		<div class="content">
			<div class="top">保管柜-{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, getJsonNum, allConfig.lockInfo) }}</div>
			<ui-go-video :play-list="playList" @getImgInfo="getImgInfo" />
			<div class="img-list">
				<div class="img" v-if="imgList.length == 0">
					<img class="default-img" src="../../assets/images/animationTips/no_img.png" alt="" />
				</div>
				<div class="img" v-for="(item, idx) in imgList" :key="idx">
					<img :src="item" alt="" />
					<div class="del" @click="delImg(idx)"></div>
				</div>
			</div>
		</div>
		<div class="form-submit">
			<div class="btn" @click="submit()">确定</div>
		</div>
	</div>
</template>
<script>
import { mapGetters } from 'vuex'
import { convertCabinetCodesToIndices } from '@/common/libs/util'
export default {
	name: 'takePictures',
	data() {
		return {
			convertCabinetCodesToIndices,
			playList: [],
			imgList: []
		}
	},
	computed: {
		...mapGetters({
			nextUser: 'app/nextUser',
			config: 'app/config',
			allConfig: 'app/allConfig'
		}),
		getJsonNum() {
			return this.nextUser.map((obj) => obj.num).join(',')
		}
	},
	methods: {
		delImg(idx) {
			this.imgList.splice(idx, 1)
		},
		submit() {
			this.$emit('getTakePictures', this.imgList)
		},
		getImgInfo(base64) {
			if (this.imgList.length == 10) return this.$baseTip.warning('最多只能截取10张！')
			this.imgList.push(base64)
		},
		goHome() {
			this.$router.push('/home')
		}
	},
	mounted() {
		this.playList = [
			{
				channelId: this.config.channelId,
				// channelId: '110000000000655',
				deviceName: '拍摄摄像头'
			}
		]
	}
}
</script>
<style lang="less" scoped>
.take-pictures {
	height: 100%;
	display: flex;
	flex-direction: column;
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		.top {
			width: 920px;
			height: 204px;
			background: #ffffff;
			border-radius: 8px;
			font-size: 36px;
			color: #2b5fda;
			line-height: 204px;
			font-weight: bold;
			margin-bottom: 60px;
		}
		.img-list {
			width: 920px;
			padding-top: 30px;
			display: flex;
			gap: 15px;
			flex-wrap: wrap;
			.img {
				width: 172px;
				height: 172px;
				background: #ffffff;
				border-radius: 8px;
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
				img {
					width: 150px;
					height: 150px;
				}
				.default-img {
					width: 50px;
					height: 45px;
				}
				.del {
					width: 48px;
					height: 48px;
					right: 0;
					position: absolute;
					top: 0;

					background: url('../../assets/images/signature/del.png') no-repeat;
					background-size: 100% 100%;
				}
			}
		}
	}
}

.page-title {
	height: 140px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding: 0 40px;
	.title-left {
		width: 170px;
		height: 60px;
	}
	.title-right {
		width: 170px;
		height: 60px;
		background: #ffffff;
		border-radius: 40px;
		opacity: 0.7;
		font-size: 26px;
		color: #5f709a;
		text-align: center;
		line-height: 60px;
		img {
			width: 36px;
			height: 36px;
			vertical-align: middle;
		}
	}
	.title-text {
		font-size: 48px;
		color: #2b3346;
		line-height: 140px;
		font-weight: bold;
	}
}
.form-submit {
	height: 256px;
	display: flex;
	justify-content: center;
	align-items: center;
	.btn {
		width: 840px;
		height: 96px;
		background: #2b5fda;
		font-size: 36px;
		color: #ffffff;
		line-height: 96px;
		text-align: center;
		&.disabled {
			background: rgba(43, 95, 218, 0.4);
		}
	}
}
</style>
