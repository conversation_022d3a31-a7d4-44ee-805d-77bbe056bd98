<template>
	<div class="tip-box">
		<template v-if="status == 1">
			<div class="content-title">
				<div>{{ $t('开关柜.请关好柜门') }}</div>
			</div>
			<div class="img-box2">
				<div class="scroll-container" id="scrollContainer">
					<div class="item" :class="['item' + (idx - currentIndex), idx - currentIndex < 0 && 'hide-item']" v-for="(item, idx) in nextUser" :key="idx">{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, item.num, allConfig.lockInfo) }}</div>
				</div>
			</div>
		</template>
		<template v-if="status == 2">
			<div class="content-title">
				<div class="name-box">
					<span class="name">{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, getJsonNum, allConfig.lockInfo) }}</span>
					{{ $t('开关柜.柜门已关闭') }}
				</div>
			</div>
			<div class="img-box3">
				<video autoplay muted>
					<source src="../../assets/images/animationTips/close.mp4" type="video/mp4" />
				</video>
			</div>
		</template>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { convertCabinetCodesToIndices } from '@/common/libs/util'
export default {
	name: 'closeTips',
	props: {
		status: {
			type: Number,
			default: 1
		}
	},
	data() {
		return {
			convertCabinetCodesToIndices,
			currentIndex: 0,
			scrollInterval: null
		}
	},
	computed: {
		...mapGetters({
			nextUser: 'app/nextUser',
			allConfig: 'app/allConfig'
		}),
		getJsonNum() {
			return this.nextUser.map((obj) => obj.num).join(',')
		}
	},
	methods: {
		changeScroll() {
			if (this.nextUser.length <= 4) {
				this.currentIndex = this.nextUser.length - 4
			} else {
				this.scrollInterval = setInterval(() => {
					this.currentIndex++
					if (this.nextUser.length - this.currentIndex <= 4) {
						clearInterval(this.scrollInterval)
						this.scrollInterval = null
					}
				}, 1000)
			}
		}
	},
	mounted() {
		this.$nextTick(() => {
			this.changeScroll()
		})
	}
}
</script>
<style lang="less" scoped>
.tip-box {
	padding-top: 258px;
}
.content-title {
	height: 356px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	.name-box {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.name {
		color: #337fff;
		display: inline-block;
		max-width: 700px;
		font-weight: bold;
		white-space: nowrap; /* 禁止文本换行 */
		overflow: hidden; /* 隐藏超出容器的部分 */
		text-overflow: ellipsis; /* 使用省略号表示被裁剪的文本 */
	}
}
.img-box2 {
	width: 580px;
	height: 580px;
	background: url('../../assets/images/animationTips/open_end.png') no-repeat;
	background-size: 100% 100%;
	margin: 0 auto;
	position: relative;
	.img-name {
		position: absolute;
		top: 210px;
		font-size: 75px;
		color: #fff199;
		width: 100%;
		text-align: center;
		z-index: 2;
	}
	.scroll-container {
		position: absolute;
		width: 100%;
		top: 60px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		height: 280px;
		color: #fff199;
		overflow: hidden;
		.item {
			transition: all 1s ease-in-out;
			width: 100%;
			text-align: center;
			top: 280px;
			position: absolute;
			height: 70px;
			font-size: 75px;
		}
		.item0 {
			height: 57px;
			font-size: 51px;
			opacity: 0.7;
			top: 0;
			position: absolute;
		}
		.item1 {
			top: 57px;
			position: absolute;
			height: 64px;
			font-size: 57px;
			opacity: 0.7;
		}
		.item2 {
			top: 121px;
			position: absolute;
			height: 74px;
			font-size: 66px;
			opacity: 0.7;
		}
		.item3 {
			top: 195px;
			position: absolute;
			height: 84px;
			font-size: 75px;
		}
		.hide-item {
			height: 70px;
			font-size: 51px;
			top: -100px;
			position: absolute;
			opacity: 0;
		}
	}
}
.img-box3 {
	width: 580px;
	height: 580px;
	margin: 0 auto;
	background: #ebf5ff;
	position: relative;
}
</style>
