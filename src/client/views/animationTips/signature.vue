<template>
	<div class="signature-box">
		<div class="signature-title">
			{{ title }}
		</div>
		<div class="content">
			<div class="content-item" v-for="(item, idx) in signatureList" :key="idx">
				<div class="label">
					<div class="header">{{ item.header }}</div>
					<div class="vertical-text">{{ item.text }}</div>
				</div>
				<div class="signature" :class="{ selected: signatureType == item.type }" @click="signature(item, idx)">
					<div class="del" @click.stop="delImg(idx)" v-if="item.signFingerImage || item.signImage || (item.sort && noRefuse)"></div>
					<div class="sort" v-if="item.sort" @click.stop="clickNoRefuse()">拒不配合</div>
					<template v-if="item.sort && noRefuse">
						<div class="signatureing">拒不配合</div>
					</template>
					<template v-else-if="item.signFingerImage || item.signImage">
						<img width="100%" height="100%" :src="item.signFingerImage || item.signImage" alt="" />
					</template>
					<template v-else-if="signatureType != item.type">
						<div class="img"></div>
						<div class="text">点击此区域签名</div>
					</template>
					<template v-else>
						<div class="signatureing">正在签名</div>
					</template>
				</div>
			</div>
		</div>
		<div class="ui-button-box" @click="clickBtn()">
			<ui-button :disabled="!isFormValid" @click="submit()">确定</ui-button>
		</div>
		<signatureModal
			v-if="signatureModalShow"
			@confirm="handleConfirm"
			@cancel="
				() => {
					signatureModalShow = false
					signatureType = ''
				}
			"
		></signatureModal>
	</div>
</template>

<script>
import WebsocketMixin from '@/client/mixins/websocket'
import { communicationModeDic } from '@/common/libs/options'
import { mapGetters } from 'vuex'
const BASE64_PREFIX = 'data:image/*;base64,'
const BASE64_JPEGPREFIX = 'data:image/jpeg;base64,'
export default {
	name: 'signature',
	mixins: [WebsocketMixin],
	data() {
		return {
			signatureModalShow: false,
			title: '请签名捺印确认',
			signatureList: [
				{
					header: '01',
					text: '涉 案 人 员',
					sort: true,
					type: 'person',
					value: '',
					signImage: '',
					fingerImage: '',
					signFingerImage: '',
					personMagicId: '',
					signIdCard: '',
					signUserName: ''
				},
				{
					header: '02',
					text: '民 警',
					sort: false,
					type: 'police',
					value: '',
					signImage: '',
					signIdCard: '',
					signUserName: ''
				},
				{
					header: '03',
					text: '管 理 员',
					sort: false,
					type: 'admin',
					value: '',
					signImage: '',
					signIdCard: '',
					signUserName: ''
				}
			],
			signatureType: '',
			signatureIndex: null,
			noRefuse: false,
			audio: null
		}
	},
	computed: {
		...mapGetters({
			config: 'app/config',
			allConfig: 'app/allConfig',
			useType: 'app/useType',
			nextUser: 'app/nextUser',
			isRemote: 'app/isRemote',
			userInfo: 'user/userInfo'
		}),
		isFormValid() {
			return this.noRefuse || this.signatureList[0].signFingerImage
		}
	},
	methods: {
		handleConfirm(signImage) {
			this.handleSignImage(signImage)
			this.signatureModalShow = false
		},
		clickBtn() {
			if (!this.isFormValid) {
				if (!this.signatureList[0].signImage && !this.signatureList[0].fingerImage && !this.signatureList[0].signFingerImage) {
					return this.$baseTip.info('请嫌疑人签名捺印确认')
				} else if (!this.signatureList[0].signImage) {
					return this.$baseTip.info('请嫌疑人签名确认')
				} else if (!this.signatureList[0].fingerImage) {
					return this.$baseTip.info('请嫌疑人捺印确认')
				}
			}
		},
		clickNoRefuse() {
			this.noRefuse = true
			this.signatureType = ''
			this.signatureList[0].signImage = ''
			this.signatureList[0].fingerImage = ''
			this.signatureList[0].signFingerImage = ''
		},
		delImg(index) {
			if (index == 0) {
				this.noRefuse = false
			}
			this.signatureList[index].signImage = ''
			this.signatureList[index].fingerImage = ''
			this.signatureList[index].signFingerImage = ''
			if (index == 1 || index == 2) {
				this.signatureList[index].signIdCard = ''
				this.signatureList[index].signUserName = ''
				if ((this.userInfo?.userType == 2 && index == 1) || (this.userInfo?.userType == 1 && index == 2)) {
					this.signatureList[index].signIdCard = this.userInfo.idCard
					this.signatureList[index].signUserName = this.userInfo.name
				}
			}
			this.signatureType = ''
		},
		handleOpen() {
			this.initSignFinger()
		},
		submit() {
			this.$emit('changePage', this.signatureList)
		},
		goHome() {
			this.$router.push('/home')
		},
		signature(item, idx) {
			if (this.signatureType == 'person' && !this.noRefuse) {
				if (!this.signatureList[0].signImage && !this.signatureList[0].fingerImage && !this.signatureList[0].signFingerImage) {
					return this.$baseTip.info('请嫌疑人签名捺印确认')
				} else if (!this.signatureList[0].signImage) {
					return this.$baseTip.info('请嫌疑人签名确认')
				} else if (!this.signatureList[0].fingerImage) {
					return this.$baseTip.info('请嫌疑人捺印确认')
				}
			}
			if (item.signFingerImage || item.signImage || (item.type == 'person' && this.noRefuse)) {
				return this.$baseTip.info('请先删除数据！')
			}
			this.signatureType = item.type
			this.signatureIndex = idx
			if (!this.config.screenSignature) {
				this.handle('onlySign')
			} else {
				this.signatureModalShow = true
			}
		},
		initSignFinger() {
			const info = this.allConfig.signFingerInfo
			const params = {
				manufacturer: info.manufacturer,
				boardType: info.boardType,
				path: info.path
			}
			this.ws.initSignFinger(params)
		},
		// 返回数据处理
		handleMessage(message) {
			const { code, msg, data, action } = message
			if (code != 0) {
				return this.$baseTip.info(data?.message || msg || '数据异常！')
			}
			const { signFingerData } = data
			switch (action) {
				case 'signOK':
					const signInfo = signFingerData[0]?.signInfo || {}
					const signImage = signInfo.signPngData ? BASE64_PREFIX + signInfo.signPngData : ''
					this.handleSignImage(signImage)
					break
				case 'fingerOK':
					this.handleFingerImage(signFingerData)
					break
				default:
					break
			}
		},
		handleSignImage(signImage) {
			this.signatureList[this.signatureIndex].signImage = signImage
			if (this.signatureType == 'person') {
				this.handle('onlyFinger')
				this.handleSignFingerImage()
			} else {
				this.signatureType = ''
			}
		},
		handleFingerImage(signFingerData) {
			if (this.signatureType != 'person') return
			const fingerInfo = signFingerData[0]?.fingerInfo || {}
			const fingerImage = fingerInfo.fingerData ? BASE64_PREFIX + fingerInfo.fingerData : ''
			this.signatureList[this.signatureIndex].fingerImage = fingerImage
			this.handleSignFingerImage()
		},
		handleSignFingerImage() {
			const { fingerImage, signImage } = this.signatureList[this.signatureIndex]
			if (fingerImage && signImage) {
				const canvas = document.createElement('canvas')
				const ctx = canvas.getContext('2d')
				const loadImage = (base64, callback) => {
					const image = new Image()
					image.src = base64
					image.onload = () => {
						callback && callback(image)
					}
				}
				loadImage(signImage, (signImg) => {
					canvas.width = signImg.width
					canvas.height = signImg.height
					ctx.drawImage(signImg, 0, 0)
					loadImage(fingerImage, (fingerImg) => {
						// 计算居中位置
						const signWidth = signImg.width
						const signHeight = signImg.height
						const fingerOrigWidth = fingerImg.width
						const fingerOrigHeight = fingerImg.height
						let scaleFactor
						if (fingerOrigWidth / signWidth > fingerOrigHeight / signHeight) {
							// 如果宽度比例更大，则按宽度缩放
							scaleFactor = signWidth / fingerOrigWidth
						} else {
							// 如果高度比例更大或相等，则按高度缩放
							scaleFactor = signHeight / fingerOrigHeight
						}
						// 计算缩小后的尺寸
						const fingerScaledWidth = fingerOrigWidth * scaleFactor
						const fingerScaledHeight = fingerOrigHeight * scaleFactor
						// 计算居中位置
						const fingerX = (signWidth - fingerScaledWidth) / 2
						const fingerY = (signHeight - fingerScaledHeight) / 2
						// 绘制缩小并居中后的 fingerImage
						ctx.drawImage(fingerImg, fingerX, fingerY, fingerScaledWidth, fingerScaledHeight)
						this.signatureList[this.signatureIndex].signFingerImage = canvas.toDataURL('image/png')
						this.signatureType = ''
					})
				})
			}
		},
		connect() {
			this.initConnect('signFinger')
		},
		handle(type) {
			if (!this.ws) {
				return this.connect()
			}
			const info = this.allConfig.signFingerInfo
			const params = {
				manufacturer: info.manufacturer,
				boardType: info.boardType,
				path: info.path
			}
			this.ws[type](params)
		}
	},
	mounted() {
		if (this.isRemote) {
			const info = this.nextUser[0]
			this.signatureList[0].personMagicId = info.personMagicId || ''
			this.signatureList[0].signIdCard = info.personIdCard || ''
			this.signatureList[0].signUserName = info.personName || ''
			this.signatureList[1].signIdCard = info.policeIdCard || ''
			this.signatureList[1].signUserName = info.policeName || ''
			this.signatureList[2].signIdCard = info.manageIdCard || ''
			this.signatureList[2].signUserName = info.manageName || ''
		} else {
			const info = this.nextUser[0]
			this.signatureList[0].personMagicId = info.userId || ''
			this.signatureList[0].signIdCard = info.idCard || ''
			this.signatureList[0].signUserName = info.userName || ''
			this.signatureList[1].signIdCard = this.userInfo.userType == 2 ? this.userInfo.idCard : ''
		}
		if (this.config.communicationMode == communicationModeDic.ONLINE) {
			this.$http.webApi.getWithsign(this.nextUser[0]?.userId).then((res) => {
				const arr = res.data.signs
				if (this.useType == 'crwp') {
					const foundObject1 = arr.find((obj) => obj.signType === 'belongs_suspect_confirm')
					if (foundObject1) {
						if (foundObject1.signPicUrl == 'no_refuse' || !foundObject1.signPicUrl) {
							// this.noRefuse = true
							this.signatureList[0].signFingerImage = ''
							this.audio = new Audio(require('../../assets/audio/signature.mp3'))
							this.audio.play()
						} else {
							this.signatureList[0].signFingerImage = BASE64_JPEGPREFIX + foundObject1.signPicUrl
						}
						this.signatureList[0].personMagicId = foundObject1.personMagicId
						this.signatureList[0].signIdCard = this.signatureList[0].signIdCard || foundObject1.signIdCard
						this.signatureList[0].signUserName = this.signatureList[0].signUserName || foundObject1.signUserName
					}
					const foundObject2 = arr.find((obj) => obj.signType === 'belongs_handle_police')
					if (foundObject2 && foundObject2.signPicUrl) {
						this.signatureList[1].signImage = BASE64_JPEGPREFIX + foundObject2.signPicUrl
						this.signatureList[1].signIdCard = this.signatureList[1].signIdCard || foundObject2.signIdCard
						this.signatureList[1].signUserName = this.signatureList[1].signUserName || foundObject2.signUserName
					}
					const foundObject3 = arr.find((obj) => obj.signType === 'belongs_manage_police')
					if (foundObject3 && foundObject3.signPicUrl) {
						this.signatureList[2].signImage = BASE64_JPEGPREFIX + foundObject3.signPicUrl
						this.signatureList[2].signIdCard = this.signatureList[2].signIdCard || foundObject3.signIdCard
						this.signatureList[2].signUserName = this.signatureList[2].signUserName || foundObject3.signUserName
					}
				} else if (this.useType == 'qcwp') {
					const foundObject1 = arr.find((obj) => obj.signType === 'belongs_reached_suspect')
					if (foundObject1) {
						if (foundObject1.signPicUrl == 'no_refuse' || !foundObject1.signPicUrl) {
							// this.noRefuse = true
							this.signatureList[0].signFingerImage = ''
							this.audio = new Audio(require('../../assets/audio/signature.mp3'))
							this.audio.play()
						} else {
							this.signatureList[0].signFingerImage = BASE64_JPEGPREFIX + foundObject1.signPicUrl
						}
						this.signatureList[0].personMagicId = foundObject1.personMagicId
						this.signatureList[0].signIdCard = this.signatureList[0].signIdCard || foundObject1.signIdCard
						this.signatureList[0].signUserName = this.signatureList[0].signUserName || foundObject1.signUserName
					}
					const foundObject2 = arr.find((obj) => obj.signType === 'belongs_consignment_handler')
					if (foundObject2 && foundObject2.signPicUrl) {
						this.signatureList[1].signImage = BASE64_JPEGPREFIX + foundObject2.signPicUrl
						this.signatureList[1].signIdCard = this.signatureList[1].signIdCard || foundObject2.signIdCard
						this.signatureList[1].signUserName = this.signatureList[1].signUserName || foundObject2.signUserName
					}
					const foundObject3 = arr.find((obj) => obj.signType === 'belongs_reached_manager')
					if (foundObject3 && foundObject3.signPicUrl) {
						this.signatureList[2].signImage = BASE64_JPEGPREFIX + foundObject3.signPicUrl
						this.signatureList[2].signIdCard = this.signatureList[2].signIdCard || foundObject3.signIdCard
						this.signatureList[2].signUserName = this.signatureList[2].signUserName || foundObject3.signUserName
					}
				}
			})
		} else {
			this.audio = new Audio(require('../../assets/audio/signature.mp3'))
			this.audio.play()
		}

		this.initConnect('signFinger')
	}
}
</script>
<style lang="less" scoped>
.signature-box {
	height: 100%;
	display: flex;
	flex-direction: column;
	padding-top: 130px;
	.signature-title {
		width: 100%;
		font-size: 42px;
		color: #2b3346;
		line-height: 66px;
		text-align: center;
		font-weight: bold;
	}
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		.content-item {
			width: 840px;
			height: 414px;
			background: #ffffff;
			box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15);
			margin: 20px 0;
			display: flex;
			justify-content: center;
			align-items: center;
			.label {
				width: 77px;
				height: 373px;
				background: #ebf2ff;
				margin-right: 16px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				color: #5779b3;
				font-size: 30px;
				text-align: center;
				.header {
					font-weight: bold;
				}
				.vertical-text {
					padding: 8px 10px;
					writing-mode: vertical-rl;
				}
			}
			.signature {
				width: 708px;
				height: 373px;
				background: #ffffff;
				border: 3px dashed #337fff;
				position: relative;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				.sort {
					width: 160px;
					height: 64px;
					background: #d9e9ff;
					border-radius: 64px;
					right: 24px;
					position: absolute;
					bottom: 27px;
					font-size: 30px;
					line-height: 64px;
					color: #3663b3;
				}
				.del {
					width: 48px;
					height: 48px;
					right: 0;
					position: absolute;
					top: 0;

					background: url('../../assets/images/signature/del.png') no-repeat;
					background-size: 100% 100%;
				}
				.img {
					width: 99px;
					height: 110px;
					background: url('../../assets/images/signature/signature.png') no-repeat;
					background-size: 100% 100%;
				}
				.text {
					font-weight: 400;
					font-size: 34px;
					color: #a9b6d4;
					line-height: 34px;
					margin-top: 48px;
				}
				.signatureing {
					font-weight: 400;
					font-size: 78px;
					color: #8fedf4;
					opacity: 0.4;
					letter-spacing: 20px;
				}
				&.selected {
					border: 5px dashed #20dbe9;
				}
			}
		}
	}
}
</style>
