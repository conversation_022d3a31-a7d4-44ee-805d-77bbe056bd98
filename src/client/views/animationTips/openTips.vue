<template>
	<div class="tip-box">
		<div class="content-title">
			<template v-if="status == 0">
				<div class="name-box">
					<span class="name">{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, getJsonNum, allConfig.lockInfo) }}</span>
					{{ $t('开关柜.柜门开启中') }}
				</div>
				<div>{{ $t('开关柜.请稍后') }}...</div>
			</template>
			<template v-if="status == 1">
				<div class="name-box">
					<span class="name">{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, getJsonNum, allConfig.lockInfo) }}</span>
					{{ $t('开关柜.柜门已开启') }}
				</div>
				<div>{{ (this.config.communicationMode == communicationModeDic.ONLINE || (userInfo && userInfo.userType == 3)) && this.config.signatureSeal ? $t('开关柜.存放物品后请签名捺印确认') : $t('开关柜.存放物品后请关好柜门') }}</div>
			</template>
		</div>
		<div class="img-box">
			<video autoplay muted>
				<source src="../../assets/images/animationTips/open.mp4" type="video/mp4" />
			</video>
			<div class="scroll-container" id="scrollContainer">
				<div class="item" :class="['item' + (idx - currentIndex), idx - currentIndex < 0 && 'hide-item']" v-for="(item, idx) in nextUser" :key="idx">{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, item.num, allConfig.lockInfo) }}</div>
			</div>
		</div>
	</div>
</template>

<script>
import { communicationModeDic } from '@/common/libs/options'
import { convertCabinetCodesToIndices } from '@/common/libs/util'
import { mapGetters } from 'vuex'
export default {
	name: 'openTips',
	props: {
		status: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			convertCabinetCodesToIndices,
			currentIndex: 0,
			scrollInterval: null,
			communicationModeDic
		}
	},
	computed: {
		...mapGetters({
			userInfo: 'user/userInfo',
			nextUser: 'app/nextUser',
			config: 'app/config',
			allConfig: 'app/allConfig'
		}),
		getJsonNum() {
			return this.nextUser.map((obj) => obj.num).join(',')
		}
	},
	methods: {
		changeScroll() {
			if (this.nextUser.length <= 4) {
				this.currentIndex = this.nextUser.length - 4
			} else {
				this.scrollInterval = setInterval(() => {
					this.currentIndex++
					if (this.nextUser.length - this.currentIndex <= 4) {
						clearInterval(this.scrollInterval)
						this.scrollInterval = null
					}
				}, 1000)
			}
		}
	},
	mounted() {
		//非管理员绑定了空柜，则关联绑定
		if (this.userInfo?.userType != '1' && this.config.communicationMode != communicationModeDic.ONLINE) {
			this.nextUser.forEach((element) => {
				if (!element.userId) {
					const params = {
						sidesName: element.num,
						userType: element.going ? element.going.userType : this.userInfo?.userType,
						userId: element.going ? element.going.id : this.userInfo?.id,
						userName: element.going ? element.going.name : this.userInfo?.name,
						associatedPoliceId: element.going ? element.going.associatedPoliceId : this.userInfo?.associatedPoliceId,
						isDeleted: 'N'
					}
					this.$http.webApi.addIscdsUserList(params).then((res) => {
						console.log(res)
					})
				}
			})
		}
		this.$nextTick(() => {
			this.changeScroll()
		})
	}
}
</script>
<style lang="less" scoped>
.tip-box {
	padding-top: 258px;
}
.content-title {
	height: 356px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	.name-box {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.name {
		color: #337fff;
		display: inline-block;
		max-width: 700px;
		font-weight: bold;
		white-space: nowrap; /* 禁止文本换行 */
		overflow: hidden; /* 隐藏超出容器的部分 */
		text-overflow: ellipsis; /* 使用省略号表示被裁剪的文本 */
	}
}
.img-box {
	width: 580px;
	height: 580px;
	margin: 0 auto;
	position: relative;
	background: #ebf5ff;
	.img-name {
		position: absolute;
		top: 210px;
		font-size: 75px;
		color: #fff199;
		width: 100%;
		text-align: center;
		z-index: 2;
	}
	.scroll-container {
		position: absolute;
		width: 100%;
		top: 60px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		height: 280px;
		color: #fff199;
		overflow: hidden;
		.item {
			transition: all 1s ease-in-out;
			width: 100%;
			text-align: center;
			top: 280px;
			position: absolute;
			height: 70px;
			font-size: 75px;
		}
		.item0 {
			height: 57px;
			font-size: 51px;
			opacity: 0.7;
			top: 0;
			position: absolute;
		}
		.item1 {
			top: 57px;
			position: absolute;
			height: 64px;
			font-size: 57px;
			opacity: 0.7;
		}
		.item2 {
			top: 121px;
			position: absolute;
			height: 74px;
			font-size: 66px;
			opacity: 0.7;
		}
		.item3 {
			top: 195px;
			position: absolute;
			height: 84px;
			font-size: 75px;
		}
		.hide-item {
			height: 70px;
			font-size: 51px;
			top: -100px;
			position: absolute;
			opacity: 0;
		}
	}
}
</style>
