<template>
	<div class="animation-tips">
		<div class="content">
			<component :status="status" :is="routeName" @changePage="changePage" @getTakePictures="getTakePictures"></component>
		</div>
		<div class="ui-button-box" v-show="showGoHome && routeName != 'Signature'">
			<ui-button type="white" v-if="routeName != 'CloseTips' || closeFlag" @click="goHome">返回首页</ui-button>
			<ui-button
				v-if="routeName == 'CloseTips'"
				@click="
					() => {
						this.config.communicationMode == communicationModeDic.ONLINE ? this.performOperations() : this.submit()
					}
				"
			>
				手动提交
			</ui-button>
		</div>
		<ui-modal v-model="showErrorModal" title="提示">
			<div class="modal-content">
				<img class="modal-img" src="../../assets/images/signature/fail.png" alt="" />
				<div class="modal-text">
					存在
					<span>{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, getNoData, allConfig.lockInfo) }}</span>
					开门失败
				</div>
			</div>
			<div class="ui-modal-footer">
				<div class="btn cancel-btn" @click="goAgain">重新开柜</div>
				<div class="btn" @click="goTips">跳过失败柜格</div>
				<div class="btn" @click="goHome">回到首页</div>
			</div>
		</ui-modal>
		<clastic-cabinet ref="clasticCabinet"></clastic-cabinet>
	</div>
</template>

<script>
import WebsocketMixin from '@/client/mixins/websocket'
import { mapGetters } from 'vuex'
import { formatDate, convertCabinetCodesToIndices } from '@/common/libs/util'
import OpenTips from './openTips'
import CloseTips from './closeTips'
import BackTips from './backTips'
import Signature from './signature'
import TakePictures from './takePictures'
import { communicationModeDic } from '@/common/libs/options'
export default {
	name: 'animationTips',
	mixins: [WebsocketMixin],
	components: {
		OpenTips,
		CloseTips,
		BackTips,
		Signature,
		TakePictures
	},
	data() {
		return {
			convertCabinetCodesToIndices,
			closeFlag: false,
			showGoHome: false,
			countdown: 60,
			timer1: null,
			timer2: null,
			status: 0,
			openTime: formatDate(new Date(), 'YYYY-MM-DD HH:mm'),
			closeTime: formatDate(new Date(), 'YYYY-MM-DD HH:mm'),
			onlineTime: '',
			routeName: '',
			checkTime: 0,
			openStatusList: [],
			lockStatuIndex: 0,
			signatureList: [{}, {}, {}],
			flagsOneArray: [],
			showErrorModal: false,
			communicationModeDic,
			takePicturesList: [],
			audio: null
		}
	},
	computed: {
		...mapGetters({
			useType: 'app/useType',
			userInfo: 'user/userInfo',
			nextUser: 'app/nextUser',
			allConfig: 'app/allConfig',
			config: 'app/config',
			goodsList: 'app/goodsList',
			propertyData: 'app/propertyData',
			isRemote: 'app/isRemote'
		}),
		getNoData() {
			const a = this.nextUser.filter((item) => {
				const lockParams = this.parseStringToArray(item.num)
				return this.flagsOneArray.some((removeItem) => lockParams.address == removeItem.address && lockParams.door == removeItem.door)
			})
			return a.map((obj) => obj.num).join(',')
		}
	},
	watch: {
		openStatusList(val) {
			if (val.length == this.nextUser.length) {
				this.openLockFlag()
			}
		}
	},
	methods: {
		getTakePictures(list) {
			const processedList = list.map((item) => this.removeBase64Prefix(item))
			this.takePicturesList = processedList
			if (this.config.signatureSeal && (this.useType == 'qcwp' || this.useType == 'crwp')) {
				this.routeName = 'Signature'
			} else {
				this.routeName = 'CloseTips'
				this.lockStatuIndex = 0
				this.getLockStatus(this.nextUser)
			}
		},
		addFault(sidesName) {
			const params = {
				sidesName,
				openTime: this.openTime,
				operatorName: this.userInfo?.name,
				userType: this.userInfo?.userType,
				operatorType: '故障'
			}
			this.$http.webApi.addAccessRecords(params)
		},
		changePage(arr) {
			this.signatureList = arr
			this.routeName = 'CloseTips'
			this.lockStatuIndex = 0
			this.getLockStatus()
		},
		handleOpen() {
			this.initLock()
		},
		// 初始化锁控
		initLock() {
			const info = this.allConfig.lockInfo.A
			const params = {}
			Object.assign(params, {
				manufacturer: info.manufacturer,
				path: info.path,
				baudRate: info.baudRate
				// manufacturer: 4,
				// path: 'ws://**************:24245/locker',
				// baudRate: 9600
			})
			this.ws.initLock(params)
		},
		parseStringToArray(input) {
			const [letter, number] = input
				.match(/([A-Z])(\d+)/)
				.slice(1)
				.map((part) => parseInt(part, 10) || part)
			const lockInfo = this.allConfig.lockInfo
			let index = 0
			let door = number
			const maxNumArr = lockInfo[letter]?.maxNum.split(',') || []
			for (let i = 0; i < maxNumArr.length; i++) {
				if (door > maxNumArr[i]) {
					door = door - maxNumArr[i]
				} else {
					index = i
					break
				}
			}
			const lockPlateArr = lockInfo[letter]?.lockPlate.split(',') || []
			const address = lockPlateArr[index]
			const result = { address, door }
			return result
		},
		// 获取柜门状态
		getLockStatus() {
			const openLockList = this.parseStringToArray(this.nextUser[this.lockStatuIndex].num)
			const params = openLockList
			this.ws && this.ws.getLockStatus(params)
		},
		// 打开柜门
		openLock() {
			const arr = []
			this.nextUser.forEach((item, index) => {
				arr[index] = this.parseStringToArray(item.num)
			})
			const params = {
				openLockList: arr
			}
			this.ws.openLock(params)
			if (this.config.openWristbandDrawer && this.$refs.clasticCabinet) {
				this.$refs.clasticCabinet.openLock()
			}
		},
		goAgain() {
			this.showErrorModal = false
			const arr = this.flagsOneArray.map((item) => ({
				address: item.address,
				door: item.door
			}))
			const params = {
				openLockList: arr
			}
			this.openStatusList = this.openStatusList.filter((item) => !this.flagsOneArray.some((removeItem) => item.address == removeItem.address && item.door == removeItem.door))
			this.ws.openLock(params)
		},
		openLockFlag() {
			this.flagsOneArray = []
			for (let i = 0; i < this.openStatusList.length; i++) {
				if (this.openStatusList[i].result != 1) {
					this.flagsOneArray.push(this.openStatusList[i])
				}
			}
			if (this.flagsOneArray.length > 0) {
				this.addFault(this.getNoData)
				this.showErrorModal = true
			} else {
				this.status = 1
				this.openTime = formatDate(new Date(), 'YYYY-MM-DD HH:mm')
				this.checkTime = 0
				this.audio = new Audio(require('../../assets/audio/open_success.mp3'))
				this.audio.play()
				setTimeout(() => {
					if (this.config.communicationMode == communicationModeDic.ONLINE && this.config.outsideTake && (this.useType == 'qcwp' || this.useType == 'lsjc')) {
						this.routeName = 'TakePictures'
					} else if ((this.userInfo?.userType == 3 || this.config.communicationMode == communicationModeDic.ONLINE) && this.config.signatureSeal && (this.useType == 'qcwp' || this.useType == 'crwp')) {
						this.routeName = 'Signature'
					} else {
						this.routeName = 'CloseTips'
						this.getLockStatus(this.nextUser)
					}
				}, 5000)
			}
		},
		updateOrCreate(arr, newData) {
			let foundIndex = -1
			for (let i = 0; i < arr.length; i++) {
				if (arr[i].address == newData.address && arr[i].door == newData.door) {
					foundIndex = i
					break
				}
			}
			if (foundIndex !== -1) {
				arr[foundIndex] = newData
			} else {
				arr.push(newData)
			}
		},
		handleMessage(message) {
			const { action, code, msg, data } = message
			if (code != 0) {
				this.showGoHome = true
				return this.$baseTip.info(data?.message || msg || '数据异常！')
			}
			switch (action) {
				case 'init':
					if (data.flag == 0) {
						this.openLock(this.nextUser)
					}
					break
				case 'openLock':
					this.updateOrCreate(this.openStatusList, data)
					break
				case 'checkLock':
					this.checkTime++
					if (this.checkTime > 15) {
						this.showGoHome = true
						if (this.checkTime % 5 === 0) {
							this.audio = new Audio(require('../../assets/audio/please_close.mp3'))
							this.audio.play()
						}
					}
					if (data.result == 0) {
						this.lockStatuIndex++
						if (this.lockStatuIndex == this.nextUser.length) {
							this.audio = new Audio(require('../../assets/audio/close_success.mp3'))
							this.audio.play()
							this.status = 2
							this.closeTime = formatDate(new Date(), 'YYYY-MM-DD HH:mm')
							this.timer1 = setTimeout(() => {
								this.config.communicationMode == communicationModeDic.ONLINE ? this.performOperations() : this.submit()
							}, 5000)
						} else {
							this.getLockStatus()
						}
					} else {
						this.timer2 = setTimeout(() => {
							this.getLockStatus()
						}, 1000)
					}
					break
				default:
					break
			}
		},
		goTips() {
			const newNextUser = this.nextUser.filter((item) => {
				const lockParams = this.parseStringToArray(item.num)
				return !this.flagsOneArray.some((removeItem) => lockParams.address == removeItem.address && lockParams.door == removeItem.door)
			})
			if (newNextUser.length) {
				this.showErrorModal = false
				this.status = 1
				this.$store.commit('app/setNextUser', newNextUser)
				if (this.config.communicationMode == communicationModeDic.ONLINE && this.config.outsideTake && (this.useType == 'qcwp' || this.useType == 'lsjc')) {
					this.routeName = 'TakePictures'
				} else if ((this.userInfo?.userType == 3 || this.config.communicationMode == communicationModeDic.ONLINE) && this.config.signatureSeal && (this.useType == 'qcwp' || this.useType == 'crwp')) {
					this.routeName = 'Signature'
				} else {
					this.routeName = 'CloseTips'
					this.getLockStatus(this.nextUser)
				}
			} else {
				this.$baseTip.info('没有开启的柜格！')
			}
		},
		async performOperations() {
			this.ws && this.ws.destroyLock()
			let status = null
			switch (this.useType) {
				case 'crwp':
					status = '1'
					break
				case 'qcwp':
					status = '2'
					break
				case 'lsjc':
					status = '3'
					break
				case 'jcgh':
					status = '4'
					break
				default:
					break
			}
			const ids = this.nextUser.map((obj) => obj.id)
			const params1 = {
				boxList: [
					{
						boxIdList: ids,
						goodList: this.goodsList,
						serialnumber: this.propertyData.mac
					}
				],
				executeType: this.userInfo?.userType == 3 ? 'local' : 'remote',
				operateTime: this.onlineTime || `${this.openTime}:00`,
				operateType: '1',
				personMagicId: this.nextUser[0].userId,
				policeIdCard: this.userInfo?.userType != 3 ? this.userInfo?.idCard || this.userInfo?.certificateNumber : '',
				personType: this.userInfo?.userType == 3 ? '1' : this.userInfo?.userType == 2 ? '0' : '2',
				status,
				goodsImgs: this.takePicturesList
			}
			const params2 = {
				boxList: [
					{
						boxIdList: ids,
						goodList: this.goodsList,
						serialnumber: this.propertyData.mac
					}
				],
				executeType: this.userInfo?.userType == 3 ? 'local' : 'remote',
				// operateTime: `${this.closeTime}:00`,
				operateType: '2',
				personMagicId: this.nextUser[0].userId,
				policeIdCard: this.userInfo?.userType != 3 ? this.userInfo?.idCard || this.userInfo?.certificateNumber : '',
				personType: this.userInfo?.userType == 3 ? '1' : this.userInfo?.userType == 2 ? '0' : '2',
				status
			}
			const params3 = (status == 1 || status == 2) && {
				// isTackOut: this.useType == 'qcwp',
				isTackOut: false,
				personMagicId: this.signatureList[0]?.personMagicId,
				signMsg: [
					{
						signImageStr: this.signatureList[0].signFingerImage ? this.removeBase64Prefix(this.signatureList[0].signFingerImage) : 'no_refuse',
						signType: this.useType == 'qcwp' ? 'belongs_reached_suspect' : 'belongs_suspect_confirm',
						signUserName: this.signatureList[0].signUserName,
						signIdCard: this.signatureList[0].signIdCard
					},
					{
						signImageStr: this.signatureList[1].signImage ? this.removeBase64Prefix(this.signatureList[1].signImage) : '',
						signType: this.useType == 'qcwp' ? 'belongs_consignment_handler' : 'belongs_handle_police',
						signUserName: this.signatureList[1].signUserName,
						signIdCard: this.signatureList[1].signIdCard
					},
					{
						signImageStr: this.signatureList[2].signImage ? this.removeBase64Prefix(this.signatureList[2].signImage) : '',
						signType: this.useType == 'qcwp' ? 'belongs_reached_manager' : 'belongs_manage_police',
						signUserName: this.signatureList[2].signUserName,
						signIdCard: this.signatureList[2].signIdCard
					}
				]
			}

			try {
				if (!this.isRemote) {
					const result1 = await this.$http.webApi.temporaryUpdateItem(params1)
					const result2 = await this.$http.webApi.temporaryUpdateItem(params2)
				} else {
					const localTimeRes = await this.$http.webApi.getLocalDateTime()
					const params4 = {
						boxList: [
							{
								boxIdList: ids,
								serialnumber: this.propertyData.mac
							}
						],
						executeType: 'remote',
						operateTime: localTimeRes.data || `${this.closeTime}:00`,
						operateType: '2',
						policeIdCard: this.nextUser[0].policeIdCard || ''
					}
					const result4 = await this.$http.webApi.postOperate(params4)
				}
				if ((status == 1 || status == 2) && this.config.signatureSeal) {
					const result3 = await this.$http.webApi.personSignMsg(params3)
				}
				this.goHome()
			} catch (error) {
				this.showGoHome = true
				this.closeFlag = true
			}
		},
		removeBase64Prefix(base64Data) {
			const prefixRegex = /^data:image\/.*;base64,/
			return base64Data.replace(prefixRegex, '')
		},
		submit() {
			this.ws && this.ws.destroyLock()
			let text = null
			switch (this.useType) {
				case 'crwp':
					text = '存入物品'
					break
				case 'qcwp':
					text = '取出物品'
					break
				case 'lsjc':
					text = '临时借出'
					break
				case 'jcgh':
					text = '借出归还'
					break
				default:
					break
			}
			const ids = this.nextUser
				.filter((obj) => obj.id)
				.map((obj) => obj.id)
				.join(',')
			if (this.useType == 'qcwp') {
				//取回的时候,触发用户柜子关联删除
				ids && this.$http.webApi.delIscdsUserList({ ids }).then(() => {})
			} else {
				//修改绑定的状态
				ids && this.$http.webApi.updateIscdsUserStatus({ id: ids, status: this.useType }).then(() => {})
			}
			//操作记录
			let userName

			if (this.nextUser && this.nextUser.length > 0 && this.nextUser[0].userName) {
				userName = this.nextUser[0].userName
			} else if (this.nextUser && this.nextUser.length > 0 && this.nextUser[0].going && this.nextUser[0].going.name) {
				userName = this.nextUser[0].going.name
			} else if (this.userInfo.userType === '3' && this.userInfo.name) {
				userName = this.userInfo.name
			} else {
				userName = ''
			}
			const nums = this.nextUser.map((obj) => obj.num).join(',')
			const params = {
				sidesName: nums,
				openTime: this.openTime,
				closeTime: this.closeTime,
				operatorName: this.userInfo?.name,
				userType: this.userInfo?.userType,
				operatorType: text,
				associatedPoliceId: this.userInfo?.associatedPoliceId || this.userInfo?.id,
				userName
			}
			if (this.signatureList && this.signatureList[0]) {
				const urlList = [this.signatureList[0].signFingerImage || 'no_refuse', this.signatureList[1]?.signImage || '', this.signatureList[2]?.signImage || '']
				const urlListString = urlList.join(',')
				params.urlList = urlListString
			}
			this.$http.webApi
				.addAccessRecords(params)
				.then(() => {
					this.goHome()
				})
				.catch(() => {
					this.showGoHome = true
				})
		},
		goHome() {
			if (this.config.openWristbandDrawer && this.$refs.clasticCabinet) {
				this.$refs.clasticCabinet.closeLock()
			}
			this.$router.push('/home')
		}
	},
	created() {
		switch (this.useType) {
			case 'qcwp':
			case 'lsjc':
			case 'jcgh':
				this.routeName = 'BackTips'
				break
			default:
				this.routeName = 'OpenTips'
				break
		}
	},
	mounted() {
		this.initConnect('lock')
		if (this.config.communicationMode == communicationModeDic.ONLINE) {
			this.$http.webApi.getLocalDateTime().then((res) => {
				this.onlineTime = res.data
			})
		}
	},
	destroyed() {
		this.timer1 && clearTimeout(this.timer1)
		this.timer2 && clearTimeout(this.timer2)
	}
}
</script>
<style lang="less" scoped>
.animation-tips {
	flex: 1;
	display: flex;
	flex-direction: column;
	.content {
		flex: 1;
		text-align: center;
		font-size: 40px;
		color: #2b3346;
		line-height: 60px;
		.img-box {
			width: 580px;
			height: 580px;
			margin: 0 auto;
			position: relative;
			background: #ebf5ff;
			.img-name {
				position: absolute;
				top: 210px;
				font-size: 75px;
				color: #fff199;
				width: 100%;
				text-align: center;
				z-index: 2;
			}
		}
	}
}
.modal-content {
	height: 600px;
	margin-bottom: 44px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	.modal-img {
		width: 200px;
		height: 200px;
		margin-bottom: 50px;
	}
	.modal-text {
		line-height: 50px;
		color: #2b3346;
		font-size: 42px;
		max-width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		span {
			max-width: 560px;
			white-space: nowrap; /* 禁止文本换行 */
			overflow: hidden; /* 隐藏超出容器的部分 */
			text-overflow: ellipsis; /* 使用省略号表示被裁剪的文本 */
		}
	}
}
</style>
