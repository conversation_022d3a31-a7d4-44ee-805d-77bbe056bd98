<template>
	<div class="animation-tips">
		<div class="content">
			<div class="tip-box">
				<div class="content-title">
					<template v-if="status == 0">
						<div class="name-box">
							<span class="name">{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, getJsonNum, allConfig.lockInfo) }}</span>
							柜门开启中
						</div>
						<div>请稍后...</div>
					</template>
					<template v-if="status == 1">
						<div class="name-box">
							<span class="name">{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, getJsonNum, allConfig.lockInfo) }}</span>
							柜门已开启
						</div>
						<div>请关好柜门</div>
					</template>
					<template v-if="status == 2">
						<div class="name-box">
							<span class="name">{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, getJsonNum, allConfig.lockInfo) }}</span>
							柜门已关闭
						</div>
					</template>
				</div>
				<div class="img-box" v-if="status == 0 || status == 1">
					<video autoplay muted>
						<source src="../../assets/images/animationTips/open.mp4" type="video/mp4" />
					</video>
					<div class="scroll-container" id="scrollContainer">
						<div class="item" :class="['item' + (idx - currentIndex), idx - currentIndex < 0 && 'hide-item']" v-for="(item, idx) in lookList" :key="idx">{{ item }}</div>
					</div>
				</div>
				<div class="img-box" v-if="status == 2">
					<video autoplay muted :key="status">
						<source src="../../assets/images/animationTips/close.mp4" type="video/mp4" />
					</video>
				</div>
			</div>
		</div>
		<div class="ui-button-box" v-show="showGoHome">
			<ui-button type="white" @click="goHome">返回首页</ui-button>
			<ui-button v-if="status == 2" @click="submit">手动提交</ui-button>
		</div>
		<ui-modal v-model="showErrorModal" title="提示">
			<div class="modal-content">
				<img class="modal-img" src="../../assets/images/signature/fail.png" alt="" />
				<div class="modal-text">
					存在
					<span>{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, getNoData, allConfig.lockInfo) }}</span>
					开门失败
				</div>
			</div>
			<div class="ui-modal-footer">
				<div class="btn cancel-btn" @click="goAgain">重新开柜</div>
				<div class="btn" @click="goTips">跳过失败柜格</div>
				<div class="btn" @click="goHome">回到首页</div>
			</div>
		</ui-modal>
	</div>
</template>

<script>
import WebsocketMixin from '@/client/mixins/websocket'
import { mapGetters } from 'vuex'
import { formatDate, convertCabinetCodesToIndices } from '@/common/libs/util'

export default {
	name: 'onlyOpenAnimationTips',
	mixins: [WebsocketMixin],
	data() {
		return {
			convertCabinetCodesToIndices,
			status: 0,
			showErrorModal: false,
			audio: null,
			lookList: null,
			flagsOneArray: [],
			openStatusList: [],
			lockStatuIndex: 0,
			showGoHome: false,
			currentIndex: 0,
			queryList: [],
			queryLength: 0,
			ids: [],
			policeIdCard: null,
			closeTime: formatDate(new Date(), 'YYYY-MM-DD HH:mm')
		}
	},
	computed: {
		...mapGetters({
			allConfig: 'app/allConfig',
			propertyData: 'app/propertyData'
		}),
		getNoData() {
			const a = this.lookList.filter((item) => {
				const lockParams = this.parseStringToArray(item)
				return this.flagsOneArray.some((removeItem) => lockParams.address == removeItem.address && lockParams.door == removeItem.door)
			})
			return a.map((obj) => obj).join(',')
		},
		getJsonNum() {
			return this.lookList.map((obj) => obj).join(',')
		}
	},
	watch: {
		openStatusList(val) {
			if (val.length == this.lookList.length) {
				this.openLockFlag()
			}
		}
	},
	methods: {
		changeQueryList(data) {
			if (data.result != 0) {
				return false
			}
			this.checkTime = 0
			for (let i = 0; i < this.queryList.length; i++) {
				if (this.queryList[i].address == data.address && this.queryList[i].door == data.door) {
					this.queryList.splice(i, 1)
					if (!this.queryList.length) {
						this.audio = new Audio(require('../../assets/audio/close_success.mp3'))
						this.audio.play()
						this.status = 2
						this.timer1 = setTimeout(() => {
							this.closeTime = formatDate(new Date(), 'YYYY-MM-DD HH:mm')
							this.submit()
						}, 5000)
						return false
					}
					return false
				}
			}
		},
		handleOpen() {
			this.initLock()
		},
		// 初始化锁控
		initLock() {
			const info = this.allConfig.lockInfo.A
			const params = {}
			Object.assign(params, {
				manufacturer: info.manufacturer,
				path: info.path,
				baudRate: info.baudRate
				// manufacturer: 4,
				// path: 'ws://**************:24245/locker',
				// baudRate: 9600
			})
			this.ws.initLock(params)
		},
		parseStringToArray(input) {
			const [letter, number] = input
				.match(/([A-Z])(\d+)/)
				.slice(1)
				.map((part) => parseInt(part, 10) || part)
			const lockInfo = this.allConfig.lockInfo
			let index = 0
			let door = number
			const maxNumArr = lockInfo[letter]?.maxNum.split(',') || []
			for (let i = 0; i < maxNumArr.length; i++) {
				if (door > maxNumArr[i]) {
					door = door - maxNumArr[i]
				} else {
					index = i
					break
				}
			}
			const lockPlateArr = lockInfo[letter]?.lockPlate.split(',') || []
			const address = lockPlateArr[index]
			const result = { address, door }
			return result
		},
		// 获取柜门状态
		getLockStatus() {
			this.queryList.forEach((item) => {
				this.ws && this.ws.getLockStatus(item)
			})
		},
		// 打开柜门
		openLock() {
			const arr = []
			this.lookList.forEach((item, index) => {
				arr[index] = this.parseStringToArray(item)
			})
			this.queryList = arr
			this.queryLength = arr.length
			const params = {
				openLockList: arr
			}
			this.ws.openLock(params)
		},
		goAgain() {
			this.showErrorModal = false
			const arr = this.flagsOneArray.map((item) => ({
				address: item.address,
				door: item.door
			}))
			const params = {
				openLockList: arr
			}
			this.openStatusList = this.openStatusList.filter((item) => !this.flagsOneArray.some((removeItem) => item.address == removeItem.address && item.door == removeItem.door))
			this.ws.openLock(params)
		},
		openLockFlag() {
			this.flagsOneArray = []
			for (let i = 0; i < this.openStatusList.length; i++) {
				if (this.openStatusList[i].result != 1) {
					this.flagsOneArray.push(this.openStatusList[i])
				}
			}
			if (this.flagsOneArray.length > 0) {
				this.showErrorModal = true
			} else {
				this.status = 1
				this.checkTime = 0
				this.audio = new Audio(require('../../assets/audio/open_success.mp3'))
				this.audio.play()
				this.timer3 = setInterval(() => {
					this.checkTime++
					if (this.checkTime > 15) {
						this.showGoHome = true
						if (this.checkTime % 5 === 0) {
							this.audio = new Audio(require('../../assets/audio/please_close.mp3'))
							this.audio.play()
						}
					}
				}, 1000)
				setTimeout(() => {
					this.getLockStatus()
				}, 5000)
			}
		},
		updateOrCreate(arr, newData) {
			let foundIndex = -1
			for (let i = 0; i < arr.length; i++) {
				if (arr[i].address == newData.address && arr[i].door == newData.door) {
					foundIndex = i
					break
				}
			}
			if (foundIndex !== -1) {
				arr[foundIndex] = newData
			} else {
				arr.push(newData)
			}
		},
		handleMessage(message) {
			const { action, code, msg, data } = message
			if (code != 0) {
				this.showGoHome = true
				return this.$baseTip.info(data?.message || msg || '数据异常！')
			}
			switch (action) {
				case 'init':
					if (data.flag == 0) {
						this.openLock(this.lookList)
					}
					break
				case 'openLock':
					this.updateOrCreate(this.openStatusList, data)
					break
				case 'checkLock':
					this.lockStatuIndex++
					this.changeQueryList(data)
					if (this.lockStatuIndex == this.queryLength) {
						this.queryLength = this.queryList.length
						this.lockStatuIndex = 0
						this.getLockStatus()
					}
					break
				default:
					break
			}
		},
		goTips() {
			const newNextUser = this.lookList.filter((item) => {
				const lockParams = this.parseStringToArray(item)
				return !this.flagsOneArray.some((removeItem) => lockParams.address == removeItem.address && lockParams.door == removeItem.door)
			})
			if (newNextUser.length) {
				this.showErrorModal = false
				this.status = 1
				this.getLockStatus()
			} else {
				this.$baseTip.info('没有开启的柜格！')
			}
		},
		async submit() {
			this.ws && this.ws.destroyLock()
			if (this.policeIdCard) {
				const localTimeRes = await this.$http.webApi.getLocalDateTime()
				const params4 = {
					boxList: [
						{
							boxIdList: this.ids,
							serialnumber: this.propertyData.mac
						}
					],
					executeType: 'remote',
					operateTime: localTimeRes.data || `${this.closeTime}:00`,
					operateType: '2',
					policeIdCard: this.policeIdCard
				}
				const result4 = await this.$http.webApi.postOperate(params4)
			}

			this.$router.push('/home')
		},
		goHome() {
			this.ws && this.ws.destroyLock()
			this.$router.push('/home')
		},
		changeScroll() {
			if (this.lookList.length <= 4) {
				this.currentIndex = this.lookList.length - 4
			} else {
				this.scrollInterval = setInterval(() => {
					this.currentIndex++
					if (this.lookList.length - this.currentIndex <= 4) {
						clearInterval(this.scrollInterval)
						this.scrollInterval = null
					}
				}, 1000)
			}
		}
	},
	created() {
		this.lookList = this.$route.query.openLockList
		this.policeIdCard = this.$route.query.policeIdCard
		this.ids = this.$route.query.ids
		this.$nextTick(() => {
			this.changeScroll()
		})
	},
	mounted() {
		this.initConnect('lock')
	},
	destroyed() {
		this.timer1 && clearTimeout(this.timer1)
		this.timer3 && clearTimeout(this.timer3)
	}
}
</script>
<style lang="less" scoped>
.animation-tips {
	flex: 1;
	display: flex;
	flex-direction: column;
	.content {
		flex: 1;
		text-align: center;
		font-size: 40px;
		color: #2b3346;
		line-height: 60px;
		.img-box {
			width: 580px;
			height: 580px;
			margin: 0 auto;
			position: relative;
			background: #ebf5ff;
			.img-name {
				position: absolute;
				top: 210px;
				font-size: 75px;
				color: #fff199;
				width: 100%;
				text-align: center;
				z-index: 2;
			}
		}
	}
}
.tip-box {
	padding-top: 258px;
}
.content-title {
	height: 356px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	.name-box {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.name {
		color: #337fff;
		display: inline-block;
		max-width: 700px;
		font-weight: bold;
		white-space: nowrap; /* 禁止文本换行 */
		overflow: hidden; /* 隐藏超出容器的部分 */
		text-overflow: ellipsis; /* 使用省略号表示被裁剪的文本 */
	}
}
.modal-content {
	height: 600px;
	margin-bottom: 44px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	.modal-img {
		width: 200px;
		height: 200px;
		margin-bottom: 50px;
	}
	.modal-text {
		line-height: 50px;
		color: #2b3346;
		font-size: 42px;
		max-width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		span {
			max-width: 560px;
			white-space: nowrap; /* 禁止文本换行 */
			overflow: hidden; /* 隐藏超出容器的部分 */
			text-overflow: ellipsis; /* 使用省略号表示被裁剪的文本 */
		}
	}
}
.img-box {
	width: 580px;
	height: 580px;
	margin: 0 auto;
	position: relative;
	background: #ebf5ff;
	.img-name {
		position: absolute;
		top: 210px;
		font-size: 75px;
		color: #fff199;
		width: 100%;
		text-align: center;
		z-index: 2;
	}
	.scroll-container {
		position: absolute;
		width: 100%;
		top: 60px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		height: 280px;
		color: #fff199;
		overflow: hidden;
		.item {
			transition: all 1s ease-in-out;
			width: 100%;
			text-align: center;
			top: 280px;
			position: absolute;
			height: 70px;
			font-size: 75px;
		}
		.item0 {
			height: 57px;
			font-size: 51px;
			opacity: 0.7;
			top: 0;
			position: absolute;
		}
		.item1 {
			top: 57px;
			position: absolute;
			height: 64px;
			font-size: 57px;
			opacity: 0.7;
		}
		.item2 {
			top: 121px;
			position: absolute;
			height: 74px;
			font-size: 66px;
			opacity: 0.7;
		}
		.item3 {
			top: 195px;
			position: absolute;
			height: 84px;
			font-size: 75px;
		}
		.hide-item {
			height: 70px;
			font-size: 51px;
			top: -100px;
			position: absolute;
			opacity: 0;
		}
	}
}
</style>
