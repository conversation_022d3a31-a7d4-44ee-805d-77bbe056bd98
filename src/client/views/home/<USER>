<template>
	<div class="police-home">
		<div class="title">
			<div class="title-item">
				总量：
				<span class="value">{{ statisticsObj.allNum }}</span>
			</div>
			<div class="title-item free">空闲：({{ statisticsObj.freeNum }})</div>
			<div class="title-item user">占用：({{ statisticsObj.userNum }})</div>
			<div class="title-item fault">不可用：({{ statisticsObj.faultNum }})</div>
		</div>
		<div class="table-box">
			<ui-carousel-box :data="tableData" :contentWidth="870" :conditionTop="55" :isReverse="config.mainScreenType == '3'">
				<template slot-scope="{ data }">
					<cabinet-table :tableObj="data" :tableUserData="tableUserData" @selected="selectIscds" size="big"></cabinet-table>
				</template>
			</ui-carousel-box>
		</div>
		<div class="description">
			<div class="synchronize" v-if="config.communicationMode != communicationModeDic.ONLINE && config.mainScreenType != 1" @click="synchronize()">同步对端数据</div>
		</div>
		<ui-loading v-model="showLoading">正在同步中请稍后！</ui-loading>
	</div>
</template>

<script>
import { communicationModeDic } from '@/common/libs/options'
import { mapGetters } from 'vuex'
export default {
	name: 'policeHome',
	data() {
		return {
			statisticsObj: {},
			tableData: [],
			tableUserData: [],
			communicationModeDic,
			showLoading: false
		}
	},
	computed: {
		...mapGetters({
			config: 'app/config'
		})
	},
	methods: {
		synchronize() {
			this.showLoading = true
			setTimeout(() => {
				this.showLoading = false
				this.timer1 && clearTimeout(this.timer1)
				this.timer1 = null
				this.timer2 && clearTimeout(this.timer2)
				this.timer2 = null
				this.getSidesAllList()
				this.getIscdsUserList()
				this.getIscdsUserNum()
			}, 20 * 1000)
			this.$http.appApi.synchronizeAllData()
		},
		selectIscds(info) {
			this.$store.commit('app/setNextUser', [info])
			this.$store.commit('app/setNextRouter', '/animationTips')
			if (this.config.loginMethod.includes('2')) {
				this.$router.push('verifyHome')
			} else {
				this.$router.push('verifyAccount')
			}
		},
		getSidesAllList() {
			this.$http.appApi.getSidesAllList().then((res) => {
				this.tableData = res.data
			})
		},
		getIscdsUserList() {
			this.$http.webApi.getIscdsUserList().then((res) => {
				this.tableUserData = res.data
				if (this.config.communicationMode != this.communicationModeDic.ONLINE && this.config.mainScreenType != 1) {
					this.timer1 = setTimeout(() => {
						this.getIscdsUserList()
					}, 60 * 1000)
				}
			})
		},
		getIscdsUserNum() {
			this.$http.webApi.getIscdsUserNum().then((res) => {
				this.statisticsObj = res.data || {}
				if (this.config.communicationMode != this.communicationModeDic.ONLINE && this.config.mainScreenType != 1) {
					this.timer2 = setTimeout(() => {
						this.getIscdsUserNum()
					}, 60 * 1000)
				}
			})
		},
		getData() {
			this.getSidesAllList()
			this.getIscdsUserList()
			this.getIscdsUserNum()
		}
	},
	beforeDestroy() {
		this.timer1 && clearTimeout(this.timer1)
		this.timer1 = null
		this.timer2 && clearTimeout(this.timer2)
		this.timer2 = null
	},
	created() {
		this.$store.commit('app/setUseType', '')
		this.$store.commit('user/removeUserInfo')
		this.$store.commit('app/setNextUser', [])
		this.getData()
	}
}
</script>
<style scoped lang="less">
.police-home {
	flex: 1;
	display: flex;
	flex-direction: column;
	padding-top: 143px;
	position: relative;
	.title {
		margin: 0 auto;
		height: 72px;
		width: 822px;
		background: #fff;
		box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15), inset 0px 3px 3px 1px rgba(48, 116, 218, 0.1);
		display: flex;
		align-items: center;
		.title-item {
			flex: 1;
			font-size: 26px;
			color: #64759a;
			display: flex;
			align-items: center;
			justify-content: center;
			&.free {
				color: #00d18b;
			}
			&.user {
				color: #445bf3;
			}
			&.fault {
				color: #e60050;
			}
			.value {
				font-size: 36px;
				color: #2b4d71;
			}
		}
	}
	.table-box {
		flex: 1;
		padding-top: 22px;
	}
	.description {
		height: 22px;
		position: absolute;
		width: 100%;
		bottom: 18px;
		text-align: center;
		font-weight: 400;
		font-size: 22px;
		color: #2b5fda;
		padding: 0 40px;
		.synchronize {
			position: absolute;
			top: 0;
			right: 40px;
			height: 22px;
			width: 140px;
		}
	}
}
</style>
