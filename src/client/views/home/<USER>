<template>
	<div class="home-wrapper">
		<div class="home-top">
			<div class="title">{{ config.placeName }}</div>
			<div class="name">{{ operationInfo.terminalName }}</div>
		</div>
		<div class="statistics-box">
			<div class="statistics-item" v-for="(item, idx) in statisticsList" :key="idx" @click="toStorageSituation()">
				<img :src="item.icon" />
				<div class="label">{{ $t(`统计.${item.label}`) }}</div>
				<div class="value">{{ item.value }}</div>
			</div>
		</div>
		<div class="menu-box">
			<div class="menu-item" v-for="(item, idx) in filteredMenuList" :key="idx" :class="item.className" :style="{ backgroundImage: `url(${item.bg})` }" @click="toPage(item)">
				<div :class="['text', $i18n.locale == 'en' && 'text-english']">{{ item.name }}</div>
			</div>
		</div>
		<div class="description">
			{{ operationInfo.copyright }}
			<div class="synchronize" v-if="config.communicationMode != communicationModeDic.ONLINE && config.mainScreenType != 1" @click="synchronize()">同步对端数据</div>
		</div>
		<ui-loading v-model="showLoading">正在同步中请稍后！</ui-loading>
	</div>
</template>

<script>
import { communicationModeDic } from '@/common/libs/options'
import { mapGetters } from 'vuex'
export default {
	name: 'otherHome',
	data() {
		return {
			showLoading: false,
			statisticsList: [
				{ languageKey: 'total', label: '总量', value: 0, key: 'allNum', icon: require('../../assets/images/home/<USER>') },
				{ languageKey: 'free', label: '空闲', value: 0, key: 'freeNum', icon: require('../../assets/images/home/<USER>') },
				{ languageKey: 'occupy', label: '占用', value: 0, key: 'userNum', icon: require('../../assets/images/home/<USER>') },
				{ languageKey: 'fault', label: '不可用', value: 0, key: 'faultNum', icon: require('../../assets/images/home/<USER>') }
			],
			menuList: [
				{ languageKey: 'depositItems', key: 'crwp', name: '存入物品', onlinePath: '/selectItems', offlinePath: '/storageSituation', className: 'menu-item1', bg: require('../../assets/images/home/<USER>'), type: 'other' },
				{ languageKey: 'retrieveItems', key: 'qcwp', name: '取出物品', onlinePath: '/selectItems', offlinePath: '/storageSituation', className: 'menu-item1', bg: require('../../assets/images/home/<USER>'), type: 'other' },
				{ languageKey: 'depositItems', key: 'crwp', name: '存入物品', onlinePath: '/selectItems', offlinePath: '/storageSituation', className: 'menu-item3', bg: require('../../assets/images/home/<USER>'), type: 'main' },
				{ languageKey: 'retrieveItems', key: 'qcwp', name: '取出物品', onlinePath: '/selectItems', offlinePath: '/storageSituation', className: 'menu-item3', bg: require('../../assets/images/home/<USER>'), type: 'assistant' },
				{ languageKey: 'temporaryLending', key: 'lsjc', name: '临时借出', onlinePath: '/selectItems', offlinePath: '/storageSituation', className: 'menu-item2', bg: require('../../assets/images/home/<USER>') },
				{ languageKey: 'lendAndReturn', key: 'jcgh', name: '借出归还', onlinePath: '/selectItems', offlinePath: '/storageSituation', className: 'menu-item2', bg: require('../../assets/images/home/<USER>') },
				{ languageKey: 'locater', key: 'locater', name: '定位设备归还', onlinePath: '/locater', offlinePath: '/storageSituation', className: 'menu-item2', bg: require('../../assets/images/home/<USER>') }
			],
			timer: null,
			operationInfo: {
				terminalName: '',
				copyright: ''
			},
			communicationModeDic
		}
	},
	computed: {
		...mapGetters({
			allConfig: 'app/allConfig',
			config: 'app/config',
			propertyData: 'app/propertyData',
			token: 'user/token'
		}),
		filteredMenuList() {
			const excludeTypes = (screenType) => {
				switch (screenType) {
					case '2':
						return ['assistant', 'other']
					case '3':
						return ['main', 'other']
					default:
						return ['main', 'assistant']
				}
			}
			let arr = this.menuList
			if (this.config.communicationMode != communicationModeDic.ONLINE || !this.config.locaterReturn) {
				arr = arr.filter((item) => item.key != 'locater')
			}
			const typesToExclude = excludeTypes(this.config.mainScreenType)
			return arr.filter((item) => !typesToExclude.includes(item.type))
		},
		getDataFlag() {
			return (this.token && this.propertyData?.mac) || this.config.communicationMode != communicationModeDic.ONLINE
		}
	},
	watch: {
		allConfig(val) {
			this.operationInfo = val.operationInfo
		},
		getDataFlag: {
			handler(newVal, oldVal) {
				if (newVal) {
					this.timer && clearTimeout(this.timer)
					this.timer = null
					this.getData()
				}
			},
			immediate: true
		}
	},
	methods: {
		toStorageSituation() {
			this.$store.commit('app/setNextRouter', '/storageSituationDetail')
			if (this.config.loginMethod.includes('2')) {
				this.$router.push('verifyHome')
			} else {
				this.$router.push('verifyAccount')
			}
		},
		toPage(item) {
			if (item.key == 'locater') {
				return this.$router.push('locater')
			}
			const path = this.config.communicationMode == communicationModeDic.ONLINE ? item.onlinePath : item.offlinePath
			this.$store.commit('app/setNextRouter', path)
			this.$store.commit('app/setUseType', item.key)
			if (this.config.loginMethod.includes('2')) {
				this.$router.push('verifyHome')
			} else {
				this.$router.push('verifyAccount')
			}
		},
		getData() {
			this.$http.webApi.getIscdsUserNum().then((res) => {
				const data = res.data || {}
				this.statisticsList.forEach((item) => {
					item.value = data[item.key]
				})
				this.timer = setTimeout(() => {
					this.getData()
				}, 60 * 1000)
			})
		},
		synchronize() {
			this.showLoading = true
			setTimeout(() => {
				this.showLoading = false
				this.timer && clearTimeout(this.timer)
				this.timer = null
				this.getData()
			}, 20 * 1000)
			this.$http.appApi.synchronizeAllData()
		}
	},
	beforeDestroy() {
		this.timer && clearTimeout(this.timer)
		this.timer = null
	}
}
</script>
<style scoped lang="less">
.home-wrapper {
	width: 100%;
	height: calc(100% - 100px);
	padding: 0 100px;
	position: relative;
	background: url('../../assets/images/home/<USER>') no-repeat;
	background-size: cover;
	background-position: center -100px;
	box-sizing: border-box;
	.home-top {
		padding-top: 77px;
		padding-bottom: 110px;
		.name {
			font-weight: bold;
			font-size: 62px;
			color: #042767;
			line-height: 94px;
			height: 94px;
		}
		.title {
			font-weight: bold;
			font-size: 40px;
			color: #042767;
			line-height: 72px;
			height: 72px;
		}
	}
	.statistics-box {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		.statistics-item {
			width: 420px;
			height: 119px;
			background: linear-gradient(180deg, #ffffff 0%, #e6f3ff 100%);
			display: flex;
			align-items: center;
			margin-bottom: 30px;
			padding-right: 45px;
			padding-left: 22px;
			img {
				width: 116px;
				height: 116px;
			}
			.label {
				font-size: 32px;
				color: #64759a;

				flex: 1;
			}
			.value {
				font-size: 48px;
				color: #2b4d71;
			}
		}
	}
	.menu-box {
		display: flex;
		flex-wrap: wrap;
		padding-top: 30px;
		.menu-item1 + .menu-item1 {
			margin-left: 40px;
		}
		.menu-item1 {
			width: 420px;
			height: 420px;
			background-size: 100% 100%;
			background-repeat: no-repeat;
			margin-bottom: 40px;

			.text {
				font-size: 54px;
				color: #ffffff;
				line-height: 150px;
				letter-spacing: 10px;
				padding-left: 60px;
				font-weight: bold;
			}
		}
		.menu-item2 {
			width: 880px;
			height: 172px;
			background-size: 100% 100%;
			background-repeat: no-repeat;
			margin-bottom: 40px;
			.text {
				font-size: 54px;
				color: #ffffff;
				line-height: 172px;
				letter-spacing: 30px;
				padding-left: 60px;
				font-weight: bold;
			}
		}
		.menu-item3 {
			width: 880px;
			height: 300px;
			background-size: 100% 100%;
			background-repeat: no-repeat;
			margin-bottom: 40px;
			.text {
				font-size: 54px;
				color: #ffffff;
				line-height: 200px;
				letter-spacing: 30px;
				padding-left: 60px;
				font-weight: bold;
			}
		}
	}
	.description {
		height: 22px;
		position: absolute;
		width: 880px;
		bottom: 18px;
		text-align: center;
		font-weight: 400;
		font-size: 22px;
		color: #2b5fda;
		padding: 0 40px;
		.synchronize {
			position: absolute;
			top: 0;
			right: -100px;
			height: 22px;
			width: 140px;
		}
	}
}
</style>
