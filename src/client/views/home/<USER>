<template>
	<component :is="currentComponent"></component>
</template>

<script>
import { mapGetters } from 'vuex'
import OtherHome from './otherHome.vue'
import PoliceHome from './policeHome.vue'
import { communicationModeDic } from '@/common/libs/options'
export default {
	name: 'home',
	computed: {
		...mapGetters({
			config: 'app/config',
			propertyData: 'app/propertyData',
			token: 'user/token'
		}),
		currentComponent() {
			return this.config.communicationMode != communicationModeDic.ONLINE && this.config.userType === '1' ? PoliceHome : OtherHome
		}
	},
	created() {
		this.$store.commit('app/setUseType', '')
		this.$store.commit('app/setNextUser', [])
		this.$store.commit('user/removeUserInfo')
		this.$store.commit('app/setGoodsList', [])
		this.$store.commit('app/setIsRemote', false)
		this.init()
	},
	methods: {
		init() {
			const arr = [this.$http.appApi.getConfigInfo(), this.$http.appApi.getSystemProperties({ searchProps: 'ip,mac,version,sysArch' })]
			Promise.all(arr).then(([res1, res2]) => {
				this.allInfo = res1.data || {}
				const { businessInfo = {}, language = 'zh' } = this.allInfo
				this.$store.commit('app/setConfig', businessInfo)
				this.$store.commit('app/setAllConfig', this.allInfo)
				this.$i18n.locale = language
				if (businessInfo.communicationMode == communicationModeDic.ONLINE && !businessInfo.thirdFlag) {
					this.getClientCredential()
				}
				const oldMac = res2.data.mac
				this.$store.commit('app/setPropertyData', {
					...res2.data,
					oldMac,
					// mac: 'da:03:a5:b9:1d:62'
					mac: businessInfo.mainScreenType == '3' ? businessInfo.mainSerialNumber : res2.data.mac
				})
			})
		},
		postBoxConfig() {
			this.$http.webApi.postBoxConfig({ serialNumber: this.propertyData.mac }).then((res) => {
				this.$store.commit('app/setOnlineConfig', res.data)
			})
		},
		getClientCredential() {
			this.$http.webApi.getClientCredential().then((res) => {
				this.$store.commit('user/setToken', res.data)
				this.postBoxConfig()
			})
		}
	}
}
</script>
