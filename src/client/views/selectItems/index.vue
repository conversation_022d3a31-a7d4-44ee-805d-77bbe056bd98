<template>
	<div>
		<div v-show="status == '1'"></div>
		<div class="person-box" v-show="status == '2'">
			<div class="card-box">
				<div class="content">
					<div :class="['card-item', item.select && 'selected']" v-for="(item, idx) in personList" :key="idx" @click="suspectUse(item.magicId)">
						<div class="card-content">
							<img class="img" :src="item.faceImgUrl ? 'data:image/jpeg;base64,' + item.faceImgUrl : personImg" alt="" />
							<div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>
					</div>
					<ui-no-data v-if="!personList.length"></ui-no-data>
				</div>
			</div>
		</div>
		<div class="good-box" v-show="status == '3'">
			<div class="card-box">
				<div class="content">
					<div :class="['card-item', item.select && 'selected']" v-for="(item, idx) in goodList" :key="idx" @click="selectCard(item)">
						<div class="icon"></div>
						<div class="card-content">
							<img class="picture-img" :src="item.pictureUrl ? 'data:image/jpeg;base64,' + item.pictureUrl : pictureImg" alt="" />
							<div>
								<div class="name">{{ item.belongingsName }}</div>
								<div class="register-time">保管措施:{{ item.measureCn }}</div>
							</div>
						</div>
						<div class="btn">{{ item.cabinetNo }}</div>
					</div>
					<ui-no-data v-if="!goodList.length"></ui-no-data>
				</div>
			</div>
			<div class="del-box">
				<div class="select-box" @click="toggleSelectAll()">
					<div :class="['icon', isAllSelected && 'selected']"></div>
					<div class="text">全选</div>
				</div>
				<div class="btn-box">
					<div class="text">
						已选
						<span>{{ selectedList.length }}</span>
						件
					</div>
					<div class="btn" :class="{ disabled: selectedList.length == 0 }" @click="selectedList.length > 0 ? temporary() : null">{{ useType == 'jcgh' ? '归还' : '取出' }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
	name: 'selectItems',
	data() {
		return {
			status: '1',
			personList: [],
			personImg: require('../../assets/images/settings/person.png'),
			pictureImg: require('../../assets/images/settings/picture.png'),
			goodList: [],
			useTypeObj: {
				crwp: '1',
				qcwp: '2',
				lsjc: '3',
				jcgh: '4'
			},
			personMagicId: null
		}
	},
	computed: {
		...mapGetters({
			useType: 'app/useType',
			userInfo: 'user/userInfo',
			config: 'app/config'
		}),
		selectedList() {
			return this.goodList.filter((card) => card.select)
		},
		isAllSelected() {
			return this.goodList.every((card) => card.select)
		}
	},
	methods: {
		operationTipA(text) {
			text && this.$baseTip.info(text)
			if (this.userInfo.userType == '3' && ['crwp', 'qcwp'].includes(this.useType)) {
				this.$router.push('/home')
			}
		},
		operationTipB(text) {
			text && this.$baseTip.info(text)
			if (this.userInfo.userType == '3') {
				this.$router.push('/home')
			}
		},
		async temporary() {
			const boxIds = this.selectedList.map((item) => item.boxId).join(',')
			const idsArr = this.selectedList.map((item) => item.magicId)
			const { data = [] } = await this.$http.webApi.getIscdsUserList()
			const arr = data.filter((item) => boxIds.includes(item.id))
			if (!arr.length) {
				this.$baseTip.info(`未查询到存在关联数据!`)
				return false
			}
			const params = {
				personMagicId: this.personMagicId,
				boxIdList: arr.map((item) => item.id),
				status: this.useTypeObj[this.useType]
			}
			this.$store.commit('app/setGoodsList', idsArr)
			this.$store.commit('app/setNextUser', arr)
			await this.isCanOpenDoor(params)
		},
		toggleSelectAll() {
			const allSelected = this.isAllSelected
			this.goodList.forEach((card) => {
				this.$set(card, 'select', !allSelected)
			})
		},
		selectCard(item) {
			this.$set(item, 'select', !item.select)
		},
		async isCanOpenDoor(params) {
			try {
				await this.$http.webApi.isCanOpenDoor(params)
				this.$router.push('animationTips')
			} catch (err) {
				this.operationTipA()
			}
		},
		async getGoods() {
			try {
				this.status = this.userInfo.userType == '3' ? '1' : '2'
				const { data = [] } = await this.$http.webApi.findPersongoods({ personMagicId: this.personMagicId, statusList: this.useType == 'lsjc' ? ['1', '4'] : this.useType == 'jcgh' ? ['3'] : [] })
				this.goodList = data.map((item) => {
					return { ...item, select: true }
				})
				if (this.useType == 'qcwp') {
					this.goodList = this.goodList.filter((item) => item.getWay === 'own_take')
				}
				if (this.useType == 'crwp') {
					this.goodList = this.goodList.filter((item) => item.status != '3')
				}
				const ids = this.goodList.map((item) => item.magicId)
				if (!ids.length) {
					let text = '未查询到存在关联数据!'
					if (this.useType == 'jcgh') {
						text = '未查询到存在临时借出记录!'
					}
					throw new Error(text)
				}
				if (!['crwp', 'qcwp'].includes(this.useType)) {
					this.status = '3'
					return false
				}
				this.$store.commit('app/setGoodsList', ids)
				return true
			} catch (err) {
				this.operationTipB(err.message)
				return false
			}
		},
		async getLook() {
			try {
				const { data = [] } = await this.$http.webApi.getIscdsUserList()
				const arr = data.filter((itemA) => {
					return this.goodList.some((itemB) => itemA.id === itemB.boxId)
				})
				if (!arr.length) {
					throw new Error(`未查询到存在关联数据!`)
				}
				this.$store.commit('app/setNextUser', arr)
				return arr
			} catch (err) {
				this.operationTipB(err.message)
				return false
			}
		},
		async suspectUse(magicId) {
			this.personMagicId = magicId || this.userInfo.id
			const bool = await this.getGoods()
			if (!bool) {
				return false
			}
			const arr = await this.getLook()
			if (!arr) {
				return false
			}
			const params = {
				personMagicId: this.personMagicId,
				boxIdList: arr.map((item) => item.id),
				status: this.useTypeObj[this.useType]
			}
			await this.isCanOpenDoor(params)
		},
		async policeUse() {
			this.status = '2'
			try {
				const { data = [] } = await this.$http.webApi.getSelPersonList({ zbrIdCard: this.userInfo.idCard })
				if (!data.length) {
					this.$baseTip.info(`未查询到存在关联嫌疑人!`)
					throw new Error()
				}
				this.personList = data
			} catch (err) {
				this.$router.push('/home')
			}
		}
	},
	created() {
		if (this.userInfo.userType === '3') {
			this.suspectUse()
		} else if (this.userInfo.userType === '2') {
			this.policeUse()
		} else {
			this.$router.push('storageSituation')
		}
	}
}
</script>
<style lang="less" scoped>
.card-box {
	flex: 1;
	max-height: 1660px;
	position: relative;
	overflow-y: auto;

	&::-webkit-scrollbar {
		width: 0px;
	}

	.content {
		padding: 40px;
	}

	.card-item {
		height: 200px;
		background: #ffffff;
		box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15), inset 0px 3px 3px 1px rgba(48, 116, 218, 0.1);
		border-radius: 8px;
		margin-bottom: 32px;
		display: flex;
		align-items: center;
		padding-left: 32px;
		padding-right: 40px;

		&.deleted {
			.icon {
				width: 54px;
				height: 54px;
				background: #ccc;
				border-radius: 50%;
				border: none;
			}
		}

		.icon {
			width: 54px;
			height: 54px;
			border: 2px solid #20dbe9;
			border-radius: 50%;
		}

		.card-content {
			flex: 1;
			display: flex;
			padding-left: 36px;

			.img {
				width: 132px;
				height: 136px;
				background: #a1cdff;
				border-radius: 8px;
				margin-right: 46px;
			}

			.picture-img {
				width: 216px;
				height: 140px;
				border-radius: 8px;
				margin-right: 40px;
			}

			.name {
				font-size: 36px;
				color: #2b3346;
				line-height: 74px;
				font-weight: bold;
			}

			.register-time {
				font-weight: 400;
				font-size: 28px;
				color: #5f709a;
				line-height: 62px;
			}
		}

		.btn {
			height: 73px;
			font-size: 32px;
			color: #337eff;
			line-height: 73px;
			text-align: center;
		}

		&.selected {
			border: 3px solid #20dbe9;

			.icon {
				background-image: url('../../assets/images/settings/icon_selected.png');
				background-size: cover;
				background-position: center;
				background-repeat: no-repeat;
			}
		}
	}
}

.del-box {
	position: fixed;
	bottom: 0;
	width: 1080px;
	height: 160px;
	background: #ffffff;
	padding-left: 75px;
	padding-right: 80px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.select-box {
		display: flex;
		align-items: center;

		.icon {
			width: 54px;
			height: 54px;
			border: 2px solid #20dbe9;
			border-radius: 50%;

			&.selected {
				background-image: url('../../assets/images/settings/icon_selected.png');
				background-size: cover;
				background-position: center;
				background-repeat: no-repeat;
			}
		}

		.text {
			font-size: 36px;
			color: #333333;
			margin-left: 28px;
		}
	}

	.btn-box {
		display: flex;
		align-items: center;

		.text {
			font-size: 26px;
			color: #3d4e66;
			margin-right: 32px;

			span {
				color: #337eff;
			}
		}

		.btn {
			width: 320px;
			height: 96px;
			background: #2b5fda;
			border-radius: 8px;
			font-size: 36px;
			color: #ffffff;
			line-height: 96px;
			text-align: center;

			&.disabled {
				background: rgba(43, 95, 218, 0.4);
			}
		}
	}
}

.good-box {
	.card-box {
		flex: 1;
		max-height: 1500px;
	}
}

.modal-content {
	height: 600px;
	margin-bottom: 44px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;

	.modal-img {
		width: 200px;
		height: 200px;
		margin-bottom: 50px;
	}

	.modal-text {
		line-height: 50px;
		color: #2b3346;
		font-size: 42px;
	}
}
</style>
