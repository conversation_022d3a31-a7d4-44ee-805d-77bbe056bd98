<template>
	<div class="person-register">
		<div class="steps-box">
			<ui-steps :stepsData="stepsData" :current="current"></ui-steps>
		</div>
		<div class="ui-form-box" v-if="current === 0">
			<div class="ui-form-item">
				<div class="label">姓名</div>
				<div class="value">
					<ui-input v-keyboard v-model="formData.name" placeholder="必填" />
				</div>
			</div>
			<template v-if="config.loginMethod.includes('1')">
				<div class="ui-form-item">
					<div class="label">账号</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.loginId" placeholder="必填" />
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">密码</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.password" placeholder="必填" />
					</div>
				</div>
			</template>
			<div class="ui-form-item">
				<div class="label">警号</div>
				<div class="value">
					<ui-input v-keyboard v-model="formData.policeNumber" placeholder="必填" />
				</div>
			</div>
			<div class="ui-form-item">
				<div class="label">人员类型</div>
				<div class="value">
					<ui-select :options="filteredUserTypeList" placeholder="必填" select-title="人员类型选择" v-model="formData.userType"></ui-select>
				</div>
			</div>
			<div class="type-box">
				<div class="item" @click="showFaceBox = true" v-if="config.loginMethod.includes('2')">
					<div class="img-box">
						<img :src="formData.faceImg || require('../../assets/images/settings/face.png')" alt="" />
					</div>
					<div class="text">{{ formData.faceImg ? '修改人脸' : '录入人脸' }}</div>
				</div>
				<div
					class="item"
					@click="
						showFingerBox = true
						finger = null
					"
					v-if="config.loginMethod.includes('4')"
				>
					<div class="img-box">
						<img :src="formData.fingerprint ? require('../../assets/images/settings/light_fingerprint.png') : require('../../assets/images/settings/fingerprint.png')" alt="" />
					</div>
					<div class="text">{{ formData.fingerprint ? '修改指纹' : '录入指纹' }}</div>
				</div>
			</div>
		</div>
		<div v-if="current === 1" class="table-box">
			<ui-carousel-box :data="tableData" :isReverse="config.mainScreenType == '3'">
				<template slot-scope="{ data }">
					<cabinet-table :tableObj="data" :userId="formData.id" :tableUserData="tableUserData" @selectedList="changeList" operateType="edit" :selectString="selectedIscds[data.name] || ''"></cabinet-table>
				</template>
			</ui-carousel-box>
		</div>
		<div v-if="current === 2" class="ui-success-box">
			<img src="../../assets/images/settings/icon_success.png" alt="" />
			<div class="text">人员注册成功！</div>
			<div class="tips">
				{{ countdown }}s后，即将返回人员管理页面
				<span class="continue-register" @click="continueRegister">继续注册</span>
			</div>
		</div>
		<div class="ui-form-submit" v-show="current !== 2">
			<div class="cancel-btn" v-show="current == 0" @click="back()">取消</div>
			<div class="cancel-btn" v-show="current == 1" @click="prevSteps()">上一步</div>
			<div class="confirm-btn" :class="{ disabled: !isFormValid }" @click="isFormValid ? changeSteps() : null">下一步</div>
		</div>
		<ui-modal v-model="showFaceBox" title="录入人脸" @input="retake">
			<div v-if="showFaceBox" class="face-modal">
				<face-box
					videoId="videoIdTwo"
					ref="faceBox"
					type="register"
					@faceValue="faceValue"
					@error="
						(msg) => {
							faceTips = msg
						}
					"
					@takeStatus="
						() => {
							faceTips = '请保持面部放在取景框内'
						}
					"
				></face-box>
				<div class="tips">{{ faceTips }}</div>
			</div>
			<div class="ui-modal-footer" v-if="face">
				<div class="btn cancel-btn" @click="retake">重拍</div>
				<div class="btn" @click="faceSubmit">确认</div>
			</div>
		</ui-modal>
		<ui-modal
			v-model="showFingerBox"
			title="录入指纹"
			@input="
				() => {
					finger = null
					fingerTips = '请将指纹放在指纹仪上'
				}
			"
		>
			<div v-if="showFingerBox" class="face-modal">
				<fingerprint-box
					ref="fingerprintBox"
					type="register"
					@fingerValue="fingerValue"
					@error="
						(msg) => {
							fingerTips = msg
						}
					"
				></fingerprint-box>
				<div class="tips">{{ fingerTips }}</div>
			</div>
			<div class="ui-modal-footer" v-if="finger">
				<div class="btn cancel-btn" @click="retakeFinger">重录</div>
				<div class="btn" @click="fingerSubmit">确认</div>
			</div>
		</ui-modal>
		<ui-loading v-model="showLoading"></ui-loading>
	</div>
</template>

<script>
import Crypto from '@/common/libs/crypto'
import { mapGetters } from 'vuex'
export default {
	name: 'personRegister',
	data() {
		return {
			current: 0,
			stepsData: ['基本信息', '绑定柜格', '完成注册'],
			userTypeList: [
				{ value: '1', label: '管理员' },
				{ value: '2', label: '工作人员' }
			],
			formData: {},
			tableData: [],
			tableUserData: [],
			typeList: [
				{
					label: '录入人脸',
					id: 'face',
					icon: require('../../assets/images/settings/face.png')
				},
				{
					label: '录入指纹',
					id: 'fingerprint',
					icon: require('../../assets/images/settings/fingerprint.png')
				}
			],
			countdown: 5,
			associatedIscds: '',
			selectedIscds: {},
			showFaceBox: false,
			face: null,
			faceTips: '请保持面部放在取景框内',
			showFingerBox: false,
			finger: null,
			fingerTips: '请将指纹放在指纹仪上',
			showLoading: false
		}
	},
	watch: {
		current(val) {
			if (val === 2) {
				this.timer = setInterval(() => {
					this.countdown--
					if (this.countdown <= 0) {
						this.back()
					}
				}, 1000)
			}
		}
	},
	computed: {
		...mapGetters({
			config: 'app/config'
		}),
		filteredUserTypeList() {
			const filtered = this.userTypeList
			// if (this.config.userType === '2') {
			// 	filtered = this.userTypeList.filter((item) => item.value !== '2')
			// }
			return filtered
		},
		isFormValid() {
			if (this.current === 0) {
				let requiredFields = [this.formData.name, this.formData.policeNumber, this.formData.userType]
				if (this.config.loginMethod.includes('1')) {
					requiredFields = [...requiredFields, this.formData.password, this.formData.loginId]
				} else {
					requiredFields = [...requiredFields, this.formData.faceImg]
				}
				return requiredFields.every((value) => value)
			}
			return true
		}
	},
	methods: {
		prevSteps() {
			this.current = 0
		},
		continueRegister() {
			this.timer && clearInterval(this.timer)
			this.countdown = 5
			this.current = 0
			this.face = null
			this.finger = null
			this.formData = {}
			this.$http.webApi.getIscdsUserList().then((res) => {
				this.tableUserData = res.data
			})
		},
		retake() {
			this.face = null
			this.faceTips = '请保持面部放在取景框内'
			this.$refs.faceBox.takePhoto()
		},
		faceValue(face) {
			this.face = face
			this.faceTips = '获取成功,按确认继续'
		},
		faceSubmit() {
			this.$set(this.formData, 'faceImg', this.face)
			this.showFaceBox = false
			this.face = null
		},
		retakeFinger() {
			this.finger = null
			this.fingerTips = '请将指纹放在指纹仪上'
			if (this.$refs.fingerprintBox) {
				this.$refs.fingerprintBox.fingerImage = ''
				this.$refs.fingerprintBox.fingerStatusing = false
				this.$refs.fingerprintBox.closeDevices()
			}
		},
		fingerValue(finger) {
			this.finger = finger
			this.fingerTips = '获取成功,按确认继续'
		},
		fingerSubmit() {
			this.$set(this.formData, 'fingerprint', this.finger)
			this.showFingerBox = false
		},
		changeList(list) {
			this.$set(this.selectedIscds, list.label, list.list)
		},
		back() {
			this.timer && clearInterval(this.timer)
			this.$router.go(-1)
		},
		changeSteps() {
			if (this.current === 2) {
				this.current = 0
			} else if (this.formData.userType === '1' || this.current === 1 || (this.config.userType == '2' && this.formData.userType === '2')) {
				const params = { ...this.formData, password: this.formData.password && Crypto.aesEncrypt(this.formData.password), associatedIscds: Object.values(this.selectedIscds).join(',') || '' }
				this.showLoading = true
				this.$http.webApi
					.userRegister(params)
					.then(() => {
						this.current = 2
					})
					.finally(() => {
						this.showLoading = false
					})
			} else {
				this.current++
			}
		}
	},
	destroyed() {
		this.timer && clearInterval(this.timer)
		this.timer = null
	},
	created() {
		this.$http.appApi.getSidesAllList().then((res) => {
			this.tableData = res.data
		})
		this.$http.webApi.getIscdsUserList().then((res) => {
			this.tableUserData = res.data
		})
	}
}
</script>
<style lang="less" scoped>
.person-register {
	flex: 1;
	display: flex;
	flex-direction: column;
	.steps-box {
		padding: 0 40px;
	}
	.ui-form-box {
		flex: 1;
		padding: 100px 80px 0;
		.type-box {
			display: flex;
			padding-left: 200px;
			.item {
				width: 209px;
				height: 285px;
				background: #ffffff;
				box-shadow: 0px 4px 12px 1px #cedcf5;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				margin-right: 30px;
				.img-box {
					width: 138px;
					height: 138px;
					background: #f5faff;
					padding: 26px;
					margin-bottom: 30px;
					img {
						width: 80px;
						height: 80px;
					}
				}
				.text {
					font-size: 36px;
					color: #7a98cc;
					line-height: 36px;
				}
			}
		}
	}
	.table-box {
		flex: 1;
		padding-top: 40px;
	}
}
.face-modal {
	display: flex;
	align-items: center;
	flex-direction: column;
	margin-bottom: 30px;
	.tips {
		font-size: 42px;
		color: #2b3346;
		line-height: 162px;
		text-align: center;
		font-weight: bold;
	}
}
.continue-register {
	display: inline-block;
	width: 154px;
	height: 48px;
	background: #669eff;
	border-radius: 32px;
	font-size: 28px;
	color: #ffffff;
	text-align: center;
	line-height: 48px;
	margin-left: 24px;
}
</style>
