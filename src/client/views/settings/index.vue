<template>
	<div>
		<div class="meun-box">
			<div class="meun-item" v-for="(item, idx) in filteredMenuList" :key="idx" @click="toPage(item)">
				<img class="img" :src="item.icon" />
				<div :class="['text', $i18n.locale == 'en' && 'text-english']">{{ $t(`设置.${item.label}`) }}</div>
				<div class="next">
					<img class="next-icon" src="../../assets/images/settings/icon_arrow.png" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { communicationModeDic } from '@/common/libs/options'
import { mapGetters } from 'vuex'
export default {
	name: 'settings',
	data() {
		return {
			menuList: [
				{
					label: '人员注册',
					icon: require('../../assets/images/settings/ryzc.png'),
					path: '/personRegister',
					communicationModeShow: [communicationModeDic.OFFLINE],
					userTypeShow: ['1']
				},
				{
					label: '人员管理',
					icon: require('../../assets/images/settings/rygl.png'),
					path: '/personManage',
					communicationModeShow: [communicationModeDic.OFFLINE],
					userTypeShow: ['1', '2']
				},
				{
					label: '系统配置',
					icon: require('../../assets/images/settings/xtpz.png'),
					path: '/systemSettings',
					communicationModeShow: [communicationModeDic.ONLINE, communicationModeDic.OFFLINE],
					userTypeShow: ['1']
				},
				{
					label: '存取记录',
					icon: require('../../assets/images/settings/cqjl.png'),
					path: '/accessRecords',
					communicationModeShow: [communicationModeDic.ONLINE, communicationModeDic.OFFLINE],
					userTypeShow: ['1', '2']
				}
			]
		}
	},
	computed: {
		...mapGetters({
			userInfo: 'user/userInfo',
			config: 'app/config'
		}),
		filteredMenuList() {
			return this.menuList.filter((item) => {
				const isUserTypeMatch = item.userTypeShow.includes(this.userInfo.userType) && item.communicationModeShow.includes(this.config.communicationMode)
				return isUserTypeMatch
			})
		}
	},
	methods: {
		toPage(item) {
			this.$router.push({ path: item.path })
		}
	},
	created() {
		if (this.$store.state.user.userInfo.userType == 2) {
			this.meunList = [
				{
					label: '人员管理',
					icon: require('../../assets/images/settings/rygl.png'),
					path: '/personManage'
				},
				{
					label: '存取记录',
					icon: require('../../assets/images/settings/cqjl.png'),
					path: '/accessRecords'
				}
			]
		}
	}
}
</script>
<style lang="less" scoped>
.meun-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 45px;
	.meun-item {
		width: 840px;
		height: 320px;
		background: #ffffff;
		box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15), inset 0px 3px 3px 1px rgba(48, 116, 218, 0.1);
		margin-bottom: 40px;
		display: flex;
		align-items: center;
		padding-left: 70px;
		.img {
			width: 224px;
			height: 224px;
		}
		.text {
			text-align: center;
			font-size: 62px;
			color: #2b3346;
			font-weight: bold;
			letter-spacing: 30px;
			margin-left: 45px;
			margin-right: 5px;
			&.text-english {
				width: 400px;
				font-size: 36px;
			}
		}
		.next {
			width: 52px;
			height: 52px;
			background: #ebf2ff;
			border-radius: 50%;
			display: flex;
			justify-content: center;
			align-items: center;
			.next-icon {
				width: 24px;
				height: 24px;
			}
		}
	}
}
</style>
