<template>
	<div class="access-records">
		<div class="ui-tabs-border">
			<div v-for="(item, idx) in tabList" :key="idx" :class="['tab', searchParams.userType === item.value && 'active']" @click="changeTab(item)">{{ item.label }}</div>
		</div>
		<div class="search-box">
			<div class="search-input">
				<ui-input v-keyboard placeholder="输入操作人姓名" v-model="searchParams.operatorName"></ui-input>
				<div class="search-btn" @click="getList()">
					<img src="../../assets/images/settings/icon_search.png" alt="" />
				</div>
			</div>
		</div>
		<div class="search-box">
			<div class="search-date" @click="$refs.dateShow.openCalendar()">
				{{ $t(`存取记录.日期`) }}
				<img src="../../assets/images/settings/icon_date.png" alt="" />
			</div>
			<div class="search-type" @click="$refs.selectType.isOpen = true">
				{{ $t(`存取记录.类型`) }}
				<img src="../../assets/images/settings/icon_type.png" alt="" />
			</div>
			<div class="search-type" @click="reset">重置</div>
		</div>
		<ui-date-picker v-model="searchTime" @input="changeTime" ref="dateShow"></ui-date-picker>
		<div class="card-box wrapper" ref="wrapper" @scroll="handleScroll">
			<div class="content" v-if="cardList.length != 0">
				<div class="card-item" v-for="(item, idx) in cardList" :key="idx">
					<div class="card-top">
						<div class="name">{{ item.userName ? item.userName + '-' : '' }}{{ convertCabinetCodesToIndices(allConfig.businessInfo.cabinetNumAdd, item.sidesName, allConfig.lockInfo) }}</div>
						<div class="type-box">
							<template v-if="item.operatorType">
								<img src="../../assets/images/settings/card_type.png" alt="" />
								{{ $t(`存取记录.存取类型`) }}：{{ item.operatorType }}
							</template>
						</div>
					</div>
					<div class="card-bottom">
						<div>
							{{ $t(`存取记录.开柜`) }}:{{ item.openTime }}
							<i class="line"></i>
							{{ $t(`存取记录.关柜`) }}:{{ item.closeTime }}
						</div>
						<div class="operator-name">{{ $t(`存取记录.操作人`) }}:{{ item.operatorName }}</div>
					</div>
				</div>
			</div>
			<ui-no-data v-else></ui-no-data>
		</div>
		<ui-select ref="selectType" :showSelect="false" :options="typeList" :select-title="$t(`存取记录.存取类型`)" multiple v-model="searchType" @change="changeType"></ui-select>
	</div>
</template>

<script>
import { communicationModeDic } from '@/common/libs/options'
import { mapGetters } from 'vuex'
import { convertCabinetCodesToIndices } from '@/common/libs/util'
export default {
	name: 'accessRecords',
	data() {
		return {
			convertCabinetCodesToIndices,
			searchTime: [],
			searchType: [],
			typeList: [
				{ value: '存入物品', label: this.$t(`存取记录.存入物品`) },
				{ value: '取出物品', label: this.$t(`存取记录.取出物品`) },
				{ value: '临时借出', label: this.$t(`存取记录.临时借出`) },
				{ value: '借出归还', label: this.$t(`存取记录.借出归还`) }
			],
			tabValue: '1',
			tabList: [
				{ value: '', label: this.$t(`存取记录.全部`) },
				{ value: '1', label: this.$t(`存取记录.管理员`) },
				{ value: '2', label: this.$t(`存取记录.工作人员`) },
				{ value: '3', label: this.$t(`存取记录.非工作人员`) }
			],
			searchParams: {
				userType: '',
				operatorName: '',
				startTime: '',
				endTime: '',
				associatedPoliceId: '',
				page: 1,
				size: 10
			},
			cardList: [],
			scroll: null,
			total: 0,
			loading: false
		}
	},
	computed: {
		...mapGetters({
			config: 'app/config',
			allConfig: 'app/allConfig'
		})
	},
	methods: {
		reset() {
			this.searchTime = []
			this.searchType = []
			this.searchParams = {
				userType: '',
				operatorName: '',
				startTime: '',
				endTime: '',
				associatedPoliceId: this.$store.state.user.userInfo.userType == 2 ? this.$store.state.user.userInfo.id : '',
				page: 1,
				size: 10
			}
			this.getList()
		},
		changeTime() {
			if (this.searchTime && this.searchTime[0]) {
				this.searchParams.startTime = `${this.searchTime[0]} 00:00`
				this.searchParams.endTime = `${this.searchTime[1]} 23:59`
			} else {
				this.searchParams.startTime = ''
				this.searchParams.endTime = ''
			}
			this.getList()
		},
		changeType() {
			this.searchParams.operatorType = this.searchType.join(',')
			this.getList()
		},
		changeTab(item) {
			this.searchParams.userType = item.value
			this.getList()
		},
		getList() {
			this.searchParams.page = 1
			this.cardList = []
			this.$http.webApi.getAccessRecords(this.searchParams).then((res) => {
				const { data, total } = res.data
				this.cardList = data
				this.total = total
				this.loading = false
			})
		},
		loadMoreData() {
			if (this.total > this.cardList.length) {
				this.searchParams.page = this.searchParams.page + 1
				this.addList()
			}
		},
		addList() {
			this.$http.webApi.getAccessRecords(this.searchParams).then((res) => {
				const { data, total } = res.data
				this.cardList = [...this.cardList, ...data]
				this.total = total
				this.loading = false
			})
		},
		handleScroll(event) {
			if (this.loading) return
			const { scrollTop, scrollHeight, clientHeight } = event.target
			if (scrollTop + clientHeight >= scrollHeight - 10) {
				this.loading = true
				this.loadMoreData()
			}
		}
	},
	created() {
		if (this.config.communicationMode == communicationModeDic.ONLINE) {
			this.tabList = [
				{ value: '', label: this.$t(`存取记录.全部`) },
				{ value: '2', label: this.$t(`存取记录.工作人员`) },
				{ value: '3', label: this.$t(`存取记录.非工作人员`) }
			]
			this.$http.webApi.dicType('GOODS_ACCESS_TYPE').then((res) => {
				const { data = [] } = res
				this.typeList = data.map((item) => {
					return {
						label: item.fieldValue,
						value: item.fieldCode
					}
				})
			})
		}
		this.searchParams.associatedPoliceId = this.$store.state.user.userInfo.userType == 2 ? this.$store.state.user.userInfo.id : ''
		this.getList()
	}
}
</script>
<style lang="less" scoped>
.access-records {
	flex: 1;
	display: flex;
	flex-direction: column;
	.search-box {
		height: 96px;
		margin-top: 40px;
		display: flex;
		padding: 0 40px;
		.search-input {
			width: 100%;
			position: relative;
			padding-right: 87px;
			.search-btn {
				position: absolute;
				top: 0;
				right: 0;
				width: 96px;
				height: 96px;
				background: #669eff;
				border-radius: 8px;
				display: flex;
				justify-content: center;
				align-items: center;
				img {
					width: 50px;
					height: 50px;
				}
			}
		}
		.search-date,
		.search-type {
			margin-right: 24px;
			width: 225px;
			height: 96px;
			background: #669eff;
			border-radius: 8px;
			font-size: 40px;
			color: #ffffff;
			line-height: 40px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 48px;
				height: 48px;
				margin-left: 16px;
			}
		}
	}
	.card-box {
		height: 1300px;
		position: relative;
		overflow-y: auto;
		&::-webkit-scrollbar {
			width: 0px;
		}
		.content {
			padding: 40px;
			.card-item {
				height: 180px;
				background: #ffffff;
				box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15), inset 0px 3px 3px 1px rgba(48, 116, 218, 0.1);
				border-radius: 8px;
				margin-bottom: 32px;
				padding: 0 30px;
				.card-top {
					display: flex;
					justify-content: space-between;
					align-items: center;
					height: 117px;
					.name {
						font-size: 36px;
						color: #2b3346;
						font-weight: bold;
					}
					.type-box {
						font-weight: 400;
						font-size: 30px;
						color: #2b3346;
						display: flex;
						align-items: center;
						img {
							width: 36px;
							height: 36px;
							margin-right: 5px;
						}
					}
				}
				.card-bottom {
					display: flex;
					justify-content: space-between;
					font-weight: 400;
					font-size: 28px;
					color: #5f709a;
					.line {
						display: inline-block;
						flex-shrink: 0;
						width: 32px;
						height: 1px;
						background: none;
						position: relative;
						margin: 0 10px;
						&::before {
							content: '.........';
							position: absolute;
							left: -4px;
							top: -28px;
							text-align: center;
							color: #5f709a;
							font-size: 20px;
						}
					}
					.operator-name {
						width: 232px;
						white-space: nowrap; /* 禁止换行 */
						overflow: hidden; /* 超出部分隐藏 */
						text-overflow: ellipsis; /* 超出部分显示省略号 */
						text-align: right;
					}
				}
			}
		}
	}
}
</style>
