<template>
	<div class="camera-setting">
		<div class="face-box">
			<div class="circle-container">
				<div class="gradient-circle">
					<div class="inner-circle"></div>
				</div>
			</div>
			<div class="face-content">
				<video class="common" v-show="cameraMediaStream" :id="vdeoBoxName" autoplay playsinline :style="videoStyleComputed"></video>
			</div>
		</div>
		<div class="btn" @click="switchCamera()">切换</div>
		<div class="btn" @click="rotateXCamera()">旋转</div>
		<!-- <div class="btn" @click="rotateYCamera()">翻转</div> -->
		<div class="btn submit" @click="submit()">保存</div>
		<div class="btn close" @click="closeCamera(true)">退出</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getMediaDevices } from '@/common/libs/util'
export default {
	name: 'cameraSetting',
	data() {
		return {
			cameraMediaStream: null,
			videoStyle: { width: 660, height: 660 },
			videoClass: {
				rotateY: 0,
				rotateX: 0
			},
			cameraOptions: [],
			deviceId: null,
			vdeoBoxName: 'a',
			deviceIndex: 0,
			openStatus: true,
			isButtonDisabled: false
		}
	},
	computed: {
		...mapGetters({
			allConfig: 'app/allConfig'
		}),
		videoStyleComputed() {
			const rotateY = this.videoClass.rotateY / 2
			const rotateX = this.videoClass.rotateX / 2
			let transform = ''
			if (rotateY != 0 && rotateX == 0) {
				transform = `rotateY(${rotateY}turn)`
			} else if (rotateY == 0 && rotateX != 0) {
				transform = `rotate(${rotateX}turn)`
			} else {
				transform = `rotate(${rotateX}turn) rotateX(${rotateY}turn)`
			}
			return {
				height: `${this.videoStyle.height}px`,
				width: `${this.videoStyle.width}px`,
				transform
			}
		}
	},
	methods: {
		async getDevice() {
			this.cameraOptions = await getMediaDevices()
			const flag = this.cameraOptions.findIndex((item) => {
				return item.deviceId === this.allConfig.colorCameraInfo.deviceId
			})
			if (flag != -1) {
				this.deviceId = this.allConfig.colorCameraInfo.deviceId
				this.deviceIndex = flag
			} else {
				this.deviceId = this.cameraOptions[0].deviceId || 'onlyOne'
				this.deviceIndex = 0
			}
			await this.openCamera()
		},
		submit() {
			const params = {
				...this.allConfig.colorCameraInfo,
				rotateX: Number(this.videoClass.rotateX),
				deviceId: this.deviceId
			}
			this.$http.appApi.updateConfigInfo({ colorCameraInfo: params }).then((res) => {
				this.$baseTip.success('保存成功!')
				this.$store.commit('app/setAllConfig', res.data)
			})
		},
		switchCamera() {
			if (this.isButtonDisabled) {
				return
			}
			this.isButtonDisabled = true
			setTimeout(() => {
				this.isButtonDisabled = false
			}, 2000)
			if (this.deviceIndex === this.cameraOptions.length - 1) {
				this.deviceIndex = 0
				this.deviceId = this.cameraOptions[this.deviceIndex].deviceId
			} else {
				this.deviceIndex++
				this.deviceId = this.cameraOptions[this.deviceIndex].deviceId
			}
			this.closeCamera()
			setTimeout(() => {
				this.openStatus = true
				this.openCamera()
			}, 1000)
		},
		// rotateYCamera() {
		// 	this.videoClass.rotateY = Number(this.videoClass.rotateY) + 1
		// 	if (this.videoClass.rotateY == 2) {
		// 		this.videoClass.rotateY = 0
		// 	}
		// 	this.closeCamera()
		// 	setTimeout(() => {
		// 		this.openCamera()
		// 	}, 1000)
		// },
		rotateXCamera() {
			if (this.isButtonDisabled) {
				return
			}
			this.isButtonDisabled = true
			setTimeout(() => {
				this.isButtonDisabled = false
			}, 2000)
			this.videoClass.rotateX = Number(this.videoClass.rotateX) + 0.5
			if (this.videoClass.rotateX == 2) {
				this.videoClass.rotateX = 0
			}
			this.closeCamera()
			setTimeout(() => {
				this.openStatus = true
				this.openCamera()
			}, 1000)
		},
		closeCamera(flag) {
			if (!this.cameraMediaStream || !(this.cameraMediaStream instanceof MediaStream)) {
				this.cameraMediaStream = null
				this.openStatus = false
				flag && this.$emit('hidd')
				return
			}
			try {
				this.cameraMediaStream.getTracks().forEach((track) => {
					track.stop()
				})
			} catch (error) {
				console.error('Failed to stop camera tracks:', error)
			} finally {
				this.openStatus = false
				this.cameraMediaStream = null
				flag && this.$emit('hidd')
			}
		},
		async openCamera() {
			if (!this.deviceId) {
				this.$baseTip.info('请选择摄像头!')
				return
			}
			const constraints = {
				video: {
					width: 1080,
					height: 1080,
					deviceId: this.deviceId ? { exact: this.deviceId } : undefined
				},
				audio: false
			}
			navigator.mediaDevices
				.getUserMedia(constraints)
				.then((stream) => {
					this.cameraMediaStream = stream
					const videoBoxElement = document.getElementById(this.vdeoBoxName)

					if (!this.openStatus || !videoBoxElement) {
						this.closeCamera()
						return
					}
					try {
						videoBoxElement.srcObject = stream
					} catch (error) {
						videoBoxElement.src = URL.createObjectURL(stream)
					}
				})
				.catch(() => {
					this.$baseTip.info('无法获取摄像头!')
				})
		}
	},
	mounted() {
		this.getDevice()
	}
}
</script>
<style lang="less" scoped>
.camera-setting {
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 100%;
	padding-top: 100px;
	.title {
		font-size: 40px;
		color: aqua;
		line-height: 100px;
	}
	.face-box {
		width: 724px;
		height: 724px;
		border-radius: 50%;
		margin-bottom: 250px;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		.face-content {
			position: absolute;
			top: 32px;
			left: 32px;
			height: 660px;
			width: 660px;
			border-radius: 50%;
			overflow: hidden;
		}
		.circle-container {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 724px;
			width: 724px;
			.gradient-circle {
				width: 724px;
				height: 724px;
				border-radius: 50%;
				background: conic-gradient(rgba(38, 103, 255, 1), rgba(121, 100, 244, 1), rgba(94, 244, 255, 0));
				padding: 10px;
				display: flex;
				justify-content: center;
				align-items: center;
				animation: rotate 3s linear infinite;
				.inner-circle {
					width: 100%;
					height: 100%;
					background-color: #f0f0f0;
					border-radius: 50%;
				}
			}
		}
	}
	.btn {
		width: 660px;
		height: 96px;
		background: #669eff;
		border-radius: 8px;
		font-size: 36px;
		color: #ffffff;
		line-height: 96px;
		text-align: center;
		margin-top: 20px;
		&.close {
			background: #ff5376;
		}
		&.submit {
			margin-top: 150px;
			background: #2b5fda;
		}
	}
}
@keyframes rotate {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
</style>
