<template>
	<div class="improve-information">
		<div class="steps-box">
			<ui-steps :stepsData="stepsData" :current="current"></ui-steps>
		</div>
		<div class="ui-form-box" v-if="current === 0">
			<template v-if="formData.userType != '3'">
				<div class="ui-form-item">
					<div class="label">姓名</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.name" placeholder="必填" disabled />
					</div>
				</div>
				<template v-if="config.loginMethod.includes('1')">
					<div class="ui-form-item">
						<div class="label">账号</div>
						<div class="value">
							<ui-input v-model="formData.loginId" placeholder="必填" disabled />
						</div>
					</div>
					<div class="ui-form-item">
						<div class="label">密码</div>
						<div class="value">
							<ui-input :value="'******'" placeholder="必填" disabled />
						</div>
					</div>
				</template>
				<div class="ui-form-item" v-if="formData.loginId !== 'gly'">
					<div class="label">警号</div>
					<div class="value">
						<ui-input v-model="formData.policeNumber" placeholder="必填" disabled />
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">人员类型</div>
					<div class="value">
						<ui-input :value="formData.userType == 1 ? '管理员' : '工作人员'" placeholder="必填" disabled />
					</div>
				</div>
				<div class="type-box" :class="{ 'type-sysadmin': formData.loginId === 'gly' }">
					<div class="item" @click="eidtAccountShow = true" v-if="config.loginMethod.includes('1')">
						<div class="img-box">
							<img src="../../assets/images/settings/light_account.png" alt="" />
						</div>
						<div class="text">修改密码</div>
					</div>
					<div class="item" @click="showFaceBox = true" v-if="config.loginMethod.includes('2')">
						<div class="img-box">
							<img :src="formData.faceImg || require('../../assets/images/settings/face.png')" alt="" />
						</div>
						<div class="text">{{ formData.faceImg ? '修改人脸' : '录入人脸' }}</div>
					</div>
					<div
						class="item"
						@click="
							showFingerBox = true
							finger = null
						"
						v-if="config.loginMethod.includes('4')"
					>
						<div class="img-box">
							<img :src="formData.fingerprint ? require('../../assets/images/settings/light_fingerprint.png') : require('../../assets/images/settings/fingerprint.png')" alt="" />
						</div>
						<div class="text">{{ formData.fingerprint ? '修改指纹' : '录入指纹' }}</div>
					</div>
					<div class="item" @click="eidtGestureShow = true" v-if="formData.loginId === 'gly' && config.loginMethod.includes('5')">
						<div class="img-box">
							<img src="../../assets/images/settings/light_account.png" alt="" />
						</div>
						<div class="text">修改手势</div>
					</div>
				</div>
			</template>
			<template v-else>
				<div class="ui-form-item">
					<div class="label">姓名</div>
					<div class="value">
						<ui-input v-model="formData.name" placeholder="必填" disabled />
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">身份证</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.certificateNumber" placeholder="选填" />
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">关联民警</div>
					<div class="value">
						<ui-input v-model="formData.associatedPoliceName" placeholder="必填" disabled />
					</div>
				</div>
				<template v-if="config.loginMethod.includes('1') && formData.loginId">
					<div class="ui-form-item">
						<div class="label">账号</div>
						<div class="value">
							<ui-input v-model="formData.loginId" disabled />
						</div>
					</div>
					<div class="ui-form-item">
						<div class="label">密码</div>
						<div class="value">
							<ui-input :value="formData.password ? '******' : ''" disabled />
						</div>
					</div>
				</template>
				<div class="type-box is-admin">
					<div class="item" v-if="config.loginMethod.includes('1') && formData.password" @click="eidtAccountShow = true">
						<div class="img-box">
							<img src="../../assets/images/settings/light_account.png" alt="" />
						</div>
						<div class="text">修改密码</div>
					</div>
					<div class="item" @click="showFaceBox = true" v-if="config.loginMethod.includes('2')">
						<div class="img-box">
							<img :src="formData.faceImg || require('../../assets/images/settings/face.png')" alt="" />
						</div>
						<div class="text">修改人脸</div>
					</div>
				</div>
			</template>
		</div>
		<div v-if="current === 1" class="table-box">
			<ui-carousel-box :data="tableData" :isReverse="config.mainScreenType == '3'">
				<template slot-scope="{ data }">
					<cabinet-table :tableObj="data" :userId="formData.id" :tableUserData="tableUserData" @selectedList="changeList" operateType="edit" :selectString="selectedIscds[data.name] || ''"></cabinet-table>
				</template>
			</ui-carousel-box>
		</div>
		<div v-if="current === 2" class="ui-success-box">
			<img src="../../assets/images/settings/icon_success.png" alt="" />
			<div class="text">完善信息成功！</div>
			<div class="tips">{{ countdown }}s后，即将返回人员管理页面</div>
		</div>
		<div class="ui-form-submit" v-show="current !== 2">
			<div class="cancel-btn" v-show="current == 0" @click="back()">取消</div>
			<div class="cancel-btn" v-show="current == 1" @click="prevSteps()">上一步</div>
			<div class="confirm-btn" @click="changeSteps()">下一步</div>
		</div>
		<ui-modal v-model="eidtAccountShow" title="修改密码">
			<div class="ui-form-box">
				<!-- <div class="ui-form-item">
					<div class="label">旧密码</div>
					<div class="value">
						<ui-input v-model="accountData.oldPassword"></ui-input>
					</div>
				</div> -->
				<div class="ui-form-item">
					<div class="label">新密码</div>
					<div class="value">
						<ui-input v-keyboard v-model="accountData.newPassword"></ui-input>
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">重复密码</div>
					<div class="value">
						<ui-input v-keyboard v-model="accountData.repeatPassword"></ui-input>
					</div>
				</div>
			</div>
			<div class="ui-modal-footer">
				<div class="btn" @click="accountSubmit()">确认</div>
			</div>
		</ui-modal>
		<ui-modal v-model="eidtGestureShow" title="修改手势">
			<div class="gesture-box">
				<verify-gesture gestureType="giveValue" @gestureValue="changeGesture"></verify-gesture>
			</div>
			<div class="ui-modal-footer">
				<div class="btn" @click="gestureSubmit()">确认</div>
			</div>
		</ui-modal>
		<ui-modal v-model="showFaceBox" title="录入人脸" @input="retake">
			<div v-if="showFaceBox" class="face-modal">
				<face-box
					videoId="videoIdOne"
					ref="faceBox"
					type="register"
					@faceValue="faceValue"
					@error="
						(msg) => {
							faceTips = msg
						}
					"
					@takeStatus="
						() => {
							faceTips = '请保持面部放在取景框内'
						}
					"
				></face-box>
				<div class="tips">{{ faceTips }}</div>
			</div>
			<div class="ui-modal-footer" v-if="face">
				<div class="btn cancel-btn" @click="retake">重拍</div>
				<div class="btn" @click="faceSubmit">确认</div>
			</div>
		</ui-modal>
		<ui-modal
			v-model="showFingerBox"
			title="录入指纹"
			@input="
				() => {
					finger = null
					fingerTips = '请将指纹放在指纹仪上'
				}
			"
		>
			<div v-if="showFingerBox" class="face-modal">
				<fingerprint-box
					ref="fingerprintBox"
					type="register"
					@fingerValue="fingerValue"
					@error="
						(msg) => {
							fingerTips = msg
						}
					"
				></fingerprint-box>
				<div class="tips">{{ fingerTips }}</div>
			</div>
			<div class="ui-modal-footer" v-if="finger">
				<div class="btn cancel-btn" @click="retakeFinger">重录</div>
				<div class="btn" @click="fingerSubmit">确认</div>
			</div>
		</ui-modal>
	</div>
</template>

<script>
import Crypto from '@/common/libs/crypto'
import { mapGetters } from 'vuex'
export default {
	name: 'improveInformation',
	data() {
		return {
			accountData: {},
			eidtAccountShow: false,
			eidtGestureShow: false,
			current: 0,
			stepsData: ['基本信息', '绑定柜格', '完善成功'],
			userTypeList: [
				{ value: '1', label: '管理员' },
				{ value: '2', label: '工作人员' }
			],
			formData: {},
			tableData: [],
			selectedIscds: {},
			typeList: [
				{
					label: '修改密码',
					id: 'account',
					icon: require('../../assets/images/settings/account.png')
				},
				{
					label: '录入人脸',
					id: 'faceImg',
					icon: require('../../assets/images/settings/face.png')
				},
				{
					label: '录入指纹',
					id: 'fingerprint',
					icon: require('../../assets/images/settings/fingerprint.png')
				},
				{
					label: '录入手势',
					id: 'gesture',
					icon: require('../../assets/images/settings/account.png')
				}
			],
			countdown: 5,
			showFaceBox: false,
			face: null,
			faceTips: '请保持面部放在取景框内',
			showFingerBox: false,
			finger: null,
			fingerTips: '请将指纹放在指纹仪上'
		}
	},
	computed: {
		...mapGetters({
			config: 'app/config'
		})
	},
	watch: {
		current(val) {
			if (val === 2) {
				this.timer = setInterval(() => {
					this.countdown--
					if (this.countdown <= 0) {
						this.back()
					}
				}, 1000)
			}
		}
	},
	methods: {
		prevSteps() {
			this.current = 0
		},
		retake() {
			this.face = null
			this.faceTips = '请保持面部放在取景框内'
			this.$refs.faceBox.takePhoto()
		},
		faceValue(face) {
			this.face = face
			this.faceTips = '获取成功,按确认继续'
		},
		faceSubmit() {
			this.$set(this.formData, 'faceImg', this.face)
			this.showFaceBox = false
			this.face = null
		},
		retakeFinger() {
			this.finger = null
			this.fingerTips = '请将指纹放在指纹仪上'
			if (this.$refs.fingerprintBox) {
				this.$refs.fingerprintBox.fingerImage = ''
				this.$refs.fingerprintBox.fingerStatusing = false
				this.$refs.fingerprintBox.closeDevices()
			}
		},
		fingerValue(finger) {
			this.finger = finger
			this.fingerTips = '获取成功,按确认继续'
		},
		fingerSubmit() {
			this.$set(this.formData, 'fingerprint', this.finger)
			this.showFingerBox = false
		},
		changeGesture(value) {
			this.accountData.gesture = value
		},
		gestureSubmit() {
			this.formData.gesture = this.accountData.gesture
			this.$baseTip.info('设置成功，提交后可使用新密码登录!')
			this.eidtGestureShow = false
		},
		accountSubmit() {
			if (this.accountData.newPassword == this.accountData.repeatPassword) {
				this.formData.password = Crypto.aesEncrypt(this.accountData.newPassword)
				this.$baseTip.info('设置成功，提交后可使用新密码登录!')
				this.eidtAccountShow = false
			} else {
				this.$baseTip.error('两次新密码输入不一致，请重新输入!')
			}
			// if (Crypto.aesEncrypt(this.accountData.oldPassword) == this.formData.password) {
			// 	if (this.accountData.newPassword == this.accountData.repeatPassword) {
			// 		this.formData.password = Crypto.aesEncrypt(this.accountData.newPassword)
			// 		this.$baseTip.info('设置成功，提交后可使用新密码登录!')
			// 		this.eidtAccountShow = false
			// 	} else {
			// 		this.$baseTip.error('两次新密码输入不一致，请重新输入!')
			// 	}
			// } else {
			// 	this.$baseTip.error('旧密码输入错误，请重新输入!')
			// }
		},
		changeList(list) {
			this.$set(this.selectedIscds, list.label, list.list)
		},
		back() {
			this.timer && clearInterval(this.timer)
			this.$router.go(-1)
		},
		changeSteps() {
			if (this.current === 2) {
				this.current = 0
			} else if (this.formData.userType != '2' || this.current === 1 || this.config.userType === '2') {
				this.formData.associatedIscds = Object.values(this.selectedIscds)
					.filter((value) => value.trim() !== '')
					.join(',')
				this.$http.webApi.userUpdate(this.formData).then(() => {
					this.current = 2
				})
			} else {
				this.current++
			}
		},
		getData() {
			this.$http.webApi.findInfoById({ id: this.$route.query.id }).then((res) => {
				this.formData = res.data
			})
			this.$http.appApi.getSidesAllList().then((res) => {
				this.tableData = res.data
			})
			this.$http.webApi.getIscdsUserList().then((res) => {
				this.tableUserData = res.data
			})
		}
	},
	created() {
		this.getData()
	},
	beforeDestroy() {
		this.timer && clearInterval(this.timer)
		this.timer = null
	}
}
</script>
<style lang="less" scoped>
.improve-information {
	flex: 1;
	display: flex;
	flex-direction: column;
	.steps-box {
		padding: 0 40px;
	}
	& > .ui-form-box {
		flex: 1;
		padding: 100px 80px 0;
		.type-box {
			display: flex;
			margin-left: 200px;
			.item {
				width: 209px;
				height: 285px;
				background: #ffffff;
				box-shadow: 0px 4px 12px 1px #cedcf5;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				margin-right: 30px;
				.img-box {
					width: 138px;
					height: 138px;
					background: #f5faff;
					padding: 26px;
					margin-bottom: 30px;
					img {
						width: 80px;
						height: 80px;
					}
				}
				.text {
					font-size: 36px;
					color: #7a98cc;
					line-height: 36px;
				}
			}
			&.type-sysadmin {
				margin-left: 0;
			}
		}
	}
	.table-box {
		flex: 1;
		padding-top: 40px;
	}
}
.gesture-box {
	display: flex;
	justify-content: center;
	margin-bottom: 47px;
}
.face-modal {
	display: flex;
	align-items: center;
	flex-direction: column;
	margin-bottom: 30px;
	.tips {
		font-size: 42px;
		color: #2b3346;
		line-height: 162px;
		text-align: center;
		font-weight: bold;
	}
}
</style>
