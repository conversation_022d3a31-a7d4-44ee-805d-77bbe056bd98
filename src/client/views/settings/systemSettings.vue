<template>
	<div class="form-container">
		<div class="ui-tabs-block">
			<div v-for="(item, idx) in tabList" :key="idx" :class="['tab', tabValue === item.value && 'active']" @click="changeTab(item)">{{ item.label }}</div>
		</div>
		<div class="ui-form-box" v-show="tabValue === '1'">
			<template>
				<div class="ui-form-item">
					<div class="label">通信模式</div>
					<div class="value">
						<ui-radio v-for="(item, idx) in communicationModeOptions" :key="idx" v-model="formData.communicationMode" :label="item.value" @input="changeCommunicationMode()" width="370px">{{ item.label }}</ui-radio>
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">场所名称</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.placeName" placeholder="选填" :maxLength="15" />
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">终端标题</div>
					<div class="value">
						<ui-input v-keyboard v-model="operationInfo.terminalName" placeholder="选填" :maxLength="10" />
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">版权</div>
					<div class="value">
						<ui-input v-keyboard v-model="operationInfo.copyright" placeholder="选填" />
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">登录方式</div>
					<div class="value">
						<ui-select v-model="loginMethod" multiple :options="loginMethodList" placeholder="必填" select-title="登录方式选择"></ui-select>
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">操作时间</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.operationTime" numeric placeholder="必填" />
					</div>
				</div>
				<div class="ui-form-item" v-if="formData.communicationMode == communicationModeDic.OFFLINE">
					<div class="label">使用人员</div>
					<div class="value">
						<ui-select :options="userTypeList" placeholder="必填" select-title="使用人员选择" v-model="formData.userType"></ui-select>
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">相机设置</div>
					<div class="value" @click="showModal = true">
						<ui-input value="点击设置" disabled />
					</div>
				</div>
			</template>
		</div>
		<div class="ui-form-box" v-show="tabValue === '2'">
			<template>
				<div class="ui-form-item" v-if="formData.communicationMode == communicationModeDic.ONLINE">
					<div class="label">服务ip</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.serviceIp" placeholder="必填" />
					</div>
				</div>
				<div class="ui-form-item" v-if="formData.communicationMode == communicationModeDic.ONLINE">
					<div class="label">服务端口</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.httpPort" placeholder="必填" />
					</div>
				</div>
				<div class="ui-form-item" v-if="formData.communicationMode == communicationModeDic.ONLINE">
					<div class="label spacing">离区拍照</div>
					<div class="value">
						<ui-switch v-model="formData.outsideTake">
							<span slot="open">开启</span>
							<span slot="close">关闭</span>
						</ui-switch>
					</div>
				</div>
				<div class="ui-form-item" v-if="formData.communicationMode == communicationModeDic.ONLINE && formData.outsideTake">
					<div class="label spacing">播放地址</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.outsideTakeHttp" placeholder="必填" />
					</div>
				</div>
				<div class="ui-form-item" v-if="formData.communicationMode == communicationModeDic.ONLINE && formData.outsideTake">
					<div class="label spacing">通道id</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.channelId" placeholder="必填" />
					</div>
				</div>
				<div class="ui-form-item" v-if="formData.communicationMode == communicationModeDic.OFFLINE">
					<div class="label">人脸阈值</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.facialThreshold" numeric placeholder="必填" />
					</div>
				</div>
				<div class="ui-form-item" v-if="formData.communicationMode == communicationModeDic.OFFLINE">
					<div class="label">指纹阈值</div>
					<div class="value">
						<ui-input v-keyboard v-model="formData.fingerThreshold" numeric placeholder="必填" />
					</div>
				</div>
				<div class="ui-form-item" v-show="formData.communicationMode == communicationModeDic.ONLINE || formData.userType != 1">
					<div class="label">双重认证</div>
					<div class="value">
						<ui-switch v-model="twoFactorAuthentication">
							<span slot="open">开启</span>
							<span slot="close">关闭</span>
						</ui-switch>
					</div>
				</div>
				<div class="ui-form-item" v-show="formData.communicationMode == communicationModeDic.ONLINE || formData.userType != 1">
					<div class="label">签名捺印</div>
					<div class="value">
						<ui-switch v-model="formData.signatureSeal">
							<span slot="open">开启</span>
							<span slot="close">关闭</span>
						</ui-switch>
					</div>
				</div>
				<div class="ui-form-item" v-show="(formData.communicationMode == communicationModeDic.ONLINE || formData.userType != 1) && formData.signatureSeal">
					<div class="label">屏幕签名</div>
					<div class="value">
						<ui-switch v-model="formData.screenSignature">
							<span slot="open">开启</span>
							<span slot="close">关闭</span>
						</ui-switch>
					</div>
				</div>
				<div class="ui-form-item" v-if="formData.communicationMode == communicationModeDic.ONLINE">
					<div class="label">定位归还</div>
					<div class="value">
						<ui-switch v-model="formData.locaterReturn">
							<span slot="open">开启</span>
							<span slot="close">关闭</span>
						</ui-switch>
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">主屏类型</div>
					<div class="value">
						<ui-select :options="zpList" placeholder="必填" select-title="主屏类型选择" v-model="formData.mainScreenType"></ui-select>
					</div>
				</div>
				<div class="ui-form-item" v-if="formData.communicationMode != communicationModeDic.ONLINE && formData.mainScreenType != 1">
					<div class="label">同步地址</div>
					<div class="value">
						<ui-input v-keyboard placeholder="必填" v-model="formData.dataAddress"></ui-input>
					</div>
				</div>
				<div class="ui-form-item" v-if="formData.communicationMode == communicationModeDic.ONLINE && formData.mainScreenType == '3'">
					<div class="label">主序列号</div>
					<div class="value">
						<ui-input v-keyboard placeholder="必填" v-model="formData.mainSerialNumber"></ui-input>
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">主柜型号</div>
					<div class="value">
						<div class="xh">
							<ui-select :options="mainCabinetList" placeholder="必填" select-title="主柜选择" v-model="formData.mainCabinetModel" @change="changeMainCabinet"></ui-select>
						</div>
						<div class="text">柜格{{ getNumByValue(formData.mainCabinetModel, mainCabinetList) }}</div>
					</div>
				</div>
				<div class="ui-form-item" v-for="(item, idx) in secondaryCabinetModel" :key="idx">
					<div class="label">副柜型号</div>
					<div class="value">
						<div class="xh">
							<ui-select :options="secondaryCabinetList" placeholder="必填" select-title="副柜选择" v-model="secondaryCabinetModel[idx]" disabled></ui-select>
						</div>
						<div class="text">柜格{{ getNumByValue(secondaryCabinetModel[idx], secondaryCabinetList) }}</div>
						<div class="del" v-if="secondaryCabinetModel.length > 0" @click="del(idx)"></div>
					</div>
				</div>
				<div class="add" @click="addgzxh">+新增副柜</div>
				<!-- 锁板部分start -->
				<div class="ui-form-item">
					<div class="label">锁控地址</div>
					<div class="value">
						<ui-select v-if="tabValue === '2' && isCom(configLockInfo.manufacturer)" select-title="锁控地址" :options="comOptions" v-model="configLockInfo.path"></ui-select>
						<ui-input v-keyboard v-else v-model="configLockInfo.path"></ui-input>
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">锁板拨码</div>
					<div class="value">
						<ui-input v-keyboard v-model="configLockInfo.lockPlate" placeholder="例如：1;2,3"></ui-input>
					</div>
				</div>
				<!-- 锁板部分end -->
				<!-- 补光灯部分start -->
				<div class="ui-form-item">
					<div class="label">补光地址</div>
					<div class="value">
						<ui-select v-if="tabValue === '2' && isLightCom(configLightInfo.manufacturer)" select-title="补光地址" :options="comOptions" v-model="configLightInfo.path"></ui-select>
						<ui-input v-keyboard v-else v-model="configLightInfo.path"></ui-input>
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">补光锁号</div>
					<div class="value">
						<ui-input v-keyboard v-model="configLightInfo.door"></ui-input>
					</div>
				</div>
				<!-- 补光灯部分end -->
				<div class="ui-form-item">
					<div class="label">伸缩状态</div>
					<div class="value">
						<ui-switch v-model="formData.openWristbandDrawer">
							<span slot="open">开启</span>
							<span slot="close">关闭</span>
						</ui-switch>
					</div>
				</div>
				<div class="ui-form-item" v-if="formData.openWristbandDrawer">
					<div class="label">伸缩地址</div>
					<div class="value">
						<ui-select v-if="tabValue === '2'" select-title="伸缩柜地址" :options="comOptions" v-model="wristbandDrawerInfo.path"></ui-select>
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">柜号累加</div>
					<div class="value">
						<ui-switch v-model="formData.cabinetNumAdd">
							<span slot="open">开启</span>
							<span slot="close">关闭</span>
						</ui-switch>
					</div>
				</div>
			</template>
		</div>
		<div class="ui-form-box" v-show="tabValue === '3'">
			<template>
				<div class="ui-form-item">
					<div class="label">mac地址</div>
					<div class="value">
						<ui-input v-model="propertyData.oldMac" disabled />
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">本机IP</div>
					<div class="value">
						<ui-input v-model="propertyData.ip" disabled />
					</div>
				</div>
				<div class="ui-form-item">
					<div class="label">版本号</div>
					<div class="value">
						<ui-input v-model="propertyData.version" disabled />
					</div>
				</div>
			</template>
		</div>
		<div class="form-submit">
			<div class="btn" v-show="tabValue != '3'" :class="{ disabled: !isFormValid }" @click="isFormValid ? flagSubmit(tabValue) : null">保存</div>
		</div>
		<div class="camera-settings" v-if="showModal">
			<camera-setting @hidd="showModal = false"></camera-setting>
		</div>
		<ui-modal v-model="showErrorModal" title="提示">
			<div class="modal-content">
				<div class="modal-text">修改服务后会重启，是否确认？</div>
			</div>
			<div class="ui-modal-footer">
				<div class="btn cancel-btn" @click="showErrorModal = false">取消</div>
				<div class="btn" @click="submit(tabValue)">确定</div>
			</div>
		</ui-modal>
		<ui-loading v-model="showLoading"></ui-loading>
	</div>
</template>

<script>
import cameraSetting from './cameraSetting'
import { isIP, isPort, isPortAndPort } from '@/common/libs/util'
import { mapGetters } from 'vuex'
import { communicationModeDic, communicationModeOptions, mainCabinetList, secondaryCabinetList, lockManufacturerOptions, lockManufacturerDic, createCabinetList, lightManufacturerOptions, lightManufacturerDic } from '@/common/libs/options'
export default {
	name: 'systemSettings',
	components: { cameraSetting },
	data() {
		return {
			showLoading: false,
			showErrorModal: false,
			operationInfo: {},
			cabinetList: createCabinetList(),
			configInfoList: [],
			comOptions: [],
			configLockInfo: {},
			configLightInfo: {},
			wristbandDrawerInfo: {},
			lockManufacturerOptions,
			lightManufacturerOptions,
			communicationModeDic,
			loginMethod: [],
			showModal: false,
			mainCabinetList,
			secondaryCabinetList,
			zpList: [
				{ value: '1', label: '单面柜' },
				{ value: '2', label: '双面柜-入口' },
				{ value: '3', label: '双面柜-出口' }
			],
			faceServiceList: [
				{ value: '1', label: 'GSN-人脸服务YJY款' },
				{ value: '2', label: '对接第三方' }
			],
			tabValue: '1',
			tabList: [
				{ value: '1', label: '基本信息' },
				{ value: '2', label: '模式配置' },
				{ value: '3', label: '本机信息' }
			],
			communicationModeOptions,
			formData: {},
			userTypeList: [
				{ value: '1', label: '工作人员' },
				{ value: '2', label: '非工作人员' }
			],
			loginMethodAllList: [
				{ value: '1', label: '账号' },
				{ value: '2', label: '刷脸' },
				{ value: '3', label: '刷卡' },
				{ value: '4', label: '指纹' },
				{ value: '5', label: '手势' },
				{ value: '6', label: '手环' }
			],
			secondaryCabinetModel: [],
			twoFactorAuthentication: false
		}
	},
	computed: {
		...mapGetters({
			propertyData: 'app/propertyData',
			allConfig: 'app/allConfig',
			config: 'app/config'
		}),
		loginMethodList() {
			if (this.formData.communicationMode == communicationModeDic.ONLINE) {
				return this.loginMethodAllList.filter((item) => item.value != 4)
			} else {
				return this.loginMethodAllList.filter((item) => item.value != 3 && item.value != 6)
			}
		},
		isFormValid() {
			if (this.tabValue === '1') {
				const requiredFields = [this.formData.operationTime]
				return requiredFields.every((value) => value)
			}
			if (this.tabValue === '2' && this.formData.communicationMode == communicationModeDic.ONLINE) {
				const requiredFields1 = [this.formData.serviceIp, this.formData.httpPort]
				const flag1 = requiredFields1.every((value) => value)
				if (flag1 && this.formData.mainScreenType == 3) {
					const requiredFields2 = [this.formData.mainSerialNumber]
					const flag2 = requiredFields2.every((value) => value)
					if (!flag2) {
						return flag2
					}
				}
				if (flag1 && this.formData.outsideTake) {
					const requiredFields3 = [this.formData.outsideTakeHttp, this.formData.channelId]
					const flag3 = requiredFields3.every((value) => value)
					if (!flag3) {
						return flag3
					}
				}
				return flag1
			}
			if (this.tabValue === '2' && this.formData.communicationMode == communicationModeDic.OFFLINE) {
				const requiredFields1 = [this.formData.facialThreshold, this.formData.fingerThreshold]
				const flag1 = requiredFields1.every((value) => value)
				if (flag1 && this.formData.mainScreenType != 1) {
					const requiredFields2 = [this.formData.dataAddress]
					const flag2 = requiredFields2.every((value) => value)
					if (!flag2) {
						return flag2
					}
				}
				return flag1
			}
			return true
		}
	},
	methods: {
		isLockValid() {
			const cabinetNumList = this.configLockInfo.cabinetNum.split(',')
			const lockPlateList = this.configLockInfo.lockPlate ? this.configLockInfo.lockPlate.split(';') : []
			const maxNumList = String(this.configLockInfo.maxNum).split(';')
			if (cabinetNumList.length != lockPlateList.length || cabinetNumList.length != maxNumList.length || cabinetNumList.length != this.secondaryCabinetModel.length + 1) {
				return false
			}
			// 组装数据
			const lockInfo = {}
			this.configLightInfo.address = lockPlateList[0]
			for (let j = 0; j < cabinetNumList.length; j++) {
				lockInfo[cabinetNumList[j]] = {
					isMain: j == 0,
					manufacturer: Number(this.configLockInfo.manufacturer),
					path: this.configLockInfo.path,
					baudRate: Number(this.configLockInfo.baudRate) || 9600,
					lockPlate: lockPlateList[j],
					maxNum: maxNumList[j]
				}
			}
			return lockInfo
		},
		initLockData() {
			this.getComList()
			const { lockInfo } = this.allConfig
			if (!lockInfo) {
				this.changeLock(this.mainCabinetList[0])
				return
			}
			const obj = {}
			for (const key in lockInfo) {
				const lock = lockInfo[key]
				lock.manufacturer = lock.manufacturer || lockManufacturerDic.K32_W
				if (obj[lock.manufacturer]) {
					obj[lock.manufacturer].lockPlate += lock.lockPlate ? `;${lock.lockPlate}` : ''
					obj[lock.manufacturer].maxNum += lock.maxNum ? `;${lock.maxNum}` : ''
					obj[lock.manufacturer].cabinetNum += `,${key}`
				} else {
					obj[lock.manufacturer] = { ...lock, cabinetNum: key }
				}
			}
			this.$set(this, 'configLockInfo', Object.values(obj)[0])
		},
		getComList() {
			this.$http.appApi.getSerialPortList().then((res) => {
				this.comOptions = res.data ? res.data : []
			})
		},
		isCom(manufacturer) {
			return [lockManufacturerDic.K32_S, lockManufacturerDic.J88_S, lockManufacturerDic.D99_S].includes(Number(manufacturer))
		},
		isLightCom(manufacturer) {
			return [lightManufacturerDic.K32_S, lightManufacturerDic.SELF_DEVELOP, lightManufacturerDic.D99_S].includes(Number(manufacturer))
		},
		changeCommunicationMode() {
			if (this.formData.communicationMode != communicationModeDic.ONLINE) {
				this.loginMethod = (this.loginMethod || []).filter((item) => item != 3 && item != 6)
			} else {
				this.loginMethod = (this.loginMethod || []).filter((item) => item != 4)
			}
		},
		getNumByValue(id, list) {
			const obj = list.find((item) => item.value === id)
			return obj ? obj.gf.toString() : undefined
		},
		changeMainCabinet(info) {
			this.secondaryCabinetModel = []
			// 切换主柜则自动绑定锁控
			this.changeLock(info)
			this.changeLight(info)
		},
		changeLock(info) {
			const obj = this.lockManufacturerOptions.find((item) => item.value == info[0].lockManufacturer)
			if (obj) {
				this.configLockInfo.manufacturer = obj.value
				this.configLockInfo.path = this.configLockInfo.path || obj.config.path
				this.configLockInfo.baudRate = this.configLockInfo.baudRate || obj.config.baudRate
				this.configLockInfo.lockPlate = '1'
				this.configLockInfo.cabinetNum = 'A'
				this.configLockInfo.maxNum = info[0].maxNum
			}
		},
		changeLight(info) {
			const obj = this.lightManufacturerOptions.find((item) => item.value == info[0].lightManufacturer)
			if (obj) {
				this.configLightInfo.manufacturer = obj.value
				this.configLightInfo.path = this.configLightInfo.path || obj.config.path
				this.configLightInfo.baudRate = this.configLightInfo.baudRate || obj.config.baudRate
				this.configLightInfo.address = '1'
				this.configLightInfo.door = info[0].lightDoor || String(info[0].maxNum - 0 + 1)
			}
		},
		flagSubmit(value) {
			if (this.formData.serviceIp != this.config.serviceIp || this.formData.httpPort != this.config.httpPort) {
				this.showErrorModal = true
				return
			} else {
				this.submit(value)
			}
		},
		submit(tabValue) {
			this.formData.secondaryCabinetModel = this.secondaryCabinetModel ? this.secondaryCabinetModel.join(',') : this.formData.secondaryCabinetModel
			const valueToNumMap = new Map(this.secondaryCabinetList.map((item) => [item.value, item.gf]))
			const matchedNums = this.secondaryCabinetModel.map((value) => {
				const gf = valueToNumMap.get(value)
				return gf !== undefined ? gf.toString() : 'null'
			})
			this.formData.secondaryCabinetModelNum = matchedNums.join(',')
			this.formData.mainCabinetModelNum = this.getNumByValue(this.formData.mainCabinetModel, this.mainCabinetList)
			if (this.formData.communicationMode == communicationModeDic.OFFLINE && this.formData.userType == 1) {
				this.twoFactorAuthentication = false
				this.formData.signatureSeal = false
				this.formData.locaterReturn = false
			}

			const valuesToCheck = ['1', '2']
			const containsValue = valuesToCheck.some((value) => this.loginMethod.includes(value))
			if (!containsValue) {
				return this.$baseTip.info('登录方式必须要有人脸或者账号密码!')
			}
			this.formData.loginMethod = this.loginMethod.join(',')
			this.formData.twoFactorAuthentication = this.twoFactorAuthentication ? '1' : '0'
			const params = {
				businessInfo: this.formData
			}
			let lockInfo
			if (this.tabValue == 2) {
				const ipFlag = isIP(this.formData.serviceIp)
				if (!ipFlag) return this.$baseTip.info('ip格式有误，请检查配置！')
				const portFlag = isPort(this.formData.httpPort)
				if (!portFlag) return this.$baseTip.info('端口格式有误，请检查配置！')
				const takeIpFlag = isPortAndPort(this.formData.outsideTakeHttp)
				if (this.formData.outsideTakeHttp && !takeIpFlag) return this.$baseTip.info('播放地址格式有误，请检查配置！')
				lockInfo = this.isLockValid()
				if (!lockInfo) return this.$baseTip.info('锁控配置有误，请检查配置！')
				const portAndPortFlag = isPortAndPort(this.formData.dataAddress)
				if (this.formData.communicationMode == communicationModeDic.OFFLINE && this.formData.mainScreenType != 1 && !portAndPortFlag) return this.$baseTip.info('同步地址有误，请检查配置！')
				params.lockInfo = lockInfo
				params.supplementLightInfo = this.configLightInfo
				params.wristbandDrawerInfo = this.wristbandDrawerInfo
			}
			if (this.tabValue == 1) {
				params.operationInfo = this.operationInfo
			}
			this.showLoading = true
			this.$http.appApi
				.updateConfigInfo(params)
				.then((res) => {
					this.tabValue = tabValue
					this.$baseTip.success('保存成功!')
					const { businessInfo } = res.data
					this.$store.commit('app/setConfig', businessInfo)
					this.$store.commit('app/setAllConfig', res.data)
				})
				.finally(() => {
					this.showLoading = false
				})
		},
		del(idx) {
			this.secondaryCabinetModel.splice(idx, 1)
			const cabinetNum = this.configLockInfo.cabinetNum.split(',')
			cabinetNum.pop()
			this.configLockInfo.cabinetNum = cabinetNum.join(',')
			const lockPlate = this.configLockInfo.lockPlate.split(';')
			lockPlate.pop()
			this.configLockInfo.lockPlate = lockPlate.join(';')
			const maxNum = this.configLockInfo.maxNum.split(';')
			maxNum.pop()
			this.configLockInfo.maxNum = maxNum.join(';')
		},
		addgzxh() {
			const id = this.formData.mainCabinetModel
			this.secondaryCabinetModel.push(id)
			//新增副柜的时候新增锁控的配置
			const obj = this.secondaryCabinetList.find((item) => item.value === id)
			const parts = this.configLockInfo.lockPlate.trim().split(/[;,]/)
			let lockPlateNum = Number(parts[parts.length - 1]) + 1
			if (obj.gf > obj.maxNum) {
				lockPlateNum = `${lockPlateNum},${lockPlateNum + 1}`
			}
			this.configLockInfo.lockPlate = `${this.configLockInfo.lockPlate};${lockPlateNum}`
			const lastLetter = this.configLockInfo.cabinetNum[this.configLockInfo.cabinetNum.length - 1]
			const nextLetter = String.fromCharCode(((lastLetter.charCodeAt(0) - 'A'.charCodeAt(0) + 1) % 26) + 'A'.charCodeAt(0))
			this.configLockInfo.cabinetNum = `${this.configLockInfo.cabinetNum},${nextLetter}`
			this.configLockInfo.maxNum = `${this.configLockInfo.maxNum};${obj.gf > obj.maxNum ? `${obj.maxNum},${obj.gf - obj.maxNum}` : obj.maxNum}`
		},
		changeTab(item) {
			if (this.tabValue == 1 || this.tabValue == 2) {
				if (this.isFormValid) {
					this.flagSubmit(item.value)
				} else {
					this.$baseTip.info('请输入必填项!')
				}
			} else {
				this.tabValue = item.value
			}
		}
	},
	created() {
		this.formData = JSON.parse(JSON.stringify(this.$store.state.app.config))
		this.twoFactorAuthentication = this.formData.twoFactorAuthentication === '1' ? true : false
		this.secondaryCabinetModel = this.formData.secondaryCabinetModel ? this.formData.secondaryCabinetModel.split(',') : []
		this.loginMethod = this.formData.loginMethod ? this.formData.loginMethod.split(',') : []
		this.configLightInfo = this.allConfig.supplementLightInfo
		this.operationInfo = this.allConfig.operationInfo
		this.wristbandDrawerInfo = this.allConfig.wristbandDrawerInfo
		this.initLockData()
	}
}
</script>
<style lang="less" scoped>
.form-container {
	flex: 1;
	flex-direction: column;
	display: flex;
	justify-content: space-between;
	.ui-form-box {
		flex: 1;
		padding: 0 40px;
		max-height: 1260px;
		overflow-y: auto;
		.add {
			width: 583px;
			height: 68px;
			background: #ffffff;
			border-radius: 8px 8px 8px 8px;
			border: 2px solid #337fff;
			text-align: center;
			font-size: 32px;
			color: #669eff;
			line-height: 64px;
			margin-left: 200px;
			margin-bottom: 47px;
		}
		.xh {
			width: 584px;
		}
		.text {
			font-size: 36px;
			color: #5f709a;
			line-height: 40px;
			padding-left: 26px;
			flex: 1;
		}
		.del {
			width: 43px;
			height: 43px;
			background: url('../../assets/images/settings/icon_del.png') no-repeat;
			background-size: 100% 100%;
		}
	}
	.form-submit {
		height: 256px;
		display: flex;
		justify-content: center;
		align-items: center;
		.btn {
			width: 840px;
			height: 96px;
			background: #2b5fda;
			font-size: 36px;
			color: #ffffff;
			line-height: 96px;
			text-align: center;
			&.disabled {
				background: rgba(43, 95, 218, 0.4);
			}
		}
	}
}
.camera-settings {
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(235, 245, 255, 0.9);
	z-index: 10;
}
.modal-content {
	height: 200px;
	margin-bottom: 44px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	font-size: 40px;
}
</style>
