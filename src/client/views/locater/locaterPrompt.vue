<template>
	<div class="ui-success-box">
		<div class="tips">
			<span class="red">{{ countdown }}</span>
			秒后即将返回首页
		</div>
		<img src="../../assets/images/settings/icon_success.png" alt="" />
		<div class="text">登记完成!</div>
		<div class="msg" v-if="tipsShow">
			若需归还民警卡，请使用
			<span class="bule">【归还民警卡】</span>
			功能进行归还
		</div>
	</div>
</template>
<script>
export default {
	name: 'keepLocater',
	data() {
		return {
			countdown: 5,
			timer: null,
			tipsShow: false
		}
	},
	methods: {
		goHome() {
			this.timer && clearInterval(this.timer)
			this.timer = null
			this.$router.push('home')
		}
	},
	created() {
		this.tipsShow = this.$route.params.tipsShow
	},
	mounted() {
		this.timer = setInterval(() => {
			this.countdown--
			if (this.countdown <= 0) {
				this.goHome()
			}
		}, 1000)
	},
	beforeDestroy() {
		this.timer && clearInterval(this.timer)
		this.timer = null
	}
}
</script>
<style lang="less" scoped>
.ui-success-box {
	.tips {
		height: 140px;
		line-height: 140px;
		.red {
			color: #ff5376;
		}
	}
	img {
		margin-top: 319px;
	}
	.msg {
		height: 126px;
		line-height: 140px;
		font-size: 32px;
		color: #5779b3;
		.bule {
			color: #2b5fda;
		}
	}
}
</style>
