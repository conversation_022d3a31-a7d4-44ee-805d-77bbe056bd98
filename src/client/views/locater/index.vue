<template>
	<div class="revert-locater">
		<div class="head">
			<div class="switch" v-if="showType === 'swipe'" @click="switchType('criminal')">归还手环</div>
			<div class="switch" v-if="showType === 'criminal'" @click="switchType('swipe')">归还民警卡</div>
		</div>
		<div class="verify-content">
			<div class="swipe-box" v-if="showType === 'swipe'">
				<verify-swipe ref="swipe" @changeValue="cardSubmit" @error="cardError"></verify-swipe>
			</div>
			<div class="criminal-box" v-if="showType === 'criminal'">
				<verify-criminal ref="criminal" @changeValue="cardSubmit" @error="cardError"></verify-criminal>
			</div>
		</div>
		<div class="arrow-box">
			<img src="../../assets/images/verifyHome/arrow.png" alt="" />
		</div>
		<div class="verify-tips">{{ verifyTips }}</div>
		<div class="register-box">
			<div class="text">或使用编号归还</div>
			<div class="btn" @click="goRegister()">立即归还</div>
		</div>
		<div class="type-box"></div>
		<div class="register-box"></div>
		<div class="ui-button-box">
			<ui-button @click="close()">退出归还{{ countdown > 0 ? `(${countdown})s` : '' }}</ui-button>
		</div>
		<div class="ceshi" style="flex: 1; display: flex"></div>
		<!-- 遮罩层 -->
		<div v-if="isMaskVisible" class="mask"></div>
		<keyboard-select v-if="showKeyboard" @close="showKeyboard = false" @changeValue="submit" :title="showType === 'swipe' ? '请输入民警卡编号' : '请输入手环编号'"></keyboard-select>
		<ui-modal v-model="showInfo" :title="showType == 'criminal' ? '嫌疑人信息确认' : '民警卡信息确认'">
			<div class="info-content">
				<template v-if="showType == 'criminal'">
					<div class="suspect-info-left">
						<img :src="cardInfo.faceImgUrl ? 'data:image/*;base64,' + cardInfo.faceImgUrl : suspectPhoto" alt="" />
						<div class="label">入区照片</div>
					</div>
					<div class="suspect-info-right">
						<div class="item" v-for="item in suspectKeyList" :key="item.key">
							<div class="label">{{ item.label }}</div>
							<div class="value">{{ cardInfo[item.key] }}</div>
						</div>
					</div>
				</template>
				<template v-if="showType == 'swipe'">
					<div class="police-info-left">
						<img :src="cardInfo.faceImgUrl ? 'data:image/*;base64,' + cardInfo.faceImgUrl : policePhoto" alt="" />
						<div class="label">民警照片</div>
					</div>
					<div class="police-info-right">
						<div class="item" v-for="item in policeKeyList" :key="item.key">
							<div class="label">{{ item.label }}</div>
							<div class="value">{{ cardInfo[item.key] }}</div>
						</div>
					</div>
				</template>
			</div>
			<div class="ui-modal-footer">
				<div class="btn cancel-btn" @click="hiddShowInfo">取消</div>
				<div class="btn" @click="confirm">归还</div>
			</div>
		</ui-modal>
	</div>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
	name: 'locater',
	data() {
		return {
			isMaskVisible: false,
			showType: 'criminal',
			verifyTips: '请将手环置于读取区域',
			typeList: [
				{
					label: '刷卡认证',
					id: 'swipe',
					key: '3',
					icon: require('../../assets/images/verifyHome/swipe.png')
				},
				{
					label: '手环认证',
					id: 'criminal',
					key: '6',
					icon: require('../../assets/images/verifyHome/swipe.png')
				}
			],
			countdown: 60,
			timer: null,
			showKeyboard: false,
			showInfo: false,
			cardInfo: {},
			policeKeyList: [
				{
					label: '办案民警',
					key: 'name'
				},
				{
					label: '民警警号',
					key: 'policeCode'
				},
				{
					label: '民警卡号',
					key: 'tagId'
				}
			],
			suspectKeyList: [
				{
					label: '姓名',
					key: 'name'
				},
				{
					label: '身份证号',
					key: 'idCard'
				},
				{
					label: '性别',
					key: 'genderCn'
				},
				{
					label: '民族',
					key: 'ethnicity'
				},
				{
					label: '出生日期',
					key: 'birthDate'
				}
			],
			policePhoto: require('../../assets/images/locater/police.png'),
			suspectPhoto: require('../../assets/images/locater/suspect.png')
		}
	},
	computed: {
		...mapGetters({
			config: 'app/config',
			nextRouter: 'app/nextRouter',
			userInfo: 'user/userInfo',
			nextUser: 'app/nextUser',
			useType: 'app/useType'
		})
	},
	created() {
		this.countdown = this.config.operationTime
		if (this.countdown > 0) {
			this.timer = setInterval(() => {
				this.countdown--
				if (this.countdown <= 0) {
					this.close()
				}
			}, 1000)
		}
	},
	methods: {
		switchType(showType) {
			this.countdown = this.config.operationTime
			this.showType = showType
			if (this.showType === 'swipe') {
				this.verifyTips = '请将民警卡放在感应区域'
			} else if (this.showType === 'criminal') {
				this.verifyTips = '请将手环放在感应区域'
			}
		},
		toPage(page) {
			// 这里增加遮罩层防止在接口请求过程中触发了跳转导致问题
			this.isMaskVisible = true
			setTimeout(() => {
				this.isMaskVisible = false
				this.$router.push(page)
			}, 3000)
		},
		async confirm() {
			this.isMaskVisible = true
			const map = {
				criminal: 'backCardByApply',
				swipe: 'backPoliceCards'
			}
			const param = this.showType == 'criminal' ? this.cardInfo.tagId : [{ tagId: this.cardInfo.tagId }]
			try {
				const { data = {} } = await this.$http.webApi[map[this.showType]](param)
				const params = {
					personName: this.cardInfo.name,
					policeList: this.showType == 'criminal' && data.timecard,
					personType: this.showType == 'criminal' ? 'suspect' : 'police'
				}
				this.$router.push({ name: 'keepLocater', params })
			} catch (err) {
				this.hiddShowInfo()
			}
			this.isMaskVisible = false
		},
		async submit(tagId) {
			this.isMaskVisible = true
			const map = {
				criminal: 'criminalLogin',
				swipe: 'swipeLogin'
			}
			try {
				const { data = {}, code, msg } = await this.$http.webApi[map[this.showType]]({ braceletId: tagId })
				if (code == 200) {
					this.cardInfo = data
					this.cardInfo.tagId = tagId
					this.showKeyboard = false
					this.showInfo = true
				} else {
					this.$baseTip.error(msg)
				}
			} catch (err) {}
			this.isMaskVisible = false
		},
		cardSubmit(data) {
			this.cardInfo = data
			this.cardInfo.tagId = data.tagId
			this.showKeyboard = false
			this.showInfo = true
		},
		cardError(msg) {
			this.$baseTip.error(msg)
			this.$refs.swipe && (this.$refs.swipe.loading = false)
			this.$refs.criminal && (this.$refs.criminal.loading = false)
		},
		hiddShowInfo() {
			this.showInfo = false
			this.$refs.swipe && (this.$refs.swipe.loading = false)
			this.$refs.criminal && (this.$refs.criminal.loading = false)
		},
		goRegister() {
			this.showKeyboard = true
		},
		close() {
			this.timer && clearInterval(this.timer)
			this.$router.push('home')
		}
	},
	beforeDestroy() {
		this.timer && clearInterval(this.timer)
	}
}
</script>

<style lang="less" scoped>
.mask {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
}
.info-content {
	display: flex;
	padding: 20px 0 70px 0;
	.police-info-left {
		width: 340px;
		background: #ceddf5;
		border-radius: 16px 0px 0px 16px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		img {
			width: 260px;
			height: 346px;
			border: 2px solid #91a9db;
		}
		.label {
			padding-top: 12px;
			font-size: 28px;
			color: #5f709a;
			line-height: 40px;
			text-align: right;
		}
	}
	.police-info-right {
		flex: 1;
		background: url('../../assets/images/locater/police_bg.png') no-repeat;
		background-size: 100% 100%;
		padding: 96px 0 24px 0;
		.item {
			display: flex;
			margin-bottom: 72px;
			font-size: 40px;
			color: #5f709a;
			.label {
				margin-left: 43px;
				width: 200px;
				text-align: justify;
				text-align-last: justify;
				position: relative;
				padding-right: 40px;
				&::after {
					content: ':';
					position: absolute;
					right: 28px;
				}
			}
			.value {
				flex: 1;
				color: #2b5fda;
			}
		}
	}
	.suspect-info-left {
		width: 187px;
		background: #ceddf5;
		border-radius: 16px 0px 0px 16px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		img {
			width: 152px;
			height: 187px;
			border: 2px solid #91a9db;
		}
		.label {
			padding-top: 25px;
			font-size: 28px;
			color: #5f709a;
			line-height: 40px;
			text-align: right;
		}
	}
	.suspect-info-right {
		flex: 1;
		background: url('../../assets/images/locater/suspect_bg.png') no-repeat;
		background-size: 100% 100%;
		padding: 62px 0 22px 0;
		.item {
			display: flex;
			margin-bottom: 44px;
			font-size: 40px;
			color: #5f709a;
			.label {
				margin-left: 43px;
				width: 200px;
				text-align: justify;
				text-align-last: justify;
				position: relative;
				padding-right: 40px;
				&::after {
					content: ':';
					position: absolute;
					right: 28px;
				}
			}
			.value {
				flex: 1;
				color: #2b5fda;
			}
		}
	}
}
.revert-locater {
	position: fixed;
	top: 100px;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	.head {
		width: 100%;
		height: 180px;
		display: flex;
		align-items: center;
		padding-right: 43px;
		justify-content: flex-end;
		.switch {
			width: 200px;
			height: 64px;
			background: #ffffff;
			border-radius: 80px;
			font-size: 28px;
			color: #2668ff;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 42px;
				height: 42px;
				margin-right: 5px;
			}
		}
	}
	.verify-content {
		height: 724px;
		width: 724px;
		margin-bottom: 36px;
	}
	.verify-tips {
		font-size: 42px;
		color: #2b3346;
		line-height: 55px;
		text-align: center;
		font-weight: bold;
		height: 55px;
		&.verify-tips-english {
			font-size: 30px;
		}
	}
	.arrow-box {
		height: 70px;
		display: flex;
		justify-content: center;
		img {
			width: 52px;
			height: 52px;
		}
	}
	.type-box {
		margin-top: 98px;
		margin-bottom: 26px;
		height: 188px;
		display: flex;
		.item {
			margin: 0 10px;
			width: 188px;
			height: 188px;
			background: #ffffff;
			box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15), inset 0px 3px 3px 1px rgba(48, 116, 218, 0.1);
			border-radius: 8px;
			padding-top: 25px;
			img {
				height: 102px;
				width: 100%;
			}
			.text {
				font-size: 28px;
				color: #2b60da;
				line-height: 40px;
				text-align: center;
			}
		}
	}
	.register-box {
		height: 104px;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 52px;
		.text {
			font-size: 28px;
			color: #5f709a;
		}
		.btn {
			width: 154px;
			height: 48px;
			background: #669eff;
			border-radius: 32px;
			font-size: 28px;
			color: #ffffff;
			text-align: center;
			line-height: 48px;
			margin-left: 24px;
		}
	}
	.tips {
		font-weight: normal;
		font-size: 28px;
		color: #669eff;
		line-height: 40px;
		text-align: center;
		font-style: normal;
		text-transform: none;
		margin-bottom: 130px;
	}
	.tips-img {
		position: absolute;
		top: 20px;
		left: 50%;
		transform: translateX(-50%);
	}
}
</style>
