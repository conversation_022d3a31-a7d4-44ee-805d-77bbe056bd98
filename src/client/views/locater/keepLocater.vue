<template>
	<div class="keep-locater">
		<div class="title">
			<div>
				<span class="name">{{ personName }}</span>
				{{ personType == 'suspect' ? '手环' : '民警卡' }}已释放
			</div>
			<div v-if="policeList.length">
				是否归还
				<span class="name">{{ getJsonName }}</span>
				等民警卡
			</div>
		</div>
		<div class="content">
			<img src="../../assets/images/locater/bg.png" alt="" />
		</div>
		<div class="button-box">
			<template v-if="policeList.length">
				<ui-button type="white" @click="submit">归还</ui-button>
				<ui-button @click="goPath(true)">不归还</ui-button>
			</template>
			<template v-else>
				<ui-button @click="goPath(false)">确定</ui-button>
			</template>
		</div>
		<ui-loading v-model="showLoading">正在加载</ui-loading>
	</div>
</template>
<script>
export default {
	name: 'keepLocater',
	data() {
		return {
			showLoading: false,
			personName: '',
			policeList: [],
			personType: 'personType'
		}
	},
	computed: {
		getJsonName() {
			return this.policeList.map((obj) => obj.personName).join(',')
		}
	},
	methods: {
		goPath(tipsShow = false) {
			this.$router.push({ name: 'locaterPrompt', params: { tipsShow } })
		},
		submit() {
			this.showLoading = true
			this.$http.webApi
				.backPoliceCards(this.policeList)
				.then((ret) => {
					this.goPath(false)
				})
				.finally(() => {
					this.showLoading = false
				})
		}
	},
	created() {
		const { personName = '', policeList = [], personType = 'personType' } = this.$route.params
		this.personName = personName
		this.policeList = policeList
		this.personType = personType
	}
}
</script>
<style lang="less" scoped>
.keep-locater {
	flex: 1;
	display: flex;
	flex-direction: column;
	.title {
		padding-top: 210px;
		padding-bottom: 190px;
		height: 510px;
		display: flex;
		flex-direction: column;
		justify-content: center;

		font-weight: 400;
		font-size: 40px;
		color: #2b3346;
		line-height: 62px;
		text-align: center;
		.name {
			color: #337fff;
			font-weight: bold;
		}
	}
	.content {
		flex: 1;
		text-align: center;
	}
	.button-box {
		width: 100%;
		display: flex;
		padding: 0 40px 85px 40px;
		.ui-btn {
			flex: 1;
		}
		.ui-btn + .ui-btn {
			margin-left: 40px;
		}
	}
}
</style>
