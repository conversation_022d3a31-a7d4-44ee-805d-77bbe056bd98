import Vue from 'vue'
import store from '@/client/store'
import router from '@/client/router'
import App from './App.vue'
import '@/client/assets/style/common.less'
import '@/client/assets/style/reset.less'
import http from '@/client/apiPack/index'
import components from './components'
import VueI18n from 'vue-i18n'
import en from '../common/language/en'
import zh from '../common/language/zh'
import '@/client/directive/index'
Vue.use(VueI18n)

const i18n = new VueI18n({
	locale: 'zh',
	messages: {
		zh,
		en
	}
})

Vue.use(components)
Vue.prototype.$http = http

new Vue({
	store,
	router,
	i18n,
	render: (h) => h(App)
}).$mount('#app')
