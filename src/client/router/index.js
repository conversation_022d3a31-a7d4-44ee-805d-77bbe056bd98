import Vue from 'vue'
import VueRouter from 'vue-router'
import { publicPath, routerMode } from '@/config'

Vue.use(VueRouter)
const router = new VueRouter({
	base: publicPath,
	mode: routerMode,
	scrollBehavior: () => ({
		y: 0
	}),
	routes: [
		{
			path: '/home',
			name: 'home',
			meta: {
				title: '首页'
			},
			component: (resolve) => require(['../views/home/<USER>'], resolve)
		},
		{
			path: '/storageSituation',
			name: 'storageSituation',
			meta: {
				showTitle: true
			},
			component: (resolve) => require(['../views/storageSituation/index'], resolve)
		},
		{
			path: '/storageSituationDetail',
			name: 'storageSituationDetail',
			meta: {
				showTitle: true
			},
			component: (resolve) => require(['../views/storageSituation/storageSituationDetail'], resolve)
		},
		{
			path: '/selectItems',
			name: 'selectItems',
			component: (resolve) => require(['../views/selectItems/index'], resolve),
			meta: {
				showTitle: true
			}
		},
		{
			path: '/animationTips',
			name: 'animationTips',
			component: (resolve) => require(['../views/animationTips/index'], resolve)
		},
		{
			path: '/onlyOpenAnimationTips',
			name: 'onlyOpenAnimationTips',
			component: (resolve) => require(['../views/animationTips/onlyOpenAnimationTips'], resolve)
		},
		{
			path: '/settings',
			name: 'settings',
			meta: {
				showTitle: true
			},
			component: (resolve) => require(['../views/settings/index'], resolve)
		},
		{
			path: '/accessRecords',
			name: 'accessRecords',
			meta: {
				title: '存取记录',
				showTitle: true
			},
			component: (resolve) => require(['../views/settings/accessRecords'], resolve)
		},
		{
			path: '/systemSettings',
			name: 'systemSettings',
			meta: {
				title: '系统配置',
				showTitle: true
			},
			component: (resolve) => require(['../views/settings/systemSettings'], resolve)
		},
		{
			path: '/personRegister',
			name: 'personRegister',
			meta: {
				title: '人员注册',
				showTitle: true
			},
			component: (resolve) => require(['../views/settings/personRegister'], resolve)
		},
		{
			path: '/personManage',
			name: 'personManage',
			meta: {
				title: '人员管理',
				showTitle: true
			},
			component: (resolve) => require(['../views/settings/personManage'], resolve)
		},
		{
			path: '/improveInformation',
			name: 'improveInformation',
			meta: {
				title: '完善信息',
				showTitle: true
			},
			component: (resolve) => require(['../views/settings/improveInformation'], resolve)
		},
		{
			path: '/verifyHome',
			name: 'verifyHome',
			component: (resolve) => require(['../views/verifyHome/index'], resolve)
		},
		{
			path: '/verifyAccount',
			name: 'verifyAccount',
			component: (resolve) => require(['../views/verifyHome/account'], resolve)
		},
		{
			path: '/verifyRegister',
			name: 'verifyRegister',
			meta: {
				title: '嫌疑人注册',
				showTitle: true
			},
			component: (resolve) => require(['../views/verifyHome/verifyRegister'], resolve)
		},
		{
			path: '/backTips',
			name: 'backTips',
			component: (resolve) => require(['../views/animationTips/backTips'], resolve)
		},
		{
			path: '/closeTips',
			name: 'closeTips',
			component: (resolve) => require(['../views/animationTips/closeTips'], resolve)
		},
		{
			path: '/locater',
			name: 'locater',
			component: (resolve) => require(['../views/locater/index'], resolve)
		},
		{
			path: '/keepLocater',
			name: 'keepLocater',
			component: (resolve) => require(['../views/locater/keepLocater'], resolve),
			meta: {
				showTitle: true
			}
		},
		{
			path: '/locaterPrompt',
			name: 'locaterPrompt',
			component: (resolve) => require(['../views/locater/locaterPrompt'], resolve)
		},
		{
			path: '/',
			redirect: '/home'
		}
	]
})

router.beforeEach((to, from, next) => {
	next()
})

router.afterEach(() => {
	window.scrollTo(0, 0)
})

export default router
