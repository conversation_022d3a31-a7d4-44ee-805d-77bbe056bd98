// 表单
.ui-form-box {
	.ui-form-item {
		margin-bottom: 47px;
		height: 100px;
		display: flex;
		align-items: center;
		.label {
			min-width: 200px;
			height: 52px;
			font-size: 40px;
			color: #5f709a;
			text-align: justify;
			text-align-last: justify;
			position: relative;
			padding-right: 40px;
			&::after {
				content: ':';
				position: absolute;
				right: 28px;
			}
		}
		.value {
			width: 800px;
			display: flex;
			align-items: center;
		}
        
	}
    &.label-font-35{
        .ui-form-item {
            .label {
                font-size: 32px;
            }
        }
    }
}
.ui-form-submit {
    height: 166px;
    display: flex;
    justify-content: center;
    align-items: center;
    .confirm-btn {
        width: 480px;
        height: 96px;
        background: #2b5fda;
        font-size: 36px;
        color: #ffffff;
        line-height: 96px;
        text-align: center;
        &.disabled {
            background: rgba(43, 95, 218, 0.4);
        }
    }
    .cancel-btn {
        width: 480px;
        height: 96px;
        background: #ffffff;
        border: 3px solid #2b5fda;
        font-size: 36px;
        color: #2b5fda;
        line-height: 96px;
        text-align: center;
        margin-right: 40px;
    }
}
// 弹窗
.ui-modal-footer {
    display: flex;
    .btn {
        flex: 1;
        height: 96px;
        background: #2b5fda;
        border-radius: 8px;
        font-size: 40px;
        color: #ffffff;
        line-height: 96px;
        text-align: center;
        &.cancel-btn{
            background: #EBF5FF;
            border: 3px solid #2B5FDA;
            color: #2B5FDA;
        }
    }
    .btn + .btn{
        margin-left: 20px;
    }
}
// 标签
.ui-tabs-border{
    display: flex;
    .tab {
        height: 80px;
        font-weight: 400;
        font-size: 38px;
        color: #5f709a;
        text-align: center;
        margin: 0 40px;
        border-bottom: 8px solid transparent;
        line-height: 72px;
        &.active {
            font-weight: bold;
            font-size: 42px;
            color: #2b5fda;
            border-bottom: 8px solid #2b5fda;
        }
    }
}
.ui-tabs-block{
    display: flex;
    height: 74px;
    background: #d9e9ff;
    margin: 0 40px 70px 40px;
    .tab {
        flex: 1;
        font-size: 36px;
        color: #3663b3;
        line-height: 74px;
        text-align: center;
        &.active {
            background: #669eff;
            border-radius: 8px;
            color: #fff;
        }
    }
}

// 保存成功
.ui-success-box{
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    img {
        width: 224px;
        height: 224px;
        margin-top: 225px;
        margin-bottom: 120px;
    }
    .text {
        font-size: 48px;
        color: #2b3346;
        line-height: 48px;
        font-weight: bold;
    }
    .tips {
        font-size: 32px;
        color: #5779b3;
        line-height: 112px;
    }
}

// 按钮盒子
.ui-button-box {
    width: 100%;
    display: flex;
    padding: 0 120px 80px 120px;
    .ui-btn{
        flex: 1;
    }
    .ui-btn + .ui-btn{
        margin-left: 20px;
    }
}