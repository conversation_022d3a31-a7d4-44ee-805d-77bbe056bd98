.text-english{
	letter-spacing: 0px!important
}
* {
	margin: auto;
	padding: 0;
	/*立体滚动条的颜色*/
	&:focus {
		outline: none;
	}
}

/*input提示文字颜色*/
input::placeholder,textarea::placeholder{
	color:#68778c;
}
html,
body {
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0);
}
body {
	font-size: 16px;
	color: #2b3346;
	line-height: 1;
}

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video,
input,
textarea,
button {
	margin: 0;
	padding: 0;
	border: 0;
	// vertical-align: baseline;
	font-family: 'Microsoft Yahei',sans-serif;
	box-sizing: border-box;
	outline: 0;
	text-decoration: none;
	user-select: none;
	-webkit-tap-highlight-color: transparent; /* 禁用触摸高亮 */
	touch-action: manipulation; /* 优化触摸行为 */
}
button:focus{
	outline: none;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
	display: block;
}

ol,
ul {
	list-style: none;
}

blockquote,
q {
	quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
	content: '';
	content: none;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}
