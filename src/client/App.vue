<template>
	<div id="app">
		<ipc-renderer></ipc-renderer>
		<page-header></page-header>
		<page-title></page-title>
		<router-view></router-view>
		<ui-loading v-model="showLoading">正在下载安装中请稍后！</ui-loading>
		<ui-modal v-model="upgradeModal">
			<div class="app-modal-content">检测到新版本，是否立即升级？</div>
			<div class="ui-modal-footer">
				<div class="btn cancel-btn" @click="upgradeModal = false">稍后</div>
				<div class="btn" @click="upgradeApp()">更新</div>
			</div>
		</ui-modal>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import IpcRenderer from '@/client/components/ipc_renderer/index'
import { communicationModeDic } from '@/common/libs/options'
// import onlineSocket from './libs/onlineSocket'
import { versionCompare } from '@/common/libs/util'
export default {
	name: 'App',
	data() {
		return {
			timer: null,
			showLoading: false,
			upgradeModal: false
		}
	},
	components: {
		IpcRenderer
	},
	computed: {
		...mapGetters({
			allConfig: 'app/allConfig',
			propertyData: 'app/propertyData',
			config: 'app/config'
		}),
		heartBeatFlag() {
			return this.propertyData?.ip && this.propertyData?.mac && this.allConfig?.version && this.allConfig?.backHttpServerPort && this.config?.communicationMode == communicationModeDic.ONLINE
		}
	},
	watch: {
		heartBeatFlag(val) {
			this.timer && clearTimeout(this.timer)
			this.timer = null
			if (val) {
				this.sendHeartBeat()
				this.detection()
				// this.initOnlineSocket({ flag: true })
			} else {
				// this.initOnlineSocket()
			}
		}
	},
	methods: {
		initOnlineSocket(params) {
			this.$http.appApi.connectionSocket(params)
		},
		sendHeartBeat() {
			const type = this.propertyData.sysArch == 'arm64' ? 'chc-cupboard-arm' : this.propertyData.sysArch == 'x64' ? 'chc-cupboard-uos' : 'chc-cupboard-win'
			const params = {
				curVersion: this.allConfig.version,
				mcuIp: this.propertyData.ip,
				mcuPort: this.allConfig.backHttpServerPort,
				serialNumber: this.propertyData.oldMac,
				type
			}
			this.$http.webApi.postHeartBeat(params).then((res) => {
				const data = res.data || {}
				if (data.apkUrl && data.version && this.$route.name == 'home') {
					const compareRet = versionCompare(this.allConfig.version, data.version)
					if (compareRet && compareRet > 0 && !this.showLoading) {
						this.$http.appApi.upgradeApp({ url: data.apkUrl }).then(() => {
							this.showLoading = true
							this.upgradeModal = false
						})
					}
				}
				this.timer = setTimeout(() => {
					this.sendHeartBeat()
				}, 60 * 1000)
			})
		},
		detection() {
			this.$http.webApi.getLatestVersion().then((res) => {
				this.upgradeData = res.data || {}
				if (this.upgradeData.versionNumber && this.upgradeData.fileDir) {
					const compareRet = versionCompare(this.allConfig.version, this.upgradeData.versionNumber)
					if (compareRet && compareRet > 0 && !this.showLoading) {
						this.upgradeModal = true
					}
				}
			})
		},
		upgradeApp() {
			this.$http.appApi.upgradeApp({ url: this.upgradeData.fileDir }).then((res) => {
				this.upgradeModal = false
				this.showLoading = true
			})
		}
	}
}
</script>
<style lang="less" scoped>
#app {
	width: 1080px;
	height: 1920px;
	display: flex;
	flex-direction: column;
	background: #ebf5ff;
	overflow: hidden;
}
.app-modal-content {
	height: 200px;
	margin-bottom: 44px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	font-size: 40px;
}
</style>
