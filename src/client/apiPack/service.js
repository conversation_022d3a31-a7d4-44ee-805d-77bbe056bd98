import axios from 'axios'
import store from '../store'
import qs from 'qs'
import { uiMessage } from '../components'
import { successCode, requestTimeout, baseURL, invalidCode, noPermissionCode } from '@/config'
import { communicationModeDic } from '@/common/libs/options'
const handleCode = (code, msg) => {
	switch (code) {
		case invalidCode:
		case noPermissionCode:
			uiMessage.error(msg || `接口${code}异常`)
			break
		default:
			uiMessage.error(msg || `接口${code}异常`)
			break
	}
}

const service = axios.create({
	baseURL,
	timeout: requestTimeout
})

service.interceptors.request.use(
	(config) => {
		const token = store.getters['user/token']
		const { communicationMode } = store.getters['app/config']
		token && (config.headers['Authorization'] = `${communicationMode == communicationModeDic.ONLINE ? 'bearer' : ''}${token}`)
		config.headers['auth-key'] = 'app'
		return config
	},
	(error) => {
		return Promise.reject(error)
	}
)

service.interceptors.response.use(
	(response) => {
		const { data } = response
		if (response.config.jumpMessage) {
			return data
		}
		if (successCode.includes(data.code)) {
			return data
		} else {
			handleCode(data.code, data.msg)
			return Promise.reject(data.msg)
		}
	},
	(error) => {
		const { response, message } = error
		if (error.response && error.response.data) {
			const { status, data } = response
			handleCode(status, data.msg || message)
		} else {
			let { message } = error
			if (message === 'Network Error') {
				message = '接口连接异常'
			}
			if (message.includes('timeout')) {
				message = '接口请求超时'
			}
			if (message.includes('Request failed with status code')) {
				const code = message.substr(message.length - 3)
				message = `接口${code}异常`
			}
			uiMessage.error(message || `接口未知异常`)
		}
		return Promise.reject(error)
	}
)

export default {
	get(url, params = {}) {
		return service({
			method: 'get',
			url,
			params
		})
	},
	postJson(url, params, jumpMessage) {
		return service({
			headers: {
				'Content-Type': 'application/json'
			},
			method: 'post',
			url,
			data: params,
			jumpMessage
		})
	},
	post(url, params) {
		return service({
			method: 'post',
			url,
			data: qs.stringify(params)
		})
	},
	newPost(url, params, jumpMessage) {
		return service({
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
			},
			method: 'post',
			url,
			data: qs.stringify(params),
			jumpMessage
		})
	},
	delete(url, params) {
		return service({
			method: 'delete',
			url,
			params
		})
	}
}
