import service from './service'
import store from '../store'
const config = store.state.app.config
const getBasePath = () => `http://${config.serviceIp}:${config.httpPort}/thirdService/v2/cabinet`
//人脸登录
const loginFaceImageThird = (params) => {
	const combinedParams = {
		type: 'faceImage',
		faceImage: params.faceImage
	}
	return service.newPost(`${getBasePath()}/authentication`, combinedParams)
}
//账号密码登录
const loginPasswordThird = (params) => {
	const combinedParams = {
		type: 'password',
		password: params.password,
		username: params.loginId
	}
	return service.newPost(`${getBasePath()}/authentication`, combinedParams)
}
//ic卡登录
const loginCardThird = (params) => {
	const combinedParams = {
		type: 'card',
		cardId: params.cardId
	}
	return service.newPost(`${getBasePath()}/authentication`, combinedParams)
}
//获取存取记录
const getRecordsThird = (params) => {
	const combinedParams = params
	return service.get(`${getBasePath()}/getRecords`, combinedParams)
}
//获取使用中柜格列表
const getIscdsUserListThird = (params) => {
	const combinedParams = params
	return service.get(`${getBasePath()}/getIscdsUserList`, combinedParams)
}
//获取使用中柜格使用情况
const getIscdsUserNumThird = (params) => {
	const combinedParams = params
	return service.get(`${getBasePath()}/getIscdsUserNum`, combinedParams)
}
//获取人员关联物品列表
const getGoodListThird = (params) => {
	const combinedParams = params
	return service.get(`${getBasePath()}/getGoodList`, combinedParams)
}
//心跳接口
const heartBeatThird = (params) => {
	return service.postJson(`${getBasePath()}/heartBeat`, params)
}
//获取token
const getTokenThird = (params) => {
	return service.get(`${getBasePath()}/getToken`, params)
}
//根据身份证查找关联嫌疑人
const getSelPersonListThird = async (params) => {
	return service.get(`${getBasePath()}/selPersonList`, params)
}
// 物品操作（临时借出+临时归还+物品存入+物品取出）
const updateItemThird = async (params) => {
	delete params.executeType
	return service.postJson(`${getBasePath()}/updateItem`, params)
}
// 保存新版随身物品柜签名
const updateSignMsgThird = async (params) => {
	delete params.isTackOut
	return service.postJson(`${getBasePath()}/updateSignMsg`, params)
}
// 获取新版随身物品柜签名
const getSignMsgThird = async (params) => {
	return service.get(`${getBasePath()}/getSignMsg`, params)
}
//获取柜子配置信息
const boxConfigThird = async (params) => {
	return service.postJson(`${getBasePath()}/boxConfig`, params)
}

export { boxConfigThird, getSignMsgThird, updateSignMsgThird, updateItemThird, getSelPersonListThird, getTokenThird, heartBeatThird, loginFaceImageThird, loginPasswordThird, loginCardThird, getRecordsThird, getIscdsUserListThird, getIscdsUserNumThird, getGoodListThird }
