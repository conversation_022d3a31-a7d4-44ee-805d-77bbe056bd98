import service from './service'
import store from '../store'
import { SM3 } from 'gm-crypto'
const config = store.state.app.config
function parseStringToArray(input) {
	const lockInfo = store.state.app.allConfig.lockInfo
	let name = 'A'
	let door = input
	let oldNum = 0
	for (const key in lockInfo) {
		const lock = lockInfo[key]
		const sum = lock.maxNum.split(',').reduce((accumulator, currentValue) => {
			return accumulator + parseInt(currentValue, 10)
		}, 0)
		if (door > sum + oldNum) {
			oldNum += sum
		} else {
			door = door - oldNum
			name = key
			break
		}
	}
	const result = name + door.toString().padStart(2, '0')
	return result
}
const getBasePath = () => `http://${config.serviceIp}:${config.httpPort}`
//客户端模式获取Token
const getClientCredentialOnline = (params) => service.newPost(`${getBasePath()}/smart/api/boxs/getToken`, params)
//登录
const loginOnline = async (params, type) => {
	let combinedParams = {}
	if (type === 'face') {
		combinedParams = {
			executeType: 'face',
			faceImg: [params.base64.slice('data:image/jpeg;base64,'.length)]
		}
	} else if (type === 'password') {
		combinedParams = {
			executeType: 'password',
			passWord: SM3.digest(params.password, 'utf8', 'base64'),
			userName: params.loginId
		}
	} else if (type === 'criminal') {
		//手环
		combinedParams = {
			executeType: 'criminal',
			braceletId: params.braceletId
		}
	} else if (type === 'police') {
		//民警卡
		combinedParams = {
			executeType: 'police',
			braceletId: params.braceletId
		}
	}
	try {
		const onlineInfo = await service.postJson(`${getBasePath()}/smart/api/selPersonMsg`, combinedParams, type != 'password' ? 'jumpMessage' : null)
		if (!onlineInfo.data || !onlineInfo.data.personType) {
			return new Promise((resolve, reject) => {
				resolve(onlineInfo)
			})
		}
		const data = onlineInfo.data[onlineInfo.data.personType]
		const info = {
			id: data.magicId || data.centerMagicId,
			name: data.name || data.userName,
			userType: onlineInfo.data.personType == 'suspect' ? '3' : data.isManage == 'Y' ? '1' : '2',
			idCard: onlineInfo.data.personType == 'suspect' ? data.identityType == 'id_card' && data.identityNum : data.idCard,
			associatedPoliceId: onlineInfo.data.personType == 'suspect' && data.policeIdCard,
			genderCn: data.genderCn,
			ethnicity: data.ethnicity,
			birthDate: data.birthDate,
			faceImgUrl: onlineInfo.data.personType == 'suspect' ? data.faceImgUrl : data.headImage,
			policeCode: data.policeCode
		}
		const res = {
			code: 200,
			data: type === 'face' ? [info] : info
		}
		return new Promise((resolve, reject) => {
			resolve(res)
		})
	} catch (error) {
		throw error
	}
}
//心跳接口
const postHeartBeatOnline = (params) => {
	return service.postJson(`${getBasePath()}/smart/api/heart/beat`, params)
}
//获取柜子使用情况统计
const getIscdsUserNumOnline = async () => {
	const combinedParams = {
		serialNumber: store.state.app.propertyData.mac
	}
	const info = await service.postJson(`${getBasePath()}/smart/api/total/info`, combinedParams)
	const { boxNum, disabledNum, takenNum, freeBoxNum } = info.data || {}
	const res = {
		data: {
			allNum: boxNum,
			faultNum: disabledNum,
			userNum: takenNum,
			freeNum: freeBoxNum
		}
	}
	return new Promise((resolve, reject) => {
		resolve(res)
	})
}

//获取柜子使用情况
const getSelGoodsMsgOnline = async () => {
	const combinedParams = {
		serialNumber: store.state.app.propertyData.mac
	}
	const goodsMsg = await service.get(`${getBasePath()}/smart/api/selGoodsMsg`, combinedParams)
	const data = goodsMsg.data.boxs
	const filteredAndTransformedData = data
		.filter((item) => item.userState === 'Y' || item.userState === 'P') // 过滤 userState 为 "Y" 的项
		.map((item) => ({
			associatedPoliceId: store.state.user.userInfo.idCard == item.policeIdCard ? store.state.user.userInfo.id : '',
			policeIdCard: item.policeIdCard,
			faceImg: item.faceImage ? `data:image/png;base64,${item.faceImage}` : '',
			id: item.id,
			isDeleted: item.isDeleted,
			sidesName: item.inboxNum ? parseStringToArray(item.inboxNum) : '-',
			num: item.inboxNum ? parseStringToArray(item.inboxNum) : '-',
			status: item.lendNum > 0 ? 'lsjc' : null,
			userId: item.personMagicId,
			userName: item.name,
			userType: '3',
			isForbidden: item.userState == 'P'
		}))
	const res = {
		data: filteredAndTransformedData
	}
	return new Promise((resolve, reject) => {
		resolve(res)
	})
}

//根据身份证查找关联嫌疑人
const getSelPersonListOnline = async (params) => {
	params.serialNumber = store.state.app.propertyData.mac
	return service.get(`${getBasePath()}/smart/api/selPersonList`, params)
}

//根据嫌疑人查找物品关联
const findPersongoodsOnline = async (params) => {
	params.serialNumber = store.state.app.propertyData.mac
	return service.postJson(`${getBasePath()}/smart/api/find/persongoods`, params)
}

// 物品操作（临时借出+临时归还+物品存入+物品取出）
const temporaryUpdateItemOnline = async (params) => {
	return service.postJson(`${getBasePath()}/smart/api/temporary/updateItem`, params)
}

// 保存新版随身物品柜签名
const personSignMsgOnline = async (params) => {
	return service.postJson(`${getBasePath()}/smart/api/person/signMsg`, params)
}

//获取记录
const getOperateOnline = async (params) => {
	const combinedParams = {
		arkMagicId: store.state.app.onlineConfig.magicId,
		operateUserType: params.userType == '3' ? '1' : params.userType == '2' ? '0,2' : '',
		startTime: params.startTime ? `${params.startTime}:00` : '',
		endTime: params.endTime ? `${params.endTime}:59` : '',
		operateUserName: params.operatorName,
		current: params.page,
		size: params.size,
		isSort: true
	}
	combinedParams.accessTypes = params.operatorType ? params.operatorType.split(',') : []
	const a = await service.postJson(`${getBasePath()}/smart/api/getOperate`, combinedParams)
	const filteredAndTransformedData = a.data.records.map((item) => ({
		openTime: item.openTime ? item.openTime.slice(0, -3) : '', //开柜时间
		closeTime: item.closeTime ? item.closeTime.slice(0, -3) : '', //关柜时间
		operatorName: item.operateUserName, //操作人
		operatorType: item.accessTypeCn, //操作类型
		userType: item.userType, //操作人类型
		userName: item.name, //柜子关联人姓名
		sidesName: item.inboxNum ? parseStringToArray(item.inboxNum) : ''
	}))

	const res = {
		data: {
			data: filteredAndTransformedData,
			total: a.data.total
		}
	}
	return new Promise((resolve, reject) => {
		resolve(res)
	})
}

//查看签名
const getWithsignOnline = async (personMagicId) => {
	return service.get(`${getBasePath()}/smart/api/withsign/${personMagicId}`)
}

//获取柜子配置信息
const postBoxConfigOnline = async (params) => {
	return service.postJson(`${getBasePath()}/smart/api/box/config`, params)
}

//归还手环
const backCardByApplyOnline = async (tagId) => {
	return service.get(`${getBasePath()}/smart/api/backCardByApply/${tagId}`)
}

//批量归还民警卡
const backPoliceCardsOnline = async (params) => {
	return service.postJson(`${getBasePath()}/smart/api/backPoliceCards`, params)
}

//归还民警卡
const backCardsOnline = async (tagId) => {
	return service.get(`${getBasePath()}/smart/api/backCard/${tagId}`)
}

//获取后台系统时间
const getLocalDateTimeOnline = async (params) => {
	return service.post(`${getBasePath()}/smart/api/getLocalDateTime`, params)
}

//查看最新终端版本
const getLatestVersionOnline = async () => {
	const sysArch = store.state.app.propertyData.sysArch
	const appType = sysArch == 'arm64' ? 'chc-cupboard-arm' : sysArch == 'x64' ? 'chc-cupboard-uos' : 'chc-cupboard-win'
	const params = {
		appType
	}
	return service.get(`${getBasePath()}/smart/api/latest/version`, params)
}
//查询是否能做对应操作
const isCanOpenDoorOnline = async (params) => {
	return service.postJson(`${getBasePath()}/smart/api/isCanOpenDoor`, params)
}

//远程开柜的时候，提交一个记录给后端拿关柜时间
const postOperateOnline = async (params) => {
	return service.postJson(`${getBasePath()}/smart/api/operate`, params)
}

//获取字典
const dicTypeOnline = async (type) => {
	return service.get(`${getBasePath()}/tablet/dic/type/${type}`)
}

export { dicTypeOnline, backCardsOnline, postOperateOnline, isCanOpenDoorOnline, getLatestVersionOnline, getLocalDateTimeOnline, backPoliceCardsOnline, backCardByApplyOnline, postBoxConfigOnline, getWithsignOnline, getOperateOnline, getClientCredentialOnline, loginOnline, getIscdsUserNumOnline, postHeartBeatOnline, getSelGoodsMsgOnline, getSelPersonListOnline, findPersongoodsOnline, temporaryUpdateItemOnline, personSignMsgOnline }
