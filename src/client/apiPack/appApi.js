import service from './service'
const BASE_PATH = `/api`
const appApi = {
	// 获取配置信息
	getConfigInfo: (params) => {
		return service.get(`${BASE_PATH}/app/getTerminalConfigInfo`, params)
	},
	// 更新配置信息
	updateConfigInfo: (params) => {
		return service.postJson(`${BASE_PATH}/app/setTerminalConfigInfo`, params)
	},
	// 获取终端和app 部分属性信息
	getSystemProperties(params) {
		return service.get(`${BASE_PATH}/system/getSystemProperties`, params)
	},
	// 获取所有柜子(大类) // 根据配置信息的参数自动生成的
	getSidesAllList: (params) => {
		return service.get(`${BASE_PATH}/business/getSidesAllList`, params)
	},
	// 获取串口列表
	getSerialPortList: (params) => {
		return service.get(`${BASE_PATH}/hardware/getSerialPortList`, params)
	},
	// 调用键盘
	openSoftKeyboard: (params) => {
		return service.get(`${BASE_PATH}/hardware/openSoftKeyboard`, params)
	},
	// 关闭终端
	closeApp: (params) => {
		return service.get(`${BASE_PATH}/app/closeApp`, params)
	},
	// 同步数据
	synchronizeAllData: (params) => {
		return service.get(`${BASE_PATH}/business/synchronizeAllData`, params)
	},
	// 改变线上模式的socket连接状态
	connectionSocket: (params) => {
		return service.get(`${BASE_PATH}/business/connectionSocket`, params)
	},
	// 根据url更新包
	upgradeApp: (params) => {
		return service.postJson(`${BASE_PATH}/app/upgradeApp`, params)
	}
}
export default appApi
