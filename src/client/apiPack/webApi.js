import store from '../store'
import { communicationModeDic } from '@/common/libs/options'
const isOffline = () => store.state.app.config.communicationMode != communicationModeDic.ONLINE
const isThird = () => store.state.app.config.thirdFlag
import { dicTypeOnline, backCardsOnline, backPoliceCardsOnline, backCardByApplyOnline, postOperateOnline, isCanOpenDoorOnline, getLatestVersionOnline, getLocalDateTimeOnline, postBoxConfigOnline, getWithsignOnline, getOperateOnline, personSignMsgOnline, temporaryUpdateItemOnline, loginOnline, getClientCredentialOnline, getIscdsUserNumOnline, postHeartBeatOnline, getSelGoodsMsgOnline, getSelPersonListOnline, findPersongoodsOnline } from './onlineApi'
import { boxConfigThird, getSignMsgThird, updateSignMsgThird, updateItemThird, getSelPersonListThird, getTokenThird, heartBeatThird, loginFaceImageThird, loginPasswordThird, loginCardThird, getRecordsThird, getIscdsUserListThird, getIscdsUserNumThird, getGoodListThird } from './thirdApi'
import { synchronizePersonnelOffline, fingerCompareManyByFingerFeatureOffline, faceCompareManyByBase64Offline, getFaceDetectInfoOffline, userPageListOffline, loginOffline, userRegisterOffline, userBatchDeleteOffline, findInfoByIdOffline, userUpdateOffline, gestureLoginOffline, addAccessRecordsOffline, getAccessRecordsOffline, delIscdsUserListOffline, updateIscdsUserStatusOffline, getIscdsUserListOffline, getIscdsUserNumOffline, addIscdsUserListOffline } from './offlineApi'

const webApi = {
	//登录
	login(params) {
		return params.loginId === 'gly' ? loginOffline(params) : isOffline() ? loginOffline(params) : isThird() ? loginPasswordThird(params) : loginOnline(params, 'password')
	},
	//指纹登录
	fingerCompareManyByFingerFeature(params) {
		return isOffline() ? fingerCompareManyByFingerFeatureOffline(params) : Promise.resolve()
	},
	//人员注册
	userRegister(params) {
		return isOffline() ? userRegisterOffline(params) : Promise.resolve()
	},
	//人员列表
	userPageList(params) {
		return isOffline() ? userPageListOffline(params) : Promise.resolve()
	},
	// 人员删除
	userBatchDelete(params) {
		return isOffline() ? userBatchDeleteOffline(params) : Promise.resolve()
	},
	// 人员详情
	findInfoById(params) {
		return isOffline() ? findInfoByIdOffline(params) : Promise.resolve()
	},
	// 人员修改
	userUpdate(params) {
		return isOffline() ? userUpdateOffline(params) : Promise.resolve()
	},
	//用户柜格绑定批量删除
	delIscdsUserList(params) {
		return isOffline() ? delIscdsUserListOffline(params) : Promise.resolve()
	},
	//新增用户柜格关联
	addIscdsUserList(params) {
		return isOffline() ? addIscdsUserListOffline(params) : Promise.resolve()
	},
	//新增存取记录
	addAccessRecords(params) {
		return addAccessRecordsOffline(params)
	},
	//修改使用柜子列表的状态
	updateIscdsUserStatus(params) {
		return isOffline() ? updateIscdsUserStatusOffline(params) : Promise.resolve()
	},
	synchronizePersonnel(params) {
		return isOffline() ? synchronizePersonnelOffline(params) : Promise.resolve()
	},

	// 都有的
	//手势登录
	gestureLogin(params) {
		return gestureLoginOffline(params)
	},
	//人脸识别-获取人脸检测信息
	getFaceDetectInfo(params) {
		return getFaceDetectInfoOffline(params)
	},
	//存取记录
	getAccessRecords(params) {
		return isOffline() ? getAccessRecordsOffline(params) : isThird() ? getRecordsThird(params) : getOperateOnline(params)
	},
	// 获取所有使用中的柜格
	getIscdsUserList(params) {
		return isOffline() ? getIscdsUserListOffline(params) : isThird() ? getIscdsUserListThird(params) : getSelGoodsMsgOnline()
	},
	// 获取柜格使用情况
	getIscdsUserNum(params) {
		return isOffline() ? getIscdsUserNumOffline(params) : isThird() ? getIscdsUserNumThird(params) : getIscdsUserNumOnline(params)
	},
	//人脸识别-人脸对比
	faceCompareManyByBase64(params) {
		return isOffline() ? faceCompareManyByBase64Offline(params) : isThird() ? loginFaceImageThird(params) : loginOnline(params, 'face')
	},

	// 在线特有
	// 在线心跳接口
	postHeartBeat(params) {
		return isThird() ? heartBeatThird(params) : postHeartBeatOnline(params)
	},
	//客户端模式获取Token
	getClientCredential(params) {
		return isThird() ? getTokenThird(params) : getClientCredentialOnline(params)
	},
	//根据身份证查找关联嫌疑人
	getSelPersonList(params) {
		return isThird() ? getSelPersonListThird(params) : getSelPersonListOnline(params)
	},
	//客户端模式根据嫌疑人查找关联物品
	findPersongoods(params) {
		return isThird() ? getGoodListThird(params) : findPersongoodsOnline(params)
	},
	//客户端模式 物品操作（临时借出+临时归还+物品存入+物品取出）
	temporaryUpdateItem(params) {
		return isThird() ? updateItemThird(params) : temporaryUpdateItemOnline(params)
	},
	//保存签名
	personSignMsg(params) {
		return isOffline() ? Promise.resolve() : isThird() ? updateSignMsgThird(params) : personSignMsgOnline(params)
	},
	//查看签名
	getWithsign(params) {
		return isOffline() ? Promise.resolve() : isThird() ? getSignMsgThird(params) : getWithsignOnline(params)
	},
	//客户端模式 配置柜子信息
	postBoxConfig(params) {
		return isThird() ? boxConfigThird(params) : postBoxConfigOnline(params)
	},
	//民警卡登录
	swipeLogin(params) {
		return isOffline() ? Promise.resolve() : isThird() ? loginCardThird(params) : loginOnline(params, 'police')
	},
	//手环登录
	criminalLogin(params) {
		return isOffline() ? Promise.resolve() : isThird() ? loginCardThird(params) : loginOnline(params, 'criminal')
	},
	//获取服务时间
	getLocalDateTime() {
		return isThird() ? Promise.resolve() : getLocalDateTimeOnline()
	},
	// 最新版本
	getLatestVersion() {
		return isThird() ? Promise.resolve() : getLatestVersionOnline()
	},
	//是否能开柜
	isCanOpenDoor(params) {
		return isThird() ? Promise.resolve() : isCanOpenDoorOnline(params)
	},
	//远程开柜的时候，提交一个记录给后端拿关柜时间
	postOperate(params) {
		return isThird() ? Promise.resolve() : postOperateOnline(params)
	},
	//归还手环
	backCardByApply(params) {
		return isOffline() ? Promise.resolve() : isThird() ? Promise.resolve() : backCardByApplyOnline(params)
	},
	//批量归还民警卡
	backPoliceCards(params) {
		return isOffline() ? Promise.resolve() : isThird() ? Promise.resolve() : backPoliceCardsOnline(params)
	},
	//归还民警卡
	backCards(params) {
		return isOffline() ? Promise.resolve() : isThird() ? Promise.resolve() : backCardsOnline(params)
	},
	//获取字典
	dicType(params) {
		return isThird() ? Promise.resolve() : dicTypeOnline(params)
	}
}

export default webApi
