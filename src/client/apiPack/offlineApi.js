import service from './service'
const BASE_PATH = `/api`
import Crypto from '@/common/libs/crypto'
import { communicationModeDic } from '@/common/libs/options'
import store from '../store'
const config = () => store.state.app.config
const getBasePath = () => `http://${config().serviceIp}:${config().httpPort}`
//登录
const loginOffline = async (params) => {
	params.password = Crypto.aesEncrypt(params.password)
	const OfflineRes = await service.newPost(`${BASE_PATH}/user/login`, params)
	if (config().communicationMode != communicationModeDic.ONLINE) {
		return new Promise((resolve, reject) => {
			resolve(OfflineRes)
		})
	} else {
		if (!OfflineRes.data.certificateNumber) {
			return new Promise((resolve, reject) => {
				resolve(OfflineRes)
			})
		}
		const combinedParams = {
			executeType: 'idCard',
			idCard: OfflineRes.data.certificateNumber
		}
		try {
			const onlineRes = await service.postJson(`${getBasePath()}/smart/api/selPersonMsg`, combinedParams)
			let data = {}
			if (onlineRes.data && onlineRes.data.personType && onlineRes.data[onlineRes.data.personType]) {
				data = onlineRes.data[onlineRes.data.personType]
			}
			OfflineRes.data.id = data.centerMagicId || OfflineRes.data.id
			OfflineRes.data.token = store.state.user.token
			return new Promise((resolve, reject) => {
				resolve(OfflineRes)
			})
		} catch (error) {
			return new Promise((resolve, reject) => {
				resolve(OfflineRes)
			})
		}
	}
}
//人员注册
const userRegisterOffline = (params) => service.post(`${BASE_PATH}/user/register`, params)

//人员列表
const userPageListOffline = (params) => service.post(`${BASE_PATH}/user/pageList`, params)

//人员批量删除
const userBatchDeleteOffline = (params) => service.delete(`${BASE_PATH}/user/batchDelete`, params)

//人员详情
const findInfoByIdOffline = (params) => service.get(`${BASE_PATH}/user/findInfoById`, params)

//人员修改
const userUpdateOffline = (params) => service.post(`${BASE_PATH}/user/update`, params)

//手势登录
const gestureLoginOffline = async (params) => {
	const OfflineRes = await service.newPost(`${BASE_PATH}/user/gestureLogin`, params, 'jumpMessage')
	if (config().communicationMode != communicationModeDic.ONLINE || !OfflineRes.data) {
		return new Promise((resolve, reject) => {
			resolve(OfflineRes)
		})
	} else {
		if (!OfflineRes.data.certificateNumber) {
			return new Promise((resolve, reject) => {
				resolve(OfflineRes)
			})
		}
		const combinedParams = {
			executeType: 'idCard',
			idCard: OfflineRes.data.certificateNumber
		}
		try {
			const onlineRes = await service.postJson(`${getBasePath()}/smart/api/selPersonMsg`, combinedParams)
			let data = {}
			if (onlineRes.data && onlineRes.data.personType && onlineRes.data[onlineRes.data.personType]) {
				data = onlineRes.data[onlineRes.data.personType]
			}
			OfflineRes.data.id = data.centerMagicId || OfflineRes.data.id
			OfflineRes.data.token = store.state.user.token
			return new Promise((resolve, reject) => {
				resolve(OfflineRes)
			})
		} catch (error) {
			return new Promise((resolve, reject) => {
				resolve(OfflineRes)
			})
		}
	}
}
//新增存取记录
const addAccessRecordsOffline = (params) => service.post(`${BASE_PATH}/business/addAccessRecords`, params)

//获取存取记录
const getAccessRecordsOffline = (params) => service.get(`${BASE_PATH}/business/getAccessRecords`, params)

// 获取所有使用中的柜格
const getIscdsUserListOffline = (params) => service.get(`${BASE_PATH}/business/getIscdsUserList`, params)

// 获取柜格使用情况
const getIscdsUserNumOffline = (params) => service.get(`${BASE_PATH}/business/getIscdsUserNum`, params)

//新增用户柜格关联
const addIscdsUserListOffline = (params) => service.postJson(`${BASE_PATH}/business/addIscdsUserList`, params)

//修改使用柜子列表的状态
const updateIscdsUserStatusOffline = (params) => service.postJson(`${BASE_PATH}/business/updateIscdsUserStatus`, params)

//用户柜格绑定批量删除
const delIscdsUserListOffline = (params) => service.delete(`${BASE_PATH}/business/delIscdsUserList`, params)

//人脸识别-获取人脸检测信息
const getFaceDetectInfoOffline = (params) => service.postJson(`${BASE_PATH}/user/getFaceDetectInfo`, params)

//人脸识别-人脸对比
const faceCompareManyByBase64Offline = (params) => service.postJson(`${BASE_PATH}/user/faceCompareManyByBase64`, params)

//指纹登录
const fingerCompareManyByFingerFeatureOffline = (params) => service.postJson(`${BASE_PATH}/user/fingerCompareManyByFingerFeature`, params)

//同步人员信息
const synchronizePersonnelOffline = (params) => service.postJson(`${BASE_PATH}/user/synchronizePersonnel`, params)

export { synchronizePersonnelOffline, fingerCompareManyByFingerFeatureOffline, faceCompareManyByBase64Offline, getFaceDetectInfoOffline, delIscdsUserListOffline, updateIscdsUserStatusOffline, getIscdsUserListOffline, getIscdsUserNumOffline, addIscdsUserListOffline, loginOffline, userRegisterOffline, userPageListOffline, userBatchDeleteOffline, findInfoByIdOffline, userUpdateOffline, gestureLoginOffline, addAccessRecordsOffline, getAccessRecordsOffline }
