import CryptoJS from 'crypto-js'
const key = CryptoJS.enc.Utf8.parse('SUNDUN01SUNDUN01')
const iv = CryptoJS.enc.Utf8.parse('ABCDEF0123456789')

const cryptoObj = {
	/* 加密 */
	encrypt: (word, keyStr) => {
		const srcs = CryptoJS.enc.Utf8.parse(word)
		const encrypted = CryptoJS.AES.encrypt(srcs, key, {
			iv,
			mode: CryptoJS.mode.CBC,
			padding: CryptoJS.pad.Pkcs7
		})
		return encrypted.ciphertext.toString().toUpperCase()
	},
	/* 解密 */
	decrypt: (word, keyStr) => {
		const encryptedHexStr = CryptoJS.enc.Hex.parse(word)
		const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
		const decrypt = CryptoJS.AES.decrypt(srcs, key, {
			iv,
			mode: CryptoJS.mode.CBC,
			padding: CryptoJS.pad.Pkcs7
		})
		const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
		return decryptedStr.toString()
	}
}
export default cryptoObj
