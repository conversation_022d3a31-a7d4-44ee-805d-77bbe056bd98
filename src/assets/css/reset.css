html,body,ul,li,ol,dl,dd,dt,h1,h2,h3,h4,h5,h6,form,fieldset,legend,img {
    margin: 0;
    padding: 0;
    font-size: 16px;
}
*{
    margin: 0;
    padding: 0;
}
/* li,h1,h2,h3,h4,h5,h6,p,span,div,em,i,a,strong,button{
    font-size: 16px;
    color: #333;

} */
em,i {
    font-style: normal;
}
li,ol {
    list-style: none;
}
img {
    border: 0;
    vertical-align: middle;
}
button {
    cursor: pointer;
}
a {
    text-decoration: none;
}
a:hover {
    color: #c81623;
}
/* body{
    font-family: "SourceHanSans-Regular";
} */
/* 浮动清除法 */
.clearfix::after {
    content: "";
    display: block;
    height: 0;
    clear: both;
}
.clearfix{
    zoom: 1;
}
/* @font-face{
    font-family: "Robot";
    src:url(../font/Robot.otf);
}
@font-face{
    font-family: "SourceHanSans-Regular";
    src:url(../font/SourceHanSans-Regular.otf);
} */
input:focus-visible{
    outline:#99beff  auto 1px;
    background: #f5faff;

}
::-webkit-scrollbar {
    width:20px;
    height:10px;
}
::-webkit-scrollbar-thumb{
    background:rgba(129, 135, 192, 0.541);
    border-radius:20px;
}
