
@assets:'../images';
.kongBox {
    width: 380px;
    height: 300px;
    background: url('@{assets}/noMore.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: calc(~'50% - 190px');
    top: calc(~'50% - 150px');

}
h3{
    width: 100%;
    font-size: 48px;
    font-weight: 700;
    text-align: center;
    color: #2e71e6;
    line-height: 36px;
    letter-spacing: 5px;
    margin: 64px 0;
}
.ModalTitle{
    width: 100%;
    font-size: 48px;
    font-weight: 700;
    text-align: center;
    color: #2e71e5;
    letter-spacing: 5px;
    padding-top: 64px;
}
.ModalInput{
    width: 800px;
    height: 96px;
    background: #ffffff;
    border: 2px solid #337eff;
    border-radius: 18px;
    color: #3663b3;
    font-size: 40px;
    padding-left: 32px;
    box-sizing: border-box;
    margin-top:72px;
    margin-left: calc(50% - 400px);
}
.closeModalBtn{
    display: inline-block;
    width: 96px;
    height: 96px;
    background: url('../../assets/images/closeBtnIcon.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    bottom: -160px;
    left: calc(50% - 48px);
}
.loginByIDToast{
    width: 920px;
    height: 920px;
    background-color: #fff;
    border-radius: 16px;
    margin: auto;
    // margin-top: 44px;

    box-sizing: border-box;
    padding:80px 64px;
}
.loginByIDToastContent{
    position: relative;
}
.loginId{
    width: 100%;
	height: 112px;
	background-color: rgba(245,250,255,0.00);
	border-radius: 16px;
    border: solid 2px #99BEFF;
    color: #1269ff;
    font-size: 36px;
    box-sizing: border-box;
    padding-left: 40px;
}

.loginPassword{
    width: 100%;
	height: 112px;
	background-color: rgba(245,250,255,0.00);
	border-radius: 16px;
    border: solid 2px #99beff;
    margin-top: 40px;
    color: #1269ff;
    font-size: 36px;
    box-sizing: border-box;
    padding-left: 40px;
}

.loginTitle{
    color: #337eff;
    font-size: 48px;
    font-weight: bold;
    text-align: center;
}

.keyBoxMain{
    margin-top: 40px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

.keyOneBox{
    width: 180px;
	height: 112px;
	background-color:  rgba(235,245,255,0.00);
	border-radius: 16px;
    border: solid 2px #99beff;
    color: #739de6;
    font-size: 72px;
    font-family: Arial;
    text-align: center;
    line-height: 112px;
    margin-bottom: 24px;
}

.resizeText{
    font-size: 40px;
}

.delIcon{
    display: inline-block;
    width: 66px;
    height: 41px;
    background: url("../images/delIcon.png") no-repeat;
    background-size: 100% 100%;
}
.operateBox{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 64px;
}
.buttonStyle1{
    width: 320px;
    height: 96px;
    line-height: 96px;
    text-align: center;
    background: #f26b6b;
    border-radius: 8px;
    font-size: 36px;
    margin: 0 60px;
    color: #fff;
    cursor: pointer;
}
.buttonStyle2{
    width: 320px;
    height: 96px;
    line-height: 96px;
    text-align: center;
    background: #337eff;
    border-radius: 8px;
    font-size: 36px;
    margin: 0 60px;
    color: #fff;
    cursor: pointer;
}
.buttonStyle3{
    width: 320px;
    height: 96px;
    line-height: 96px;
    text-align: center;
    background: #B34750;
    border-radius: 8px;
    font-size: 36px;
    margin: 0 60px;
    color: #fff;
    cursor: pointer;
}
.buttonStyle4{
    width: 320px;
    height: 96px;
    line-height: 96px;
    text-align: center;
    background: #fff;
    border-radius: 8px;
    font-size: 36px;
    margin: 0 60px;
    color: #337EFF;
    border: 2px solid #337eff;
    cursor: pointer;
}
.buttonStyle5{
    width: 320px;
    height: 96px;
    line-height: 96px;
    text-align: center;
    background: #ccc;
    border-radius: 8px;
    font-size: 36px;
    color: #fff;
    cursor: pointer;
}
.tips{
    color: #739DE6;
    font-size: 28px;
    padding-left: 40px;
}
.vertical-center-modal{
    display: flex;
    align-items: center;
    justify-content: center;

    .ivu-modal{
        top: 0;
    }
}
.vertical-center-modal .ivu-modal{
    border-radius: 16px;
    overflow: hidden;
}
.ivu-switch{
    width: 150px;
    height: 80px;
    border-radius: 50px;
    background-color:#323063;
    border: none;
}
.ivu-switch:after{
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color:#413eab;
    left: 4px;
    top: 4px;
}
.ivu-switch-checked:after{
    left: unset;
    right: 4px;
    top: 4px;
    background-color:#fff;
}
.switchBtn_close{
    display: inline-block;
    width: 206px;
    height: 156px;
    background: url('@{assets}/addlineBg.png') no-repeat;
    background-size: 100% 100%;
}
.switchBtn_open{
    display: inline-block;
    width: 206px;
    height: 156px;
    background: url('@{assets}/addLineBgActive.png') no-repeat;
    background-size: 100% 100%;
}
.switchBox{
    margin-top: 0;
    margin-left: 24px;
    p{
        font-size: 32px;
        text-align: left;
        color: #b9b8e6;
        line-height: 36px;
        letter-spacing: 3px;
        margin-top: 24px;
    }
}
.faceIdentifying{
    width: 720px;
    height: 720px;
    background: url('@{assets}/beforeFaceIcone.png') no-repeat;
    background-size: 100% 100%;
    margin: 148px auto 70px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    .Identifying{
        width: 100%;
        height: 100%;
        background: url('@{assets}/Identifying.png') no-repeat;
        background-size: 100% 100%;
        animation:rotate 2s infinite linear;
    }
    // #videoBox,#canvas-face-upload2,#canvas-face-upload{
    //     transform: rotate(90deg);
    // }
    #videoBox,.canvasStyle{
        width: 96%;
        height: 96.2%;
        position: absolute;
        left: 2%;
        top: 2%;
        border-radius: 50%;
    }
    .canvasStyle2{
        border-radius:0%;
    }
    #canvas-face{
        z-index: 5;
    }
    #canvas1{
        z-index: 2;
    }
    #canvas-face-upload2{
        z-index: 4;
    }
    #canvas-face-upload{
        width:260px;
        height: 330px;
        z-index: 0;
        left: calc(~'50% - 130px');
        top: calc(~'50% - 165px');
    }

}
@keyframes rotate{
    0%{
        transform: rotate(0);
    }
    100%{
        transform: rotate(360deg);
    }
}
.IdentifyingTitle{
    width: 100%;
    font-size: 48px;
    font-weight: 700;
    text-align: center;
    color: #ffffff;
    line-height: 72px;
    letter-spacing: 5px;
}
#faceCollectWrap{
    width: 100%;
    height: 100%;
    background: rgba(24, 22, 67, 0.9);
    position: fixed;
    top: 0;
    left: 0;
    overflow: auto;
}
.tipsBox{
    width: 600px;
    background: #ebf5ff;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.tipsIcon1{
    display: inline-block;
    width: 100px;
    height: 100px;
    background: url('@{assets}/warningIcon.png') no-repeat;
    background-size: 100% 100%;
    margin:60px auto 40px;
}
.tipsIcon2{
    display: inline-block;
    width: 100px;
    height: 100px;
    background: url('@{assets}/errorIcon.png') no-repeat;
    background-size: 100% 100%;
    margin:60px auto 40px;
}
.tipsIcon3{
    display: inline-block;
    width: 100px;
    height: 100px;
    background: url('@{assets}/finishIcon.png') no-repeat;
    background-size: 100% 100%;
    margin:60px auto 40px;
}
.tipsText{
    width: 520px;
    font-size: 38px;
    text-align: center;
    color: #333333;
    line-height: 60px;
    margin-bottom: 20px;
}
.ivu-modal .ivu-modal-content{
    background: #ebf5ff;
}

.ivu-modal-confirm-footer{
    margin: 0;
    text-align: center;
    background: #ebf5ff;
    padding-bottom: 40px;
    padding-top: 20px;
}
.ivu-btn-text{
    width: 160px;
    height: 70px;
    line-height: 70px;
    background: #f26b6b;
    border-radius: 4px;
    margin: 0 60px;
    span{
        color: #fff;
        font-size: 28px;
    }
}
.ivu-btn-text:hover {
    background: #f26b6b;
    border-color: transparent;
    opacity: 0.8;
}
/***/
.ivu-modal-footer {
    background:#EBF5FF;
    border: none;
}
/***/
.ivu-btn-primary{
    text-align: center;
    width: 160px;
    height: 70px;
    line-height: 70px;
    background: #0099e6;
    border-radius: 4px;
    margin: 0 60px;
    span{
        color: #fff;
        font-size: 28px;
    }
}
.ivu-modal-wrap{
    display: flex;
    justify-content: center;
    align-items: center;
    .ivu-modal{
        position: unset;
    }
}
.backHome{
    width: 200px;
    height: 80px;
    line-height: 80px;
    background: #ffffff;
    border-radius: 40px;
    color: #1269ff;
    font-size: 26px;
    letter-spacing: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
    float: left;
    .goBackBtn {
        display: inline-block;
        width: 40px;
        height: 33px;
        background: url('@{assets}/backIcon.png') no-repeat;
        background-size: 100% 100%;
        margin-right: 22px;
        cursor: pointer;
    }
    .backHomeBtn{
        display: inline-block;
        width: 40px;
        height: 33px;
        background: url('@{assets}/homeIcon.png') no-repeat;
        background-size: 100% 100%;
        margin-left: 22px;
        cursor: pointer;
    }
}
.logoutBtn{
    width: 200px;
    height: 80px;
    background: #ffffff;
    border-radius: 40px;
    font-size: 26px;
    text-align: center;
    color: #fc3e51;
    letter-spacing: 3px;
}
.logoutIcon{
    display: inline-block;
    width: 48px;
    height: 48px;
    background: url('@{assets}/logoutIcon.png') no-repeat;
    background-size: 100% 100%;
    margin-left: 18px;
}
.tipsItem{
    display: flex;
    justify-content: center;
    align-items: center;
}
.tipsItem2{
    flex-direction: column;
    margin-bottom: 30px;
    p{
        width: 100%;
        font-size: 28px;
        text-align: center;
        color: #669eff;
        line-height: 40px;
    }
    .downArrowBtn{
        width: 33px;
        height: 32px;
        background: url('@{assets}/downArrow.png') no-repeat;
        background-size: 100% 100%;
        margin-top: 27px;
    }
}
.noticeBox{
    width: 920px;
    height: 64px;
    background: #faebed;
    margin: 30px auto 40px;
    font-size: 30px;
    color: #666;
    line-height: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
    label{
        color: #e60012;
        font-weight: bold;
    }
}
.manualRegisst{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 150px;
    span{
        font-size: 28px;
        color: #FC3E51;
    }
    .MR_btn{
        width: 176px;
        height: 64px;
        line-height: 64px;
        text-align: center;
        background: #337eff;
        border-radius: 32px;
        font-size: 28px;
        color: #ffffff;
        margin-left: 32px;
    }
}
.manualRegisst{
    margin-top: 64px;
}
.noticeIcon{
    display: inline-block;
    width: 40px;
    height: 40px;
    background: url('@{assets}/noticeIcon.png') no-repeat;
    background-size: 100% 100%;
    margin-right: 10px;
}
.ivu-checkbox-inner{
    width: 48px;
    height: 48px;
    border-radius: 50%;

}
.ivu-checkbox-checked .ivu-checkbox-inner {
    background: #39CC7E;
    border: none;
}
.ivu-checkbox-checked .ivu-checkbox-inner:after{
    width: 14px;
    height: 28px;
    left: 14px;
    top: 6px;
}
.ivu-checkbox-group-item span:last-child{
    display: none;
}
.ivu-checkbox-wrapper{
    font-size: 36px;
    color: #333;
}
.loadTips{
    text-align: center;
}
.E_N_switch{
   text-align: center;
    span{
        display: inline-block;
        line-height: 112px;
        font-size: 48px;
        color: #337EFF;
        vertical-align: top;
    }
    i{
        font-size: 36px;
        color: #739DE5;
    }
}
.noMore{
    display: inline-block;
    width: 380px;
    height: 300px;
    background: url('@{assets}/noMore.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    left: 0;
    top: 390px;
    bottom: 0;
    margin: auto;
}
.versionClickView{
    width: 300px;
    height: 300px;
    position: absolute;
    right: 0;
    top: 0;
    // background: #000;
}
.updateClickView {
    width: 300px;
    height: 300px;
    position: absolute;
    left: 0;
    top: 0;
}


// gosuncn-ui dialog css reset
.x-dialog-container {
	border-radius: 16px;
}
.x-dialog-header {
	padding: 36px 40px 36px 0;
}
.x-dialog-title {
	line-height: 40px;
	height: 40px;
	font-size: 32px;
	color: #2f3648;
	text-align: center;
}
.x-dialog-footer {
	margin-bottom: 20px;
	margin-top: 38px;
	button + button,
	.gxx-button + .gxx-button {
		margin-left: 20px;
	}
	.gxx-button-default .gxx-button-content {
		color: #fff;
	}
}
.x-dialog-footer button {
	border-radius: 8px;
	height: 60px;
	width: 202px;
	font-size: 24px;
	border: 1px solid #305aff;
	color: #305aff;
}
.x-dialog-cancel:hover {
	border-color: #305aff;
	background-color: #fff;
}
.x-dialog-cancel:active {
	border-color: #305aff;
	background-color: #fff;
}
.x-dialog-confirm {
	color: #fff !important;
	background-color: #305aff !important;
	border-color: #305aff !important;
}
.x-dialog-confirm:hover {
	background: #305aff !important;
	border-color: #305aff !important;
	color: #fff;
}
.x-dialog-confirm:active {
	background: #305aff !important;
	border-color: #305aff !important;
	color: #fff;
}
