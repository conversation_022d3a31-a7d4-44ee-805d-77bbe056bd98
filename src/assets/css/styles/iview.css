.ivu-load-loop {
    -webkit-animation: ani-load-loop 1s linear infinite;
    animation: ani-load-loop 1s linear infinite
}

@-webkit-keyframes ani-load-loop {
    from {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    50% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg)
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes ani-load-loop {
    from {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    50% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg)
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

.input-group-error-append,
.input-group-error-prepend {
    background-color: #fff;
    border: 1px solid #ed4014
}

.input-group-error-append .ivu-select-selection,
.input-group-error-prepend .ivu-select-selection {
    background-color: inherit;
    border: 1px solid transparent
}

.input-group-error-prepend {
    border-right: 0
}

.input-group-error-append {
    border-left: 0
}

/*! normalize.css v5.0.0 | MIT License | github.com/necolas/normalize.css */
html {
    font-family: sans-serif;
    line-height: 1.15;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

article,
aside,
footer,
header,
nav,
section {
    display: block
}

h1 {
    font-size: 2em;
    margin: .67em 0
}

figcaption,
figure,
main {
    display: block
}

figure {
    margin: 1em 40px
}

hr {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    height: 0;
    overflow: visible
}

pre {
    font-family: monospace, monospace;
    font-size: 1em
}

a {
    background-color: transparent;
    -webkit-text-decoration-skip: objects
}

a:active,
a:hover {
    outline-width: 0
}

abbr[title] {
    border-bottom: none;
    text-decoration: underline;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

b,
strong {
    font-weight: inherit
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
samp {
    font-family: monospace, monospace;
    font-size: 1em
}

dfn {
    font-style: italic
}

mark {
    background-color: #ff0;
    color: #000
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

audio,
video {
    display: inline-block
}

audio:not([controls]) {
    display: none;
    height: 0
}

img {
    border-style: none
}

svg:not(:root) {
    overflow: hidden
}

button,
input,
optgroup,
select,
textarea {
    font-family: sans-serif;
    font-size: 100%;
    line-height: 1.15;
    margin: 0
}

button,
input {
    overflow: visible
}

button,
select {
    text-transform: none
}

[type=reset],
[type=submit],
button,
html [type=button] {
    -webkit-appearance: button
}

[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner,
button::-moz-focus-inner {
    border-style: none;
    padding: 0
}

[type=button]:-moz-focusring,
[type=reset]:-moz-focusring,
[type=submit]:-moz-focusring,
button:-moz-focusring {
    outline: 1px dotted ButtonText
}

fieldset {
    border: 1px solid silver;
    margin: 0 2px;
    padding: .35em .625em .75em
}

legend {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: inherit;
    display: table;
    max-width: 100%;
    padding: 0;
    white-space: normal
}

progress {
    display: inline-block;
    vertical-align: baseline
}

textarea {
    overflow: auto;
    resize: vertical
}

[type=checkbox],
[type=radio] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0
}

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

[type=search]::-webkit-search-cancel-button,
[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

details,
menu {
    display: block
}

summary {
    display: list-item
}

canvas {
    display: inline-block
}

template {
    display: none
}

[hidden] {
    display: none
}

* {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent
}

:after,
:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

body {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    /* font-size: 14px; */
    line-height: 1.5;
    color: #515a6e;
    background-color: #fff;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

article,
aside,
blockquote,
body,
button,
dd,
details,
div,
dl,
dt,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
hr,
input,
legend,
li,
menu,
nav,
ol,
p,
section,
td,
textarea,
th,
ul {
    margin: 0;
    padding: 0
}

button,
input,
select,
textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}

input::-ms-clear,
input::-ms-reveal {
    display: none
}

a {
    color: #2d8cf0;
    background: 0 0;
    text-decoration: none;
    outline: 0;
    cursor: pointer;
    -webkit-transition: color .2s ease;
    transition: color .2s ease
}

a:hover {
    color: #57a3f3
}

a:active {
    color: #2b85e4
}

a:active,
a:hover {
    outline: 0;
    text-decoration: none
}

a[disabled] {
    color: #ccc;
    cursor: not-allowed;
    pointer-events: none
}

code,
kbd,
pre,
samp {
    font-family: Consolas, Menlo, Courier, monospace
}

@font-face {
    font-family: Ionicons;
    src: url(fonts/ionicons.woff2?v=3.0.0) format("woff2"), url(fonts/ionicons.woff?v=3.0.0) format("woff"), url(fonts/ionicons.ttf?v=3.0.0) format("truetype"), url(fonts/ionicons.svg?v=3.0.0#Ionicons) format("svg");
    font-weight: 400;
    font-style: normal
}

.ivu-icon {
    display: inline-block;
    font-family: Ionicons;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    text-rendering: optimizeLegibility;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    vertical-align: -.125em;
    text-align: center
}

.ivu-icon-ios-add-circle-outline:before {
    content: "\f100"
}

.ivu-icon-ios-add-circle:before {
    content: "\f101"
}

.ivu-icon-ios-add:before {
    content: "\f102"
}

.ivu-icon-ios-alarm-outline:before {
    content: "\f103"
}

.ivu-icon-ios-alarm:before {
    content: "\f104"
}

.ivu-icon-ios-albums-outline:before {
    content: "\f105"
}

.ivu-icon-ios-albums:before {
    content: "\f106"
}

.ivu-icon-ios-alert-outline:before {
    content: "\f107"
}

.ivu-icon-ios-alert:before {
    content: "\f108"
}

.ivu-icon-ios-american-football-outline:before {
    content: "\f109"
}

.ivu-icon-ios-american-football:before {
    content: "\f10a"
}

.ivu-icon-ios-analytics-outline:before {
    content: "\f10b"
}

.ivu-icon-ios-analytics:before {
    content: "\f10c"
}

.ivu-icon-ios-aperture-outline:before {
    content: "\f10d"
}

.ivu-icon-ios-aperture:before {
    content: "\f10e"
}

.ivu-icon-ios-apps-outline:before {
    content: "\f10f"
}

.ivu-icon-ios-apps:before {
    content: "\f110"
}

.ivu-icon-ios-appstore-outline:before {
    content: "\f111"
}

.ivu-icon-ios-appstore:before {
    content: "\f112"
}

.ivu-icon-ios-archive-outline:before {
    content: "\f113"
}

.ivu-icon-ios-archive:before {
    content: "\f114"
}

.ivu-icon-ios-arrow-back:before {
    content: "\f115"
}

.ivu-icon-ios-arrow-down:before {
    content: "\f116"
}

.ivu-icon-ios-arrow-dropdown-circle:before {
    content: "\f117"
}

.ivu-icon-ios-arrow-dropdown:before {
    content: "\f118"
}

.ivu-icon-ios-arrow-dropleft-circle:before {
    content: "\f119"
}

.ivu-icon-ios-arrow-dropleft:before {
    content: "\f11a"
}

.ivu-icon-ios-arrow-dropright-circle:before {
    content: "\f11b"
}

.ivu-icon-ios-arrow-dropright:before {
    content: "\f11c"
}

.ivu-icon-ios-arrow-dropup-circle:before {
    content: "\f11d"
}

.ivu-icon-ios-arrow-dropup:before {
    content: "\f11e"
}

.ivu-icon-ios-arrow-forward:before {
    content: "\f11f"
}

.ivu-icon-ios-arrow-round-back:before {
    content: "\f120"
}

.ivu-icon-ios-arrow-round-down:before {
    content: "\f121"
}

.ivu-icon-ios-arrow-round-forward:before {
    content: "\f122"
}

.ivu-icon-ios-arrow-round-up:before {
    content: "\f123"
}

.ivu-icon-ios-arrow-up:before {
    content: "\f124"
}

.ivu-icon-ios-at-outline:before {
    content: "\f125"
}

.ivu-icon-ios-at:before {
    content: "\f126"
}

.ivu-icon-ios-attach:before {
    content: "\f127"
}

.ivu-icon-ios-backspace-outline:before {
    content: "\f128"
}

.ivu-icon-ios-backspace:before {
    content: "\f129"
}

.ivu-icon-ios-barcode-outline:before {
    content: "\f12a"
}

.ivu-icon-ios-barcode:before {
    content: "\f12b"
}

.ivu-icon-ios-baseball-outline:before {
    content: "\f12c"
}

.ivu-icon-ios-baseball:before {
    content: "\f12d"
}

.ivu-icon-ios-basket-outline:before {
    content: "\f12e"
}

.ivu-icon-ios-basket:before {
    content: "\f12f"
}

.ivu-icon-ios-basketball-outline:before {
    content: "\f130"
}

.ivu-icon-ios-basketball:before {
    content: "\f131"
}

.ivu-icon-ios-battery-charging:before {
    content: "\f132"
}

.ivu-icon-ios-battery-dead:before {
    content: "\f133"
}

.ivu-icon-ios-battery-full:before {
    content: "\f134"
}

.ivu-icon-ios-beaker-outline:before {
    content: "\f135"
}

.ivu-icon-ios-beaker:before {
    content: "\f136"
}

.ivu-icon-ios-beer-outline:before {
    content: "\f137"
}

.ivu-icon-ios-beer:before {
    content: "\f138"
}

.ivu-icon-ios-bicycle:before {
    content: "\f139"
}

.ivu-icon-ios-bluetooth:before {
    content: "\f13a"
}

.ivu-icon-ios-boat-outline:before {
    content: "\f13b"
}

.ivu-icon-ios-boat:before {
    content: "\f13c"
}

.ivu-icon-ios-body-outline:before {
    content: "\f13d"
}

.ivu-icon-ios-body:before {
    content: "\f13e"
}

.ivu-icon-ios-bonfire-outline:before {
    content: "\f13f"
}

.ivu-icon-ios-bonfire:before {
    content: "\f140"
}

.ivu-icon-ios-book-outline:before {
    content: "\f141"
}

.ivu-icon-ios-book:before {
    content: "\f142"
}

.ivu-icon-ios-bookmark-outline:before {
    content: "\f143"
}

.ivu-icon-ios-bookmark:before {
    content: "\f144"
}

.ivu-icon-ios-bookmarks-outline:before {
    content: "\f145"
}

.ivu-icon-ios-bookmarks:before {
    content: "\f146"
}

.ivu-icon-ios-bowtie-outline:before {
    content: "\f147"
}

.ivu-icon-ios-bowtie:before {
    content: "\f148"
}

.ivu-icon-ios-briefcase-outline:before {
    content: "\f149"
}

.ivu-icon-ios-briefcase:before {
    content: "\f14a"
}

.ivu-icon-ios-browsers-outline:before {
    content: "\f14b"
}

.ivu-icon-ios-browsers:before {
    content: "\f14c"
}

.ivu-icon-ios-brush-outline:before {
    content: "\f14d"
}

.ivu-icon-ios-brush:before {
    content: "\f14e"
}

.ivu-icon-ios-bug-outline:before {
    content: "\f14f"
}

.ivu-icon-ios-bug:before {
    content: "\f150"
}

.ivu-icon-ios-build-outline:before {
    content: "\f151"
}

.ivu-icon-ios-build:before {
    content: "\f152"
}

.ivu-icon-ios-bulb-outline:before {
    content: "\f153"
}

.ivu-icon-ios-bulb:before {
    content: "\f154"
}

.ivu-icon-ios-bus-outline:before {
    content: "\f155"
}

.ivu-icon-ios-bus:before {
    content: "\f156"
}

.ivu-icon-ios-cafe-outline:before {
    content: "\f157"
}

.ivu-icon-ios-cafe:before {
    content: "\f158"
}

.ivu-icon-ios-calculator-outline:before {
    content: "\f159"
}

.ivu-icon-ios-calculator:before {
    content: "\f15a"
}

.ivu-icon-ios-calendar-outline:before {
    content: "\f15b"
}

.ivu-icon-ios-calendar:before {
    content: "\f15c"
}

.ivu-icon-ios-call-outline:before {
    content: "\f15d"
}

.ivu-icon-ios-call:before {
    content: "\f15e"
}

.ivu-icon-ios-camera-outline:before {
    content: "\f15f"
}

.ivu-icon-ios-camera:before {
    content: "\f160"
}

.ivu-icon-ios-car-outline:before {
    content: "\f161"
}

.ivu-icon-ios-car:before {
    content: "\f162"
}

.ivu-icon-ios-card-outline:before {
    content: "\f163"
}

.ivu-icon-ios-card:before {
    content: "\f164"
}

.ivu-icon-ios-cart-outline:before {
    content: "\f165"
}

.ivu-icon-ios-cart:before {
    content: "\f166"
}

.ivu-icon-ios-cash-outline:before {
    content: "\f167"
}

.ivu-icon-ios-cash:before {
    content: "\f168"
}

.ivu-icon-ios-chatboxes-outline:before {
    content: "\f169"
}

.ivu-icon-ios-chatboxes:before {
    content: "\f16a"
}

.ivu-icon-ios-chatbubbles-outline:before {
    content: "\f16b"
}

.ivu-icon-ios-chatbubbles:before {
    content: "\f16c"
}

.ivu-icon-ios-checkbox-outline:before {
    content: "\f16d"
}

.ivu-icon-ios-checkbox:before {
    content: "\f16e"
}

.ivu-icon-ios-checkmark-circle-outline:before {
    content: "\f16f"
}

.ivu-icon-ios-checkmark-circle:before {
    content: "\f170"
}

.ivu-icon-ios-checkmark:before {
    content: "\f171"
}

.ivu-icon-ios-clipboard-outline:before {
    content: "\f172"
}

.ivu-icon-ios-clipboard:before {
    content: "\f173"
}

.ivu-icon-ios-clock-outline:before {
    content: "\f174"
}

.ivu-icon-ios-clock:before {
    content: "\f175"
}

.ivu-icon-ios-close-circle-outline:before {
    content: "\f176"
}

.ivu-icon-ios-close-circle:before {
    content: "\f177"
}

.ivu-icon-ios-close:before {
    content: "\f178"
}

.ivu-icon-ios-closed-captioning-outline:before {
    content: "\f179"
}

.ivu-icon-ios-closed-captioning:before {
    content: "\f17a"
}

.ivu-icon-ios-cloud-circle-outline:before {
    content: "\f17b"
}

.ivu-icon-ios-cloud-circle:before {
    content: "\f17c"
}

.ivu-icon-ios-cloud-done-outline:before {
    content: "\f17d"
}

.ivu-icon-ios-cloud-done:before {
    content: "\f17e"
}

.ivu-icon-ios-cloud-download-outline:before {
    content: "\f17f"
}

.ivu-icon-ios-cloud-download:before {
    content: "\f180"
}

.ivu-icon-ios-cloud-outline:before {
    content: "\f181"
}

.ivu-icon-ios-cloud-upload-outline:before {
    content: "\f182"
}

.ivu-icon-ios-cloud-upload:before {
    content: "\f183"
}

.ivu-icon-ios-cloud:before {
    content: "\f184"
}

.ivu-icon-ios-cloudy-night-outline:before {
    content: "\f185"
}

.ivu-icon-ios-cloudy-night:before {
    content: "\f186"
}

.ivu-icon-ios-cloudy-outline:before {
    content: "\f187"
}

.ivu-icon-ios-cloudy:before {
    content: "\f188"
}

.ivu-icon-ios-code-download:before {
    content: "\f189"
}

.ivu-icon-ios-code-working:before {
    content: "\f18a"
}

.ivu-icon-ios-code:before {
    content: "\f18b"
}

.ivu-icon-ios-cog-outline:before {
    content: "\f18c"
}

.ivu-icon-ios-cog:before {
    content: "\f18d"
}

.ivu-icon-ios-color-fill-outline:before {
    content: "\f18e"
}

.ivu-icon-ios-color-fill:before {
    content: "\f18f"
}

.ivu-icon-ios-color-filter-outline:before {
    content: "\f190"
}

.ivu-icon-ios-color-filter:before {
    content: "\f191"
}

.ivu-icon-ios-color-palette-outline:before {
    content: "\f192"
}

.ivu-icon-ios-color-palette:before {
    content: "\f193"
}

.ivu-icon-ios-color-wand-outline:before {
    content: "\f194"
}

.ivu-icon-ios-color-wand:before {
    content: "\f195"
}

.ivu-icon-ios-compass-outline:before {
    content: "\f196"
}

.ivu-icon-ios-compass:before {
    content: "\f197"
}

.ivu-icon-ios-construct-outline:before {
    content: "\f198"
}

.ivu-icon-ios-construct:before {
    content: "\f199"
}

.ivu-icon-ios-contact-outline:before {
    content: "\f19a"
}

.ivu-icon-ios-contact:before {
    content: "\f19b"
}

.ivu-icon-ios-contacts-outline:before {
    content: "\f19c"
}

.ivu-icon-ios-contacts:before {
    content: "\f19d"
}

.ivu-icon-ios-contract:before {
    content: "\f19e"
}

.ivu-icon-ios-contrast:before {
    content: "\f19f"
}

.ivu-icon-ios-copy-outline:before {
    content: "\f1a0"
}

.ivu-icon-ios-copy:before {
    content: "\f1a1"
}

.ivu-icon-ios-create-outline:before {
    content: "\f1a2"
}

.ivu-icon-ios-create:before {
    content: "\f1a3"
}

.ivu-icon-ios-crop-outline:before {
    content: "\f1a4"
}

.ivu-icon-ios-crop:before {
    content: "\f1a5"
}

.ivu-icon-ios-cube-outline:before {
    content: "\f1a6"
}

.ivu-icon-ios-cube:before {
    content: "\f1a7"
}

.ivu-icon-ios-cut-outline:before {
    content: "\f1a8"
}

.ivu-icon-ios-cut:before {
    content: "\f1a9"
}

.ivu-icon-ios-desktop-outline:before {
    content: "\f1aa"
}

.ivu-icon-ios-desktop:before {
    content: "\f1ab"
}

.ivu-icon-ios-disc-outline:before {
    content: "\f1ac"
}

.ivu-icon-ios-disc:before {
    content: "\f1ad"
}

.ivu-icon-ios-document-outline:before {
    content: "\f1ae"
}

.ivu-icon-ios-document:before {
    content: "\f1af"
}

.ivu-icon-ios-done-all:before {
    content: "\f1b0"
}

.ivu-icon-ios-download-outline:before {
    content: "\f1b1"
}

.ivu-icon-ios-download:before {
    content: "\f1b2"
}

.ivu-icon-ios-easel-outline:before {
    content: "\f1b3"
}

.ivu-icon-ios-easel:before {
    content: "\f1b4"
}

.ivu-icon-ios-egg-outline:before {
    content: "\f1b5"
}

.ivu-icon-ios-egg:before {
    content: "\f1b6"
}

.ivu-icon-ios-exit-outline:before {
    content: "\f1b7"
}

.ivu-icon-ios-exit:before {
    content: "\f1b8"
}

.ivu-icon-ios-expand:before {
    content: "\f1b9"
}

.ivu-icon-ios-eye-off-outline:before {
    content: "\f1ba"
}

.ivu-icon-ios-eye-off:before {
    content: "\f1bb"
}

.ivu-icon-ios-eye-outline:before {
    content: "\f1bc"
}

.ivu-icon-ios-eye:before {
    content: "\f1bd"
}

.ivu-icon-ios-fastforward-outline:before {
    content: "\f1be"
}

.ivu-icon-ios-fastforward:before {
    content: "\f1bf"
}

.ivu-icon-ios-female:before {
    content: "\f1c0"
}

.ivu-icon-ios-filing-outline:before {
    content: "\f1c1"
}

.ivu-icon-ios-filing:before {
    content: "\f1c2"
}

.ivu-icon-ios-film-outline:before {
    content: "\f1c3"
}

.ivu-icon-ios-film:before {
    content: "\f1c4"
}

.ivu-icon-ios-finger-print:before {
    content: "\f1c5"
}

.ivu-icon-ios-flag-outline:before {
    content: "\f1c6"
}

.ivu-icon-ios-flag:before {
    content: "\f1c7"
}

.ivu-icon-ios-flame-outline:before {
    content: "\f1c8"
}

.ivu-icon-ios-flame:before {
    content: "\f1c9"
}

.ivu-icon-ios-flash-outline:before {
    content: "\f1ca"
}

.ivu-icon-ios-flash:before {
    content: "\f1cb"
}

.ivu-icon-ios-flask-outline:before {
    content: "\f1cc"
}

.ivu-icon-ios-flask:before {
    content: "\f1cd"
}

.ivu-icon-ios-flower-outline:before {
    content: "\f1ce"
}

.ivu-icon-ios-flower:before {
    content: "\f1cf"
}

.ivu-icon-ios-folder-open-outline:before {
    content: "\f1d0"
}

.ivu-icon-ios-folder-open:before {
    content: "\f1d1"
}

.ivu-icon-ios-folder-outline:before {
    content: "\f1d2"
}

.ivu-icon-ios-folder:before {
    content: "\f1d3"
}

.ivu-icon-ios-football-outline:before {
    content: "\f1d4"
}

.ivu-icon-ios-football:before {
    content: "\f1d5"
}

.ivu-icon-ios-funnel-outline:before {
    content: "\f1d6"
}

.ivu-icon-ios-funnel:before {
    content: "\f1d7"
}

.ivu-icon-ios-game-controller-a-outline:before {
    content: "\f1d8"
}

.ivu-icon-ios-game-controller-a:before {
    content: "\f1d9"
}

.ivu-icon-ios-game-controller-b-outline:before {
    content: "\f1da"
}

.ivu-icon-ios-game-controller-b:before {
    content: "\f1db"
}

.ivu-icon-ios-git-branch:before {
    content: "\f1dc"
}

.ivu-icon-ios-git-commit:before {
    content: "\f1dd"
}

.ivu-icon-ios-git-compare:before {
    content: "\f1de"
}

.ivu-icon-ios-git-merge:before {
    content: "\f1df"
}

.ivu-icon-ios-git-network:before {
    content: "\f1e0"
}

.ivu-icon-ios-git-pull-request:before {
    content: "\f1e1"
}

.ivu-icon-ios-glasses-outline:before {
    content: "\f1e2"
}

.ivu-icon-ios-glasses:before {
    content: "\f1e3"
}

.ivu-icon-ios-globe-outline:before {
    content: "\f1e4"
}

.ivu-icon-ios-globe:before {
    content: "\f1e5"
}

.ivu-icon-ios-grid-outline:before {
    content: "\f1e6"
}

.ivu-icon-ios-grid:before {
    content: "\f1e7"
}

.ivu-icon-ios-hammer-outline:before {
    content: "\f1e8"
}

.ivu-icon-ios-hammer:before {
    content: "\f1e9"
}

.ivu-icon-ios-hand-outline:before {
    content: "\f1ea"
}

.ivu-icon-ios-hand:before {
    content: "\f1eb"
}

.ivu-icon-ios-happy-outline:before {
    content: "\f1ec"
}

.ivu-icon-ios-happy:before {
    content: "\f1ed"
}

.ivu-icon-ios-headset-outline:before {
    content: "\f1ee"
}

.ivu-icon-ios-headset:before {
    content: "\f1ef"
}

.ivu-icon-ios-heart-outline:before {
    content: "\f1f0"
}

.ivu-icon-ios-heart:before {
    content: "\f1f1"
}

.ivu-icon-ios-help-buoy-outline:before {
    content: "\f1f2"
}

.ivu-icon-ios-help-buoy:before {
    content: "\f1f3"
}

.ivu-icon-ios-help-circle-outline:before {
    content: "\f1f4"
}

.ivu-icon-ios-help-circle:before {
    content: "\f1f5"
}

.ivu-icon-ios-help:before {
    content: "\f1f6"
}

.ivu-icon-ios-home-outline:before {
    content: "\f1f7"
}

.ivu-icon-ios-home:before {
    content: "\f1f8"
}

.ivu-icon-ios-ice-cream-outline:before {
    content: "\f1f9"
}

.ivu-icon-ios-ice-cream:before {
    content: "\f1fa"
}

.ivu-icon-ios-image-outline:before {
    content: "\f1fb"
}

.ivu-icon-ios-image:before {
    content: "\f1fc"
}

.ivu-icon-ios-images-outline:before {
    content: "\f1fd"
}

.ivu-icon-ios-images:before {
    content: "\f1fe"
}

.ivu-icon-ios-infinite-outline:before {
    content: "\f1ff"
}

.ivu-icon-ios-infinite:before {
    content: "\f200"
}

.ivu-icon-ios-information-circle-outline:before {
    content: "\f201"
}

.ivu-icon-ios-information-circle:before {
    content: "\f202"
}

.ivu-icon-ios-information:before {
    content: "\f203"
}

.ivu-icon-ios-ionic-outline:before {
    content: "\f204"
}

.ivu-icon-ios-ionic:before {
    content: "\f205"
}

.ivu-icon-ios-ionitron-outline:before {
    content: "\f206"
}

.ivu-icon-ios-ionitron:before {
    content: "\f207"
}

.ivu-icon-ios-jet-outline:before {
    content: "\f208"
}

.ivu-icon-ios-jet:before {
    content: "\f209"
}

.ivu-icon-ios-key-outline:before {
    content: "\f20a"
}

.ivu-icon-ios-key:before {
    content: "\f20b"
}

.ivu-icon-ios-keypad-outline:before {
    content: "\f20c"
}

.ivu-icon-ios-keypad:before {
    content: "\f20d"
}

.ivu-icon-ios-laptop:before {
    content: "\f20e"
}

.ivu-icon-ios-leaf-outline:before {
    content: "\f20f"
}

.ivu-icon-ios-leaf:before {
    content: "\f210"
}

.ivu-icon-ios-link-outline:before {
    content: "\f211"
}

.ivu-icon-ios-link:before {
    content: "\f212"
}

.ivu-icon-ios-list-box-outline:before {
    content: "\f213"
}

.ivu-icon-ios-list-box:before {
    content: "\f214"
}

.ivu-icon-ios-list:before {
    content: "\f215"
}

.ivu-icon-ios-locate-outline:before {
    content: "\f216"
}

.ivu-icon-ios-locate:before {
    content: "\f217"
}

.ivu-icon-ios-lock-outline:before {
    content: "\f218"
}

.ivu-icon-ios-lock:before {
    content: "\f219"
}

.ivu-icon-ios-log-in:before {
    content: "\f21a"
}

.ivu-icon-ios-log-out:before {
    content: "\f21b"
}

.ivu-icon-ios-magnet-outline:before {
    content: "\f21c"
}

.ivu-icon-ios-magnet:before {
    content: "\f21d"
}

.ivu-icon-ios-mail-open-outline:before {
    content: "\f21e"
}

.ivu-icon-ios-mail-open:before {
    content: "\f21f"
}

.ivu-icon-ios-mail-outline:before {
    content: "\f220"
}

.ivu-icon-ios-mail:before {
    content: "\f221"
}

.ivu-icon-ios-male:before {
    content: "\f222"
}

.ivu-icon-ios-man-outline:before {
    content: "\f223"
}

.ivu-icon-ios-man:before {
    content: "\f224"
}

.ivu-icon-ios-map-outline:before {
    content: "\f225"
}

.ivu-icon-ios-map:before {
    content: "\f226"
}

.ivu-icon-ios-medal-outline:before {
    content: "\f227"
}

.ivu-icon-ios-medal:before {
    content: "\f228"
}

.ivu-icon-ios-medical-outline:before {
    content: "\f229"
}

.ivu-icon-ios-medical:before {
    content: "\f22a"
}

.ivu-icon-ios-medkit-outline:before {
    content: "\f22b"
}

.ivu-icon-ios-medkit:before {
    content: "\f22c"
}

.ivu-icon-ios-megaphone-outline:before {
    content: "\f22d"
}

.ivu-icon-ios-megaphone:before {
    content: "\f22e"
}

.ivu-icon-ios-menu-outline:before {
    content: "\f22f"
}

.ivu-icon-ios-menu:before {
    content: "\f230"
}

.ivu-icon-ios-mic-off-outline:before {
    content: "\f231"
}

.ivu-icon-ios-mic-off:before {
    content: "\f232"
}

.ivu-icon-ios-mic-outline:before {
    content: "\f233"
}

.ivu-icon-ios-mic:before {
    content: "\f234"
}

.ivu-icon-ios-microphone-outline:before {
    content: "\f235"
}

.ivu-icon-ios-microphone:before {
    content: "\f236"
}

.ivu-icon-ios-moon-outline:before {
    content: "\f237"
}

.ivu-icon-ios-moon:before {
    content: "\f238"
}

.ivu-icon-ios-more-outline:before {
    content: "\f239"
}

.ivu-icon-ios-more:before {
    content: "\f23a"
}

.ivu-icon-ios-move:before {
    content: "\f23b"
}

.ivu-icon-ios-musical-note-outline:before {
    content: "\f23c"
}

.ivu-icon-ios-musical-note:before {
    content: "\f23d"
}

.ivu-icon-ios-musical-notes-outline:before {
    content: "\f23e"
}

.ivu-icon-ios-musical-notes:before {
    content: "\f23f"
}

.ivu-icon-ios-navigate-outline:before {
    content: "\f240"
}

.ivu-icon-ios-navigate:before {
    content: "\f241"
}

.ivu-icon-ios-no-smoking-outline:before {
    content: "\f242"
}

.ivu-icon-ios-no-smoking:before {
    content: "\f243"
}

.ivu-icon-ios-notifications-off-outline:before {
    content: "\f244"
}

.ivu-icon-ios-notifications-off:before {
    content: "\f245"
}

.ivu-icon-ios-notifications-outline:before {
    content: "\f246"
}

.ivu-icon-ios-notifications:before {
    content: "\f247"
}

.ivu-icon-ios-nuclear-outline:before {
    content: "\f248"
}

.ivu-icon-ios-nuclear:before {
    content: "\f249"
}

.ivu-icon-ios-nutrition-outline:before {
    content: "\f24a"
}

.ivu-icon-ios-nutrition:before {
    content: "\f24b"
}

.ivu-icon-ios-open-outline:before {
    content: "\f24c"
}

.ivu-icon-ios-open:before {
    content: "\f24d"
}

.ivu-icon-ios-options-outline:before {
    content: "\f24e"
}

.ivu-icon-ios-options:before {
    content: "\f24f"
}

.ivu-icon-ios-outlet-outline:before {
    content: "\f250"
}

.ivu-icon-ios-outlet:before {
    content: "\f251"
}

.ivu-icon-ios-paper-outline:before {
    content: "\f252"
}

.ivu-icon-ios-paper-plane-outline:before {
    content: "\f253"
}

.ivu-icon-ios-paper-plane:before {
    content: "\f254"
}

.ivu-icon-ios-paper:before {
    content: "\f255"
}

.ivu-icon-ios-partly-sunny-outline:before {
    content: "\f256"
}

.ivu-icon-ios-partly-sunny:before {
    content: "\f257"
}

.ivu-icon-ios-pause-outline:before {
    content: "\f258"
}

.ivu-icon-ios-pause:before {
    content: "\f259"
}

.ivu-icon-ios-paw-outline:before {
    content: "\f25a"
}

.ivu-icon-ios-paw:before {
    content: "\f25b"
}

.ivu-icon-ios-people-outline:before {
    content: "\f25c"
}

.ivu-icon-ios-people:before {
    content: "\f25d"
}

.ivu-icon-ios-person-add-outline:before {
    content: "\f25e"
}

.ivu-icon-ios-person-add:before {
    content: "\f25f"
}

.ivu-icon-ios-person-outline:before {
    content: "\f260"
}

.ivu-icon-ios-person:before {
    content: "\f261"
}

.ivu-icon-ios-phone-landscape:before {
    content: "\f262"
}

.ivu-icon-ios-phone-portrait:before {
    content: "\f263"
}

.ivu-icon-ios-photos-outline:before {
    content: "\f264"
}

.ivu-icon-ios-photos:before {
    content: "\f265"
}

.ivu-icon-ios-pie-outline:before {
    content: "\f266"
}

.ivu-icon-ios-pie:before {
    content: "\f267"
}

.ivu-icon-ios-pin-outline:before {
    content: "\f268"
}

.ivu-icon-ios-pin:before {
    content: "\f269"
}

.ivu-icon-ios-pint-outline:before {
    content: "\f26a"
}

.ivu-icon-ios-pint:before {
    content: "\f26b"
}

.ivu-icon-ios-pizza-outline:before {
    content: "\f26c"
}

.ivu-icon-ios-pizza:before {
    content: "\f26d"
}

.ivu-icon-ios-plane-outline:before {
    content: "\f26e"
}

.ivu-icon-ios-plane:before {
    content: "\f26f"
}

.ivu-icon-ios-planet-outline:before {
    content: "\f270"
}

.ivu-icon-ios-planet:before {
    content: "\f271"
}

.ivu-icon-ios-play-outline:before {
    content: "\f272"
}

.ivu-icon-ios-play:before {
    content: "\f273"
}

.ivu-icon-ios-podium-outline:before {
    content: "\f274"
}

.ivu-icon-ios-podium:before {
    content: "\f275"
}

.ivu-icon-ios-power-outline:before {
    content: "\f276"
}

.ivu-icon-ios-power:before {
    content: "\f277"
}

.ivu-icon-ios-pricetag-outline:before {
    content: "\f278"
}

.ivu-icon-ios-pricetag:before {
    content: "\f279"
}

.ivu-icon-ios-pricetags-outline:before {
    content: "\f27a"
}

.ivu-icon-ios-pricetags:before {
    content: "\f27b"
}

.ivu-icon-ios-print-outline:before {
    content: "\f27c"
}

.ivu-icon-ios-print:before {
    content: "\f27d"
}

.ivu-icon-ios-pulse-outline:before {
    content: "\f27e"
}

.ivu-icon-ios-pulse:before {
    content: "\f27f"
}

.ivu-icon-ios-qr-scanner:before {
    content: "\f280"
}

.ivu-icon-ios-quote-outline:before {
    content: "\f281"
}

.ivu-icon-ios-quote:before {
    content: "\f282"
}

.ivu-icon-ios-radio-button-off:before {
    content: "\f283"
}

.ivu-icon-ios-radio-button-on:before {
    content: "\f284"
}

.ivu-icon-ios-radio-outline:before {
    content: "\f285"
}

.ivu-icon-ios-radio:before {
    content: "\f286"
}

.ivu-icon-ios-rainy-outline:before {
    content: "\f287"
}

.ivu-icon-ios-rainy:before {
    content: "\f288"
}

.ivu-icon-ios-recording-outline:before {
    content: "\f289"
}

.ivu-icon-ios-recording:before {
    content: "\f28a"
}

.ivu-icon-ios-redo-outline:before {
    content: "\f28b"
}

.ivu-icon-ios-redo:before {
    content: "\f28c"
}

.ivu-icon-ios-refresh-circle-outline:before {
    content: "\f28d"
}

.ivu-icon-ios-refresh-circle:before {
    content: "\f28e"
}

.ivu-icon-ios-refresh:before {
    content: "\f28f"
}

.ivu-icon-ios-remove-circle-outline:before {
    content: "\f290"
}

.ivu-icon-ios-remove-circle:before {
    content: "\f291"
}

.ivu-icon-ios-remove:before {
    content: "\f292"
}

.ivu-icon-ios-reorder:before {
    content: "\f293"
}

.ivu-icon-ios-repeat:before {
    content: "\f294"
}

.ivu-icon-ios-resize:before {
    content: "\f295"
}

.ivu-icon-ios-restaurant-outline:before {
    content: "\f296"
}

.ivu-icon-ios-restaurant:before {
    content: "\f297"
}

.ivu-icon-ios-return-left:before {
    content: "\f298"
}

.ivu-icon-ios-return-right:before {
    content: "\f299"
}

.ivu-icon-ios-reverse-camera-outline:before {
    content: "\f29a"
}

.ivu-icon-ios-reverse-camera:before {
    content: "\f29b"
}

.ivu-icon-ios-rewind-outline:before {
    content: "\f29c"
}

.ivu-icon-ios-rewind:before {
    content: "\f29d"
}

.ivu-icon-ios-ribbon-outline:before {
    content: "\f29e"
}

.ivu-icon-ios-ribbon:before {
    content: "\f29f"
}

.ivu-icon-ios-rose-outline:before {
    content: "\f2a0"
}

.ivu-icon-ios-rose:before {
    content: "\f2a1"
}

.ivu-icon-ios-sad-outline:before {
    content: "\f2a2"
}

.ivu-icon-ios-sad:before {
    content: "\f2a3"
}

.ivu-icon-ios-school-outline:before {
    content: "\f2a4"
}

.ivu-icon-ios-school:before {
    content: "\f2a5"
}

.ivu-icon-ios-search-outline:before {
    content: "\f2a6"
}

.ivu-icon-ios-search:before {
    content: "\f2a7"
}

.ivu-icon-ios-send-outline:before {
    content: "\f2a8"
}

.ivu-icon-ios-send:before {
    content: "\f2a9"
}

.ivu-icon-ios-settings-outline:before {
    content: "\f2aa"
}

.ivu-icon-ios-settings:before {
    content: "\f2ab"
}

.ivu-icon-ios-share-alt-outline:before {
    content: "\f2ac"
}

.ivu-icon-ios-share-alt:before {
    content: "\f2ad"
}

.ivu-icon-ios-share-outline:before {
    content: "\f2ae"
}

.ivu-icon-ios-share:before {
    content: "\f2af"
}

.ivu-icon-ios-shirt-outline:before {
    content: "\f2b0"
}

.ivu-icon-ios-shirt:before {
    content: "\f2b1"
}

.ivu-icon-ios-shuffle:before {
    content: "\f2b2"
}

.ivu-icon-ios-skip-backward-outline:before {
    content: "\f2b3"
}

.ivu-icon-ios-skip-backward:before {
    content: "\f2b4"
}

.ivu-icon-ios-skip-forward-outline:before {
    content: "\f2b5"
}

.ivu-icon-ios-skip-forward:before {
    content: "\f2b6"
}

.ivu-icon-ios-snow-outline:before {
    content: "\f2b7"
}

.ivu-icon-ios-snow:before {
    content: "\f2b8"
}

.ivu-icon-ios-speedometer-outline:before {
    content: "\f2b9"
}

.ivu-icon-ios-speedometer:before {
    content: "\f2ba"
}

.ivu-icon-ios-square-outline:before {
    content: "\f2bb"
}

.ivu-icon-ios-square:before {
    content: "\f2bc"
}

.ivu-icon-ios-star-half:before {
    content: "\f2bd"
}

.ivu-icon-ios-star-outline:before {
    content: "\f2be"
}

.ivu-icon-ios-star:before {
    content: "\f2bf"
}

.ivu-icon-ios-stats-outline:before {
    content: "\f2c0"
}

.ivu-icon-ios-stats:before {
    content: "\f2c1"
}

.ivu-icon-ios-stopwatch-outline:before {
    content: "\f2c2"
}

.ivu-icon-ios-stopwatch:before {
    content: "\f2c3"
}

.ivu-icon-ios-subway-outline:before {
    content: "\f2c4"
}

.ivu-icon-ios-subway:before {
    content: "\f2c5"
}

.ivu-icon-ios-sunny-outline:before {
    content: "\f2c6"
}

.ivu-icon-ios-sunny:before {
    content: "\f2c7"
}

.ivu-icon-ios-swap:before {
    content: "\f2c8"
}

.ivu-icon-ios-switch-outline:before {
    content: "\f2c9"
}

.ivu-icon-ios-switch:before {
    content: "\f2ca"
}

.ivu-icon-ios-sync:before {
    content: "\f2cb"
}

.ivu-icon-ios-tablet-landscape:before {
    content: "\f2cc"
}

.ivu-icon-ios-tablet-portrait:before {
    content: "\f2cd"
}

.ivu-icon-ios-tennisball-outline:before {
    content: "\f2ce"
}

.ivu-icon-ios-tennisball:before {
    content: "\f2cf"
}

.ivu-icon-ios-text-outline:before {
    content: "\f2d0"
}

.ivu-icon-ios-text:before {
    content: "\f2d1"
}

.ivu-icon-ios-thermometer-outline:before {
    content: "\f2d2"
}

.ivu-icon-ios-thermometer:before {
    content: "\f2d3"
}

.ivu-icon-ios-thumbs-down-outline:before {
    content: "\f2d4"
}

.ivu-icon-ios-thumbs-down:before {
    content: "\f2d5"
}

.ivu-icon-ios-thumbs-up-outline:before {
    content: "\f2d6"
}

.ivu-icon-ios-thumbs-up:before {
    content: "\f2d7"
}

.ivu-icon-ios-thunderstorm-outline:before {
    content: "\f2d8"
}

.ivu-icon-ios-thunderstorm:before {
    content: "\f2d9"
}

.ivu-icon-ios-time-outline:before {
    content: "\f2da"
}

.ivu-icon-ios-time:before {
    content: "\f2db"
}

.ivu-icon-ios-timer-outline:before {
    content: "\f2dc"
}

.ivu-icon-ios-timer:before {
    content: "\f2dd"
}

.ivu-icon-ios-train-outline:before {
    content: "\f2de"
}

.ivu-icon-ios-train:before {
    content: "\f2df"
}

.ivu-icon-ios-transgender:before {
    content: "\f2e0"
}

.ivu-icon-ios-trash-outline:before {
    content: "\f2e1"
}

.ivu-icon-ios-trash:before {
    content: "\f2e2"
}

.ivu-icon-ios-trending-down:before {
    content: "\f2e3"
}

.ivu-icon-ios-trending-up:before {
    content: "\f2e4"
}

.ivu-icon-ios-trophy-outline:before {
    content: "\f2e5"
}

.ivu-icon-ios-trophy:before {
    content: "\f2e6"
}

.ivu-icon-ios-umbrella-outline:before {
    content: "\f2e7"
}

.ivu-icon-ios-umbrella:before {
    content: "\f2e8"
}

.ivu-icon-ios-undo-outline:before {
    content: "\f2e9"
}

.ivu-icon-ios-undo:before {
    content: "\f2ea"
}

.ivu-icon-ios-unlock-outline:before {
    content: "\f2eb"
}

.ivu-icon-ios-unlock:before {
    content: "\f2ec"
}

.ivu-icon-ios-videocam-outline:before {
    content: "\f2ed"
}

.ivu-icon-ios-videocam:before {
    content: "\f2ee"
}

.ivu-icon-ios-volume-down:before {
    content: "\f2ef"
}

.ivu-icon-ios-volume-mute:before {
    content: "\f2f0"
}

.ivu-icon-ios-volume-off:before {
    content: "\f2f1"
}

.ivu-icon-ios-volume-up:before {
    content: "\f2f2"
}

.ivu-icon-ios-walk:before {
    content: "\f2f3"
}

.ivu-icon-ios-warning-outline:before {
    content: "\f2f4"
}

.ivu-icon-ios-warning:before {
    content: "\f2f5"
}

.ivu-icon-ios-watch:before {
    content: "\f2f6"
}

.ivu-icon-ios-water-outline:before {
    content: "\f2f7"
}

.ivu-icon-ios-water:before {
    content: "\f2f8"
}

.ivu-icon-ios-wifi-outline:before {
    content: "\f2f9"
}

.ivu-icon-ios-wifi:before {
    content: "\f2fa"
}

.ivu-icon-ios-wine-outline:before {
    content: "\f2fb"
}

.ivu-icon-ios-wine:before {
    content: "\f2fc"
}

.ivu-icon-ios-woman-outline:before {
    content: "\f2fd"
}

.ivu-icon-ios-woman:before {
    content: "\f2fe"
}

.ivu-icon-logo-android:before {
    content: "\f2ff"
}

.ivu-icon-logo-angular:before {
    content: "\f300"
}

.ivu-icon-logo-apple:before {
    content: "\f301"
}

.ivu-icon-logo-bitcoin:before {
    content: "\f302"
}

.ivu-icon-logo-buffer:before {
    content: "\f303"
}

.ivu-icon-logo-chrome:before {
    content: "\f304"
}

.ivu-icon-logo-codepen:before {
    content: "\f305"
}

.ivu-icon-logo-css3:before {
    content: "\f306"
}

.ivu-icon-logo-designernews:before {
    content: "\f307"
}

.ivu-icon-logo-dribbble:before {
    content: "\f308"
}

.ivu-icon-logo-dropbox:before {
    content: "\f309"
}

.ivu-icon-logo-euro:before {
    content: "\f30a"
}

.ivu-icon-logo-facebook:before {
    content: "\f30b"
}

.ivu-icon-logo-foursquare:before {
    content: "\f30c"
}

.ivu-icon-logo-freebsd-devil:before {
    content: "\f30d"
}

.ivu-icon-logo-github:before {
    content: "\f30e"
}

.ivu-icon-logo-google:before {
    content: "\f30f"
}

.ivu-icon-logo-googleplus:before {
    content: "\f310"
}

.ivu-icon-logo-hackernews:before {
    content: "\f311"
}

.ivu-icon-logo-html5:before {
    content: "\f312"
}

.ivu-icon-logo-instagram:before {
    content: "\f313"
}

.ivu-icon-logo-javascript:before {
    content: "\f314"
}

.ivu-icon-logo-linkedin:before {
    content: "\f315"
}

.ivu-icon-logo-markdown:before {
    content: "\f316"
}

.ivu-icon-logo-nodejs:before {
    content: "\f317"
}

.ivu-icon-logo-octocat:before {
    content: "\f318"
}

.ivu-icon-logo-pinterest:before {
    content: "\f319"
}

.ivu-icon-logo-playstation:before {
    content: "\f31a"
}

.ivu-icon-logo-python:before {
    content: "\f31b"
}

.ivu-icon-logo-reddit:before {
    content: "\f31c"
}

.ivu-icon-logo-rss:before {
    content: "\f31d"
}

.ivu-icon-logo-sass:before {
    content: "\f31e"
}

.ivu-icon-logo-skype:before {
    content: "\f31f"
}

.ivu-icon-logo-snapchat:before {
    content: "\f320"
}

.ivu-icon-logo-steam:before {
    content: "\f321"
}

.ivu-icon-logo-tumblr:before {
    content: "\f322"
}

.ivu-icon-logo-tux:before {
    content: "\f323"
}

.ivu-icon-logo-twitch:before {
    content: "\f324"
}

.ivu-icon-logo-twitter:before {
    content: "\f325"
}

.ivu-icon-logo-usd:before {
    content: "\f326"
}

.ivu-icon-logo-vimeo:before {
    content: "\f327"
}

.ivu-icon-logo-whatsapp:before {
    content: "\f328"
}

.ivu-icon-logo-windows:before {
    content: "\f329"
}

.ivu-icon-logo-wordpress:before {
    content: "\f32a"
}

.ivu-icon-logo-xbox:before {
    content: "\f32b"
}

.ivu-icon-logo-yahoo:before {
    content: "\f32c"
}

.ivu-icon-logo-yen:before {
    content: "\f32d"
}

.ivu-icon-logo-youtube:before {
    content: "\f32e"
}

.ivu-icon-md-add-circle:before {
    content: "\f32f"
}

.ivu-icon-md-add:before {
    content: "\f330"
}

.ivu-icon-md-alarm:before {
    content: "\f331"
}

.ivu-icon-md-albums:before {
    content: "\f332"
}

.ivu-icon-md-alert:before {
    content: "\f333"
}

.ivu-icon-md-american-football:before {
    content: "\f334"
}

.ivu-icon-md-analytics:before {
    content: "\f335"
}

.ivu-icon-md-aperture:before {
    content: "\f336"
}

.ivu-icon-md-apps:before {
    content: "\f337"
}

.ivu-icon-md-appstore:before {
    content: "\f338"
}

.ivu-icon-md-archive:before {
    content: "\f339"
}

.ivu-icon-md-arrow-back:before {
    content: "\f33a"
}

.ivu-icon-md-arrow-down:before {
    content: "\f33b"
}

.ivu-icon-md-arrow-dropdown-circle:before {
    content: "\f33c"
}

.ivu-icon-md-arrow-dropdown:before {
    content: "\f33d"
}

.ivu-icon-md-arrow-dropleft-circle:before {
    content: "\f33e"
}

.ivu-icon-md-arrow-dropleft:before {
    content: "\f33f"
}

.ivu-icon-md-arrow-dropright-circle:before {
    content: "\f340"
}

.ivu-icon-md-arrow-dropright:before {
    content: "\f341"
}

.ivu-icon-md-arrow-dropup-circle:before {
    content: "\f342"
}

.ivu-icon-md-arrow-dropup:before {
    content: "\f343"
}

.ivu-icon-md-arrow-forward:before {
    content: "\f344"
}

.ivu-icon-md-arrow-round-back:before {
    content: "\f345"
}

.ivu-icon-md-arrow-round-down:before {
    content: "\f346"
}

.ivu-icon-md-arrow-round-forward:before {
    content: "\f347"
}

.ivu-icon-md-arrow-round-up:before {
    content: "\f348"
}

.ivu-icon-md-arrow-up:before {
    content: "\f349"
}

.ivu-icon-md-at:before {
    content: "\f34a"
}

.ivu-icon-md-attach:before {
    content: "\f34b"
}

.ivu-icon-md-backspace:before {
    content: "\f34c"
}

.ivu-icon-md-barcode:before {
    content: "\f34d"
}

.ivu-icon-md-baseball:before {
    content: "\f34e"
}

.ivu-icon-md-basket:before {
    content: "\f34f"
}

.ivu-icon-md-basketball:before {
    content: "\f350"
}

.ivu-icon-md-battery-charging:before {
    content: "\f351"
}

.ivu-icon-md-battery-dead:before {
    content: "\f352"
}

.ivu-icon-md-battery-full:before {
    content: "\f353"
}

.ivu-icon-md-beaker:before {
    content: "\f354"
}

.ivu-icon-md-beer:before {
    content: "\f355"
}

.ivu-icon-md-bicycle:before {
    content: "\f356"
}

.ivu-icon-md-bluetooth:before {
    content: "\f357"
}

.ivu-icon-md-boat:before {
    content: "\f358"
}

.ivu-icon-md-body:before {
    content: "\f359"
}

.ivu-icon-md-bonfire:before {
    content: "\f35a"
}

.ivu-icon-md-book:before {
    content: "\f35b"
}

.ivu-icon-md-bookmark:before {
    content: "\f35c"
}

.ivu-icon-md-bookmarks:before {
    content: "\f35d"
}

.ivu-icon-md-bowtie:before {
    content: "\f35e"
}

.ivu-icon-md-briefcase:before {
    content: "\f35f"
}

.ivu-icon-md-browsers:before {
    content: "\f360"
}

.ivu-icon-md-brush:before {
    content: "\f361"
}

.ivu-icon-md-bug:before {
    content: "\f362"
}

.ivu-icon-md-build:before {
    content: "\f363"
}

.ivu-icon-md-bulb:before {
    content: "\f364"
}

.ivu-icon-md-bus:before {
    content: "\f365"
}

.ivu-icon-md-cafe:before {
    content: "\f366"
}

.ivu-icon-md-calculator:before {
    content: "\f367"
}

.ivu-icon-md-calendar:before {
    content: "\f368"
}

.ivu-icon-md-call:before {
    content: "\f369"
}

.ivu-icon-md-camera:before {
    content: "\f36a"
}

.ivu-icon-md-car:before {
    content: "\f36b"
}

.ivu-icon-md-card:before {
    content: "\f36c"
}

.ivu-icon-md-cart:before {
    content: "\f36d"
}

.ivu-icon-md-cash:before {
    content: "\f36e"
}

.ivu-icon-md-chatboxes:before {
    content: "\f36f"
}

.ivu-icon-md-chatbubbles:before {
    content: "\f370"
}

.ivu-icon-md-checkbox-outline:before {
    content: "\f371"
}

.ivu-icon-md-checkbox:before {
    content: "\f372"
}

.ivu-icon-md-checkmark-circle-outline:before {
    content: "\f373"
}

.ivu-icon-md-checkmark-circle:before {
    content: "\f374"
}

.ivu-icon-md-checkmark:before {
    content: "\f375"
}

.ivu-icon-md-clipboard:before {
    content: "\f376"
}

.ivu-icon-md-clock:before {
    content: "\f377"
}

.ivu-icon-md-close-circle:before {
    content: "\f378"
}

.ivu-icon-md-close:before {
    content: "\f379"
}

.ivu-icon-md-closed-captioning:before {
    content: "\f37a"
}

.ivu-icon-md-cloud-circle:before {
    content: "\f37b"
}

.ivu-icon-md-cloud-done:before {
    content: "\f37c"
}

.ivu-icon-md-cloud-download:before {
    content: "\f37d"
}

.ivu-icon-md-cloud-outline:before {
    content: "\f37e"
}

.ivu-icon-md-cloud-upload:before {
    content: "\f37f"
}

.ivu-icon-md-cloud:before {
    content: "\f380"
}

.ivu-icon-md-cloudy-night:before {
    content: "\f381"
}

.ivu-icon-md-cloudy:before {
    content: "\f382"
}

.ivu-icon-md-code-download:before {
    content: "\f383"
}

.ivu-icon-md-code-working:before {
    content: "\f384"
}

.ivu-icon-md-code:before {
    content: "\f385"
}

.ivu-icon-md-cog:before {
    content: "\f386"
}

.ivu-icon-md-color-fill:before {
    content: "\f387"
}

.ivu-icon-md-color-filter:before {
    content: "\f388"
}

.ivu-icon-md-color-palette:before {
    content: "\f389"
}

.ivu-icon-md-color-wand:before {
    content: "\f38a"
}

.ivu-icon-md-compass:before {
    content: "\f38b"
}

.ivu-icon-md-construct:before {
    content: "\f38c"
}

.ivu-icon-md-contact:before {
    content: "\f38d"
}

.ivu-icon-md-contacts:before {
    content: "\f38e"
}

.ivu-icon-md-contract:before {
    content: "\f38f"
}

.ivu-icon-md-contrast:before {
    content: "\f390"
}

.ivu-icon-md-copy:before {
    content: "\f391"
}

.ivu-icon-md-create:before {
    content: "\f392"
}

.ivu-icon-md-crop:before {
    content: "\f393"
}

.ivu-icon-md-cube:before {
    content: "\f394"
}

.ivu-icon-md-cut:before {
    content: "\f395"
}

.ivu-icon-md-desktop:before {
    content: "\f396"
}

.ivu-icon-md-disc:before {
    content: "\f397"
}

.ivu-icon-md-document:before {
    content: "\f398"
}

.ivu-icon-md-done-all:before {
    content: "\f399"
}

.ivu-icon-md-download:before {
    content: "\f39a"
}

.ivu-icon-md-easel:before {
    content: "\f39b"
}

.ivu-icon-md-egg:before {
    content: "\f39c"
}

.ivu-icon-md-exit:before {
    content: "\f39d"
}

.ivu-icon-md-expand:before {
    content: "\f39e"
}

.ivu-icon-md-eye-off:before {
    content: "\f39f"
}

.ivu-icon-md-eye:before {
    content: "\f3a0"
}

.ivu-icon-md-fastforward:before {
    content: "\f3a1"
}

.ivu-icon-md-female:before {
    content: "\f3a2"
}

.ivu-icon-md-filing:before {
    content: "\f3a3"
}

.ivu-icon-md-film:before {
    content: "\f3a4"
}

.ivu-icon-md-finger-print:before {
    content: "\f3a5"
}

.ivu-icon-md-flag:before {
    content: "\f3a6"
}

.ivu-icon-md-flame:before {
    content: "\f3a7"
}

.ivu-icon-md-flash:before {
    content: "\f3a8"
}

.ivu-icon-md-flask:before {
    content: "\f3a9"
}

.ivu-icon-md-flower:before {
    content: "\f3aa"
}

.ivu-icon-md-folder-open:before {
    content: "\f3ab"
}

.ivu-icon-md-folder:before {
    content: "\f3ac"
}

.ivu-icon-md-football:before {
    content: "\f3ad"
}

.ivu-icon-md-funnel:before {
    content: "\f3ae"
}

.ivu-icon-md-game-controller-a:before {
    content: "\f3af"
}

.ivu-icon-md-game-controller-b:before {
    content: "\f3b0"
}

.ivu-icon-md-git-branch:before {
    content: "\f3b1"
}

.ivu-icon-md-git-commit:before {
    content: "\f3b2"
}

.ivu-icon-md-git-compare:before {
    content: "\f3b3"
}

.ivu-icon-md-git-merge:before {
    content: "\f3b4"
}

.ivu-icon-md-git-network:before {
    content: "\f3b5"
}

.ivu-icon-md-git-pull-request:before {
    content: "\f3b6"
}

.ivu-icon-md-glasses:before {
    content: "\f3b7"
}

.ivu-icon-md-globe:before {
    content: "\f3b8"
}

.ivu-icon-md-grid:before {
    content: "\f3b9"
}

.ivu-icon-md-hammer:before {
    content: "\f3ba"
}

.ivu-icon-md-hand:before {
    content: "\f3bb"
}

.ivu-icon-md-happy:before {
    content: "\f3bc"
}

.ivu-icon-md-headset:before {
    content: "\f3bd"
}

.ivu-icon-md-heart-outline:before {
    content: "\f3be"
}

.ivu-icon-md-heart:before {
    content: "\f3bf"
}

.ivu-icon-md-help-buoy:before {
    content: "\f3c0"
}

.ivu-icon-md-help-circle:before {
    content: "\f3c1"
}

.ivu-icon-md-help:before {
    content: "\f3c2"
}

.ivu-icon-md-home:before {
    content: "\f3c3"
}

.ivu-icon-md-ice-cream:before {
    content: "\f3c4"
}

.ivu-icon-md-image:before {
    content: "\f3c5"
}

.ivu-icon-md-images:before {
    content: "\f3c6"
}

.ivu-icon-md-infinite:before {
    content: "\f3c7"
}

.ivu-icon-md-information-circle:before {
    content: "\f3c8"
}

.ivu-icon-md-information:before {
    content: "\f3c9"
}

.ivu-icon-md-ionic:before {
    content: "\f3ca"
}

.ivu-icon-md-ionitron:before {
    content: "\f3cb"
}

.ivu-icon-md-jet:before {
    content: "\f3cc"
}

.ivu-icon-md-key:before {
    content: "\f3cd"
}

.ivu-icon-md-keypad:before {
    content: "\f3ce"
}

.ivu-icon-md-laptop:before {
    content: "\f3cf"
}

.ivu-icon-md-leaf:before {
    content: "\f3d0"
}

.ivu-icon-md-link:before {
    content: "\f3d1"
}

.ivu-icon-md-list-box:before {
    content: "\f3d2"
}

.ivu-icon-md-list:before {
    content: "\f3d3"
}

.ivu-icon-md-locate:before {
    content: "\f3d4"
}

.ivu-icon-md-lock:before {
    content: "\f3d5"
}

.ivu-icon-md-log-in:before {
    content: "\f3d6"
}

.ivu-icon-md-log-out:before {
    content: "\f3d7"
}

.ivu-icon-md-magnet:before {
    content: "\f3d8"
}

.ivu-icon-md-mail-open:before {
    content: "\f3d9"
}

.ivu-icon-md-mail:before {
    content: "\f3da"
}

.ivu-icon-md-male:before {
    content: "\f3db"
}

.ivu-icon-md-man:before {
    content: "\f3dc"
}

.ivu-icon-md-map:before {
    content: "\f3dd"
}

.ivu-icon-md-medal:before {
    content: "\f3de"
}

.ivu-icon-md-medical:before {
    content: "\f3df"
}

.ivu-icon-md-medkit:before {
    content: "\f3e0"
}

.ivu-icon-md-megaphone:before {
    content: "\f3e1"
}

.ivu-icon-md-menu:before {
    content: "\f3e2"
}

.ivu-icon-md-mic-off:before {
    content: "\f3e3"
}

.ivu-icon-md-mic:before {
    content: "\f3e4"
}

.ivu-icon-md-microphone:before {
    content: "\f3e5"
}

.ivu-icon-md-moon:before {
    content: "\f3e6"
}

.ivu-icon-md-more:before {
    content: "\f3e7"
}

.ivu-icon-md-move:before {
    content: "\f3e8"
}

.ivu-icon-md-musical-note:before {
    content: "\f3e9"
}

.ivu-icon-md-musical-notes:before {
    content: "\f3ea"
}

.ivu-icon-md-navigate:before {
    content: "\f3eb"
}

.ivu-icon-md-no-smoking:before {
    content: "\f3ec"
}

.ivu-icon-md-notifications-off:before {
    content: "\f3ed"
}

.ivu-icon-md-notifications-outline:before {
    content: "\f3ee"
}

.ivu-icon-md-notifications:before {
    content: "\f3ef"
}

.ivu-icon-md-nuclear:before {
    content: "\f3f0"
}

.ivu-icon-md-nutrition:before {
    content: "\f3f1"
}

.ivu-icon-md-open:before {
    content: "\f3f2"
}

.ivu-icon-md-options:before {
    content: "\f3f3"
}

.ivu-icon-md-outlet:before {
    content: "\f3f4"
}

.ivu-icon-md-paper-plane:before {
    content: "\f3f5"
}

.ivu-icon-md-paper:before {
    content: "\f3f6"
}

.ivu-icon-md-partly-sunny:before {
    content: "\f3f7"
}

.ivu-icon-md-pause:before {
    content: "\f3f8"
}

.ivu-icon-md-paw:before {
    content: "\f3f9"
}

.ivu-icon-md-people:before {
    content: "\f3fa"
}

.ivu-icon-md-person-add:before {
    content: "\f3fb"
}

.ivu-icon-md-person:before {
    content: "\f3fc"
}

.ivu-icon-md-phone-landscape:before {
    content: "\f3fd"
}

.ivu-icon-md-phone-portrait:before {
    content: "\f3fe"
}

.ivu-icon-md-photos:before {
    content: "\f3ff"
}

.ivu-icon-md-pie:before {
    content: "\f400"
}

.ivu-icon-md-pin:before {
    content: "\f401"
}

.ivu-icon-md-pint:before {
    content: "\f402"
}

.ivu-icon-md-pizza:before {
    content: "\f403"
}

.ivu-icon-md-plane:before {
    content: "\f404"
}

.ivu-icon-md-planet:before {
    content: "\f405"
}

.ivu-icon-md-play:before {
    content: "\f406"
}

.ivu-icon-md-podium:before {
    content: "\f407"
}

.ivu-icon-md-power:before {
    content: "\f408"
}

.ivu-icon-md-pricetag:before {
    content: "\f409"
}

.ivu-icon-md-pricetags:before {
    content: "\f40a"
}

.ivu-icon-md-print:before {
    content: "\f40b"
}

.ivu-icon-md-pulse:before {
    content: "\f40c"
}

.ivu-icon-md-qr-scanner:before {
    content: "\f40d"
}

.ivu-icon-md-quote:before {
    content: "\f40e"
}

.ivu-icon-md-radio-button-off:before {
    content: "\f40f"
}

.ivu-icon-md-radio-button-on:before {
    content: "\f410"
}

.ivu-icon-md-radio:before {
    content: "\f411"
}

.ivu-icon-md-rainy:before {
    content: "\f412"
}

.ivu-icon-md-recording:before {
    content: "\f413"
}

.ivu-icon-md-redo:before {
    content: "\f414"
}

.ivu-icon-md-refresh-circle:before {
    content: "\f415"
}

.ivu-icon-md-refresh:before {
    content: "\f416"
}

.ivu-icon-md-remove-circle:before {
    content: "\f417"
}

.ivu-icon-md-remove:before {
    content: "\f418"
}

.ivu-icon-md-reorder:before {
    content: "\f419"
}

.ivu-icon-md-repeat:before {
    content: "\f41a"
}

.ivu-icon-md-resize:before {
    content: "\f41b"
}

.ivu-icon-md-restaurant:before {
    content: "\f41c"
}

.ivu-icon-md-return-left:before {
    content: "\f41d"
}

.ivu-icon-md-return-right:before {
    content: "\f41e"
}

.ivu-icon-md-reverse-camera:before {
    content: "\f41f"
}

.ivu-icon-md-rewind:before {
    content: "\f420"
}

.ivu-icon-md-ribbon:before {
    content: "\f421"
}

.ivu-icon-md-rose:before {
    content: "\f422"
}

.ivu-icon-md-sad:before {
    content: "\f423"
}

.ivu-icon-md-school:before {
    content: "\f424"
}

.ivu-icon-md-search:before {
    content: "\f425"
}

.ivu-icon-md-send:before {
    content: "\f426"
}

.ivu-icon-md-settings:before {
    content: "\f427"
}

.ivu-icon-md-share-alt:before {
    content: "\f428"
}

.ivu-icon-md-share:before {
    content: "\f429"
}

.ivu-icon-md-shirt:before {
    content: "\f42a"
}

.ivu-icon-md-shuffle:before {
    content: "\f42b"
}

.ivu-icon-md-skip-backward:before {
    content: "\f42c"
}

.ivu-icon-md-skip-forward:before {
    content: "\f42d"
}

.ivu-icon-md-snow:before {
    content: "\f42e"
}

.ivu-icon-md-speedometer:before {
    content: "\f42f"
}

.ivu-icon-md-square-outline:before {
    content: "\f430"
}

.ivu-icon-md-square:before {
    content: "\f431"
}

.ivu-icon-md-star-half:before {
    content: "\f432"
}

.ivu-icon-md-star-outline:before {
    content: "\f433"
}

.ivu-icon-md-star:before {
    content: "\f434"
}

.ivu-icon-md-stats:before {
    content: "\f435"
}

.ivu-icon-md-stopwatch:before {
    content: "\f436"
}

.ivu-icon-md-subway:before {
    content: "\f437"
}

.ivu-icon-md-sunny:before {
    content: "\f438"
}

.ivu-icon-md-swap:before {
    content: "\f439"
}

.ivu-icon-md-switch:before {
    content: "\f43a"
}

.ivu-icon-md-sync:before {
    content: "\f43b"
}

.ivu-icon-md-tablet-landscape:before {
    content: "\f43c"
}

.ivu-icon-md-tablet-portrait:before {
    content: "\f43d"
}

.ivu-icon-md-tennisball:before {
    content: "\f43e"
}

.ivu-icon-md-text:before {
    content: "\f43f"
}

.ivu-icon-md-thermometer:before {
    content: "\f440"
}

.ivu-icon-md-thumbs-down:before {
    content: "\f441"
}

.ivu-icon-md-thumbs-up:before {
    content: "\f442"
}

.ivu-icon-md-thunderstorm:before {
    content: "\f443"
}

.ivu-icon-md-time:before {
    content: "\f444"
}

.ivu-icon-md-timer:before {
    content: "\f445"
}

.ivu-icon-md-train:before {
    content: "\f446"
}

.ivu-icon-md-transgender:before {
    content: "\f447"
}

.ivu-icon-md-trash:before {
    content: "\f448"
}

.ivu-icon-md-trending-down:before {
    content: "\f449"
}

.ivu-icon-md-trending-up:before {
    content: "\f44a"
}

.ivu-icon-md-trophy:before {
    content: "\f44b"
}

.ivu-icon-md-umbrella:before {
    content: "\f44c"
}

.ivu-icon-md-undo:before {
    content: "\f44d"
}

.ivu-icon-md-unlock:before {
    content: "\f44e"
}

.ivu-icon-md-videocam:before {
    content: "\f44f"
}

.ivu-icon-md-volume-down:before {
    content: "\f450"
}

.ivu-icon-md-volume-mute:before {
    content: "\f451"
}

.ivu-icon-md-volume-off:before {
    content: "\f452"
}

.ivu-icon-md-volume-up:before {
    content: "\f453"
}

.ivu-icon-md-walk:before {
    content: "\f454"
}

.ivu-icon-md-warning:before {
    content: "\f455"
}

.ivu-icon-md-watch:before {
    content: "\f456"
}

.ivu-icon-md-water:before {
    content: "\f457"
}

.ivu-icon-md-wifi:before {
    content: "\f458"
}

.ivu-icon-md-wine:before {
    content: "\f459"
}

.ivu-icon-md-woman:before {
    content: "\f45a"
}

.ivu-icon-ios-loading:before {
    content: "\f45b"
}

.ivu-row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap
}

.ivu-row::after,
.ivu-row::before {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.ivu-row-no-wrap {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap
}

.ivu-row-start {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.ivu-row-center {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.ivu-row-end {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

.ivu-row-space-between {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.ivu-row-space-around {
    -ms-flex-pack: distribute;
    justify-content: space-around
}

.ivu-row-top {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.ivu-row-middle {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.ivu-row-bottom {
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end
}

.ivu-col {
    position: relative;
    max-width: 100%;
    min-height: 1px
}

.ivu-col-span-24 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
}

.ivu-col-push-24 {
    left: 100%
}

.ivu-col-pull-24 {
    right: 100%
}

.ivu-col-offset-24 {
    margin-left: 100%
}

.ivu-col-order-24 {
    -webkit-box-ordinal-group: 25;
    -ms-flex-order: 24;
    order: 24
}

.ivu-col-span-23 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 95.83333333%;
    flex: 0 0 95.83333333%;
    max-width: 95.83333333%
}

.ivu-col-push-23 {
    left: 95.83333333%
}

.ivu-col-pull-23 {
    right: 95.83333333%
}

.ivu-col-offset-23 {
    margin-left: 95.83333333%
}

.ivu-col-order-23 {
    -webkit-box-ordinal-group: 24;
    -ms-flex-order: 23;
    order: 23
}

.ivu-col-span-22 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.66666667%;
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%
}

.ivu-col-push-22 {
    left: 91.66666667%
}

.ivu-col-pull-22 {
    right: 91.66666667%
}

.ivu-col-offset-22 {
    margin-left: 91.66666667%
}

.ivu-col-order-22 {
    -webkit-box-ordinal-group: 23;
    -ms-flex-order: 22;
    order: 22
}

.ivu-col-span-21 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 87.5%;
    flex: 0 0 87.5%;
    max-width: 87.5%
}

.ivu-col-push-21 {
    left: 87.5%
}

.ivu-col-pull-21 {
    right: 87.5%
}

.ivu-col-offset-21 {
    margin-left: 87.5%
}

.ivu-col-order-21 {
    -webkit-box-ordinal-group: 22;
    -ms-flex-order: 21;
    order: 21
}

.ivu-col-span-20 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.33333333%;
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%
}

.ivu-col-push-20 {
    left: 83.33333333%
}

.ivu-col-pull-20 {
    right: 83.33333333%
}

.ivu-col-offset-20 {
    margin-left: 83.33333333%
}

.ivu-col-order-20 {
    -webkit-box-ordinal-group: 21;
    -ms-flex-order: 20;
    order: 20
}

.ivu-col-span-19 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 79.16666667%;
    flex: 0 0 79.16666667%;
    max-width: 79.16666667%
}

.ivu-col-push-19 {
    left: 79.16666667%
}

.ivu-col-pull-19 {
    right: 79.16666667%
}

.ivu-col-offset-19 {
    margin-left: 79.16666667%
}

.ivu-col-order-19 {
    -webkit-box-ordinal-group: 20;
    -ms-flex-order: 19;
    order: 19
}

.ivu-col-span-18 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
}

.ivu-col-push-18 {
    left: 75%
}

.ivu-col-pull-18 {
    right: 75%
}

.ivu-col-offset-18 {
    margin-left: 75%
}

.ivu-col-order-18 {
    -webkit-box-ordinal-group: 19;
    -ms-flex-order: 18;
    order: 18
}

.ivu-col-span-17 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 70.83333333%;
    flex: 0 0 70.83333333%;
    max-width: 70.83333333%
}

.ivu-col-push-17 {
    left: 70.83333333%
}

.ivu-col-pull-17 {
    right: 70.83333333%
}

.ivu-col-offset-17 {
    margin-left: 70.83333333%
}

.ivu-col-order-17 {
    -webkit-box-ordinal-group: 18;
    -ms-flex-order: 17;
    order: 17
}

.ivu-col-span-16 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.66666667%;
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%
}

.ivu-col-push-16 {
    left: 66.66666667%
}

.ivu-col-pull-16 {
    right: 66.66666667%
}

.ivu-col-offset-16 {
    margin-left: 66.66666667%
}

.ivu-col-order-16 {
    -webkit-box-ordinal-group: 17;
    -ms-flex-order: 16;
    order: 16
}

.ivu-col-span-15 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 62.5%;
    flex: 0 0 62.5%;
    max-width: 62.5%
}

.ivu-col-push-15 {
    left: 62.5%
}

.ivu-col-pull-15 {
    right: 62.5%
}

.ivu-col-offset-15 {
    margin-left: 62.5%
}

.ivu-col-order-15 {
    -webkit-box-ordinal-group: 16;
    -ms-flex-order: 15;
    order: 15
}

.ivu-col-span-14 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.33333333%;
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%
}

.ivu-col-push-14 {
    left: 58.33333333%
}

.ivu-col-pull-14 {
    right: 58.33333333%
}

.ivu-col-offset-14 {
    margin-left: 58.33333333%
}

.ivu-col-order-14 {
    -webkit-box-ordinal-group: 15;
    -ms-flex-order: 14;
    order: 14
}

.ivu-col-span-13 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 54.16666667%;
    flex: 0 0 54.16666667%;
    max-width: 54.16666667%
}

.ivu-col-push-13 {
    left: 54.16666667%
}

.ivu-col-pull-13 {
    right: 54.16666667%
}

.ivu-col-offset-13 {
    margin-left: 54.16666667%
}

.ivu-col-order-13 {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13
}

.ivu-col-span-12 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
}

.ivu-col-push-12 {
    left: 50%
}

.ivu-col-pull-12 {
    right: 50%
}

.ivu-col-offset-12 {
    margin-left: 50%
}

.ivu-col-order-12 {
    -webkit-box-ordinal-group: 13;
    -ms-flex-order: 12;
    order: 12
}

.ivu-col-span-11 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 45.83333333%;
    flex: 0 0 45.83333333%;
    max-width: 45.83333333%
}

.ivu-col-push-11 {
    left: 45.83333333%
}

.ivu-col-pull-11 {
    right: 45.83333333%
}

.ivu-col-offset-11 {
    margin-left: 45.83333333%
}

.ivu-col-order-11 {
    -webkit-box-ordinal-group: 12;
    -ms-flex-order: 11;
    order: 11
}

.ivu-col-span-10 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.66666667%;
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%
}

.ivu-col-push-10 {
    left: 41.66666667%
}

.ivu-col-pull-10 {
    right: 41.66666667%
}

.ivu-col-offset-10 {
    margin-left: 41.66666667%
}

.ivu-col-order-10 {
    -webkit-box-ordinal-group: 11;
    -ms-flex-order: 10;
    order: 10
}

.ivu-col-span-9 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 37.5%;
    flex: 0 0 37.5%;
    max-width: 37.5%
}

.ivu-col-push-9 {
    left: 37.5%
}

.ivu-col-pull-9 {
    right: 37.5%
}

.ivu-col-offset-9 {
    margin-left: 37.5%
}

.ivu-col-order-9 {
    -webkit-box-ordinal-group: 10;
    -ms-flex-order: 9;
    order: 9
}

.ivu-col-span-8 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333333%;
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%
}

.ivu-col-push-8 {
    left: 33.33333333%
}

.ivu-col-pull-8 {
    right: 33.33333333%
}

.ivu-col-offset-8 {
    margin-left: 33.33333333%
}

.ivu-col-order-8 {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8
}

.ivu-col-span-7 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 29.16666667%;
    flex: 0 0 29.16666667%;
    max-width: 29.16666667%
}

.ivu-col-push-7 {
    left: 29.16666667%
}

.ivu-col-pull-7 {
    right: 29.16666667%
}

.ivu-col-offset-7 {
    margin-left: 29.16666667%
}

.ivu-col-order-7 {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7
}

.ivu-col-span-6 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
}

.ivu-col-push-6 {
    left: 25%
}

.ivu-col-pull-6 {
    right: 25%
}

.ivu-col-offset-6 {
    margin-left: 25%
}

.ivu-col-order-6 {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6
}

.ivu-col-span-5 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 20.83333333%;
    flex: 0 0 20.83333333%;
    max-width: 20.83333333%
}

.ivu-col-push-5 {
    left: 20.83333333%
}

.ivu-col-pull-5 {
    right: 20.83333333%
}

.ivu-col-offset-5 {
    margin-left: 20.83333333%
}

.ivu-col-order-5 {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5
}

.ivu-col-span-4 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.66666667%;
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%
}

.ivu-col-push-4 {
    left: 16.66666667%
}

.ivu-col-pull-4 {
    right: 16.66666667%
}

.ivu-col-offset-4 {
    margin-left: 16.66666667%
}

.ivu-col-order-4 {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4
}

.ivu-col-span-3 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 12.5%;
    flex: 0 0 12.5%;
    max-width: 12.5%
}

.ivu-col-push-3 {
    left: 12.5%
}

.ivu-col-pull-3 {
    right: 12.5%
}

.ivu-col-offset-3 {
    margin-left: 12.5%
}

.ivu-col-order-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
}

.ivu-col-span-2 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.33333333%;
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%
}

.ivu-col-push-2 {
    left: 8.33333333%
}

.ivu-col-pull-2 {
    right: 8.33333333%
}

.ivu-col-offset-2 {
    margin-left: 8.33333333%
}

.ivu-col-order-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
}

.ivu-col-span-1 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 4.16666667%;
    flex: 0 0 4.16666667%;
    max-width: 4.16666667%
}

.ivu-col-push-1 {
    left: 4.16666667%
}

.ivu-col-pull-1 {
    right: 4.16666667%
}

.ivu-col-offset-1 {
    margin-left: 4.16666667%
}

.ivu-col-order-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
}

.ivu-col-span-0 {
    display: none
}

.ivu-col-push-0 {
    left: auto
}

.ivu-col-pull-0 {
    right: auto
}

.ivu-col-push-0 {
    left: auto
}

.ivu-col-pull-0 {
    right: auto
}

.ivu-col-offset-0 {
    margin-left: 0
}

.ivu-col-order-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
}

.ivu-col-span-xs-24 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
}

.ivu-col-xs-push-24 {
    left: 100%
}

.ivu-col-xs-pull-24 {
    right: 100%
}

.ivu-col-xs-offset-24 {
    margin-left: 100%
}

.ivu-col-xs-order-24 {
    -webkit-box-ordinal-group: 25;
    -ms-flex-order: 24;
    order: 24
}

.ivu-col-span-xs-23 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 95.83333333%;
    flex: 0 0 95.83333333%;
    max-width: 95.83333333%
}

.ivu-col-xs-push-23 {
    left: 95.83333333%
}

.ivu-col-xs-pull-23 {
    right: 95.83333333%
}

.ivu-col-xs-offset-23 {
    margin-left: 95.83333333%
}

.ivu-col-xs-order-23 {
    -webkit-box-ordinal-group: 24;
    -ms-flex-order: 23;
    order: 23
}

.ivu-col-span-xs-22 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.66666667%;
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%
}

.ivu-col-xs-push-22 {
    left: 91.66666667%
}

.ivu-col-xs-pull-22 {
    right: 91.66666667%
}

.ivu-col-xs-offset-22 {
    margin-left: 91.66666667%
}

.ivu-col-xs-order-22 {
    -webkit-box-ordinal-group: 23;
    -ms-flex-order: 22;
    order: 22
}

.ivu-col-span-xs-21 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 87.5%;
    flex: 0 0 87.5%;
    max-width: 87.5%
}

.ivu-col-xs-push-21 {
    left: 87.5%
}

.ivu-col-xs-pull-21 {
    right: 87.5%
}

.ivu-col-xs-offset-21 {
    margin-left: 87.5%
}

.ivu-col-xs-order-21 {
    -webkit-box-ordinal-group: 22;
    -ms-flex-order: 21;
    order: 21
}

.ivu-col-span-xs-20 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.33333333%;
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%
}

.ivu-col-xs-push-20 {
    left: 83.33333333%
}

.ivu-col-xs-pull-20 {
    right: 83.33333333%
}

.ivu-col-xs-offset-20 {
    margin-left: 83.33333333%
}

.ivu-col-xs-order-20 {
    -webkit-box-ordinal-group: 21;
    -ms-flex-order: 20;
    order: 20
}

.ivu-col-span-xs-19 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 79.16666667%;
    flex: 0 0 79.16666667%;
    max-width: 79.16666667%
}

.ivu-col-xs-push-19 {
    left: 79.16666667%
}

.ivu-col-xs-pull-19 {
    right: 79.16666667%
}

.ivu-col-xs-offset-19 {
    margin-left: 79.16666667%
}

.ivu-col-xs-order-19 {
    -webkit-box-ordinal-group: 20;
    -ms-flex-order: 19;
    order: 19
}

.ivu-col-span-xs-18 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
}

.ivu-col-xs-push-18 {
    left: 75%
}

.ivu-col-xs-pull-18 {
    right: 75%
}

.ivu-col-xs-offset-18 {
    margin-left: 75%
}

.ivu-col-xs-order-18 {
    -webkit-box-ordinal-group: 19;
    -ms-flex-order: 18;
    order: 18
}

.ivu-col-span-xs-17 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 70.83333333%;
    flex: 0 0 70.83333333%;
    max-width: 70.83333333%
}

.ivu-col-xs-push-17 {
    left: 70.83333333%
}

.ivu-col-xs-pull-17 {
    right: 70.83333333%
}

.ivu-col-xs-offset-17 {
    margin-left: 70.83333333%
}

.ivu-col-xs-order-17 {
    -webkit-box-ordinal-group: 18;
    -ms-flex-order: 17;
    order: 17
}

.ivu-col-span-xs-16 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.66666667%;
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%
}

.ivu-col-xs-push-16 {
    left: 66.66666667%
}

.ivu-col-xs-pull-16 {
    right: 66.66666667%
}

.ivu-col-xs-offset-16 {
    margin-left: 66.66666667%
}

.ivu-col-xs-order-16 {
    -webkit-box-ordinal-group: 17;
    -ms-flex-order: 16;
    order: 16
}

.ivu-col-span-xs-15 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 62.5%;
    flex: 0 0 62.5%;
    max-width: 62.5%
}

.ivu-col-xs-push-15 {
    left: 62.5%
}

.ivu-col-xs-pull-15 {
    right: 62.5%
}

.ivu-col-xs-offset-15 {
    margin-left: 62.5%
}

.ivu-col-xs-order-15 {
    -webkit-box-ordinal-group: 16;
    -ms-flex-order: 15;
    order: 15
}

.ivu-col-span-xs-14 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.33333333%;
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%
}

.ivu-col-xs-push-14 {
    left: 58.33333333%
}

.ivu-col-xs-pull-14 {
    right: 58.33333333%
}

.ivu-col-xs-offset-14 {
    margin-left: 58.33333333%
}

.ivu-col-xs-order-14 {
    -webkit-box-ordinal-group: 15;
    -ms-flex-order: 14;
    order: 14
}

.ivu-col-span-xs-13 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 54.16666667%;
    flex: 0 0 54.16666667%;
    max-width: 54.16666667%
}

.ivu-col-xs-push-13 {
    left: 54.16666667%
}

.ivu-col-xs-pull-13 {
    right: 54.16666667%
}

.ivu-col-xs-offset-13 {
    margin-left: 54.16666667%
}

.ivu-col-xs-order-13 {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13
}

.ivu-col-span-xs-12 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
}

.ivu-col-xs-push-12 {
    left: 50%
}

.ivu-col-xs-pull-12 {
    right: 50%
}

.ivu-col-xs-offset-12 {
    margin-left: 50%
}

.ivu-col-xs-order-12 {
    -webkit-box-ordinal-group: 13;
    -ms-flex-order: 12;
    order: 12
}

.ivu-col-span-xs-11 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 45.83333333%;
    flex: 0 0 45.83333333%;
    max-width: 45.83333333%
}

.ivu-col-xs-push-11 {
    left: 45.83333333%
}

.ivu-col-xs-pull-11 {
    right: 45.83333333%
}

.ivu-col-xs-offset-11 {
    margin-left: 45.83333333%
}

.ivu-col-xs-order-11 {
    -webkit-box-ordinal-group: 12;
    -ms-flex-order: 11;
    order: 11
}

.ivu-col-span-xs-10 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.66666667%;
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%
}

.ivu-col-xs-push-10 {
    left: 41.66666667%
}

.ivu-col-xs-pull-10 {
    right: 41.66666667%
}

.ivu-col-xs-offset-10 {
    margin-left: 41.66666667%
}

.ivu-col-xs-order-10 {
    -webkit-box-ordinal-group: 11;
    -ms-flex-order: 10;
    order: 10
}

.ivu-col-span-xs-9 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 37.5%;
    flex: 0 0 37.5%;
    max-width: 37.5%
}

.ivu-col-xs-push-9 {
    left: 37.5%
}

.ivu-col-xs-pull-9 {
    right: 37.5%
}

.ivu-col-xs-offset-9 {
    margin-left: 37.5%
}

.ivu-col-xs-order-9 {
    -webkit-box-ordinal-group: 10;
    -ms-flex-order: 9;
    order: 9
}

.ivu-col-span-xs-8 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333333%;
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%
}

.ivu-col-xs-push-8 {
    left: 33.33333333%
}

.ivu-col-xs-pull-8 {
    right: 33.33333333%
}

.ivu-col-xs-offset-8 {
    margin-left: 33.33333333%
}

.ivu-col-xs-order-8 {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8
}

.ivu-col-span-xs-7 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 29.16666667%;
    flex: 0 0 29.16666667%;
    max-width: 29.16666667%
}

.ivu-col-xs-push-7 {
    left: 29.16666667%
}

.ivu-col-xs-pull-7 {
    right: 29.16666667%
}

.ivu-col-xs-offset-7 {
    margin-left: 29.16666667%
}

.ivu-col-xs-order-7 {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7
}

.ivu-col-span-xs-6 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
}

.ivu-col-xs-push-6 {
    left: 25%
}

.ivu-col-xs-pull-6 {
    right: 25%
}

.ivu-col-xs-offset-6 {
    margin-left: 25%
}

.ivu-col-xs-order-6 {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6
}

.ivu-col-span-xs-5 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 20.83333333%;
    flex: 0 0 20.83333333%;
    max-width: 20.83333333%
}

.ivu-col-xs-push-5 {
    left: 20.83333333%
}

.ivu-col-xs-pull-5 {
    right: 20.83333333%
}

.ivu-col-xs-offset-5 {
    margin-left: 20.83333333%
}

.ivu-col-xs-order-5 {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5
}

.ivu-col-span-xs-4 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.66666667%;
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%
}

.ivu-col-xs-push-4 {
    left: 16.66666667%
}

.ivu-col-xs-pull-4 {
    right: 16.66666667%
}

.ivu-col-xs-offset-4 {
    margin-left: 16.66666667%
}

.ivu-col-xs-order-4 {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4
}

.ivu-col-span-xs-3 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 12.5%;
    flex: 0 0 12.5%;
    max-width: 12.5%
}

.ivu-col-xs-push-3 {
    left: 12.5%
}

.ivu-col-xs-pull-3 {
    right: 12.5%
}

.ivu-col-xs-offset-3 {
    margin-left: 12.5%
}

.ivu-col-xs-order-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
}

.ivu-col-span-xs-2 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.33333333%;
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%
}

.ivu-col-xs-push-2 {
    left: 8.33333333%
}

.ivu-col-xs-pull-2 {
    right: 8.33333333%
}

.ivu-col-xs-offset-2 {
    margin-left: 8.33333333%
}

.ivu-col-xs-order-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
}

.ivu-col-span-xs-1 {
    display: block;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 4.16666667%;
    flex: 0 0 4.16666667%;
    max-width: 4.16666667%
}

.ivu-col-xs-push-1 {
    left: 4.16666667%
}

.ivu-col-xs-pull-1 {
    right: 4.16666667%
}

.ivu-col-xs-offset-1 {
    margin-left: 4.16666667%
}

.ivu-col-xs-order-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
}

.ivu-col-span-xs-0 {
    display: none
}

.ivu-col-push-0 {
    left: auto
}

.ivu-col-pull-0 {
    right: auto
}

.ivu-col-xs-push-0 {
    left: auto
}

.ivu-col-xs-pull-0 {
    right: auto
}

.ivu-col-xs-offset-0 {
    margin-left: 0
}

.ivu-col-xs-order-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
}

@media (min-width:576px) {
    .ivu-col-span-sm-24 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .ivu-col-sm-push-24 {
        left: 100%
    }

    .ivu-col-sm-pull-24 {
        right: 100%
    }

    .ivu-col-sm-offset-24 {
        margin-left: 100%
    }

    .ivu-col-sm-order-24 {
        -webkit-box-ordinal-group: 25;
        -ms-flex-order: 24;
        order: 24
    }

    .ivu-col-span-sm-23 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 95.83333333%;
        flex: 0 0 95.83333333%;
        max-width: 95.83333333%
    }

    .ivu-col-sm-push-23 {
        left: 95.83333333%
    }

    .ivu-col-sm-pull-23 {
        right: 95.83333333%
    }

    .ivu-col-sm-offset-23 {
        margin-left: 95.83333333%
    }

    .ivu-col-sm-order-23 {
        -webkit-box-ordinal-group: 24;
        -ms-flex-order: 23;
        order: 23
    }

    .ivu-col-span-sm-22 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66666667%;
        flex: 0 0 91.66666667%;
        max-width: 91.66666667%
    }

    .ivu-col-sm-push-22 {
        left: 91.66666667%
    }

    .ivu-col-sm-pull-22 {
        right: 91.66666667%
    }

    .ivu-col-sm-offset-22 {
        margin-left: 91.66666667%
    }

    .ivu-col-sm-order-22 {
        -webkit-box-ordinal-group: 23;
        -ms-flex-order: 22;
        order: 22
    }

    .ivu-col-span-sm-21 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 87.5%;
        flex: 0 0 87.5%;
        max-width: 87.5%
    }

    .ivu-col-sm-push-21 {
        left: 87.5%
    }

    .ivu-col-sm-pull-21 {
        right: 87.5%
    }

    .ivu-col-sm-offset-21 {
        margin-left: 87.5%
    }

    .ivu-col-sm-order-21 {
        -webkit-box-ordinal-group: 22;
        -ms-flex-order: 21;
        order: 21
    }

    .ivu-col-span-sm-20 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333333%;
        flex: 0 0 83.33333333%;
        max-width: 83.33333333%
    }

    .ivu-col-sm-push-20 {
        left: 83.33333333%
    }

    .ivu-col-sm-pull-20 {
        right: 83.33333333%
    }

    .ivu-col-sm-offset-20 {
        margin-left: 83.33333333%
    }

    .ivu-col-sm-order-20 {
        -webkit-box-ordinal-group: 21;
        -ms-flex-order: 20;
        order: 20
    }

    .ivu-col-span-sm-19 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 79.16666667%;
        flex: 0 0 79.16666667%;
        max-width: 79.16666667%
    }

    .ivu-col-sm-push-19 {
        left: 79.16666667%
    }

    .ivu-col-sm-pull-19 {
        right: 79.16666667%
    }

    .ivu-col-sm-offset-19 {
        margin-left: 79.16666667%
    }

    .ivu-col-sm-order-19 {
        -webkit-box-ordinal-group: 20;
        -ms-flex-order: 19;
        order: 19
    }

    .ivu-col-span-sm-18 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .ivu-col-sm-push-18 {
        left: 75%
    }

    .ivu-col-sm-pull-18 {
        right: 75%
    }

    .ivu-col-sm-offset-18 {
        margin-left: 75%
    }

    .ivu-col-sm-order-18 {
        -webkit-box-ordinal-group: 19;
        -ms-flex-order: 18;
        order: 18
    }

    .ivu-col-span-sm-17 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 70.83333333%;
        flex: 0 0 70.83333333%;
        max-width: 70.83333333%
    }

    .ivu-col-sm-push-17 {
        left: 70.83333333%
    }

    .ivu-col-sm-pull-17 {
        right: 70.83333333%
    }

    .ivu-col-sm-offset-17 {
        margin-left: 70.83333333%
    }

    .ivu-col-sm-order-17 {
        -webkit-box-ordinal-group: 18;
        -ms-flex-order: 17;
        order: 17
    }

    .ivu-col-span-sm-16 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66666667%;
        flex: 0 0 66.66666667%;
        max-width: 66.66666667%
    }

    .ivu-col-sm-push-16 {
        left: 66.66666667%
    }

    .ivu-col-sm-pull-16 {
        right: 66.66666667%
    }

    .ivu-col-sm-offset-16 {
        margin-left: 66.66666667%
    }

    .ivu-col-sm-order-16 {
        -webkit-box-ordinal-group: 17;
        -ms-flex-order: 16;
        order: 16
    }

    .ivu-col-span-sm-15 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 62.5%;
        flex: 0 0 62.5%;
        max-width: 62.5%
    }

    .ivu-col-sm-push-15 {
        left: 62.5%
    }

    .ivu-col-sm-pull-15 {
        right: 62.5%
    }

    .ivu-col-sm-offset-15 {
        margin-left: 62.5%
    }

    .ivu-col-sm-order-15 {
        -webkit-box-ordinal-group: 16;
        -ms-flex-order: 15;
        order: 15
    }

    .ivu-col-span-sm-14 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333333%;
        flex: 0 0 58.33333333%;
        max-width: 58.33333333%
    }

    .ivu-col-sm-push-14 {
        left: 58.33333333%
    }

    .ivu-col-sm-pull-14 {
        right: 58.33333333%
    }

    .ivu-col-sm-offset-14 {
        margin-left: 58.33333333%
    }

    .ivu-col-sm-order-14 {
        -webkit-box-ordinal-group: 15;
        -ms-flex-order: 14;
        order: 14
    }

    .ivu-col-span-sm-13 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 54.16666667%;
        flex: 0 0 54.16666667%;
        max-width: 54.16666667%
    }

    .ivu-col-sm-push-13 {
        left: 54.16666667%
    }

    .ivu-col-sm-pull-13 {
        right: 54.16666667%
    }

    .ivu-col-sm-offset-13 {
        margin-left: 54.16666667%
    }

    .ivu-col-sm-order-13 {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13
    }

    .ivu-col-span-sm-12 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .ivu-col-sm-push-12 {
        left: 50%
    }

    .ivu-col-sm-pull-12 {
        right: 50%
    }

    .ivu-col-sm-offset-12 {
        margin-left: 50%
    }

    .ivu-col-sm-order-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12
    }

    .ivu-col-span-sm-11 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 45.83333333%;
        flex: 0 0 45.83333333%;
        max-width: 45.83333333%
    }

    .ivu-col-sm-push-11 {
        left: 45.83333333%
    }

    .ivu-col-sm-pull-11 {
        right: 45.83333333%
    }

    .ivu-col-sm-offset-11 {
        margin-left: 45.83333333%
    }

    .ivu-col-sm-order-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11
    }

    .ivu-col-span-sm-10 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66666667%;
        flex: 0 0 41.66666667%;
        max-width: 41.66666667%
    }

    .ivu-col-sm-push-10 {
        left: 41.66666667%
    }

    .ivu-col-sm-pull-10 {
        right: 41.66666667%
    }

    .ivu-col-sm-offset-10 {
        margin-left: 41.66666667%
    }

    .ivu-col-sm-order-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10
    }

    .ivu-col-span-sm-9 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 37.5%;
        flex: 0 0 37.5%;
        max-width: 37.5%
    }

    .ivu-col-sm-push-9 {
        left: 37.5%
    }

    .ivu-col-sm-pull-9 {
        right: 37.5%
    }

    .ivu-col-sm-offset-9 {
        margin-left: 37.5%
    }

    .ivu-col-sm-order-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9
    }

    .ivu-col-span-sm-8 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333333%;
        flex: 0 0 33.33333333%;
        max-width: 33.33333333%
    }

    .ivu-col-sm-push-8 {
        left: 33.33333333%
    }

    .ivu-col-sm-pull-8 {
        right: 33.33333333%
    }

    .ivu-col-sm-offset-8 {
        margin-left: 33.33333333%
    }

    .ivu-col-sm-order-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8
    }

    .ivu-col-span-sm-7 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 29.16666667%;
        flex: 0 0 29.16666667%;
        max-width: 29.16666667%
    }

    .ivu-col-sm-push-7 {
        left: 29.16666667%
    }

    .ivu-col-sm-pull-7 {
        right: 29.16666667%
    }

    .ivu-col-sm-offset-7 {
        margin-left: 29.16666667%
    }

    .ivu-col-sm-order-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7
    }

    .ivu-col-span-sm-6 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .ivu-col-sm-push-6 {
        left: 25%
    }

    .ivu-col-sm-pull-6 {
        right: 25%
    }

    .ivu-col-sm-offset-6 {
        margin-left: 25%
    }

    .ivu-col-sm-order-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6
    }

    .ivu-col-span-sm-5 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 20.83333333%;
        flex: 0 0 20.83333333%;
        max-width: 20.83333333%
    }

    .ivu-col-sm-push-5 {
        left: 20.83333333%
    }

    .ivu-col-sm-pull-5 {
        right: 20.83333333%
    }

    .ivu-col-sm-offset-5 {
        margin-left: 20.83333333%
    }

    .ivu-col-sm-order-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5
    }

    .ivu-col-span-sm-4 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66666667%;
        flex: 0 0 16.66666667%;
        max-width: 16.66666667%
    }

    .ivu-col-sm-push-4 {
        left: 16.66666667%
    }

    .ivu-col-sm-pull-4 {
        right: 16.66666667%
    }

    .ivu-col-sm-offset-4 {
        margin-left: 16.66666667%
    }

    .ivu-col-sm-order-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4
    }

    .ivu-col-span-sm-3 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 12.5%;
        flex: 0 0 12.5%;
        max-width: 12.5%
    }

    .ivu-col-sm-push-3 {
        left: 12.5%
    }

    .ivu-col-sm-pull-3 {
        right: 12.5%
    }

    .ivu-col-sm-offset-3 {
        margin-left: 12.5%
    }

    .ivu-col-sm-order-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }

    .ivu-col-span-sm-2 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333333%;
        flex: 0 0 8.33333333%;
        max-width: 8.33333333%
    }

    .ivu-col-sm-push-2 {
        left: 8.33333333%
    }

    .ivu-col-sm-pull-2 {
        right: 8.33333333%
    }

    .ivu-col-sm-offset-2 {
        margin-left: 8.33333333%
    }

    .ivu-col-sm-order-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }

    .ivu-col-span-sm-1 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 4.16666667%;
        flex: 0 0 4.16666667%;
        max-width: 4.16666667%
    }

    .ivu-col-sm-push-1 {
        left: 4.16666667%
    }

    .ivu-col-sm-pull-1 {
        right: 4.16666667%
    }

    .ivu-col-sm-offset-1 {
        margin-left: 4.16666667%
    }

    .ivu-col-sm-order-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }

    .ivu-col-span-sm-0 {
        display: none
    }

    .ivu-col-push-0 {
        left: auto
    }

    .ivu-col-pull-0 {
        right: auto
    }

    .ivu-col-sm-push-0 {
        left: auto
    }

    .ivu-col-sm-pull-0 {
        right: auto
    }

    .ivu-col-sm-offset-0 {
        margin-left: 0
    }

    .ivu-col-sm-order-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
}

@media (min-width:768px) {
    .ivu-col-span-md-24 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .ivu-col-md-push-24 {
        left: 100%
    }

    .ivu-col-md-pull-24 {
        right: 100%
    }

    .ivu-col-md-offset-24 {
        margin-left: 100%
    }

    .ivu-col-md-order-24 {
        -webkit-box-ordinal-group: 25;
        -ms-flex-order: 24;
        order: 24
    }

    .ivu-col-span-md-23 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 95.83333333%;
        flex: 0 0 95.83333333%;
        max-width: 95.83333333%
    }

    .ivu-col-md-push-23 {
        left: 95.83333333%
    }

    .ivu-col-md-pull-23 {
        right: 95.83333333%
    }

    .ivu-col-md-offset-23 {
        margin-left: 95.83333333%
    }

    .ivu-col-md-order-23 {
        -webkit-box-ordinal-group: 24;
        -ms-flex-order: 23;
        order: 23
    }

    .ivu-col-span-md-22 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66666667%;
        flex: 0 0 91.66666667%;
        max-width: 91.66666667%
    }

    .ivu-col-md-push-22 {
        left: 91.66666667%
    }

    .ivu-col-md-pull-22 {
        right: 91.66666667%
    }

    .ivu-col-md-offset-22 {
        margin-left: 91.66666667%
    }

    .ivu-col-md-order-22 {
        -webkit-box-ordinal-group: 23;
        -ms-flex-order: 22;
        order: 22
    }

    .ivu-col-span-md-21 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 87.5%;
        flex: 0 0 87.5%;
        max-width: 87.5%
    }

    .ivu-col-md-push-21 {
        left: 87.5%
    }

    .ivu-col-md-pull-21 {
        right: 87.5%
    }

    .ivu-col-md-offset-21 {
        margin-left: 87.5%
    }

    .ivu-col-md-order-21 {
        -webkit-box-ordinal-group: 22;
        -ms-flex-order: 21;
        order: 21
    }

    .ivu-col-span-md-20 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333333%;
        flex: 0 0 83.33333333%;
        max-width: 83.33333333%
    }

    .ivu-col-md-push-20 {
        left: 83.33333333%
    }

    .ivu-col-md-pull-20 {
        right: 83.33333333%
    }

    .ivu-col-md-offset-20 {
        margin-left: 83.33333333%
    }

    .ivu-col-md-order-20 {
        -webkit-box-ordinal-group: 21;
        -ms-flex-order: 20;
        order: 20
    }

    .ivu-col-span-md-19 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 79.16666667%;
        flex: 0 0 79.16666667%;
        max-width: 79.16666667%
    }

    .ivu-col-md-push-19 {
        left: 79.16666667%
    }

    .ivu-col-md-pull-19 {
        right: 79.16666667%
    }

    .ivu-col-md-offset-19 {
        margin-left: 79.16666667%
    }

    .ivu-col-md-order-19 {
        -webkit-box-ordinal-group: 20;
        -ms-flex-order: 19;
        order: 19
    }

    .ivu-col-span-md-18 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .ivu-col-md-push-18 {
        left: 75%
    }

    .ivu-col-md-pull-18 {
        right: 75%
    }

    .ivu-col-md-offset-18 {
        margin-left: 75%
    }

    .ivu-col-md-order-18 {
        -webkit-box-ordinal-group: 19;
        -ms-flex-order: 18;
        order: 18
    }

    .ivu-col-span-md-17 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 70.83333333%;
        flex: 0 0 70.83333333%;
        max-width: 70.83333333%
    }

    .ivu-col-md-push-17 {
        left: 70.83333333%
    }

    .ivu-col-md-pull-17 {
        right: 70.83333333%
    }

    .ivu-col-md-offset-17 {
        margin-left: 70.83333333%
    }

    .ivu-col-md-order-17 {
        -webkit-box-ordinal-group: 18;
        -ms-flex-order: 17;
        order: 17
    }

    .ivu-col-span-md-16 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66666667%;
        flex: 0 0 66.66666667%;
        max-width: 66.66666667%
    }

    .ivu-col-md-push-16 {
        left: 66.66666667%
    }

    .ivu-col-md-pull-16 {
        right: 66.66666667%
    }

    .ivu-col-md-offset-16 {
        margin-left: 66.66666667%
    }

    .ivu-col-md-order-16 {
        -webkit-box-ordinal-group: 17;
        -ms-flex-order: 16;
        order: 16
    }

    .ivu-col-span-md-15 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 62.5%;
        flex: 0 0 62.5%;
        max-width: 62.5%
    }

    .ivu-col-md-push-15 {
        left: 62.5%
    }

    .ivu-col-md-pull-15 {
        right: 62.5%
    }

    .ivu-col-md-offset-15 {
        margin-left: 62.5%
    }

    .ivu-col-md-order-15 {
        -webkit-box-ordinal-group: 16;
        -ms-flex-order: 15;
        order: 15
    }

    .ivu-col-span-md-14 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333333%;
        flex: 0 0 58.33333333%;
        max-width: 58.33333333%
    }

    .ivu-col-md-push-14 {
        left: 58.33333333%
    }

    .ivu-col-md-pull-14 {
        right: 58.33333333%
    }

    .ivu-col-md-offset-14 {
        margin-left: 58.33333333%
    }

    .ivu-col-md-order-14 {
        -webkit-box-ordinal-group: 15;
        -ms-flex-order: 14;
        order: 14
    }

    .ivu-col-span-md-13 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 54.16666667%;
        flex: 0 0 54.16666667%;
        max-width: 54.16666667%
    }

    .ivu-col-md-push-13 {
        left: 54.16666667%
    }

    .ivu-col-md-pull-13 {
        right: 54.16666667%
    }

    .ivu-col-md-offset-13 {
        margin-left: 54.16666667%
    }

    .ivu-col-md-order-13 {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13
    }

    .ivu-col-span-md-12 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .ivu-col-md-push-12 {
        left: 50%
    }

    .ivu-col-md-pull-12 {
        right: 50%
    }

    .ivu-col-md-offset-12 {
        margin-left: 50%
    }

    .ivu-col-md-order-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12
    }

    .ivu-col-span-md-11 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 45.83333333%;
        flex: 0 0 45.83333333%;
        max-width: 45.83333333%
    }

    .ivu-col-md-push-11 {
        left: 45.83333333%
    }

    .ivu-col-md-pull-11 {
        right: 45.83333333%
    }

    .ivu-col-md-offset-11 {
        margin-left: 45.83333333%
    }

    .ivu-col-md-order-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11
    }

    .ivu-col-span-md-10 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66666667%;
        flex: 0 0 41.66666667%;
        max-width: 41.66666667%
    }

    .ivu-col-md-push-10 {
        left: 41.66666667%
    }

    .ivu-col-md-pull-10 {
        right: 41.66666667%
    }

    .ivu-col-md-offset-10 {
        margin-left: 41.66666667%
    }

    .ivu-col-md-order-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10
    }

    .ivu-col-span-md-9 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 37.5%;
        flex: 0 0 37.5%;
        max-width: 37.5%
    }

    .ivu-col-md-push-9 {
        left: 37.5%
    }

    .ivu-col-md-pull-9 {
        right: 37.5%
    }

    .ivu-col-md-offset-9 {
        margin-left: 37.5%
    }

    .ivu-col-md-order-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9
    }

    .ivu-col-span-md-8 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333333%;
        flex: 0 0 33.33333333%;
        max-width: 33.33333333%
    }

    .ivu-col-md-push-8 {
        left: 33.33333333%
    }

    .ivu-col-md-pull-8 {
        right: 33.33333333%
    }

    .ivu-col-md-offset-8 {
        margin-left: 33.33333333%
    }

    .ivu-col-md-order-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8
    }

    .ivu-col-span-md-7 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 29.16666667%;
        flex: 0 0 29.16666667%;
        max-width: 29.16666667%
    }

    .ivu-col-md-push-7 {
        left: 29.16666667%
    }

    .ivu-col-md-pull-7 {
        right: 29.16666667%
    }

    .ivu-col-md-offset-7 {
        margin-left: 29.16666667%
    }

    .ivu-col-md-order-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7
    }

    .ivu-col-span-md-6 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .ivu-col-md-push-6 {
        left: 25%
    }

    .ivu-col-md-pull-6 {
        right: 25%
    }

    .ivu-col-md-offset-6 {
        margin-left: 25%
    }

    .ivu-col-md-order-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6
    }

    .ivu-col-span-md-5 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 20.83333333%;
        flex: 0 0 20.83333333%;
        max-width: 20.83333333%
    }

    .ivu-col-md-push-5 {
        left: 20.83333333%
    }

    .ivu-col-md-pull-5 {
        right: 20.83333333%
    }

    .ivu-col-md-offset-5 {
        margin-left: 20.83333333%
    }

    .ivu-col-md-order-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5
    }

    .ivu-col-span-md-4 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66666667%;
        flex: 0 0 16.66666667%;
        max-width: 16.66666667%
    }

    .ivu-col-md-push-4 {
        left: 16.66666667%
    }

    .ivu-col-md-pull-4 {
        right: 16.66666667%
    }

    .ivu-col-md-offset-4 {
        margin-left: 16.66666667%
    }

    .ivu-col-md-order-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4
    }

    .ivu-col-span-md-3 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 12.5%;
        flex: 0 0 12.5%;
        max-width: 12.5%
    }

    .ivu-col-md-push-3 {
        left: 12.5%
    }

    .ivu-col-md-pull-3 {
        right: 12.5%
    }

    .ivu-col-md-offset-3 {
        margin-left: 12.5%
    }

    .ivu-col-md-order-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }

    .ivu-col-span-md-2 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333333%;
        flex: 0 0 8.33333333%;
        max-width: 8.33333333%
    }

    .ivu-col-md-push-2 {
        left: 8.33333333%
    }

    .ivu-col-md-pull-2 {
        right: 8.33333333%
    }

    .ivu-col-md-offset-2 {
        margin-left: 8.33333333%
    }

    .ivu-col-md-order-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }

    .ivu-col-span-md-1 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 4.16666667%;
        flex: 0 0 4.16666667%;
        max-width: 4.16666667%
    }

    .ivu-col-md-push-1 {
        left: 4.16666667%
    }

    .ivu-col-md-pull-1 {
        right: 4.16666667%
    }

    .ivu-col-md-offset-1 {
        margin-left: 4.16666667%
    }

    .ivu-col-md-order-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }

    .ivu-col-span-md-0 {
        display: none
    }

    .ivu-col-push-0 {
        left: auto
    }

    .ivu-col-pull-0 {
        right: auto
    }

    .ivu-col-md-push-0 {
        left: auto
    }

    .ivu-col-md-pull-0 {
        right: auto
    }

    .ivu-col-md-offset-0 {
        margin-left: 0
    }

    .ivu-col-md-order-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
}

@media (min-width:992px) {
    .ivu-col-span-lg-24 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .ivu-col-lg-push-24 {
        left: 100%
    }

    .ivu-col-lg-pull-24 {
        right: 100%
    }

    .ivu-col-lg-offset-24 {
        margin-left: 100%
    }

    .ivu-col-lg-order-24 {
        -webkit-box-ordinal-group: 25;
        -ms-flex-order: 24;
        order: 24
    }

    .ivu-col-span-lg-23 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 95.83333333%;
        flex: 0 0 95.83333333%;
        max-width: 95.83333333%
    }

    .ivu-col-lg-push-23 {
        left: 95.83333333%
    }

    .ivu-col-lg-pull-23 {
        right: 95.83333333%
    }

    .ivu-col-lg-offset-23 {
        margin-left: 95.83333333%
    }

    .ivu-col-lg-order-23 {
        -webkit-box-ordinal-group: 24;
        -ms-flex-order: 23;
        order: 23
    }

    .ivu-col-span-lg-22 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66666667%;
        flex: 0 0 91.66666667%;
        max-width: 91.66666667%
    }

    .ivu-col-lg-push-22 {
        left: 91.66666667%
    }

    .ivu-col-lg-pull-22 {
        right: 91.66666667%
    }

    .ivu-col-lg-offset-22 {
        margin-left: 91.66666667%
    }

    .ivu-col-lg-order-22 {
        -webkit-box-ordinal-group: 23;
        -ms-flex-order: 22;
        order: 22
    }

    .ivu-col-span-lg-21 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 87.5%;
        flex: 0 0 87.5%;
        max-width: 87.5%
    }

    .ivu-col-lg-push-21 {
        left: 87.5%
    }

    .ivu-col-lg-pull-21 {
        right: 87.5%
    }

    .ivu-col-lg-offset-21 {
        margin-left: 87.5%
    }

    .ivu-col-lg-order-21 {
        -webkit-box-ordinal-group: 22;
        -ms-flex-order: 21;
        order: 21
    }

    .ivu-col-span-lg-20 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333333%;
        flex: 0 0 83.33333333%;
        max-width: 83.33333333%
    }

    .ivu-col-lg-push-20 {
        left: 83.33333333%
    }

    .ivu-col-lg-pull-20 {
        right: 83.33333333%
    }

    .ivu-col-lg-offset-20 {
        margin-left: 83.33333333%
    }

    .ivu-col-lg-order-20 {
        -webkit-box-ordinal-group: 21;
        -ms-flex-order: 20;
        order: 20
    }

    .ivu-col-span-lg-19 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 79.16666667%;
        flex: 0 0 79.16666667%;
        max-width: 79.16666667%
    }

    .ivu-col-lg-push-19 {
        left: 79.16666667%
    }

    .ivu-col-lg-pull-19 {
        right: 79.16666667%
    }

    .ivu-col-lg-offset-19 {
        margin-left: 79.16666667%
    }

    .ivu-col-lg-order-19 {
        -webkit-box-ordinal-group: 20;
        -ms-flex-order: 19;
        order: 19
    }

    .ivu-col-span-lg-18 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .ivu-col-lg-push-18 {
        left: 75%
    }

    .ivu-col-lg-pull-18 {
        right: 75%
    }

    .ivu-col-lg-offset-18 {
        margin-left: 75%
    }

    .ivu-col-lg-order-18 {
        -webkit-box-ordinal-group: 19;
        -ms-flex-order: 18;
        order: 18
    }

    .ivu-col-span-lg-17 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 70.83333333%;
        flex: 0 0 70.83333333%;
        max-width: 70.83333333%
    }

    .ivu-col-lg-push-17 {
        left: 70.83333333%
    }

    .ivu-col-lg-pull-17 {
        right: 70.83333333%
    }

    .ivu-col-lg-offset-17 {
        margin-left: 70.83333333%
    }

    .ivu-col-lg-order-17 {
        -webkit-box-ordinal-group: 18;
        -ms-flex-order: 17;
        order: 17
    }

    .ivu-col-span-lg-16 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66666667%;
        flex: 0 0 66.66666667%;
        max-width: 66.66666667%
    }

    .ivu-col-lg-push-16 {
        left: 66.66666667%
    }

    .ivu-col-lg-pull-16 {
        right: 66.66666667%
    }

    .ivu-col-lg-offset-16 {
        margin-left: 66.66666667%
    }

    .ivu-col-lg-order-16 {
        -webkit-box-ordinal-group: 17;
        -ms-flex-order: 16;
        order: 16
    }

    .ivu-col-span-lg-15 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 62.5%;
        flex: 0 0 62.5%;
        max-width: 62.5%
    }

    .ivu-col-lg-push-15 {
        left: 62.5%
    }

    .ivu-col-lg-pull-15 {
        right: 62.5%
    }

    .ivu-col-lg-offset-15 {
        margin-left: 62.5%
    }

    .ivu-col-lg-order-15 {
        -webkit-box-ordinal-group: 16;
        -ms-flex-order: 15;
        order: 15
    }

    .ivu-col-span-lg-14 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333333%;
        flex: 0 0 58.33333333%;
        max-width: 58.33333333%
    }

    .ivu-col-lg-push-14 {
        left: 58.33333333%
    }

    .ivu-col-lg-pull-14 {
        right: 58.33333333%
    }

    .ivu-col-lg-offset-14 {
        margin-left: 58.33333333%
    }

    .ivu-col-lg-order-14 {
        -webkit-box-ordinal-group: 15;
        -ms-flex-order: 14;
        order: 14
    }

    .ivu-col-span-lg-13 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 54.16666667%;
        flex: 0 0 54.16666667%;
        max-width: 54.16666667%
    }

    .ivu-col-lg-push-13 {
        left: 54.16666667%
    }

    .ivu-col-lg-pull-13 {
        right: 54.16666667%
    }

    .ivu-col-lg-offset-13 {
        margin-left: 54.16666667%
    }

    .ivu-col-lg-order-13 {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13
    }

    .ivu-col-span-lg-12 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .ivu-col-lg-push-12 {
        left: 50%
    }

    .ivu-col-lg-pull-12 {
        right: 50%
    }

    .ivu-col-lg-offset-12 {
        margin-left: 50%
    }

    .ivu-col-lg-order-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12
    }

    .ivu-col-span-lg-11 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 45.83333333%;
        flex: 0 0 45.83333333%;
        max-width: 45.83333333%
    }

    .ivu-col-lg-push-11 {
        left: 45.83333333%
    }

    .ivu-col-lg-pull-11 {
        right: 45.83333333%
    }

    .ivu-col-lg-offset-11 {
        margin-left: 45.83333333%
    }

    .ivu-col-lg-order-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11
    }

    .ivu-col-span-lg-10 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66666667%;
        flex: 0 0 41.66666667%;
        max-width: 41.66666667%
    }

    .ivu-col-lg-push-10 {
        left: 41.66666667%
    }

    .ivu-col-lg-pull-10 {
        right: 41.66666667%
    }

    .ivu-col-lg-offset-10 {
        margin-left: 41.66666667%
    }

    .ivu-col-lg-order-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10
    }

    .ivu-col-span-lg-9 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 37.5%;
        flex: 0 0 37.5%;
        max-width: 37.5%
    }

    .ivu-col-lg-push-9 {
        left: 37.5%
    }

    .ivu-col-lg-pull-9 {
        right: 37.5%
    }

    .ivu-col-lg-offset-9 {
        margin-left: 37.5%
    }

    .ivu-col-lg-order-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9
    }

    .ivu-col-span-lg-8 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333333%;
        flex: 0 0 33.33333333%;
        max-width: 33.33333333%
    }

    .ivu-col-lg-push-8 {
        left: 33.33333333%
    }

    .ivu-col-lg-pull-8 {
        right: 33.33333333%
    }

    .ivu-col-lg-offset-8 {
        margin-left: 33.33333333%
    }

    .ivu-col-lg-order-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8
    }

    .ivu-col-span-lg-7 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 29.16666667%;
        flex: 0 0 29.16666667%;
        max-width: 29.16666667%
    }

    .ivu-col-lg-push-7 {
        left: 29.16666667%
    }

    .ivu-col-lg-pull-7 {
        right: 29.16666667%
    }

    .ivu-col-lg-offset-7 {
        margin-left: 29.16666667%
    }

    .ivu-col-lg-order-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7
    }

    .ivu-col-span-lg-6 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .ivu-col-lg-push-6 {
        left: 25%
    }

    .ivu-col-lg-pull-6 {
        right: 25%
    }

    .ivu-col-lg-offset-6 {
        margin-left: 25%
    }

    .ivu-col-lg-order-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6
    }

    .ivu-col-span-lg-5 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 20.83333333%;
        flex: 0 0 20.83333333%;
        max-width: 20.83333333%
    }

    .ivu-col-lg-push-5 {
        left: 20.83333333%
    }

    .ivu-col-lg-pull-5 {
        right: 20.83333333%
    }

    .ivu-col-lg-offset-5 {
        margin-left: 20.83333333%
    }

    .ivu-col-lg-order-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5
    }

    .ivu-col-span-lg-4 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66666667%;
        flex: 0 0 16.66666667%;
        max-width: 16.66666667%
    }

    .ivu-col-lg-push-4 {
        left: 16.66666667%
    }

    .ivu-col-lg-pull-4 {
        right: 16.66666667%
    }

    .ivu-col-lg-offset-4 {
        margin-left: 16.66666667%
    }

    .ivu-col-lg-order-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4
    }

    .ivu-col-span-lg-3 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 12.5%;
        flex: 0 0 12.5%;
        max-width: 12.5%
    }

    .ivu-col-lg-push-3 {
        left: 12.5%
    }

    .ivu-col-lg-pull-3 {
        right: 12.5%
    }

    .ivu-col-lg-offset-3 {
        margin-left: 12.5%
    }

    .ivu-col-lg-order-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }

    .ivu-col-span-lg-2 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333333%;
        flex: 0 0 8.33333333%;
        max-width: 8.33333333%
    }

    .ivu-col-lg-push-2 {
        left: 8.33333333%
    }

    .ivu-col-lg-pull-2 {
        right: 8.33333333%
    }

    .ivu-col-lg-offset-2 {
        margin-left: 8.33333333%
    }

    .ivu-col-lg-order-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }

    .ivu-col-span-lg-1 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 4.16666667%;
        flex: 0 0 4.16666667%;
        max-width: 4.16666667%
    }

    .ivu-col-lg-push-1 {
        left: 4.16666667%
    }

    .ivu-col-lg-pull-1 {
        right: 4.16666667%
    }

    .ivu-col-lg-offset-1 {
        margin-left: 4.16666667%
    }

    .ivu-col-lg-order-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }

    .ivu-col-span-lg-0 {
        display: none
    }

    .ivu-col-push-0 {
        left: auto
    }

    .ivu-col-pull-0 {
        right: auto
    }

    .ivu-col-lg-push-0 {
        left: auto
    }

    .ivu-col-lg-pull-0 {
        right: auto
    }

    .ivu-col-lg-offset-0 {
        margin-left: 0
    }

    .ivu-col-lg-order-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
}

@media (min-width:1200px) {
    .ivu-col-span-xl-24 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .ivu-col-xl-push-24 {
        left: 100%
    }

    .ivu-col-xl-pull-24 {
        right: 100%
    }

    .ivu-col-xl-offset-24 {
        margin-left: 100%
    }

    .ivu-col-xl-order-24 {
        -webkit-box-ordinal-group: 25;
        -ms-flex-order: 24;
        order: 24
    }

    .ivu-col-span-xl-23 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 95.83333333%;
        flex: 0 0 95.83333333%;
        max-width: 95.83333333%
    }

    .ivu-col-xl-push-23 {
        left: 95.83333333%
    }

    .ivu-col-xl-pull-23 {
        right: 95.83333333%
    }

    .ivu-col-xl-offset-23 {
        margin-left: 95.83333333%
    }

    .ivu-col-xl-order-23 {
        -webkit-box-ordinal-group: 24;
        -ms-flex-order: 23;
        order: 23
    }

    .ivu-col-span-xl-22 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66666667%;
        flex: 0 0 91.66666667%;
        max-width: 91.66666667%
    }

    .ivu-col-xl-push-22 {
        left: 91.66666667%
    }

    .ivu-col-xl-pull-22 {
        right: 91.66666667%
    }

    .ivu-col-xl-offset-22 {
        margin-left: 91.66666667%
    }

    .ivu-col-xl-order-22 {
        -webkit-box-ordinal-group: 23;
        -ms-flex-order: 22;
        order: 22
    }

    .ivu-col-span-xl-21 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 87.5%;
        flex: 0 0 87.5%;
        max-width: 87.5%
    }

    .ivu-col-xl-push-21 {
        left: 87.5%
    }

    .ivu-col-xl-pull-21 {
        right: 87.5%
    }

    .ivu-col-xl-offset-21 {
        margin-left: 87.5%
    }

    .ivu-col-xl-order-21 {
        -webkit-box-ordinal-group: 22;
        -ms-flex-order: 21;
        order: 21
    }

    .ivu-col-span-xl-20 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333333%;
        flex: 0 0 83.33333333%;
        max-width: 83.33333333%
    }

    .ivu-col-xl-push-20 {
        left: 83.33333333%
    }

    .ivu-col-xl-pull-20 {
        right: 83.33333333%
    }

    .ivu-col-xl-offset-20 {
        margin-left: 83.33333333%
    }

    .ivu-col-xl-order-20 {
        -webkit-box-ordinal-group: 21;
        -ms-flex-order: 20;
        order: 20
    }

    .ivu-col-span-xl-19 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 79.16666667%;
        flex: 0 0 79.16666667%;
        max-width: 79.16666667%
    }

    .ivu-col-xl-push-19 {
        left: 79.16666667%
    }

    .ivu-col-xl-pull-19 {
        right: 79.16666667%
    }

    .ivu-col-xl-offset-19 {
        margin-left: 79.16666667%
    }

    .ivu-col-xl-order-19 {
        -webkit-box-ordinal-group: 20;
        -ms-flex-order: 19;
        order: 19
    }

    .ivu-col-span-xl-18 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .ivu-col-xl-push-18 {
        left: 75%
    }

    .ivu-col-xl-pull-18 {
        right: 75%
    }

    .ivu-col-xl-offset-18 {
        margin-left: 75%
    }

    .ivu-col-xl-order-18 {
        -webkit-box-ordinal-group: 19;
        -ms-flex-order: 18;
        order: 18
    }

    .ivu-col-span-xl-17 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 70.83333333%;
        flex: 0 0 70.83333333%;
        max-width: 70.83333333%
    }

    .ivu-col-xl-push-17 {
        left: 70.83333333%
    }

    .ivu-col-xl-pull-17 {
        right: 70.83333333%
    }

    .ivu-col-xl-offset-17 {
        margin-left: 70.83333333%
    }

    .ivu-col-xl-order-17 {
        -webkit-box-ordinal-group: 18;
        -ms-flex-order: 17;
        order: 17
    }

    .ivu-col-span-xl-16 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66666667%;
        flex: 0 0 66.66666667%;
        max-width: 66.66666667%
    }

    .ivu-col-xl-push-16 {
        left: 66.66666667%
    }

    .ivu-col-xl-pull-16 {
        right: 66.66666667%
    }

    .ivu-col-xl-offset-16 {
        margin-left: 66.66666667%
    }

    .ivu-col-xl-order-16 {
        -webkit-box-ordinal-group: 17;
        -ms-flex-order: 16;
        order: 16
    }

    .ivu-col-span-xl-15 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 62.5%;
        flex: 0 0 62.5%;
        max-width: 62.5%
    }

    .ivu-col-xl-push-15 {
        left: 62.5%
    }

    .ivu-col-xl-pull-15 {
        right: 62.5%
    }

    .ivu-col-xl-offset-15 {
        margin-left: 62.5%
    }

    .ivu-col-xl-order-15 {
        -webkit-box-ordinal-group: 16;
        -ms-flex-order: 15;
        order: 15
    }

    .ivu-col-span-xl-14 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333333%;
        flex: 0 0 58.33333333%;
        max-width: 58.33333333%
    }

    .ivu-col-xl-push-14 {
        left: 58.33333333%
    }

    .ivu-col-xl-pull-14 {
        right: 58.33333333%
    }

    .ivu-col-xl-offset-14 {
        margin-left: 58.33333333%
    }

    .ivu-col-xl-order-14 {
        -webkit-box-ordinal-group: 15;
        -ms-flex-order: 14;
        order: 14
    }

    .ivu-col-span-xl-13 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 54.16666667%;
        flex: 0 0 54.16666667%;
        max-width: 54.16666667%
    }

    .ivu-col-xl-push-13 {
        left: 54.16666667%
    }

    .ivu-col-xl-pull-13 {
        right: 54.16666667%
    }

    .ivu-col-xl-offset-13 {
        margin-left: 54.16666667%
    }

    .ivu-col-xl-order-13 {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13
    }

    .ivu-col-span-xl-12 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .ivu-col-xl-push-12 {
        left: 50%
    }

    .ivu-col-xl-pull-12 {
        right: 50%
    }

    .ivu-col-xl-offset-12 {
        margin-left: 50%
    }

    .ivu-col-xl-order-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12
    }

    .ivu-col-span-xl-11 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 45.83333333%;
        flex: 0 0 45.83333333%;
        max-width: 45.83333333%
    }

    .ivu-col-xl-push-11 {
        left: 45.83333333%
    }

    .ivu-col-xl-pull-11 {
        right: 45.83333333%
    }

    .ivu-col-xl-offset-11 {
        margin-left: 45.83333333%
    }

    .ivu-col-xl-order-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11
    }

    .ivu-col-span-xl-10 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66666667%;
        flex: 0 0 41.66666667%;
        max-width: 41.66666667%
    }

    .ivu-col-xl-push-10 {
        left: 41.66666667%
    }

    .ivu-col-xl-pull-10 {
        right: 41.66666667%
    }

    .ivu-col-xl-offset-10 {
        margin-left: 41.66666667%
    }

    .ivu-col-xl-order-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10
    }

    .ivu-col-span-xl-9 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 37.5%;
        flex: 0 0 37.5%;
        max-width: 37.5%
    }

    .ivu-col-xl-push-9 {
        left: 37.5%
    }

    .ivu-col-xl-pull-9 {
        right: 37.5%
    }

    .ivu-col-xl-offset-9 {
        margin-left: 37.5%
    }

    .ivu-col-xl-order-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9
    }

    .ivu-col-span-xl-8 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333333%;
        flex: 0 0 33.33333333%;
        max-width: 33.33333333%
    }

    .ivu-col-xl-push-8 {
        left: 33.33333333%
    }

    .ivu-col-xl-pull-8 {
        right: 33.33333333%
    }

    .ivu-col-xl-offset-8 {
        margin-left: 33.33333333%
    }

    .ivu-col-xl-order-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8
    }

    .ivu-col-span-xl-7 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 29.16666667%;
        flex: 0 0 29.16666667%;
        max-width: 29.16666667%
    }

    .ivu-col-xl-push-7 {
        left: 29.16666667%
    }

    .ivu-col-xl-pull-7 {
        right: 29.16666667%
    }

    .ivu-col-xl-offset-7 {
        margin-left: 29.16666667%
    }

    .ivu-col-xl-order-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7
    }

    .ivu-col-span-xl-6 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .ivu-col-xl-push-6 {
        left: 25%
    }

    .ivu-col-xl-pull-6 {
        right: 25%
    }

    .ivu-col-xl-offset-6 {
        margin-left: 25%
    }

    .ivu-col-xl-order-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6
    }

    .ivu-col-span-xl-5 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 20.83333333%;
        flex: 0 0 20.83333333%;
        max-width: 20.83333333%
    }

    .ivu-col-xl-push-5 {
        left: 20.83333333%
    }

    .ivu-col-xl-pull-5 {
        right: 20.83333333%
    }

    .ivu-col-xl-offset-5 {
        margin-left: 20.83333333%
    }

    .ivu-col-xl-order-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5
    }

    .ivu-col-span-xl-4 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66666667%;
        flex: 0 0 16.66666667%;
        max-width: 16.66666667%
    }

    .ivu-col-xl-push-4 {
        left: 16.66666667%
    }

    .ivu-col-xl-pull-4 {
        right: 16.66666667%
    }

    .ivu-col-xl-offset-4 {
        margin-left: 16.66666667%
    }

    .ivu-col-xl-order-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4
    }

    .ivu-col-span-xl-3 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 12.5%;
        flex: 0 0 12.5%;
        max-width: 12.5%
    }

    .ivu-col-xl-push-3 {
        left: 12.5%
    }

    .ivu-col-xl-pull-3 {
        right: 12.5%
    }

    .ivu-col-xl-offset-3 {
        margin-left: 12.5%
    }

    .ivu-col-xl-order-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }

    .ivu-col-span-xl-2 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333333%;
        flex: 0 0 8.33333333%;
        max-width: 8.33333333%
    }

    .ivu-col-xl-push-2 {
        left: 8.33333333%
    }

    .ivu-col-xl-pull-2 {
        right: 8.33333333%
    }

    .ivu-col-xl-offset-2 {
        margin-left: 8.33333333%
    }

    .ivu-col-xl-order-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }

    .ivu-col-span-xl-1 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 4.16666667%;
        flex: 0 0 4.16666667%;
        max-width: 4.16666667%
    }

    .ivu-col-xl-push-1 {
        left: 4.16666667%
    }

    .ivu-col-xl-pull-1 {
        right: 4.16666667%
    }

    .ivu-col-xl-offset-1 {
        margin-left: 4.16666667%
    }

    .ivu-col-xl-order-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }

    .ivu-col-span-xl-0 {
        display: none
    }

    .ivu-col-push-0 {
        left: auto
    }

    .ivu-col-pull-0 {
        right: auto
    }

    .ivu-col-xl-push-0 {
        left: auto
    }

    .ivu-col-xl-pull-0 {
        right: auto
    }

    .ivu-col-xl-offset-0 {
        margin-left: 0
    }

    .ivu-col-xl-order-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
}

@media (min-width:1600px) {
    .ivu-col-span-xxl-24 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .ivu-col-xxl-push-24 {
        left: 100%
    }

    .ivu-col-xxl-pull-24 {
        right: 100%
    }

    .ivu-col-xxl-offset-24 {
        margin-left: 100%
    }

    .ivu-col-xxl-order-24 {
        -webkit-box-ordinal-group: 25;
        -ms-flex-order: 24;
        order: 24
    }

    .ivu-col-span-xxl-23 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 95.83333333%;
        flex: 0 0 95.83333333%;
        max-width: 95.83333333%
    }

    .ivu-col-xxl-push-23 {
        left: 95.83333333%
    }

    .ivu-col-xxl-pull-23 {
        right: 95.83333333%
    }

    .ivu-col-xxl-offset-23 {
        margin-left: 95.83333333%
    }

    .ivu-col-xxl-order-23 {
        -webkit-box-ordinal-group: 24;
        -ms-flex-order: 23;
        order: 23
    }

    .ivu-col-span-xxl-22 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66666667%;
        flex: 0 0 91.66666667%;
        max-width: 91.66666667%
    }

    .ivu-col-xxl-push-22 {
        left: 91.66666667%
    }

    .ivu-col-xxl-pull-22 {
        right: 91.66666667%
    }

    .ivu-col-xxl-offset-22 {
        margin-left: 91.66666667%
    }

    .ivu-col-xxl-order-22 {
        -webkit-box-ordinal-group: 23;
        -ms-flex-order: 22;
        order: 22
    }

    .ivu-col-span-xxl-21 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 87.5%;
        flex: 0 0 87.5%;
        max-width: 87.5%
    }

    .ivu-col-xxl-push-21 {
        left: 87.5%
    }

    .ivu-col-xxl-pull-21 {
        right: 87.5%
    }

    .ivu-col-xxl-offset-21 {
        margin-left: 87.5%
    }

    .ivu-col-xxl-order-21 {
        -webkit-box-ordinal-group: 22;
        -ms-flex-order: 21;
        order: 21
    }

    .ivu-col-span-xxl-20 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333333%;
        flex: 0 0 83.33333333%;
        max-width: 83.33333333%
    }

    .ivu-col-xxl-push-20 {
        left: 83.33333333%
    }

    .ivu-col-xxl-pull-20 {
        right: 83.33333333%
    }

    .ivu-col-xxl-offset-20 {
        margin-left: 83.33333333%
    }

    .ivu-col-xxl-order-20 {
        -webkit-box-ordinal-group: 21;
        -ms-flex-order: 20;
        order: 20
    }

    .ivu-col-span-xxl-19 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 79.16666667%;
        flex: 0 0 79.16666667%;
        max-width: 79.16666667%
    }

    .ivu-col-xxl-push-19 {
        left: 79.16666667%
    }

    .ivu-col-xxl-pull-19 {
        right: 79.16666667%
    }

    .ivu-col-xxl-offset-19 {
        margin-left: 79.16666667%
    }

    .ivu-col-xxl-order-19 {
        -webkit-box-ordinal-group: 20;
        -ms-flex-order: 19;
        order: 19
    }

    .ivu-col-span-xxl-18 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .ivu-col-xxl-push-18 {
        left: 75%
    }

    .ivu-col-xxl-pull-18 {
        right: 75%
    }

    .ivu-col-xxl-offset-18 {
        margin-left: 75%
    }

    .ivu-col-xxl-order-18 {
        -webkit-box-ordinal-group: 19;
        -ms-flex-order: 18;
        order: 18
    }

    .ivu-col-span-xxl-17 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 70.83333333%;
        flex: 0 0 70.83333333%;
        max-width: 70.83333333%
    }

    .ivu-col-xxl-push-17 {
        left: 70.83333333%
    }

    .ivu-col-xxl-pull-17 {
        right: 70.83333333%
    }

    .ivu-col-xxl-offset-17 {
        margin-left: 70.83333333%
    }

    .ivu-col-xxl-order-17 {
        -webkit-box-ordinal-group: 18;
        -ms-flex-order: 17;
        order: 17
    }

    .ivu-col-span-xxl-16 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66666667%;
        flex: 0 0 66.66666667%;
        max-width: 66.66666667%
    }

    .ivu-col-xxl-push-16 {
        left: 66.66666667%
    }

    .ivu-col-xxl-pull-16 {
        right: 66.66666667%
    }

    .ivu-col-xxl-offset-16 {
        margin-left: 66.66666667%
    }

    .ivu-col-xxl-order-16 {
        -webkit-box-ordinal-group: 17;
        -ms-flex-order: 16;
        order: 16
    }

    .ivu-col-span-xxl-15 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 62.5%;
        flex: 0 0 62.5%;
        max-width: 62.5%
    }

    .ivu-col-xxl-push-15 {
        left: 62.5%
    }

    .ivu-col-xxl-pull-15 {
        right: 62.5%
    }

    .ivu-col-xxl-offset-15 {
        margin-left: 62.5%
    }

    .ivu-col-xxl-order-15 {
        -webkit-box-ordinal-group: 16;
        -ms-flex-order: 15;
        order: 15
    }

    .ivu-col-span-xxl-14 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333333%;
        flex: 0 0 58.33333333%;
        max-width: 58.33333333%
    }

    .ivu-col-xxl-push-14 {
        left: 58.33333333%
    }

    .ivu-col-xxl-pull-14 {
        right: 58.33333333%
    }

    .ivu-col-xxl-offset-14 {
        margin-left: 58.33333333%
    }

    .ivu-col-xxl-order-14 {
        -webkit-box-ordinal-group: 15;
        -ms-flex-order: 14;
        order: 14
    }

    .ivu-col-span-xxl-13 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 54.16666667%;
        flex: 0 0 54.16666667%;
        max-width: 54.16666667%
    }

    .ivu-col-xxl-push-13 {
        left: 54.16666667%
    }

    .ivu-col-xxl-pull-13 {
        right: 54.16666667%
    }

    .ivu-col-xxl-offset-13 {
        margin-left: 54.16666667%
    }

    .ivu-col-xxl-order-13 {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13
    }

    .ivu-col-span-xxl-12 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .ivu-col-xxl-push-12 {
        left: 50%
    }

    .ivu-col-xxl-pull-12 {
        right: 50%
    }

    .ivu-col-xxl-offset-12 {
        margin-left: 50%
    }

    .ivu-col-xxl-order-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12
    }

    .ivu-col-span-xxl-11 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 45.83333333%;
        flex: 0 0 45.83333333%;
        max-width: 45.83333333%
    }

    .ivu-col-xxl-push-11 {
        left: 45.83333333%
    }

    .ivu-col-xxl-pull-11 {
        right: 45.83333333%
    }

    .ivu-col-xxl-offset-11 {
        margin-left: 45.83333333%
    }

    .ivu-col-xxl-order-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11
    }

    .ivu-col-span-xxl-10 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66666667%;
        flex: 0 0 41.66666667%;
        max-width: 41.66666667%
    }

    .ivu-col-xxl-push-10 {
        left: 41.66666667%
    }

    .ivu-col-xxl-pull-10 {
        right: 41.66666667%
    }

    .ivu-col-xxl-offset-10 {
        margin-left: 41.66666667%
    }

    .ivu-col-xxl-order-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10
    }

    .ivu-col-span-xxl-9 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 37.5%;
        flex: 0 0 37.5%;
        max-width: 37.5%
    }

    .ivu-col-xxl-push-9 {
        left: 37.5%
    }

    .ivu-col-xxl-pull-9 {
        right: 37.5%
    }

    .ivu-col-xxl-offset-9 {
        margin-left: 37.5%
    }

    .ivu-col-xxl-order-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9
    }

    .ivu-col-span-xxl-8 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333333%;
        flex: 0 0 33.33333333%;
        max-width: 33.33333333%
    }

    .ivu-col-xxl-push-8 {
        left: 33.33333333%
    }

    .ivu-col-xxl-pull-8 {
        right: 33.33333333%
    }

    .ivu-col-xxl-offset-8 {
        margin-left: 33.33333333%
    }

    .ivu-col-xxl-order-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8
    }

    .ivu-col-span-xxl-7 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 29.16666667%;
        flex: 0 0 29.16666667%;
        max-width: 29.16666667%
    }

    .ivu-col-xxl-push-7 {
        left: 29.16666667%
    }

    .ivu-col-xxl-pull-7 {
        right: 29.16666667%
    }

    .ivu-col-xxl-offset-7 {
        margin-left: 29.16666667%
    }

    .ivu-col-xxl-order-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7
    }

    .ivu-col-span-xxl-6 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .ivu-col-xxl-push-6 {
        left: 25%
    }

    .ivu-col-xxl-pull-6 {
        right: 25%
    }

    .ivu-col-xxl-offset-6 {
        margin-left: 25%
    }

    .ivu-col-xxl-order-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6
    }

    .ivu-col-span-xxl-5 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 20.83333333%;
        flex: 0 0 20.83333333%;
        max-width: 20.83333333%
    }

    .ivu-col-xxl-push-5 {
        left: 20.83333333%
    }

    .ivu-col-xxl-pull-5 {
        right: 20.83333333%
    }

    .ivu-col-xxl-offset-5 {
        margin-left: 20.83333333%
    }

    .ivu-col-xxl-order-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5
    }

    .ivu-col-span-xxl-4 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66666667%;
        flex: 0 0 16.66666667%;
        max-width: 16.66666667%
    }

    .ivu-col-xxl-push-4 {
        left: 16.66666667%
    }

    .ivu-col-xxl-pull-4 {
        right: 16.66666667%
    }

    .ivu-col-xxl-offset-4 {
        margin-left: 16.66666667%
    }

    .ivu-col-xxl-order-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4
    }

    .ivu-col-span-xxl-3 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 12.5%;
        flex: 0 0 12.5%;
        max-width: 12.5%
    }

    .ivu-col-xxl-push-3 {
        left: 12.5%
    }

    .ivu-col-xxl-pull-3 {
        right: 12.5%
    }

    .ivu-col-xxl-offset-3 {
        margin-left: 12.5%
    }

    .ivu-col-xxl-order-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }

    .ivu-col-span-xxl-2 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333333%;
        flex: 0 0 8.33333333%;
        max-width: 8.33333333%
    }

    .ivu-col-xxl-push-2 {
        left: 8.33333333%
    }

    .ivu-col-xxl-pull-2 {
        right: 8.33333333%
    }

    .ivu-col-xxl-offset-2 {
        margin-left: 8.33333333%
    }

    .ivu-col-xxl-order-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }

    .ivu-col-span-xxl-1 {
        display: block;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 4.16666667%;
        flex: 0 0 4.16666667%;
        max-width: 4.16666667%
    }

    .ivu-col-xxl-push-1 {
        left: 4.16666667%
    }

    .ivu-col-xxl-pull-1 {
        right: 4.16666667%
    }

    .ivu-col-xxl-offset-1 {
        margin-left: 4.16666667%
    }

    .ivu-col-xxl-order-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }

    .ivu-col-span-xxl-0 {
        display: none
    }

    .ivu-col-push-0 {
        left: auto
    }

    .ivu-col-pull-0 {
        right: auto
    }

    .ivu-col-xxl-push-0 {
        left: auto
    }

    .ivu-col-xxl-pull-0 {
        right: auto
    }

    .ivu-col-xxl-offset-0 {
        margin-left: 0
    }

    .ivu-col-xxl-order-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
}

.ivu-article h1 {
    font-size: 26px;
    font-weight: 400
}

.ivu-article h2 {
    font-size: 20px;
    font-weight: 400
}

.ivu-article h3 {
    font-size: 16px;
    font-weight: 400
}

.ivu-article h4 {
    font-size: 14px;
    font-weight: 400
}

.ivu-article h5 {
    font-size: 12px;
    font-weight: 400
}

.ivu-article h6 {
    font-size: 12px;
    font-weight: 400
}

.ivu-article blockquote {
    padding: 5px 5px 3px 10px;
    line-height: 1.5;
    border-left: 4px solid #ddd;
    margin-bottom: 20px;
    color: #666;
    font-size: 14px
}

.ivu-article ul:not([class^=ivu-]) {
    padding-left: 40px;
    list-style-type: disc
}

.ivu-article li:not([class^=ivu-]) {
    margin-bottom: 5px;
    font-size: 14px
}

.ivu-article ol ul:not([class^=ivu-]),
.ivu-article ul ul:not([class^=ivu-]) {
    list-style-type: circle
}

.ivu-article p {
    margin: 5px;
    font-size: 14px
}

.ivu-article a:not([class^=ivu-])[target="_blank"]:after {
    content: "\F3F2";
    font-family: Ionicons;
    color: #aaa;
    margin-left: 3px
}

.fade-appear,
.fade-enter-active {
    -webkit-animation-duration: .15s;
    animation-duration: .15s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.fade-leave-active {
    -webkit-animation-duration: .15s;
    animation-duration: .15s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.fade-appear,
.fade-enter-active {
    -webkit-animation-name: ivuFadeIn;
    animation-name: ivuFadeIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.fade-leave-active {
    -webkit-animation-name: ivuFadeOut;
    animation-name: ivuFadeOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.fade-appear,
.fade-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear
}

.fade-leave-active {
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear
}

@-webkit-keyframes ivuFadeIn {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

@keyframes ivuFadeIn {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

@-webkit-keyframes ivuFadeOut {
    0% {
        opacity: 1
    }

    100% {
        opacity: 0
    }
}

@keyframes ivuFadeOut {
    0% {
        opacity: 1
    }

    100% {
        opacity: 0
    }
}

.move-up-appear,
.move-up-enter-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.move-up-leave-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.move-up-appear,
.move-up-enter-active {
    -webkit-animation-name: ivuMoveUpIn;
    animation-name: ivuMoveUpIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.move-up-leave-active {
    -webkit-animation-name: ivuMoveUpOut;
    animation-name: ivuMoveUpOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.move-up-appear,
.move-up-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.move-up-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.move-down-appear,
.move-down-enter-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.move-down-leave-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.move-down-appear,
.move-down-enter-active {
    -webkit-animation-name: ivuMoveDownIn;
    animation-name: ivuMoveDownIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.move-down-leave-active {
    -webkit-animation-name: ivuMoveDownOut;
    animation-name: ivuMoveDownOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.move-down-appear,
.move-down-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.move-down-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.move-left-appear,
.move-left-enter-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.move-left-leave-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.move-left-appear,
.move-left-enter-active {
    -webkit-animation-name: ivuMoveLeftIn;
    animation-name: ivuMoveLeftIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.move-left-leave-active {
    -webkit-animation-name: ivuMoveLeftOut;
    animation-name: ivuMoveLeftOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.move-left-appear,
.move-left-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.move-left-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.move-right-appear,
.move-right-enter-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.move-right-leave-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.move-right-appear,
.move-right-enter-active {
    -webkit-animation-name: ivuMoveRightIn;
    animation-name: ivuMoveRightIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.move-right-leave-active {
    -webkit-animation-name: ivuMoveRightOut;
    animation-name: ivuMoveRightOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.move-right-appear,
.move-right-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.move-right-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

@-webkit-keyframes ivuMoveDownIn {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(100%);
        transform: translateY(100%);
        opacity: 0
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }
}

@keyframes ivuMoveDownIn {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(100%);
        transform: translateY(100%);
        opacity: 0
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }
}

@-webkit-keyframes ivuMoveDownOut {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(100%);
        transform: translateY(100%);
        opacity: 0
    }
}

@keyframes ivuMoveDownOut {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(100%);
        transform: translateY(100%);
        opacity: 0
    }
}

@-webkit-keyframes ivuMoveLeftIn {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%);
        opacity: 0
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0);
        opacity: 1
    }
}

@keyframes ivuMoveLeftIn {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%);
        opacity: 0
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0);
        opacity: 1
    }
}

@-webkit-keyframes ivuMoveLeftOut {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0);
        opacity: 1
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%);
        opacity: 0
    }
}

@keyframes ivuMoveLeftOut {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0);
        opacity: 1
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%);
        opacity: 0
    }
}

@-webkit-keyframes ivuMoveRightIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes ivuMoveRightIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@-webkit-keyframes ivuMoveRightOut {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0);
        opacity: 1
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        opacity: 0
    }
}

@keyframes ivuMoveRightOut {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0);
        opacity: 1
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        opacity: 0
    }
}

@-webkit-keyframes ivuMoveUpIn {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
        opacity: 0
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }
}

@keyframes ivuMoveUpIn {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
        opacity: 0
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }
}

@-webkit-keyframes ivuMoveUpOut {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
        opacity: 0
    }
}

@keyframes ivuMoveUpOut {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
        opacity: 0
    }
}

.move-notice-appear,
.move-notice-enter-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.move-notice-leave-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.move-notice-appear,
.move-notice-enter-active {
    -webkit-animation-name: ivuMoveNoticeIn;
    animation-name: ivuMoveNoticeIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.move-notice-leave-active {
    -webkit-animation-name: ivuMoveNoticeOut;
    animation-name: ivuMoveNoticeOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.move-notice-appear,
.move-notice-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.move-notice-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

@-webkit-keyframes ivuMoveNoticeIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes ivuMoveNoticeIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@-webkit-keyframes ivuMoveNoticeOut {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0);
        opacity: 1
    }

    70% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        height: auto;
        padding: 16px;
        margin-bottom: 10px;
        opacity: 0
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        height: 0;
        padding: 0;
        margin-bottom: 0;
        opacity: 0
    }
}

@keyframes ivuMoveNoticeOut {
    0% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(0);
        transform: translateX(0);
        opacity: 1
    }

    70% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        height: auto;
        padding: 16px;
        margin-bottom: 10px;
        opacity: 0
    }

    100% {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        height: 0;
        padding: 0;
        margin-bottom: 0;
        opacity: 0
    }
}

.ease-appear,
.ease-enter-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ease-leave-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ease-appear,
.ease-enter-active {
    -webkit-animation-name: ivuEaseIn;
    animation-name: ivuEaseIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ease-leave-active {
    -webkit-animation-name: ivuEaseOut;
    animation-name: ivuEaseOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ease-appear,
.ease-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-duration: .2s;
    animation-duration: .2s
}

.ease-leave-active {
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-duration: .2s;
    animation-duration: .2s
}

@-webkit-keyframes ivuEaseIn {
    0% {
        opacity: 0;
        -webkit-transform: scale(.9);
        transform: scale(.9)
    }

    100% {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes ivuEaseIn {
    0% {
        opacity: 0;
        -webkit-transform: scale(.9);
        transform: scale(.9)
    }

    100% {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@-webkit-keyframes ivuEaseOut {
    0% {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    100% {
        opacity: 0;
        -webkit-transform: scale(.9);
        transform: scale(.9)
    }
}

@keyframes ivuEaseOut {
    0% {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    100% {
        opacity: 0;
        -webkit-transform: scale(.9);
        transform: scale(.9)
    }
}

.transition-drop-appear,
.transition-drop-enter-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.transition-drop-leave-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.transition-drop-appear,
.transition-drop-enter-active {
    -webkit-animation-name: ivuTransitionDropIn;
    animation-name: ivuTransitionDropIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.transition-drop-leave-active {
    -webkit-animation-name: ivuTransitionDropOut;
    animation-name: ivuTransitionDropOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.transition-drop-appear,
.transition-drop-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.transition-drop-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.slide-up-appear,
.slide-up-enter-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.slide-up-leave-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.slide-up-appear,
.slide-up-enter-active {
    -webkit-animation-name: ivuSlideUpIn;
    animation-name: ivuSlideUpIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.slide-up-leave-active {
    -webkit-animation-name: ivuSlideUpOut;
    animation-name: ivuSlideUpOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.slide-up-appear,
.slide-up-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.slide-up-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.slide-down-appear,
.slide-down-enter-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.slide-down-leave-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.slide-down-appear,
.slide-down-enter-active {
    -webkit-animation-name: ivuSlideDownIn;
    animation-name: ivuSlideDownIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.slide-down-leave-active {
    -webkit-animation-name: ivuSlideDownOut;
    animation-name: ivuSlideDownOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.slide-down-appear,
.slide-down-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.slide-down-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.slide-left-appear,
.slide-left-enter-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.slide-left-leave-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.slide-left-appear,
.slide-left-enter-active {
    -webkit-animation-name: ivuSlideLeftIn;
    animation-name: ivuSlideLeftIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.slide-left-leave-active {
    -webkit-animation-name: ivuSlideLeftOut;
    animation-name: ivuSlideLeftOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.slide-left-appear,
.slide-left-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.slide-left-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.slide-right-appear,
.slide-right-enter-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.slide-right-leave-active {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.slide-right-appear,
.slide-right-enter-active {
    -webkit-animation-name: ivuSlideRightIn;
    animation-name: ivuSlideRightIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.slide-right-leave-active {
    -webkit-animation-name: ivuSlideRightOut;
    animation-name: ivuSlideRightOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.slide-right-appear,
.slide-right-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.slide-right-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

@-webkit-keyframes ivuTransitionDropIn {
    0% {
        opacity: 0;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }

    100% {
        opacity: 1;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }
}

@keyframes ivuTransitionDropIn {
    0% {
        opacity: 0;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }

    100% {
        opacity: 1;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }
}

@-webkit-keyframes ivuTransitionDropOut {
    0% {
        opacity: 1;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }

    100% {
        opacity: 0;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }
}

@keyframes ivuTransitionDropOut {
    0% {
        opacity: 1;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }

    100% {
        opacity: 0;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }
}

@-webkit-keyframes ivuSlideUpIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }
}

@keyframes ivuSlideUpIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }
}

@-webkit-keyframes ivuSlideUpOut {
    0% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }
}

@keyframes ivuSlideUpOut {
    0% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }
}

@-webkit-keyframes ivuSlideDownIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 100% 100%;
        transform-origin: 100% 100%;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 100% 100%;
        transform-origin: 100% 100%;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }
}

@keyframes ivuSlideDownIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 100% 100%;
        transform-origin: 100% 100%;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 100% 100%;
        transform-origin: 100% 100%;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }
}

@-webkit-keyframes ivuSlideDownOut {
    0% {
        opacity: 1;
        -webkit-transform-origin: 100% 100%;
        transform-origin: 100% 100%;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 100% 100%;
        transform-origin: 100% 100%;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }
}

@keyframes ivuSlideDownOut {
    0% {
        opacity: 1;
        -webkit-transform-origin: 100% 100%;
        transform-origin: 100% 100%;
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 100% 100%;
        transform-origin: 100% 100%;
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8)
    }
}

@-webkit-keyframes ivuSlideLeftIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleX(.8);
        transform: scaleX(.8)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
}

@keyframes ivuSlideLeftIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleX(.8);
        transform: scaleX(.8)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
}

@-webkit-keyframes ivuSlideLeftOut {
    0% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleX(.8);
        transform: scaleX(.8)
    }
}

@keyframes ivuSlideLeftOut {
    0% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleX(.8);
        transform: scaleX(.8)
    }
}

@-webkit-keyframes ivuSlideRightIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 100% 0;
        transform-origin: 100% 0;
        -webkit-transform: scaleX(.8);
        transform: scaleX(.8)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 100% 0;
        transform-origin: 100% 0;
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
}

@keyframes ivuSlideRightIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 100% 0;
        transform-origin: 100% 0;
        -webkit-transform: scaleX(.8);
        transform: scaleX(.8)
    }

    100% {
        opacity: 1;
        -webkit-transform-origin: 100% 0;
        transform-origin: 100% 0;
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
}

@-webkit-keyframes ivuSlideRightOut {
    0% {
        opacity: 1;
        -webkit-transform-origin: 100% 0;
        transform-origin: 100% 0;
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 100% 0;
        transform-origin: 100% 0;
        -webkit-transform: scaleX(.8);
        transform: scaleX(.8)
    }
}

@keyframes ivuSlideRightOut {
    0% {
        opacity: 1;
        -webkit-transform-origin: 100% 0;
        transform-origin: 100% 0;
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 100% 0;
        transform-origin: 100% 0;
        -webkit-transform: scaleX(.8);
        transform: scaleX(.8)
    }
}

.collapse-transition {
    -webkit-transition: .2s height ease-in-out, .2s padding-top ease-in-out, .2s padding-bottom ease-in-out;
    transition: .2s height ease-in-out, .2s padding-top ease-in-out, .2s padding-bottom ease-in-out
}

.ivu-btn {
    display: inline-block;
    margin-bottom: 0;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    line-height: 1.5;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    height: 32px;
    padding: 0 15px;
    font-size: 14px;
    border-radius: 4px;
    -webkit-transition: color .2s linear, background-color .2s linear, border .2s linear, -webkit-box-shadow .2s linear;
    transition: color .2s linear, background-color .2s linear, border .2s linear, -webkit-box-shadow .2s linear;
    transition: color .2s linear, background-color .2s linear, border .2s linear, box-shadow .2s linear;
    transition: color .2s linear, background-color .2s linear, border .2s linear, box-shadow .2s linear, -webkit-box-shadow .2s linear;
    color: #515a6e;
    background-color: #fff;
    border-color: #dcdee2
}

.ivu-btn>.ivu-icon {
    line-height: 1.5
}

.ivu-btn-icon-only.ivu-btn-circle>.ivu-icon {
    vertical-align: baseline
}

.ivu-btn>i,
.ivu-btn>span {
    display: inline-block
}

.ivu-btn,
.ivu-btn:active,
.ivu-btn:focus {
    outline: 0
}

.ivu-btn:not([disabled]):hover {
    text-decoration: none
}

.ivu-btn:not([disabled]):active {
    outline: 0
}

.ivu-btn.disabled,
.ivu-btn[disabled] {
    cursor: not-allowed
}

.ivu-btn.disabled>*,
.ivu-btn[disabled]>* {
    pointer-events: none
}

.ivu-btn-large {
    height: 40px;
    padding: 0 15px;
    font-size: 16px;
    border-radius: 4px
}

.ivu-btn-small {
    height: 24px;
    padding: 0 7px;
    font-size: 14px;
    border-radius: 3px
}

.ivu-btn-icon-only {
    width: 32px;
    height: 32px;
    padding: 0;
    font-size: 16px;
    border-radius: 4px
}

.ivu-btn-icon-only.ivu-btn-large {
    width: 40px;
    height: 40px;
    padding: 0;
    font-size: 18px;
    border-radius: 4px
}

.ivu-btn-icon-only.ivu-btn-small {
    width: 24px;
    height: 24px;
    padding: 0;
    font-size: 14px;
    border-radius: 4px
}

.ivu-btn>a:only-child {
    color: currentColor
}

.ivu-btn>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn:hover {
    color: #747b8b;
    background-color: #fff;
    border-color: #e3e5e8
}

.ivu-btn:hover>a:only-child {
    color: currentColor
}

.ivu-btn:hover>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn.active,
.ivu-btn:active {
    color: #4d5669;
    background-color: #f2f2f2;
    border-color: #f2f2f2
}

.ivu-btn.active>a:only-child,
.ivu-btn:active>a:only-child {
    color: currentColor
}

.ivu-btn.active>a:only-child:after,
.ivu-btn:active>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn.disabled,
.ivu-btn.disabled.active,
.ivu-btn.disabled:active,
.ivu-btn.disabled:focus,
.ivu-btn.disabled:hover,
.ivu-btn[disabled],
.ivu-btn[disabled].active,
.ivu-btn[disabled]:active,
.ivu-btn[disabled]:focus,
.ivu-btn[disabled]:hover,
fieldset[disabled] .ivu-btn,
fieldset[disabled] .ivu-btn.active,
fieldset[disabled] .ivu-btn:active,
fieldset[disabled] .ivu-btn:focus,
fieldset[disabled] .ivu-btn:hover {
    color: #c5c8ce;
    background-color: #f7f7f7;
    border-color: #dcdee2
}

.ivu-btn.disabled.active>a:only-child,
.ivu-btn.disabled:active>a:only-child,
.ivu-btn.disabled:focus>a:only-child,
.ivu-btn.disabled:hover>a:only-child,
.ivu-btn.disabled>a:only-child,
.ivu-btn[disabled].active>a:only-child,
.ivu-btn[disabled]:active>a:only-child,
.ivu-btn[disabled]:focus>a:only-child,
.ivu-btn[disabled]:hover>a:only-child,
.ivu-btn[disabled]>a:only-child,
fieldset[disabled] .ivu-btn.active>a:only-child,
fieldset[disabled] .ivu-btn:active>a:only-child,
fieldset[disabled] .ivu-btn:focus>a:only-child,
fieldset[disabled] .ivu-btn:hover>a:only-child,
fieldset[disabled] .ivu-btn>a:only-child {
    color: currentColor
}

.ivu-btn.disabled.active>a:only-child:after,
.ivu-btn.disabled:active>a:only-child:after,
.ivu-btn.disabled:focus>a:only-child:after,
.ivu-btn.disabled:hover>a:only-child:after,
.ivu-btn.disabled>a:only-child:after,
.ivu-btn[disabled].active>a:only-child:after,
.ivu-btn[disabled]:active>a:only-child:after,
.ivu-btn[disabled]:focus>a:only-child:after,
.ivu-btn[disabled]:hover>a:only-child:after,
.ivu-btn[disabled]>a:only-child:after,
fieldset[disabled] .ivu-btn.active>a:only-child:after,
fieldset[disabled] .ivu-btn:active>a:only-child:after,
fieldset[disabled] .ivu-btn:focus>a:only-child:after,
fieldset[disabled] .ivu-btn:hover>a:only-child:after,
fieldset[disabled] .ivu-btn>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn:hover {
    color: #57a3f3;
    background-color: #fff;
    border-color: #57a3f3
}

.ivu-btn:hover>a:only-child {
    color: currentColor
}

.ivu-btn:hover>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn.active,
.ivu-btn:active {
    color: #2b85e4;
    background-color: #fff;
    border-color: #2b85e4
}

.ivu-btn.active>a:only-child,
.ivu-btn:active>a:only-child {
    color: currentColor
}

.ivu-btn.active>a:only-child:after,
.ivu-btn:active>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-btn-long {
    width: 100%
}

.ivu-btn>.ivu-icon+span,
.ivu-btn>span+.ivu-icon {
    margin-left: 4px
}

.ivu-btn-primary {
    color: #fff;
    background-color: #2d8cf0;
    border-color: #2d8cf0
}

.ivu-btn-primary>a:only-child {
    color: currentColor
}

.ivu-btn-primary>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-primary:hover {
    color: #fff;
    background-color: #57a3f3;
    border-color: #57a3f3
}

.ivu-btn-primary:hover>a:only-child {
    color: currentColor
}

.ivu-btn-primary:hover>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-primary.active,
.ivu-btn-primary:active {
    color: #f2f2f2;
    background-color: #2b85e4;
    border-color: #2b85e4
}

.ivu-btn-primary.active>a:only-child,
.ivu-btn-primary:active>a:only-child {
    color: currentColor
}

.ivu-btn-primary.active>a:only-child:after,
.ivu-btn-primary:active>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-primary.disabled,
.ivu-btn-primary.disabled.active,
.ivu-btn-primary.disabled:active,
.ivu-btn-primary.disabled:focus,
.ivu-btn-primary.disabled:hover,
.ivu-btn-primary[disabled],
.ivu-btn-primary[disabled].active,
.ivu-btn-primary[disabled]:active,
.ivu-btn-primary[disabled]:focus,
.ivu-btn-primary[disabled]:hover,
fieldset[disabled] .ivu-btn-primary,
fieldset[disabled] .ivu-btn-primary.active,
fieldset[disabled] .ivu-btn-primary:active,
fieldset[disabled] .ivu-btn-primary:focus,
fieldset[disabled] .ivu-btn-primary:hover {
    color: #c5c8ce;
    background-color: #f7f7f7;
    border-color: #dcdee2
}

.ivu-btn-primary.disabled.active>a:only-child,
.ivu-btn-primary.disabled:active>a:only-child,
.ivu-btn-primary.disabled:focus>a:only-child,
.ivu-btn-primary.disabled:hover>a:only-child,
.ivu-btn-primary.disabled>a:only-child,
.ivu-btn-primary[disabled].active>a:only-child,
.ivu-btn-primary[disabled]:active>a:only-child,
.ivu-btn-primary[disabled]:focus>a:only-child,
.ivu-btn-primary[disabled]:hover>a:only-child,
.ivu-btn-primary[disabled]>a:only-child,
fieldset[disabled] .ivu-btn-primary.active>a:only-child,
fieldset[disabled] .ivu-btn-primary:active>a:only-child,
fieldset[disabled] .ivu-btn-primary:focus>a:only-child,
fieldset[disabled] .ivu-btn-primary:hover>a:only-child,
fieldset[disabled] .ivu-btn-primary>a:only-child {
    color: currentColor
}

.ivu-btn-primary.disabled.active>a:only-child:after,
.ivu-btn-primary.disabled:active>a:only-child:after,
.ivu-btn-primary.disabled:focus>a:only-child:after,
.ivu-btn-primary.disabled:hover>a:only-child:after,
.ivu-btn-primary.disabled>a:only-child:after,
.ivu-btn-primary[disabled].active>a:only-child:after,
.ivu-btn-primary[disabled]:active>a:only-child:after,
.ivu-btn-primary[disabled]:focus>a:only-child:after,
.ivu-btn-primary[disabled]:hover>a:only-child:after,
.ivu-btn-primary[disabled]>a:only-child:after,
fieldset[disabled] .ivu-btn-primary.active>a:only-child:after,
fieldset[disabled] .ivu-btn-primary:active>a:only-child:after,
fieldset[disabled] .ivu-btn-primary:focus>a:only-child:after,
fieldset[disabled] .ivu-btn-primary:hover>a:only-child:after,
fieldset[disabled] .ivu-btn-primary>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-primary.active,
.ivu-btn-primary:active,
.ivu-btn-primary:hover {
    color: #fff
}

.ivu-btn-primary:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-btn-group:not(.ivu-btn-group-vertical) .ivu-btn-primary:not(:first-child):not(:last-child) {
    border-right-color: #2b85e4;
    border-left-color: #2b85e4
}

.ivu-btn-group:not(.ivu-btn-group-vertical) .ivu-btn-primary:first-child:not(:last-child) {
    border-right-color: #2b85e4
}

.ivu-btn-group:not(.ivu-btn-group-vertical) .ivu-btn-primary:first-child:not(:last-child)[disabled] {
    border-right-color: #dcdee2
}

.ivu-btn-group:not(.ivu-btn-group-vertical) .ivu-btn-primary+.ivu-btn,
.ivu-btn-group:not(.ivu-btn-group-vertical) .ivu-btn-primary:last-child:not(:first-child) {
    border-left-color: #2b85e4
}

.ivu-btn-group:not(.ivu-btn-group-vertical) .ivu-btn-primary+.ivu-btn[disabled],
.ivu-btn-group:not(.ivu-btn-group-vertical) .ivu-btn-primary:last-child:not(:first-child)[disabled] {
    border-left-color: #dcdee2
}

.ivu-btn-group-vertical .ivu-btn-primary:not(:first-child):not(:last-child) {
    border-top-color: #2b85e4;
    border-bottom-color: #2b85e4
}

.ivu-btn-group-vertical .ivu-btn-primary:first-child:not(:last-child) {
    border-bottom-color: #2b85e4
}

.ivu-btn-group-vertical .ivu-btn-primary:first-child:not(:last-child)[disabled] {
    border-top-color: #dcdee2
}

.ivu-btn-group-vertical .ivu-btn-primary+.ivu-btn,
.ivu-btn-group-vertical .ivu-btn-primary:last-child:not(:first-child) {
    border-top-color: #2b85e4
}

.ivu-btn-group-vertical .ivu-btn-primary+.ivu-btn[disabled],
.ivu-btn-group-vertical .ivu-btn-primary:last-child:not(:first-child)[disabled] {
    border-bottom-color: #dcdee2
}

.ivu-btn-dashed {
    color: #515a6e;
    background-color: #fff;
    border-color: #dcdee2;
    border-style: dashed
}

.ivu-btn-dashed>a:only-child {
    color: currentColor
}

.ivu-btn-dashed>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-dashed:hover {
    color: #747b8b;
    background-color: #fff;
    border-color: #e3e5e8
}

.ivu-btn-dashed:hover>a:only-child {
    color: currentColor
}

.ivu-btn-dashed:hover>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-dashed.active,
.ivu-btn-dashed:active {
    color: #4d5669;
    background-color: #f2f2f2;
    border-color: #f2f2f2
}

.ivu-btn-dashed.active>a:only-child,
.ivu-btn-dashed:active>a:only-child {
    color: currentColor
}

.ivu-btn-dashed.active>a:only-child:after,
.ivu-btn-dashed:active>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-dashed.disabled,
.ivu-btn-dashed.disabled.active,
.ivu-btn-dashed.disabled:active,
.ivu-btn-dashed.disabled:focus,
.ivu-btn-dashed.disabled:hover,
.ivu-btn-dashed[disabled],
.ivu-btn-dashed[disabled].active,
.ivu-btn-dashed[disabled]:active,
.ivu-btn-dashed[disabled]:focus,
.ivu-btn-dashed[disabled]:hover,
fieldset[disabled] .ivu-btn-dashed,
fieldset[disabled] .ivu-btn-dashed.active,
fieldset[disabled] .ivu-btn-dashed:active,
fieldset[disabled] .ivu-btn-dashed:focus,
fieldset[disabled] .ivu-btn-dashed:hover {
    color: #c5c8ce;
    background-color: #f7f7f7;
    border-color: #dcdee2
}

.ivu-btn-dashed.disabled.active>a:only-child,
.ivu-btn-dashed.disabled:active>a:only-child,
.ivu-btn-dashed.disabled:focus>a:only-child,
.ivu-btn-dashed.disabled:hover>a:only-child,
.ivu-btn-dashed.disabled>a:only-child,
.ivu-btn-dashed[disabled].active>a:only-child,
.ivu-btn-dashed[disabled]:active>a:only-child,
.ivu-btn-dashed[disabled]:focus>a:only-child,
.ivu-btn-dashed[disabled]:hover>a:only-child,
.ivu-btn-dashed[disabled]>a:only-child,
fieldset[disabled] .ivu-btn-dashed.active>a:only-child,
fieldset[disabled] .ivu-btn-dashed:active>a:only-child,
fieldset[disabled] .ivu-btn-dashed:focus>a:only-child,
fieldset[disabled] .ivu-btn-dashed:hover>a:only-child,
fieldset[disabled] .ivu-btn-dashed>a:only-child {
    color: currentColor
}

.ivu-btn-dashed.disabled.active>a:only-child:after,
.ivu-btn-dashed.disabled:active>a:only-child:after,
.ivu-btn-dashed.disabled:focus>a:only-child:after,
.ivu-btn-dashed.disabled:hover>a:only-child:after,
.ivu-btn-dashed.disabled>a:only-child:after,
.ivu-btn-dashed[disabled].active>a:only-child:after,
.ivu-btn-dashed[disabled]:active>a:only-child:after,
.ivu-btn-dashed[disabled]:focus>a:only-child:after,
.ivu-btn-dashed[disabled]:hover>a:only-child:after,
.ivu-btn-dashed[disabled]>a:only-child:after,
fieldset[disabled] .ivu-btn-dashed.active>a:only-child:after,
fieldset[disabled] .ivu-btn-dashed:active>a:only-child:after,
fieldset[disabled] .ivu-btn-dashed:focus>a:only-child:after,
fieldset[disabled] .ivu-btn-dashed:hover>a:only-child:after,
fieldset[disabled] .ivu-btn-dashed>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-dashed:hover {
    color: #57a3f3;
    background-color: #fff;
    border-color: #57a3f3
}

.ivu-btn-dashed:hover>a:only-child {
    color: currentColor
}

.ivu-btn-dashed:hover>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-dashed.active,
.ivu-btn-dashed:active {
    color: #2b85e4;
    background-color: #fff;
    border-color: #2b85e4
}

.ivu-btn-dashed.active>a:only-child,
.ivu-btn-dashed:active>a:only-child {
    color: currentColor
}

.ivu-btn-dashed.active>a:only-child:after,
.ivu-btn-dashed:active>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-dashed:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-btn-text {
    color: #515a6e;
    background-color: transparent;
    border-color: transparent
}

.ivu-btn-text>a:only-child {
    color: currentColor
}

.ivu-btn-text>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-text:hover {
    color: #747b8b;
    background-color: rgba(255, 255, 255, .2);
    border-color: rgba(255, 255, 255, .2)
}

.ivu-btn-text:hover>a:only-child {
    color: currentColor
}

.ivu-btn-text:hover>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-text.active,
.ivu-btn-text:active {
    color: #4d5669;
    background-color: rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05)
}

.ivu-btn-text.active>a:only-child,
.ivu-btn-text:active>a:only-child {
    color: currentColor
}

.ivu-btn-text.active>a:only-child:after,
.ivu-btn-text:active>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-text.disabled,
.ivu-btn-text.disabled.active,
.ivu-btn-text.disabled:active,
.ivu-btn-text.disabled:focus,
.ivu-btn-text.disabled:hover,
.ivu-btn-text[disabled],
.ivu-btn-text[disabled].active,
.ivu-btn-text[disabled]:active,
.ivu-btn-text[disabled]:focus,
.ivu-btn-text[disabled]:hover,
fieldset[disabled] .ivu-btn-text,
fieldset[disabled] .ivu-btn-text.active,
fieldset[disabled] .ivu-btn-text:active,
fieldset[disabled] .ivu-btn-text:focus,
fieldset[disabled] .ivu-btn-text:hover {
    color: #c5c8ce;
    background-color: #f7f7f7;
    border-color: #dcdee2
}

.ivu-btn-text.disabled.active>a:only-child,
.ivu-btn-text.disabled:active>a:only-child,
.ivu-btn-text.disabled:focus>a:only-child,
.ivu-btn-text.disabled:hover>a:only-child,
.ivu-btn-text.disabled>a:only-child,
.ivu-btn-text[disabled].active>a:only-child,
.ivu-btn-text[disabled]:active>a:only-child,
.ivu-btn-text[disabled]:focus>a:only-child,
.ivu-btn-text[disabled]:hover>a:only-child,
.ivu-btn-text[disabled]>a:only-child,
fieldset[disabled] .ivu-btn-text.active>a:only-child,
fieldset[disabled] .ivu-btn-text:active>a:only-child,
fieldset[disabled] .ivu-btn-text:focus>a:only-child,
fieldset[disabled] .ivu-btn-text:hover>a:only-child,
fieldset[disabled] .ivu-btn-text>a:only-child {
    color: currentColor
}

.ivu-btn-text.disabled.active>a:only-child:after,
.ivu-btn-text.disabled:active>a:only-child:after,
.ivu-btn-text.disabled:focus>a:only-child:after,
.ivu-btn-text.disabled:hover>a:only-child:after,
.ivu-btn-text.disabled>a:only-child:after,
.ivu-btn-text[disabled].active>a:only-child:after,
.ivu-btn-text[disabled]:active>a:only-child:after,
.ivu-btn-text[disabled]:focus>a:only-child:after,
.ivu-btn-text[disabled]:hover>a:only-child:after,
.ivu-btn-text[disabled]>a:only-child:after,
fieldset[disabled] .ivu-btn-text.active>a:only-child:after,
fieldset[disabled] .ivu-btn-text:active>a:only-child:after,
fieldset[disabled] .ivu-btn-text:focus>a:only-child:after,
fieldset[disabled] .ivu-btn-text:hover>a:only-child:after,
fieldset[disabled] .ivu-btn-text>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-text.disabled,
.ivu-btn-text.disabled.active,
.ivu-btn-text.disabled:active,
.ivu-btn-text.disabled:focus,
.ivu-btn-text.disabled:hover,
.ivu-btn-text[disabled],
.ivu-btn-text[disabled].active,
.ivu-btn-text[disabled]:active,
.ivu-btn-text[disabled]:focus,
.ivu-btn-text[disabled]:hover,
fieldset[disabled] .ivu-btn-text,
fieldset[disabled] .ivu-btn-text.active,
fieldset[disabled] .ivu-btn-text:active,
fieldset[disabled] .ivu-btn-text:focus,
fieldset[disabled] .ivu-btn-text:hover {
    color: #c5c8ce;
    background-color: #fff;
    border-color: transparent
}

.ivu-btn-text.disabled.active>a:only-child,
.ivu-btn-text.disabled:active>a:only-child,
.ivu-btn-text.disabled:focus>a:only-child,
.ivu-btn-text.disabled:hover>a:only-child,
.ivu-btn-text.disabled>a:only-child,
.ivu-btn-text[disabled].active>a:only-child,
.ivu-btn-text[disabled]:active>a:only-child,
.ivu-btn-text[disabled]:focus>a:only-child,
.ivu-btn-text[disabled]:hover>a:only-child,
.ivu-btn-text[disabled]>a:only-child,
fieldset[disabled] .ivu-btn-text.active>a:only-child,
fieldset[disabled] .ivu-btn-text:active>a:only-child,
fieldset[disabled] .ivu-btn-text:focus>a:only-child,
fieldset[disabled] .ivu-btn-text:hover>a:only-child,
fieldset[disabled] .ivu-btn-text>a:only-child {
    color: currentColor
}

.ivu-btn-text.disabled.active>a:only-child:after,
.ivu-btn-text.disabled:active>a:only-child:after,
.ivu-btn-text.disabled:focus>a:only-child:after,
.ivu-btn-text.disabled:hover>a:only-child:after,
.ivu-btn-text.disabled>a:only-child:after,
.ivu-btn-text[disabled].active>a:only-child:after,
.ivu-btn-text[disabled]:active>a:only-child:after,
.ivu-btn-text[disabled]:focus>a:only-child:after,
.ivu-btn-text[disabled]:hover>a:only-child:after,
.ivu-btn-text[disabled]>a:only-child:after,
fieldset[disabled] .ivu-btn-text.active>a:only-child:after,
fieldset[disabled] .ivu-btn-text:active>a:only-child:after,
fieldset[disabled] .ivu-btn-text:focus>a:only-child:after,
fieldset[disabled] .ivu-btn-text:hover>a:only-child:after,
fieldset[disabled] .ivu-btn-text>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-text:hover {
    color: #57a3f3;
    background-color: #fff;
    border-color: transparent
}

.ivu-btn-text:hover>a:only-child {
    color: currentColor
}

.ivu-btn-text:hover>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-text.active,
.ivu-btn-text:active {
    color: #2b85e4;
    background-color: #fff;
    border-color: transparent
}

.ivu-btn-text.active>a:only-child,
.ivu-btn-text:active>a:only-child {
    color: currentColor
}

.ivu-btn-text.active>a:only-child:after,
.ivu-btn-text:active>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-text:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-btn-success {
    color: #fff;
    background-color: #19be6b;
    border-color: #19be6b
}

.ivu-btn-success>a:only-child {
    color: currentColor
}

.ivu-btn-success>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-success:hover {
    color: #fff;
    background-color: #47cb89;
    border-color: #47cb89
}

.ivu-btn-success:hover>a:only-child {
    color: currentColor
}

.ivu-btn-success:hover>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-success.active,
.ivu-btn-success:active {
    color: #f2f2f2;
    background-color: #18b566;
    border-color: #18b566
}

.ivu-btn-success.active>a:only-child,
.ivu-btn-success:active>a:only-child {
    color: currentColor
}

.ivu-btn-success.active>a:only-child:after,
.ivu-btn-success:active>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-success.disabled,
.ivu-btn-success.disabled.active,
.ivu-btn-success.disabled:active,
.ivu-btn-success.disabled:focus,
.ivu-btn-success.disabled:hover,
.ivu-btn-success[disabled],
.ivu-btn-success[disabled].active,
.ivu-btn-success[disabled]:active,
.ivu-btn-success[disabled]:focus,
.ivu-btn-success[disabled]:hover,
fieldset[disabled] .ivu-btn-success,
fieldset[disabled] .ivu-btn-success.active,
fieldset[disabled] .ivu-btn-success:active,
fieldset[disabled] .ivu-btn-success:focus,
fieldset[disabled] .ivu-btn-success:hover {
    color: #c5c8ce;
    background-color: #f7f7f7;
    border-color: #dcdee2
}

.ivu-btn-success.disabled.active>a:only-child,
.ivu-btn-success.disabled:active>a:only-child,
.ivu-btn-success.disabled:focus>a:only-child,
.ivu-btn-success.disabled:hover>a:only-child,
.ivu-btn-success.disabled>a:only-child,
.ivu-btn-success[disabled].active>a:only-child,
.ivu-btn-success[disabled]:active>a:only-child,
.ivu-btn-success[disabled]:focus>a:only-child,
.ivu-btn-success[disabled]:hover>a:only-child,
.ivu-btn-success[disabled]>a:only-child,
fieldset[disabled] .ivu-btn-success.active>a:only-child,
fieldset[disabled] .ivu-btn-success:active>a:only-child,
fieldset[disabled] .ivu-btn-success:focus>a:only-child,
fieldset[disabled] .ivu-btn-success:hover>a:only-child,
fieldset[disabled] .ivu-btn-success>a:only-child {
    color: currentColor
}

.ivu-btn-success.disabled.active>a:only-child:after,
.ivu-btn-success.disabled:active>a:only-child:after,
.ivu-btn-success.disabled:focus>a:only-child:after,
.ivu-btn-success.disabled:hover>a:only-child:after,
.ivu-btn-success.disabled>a:only-child:after,
.ivu-btn-success[disabled].active>a:only-child:after,
.ivu-btn-success[disabled]:active>a:only-child:after,
.ivu-btn-success[disabled]:focus>a:only-child:after,
.ivu-btn-success[disabled]:hover>a:only-child:after,
.ivu-btn-success[disabled]>a:only-child:after,
fieldset[disabled] .ivu-btn-success.active>a:only-child:after,
fieldset[disabled] .ivu-btn-success:active>a:only-child:after,
fieldset[disabled] .ivu-btn-success:focus>a:only-child:after,
fieldset[disabled] .ivu-btn-success:hover>a:only-child:after,
fieldset[disabled] .ivu-btn-success>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-success.active,
.ivu-btn-success:active,
.ivu-btn-success:hover {
    color: #fff
}

.ivu-btn-success:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(25, 190, 107, .2);
    box-shadow: 0 0 0 2px rgba(25, 190, 107, .2)
}

.ivu-btn-warning {
    color: #fff;
    background-color: #f90;
    border-color: #f90
}

.ivu-btn-warning>a:only-child {
    color: currentColor
}

.ivu-btn-warning>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-warning:hover {
    color: #fff;
    background-color: #ffad33;
    border-color: #ffad33
}

.ivu-btn-warning:hover>a:only-child {
    color: currentColor
}

.ivu-btn-warning:hover>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-warning.active,
.ivu-btn-warning:active {
    color: #f2f2f2;
    background-color: #f29100;
    border-color: #f29100
}

.ivu-btn-warning.active>a:only-child,
.ivu-btn-warning:active>a:only-child {
    color: currentColor
}

.ivu-btn-warning.active>a:only-child:after,
.ivu-btn-warning:active>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-warning.disabled,
.ivu-btn-warning.disabled.active,
.ivu-btn-warning.disabled:active,
.ivu-btn-warning.disabled:focus,
.ivu-btn-warning.disabled:hover,
.ivu-btn-warning[disabled],
.ivu-btn-warning[disabled].active,
.ivu-btn-warning[disabled]:active,
.ivu-btn-warning[disabled]:focus,
.ivu-btn-warning[disabled]:hover,
fieldset[disabled] .ivu-btn-warning,
fieldset[disabled] .ivu-btn-warning.active,
fieldset[disabled] .ivu-btn-warning:active,
fieldset[disabled] .ivu-btn-warning:focus,
fieldset[disabled] .ivu-btn-warning:hover {
    color: #c5c8ce;
    background-color: #f7f7f7;
    border-color: #dcdee2
}

.ivu-btn-warning.disabled.active>a:only-child,
.ivu-btn-warning.disabled:active>a:only-child,
.ivu-btn-warning.disabled:focus>a:only-child,
.ivu-btn-warning.disabled:hover>a:only-child,
.ivu-btn-warning.disabled>a:only-child,
.ivu-btn-warning[disabled].active>a:only-child,
.ivu-btn-warning[disabled]:active>a:only-child,
.ivu-btn-warning[disabled]:focus>a:only-child,
.ivu-btn-warning[disabled]:hover>a:only-child,
.ivu-btn-warning[disabled]>a:only-child,
fieldset[disabled] .ivu-btn-warning.active>a:only-child,
fieldset[disabled] .ivu-btn-warning:active>a:only-child,
fieldset[disabled] .ivu-btn-warning:focus>a:only-child,
fieldset[disabled] .ivu-btn-warning:hover>a:only-child,
fieldset[disabled] .ivu-btn-warning>a:only-child {
    color: currentColor
}

.ivu-btn-warning.disabled.active>a:only-child:after,
.ivu-btn-warning.disabled:active>a:only-child:after,
.ivu-btn-warning.disabled:focus>a:only-child:after,
.ivu-btn-warning.disabled:hover>a:only-child:after,
.ivu-btn-warning.disabled>a:only-child:after,
.ivu-btn-warning[disabled].active>a:only-child:after,
.ivu-btn-warning[disabled]:active>a:only-child:after,
.ivu-btn-warning[disabled]:focus>a:only-child:after,
.ivu-btn-warning[disabled]:hover>a:only-child:after,
.ivu-btn-warning[disabled]>a:only-child:after,
fieldset[disabled] .ivu-btn-warning.active>a:only-child:after,
fieldset[disabled] .ivu-btn-warning:active>a:only-child:after,
fieldset[disabled] .ivu-btn-warning:focus>a:only-child:after,
fieldset[disabled] .ivu-btn-warning:hover>a:only-child:after,
fieldset[disabled] .ivu-btn-warning>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-warning.active,
.ivu-btn-warning:active,
.ivu-btn-warning:hover {
    color: #fff
}

.ivu-btn-warning:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(255, 153, 0, .2);
    box-shadow: 0 0 0 2px rgba(255, 153, 0, .2)
}

.ivu-btn-error {
    color: #fff;
    background-color: #ed4014;
    border-color: #ed4014
}

.ivu-btn-error>a:only-child {
    color: currentColor
}

.ivu-btn-error>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-error:hover {
    color: #fff;
    background-color: #f16643;
    border-color: #f16643
}

.ivu-btn-error:hover>a:only-child {
    color: currentColor
}

.ivu-btn-error:hover>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-error.active,
.ivu-btn-error:active {
    color: #f2f2f2;
    background-color: #e13d13;
    border-color: #e13d13
}

.ivu-btn-error.active>a:only-child,
.ivu-btn-error:active>a:only-child {
    color: currentColor
}

.ivu-btn-error.active>a:only-child:after,
.ivu-btn-error:active>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-error.disabled,
.ivu-btn-error.disabled.active,
.ivu-btn-error.disabled:active,
.ivu-btn-error.disabled:focus,
.ivu-btn-error.disabled:hover,
.ivu-btn-error[disabled],
.ivu-btn-error[disabled].active,
.ivu-btn-error[disabled]:active,
.ivu-btn-error[disabled]:focus,
.ivu-btn-error[disabled]:hover,
fieldset[disabled] .ivu-btn-error,
fieldset[disabled] .ivu-btn-error.active,
fieldset[disabled] .ivu-btn-error:active,
fieldset[disabled] .ivu-btn-error:focus,
fieldset[disabled] .ivu-btn-error:hover {
    color: #c5c8ce;
    background-color: #f7f7f7;
    border-color: #dcdee2
}

.ivu-btn-error.disabled.active>a:only-child,
.ivu-btn-error.disabled:active>a:only-child,
.ivu-btn-error.disabled:focus>a:only-child,
.ivu-btn-error.disabled:hover>a:only-child,
.ivu-btn-error.disabled>a:only-child,
.ivu-btn-error[disabled].active>a:only-child,
.ivu-btn-error[disabled]:active>a:only-child,
.ivu-btn-error[disabled]:focus>a:only-child,
.ivu-btn-error[disabled]:hover>a:only-child,
.ivu-btn-error[disabled]>a:only-child,
fieldset[disabled] .ivu-btn-error.active>a:only-child,
fieldset[disabled] .ivu-btn-error:active>a:only-child,
fieldset[disabled] .ivu-btn-error:focus>a:only-child,
fieldset[disabled] .ivu-btn-error:hover>a:only-child,
fieldset[disabled] .ivu-btn-error>a:only-child {
    color: currentColor
}

.ivu-btn-error.disabled.active>a:only-child:after,
.ivu-btn-error.disabled:active>a:only-child:after,
.ivu-btn-error.disabled:focus>a:only-child:after,
.ivu-btn-error.disabled:hover>a:only-child:after,
.ivu-btn-error.disabled>a:only-child:after,
.ivu-btn-error[disabled].active>a:only-child:after,
.ivu-btn-error[disabled]:active>a:only-child:after,
.ivu-btn-error[disabled]:focus>a:only-child:after,
.ivu-btn-error[disabled]:hover>a:only-child:after,
.ivu-btn-error[disabled]>a:only-child:after,
fieldset[disabled] .ivu-btn-error.active>a:only-child:after,
fieldset[disabled] .ivu-btn-error:active>a:only-child:after,
fieldset[disabled] .ivu-btn-error:focus>a:only-child:after,
fieldset[disabled] .ivu-btn-error:hover>a:only-child:after,
fieldset[disabled] .ivu-btn-error>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-error.active,
.ivu-btn-error:active,
.ivu-btn-error:hover {
    color: #fff
}

.ivu-btn-error:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(237, 64, 20, .2);
    box-shadow: 0 0 0 2px rgba(237, 64, 20, .2)
}

.ivu-btn-info {
    color: #fff;
    background-color: #2db7f5;
    border-color: #2db7f5
}

.ivu-btn-info>a:only-child {
    color: currentColor
}

.ivu-btn-info>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-info:hover {
    color: #fff;
    background-color: #57c5f7;
    border-color: #57c5f7
}

.ivu-btn-info:hover>a:only-child {
    color: currentColor
}

.ivu-btn-info:hover>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-info.active,
.ivu-btn-info:active {
    color: #f2f2f2;
    background-color: #2baee9;
    border-color: #2baee9
}

.ivu-btn-info.active>a:only-child,
.ivu-btn-info:active>a:only-child {
    color: currentColor
}

.ivu-btn-info.active>a:only-child:after,
.ivu-btn-info:active>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-info.disabled,
.ivu-btn-info.disabled.active,
.ivu-btn-info.disabled:active,
.ivu-btn-info.disabled:focus,
.ivu-btn-info.disabled:hover,
.ivu-btn-info[disabled],
.ivu-btn-info[disabled].active,
.ivu-btn-info[disabled]:active,
.ivu-btn-info[disabled]:focus,
.ivu-btn-info[disabled]:hover,
fieldset[disabled] .ivu-btn-info,
fieldset[disabled] .ivu-btn-info.active,
fieldset[disabled] .ivu-btn-info:active,
fieldset[disabled] .ivu-btn-info:focus,
fieldset[disabled] .ivu-btn-info:hover {
    color: #c5c8ce;
    background-color: #f7f7f7;
    border-color: #dcdee2
}

.ivu-btn-info.disabled.active>a:only-child,
.ivu-btn-info.disabled:active>a:only-child,
.ivu-btn-info.disabled:focus>a:only-child,
.ivu-btn-info.disabled:hover>a:only-child,
.ivu-btn-info.disabled>a:only-child,
.ivu-btn-info[disabled].active>a:only-child,
.ivu-btn-info[disabled]:active>a:only-child,
.ivu-btn-info[disabled]:focus>a:only-child,
.ivu-btn-info[disabled]:hover>a:only-child,
.ivu-btn-info[disabled]>a:only-child,
fieldset[disabled] .ivu-btn-info.active>a:only-child,
fieldset[disabled] .ivu-btn-info:active>a:only-child,
fieldset[disabled] .ivu-btn-info:focus>a:only-child,
fieldset[disabled] .ivu-btn-info:hover>a:only-child,
fieldset[disabled] .ivu-btn-info>a:only-child {
    color: currentColor
}

.ivu-btn-info.disabled.active>a:only-child:after,
.ivu-btn-info.disabled:active>a:only-child:after,
.ivu-btn-info.disabled:focus>a:only-child:after,
.ivu-btn-info.disabled:hover>a:only-child:after,
.ivu-btn-info.disabled>a:only-child:after,
.ivu-btn-info[disabled].active>a:only-child:after,
.ivu-btn-info[disabled]:active>a:only-child:after,
.ivu-btn-info[disabled]:focus>a:only-child:after,
.ivu-btn-info[disabled]:hover>a:only-child:after,
.ivu-btn-info[disabled]>a:only-child:after,
fieldset[disabled] .ivu-btn-info.active>a:only-child:after,
fieldset[disabled] .ivu-btn-info:active>a:only-child:after,
fieldset[disabled] .ivu-btn-info:focus>a:only-child:after,
fieldset[disabled] .ivu-btn-info:hover>a:only-child:after,
fieldset[disabled] .ivu-btn-info>a:only-child:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: 0 0
}

.ivu-btn-info.active,
.ivu-btn-info:active,
.ivu-btn-info:hover {
    color: #fff
}

.ivu-btn-info:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(45, 183, 245, .2);
    box-shadow: 0 0 0 2px rgba(45, 183, 245, .2)
}

.ivu-btn-circle,
.ivu-btn-circle-outline {
    border-radius: 32px
}

.ivu-btn-circle-outline.ivu-btn-large,
.ivu-btn-circle.ivu-btn-large {
    border-radius: 40px
}

.ivu-btn-circle-outline.ivu-btn-size,
.ivu-btn-circle.ivu-btn-size {
    border-radius: 24px
}

.ivu-btn-circle-outline.ivu-btn-icon-only,
.ivu-btn-circle.ivu-btn-icon-only {
    width: 32px;
    height: 32px;
    padding: 0;
    font-size: 16px;
    border-radius: 50%
}

.ivu-btn-circle-outline.ivu-btn-icon-only.ivu-btn-large,
.ivu-btn-circle.ivu-btn-icon-only.ivu-btn-large {
    width: 40px;
    height: 40px;
    padding: 0;
    font-size: 18px;
    border-radius: 50%
}

.ivu-btn-circle-outline.ivu-btn-icon-only.ivu-btn-small,
.ivu-btn-circle.ivu-btn-icon-only.ivu-btn-small {
    width: 24px;
    height: 24px;
    padding: 0;
    font-size: 14px;
    border-radius: 50%
}

.ivu-btn:before {
    position: absolute;
    top: -1px;
    left: -1px;
    bottom: -1px;
    right: -1px;
    background: #fff;
    opacity: .35;
    content: '';
    border-radius: inherit;
    z-index: 1;
    -webkit-transition: opacity .2s;
    transition: opacity .2s;
    pointer-events: none;
    display: none
}

.ivu-btn.ivu-btn-loading {
    pointer-events: none;
    position: relative
}

.ivu-btn.ivu-btn-loading:before {
    display: block
}

.ivu-btn-group {
    position: relative;
    display: inline-block;
    vertical-align: middle
}

.ivu-btn-group>.ivu-btn {
    position: relative;
    float: left
}

.ivu-btn-group>.ivu-btn.active,
.ivu-btn-group>.ivu-btn:active,
.ivu-btn-group>.ivu-btn:hover {
    z-index: 2
}

.ivu-btn-group-circle .ivu-btn {
    border-radius: 32px
}

.ivu-btn-group-large.ivu-btn-group-circle .ivu-btn {
    border-radius: 40px
}

.ivu-btn-group-large>.ivu-btn {
    height: 40px;
    padding: 0 15px;
    font-size: 16px;
    border-radius: 4px
}

.ivu-btn-group-small.ivu-btn-group-circle .ivu-btn {
    border-radius: 24px
}

.ivu-btn-group-small>.ivu-btn {
    height: 24px;
    padding: 0 7px;
    font-size: 14px;
    border-radius: 3px
}

.ivu-btn-group-small>.ivu-btn>.ivu-icon {
    font-size: 14px
}

.ivu-btn-group-small .ivu-btn-icon-only {
    width: 24px;
    height: 24px;
    padding: 0
}

.ivu-btn-group-large .ivu-btn-icon-only {
    width: 40px;
    height: 40px;
    padding: 0
}

.ivu-btn+.ivu-btn-group,
.ivu-btn-group .ivu-btn+.ivu-btn,
.ivu-btn-group+.ivu-btn,
.ivu-btn-group+.ivu-btn-group {
    margin-left: -1px
}

.ivu-btn-group .ivu-btn:not(:first-child):not(:last-child) {
    border-radius: 0
}

.ivu-btn-group:not(.ivu-btn-group-vertical)>.ivu-btn:first-child {
    margin-left: 0
}

.ivu-btn-group:not(.ivu-btn-group-vertical)>.ivu-btn:first-child:not(:last-child) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.ivu-btn-group:not(.ivu-btn-group-vertical)>.ivu-btn:last-child:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.ivu-btn-group>.ivu-btn-group {
    float: left
}

.ivu-btn-group>.ivu-btn-group:not(:first-child):not(:last-child)>.ivu-btn {
    border-radius: 0
}

.ivu-btn-group:not(.ivu-btn-group-vertical)>.ivu-btn-group:first-child:not(:last-child)>.ivu-btn:last-child {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
    padding-right: 8px
}

.ivu-btn-group:not(.ivu-btn-group-vertical)>.ivu-btn-group:last-child:not(:first-child)>.ivu-btn:first-child {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    padding-left: 8px
}

.ivu-btn-group-vertical {
    display: inline-block;
    vertical-align: middle
}

.ivu-btn-group-vertical>.ivu-btn {
    display: block;
    width: 100%;
    max-width: 100%;
    float: none;
    min-width: 32px
}

.ivu-btn-group-vertical.ivu-btn-group-small>.ivu-btn {
    min-width: 24px
}

.ivu-btn-group-vertical.ivu-btn-group-large>.ivu-btn {
    min-width: 40px
}

.ivu-btn+.ivu-btn-group-vertical,
.ivu-btn-group-vertical .ivu-btn+.ivu-btn,
.ivu-btn-group-vertical+.ivu-btn,
.ivu-btn-group-vertical+.ivu-btn-group-vertical {
    margin-top: -1px;
    margin-left: 0
}

.ivu-btn-group-vertical>.ivu-btn:first-child {
    margin-top: 0
}

.ivu-btn-group-vertical>.ivu-btn:first-child:not(:last-child) {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.ivu-btn-group-vertical>.ivu-btn:last-child:not(:first-child) {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.ivu-btn-group-vertical>.ivu-btn-group-vertical:first-child:not(:last-child)>.ivu-btn:last-child {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    padding-bottom: 8px
}

.ivu-btn-group-vertical>.ivu-btn-group-vertical:last-child:not(:first-child)>.ivu-btn:first-child {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    padding-top: 8px
}

.ivu-btn-ghost {
    color: #fff;
    background: 0 0
}

.ivu-btn-ghost:hover {
    background: 0 0
}

.ivu-btn-ghost.ivu-btn-dashed,
.ivu-btn-ghost.ivu-btn-default {
    color: #fff;
    border-color: #fff
}

.ivu-btn-ghost.ivu-btn-dashed:hover,
.ivu-btn-ghost.ivu-btn-default:hover {
    color: #57a3f3;
    border-color: #57a3f3
}

.ivu-btn-ghost.ivu-btn-primary {
    color: #2d8cf0
}

.ivu-btn-ghost.ivu-btn-primary:hover {
    color: #57a3f3;
    background: rgba(245, 249, 254, .5)
}

.ivu-btn-ghost.ivu-btn-info {
    color: #2db7f5
}

.ivu-btn-ghost.ivu-btn-info:hover {
    color: #57c5f7;
    background: rgba(245, 251, 254, .5)
}

.ivu-btn-ghost.ivu-btn-success {
    color: #19be6b
}

.ivu-btn-ghost.ivu-btn-success:hover {
    color: #47cb89;
    background: rgba(244, 252, 248, .5)
}

.ivu-btn-ghost.ivu-btn-warning {
    color: #f90
}

.ivu-btn-ghost.ivu-btn-warning:hover {
    color: #ffad33;
    background: rgba(255, 250, 242, .5)
}

.ivu-btn-ghost.ivu-btn-error {
    color: #ed4014
}

.ivu-btn-ghost.ivu-btn-error:hover {
    color: #f16643;
    background: rgba(254, 245, 243, .5)
}

.ivu-btn-ghost.ivu-btn-dashed[disabled],
.ivu-btn-ghost.ivu-btn-default[disabled],
.ivu-btn-ghost.ivu-btn-error[disabled],
.ivu-btn-ghost.ivu-btn-info[disabled],
.ivu-btn-ghost.ivu-btn-primary[disabled],
.ivu-btn-ghost.ivu-btn-success[disabled],
.ivu-btn-ghost.ivu-btn-warning[disabled] {
    background: 0 0;
    color: rgba(0, 0, 0, .25);
    border-color: #dcdee2
}

.ivu-btn-ghost.ivu-btn-text[disabled] {
    background: 0 0;
    color: rgba(0, 0, 0, .25)
}

a.ivu-btn {
    padding-top: .1px;
    line-height: 30px
}

a.ivu-btn-large {
    line-height: 38px
}

a.ivu-btn-small {
    line-height: 22px
}

.ivu-affix {
    position: fixed;
    z-index: 10
}

.ivu-back-top {
    z-index: 10;
    position: fixed;
    cursor: pointer;
    display: none
}

.ivu-back-top.ivu-back-top-show {
    display: block
}

.ivu-back-top-inner {
    background-color: rgba(0, 0, 0, .6);
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 3px rgba(0, 0, 0, .2);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-back-top-inner:hover {
    background-color: rgba(0, 0, 0, .7)
}

.ivu-back-top i {
    color: #fff;
    font-size: 24px;
    padding: 8px 12px
}

.ivu-badge {
    position: relative;
    display: inline-block
}

.ivu-badge-count {
    font-family: "Monospaced Number";
    line-height: 1;
    vertical-align: middle;
    position: absolute;
    -webkit-transform: translateX(50%);
    -ms-transform: translateX(50%);
    transform: translateX(50%);
    top: -10px;
    right: 0;
    height: 20px;
    border-radius: 10px;
    min-width: 20px;
    background: #ed4014;
    border: 1px solid transparent;
    color: #fff;
    line-height: 18px;
    text-align: center;
    padding: 0 6px;
    font-size: 12px;
    white-space: nowrap;
    -webkit-transform-origin: -10% center;
    -ms-transform-origin: -10% center;
    transform-origin: -10% center;
    z-index: 10;
    -webkit-box-shadow: 0 0 0 1px #fff;
    box-shadow: 0 0 0 1px #fff
}

.ivu-badge-count-custom {
    background: 0 0;
    color: inherit;
    border-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-badge-count a,
.ivu-badge-count a:hover {
    color: #fff
}

.ivu-badge-count-alone {
    top: auto;
    display: block;
    position: relative;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0)
}

.ivu-badge-count-primary {
    background: #2d8cf0
}

.ivu-badge-count-success {
    background: #19be6b
}

.ivu-badge-count-error {
    background: #ed4014
}

.ivu-badge-count-warning {
    background: #f90
}

.ivu-badge-count-info {
    background: #2db7f5
}

.ivu-badge-count-normal {
    background: #e6ebf1;
    color: #808695
}

.ivu-badge-dot {
    position: absolute;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    -webkit-transform-origin: 0 center;
    -ms-transform-origin: 0 center;
    transform-origin: 0 center;
    top: -4px;
    right: -8px;
    height: 8px;
    width: 8px;
    border-radius: 100%;
    background: #ed4014;
    z-index: 10;
    -webkit-box-shadow: 0 0 0 1px #fff;
    box-shadow: 0 0 0 1px #fff
}

.ivu-badge-status {
    line-height: inherit;
    vertical-align: baseline
}

.ivu-badge-status-dot {
    width: 6px;
    height: 6px;
    display: inline-block;
    border-radius: 50%;
    vertical-align: middle;
    position: relative;
    top: -1px
}

.ivu-badge-status-success {
    background-color: #19be6b
}

.ivu-badge-status-processing {
    background-color: #2d8cf0;
    position: relative
}

.ivu-badge-status-processing:after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 1px solid #2d8cf0;
    content: '';
    -webkit-animation: aniStatusProcessing 1.2s infinite ease-in-out;
    animation: aniStatusProcessing 1.2s infinite ease-in-out
}

.ivu-badge-status-default {
    background-color: #e6ebf1
}

.ivu-badge-status-error {
    background-color: #ed4014
}

.ivu-badge-status-warning {
    background-color: #f90
}

.ivu-badge-status-text {
    display: inline-block;
    color: #515a6e;
    font-size: 14px;
    margin-left: 6px
}

.ivu-badge-status-pink {
    background-color: #eb2f96
}

.ivu-badge-status-magenta {
    background-color: #eb2f96
}

.ivu-badge-status-red {
    background-color: #f5222d
}

.ivu-badge-status-volcano {
    background-color: #fa541c
}

.ivu-badge-status-orange {
    background-color: #fa8c16
}

.ivu-badge-status-yellow {
    background-color: #fadb14
}

.ivu-badge-status-gold {
    background-color: #faad14
}

.ivu-badge-status-cyan {
    background-color: #13c2c2
}

.ivu-badge-status-lime {
    background-color: #a0d911
}

.ivu-badge-status-green {
    background-color: #52c41a
}

.ivu-badge-status-blue {
    background-color: #1890ff
}

.ivu-badge-status-geekblue {
    background-color: #2f54eb
}

.ivu-badge-status-purple {
    background-color: #722ed1
}

@-webkit-keyframes aniStatusProcessing {
    0% {
        -webkit-transform: scale(.8);
        transform: scale(.8);
        opacity: .5
    }

    100% {
        -webkit-transform: scale(2.4);
        transform: scale(2.4);
        opacity: 0
    }
}

@keyframes aniStatusProcessing {
    0% {
        -webkit-transform: scale(.8);
        transform: scale(.8);
        opacity: .5
    }

    100% {
        -webkit-transform: scale(2.4);
        transform: scale(2.4);
        opacity: 0
    }
}

.ivu-chart-circle {
    display: inline-block;
    position: relative
}

.ivu-chart-circle-inner {
    width: 100%;
    text-align: center;
    position: absolute;
    left: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    line-height: 1
}

.ivu-spin {
    color: #2d8cf0;
    vertical-align: middle;
    text-align: center
}

.ivu-spin-dot {
    position: relative;
    display: block;
    border-radius: 50%;
    background-color: #2d8cf0;
    width: 20px;
    height: 20px;
    -webkit-animation: ani-spin-bounce 1s 0s ease-in-out infinite;
    animation: ani-spin-bounce 1s 0s ease-in-out infinite
}

.ivu-spin-large .ivu-spin-dot {
    width: 32px;
    height: 32px
}

.ivu-spin-small .ivu-spin-dot {
    width: 12px;
    height: 12px
}

.ivu-spin-fix {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 8;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, .9)
}

.ivu-spin-fullscreen {
    z-index: 2010
}

.ivu-spin-fullscreen-wrapper {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

.ivu-spin-fix .ivu-spin-main {
    position: absolute;
    top: 50%;
    left: 50%;
    -ms-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.ivu-spin-fix .ivu-spin-dot {
    display: inline-block
}

.ivu-spin-show-text .ivu-spin-dot,
.ivu-spin-text {
    display: none
}

.ivu-spin-show-text .ivu-spin-text {
    display: block
}

.ivu-table-wrapper>.ivu-spin-fix {
    border: none
}

.ivu-table-wrapper-with-border>.ivu-spin-fix {
    border: 1px solid #dcdee2;
    border-top: 0;
    border-left: 0
}

@-webkit-keyframes ani-spin-bounce {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 0
    }
}

@keyframes ani-spin-bounce {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 0
    }
}

.ivu-alert {
    position: relative;
    padding: 8px 48px 8px 16px;
    border-radius: 4px;
    color: #515a6e;
    font-size: 14px;
    line-height: 16px;
    margin-bottom: 10px
}

.ivu-alert.ivu-alert-with-icon {
    padding: 8px 48px 8px 38px
}

.ivu-alert-icon {
    font-size: 16px;
    top: 6px;
    left: 12px;
    position: absolute
}

.ivu-alert-desc {
    font-size: 14px;
    color: #515a6e;
    line-height: 21px;
    display: none;
    text-align: justify
}

.ivu-alert-success {
    border: 1px solid #8ce6b0;
    background-color: #edfff3
}

.ivu-alert-success .ivu-alert-icon {
    color: #19be6b
}

.ivu-alert-info {
    border: 1px solid #abdcff;
    background-color: #f0faff
}

.ivu-alert-info .ivu-alert-icon {
    color: #2d8cf0
}

.ivu-alert-warning {
    border: 1px solid #ffd77a;
    background-color: #fff9e6
}

.ivu-alert-warning .ivu-alert-icon {
    color: #f90
}

.ivu-alert-error {
    border: 1px solid #ffb08f;
    background-color: #ffefe6
}

.ivu-alert-error .ivu-alert-icon {
    color: #ed4014
}

.ivu-alert-close {
    font-size: 12px;
    position: absolute;
    right: 8px;
    top: 8px;
    overflow: hidden;
    cursor: pointer
}

.ivu-alert-close .ivu-icon-ios-close {
    font-size: 22px;
    color: #999;
    -webkit-transition: color .2s ease;
    transition: color .2s ease;
    position: relative;
    top: -3px
}

.ivu-alert-close .ivu-icon-ios-close:hover {
    color: #444
}

.ivu-alert-with-desc {
    padding: 16px;
    position: relative;
    border-radius: 4px;
    margin-bottom: 10px;
    color: #515a6e;
    line-height: 1.5
}

.ivu-alert-with-desc.ivu-alert-with-icon {
    padding: 16px 16px 16px 69px
}

.ivu-alert-with-desc .ivu-alert-desc {
    display: block
}

.ivu-alert-with-desc .ivu-alert-message {
    font-size: 16px;
    color: #17233d;
    display: block;
    margin-bottom: 4px
}

.ivu-alert-with-desc .ivu-alert-icon {
    top: 50%;
    left: 24px;
    margin-top: -24px;
    font-size: 28px
}

.ivu-alert-with-banner {
    border-radius: 0
}

.ivu-collapse {
    background-color: #f7f7f7;
    border-radius: 3px;
    border: 1px solid #dcdee2
}

.ivu-collapse-simple {
    border-left: none;
    border-right: none;
    background-color: #fff;
    border-radius: 0
}

.ivu-collapse>.ivu-collapse-item {
    border-top: 1px solid #dcdee2
}

.ivu-collapse>.ivu-collapse-item:first-child {
    border-top: 0
}

.ivu-collapse>.ivu-collapse-item>.ivu-collapse-header {
    height: 38px;
    line-height: 38px;
    padding-left: 16px;
    color: #666;
    cursor: pointer;
    position: relative;
    border-bottom: 1px solid transparent;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-collapse>.ivu-collapse-item>.ivu-collapse-header>i {
    -webkit-transition: -webkit-transform .2s ease-in-out;
    transition: -webkit-transform .2s ease-in-out;
    transition: transform .2s ease-in-out;
    transition: transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
    margin-right: 14px
}

.ivu-collapse>.ivu-collapse-item.ivu-collapse-item-active>.ivu-collapse-header {
    border-bottom: 1px solid #dcdee2
}

.ivu-collapse-simple>.ivu-collapse-item.ivu-collapse-item-active>.ivu-collapse-header {
    border-bottom: 1px solid transparent
}

.ivu-collapse>.ivu-collapse-item.ivu-collapse-item-active>.ivu-collapse-header>i {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg)
}

.ivu-collapse-content {
    color: #515a6e;
    padding: 0 16px;
    background-color: #fff
}

.ivu-collapse-content>.ivu-collapse-content-box {
    padding-top: 16px;
    padding-bottom: 16px
}

.ivu-collapse-simple>.ivu-collapse-item>.ivu-collapse-content>.ivu-collapse-content-box {
    padding-top: 0
}

.ivu-collapse-item:last-child>.ivu-collapse-content {
    border-radius: 0 0 3px 3px
}

.ivu-card {
    display: block;
    background: #fff;
    border-radius: 4px;
    font-size: 14px;
    position: relative;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-card-bordered {
    border: 1px solid #dcdee2;
    border-color: #e8eaec
}

.ivu-card-shadow {
    -webkit-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .1)
}

.ivu-card:hover {
    -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    border-color: #eee
}

.ivu-card.ivu-card-dis-hover:hover {
    -webkit-box-shadow: none;
    box-shadow: none;
    border-color: transparent
}

.ivu-card.ivu-card-dis-hover.ivu-card-bordered:hover {
    border-color: #e8eaec
}

.ivu-card.ivu-card-shadow:hover {
    -webkit-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .1)
}

.ivu-card-head {
    border-bottom: 1px solid #e8eaec;
    padding: 14px 16px;
    line-height: 1
}

.ivu-card-head p,
.ivu-card-head-inner {
    display: inline-block;
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 16px;
    color: #17233d;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ivu-card-extra {
    position: absolute;
    right: 16px;
    top: 14px
}

.ivu-card-body {
    padding: 16px
}

.ivu-message {
    font-size: 14px;
    position: fixed;
    z-index: 1010;
    width: 100%;
    top: 16px;
    left: 0;
    pointer-events: none
}

.ivu-message-notice {
    padding: 8px;
    text-align: center;
    -webkit-transition: height .3s ease-in-out, padding .3s ease-in-out;
    transition: height .3s ease-in-out, padding .3s ease-in-out
}

.ivu-message-notice:first-child {
    margin-top: -8px
}

.ivu-message-notice-close {
    position: absolute;
    right: 4px;
    top: 10px;
    color: #999;
    outline: 0
}

.ivu-message-notice-close i.ivu-icon {
    font-size: 22px;
    color: #999;
    -webkit-transition: color .2s ease;
    transition: color .2s ease;
    position: relative;
    top: -3px
}

.ivu-message-notice-close i.ivu-icon:hover {
    color: #444
}

.ivu-message-notice-content {
    display: inline-block;
    pointer-events: all;
    padding: 8px 16px;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    background: #fff;
    position: relative
}

.ivu-message-notice-content-text {
    display: inline-block
}

.ivu-message-notice-closable .ivu-message-notice-content-text {
    padding-right: 32px
}

.ivu-message-success .ivu-icon {
    color: #19be6b
}

.ivu-message-error .ivu-icon {
    color: #ed4014
}

.ivu-message-warning .ivu-icon {
    color: #f90
}

.ivu-message-info .ivu-icon,
.ivu-message-loading .ivu-icon {
    color: #2d8cf0
}

.ivu-message .ivu-icon {
    margin-right: 4px;
    font-size: 16px;
    vertical-align: middle
}

.ivu-message-custom-content span {
    vertical-align: middle
}

.ivu-message-notice-with-background .ivu-message-notice-content-background {
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-message-notice-with-background .ivu-message-notice-content-info {
    background: #f0faff;
    color: #2e8bf0;
    border: 1px solid #d4eeff
}

.ivu-message-notice-with-background .ivu-message-notice-content-success {
    background: #edfff3;
    color: #19bf6c;
    border: 1px solid #bbf2cf
}

.ivu-message-notice-with-background .ivu-message-notice-content-warning {
    background: #fff9e6;
    color: #f90;
    border: 1px solid #ffe7a3
}

.ivu-message-notice-with-background .ivu-message-notice-content-error {
    background: #ffefe6;
    color: #ed3f13;
    border: 1px solid #ffcfb8
}

.ivu-notice {
    width: 335px;
    margin-right: 24px;
    position: fixed;
    z-index: 1010
}

.ivu-notice-content-with-icon {
    margin-left: 51px
}

.ivu-notice-with-desc.ivu-notice-with-icon .ivu-notice-title {
    margin-left: 51px
}

.ivu-notice-notice {
    margin-bottom: 10px;
    padding: 16px;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    background: #fff;
    line-height: 1;
    position: relative;
    overflow: hidden
}

.ivu-notice-notice-close {
    position: absolute;
    right: 8px;
    top: 15px;
    color: #999;
    outline: 0
}

.ivu-notice-notice-close i {
    font-size: 22px;
    color: #999;
    -webkit-transition: color .2s ease;
    transition: color .2s ease;
    position: relative;
    top: -3px
}

.ivu-notice-notice-close i:hover {
    color: #444
}

.ivu-notice-notice-content-with-render .ivu-notice-desc {
    display: none
}

.ivu-notice-notice-with-desc .ivu-notice-notice-close {
    top: 11px
}

.ivu-notice-content-with-render-notitle {
    margin-left: 26px
}

.ivu-notice-title {
    font-size: 16px;
    line-height: 19px;
    color: #17233d;
    padding-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ivu-notice-with-desc .ivu-notice-title {
    margin-bottom: 8px
}

.ivu-notice-desc {
    font-size: 14px;
    color: #515a6e;
    text-align: justify;
    line-height: 1.5
}

.ivu-notice-with-desc.ivu-notice-with-icon .ivu-notice-desc {
    margin-left: 51px
}

.ivu-notice-with-icon .ivu-notice-title {
    margin-left: 26px
}

.ivu-notice-icon {
    position: absolute;
    top: -2px;
    font-size: 20px
}

.ivu-notice-icon-success {
    color: #19be6b
}

.ivu-notice-icon-info {
    color: #2d8cf0
}

.ivu-notice-icon-warning {
    color: #f90
}

.ivu-notice-icon-error {
    color: #ed4014
}

.ivu-notice-with-desc .ivu-notice-icon {
    font-size: 36px;
    top: -6px
}

.ivu-notice-custom-content {
    position: relative
}

.ivu-radio-focus {
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    z-index: 1
}

.ivu-radio-group {
    display: inline-block;
    font-size: 14px;
    vertical-align: middle
}

.ivu-radio-group-vertical .ivu-radio-wrapper {
    display: block;
    height: 30px;
    line-height: 30px
}

.ivu-radio-wrapper {
    font-size: 14px;
    vertical-align: middle;
    display: inline-block;
    position: relative;
    white-space: nowrap;
    margin-right: 8px;
    cursor: pointer
}

.ivu-radio-wrapper-disabled {
    cursor: not-allowed
}

.ivu-radio {
    display: inline-block;
    margin-right: 4px;
    white-space: nowrap;
    position: relative;
    line-height: 1;
    vertical-align: middle;
    cursor: pointer
}

.ivu-radio:hover .ivu-radio-inner {
    border-color: #bcbcbc
}

.ivu-radio-inner {
    display: inline-block;
    width: 16px;
    height: 16px;
    position: relative;
    top: 0;
    left: 0;
    background-color: #fff;
    border: 1px solid #dcdee2;
    border-radius: 50%;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-radio-inner:after {
    position: absolute;
    width: 10px;
    height: 10px;
    left: 2px;
    top: 2px;
    border-radius: 6px;
    display: table;
    border-top: 0;
    border-left: 0;
    content: ' ';
    background-color: #2d8cf0;
    opacity: 0;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0)
}

.ivu-radio-large {
    font-size: 16px
}

.ivu-radio-large .ivu-radio-inner {
    width: 18px;
    height: 18px
}

.ivu-radio-large .ivu-radio-inner:after {
    width: 12px;
    height: 12px
}

.ivu-radio-large .ivu-radio-wrapper,
.ivu-radio-large.ivu-radio-wrapper {
    font-size: 16px
}

.ivu-radio-small .ivu-radio-inner {
    width: 14px;
    height: 14px
}

.ivu-radio-small .ivu-radio-inner:after {
    width: 8px;
    height: 8px
}

.ivu-radio-input {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    opacity: 0;
    cursor: pointer
}

.ivu-radio-border {
    border: 1px solid #dcdee2;
    border-radius: 4px;
    height: 32px;
    line-height: 30px;
    padding: 0 15px;
    -webkit-transition: border .2s ease-in-out;
    transition: border .2s ease-in-out
}

.ivu-radio-group-small .ivu-radio-border,
.ivu-radio-small.ivu-radio-border {
    height: 24px;
    line-height: 22px;
    padding: 0 7px
}

.ivu-radio-group-large .ivu-radio-border,
.ivu-radio-large.ivu-radio-border {
    height: 40px;
    line-height: 36px;
    padding: 0 15px
}

.ivu-radio-wrapper-checked.ivu-radio-border {
    border-color: #2d8cf0
}

.ivu-radio-wrapper-disabled.ivu-radio-border {
    border-color: #dcdee2
}

.ivu-radio-checked .ivu-radio-inner {
    border-color: #2d8cf0
}

.ivu-radio-checked .ivu-radio-inner:after {
    opacity: 1;
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-radio-checked:hover .ivu-radio-inner {
    border-color: #2d8cf0
}

.ivu-radio-disabled {
    cursor: not-allowed
}

.ivu-radio-disabled .ivu-radio-input {
    cursor: not-allowed
}

.ivu-radio-disabled:hover .ivu-radio-inner {
    border-color: #dcdee2
}

.ivu-radio-disabled .ivu-radio-inner {
    border-color: #dcdee2;
    background-color: #f3f3f3
}

.ivu-radio-disabled .ivu-radio-inner:after {
    background-color: #ccc
}

.ivu-radio-disabled .ivu-radio-disabled+span {
    color: #ccc
}

span.ivu-radio+* {
    margin-left: 2px;
    margin-right: 2px
}

.ivu-radio-group-button {
    font-size: 0;
    -webkit-text-size-adjust: none
}

.ivu-radio-group-button .ivu-radio {
    width: 0;
    margin-right: 0
}

.ivu-radio-group-button .ivu-radio-wrapper {
    display: inline-block;
    height: 32px;
    line-height: 30px;
    margin: 0;
    padding: 0 15px;
    font-size: 14px;
    color: #515a6e;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    cursor: pointer;
    border: 1px solid #dcdee2;
    border-left: 0;
    background: #fff;
    position: relative
}

.ivu-radio-group-button .ivu-radio-wrapper>span {
    margin-left: 0
}

.ivu-radio-group-button .ivu-radio-wrapper:after,
.ivu-radio-group-button .ivu-radio-wrapper:before {
    content: '';
    display: block;
    position: absolute;
    width: 1px;
    height: 100%;
    left: -1px;
    top: 0;
    background: #dcdee2;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-radio-group-button .ivu-radio-wrapper:after {
    height: 36px;
    left: -1px;
    top: -3px;
    background: rgba(45, 140, 240, .2);
    opacity: 0
}

.ivu-radio-group-button .ivu-radio-wrapper:first-child {
    border-radius: 4px 0 0 4px;
    border-left: 1px solid #dcdee2
}

.ivu-radio-group-button .ivu-radio-wrapper:first-child:after,
.ivu-radio-group-button .ivu-radio-wrapper:first-child:before {
    display: none
}

.ivu-radio-group-button .ivu-radio-wrapper:last-child {
    border-radius: 0 4px 4px 0
}

.ivu-radio-group-button .ivu-radio-wrapper:first-child:last-child {
    border-radius: 4px
}

.ivu-radio-group-button .ivu-radio-wrapper:hover {
    position: relative;
    color: #2d8cf0
}

.ivu-radio-group-button .ivu-radio-wrapper:hover .ivu-radio {
    background-color: #000
}

.ivu-radio-group-button .ivu-radio-wrapper .ivu-radio-inner,
.ivu-radio-group-button .ivu-radio-wrapper input {
    opacity: 0;
    width: 0;
    height: 0
}

.ivu-radio-group-button .ivu-radio-wrapper-checked {
    background: #fff;
    border-color: #2d8cf0;
    color: #2d8cf0;
    -webkit-box-shadow: -1px 0 0 0 #2d8cf0;
    box-shadow: -1px 0 0 0 #2d8cf0;
    z-index: 1
}

.ivu-radio-group-button .ivu-radio-wrapper-checked:before {
    background: #2d8cf0;
    opacity: .1
}

.ivu-radio-group-button .ivu-radio-wrapper-checked.ivu-radio-focus {
    -webkit-box-shadow: -1px 0 0 0 #2d8cf0, 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: -1px 0 0 0 #2d8cf0, 0 0 0 2px rgba(45, 140, 240, .2);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-radio-group-button .ivu-radio-wrapper-checked.ivu-radio-focus:after {
    left: -3px;
    top: -3px;
    opacity: 1;
    background: rgba(45, 140, 240, .2)
}

.ivu-radio-group-button .ivu-radio-wrapper-checked.ivu-radio-focus:first-child {
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-radio-group-button .ivu-radio-wrapper-checked:first-child {
    border-color: #2d8cf0;
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-radio-group-button .ivu-radio-wrapper-checked:hover {
    border-color: #57a3f3;
    color: #57a3f3
}

.ivu-radio-group-button .ivu-radio-wrapper-checked:active {
    border-color: #2b85e4;
    color: #2b85e4
}

.ivu-radio-group-button .ivu-radio-wrapper-disabled {
    border-color: #dcdee2;
    background-color: #f7f7f7;
    cursor: not-allowed;
    color: #ccc
}

.ivu-radio-group-button .ivu-radio-wrapper-disabled:first-child,
.ivu-radio-group-button .ivu-radio-wrapper-disabled:hover {
    border-color: #dcdee2;
    background-color: #f7f7f7;
    color: #ccc
}

.ivu-radio-group-button .ivu-radio-wrapper-disabled:first-child {
    border-left-color: #dcdee2
}

.ivu-radio-group-button .ivu-radio-wrapper-disabled.ivu-radio-wrapper-checked {
    color: #fff;
    background-color: #e6e6e6;
    border-color: #dcdee2;
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.ivu-radio-group-button-solid .ivu-radio-wrapper-checked:not(.ivu-radio-wrapper-disabled) {
    background: #2d8cf0;
    color: #fff
}

.ivu-radio-group-button-solid .ivu-radio-wrapper-checked:not(.ivu-radio-wrapper-disabled):hover {
    background: #57a3f3;
    color: #fff
}

.ivu-radio-group-button.ivu-radio-group-large .ivu-radio-wrapper {
    height: 40px;
    line-height: 38px;
    font-size: 16px
}

.ivu-radio-group-button.ivu-radio-group-large .ivu-radio-wrapper:after {
    height: 44px
}

.ivu-radio-group-button.ivu-radio-group-small .ivu-radio-wrapper {
    height: 24px;
    line-height: 22px;
    padding: 0 12px;
    font-size: 14px
}

.ivu-radio-group-button.ivu-radio-group-small .ivu-radio-wrapper:after {
    height: 28px
}

.ivu-radio-group-button.ivu-radio-group-small .ivu-radio-wrapper:first-child {
    border-radius: 3px 0 0 3px
}

.ivu-radio-group-button.ivu-radio-group-small .ivu-radio-wrapper:last-child {
    border-radius: 0 3px 3px 0
}

.ivu-checkbox-focus {
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    z-index: 1
}

.ivu-checkbox {
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    cursor: pointer;
    line-height: 1;
    position: relative
}

.ivu-checkbox-disabled {
    cursor: not-allowed
}

.ivu-checkbox:hover .ivu-checkbox-inner {
    border-color: #bcbcbc
}

.ivu-checkbox-inner {
    display: inline-block;
    width: 16px;
    height: 16px;
    position: relative;
    top: 0;
    left: 0;
    border: 1px solid #dcdee2;
    border-radius: 2px;
    background-color: #fff;
    -webkit-transition: border-color .2s ease-in-out, background-color .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border-color .2s ease-in-out, background-color .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border-color .2s ease-in-out, background-color .2s ease-in-out, box-shadow .2s ease-in-out;
    transition: border-color .2s ease-in-out, background-color .2s ease-in-out, box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out
}

.ivu-checkbox-inner:after {
    content: '';
    display: table;
    width: 4px;
    height: 8px;
    position: absolute;
    top: 1px;
    left: 4px;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    -webkit-transform: rotate(45deg) scale(0);
    -ms-transform: rotate(45deg) scale(0);
    transform: rotate(45deg) scale(0);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-checkbox-large .ivu-checkbox-inner {
    width: 18px;
    height: 18px
}

.ivu-checkbox-large .ivu-checkbox-inner:after {
    width: 5px;
    height: 9px
}

.ivu-checkbox-small {
    font-size: 14px
}

.ivu-checkbox-small .ivu-checkbox-inner {
    width: 14px;
    height: 14px
}

.ivu-checkbox-small .ivu-checkbox-inner:after {
    top: 0;
    left: 3px
}

.ivu-checkbox-input {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    cursor: pointer;
    opacity: 0
}

.ivu-checkbox-input[disabled] {
    cursor: not-allowed
}

.ivu-checkbox-border {
    border: 1px solid #dcdee2;
    border-radius: 4px;
    height: 32px;
    line-height: 30px;
    padding: 0 15px;
    -webkit-transition: border .2s ease-in-out;
    transition: border .2s ease-in-out
}

.ivu-checkbox-group.ivu-checkbox-small .ivu-checkbox-border,
.ivu-checkbox-small.ivu-checkbox-border {
    height: 24px;
    line-height: 22px;
    padding: 0 7px
}

.ivu-checkbox-group.ivu-checkbox-large .ivu-checkbox-border,
.ivu-checkbox-large.ivu-checkbox-border {
    height: 40px;
    line-height: 36px;
    padding: 0 15px
}

.ivu-checkbox-wrapper-checked.ivu-checkbox-border {
    border-color: #2d8cf0
}

.ivu-checkbox-wrapper-disabled.ivu-checkbox-border {
    border-color: #dcdee2
}

.ivu-checkbox-checked:hover .ivu-checkbox-inner {
    border-color: #2d8cf0
}

.ivu-checkbox-checked .ivu-checkbox-inner {
    border-color: #2d8cf0;
    background-color: #2d8cf0
}

.ivu-checkbox-checked .ivu-checkbox-inner:after {
    content: '';
    display: table;
    width: 4px;
    height: 8px;
    position: absolute;
    top: 2px;
    left: 5px;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    -webkit-transform: rotate(45deg) scale(1);
    -ms-transform: rotate(45deg) scale(1);
    transform: rotate(45deg) scale(1);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-checkbox-large .ivu-checkbox-checked .ivu-checkbox-inner:after {
    width: 6px;
    height: 10px
}

.ivu-checkbox-small .ivu-checkbox-checked .ivu-checkbox-inner:after {
    top: 1px;
    left: 4px
}

.ivu-checkbox-disabled.ivu-checkbox-checked:hover .ivu-checkbox-inner {
    border-color: #dcdee2
}

.ivu-checkbox-disabled.ivu-checkbox-checked .ivu-checkbox-inner {
    background-color: #f3f3f3;
    border-color: #dcdee2
}

.ivu-checkbox-disabled.ivu-checkbox-checked .ivu-checkbox-inner:after {
    -webkit-animation-name: none;
    animation-name: none;
    border-color: #ccc
}

.ivu-checkbox-disabled:hover .ivu-checkbox-inner {
    border-color: #dcdee2
}

.ivu-checkbox-disabled .ivu-checkbox-inner {
    border-color: #dcdee2;
    background-color: #f3f3f3
}

.ivu-checkbox-disabled .ivu-checkbox-inner:after {
    -webkit-animation-name: none;
    animation-name: none;
    border-color: #f3f3f3
}

.ivu-checkbox-disabled .ivu-checkbox-inner-input {
    cursor: default
}

.ivu-checkbox-disabled+span {
    color: #ccc;
    cursor: not-allowed
}

.ivu-checkbox-indeterminate .ivu-checkbox-inner:after {
    content: '';
    width: 10px;
    height: 1px;
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
    position: absolute;
    left: 2px;
    top: 6px
}

.ivu-checkbox-indeterminate:hover .ivu-checkbox-inner {
    border-color: #2d8cf0
}

.ivu-checkbox-indeterminate .ivu-checkbox-inner {
    background-color: #2d8cf0;
    border-color: #2d8cf0
}

.ivu-checkbox-indeterminate.ivu-checkbox-disabled .ivu-checkbox-inner {
    background-color: #f3f3f3;
    border-color: #dcdee2
}

.ivu-checkbox-indeterminate.ivu-checkbox-disabled .ivu-checkbox-inner:after {
    border-color: #c5c8ce
}

.ivu-checkbox-large .ivu-checkbox-indeterminate .ivu-checkbox-inner:after {
    width: 12px;
    top: 7px
}

.ivu-checkbox-small .ivu-checkbox-indeterminate .ivu-checkbox-inner:after {
    width: 8px;
    top: 5px
}

.ivu-checkbox-wrapper {
    cursor: pointer;
    font-size: 14px;
    display: inline-block;
    margin-right: 8px
}

.ivu-checkbox-wrapper-disabled {
    cursor: not-allowed
}

.ivu-checkbox-wrapper.ivu-checkbox-large {
    font-size: 16px
}

.ivu-checkbox+span,
.ivu-checkbox-wrapper+span {
    margin-right: 4px
}

.ivu-checkbox-group {
    font-size: 14px
}

.ivu-checkbox-group-item {
    display: inline-block
}

.ivu-switch {
    display: inline-block;
    width: 44px;
    height: 22px;
    line-height: 20px;
    border-radius: 22px;
    vertical-align: middle;
    border: 1px solid #ccc;
    background-color: #ccc;
    position: relative;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-switch-loading {
    opacity: .4
}

.ivu-switch-inner {
    color: #fff;
    font-size: 12px;
    position: absolute;
    left: 23px
}

.ivu-switch-inner i {
    width: 12px;
    height: 12px;
    text-align: center;
    position: relative;
    top: -1px
}

.ivu-switch:after {
    content: '';
    width: 18px;
    height: 18px;
    border-radius: 18px;
    background-color: #fff;
    position: absolute;
    left: 1px;
    top: 1px;
    cursor: pointer;
    -webkit-transition: left .2s ease-in-out, width .2s ease-in-out;
    transition: left .2s ease-in-out, width .2s ease-in-out
}

.ivu-switch:active:after {
    width: 26px
}

.ivu-switch:before {
    content: '';
    display: none;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: transparent;
    position: absolute;
    left: 3px;
    top: 3px;
    z-index: 1;
    border: 1px solid #2d8cf0;
    border-color: transparent transparent transparent #2d8cf0;
    -webkit-animation: switch-loading 1s linear;
    animation: switch-loading 1s linear;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite
}

.ivu-switch-loading:before {
    display: block
}

.ivu-switch:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    outline: 0
}

.ivu-switch:focus:hover {
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-switch-small {
    width: 28px;
    height: 16px;
    line-height: 14px
}

.ivu-switch-small:after {
    width: 12px;
    height: 12px
}

.ivu-switch-small:active:after {
    width: 14px
}

.ivu-switch-small:before {
    width: 10px;
    height: 10px;
    left: 2px;
    top: 2px
}

.ivu-switch-small.ivu-switch-checked:after {
    left: 13px
}

.ivu-switch-small.ivu-switch-checked:before {
    left: 14px
}

.ivu-switch-small:active.ivu-switch-checked:after {
    left: 11px
}

.ivu-switch-large {
    width: 56px
}

.ivu-switch-large:active:after {
    width: 26px
}

.ivu-switch-large:active:after {
    width: 30px
}

.ivu-switch-large.ivu-switch-checked:after {
    left: 35px
}

.ivu-switch-large.ivu-switch-checked:before {
    left: 37px
}

.ivu-switch-large:active.ivu-switch-checked:after {
    left: 23px
}

.ivu-switch-checked {
    border-color: #2d8cf0;
    background-color: #2d8cf0
}

.ivu-switch-checked .ivu-switch-inner {
    left: 7px
}

.ivu-switch-checked:after {
    left: 23px
}

.ivu-switch-checked:before {
    left: 25px
}

.ivu-switch-checked:active:after {
    left: 15px
}

.ivu-switch-disabled {
    cursor: not-allowed;
    opacity: .4
}

.ivu-switch-disabled:after {
    background: #fff;
    cursor: not-allowed
}

.ivu-switch-disabled .ivu-switch-inner {
    color: #fff
}

.ivu-switch-disabled.ivu-switch-checked {
    border-color: #2d8cf0;
    background-color: #2d8cf0;
    opacity: .4
}

.ivu-switch-disabled.ivu-switch-checked:after {
    background: #fff
}

.ivu-switch-disabled.ivu-switch-checked .ivu-switch-inner {
    color: #fff
}

@-webkit-keyframes switch-loading {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes switch-loading {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

.ivu-input-number {
    display: inline-block;
    width: 100%;
    line-height: 1.5;
    padding: 4px 7px;
    font-size: 14px;
    color: #515a6e;
    background-color: #fff;
    background-image: none;
    position: relative;
    cursor: text;
    -webkit-transition: border .2s ease-in-out, background .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    margin: 0;
    padding: 0;
    width: 80px;
    height: 32px;
    line-height: 32px;
    vertical-align: middle;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    overflow: hidden;
    cursor: default
}

.ivu-input-number::-moz-placeholder {
    color: #c5c8ce;
    opacity: 1
}

.ivu-input-number:-ms-input-placeholder {
    color: #c5c8ce
}

.ivu-input-number::-webkit-input-placeholder {
    color: #c5c8ce
}

.ivu-input-number:hover {
    border-color: #57a3f3
}

.ivu-input-number:focus {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-input-number[disabled],
fieldset[disabled] .ivu-input-number {
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc
}

.ivu-input-number[disabled]:hover,
fieldset[disabled] .ivu-input-number:hover {
    border-color: #e3e5e8
}

textarea.ivu-input-number {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    vertical-align: bottom;
    font-size: 14px
}

.ivu-input-number-large {
    font-size: 16px;
    padding: 6px 7px;
    height: 40px
}

.ivu-input-number-small {
    padding: 1px 7px;
    height: 24px;
    border-radius: 3px
}

.ivu-input-number-no-border {
    border-radius: 0;
    border-color: transparent
}

.ivu-input-number-no-border:hover {
    border-color: transparent
}

.ivu-input-number-no-border:focus {
    border-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-input-number-no-border[disabled] {
    background-color: transparent
}

.ivu-input-number-no-border[disabled]:hover {
    border-color: transparent
}

.ivu-input-number-handler-wrap {
    width: 22px;
    height: 100%;
    border-left: 1px solid #dcdee2;
    border-radius: 0 4px 4px 0;
    background: #fff;
    position: absolute;
    top: 0;
    right: 0;
    opacity: 0;
    -webkit-transition: opacity .2s ease-in-out;
    transition: opacity .2s ease-in-out
}

.ivu-input-number:hover .ivu-input-number-handler-wrap {
    opacity: 1
}

.ivu-input-number-handler-up {
    cursor: pointer
}

.ivu-input-number-handler-up-inner {
    top: 1px
}

.ivu-input-number-handler-down {
    border-top: 1px solid #dcdee2;
    top: -1px;
    cursor: pointer
}

.ivu-input-number-handler {
    display: block;
    width: 100%;
    height: 16px;
    line-height: 0;
    text-align: center;
    overflow: hidden;
    color: #999;
    position: relative
}

.ivu-input-number-handler:hover .ivu-input-number-handler-down-inner,
.ivu-input-number-handler:hover .ivu-input-number-handler-up-inner {
    color: #57a3f3
}

.ivu-input-number-handler-down-inner,
.ivu-input-number-handler-up-inner {
    width: 12px;
    height: 12px;
    line-height: 12px;
    font-size: 14px;
    color: #999;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: absolute;
    right: 5px;
    -webkit-transition: all .2s linear;
    transition: all .2s linear
}

.ivu-input-number:hover {
    border-color: #57a3f3
}

.ivu-input-number-focused {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-input-number-disabled {
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc
}

.ivu-input-number-disabled:hover {
    border-color: #e3e5e8
}

.ivu-input-number-input-wrap {
    overflow: hidden;
    height: 32px
}

.ivu-input-number-input {
    width: 100%;
    height: 32px;
    line-height: 32px;
    padding: 0 7px;
    text-align: left;
    outline: 0;
    -moz-appearance: textfield;
    color: #666;
    border: 0;
    border-radius: 4px;
    -webkit-transition: all .2s linear;
    transition: all .2s linear
}

.ivu-input-number-input[disabled] {
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc
}

.ivu-input-number-input[disabled]:hover {
    border-color: #e3e5e8
}

.ivu-input-number-input::-webkit-input-placeholder {
    color: #c5c8ce
}

.ivu-input-number-input::-moz-placeholder {
    color: #c5c8ce
}

.ivu-input-number-input::-ms-input-placeholder {
    color: #c5c8ce
}

.ivu-input-number-input::placeholder {
    color: #c5c8ce
}

.ivu-input-number-large {
    padding: 0
}

.ivu-input-number-large .ivu-input-number-input-wrap {
    height: 40px
}

.ivu-input-number-large .ivu-input-number-handler {
    height: 20px
}

.ivu-input-number-large input {
    height: 40px;
    line-height: 40px
}

.ivu-input-number-large .ivu-input-number-handler-up-inner {
    top: 2px
}

.ivu-input-number-large .ivu-input-number-handler-down-inner {
    bottom: 2px
}

.ivu-input-number-small {
    padding: 0
}

.ivu-input-number-small .ivu-input-number-input-wrap {
    height: 24px
}

.ivu-input-number-small .ivu-input-number-handler {
    height: 12px
}

.ivu-input-number-small input {
    height: 24px;
    line-height: 24px;
    margin-top: -1px;
    vertical-align: top
}

.ivu-input-number-small .ivu-input-number-handler-up-inner {
    top: -1px
}

.ivu-input-number-small .ivu-input-number-handler-down-inner {
    bottom: -1px
}

.ivu-input-number-disabled .ivu-input-number-handler-down-inner,
.ivu-input-number-disabled .ivu-input-number-handler-up-inner,
.ivu-input-number-handler-down-disabled .ivu-input-number-handler-down-inner,
.ivu-input-number-handler-down-disabled .ivu-input-number-handler-up-inner,
.ivu-input-number-handler-up-disabled .ivu-input-number-handler-down-inner,
.ivu-input-number-handler-up-disabled .ivu-input-number-handler-up-inner {
    opacity: .72;
    color: #ccc !important;
    cursor: not-allowed
}

.ivu-input-number-disabled .ivu-input-number-input {
    opacity: .72;
    cursor: not-allowed;
    background-color: #f3f3f3
}

.ivu-input-number-disabled .ivu-input-number-handler-wrap {
    display: none
}

.ivu-input-number-disabled .ivu-input-number-handler {
    opacity: .72;
    color: #ccc !important;
    cursor: not-allowed
}

.ivu-input-number-controls-outside {
    width: 144px;
    padding: 0 32px
}

.ivu-input-number-controls-outside .ivu-input-number-input {
    border-radius: 0
}

.ivu-input-number-controls-outside-btn {
    display: inline-block;
    width: 32px;
    height: 32px;
    line-height: 30px;
    position: absolute;
    top: 0;
    text-align: center;
    background-color: #f8f8f9;
    color: #515a6e;
    cursor: pointer
}

.ivu-input-number-controls-outside-btn i {
    font-size: 16px
}

.ivu-input-number-controls-outside-btn:hover i {
    color: #2d8cf0
}

.ivu-input-number-controls-outside-btn-disabled,
.ivu-input-number-controls-outside-btn-disabled:hover {
    cursor: not-allowed
}

.ivu-input-number-controls-outside-btn-disabled i,
.ivu-input-number-controls-outside-btn-disabled:hover i {
    color: #ccc
}

.ivu-input-number-controls-outside-up {
    right: 0;
    border-left: 1px solid #dcdee2
}

.ivu-input-number-controls-outside-down {
    left: 0;
    border-right: 1px solid #dcdee2
}

.ivu-input-number-disabled.ivu-input-number-controls-outside .ivu-input-number-controls-outside-btn {
    cursor: not-allowed
}

.ivu-input-number-disabled.ivu-input-number-controls-outside .ivu-input-number-controls-outside-btn i {
    color: #ccc
}

.ivu-input-number-large.ivu-input-number-controls-outside {
    width: 160px;
    padding: 0 40px
}

.ivu-input-number-large.ivu-input-number-controls-outside .ivu-input-number-controls-outside-btn {
    width: 40px;
    height: 40px;
    line-height: 38px
}

.ivu-input-number-large.ivu-input-number-controls-outside .ivu-input-number-controls-outside-btn i {
    font-size: 20px
}

.ivu-input-number-small.ivu-input-number-controls-outside {
    width: 128px;
    padding: 0 24px
}

.ivu-input-number-small.ivu-input-number-controls-outside .ivu-input-number-controls-outside-btn {
    width: 24px;
    height: 24px;
    line-height: 22px
}

.ivu-input-number-small.ivu-input-number-controls-outside .ivu-input-number-controls-outside-btn i {
    font-size: 14px
}

.ivu-form-item-error .ivu-input-number {
    border: 1px solid #ed4014
}

.ivu-form-item-error .ivu-input-number:hover {
    border-color: #ed4014
}

.ivu-form-item-error .ivu-input-number:focus {
    border-color: #ed4014;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(237, 64, 20, .2);
    box-shadow: 0 0 0 2px rgba(237, 64, 20, .2)
}

.ivu-form-item-error .ivu-input-number-focused {
    border-color: #ed4014;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(237, 64, 20, .2);
    box-shadow: 0 0 0 2px rgba(237, 64, 20, .2)
}

.ivu-scroll-wrapper {
    width: auto;
    margin: 0 auto;
    position: relative;
    outline: 0
}

.ivu-scroll-container {
    overflow-y: scroll
}

@-webkit-keyframes ani-stop-slide {
    from {
        overflow-y: hidden;
        tansform: translateZ(0)
    }

    to {
        overflow-y: scroll;
        tansform: translateZ(0)
    }
}

@keyframes ani-stop-slide {
    from {
        overflow-y: hidden;
        tansform: translateZ(0)
    }

    to {
        overflow-y: scroll;
        tansform: translateZ(0)
    }
}

.ivu-scroll-container-loading {
    -webkit-animation: ani-stop-slide 1.5s;
    animation: ani-stop-slide 1.5s
}

.ivu-scroll-content {
    opacity: 1;
    -webkit-transition: opacity .5s;
    transition: opacity .5s
}

.ivu-scroll-content-loading {
    opacity: .5
}

.ivu-scroll-loader {
    text-align: center;
    padding: 0;
    -webkit-transition: padding .5s;
    transition: padding .5s
}

.ivu-scroll-loader-wrapper {
    padding: 5px 0;
    height: 0;
    background-color: inherit;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
    -webkit-transition: opacity .3s, height .5s, -webkit-transform .5s;
    transition: opacity .3s, height .5s, -webkit-transform .5s;
    transition: opacity .3s, transform .5s, height .5s;
    transition: opacity .3s, transform .5s, height .5s, -webkit-transform .5s
}

.ivu-scroll-loader-wrapper-active {
    height: 40px;
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1)
}

@-webkit-keyframes ani-demo-spin {
    from {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    50% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg)
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes ani-demo-spin {
    from {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    50% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg)
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

.ivu-scroll-loader-wrapper .ivu-scroll-spinner {
    position: relative
}

.ivu-scroll-loader-wrapper .ivu-scroll-spinner-icon {
    -webkit-animation: ani-demo-spin 1s linear infinite;
    animation: ani-demo-spin 1s linear infinite
}

.ivu-tag {
    display: inline-block;
    height: 22px;
    line-height: 22px;
    margin: 2px 4px 2px 0;
    padding: 0 8px;
    border: 1px solid #e8eaec;
    border-radius: 3px;
    background: #f7f7f7;
    font-size: 12px;
    vertical-align: middle;
    opacity: 1;
    overflow: hidden
}

.ivu-tag-size-large {
    height: 32px;
    line-height: 32px;
    padding: 0 12px
}

.ivu-tag-size-medium {
    height: 28px;
    line-height: 28px;
    padding: 0 10px
}

.ivu-tag:not(.ivu-tag-border):not(.ivu-tag-dot):not(.ivu-tag-checked) {
    background: 0 0;
    border: 0;
    color: #515a6e
}

.ivu-tag:not(.ivu-tag-border):not(.ivu-tag-dot):not(.ivu-tag-checked) .ivu-icon-ios-close {
    color: #515a6e !important
}

.ivu-tag-color-error {
    color: #ed4014 !important;
    border-color: #ed4014
}

.ivu-tag-color-success {
    color: #19be6b !important;
    border-color: #19be6b
}

.ivu-tag-color-primary {
    color: #2d8cf0 !important;
    border-color: #2d8cf0
}

.ivu-tag-color-warning {
    color: #f90 !important;
    border-color: #f90
}

.ivu-tag-color-white {
    color: #fff !important
}

.ivu-tag-dot {
    height: 32px;
    line-height: 32px;
    border: 1px solid #e8eaec !important;
    color: #515a6e !important;
    background: #fff !important;
    padding: 0 12px
}

.ivu-tag-dot-inner {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 50%;
    background: #e8eaec;
    position: relative;
    top: 1px
}

.ivu-tag-dot .ivu-icon-ios-close {
    color: #666 !important;
    margin-left: 12px !important
}

.ivu-tag-border {
    height: 24px;
    line-height: 24px;
    border: 1px solid #e8eaec;
    color: #e8eaec;
    background: #fff !important;
    position: relative
}

.ivu-tag-border .ivu-icon-ios-close {
    color: #666;
    margin-left: 12px !important
}

.ivu-tag-border:after {
    content: "";
    display: none;
    width: 1px;
    background: currentColor;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 22px
}

.ivu-tag-border.ivu-tag-closable:after {
    display: block
}

.ivu-tag-border.ivu-tag-closable .ivu-icon-ios-close {
    margin-left: 18px !important;
    left: 4px;
    top: -1px
}

.ivu-tag-border.ivu-tag-primary {
    color: #2d8cf0 !important;
    border: 1px solid #2d8cf0 !important
}

.ivu-tag-border.ivu-tag-primary:after {
    background: #2d8cf0
}

.ivu-tag-border.ivu-tag-primary .ivu-icon-ios-close {
    color: #2d8cf0 !important
}

.ivu-tag-border.ivu-tag-success {
    color: #19be6b !important;
    border: 1px solid #19be6b !important
}

.ivu-tag-border.ivu-tag-success:after {
    background: #19be6b
}

.ivu-tag-border.ivu-tag-success .ivu-icon-ios-close {
    color: #19be6b !important
}

.ivu-tag-border.ivu-tag-warning {
    color: #f90 !important;
    border: 1px solid #f90 !important
}

.ivu-tag-border.ivu-tag-warning:after {
    background: #f90
}

.ivu-tag-border.ivu-tag-warning .ivu-icon-ios-close {
    color: #f90 !important
}

.ivu-tag-border.ivu-tag-error {
    color: #ed4014 !important;
    border: 1px solid #ed4014 !important
}

.ivu-tag-border.ivu-tag-error:after {
    background: #ed4014
}

.ivu-tag-border.ivu-tag-error .ivu-icon-ios-close {
    color: #ed4014 !important
}

.ivu-tag:hover {
    opacity: .85
}

.ivu-tag-text {
    color: #515a6e
}

.ivu-tag-text a:first-child:last-child {
    display: inline-block;
    margin: 0 -8px;
    padding: 0 8px
}

.ivu-tag .ivu-icon-ios-close {
    display: inline-block;
    font-size: 14px;
    -webkit-transform: scale(1.42857143) rotate(0);
    -ms-transform: scale(1.42857143) rotate(0);
    transform: scale(1.42857143) rotate(0);
    cursor: pointer;
    margin-left: 2px;
    color: #666;
    opacity: .66;
    position: relative;
    top: -1px
}

:root .ivu-tag .ivu-icon-ios-close {
    font-size: 14px
}

.ivu-tag .ivu-icon-ios-close:hover {
    opacity: 1
}

.ivu-tag-error,
.ivu-tag-primary,
.ivu-tag-success,
.ivu-tag-warning {
    border: 0
}

.ivu-tag-error,
.ivu-tag-error .ivu-icon-ios-close,
.ivu-tag-error .ivu-icon-ios-close:hover,
.ivu-tag-error a,
.ivu-tag-error a:hover,
.ivu-tag-primary,
.ivu-tag-primary .ivu-icon-ios-close,
.ivu-tag-primary .ivu-icon-ios-close:hover,
.ivu-tag-primary a,
.ivu-tag-primary a:hover,
.ivu-tag-success,
.ivu-tag-success .ivu-icon-ios-close,
.ivu-tag-success .ivu-icon-ios-close:hover,
.ivu-tag-success a,
.ivu-tag-success a:hover,
.ivu-tag-warning,
.ivu-tag-warning .ivu-icon-ios-close,
.ivu-tag-warning .ivu-icon-ios-close:hover,
.ivu-tag-warning a,
.ivu-tag-warning a:hover {
    color: #fff
}

.ivu-tag-primary,
.ivu-tag-primary.ivu-tag-dot .ivu-tag-dot-inner {
    background: #2d8cf0
}

.ivu-tag-success,
.ivu-tag-success.ivu-tag-dot .ivu-tag-dot-inner {
    background: #19be6b
}

.ivu-tag-warning,
.ivu-tag-warning.ivu-tag-dot .ivu-tag-dot-inner {
    background: #f90
}

.ivu-tag-error,
.ivu-tag-error.ivu-tag-dot .ivu-tag-dot-inner {
    background: #ed4014
}

.ivu-tag-pink {
    line-height: 20px;
    background: #fff0f6;
    border-color: #ffadd2
}

.ivu-tag-pink .ivu-tag-text {
    color: #eb2f96 !important
}

.ivu-tag-pink.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-pink {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-pink {
    line-height: 26px
}

.ivu-tag-magenta {
    line-height: 20px;
    background: #fff0f6;
    border-color: #ffadd2
}

.ivu-tag-magenta .ivu-tag-text {
    color: #eb2f96 !important
}

.ivu-tag-magenta.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-magenta {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-magenta {
    line-height: 26px
}

.ivu-tag-red {
    line-height: 20px;
    background: #fff1f0;
    border-color: #ffa39e
}

.ivu-tag-red .ivu-tag-text {
    color: #f5222d !important
}

.ivu-tag-red.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-red {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-red {
    line-height: 26px
}

.ivu-tag-volcano {
    line-height: 20px;
    background: #fff2e8;
    border-color: #ffbb96
}

.ivu-tag-volcano .ivu-tag-text {
    color: #fa541c !important
}

.ivu-tag-volcano.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-volcano {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-volcano {
    line-height: 26px
}

.ivu-tag-orange {
    line-height: 20px;
    background: #fff7e6;
    border-color: #ffd591
}

.ivu-tag-orange .ivu-tag-text {
    color: #fa8c16 !important
}

.ivu-tag-orange.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-orange {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-orange {
    line-height: 26px
}

.ivu-tag-yellow {
    line-height: 20px;
    background: #feffe6;
    border-color: #fffb8f
}

.ivu-tag-yellow .ivu-tag-text {
    color: #fadb14 !important
}

.ivu-tag-yellow.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-yellow {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-yellow {
    line-height: 26px
}

.ivu-tag-gold {
    line-height: 20px;
    background: #fffbe6;
    border-color: #ffe58f
}

.ivu-tag-gold .ivu-tag-text {
    color: #faad14 !important
}

.ivu-tag-gold.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-gold {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-gold {
    line-height: 26px
}

.ivu-tag-cyan {
    line-height: 20px;
    background: #e6fffb;
    border-color: #87e8de
}

.ivu-tag-cyan .ivu-tag-text {
    color: #13c2c2 !important
}

.ivu-tag-cyan.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-cyan {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-cyan {
    line-height: 26px
}

.ivu-tag-lime {
    line-height: 20px;
    background: #fcffe6;
    border-color: #eaff8f
}

.ivu-tag-lime .ivu-tag-text {
    color: #a0d911 !important
}

.ivu-tag-lime.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-lime {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-lime {
    line-height: 26px
}

.ivu-tag-green {
    line-height: 20px;
    background: #f6ffed;
    border-color: #b7eb8f
}

.ivu-tag-green .ivu-tag-text {
    color: #52c41a !important
}

.ivu-tag-green.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-green {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-green {
    line-height: 26px
}

.ivu-tag-blue {
    line-height: 20px;
    background: #e6f7ff;
    border-color: #91d5ff
}

.ivu-tag-blue .ivu-tag-text {
    color: #1890ff !important
}

.ivu-tag-blue.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-blue {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-blue {
    line-height: 26px
}

.ivu-tag-geekblue {
    line-height: 20px;
    background: #f0f5ff;
    border-color: #adc6ff
}

.ivu-tag-geekblue .ivu-tag-text {
    color: #2f54eb !important
}

.ivu-tag-geekblue.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-geekblue {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-geekblue {
    line-height: 26px
}

.ivu-tag-purple {
    line-height: 20px;
    background: #f9f0ff;
    border-color: #d3adf7
}

.ivu-tag-purple .ivu-tag-text {
    color: #722ed1 !important
}

.ivu-tag-purple.ivu-tag-dot {
    line-height: 32px
}

.ivu-tag-size-large.ivu-tag-purple {
    line-height: 30px
}

.ivu-tag-size-medium.ivu-tag-purple {
    line-height: 26px
}

.ivu-layout {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto;
    background: #f5f7f9
}

.ivu-layout.ivu-layout-has-sider {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
}

.ivu-layout.ivu-layout-has-sider>.ivu-layout,
.ivu-layout.ivu-layout-has-sider>.ivu-layout-content {
    overflow-x: hidden
}

.ivu-layout-footer,
.ivu-layout-header {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto
}

.ivu-layout-header {
    background: #515a6e;
    padding: 0 50px;
    height: 64px;
    line-height: 64px
}

.ivu-layout-sider {
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    position: relative;
    background: #515a6e;
    min-width: 0
}

.ivu-layout-sider-children {
    height: 100%;
    padding-top: .1px;
    margin-top: -.1px
}

.ivu-layout-sider-has-trigger {
    padding-bottom: 48px
}

.ivu-layout-sider-trigger {
    position: fixed;
    bottom: 0;
    text-align: center;
    cursor: pointer;
    height: 48px;
    line-height: 48px;
    color: #fff;
    background: #515a6e;
    z-index: 1000;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-layout-sider-trigger .ivu-icon {
    font-size: 16px
}

.ivu-layout-sider-trigger>* {
    -webkit-transition: all .2s;
    transition: all .2s
}

.ivu-layout-sider-trigger-collapsed .ivu-layout-sider-trigger-icon {
    -webkit-transform: rotateZ(180deg);
    -ms-transform: rotate(180deg);
    transform: rotateZ(180deg)
}

.ivu-layout-sider-zero-width>* {
    overflow: hidden
}

.ivu-layout-sider-zero-width-trigger {
    position: absolute;
    top: 64px;
    right: -36px;
    text-align: center;
    width: 36px;
    height: 42px;
    line-height: 42px;
    background: #515a6e;
    color: #fff;
    font-size: 18px;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    -webkit-transition: background .3s ease;
    transition: background .3s ease
}

.ivu-layout-sider-zero-width-trigger:hover {
    background: #626b7d
}

.ivu-layout-sider-zero-width-trigger.ivu-layout-sider-zero-width-trigger-left {
    right: 0;
    left: -36px;
    border-radius: 6px 0 0 6px
}

.ivu-layout-footer {
    background: #f5f7f9;
    padding: 24px 50px;
    color: #515a6e;
    font-size: 14px
}

.ivu-layout-content {
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto
}

.ivu-loading-bar {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 2000
}

.ivu-loading-bar-inner {
    -webkit-transition: width .2s linear;
    transition: width .2s linear
}

.ivu-loading-bar-inner-color-primary {
    background-color: #2d8cf0
}

.ivu-loading-bar-inner-failed-color-error {
    background-color: #ed4014
}

.ivu-progress {
    display: inline-block;
    width: 100%;
    font-size: 12px;
    position: relative
}

.ivu-progress-vertical {
    height: 100%;
    width: auto
}

.ivu-progress-outer {
    display: inline-block;
    width: 100%;
    margin-right: 0;
    padding-right: 0
}

.ivu-progress-show-info .ivu-progress-outer {
    padding-right: 55px;
    margin-right: -55px
}

.ivu-progress-vertical .ivu-progress-outer {
    height: 100%;
    width: auto
}

.ivu-progress-inner {
    display: inline-block;
    width: 100%;
    background-color: #f3f3f3;
    border-radius: 100px;
    vertical-align: middle;
    position: relative
}

.ivu-progress-inner-text {
    display: inline-block;
    vertical-align: middle;
    color: #fff;
    font-size: 12px;
    margin: 0 6px
}

.ivu-progress-vertical .ivu-progress-inner {
    height: 100%;
    width: auto
}

.ivu-progress-vertical .ivu-progress-inner:after,
.ivu-progress-vertical .ivu-progress-inner>* {
    display: inline-block;
    vertical-align: bottom
}

.ivu-progress-vertical .ivu-progress-inner:after {
    content: '';
    height: 100%
}

.ivu-progress-bg {
    text-align: right;
    border-radius: 100px;
    background-color: #2d8cf0;
    -webkit-transition: all .2s linear;
    transition: all .2s linear;
    position: relative
}

.ivu-progress-bg:after {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle
}

.ivu-progress-success-bg {
    border-radius: 100px;
    background-color: #19be6b;
    -webkit-transition: all .2s linear;
    transition: all .2s linear;
    position: absolute;
    top: 0;
    left: 0
}

.ivu-progress-text {
    display: inline-block;
    margin-left: 5px;
    text-align: left;
    font-size: 1em;
    vertical-align: middle;
    color: #808695
}

.ivu-progress-active .ivu-progress-bg:before {
    content: '';
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    border-radius: 10px;
    -webkit-animation: ivu-progress-active 2s ease-in-out infinite;
    animation: ivu-progress-active 2s ease-in-out infinite
}

.ivu-progress-vertical.ivu-progress-active .ivu-progress-bg:before {
    top: auto;
    -webkit-animation: ivu-progress-active-vertical 2s ease-in-out infinite;
    animation: ivu-progress-active-vertical 2s ease-in-out infinite
}

.ivu-progress-wrong .ivu-progress-bg {
    background-color: #ed4014
}

.ivu-progress-wrong .ivu-progress-text {
    color: #ed4014
}

.ivu-progress-success .ivu-progress-bg {
    background-color: #19be6b
}

.ivu-progress-success .ivu-progress-text {
    color: #19be6b
}

@-webkit-keyframes ivu-progress-active {
    0% {
        opacity: .3;
        width: 0
    }

    100% {
        opacity: 0;
        width: 100%
    }
}

@keyframes ivu-progress-active {
    0% {
        opacity: .3;
        width: 0
    }

    100% {
        opacity: 0;
        width: 100%
    }
}

@-webkit-keyframes ivu-progress-active-vertical {
    0% {
        opacity: .3;
        height: 0
    }

    100% {
        opacity: 0;
        height: 100%
    }
}

@keyframes ivu-progress-active-vertical {
    0% {
        opacity: .3;
        height: 0
    }

    100% {
        opacity: 0;
        height: 100%
    }
}

.ivu-timeline {
    list-style: none;
    margin: 0;
    padding: 0
}

.ivu-timeline-item {
    margin: 0 !important;
    padding: 0 0 12px 0;
    list-style: none;
    position: relative
}

.ivu-timeline-item-tail {
    height: 100%;
    border-left: 1px solid #e8eaec;
    position: absolute;
    left: 6px;
    top: 0
}

.ivu-timeline-item-pending .ivu-timeline-item-tail {
    display: none
}

.ivu-timeline-item-head {
    width: 13px;
    height: 13px;
    background-color: #fff;
    border-radius: 50%;
    border: 1px solid transparent;
    position: absolute
}

.ivu-timeline-item-head-blue {
    border-color: #2d8cf0;
    color: #2d8cf0
}

.ivu-timeline-item-head-red {
    border-color: #ed4014;
    color: #ed4014
}

.ivu-timeline-item-head-green {
    border-color: #19be6b;
    color: #19be6b
}

.ivu-timeline-item-head-custom {
    width: 40px;
    height: auto;
    margin-top: 6px;
    padding: 3px 0;
    text-align: center;
    line-height: 1;
    border: 0;
    border-radius: 0;
    font-size: 14px;
    position: absolute;
    left: -13px;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%)
}

.ivu-timeline-item-content {
    padding: 1px 1px 10px 24px;
    font-size: 14px;
    position: relative;
    top: -3px
}

.ivu-timeline-item:last-child .ivu-timeline-item-tail {
    display: none
}

.ivu-timeline.ivu-timeline-pending .ivu-timeline-item:nth-last-of-type(2) .ivu-timeline-item-tail {
    border-left: 1px dotted #e8eaec
}

.ivu-timeline.ivu-timeline-pending .ivu-timeline-item:nth-last-of-type(2) .ivu-timeline-item-content {
    min-height: 48px
}

.ivu-page:after {
    content: '';
    display: block;
    height: 0;
    clear: both;
    overflow: hidden;
    visibility: hidden
}

.ivu-page-item {
    display: inline-block;
    vertical-align: middle;
    min-width: 32px;
    height: 32px;
    line-height: 30px;
    margin-right: 4px;
    text-align: center;
    list-style: none;
    background-color: #fff;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
    font-family: Arial;
    font-weight: 500;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    -webkit-transition: border .2s ease-in-out, color .2s ease-in-out;
    transition: border .2s ease-in-out, color .2s ease-in-out
}

.ivu-page-item a {
    margin: 0 6px;
    text-decoration: none;
    color: #515a6e
}

.ivu-page-item:hover {
    border-color: #2d8cf0
}

.ivu-page-item:hover a {
    color: #2d8cf0
}

.ivu-page-item-active {
    border-color: #2d8cf0
}

.ivu-page-item-active a,
.ivu-page-item-active:hover a {
    color: #2d8cf0
}

.ivu-page-with-disabled .ivu-page-disabled,
.ivu-page-with-disabled .ivu-page-item {
    cursor: not-allowed;
    background-color: #f3f3f3
}

.ivu-page-with-disabled .ivu-page-disabled a,
.ivu-page-with-disabled .ivu-page-item a {
    color: #ccc
}

.ivu-page-with-disabled .ivu-page-disabled:hover,
.ivu-page-with-disabled .ivu-page-item:hover {
    border-color: #dcdee2
}

.ivu-page-with-disabled .ivu-page-disabled:hover a,
.ivu-page-with-disabled .ivu-page-item:hover a {
    color: #ccc;
    cursor: not-allowed
}

.ivu-page-with-disabled .ivu-page-disabled-active,
.ivu-page-with-disabled .ivu-page-item-active {
    background-color: #dcdee2;
    border-color: #dcdee2
}

.ivu-page-with-disabled .ivu-page-disabled-active a,
.ivu-page-with-disabled .ivu-page-disabled-active:hover a,
.ivu-page-with-disabled .ivu-page-item-active a,
.ivu-page-with-disabled .ivu-page-item-active:hover a {
    color: #fff
}

.ivu-page-item-jump-next i,
.ivu-page-item-jump-prev i {
    color: #ccc
}

.ivu-page-item-jump-next i:first-child,
.ivu-page-item-jump-prev i:first-child {
    display: none
}

.ivu-page-item-jump-next:hover i:first-child,
.ivu-page-item-jump-prev:hover i:first-child {
    display: inline;
    color: #2d8cf0
}

.ivu-page-item-jump-next:hover i:last-child,
.ivu-page-item-jump-prev:hover i:last-child {
    display: none
}

.ivu-page-with-disabled .ivu-page-item-jump-next,
.ivu-page-with-disabled .ivu-page-item-jump-prev {
    cursor: not-allowed
}

.ivu-page-with-disabled .ivu-page-item-jump-next i,
.ivu-page-with-disabled .ivu-page-item-jump-prev i {
    color: #ccc
}

.ivu-page-with-disabled .ivu-page-item-jump-next:hover i:first-child,
.ivu-page-with-disabled .ivu-page-item-jump-prev:hover i:first-child {
    display: none
}

.ivu-page-with-disabled .ivu-page-item-jump-next:hover i:last-child,
.ivu-page-with-disabled .ivu-page-item-jump-prev:hover i:last-child {
    display: inline
}

.ivu-page-item-jump-prev:hover i:first-child:after {
    content: "\F115";
    margin-left: -8px
}

.ivu-page-item-jump-next:hover i:first-child:after {
    content: "\F11F";
    margin-left: -8px
}

.ivu-page-prev {
    margin-right: 4px
}

.ivu-page-item-jump-next,
.ivu-page-item-jump-prev {
    margin-right: 4px
}

.ivu-page-item-jump-next,
.ivu-page-item-jump-prev,
.ivu-page-next,
.ivu-page-prev {
    display: inline-block;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    min-width: 32px;
    height: 32px;
    line-height: 30px;
    list-style: none;
    text-align: center;
    cursor: pointer;
    color: #666;
    font-family: Arial;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-page-item-jump-next,
.ivu-page-item-jump-prev {
    border-color: transparent
}

.ivu-page-next,
.ivu-page-prev {
    background-color: #fff
}

.ivu-page-next a,
.ivu-page-prev a {
    color: #666;
    font-size: 14px
}

.ivu-page-next:hover,
.ivu-page-prev:hover {
    border-color: #2d8cf0
}

.ivu-page-next:hover a,
.ivu-page-prev:hover a {
    color: #2d8cf0
}

.ivu-page-disabled {
    cursor: not-allowed
}

.ivu-page-disabled a {
    color: #ccc
}

.ivu-page-disabled:hover {
    border-color: #dcdee2
}

.ivu-page-disabled:hover a {
    color: #ccc;
    cursor: not-allowed
}

.ivu-page-options {
    display: inline-block;
    vertical-align: middle;
    margin-left: 15px
}

.ivu-page-options-sizer {
    display: inline-block;
    margin-right: 10px
}

.ivu-page-options-elevator {
    display: inline-block;
    vertical-align: middle;
    height: 32px;
    line-height: 32px
}

.ivu-page-options-elevator input {
    display: inline-block;
    width: 100%;
    height: 32px;
    line-height: 1.5;
    padding: 4px 7px;
    font-size: 14px;
    border: 1px solid #dcdee2;
    color: #515a6e;
    background-color: #fff;
    background-image: none;
    position: relative;
    cursor: text;
    -webkit-transition: border .2s ease-in-out, background .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    border-radius: 4px;
    margin: 0 8px;
    width: 50px
}

.ivu-page-options-elevator input::-moz-placeholder {
    color: #c5c8ce;
    opacity: 1
}

.ivu-page-options-elevator input:-ms-input-placeholder {
    color: #c5c8ce
}

.ivu-page-options-elevator input::-webkit-input-placeholder {
    color: #c5c8ce
}

.ivu-page-options-elevator input:hover {
    border-color: #57a3f3
}

.ivu-page-options-elevator input:focus {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-page-options-elevator input[disabled],
fieldset[disabled] .ivu-page-options-elevator input {
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc
}

.ivu-page-options-elevator input[disabled]:hover,
fieldset[disabled] .ivu-page-options-elevator input:hover {
    border-color: #e3e5e8
}

textarea.ivu-page-options-elevator input {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    vertical-align: bottom;
    font-size: 14px
}

.ivu-page-options-elevator input-large {
    font-size: 16px;
    padding: 6px 7px;
    height: 40px
}

.ivu-page-options-elevator input-small {
    padding: 1px 7px;
    height: 24px;
    border-radius: 3px
}

.ivu-page-options-elevator input-no-border {
    border-radius: 0;
    border-color: transparent
}

.ivu-page-options-elevator input-no-border:hover {
    border-color: transparent
}

.ivu-page-options-elevator input-no-border:focus {
    border-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-page-options-elevator input-no-border[disabled] {
    background-color: transparent
}

.ivu-page-options-elevator input-no-border[disabled]:hover {
    border-color: transparent
}

.ivu-page-total {
    display: inline-block;
    height: 32px;
    line-height: 32px;
    margin-right: 10px
}

.ivu-page-simple .ivu-page-next,
.ivu-page-simple .ivu-page-prev {
    margin: 0;
    border: 0;
    height: 24px;
    line-height: normal;
    font-size: 18px
}

.ivu-page-simple .ivu-page-simple-pager {
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle
}

.ivu-page-simple .ivu-page-simple-pager input {
    display: inline-block;
    width: 100%;
    height: 32px;
    line-height: 1.5;
    padding: 4px 7px;
    font-size: 14px;
    color: #515a6e;
    background-image: none;
    position: relative;
    cursor: text;
    -webkit-transition: border .2s ease-in-out, background .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    width: 30px;
    height: 24px;
    margin: 0 8px;
    padding: 5px 8px;
    text-align: center;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background-color: #fff;
    outline: 0;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    -webkit-transition: border-color .2s ease-in-out;
    transition: border-color .2s ease-in-out
}

.ivu-page-simple .ivu-page-simple-pager input::-moz-placeholder {
    color: #c5c8ce;
    opacity: 1
}

.ivu-page-simple .ivu-page-simple-pager input:-ms-input-placeholder {
    color: #c5c8ce
}

.ivu-page-simple .ivu-page-simple-pager input::-webkit-input-placeholder {
    color: #c5c8ce
}

.ivu-page-simple .ivu-page-simple-pager input:hover {
    border-color: #57a3f3
}

.ivu-page-simple .ivu-page-simple-pager input:focus {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-page-simple .ivu-page-simple-pager input[disabled],
fieldset[disabled] .ivu-page-simple .ivu-page-simple-pager input {
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc
}

.ivu-page-simple .ivu-page-simple-pager input[disabled]:hover,
fieldset[disabled] .ivu-page-simple .ivu-page-simple-pager input:hover {
    border-color: #e3e5e8
}

textarea.ivu-page-simple .ivu-page-simple-pager input {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    vertical-align: bottom;
    font-size: 14px
}

.ivu-page-simple .ivu-page-simple-pager input-large {
    font-size: 16px;
    padding: 6px 7px;
    height: 40px
}

.ivu-page-simple .ivu-page-simple-pager input-small {
    padding: 1px 7px;
    height: 24px;
    border-radius: 3px
}

.ivu-page-simple .ivu-page-simple-pager input-no-border {
    border-radius: 0;
    border-color: transparent
}

.ivu-page-simple .ivu-page-simple-pager input-no-border:hover {
    border-color: transparent
}

.ivu-page-simple .ivu-page-simple-pager input-no-border:focus {
    border-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-page-simple .ivu-page-simple-pager input-no-border[disabled] {
    background-color: transparent
}

.ivu-page-simple .ivu-page-simple-pager input-no-border[disabled]:hover {
    border-color: transparent
}

.ivu-page-simple .ivu-page-simple-pager input:hover {
    border-color: #2d8cf0
}

.ivu-page-simple .ivu-page-simple-pager span {
    padding: 0 8px 0 2px
}

.ivu-page-custom-text,
.ivu-page-custom-text:hover {
    border-color: transparent
}

.ivu-page.mini .ivu-page-total {
    height: 24px;
    line-height: 24px
}

.ivu-page.mini .ivu-page-item {
    border: 0;
    margin: 0;
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 3px
}

.ivu-page.mini .ivu-page-next,
.ivu-page.mini .ivu-page-prev {
    margin: 0;
    min-width: 24px;
    height: 24px;
    line-height: 22px;
    border: 0
}

.ivu-page.mini .ivu-page-next a i:after,
.ivu-page.mini .ivu-page-prev a i:after {
    height: 24px;
    line-height: 24px
}

.ivu-page.mini .ivu-page-item-jump-next,
.ivu-page.mini .ivu-page-item-jump-prev {
    height: 24px;
    line-height: 24px;
    border: none;
    margin-right: 0
}

.ivu-page.mini .ivu-page-options {
    margin-left: 8px
}

.ivu-page.mini .ivu-page-options-elevator {
    height: 24px;
    line-height: 24px
}

.ivu-page.mini .ivu-page-options-elevator input {
    padding: 1px 7px;
    height: 24px;
    border-radius: 3px;
    width: 44px
}

.ivu-steps {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    font-size: 0;
    line-height: 1.5
}

.ivu-steps-item {
    display: inline-block;
    position: relative;
    vertical-align: top;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: hidden
}

.ivu-steps-item:last-child {
    -webkit-box-flex: 0;
    -ms-flex: 0;
    flex: none
}

.ivu-steps-item.ivu-steps-status-wait .ivu-steps-head-inner {
    background-color: #fff
}

.ivu-steps-item.ivu-steps-status-wait .ivu-steps-head-inner span,
.ivu-steps-item.ivu-steps-status-wait .ivu-steps-head-inner>.ivu-steps-icon {
    color: #ccc
}

.ivu-steps-item.ivu-steps-status-wait .ivu-steps-title {
    color: #999
}

.ivu-steps-item.ivu-steps-status-wait .ivu-steps-content {
    color: #999
}

.ivu-steps-item.ivu-steps-status-wait .ivu-steps-tail>i {
    background-color: #e8eaec
}

.ivu-steps-item.ivu-steps-status-process .ivu-steps-head-inner {
    border-color: #2d8cf0;
    background-color: #2d8cf0
}

.ivu-steps-item.ivu-steps-status-process .ivu-steps-head-inner span,
.ivu-steps-item.ivu-steps-status-process .ivu-steps-head-inner>.ivu-steps-icon {
    color: #fff
}

.ivu-steps-item.ivu-steps-status-process .ivu-steps-title {
    color: #666
}

.ivu-steps-item.ivu-steps-status-process .ivu-steps-content {
    color: #666
}

.ivu-steps-item.ivu-steps-status-process .ivu-steps-tail>i {
    background-color: #e8eaec
}

.ivu-steps-item.ivu-steps-status-finish .ivu-steps-head-inner {
    background-color: #fff;
    border-color: #2d8cf0
}

.ivu-steps-item.ivu-steps-status-finish .ivu-steps-head-inner span,
.ivu-steps-item.ivu-steps-status-finish .ivu-steps-head-inner>.ivu-steps-icon {
    color: #2d8cf0
}

.ivu-steps-item.ivu-steps-status-finish .ivu-steps-tail>i:after {
    width: 100%;
    background: #2d8cf0;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    opacity: 1
}

.ivu-steps-item.ivu-steps-status-finish .ivu-steps-title {
    color: #999
}

.ivu-steps-item.ivu-steps-status-finish .ivu-steps-content {
    color: #999
}

.ivu-steps-item.ivu-steps-status-error .ivu-steps-head-inner {
    background-color: #fff;
    border-color: #ed4014
}

.ivu-steps-item.ivu-steps-status-error .ivu-steps-head-inner>.ivu-steps-icon {
    color: #ed4014
}

.ivu-steps-item.ivu-steps-status-error .ivu-steps-title {
    color: #ed4014
}

.ivu-steps-item.ivu-steps-status-error .ivu-steps-content {
    color: #ed4014
}

.ivu-steps-item.ivu-steps-status-error .ivu-steps-tail>i {
    background-color: #e8eaec
}

.ivu-steps-item.ivu-steps-next-error .ivu-steps-tail>i,
.ivu-steps-item.ivu-steps-next-error .ivu-steps-tail>i:after {
    background-color: #ed4014
}

.ivu-steps-item.ivu-steps-custom .ivu-steps-head-inner {
    background: 0 0;
    border: 0;
    width: auto;
    height: auto
}

.ivu-steps-item.ivu-steps-custom .ivu-steps-head-inner>.ivu-steps-icon {
    font-size: 20px;
    top: 2px;
    width: 20px;
    height: 20px
}

.ivu-steps-item.ivu-steps-custom.ivu-steps-status-process .ivu-steps-head-inner>.ivu-steps-icon {
    color: #2d8cf0
}

.ivu-steps-item:last-child .ivu-steps-tail {
    display: none
}

.ivu-steps .ivu-steps-head,
.ivu-steps .ivu-steps-main {
    position: relative;
    display: inline-block;
    vertical-align: top
}

.ivu-steps .ivu-steps-head {
    background: #fff
}

.ivu-steps .ivu-steps-head-inner {
    display: block;
    width: 26px;
    height: 26px;
    line-height: 24px;
    margin-right: 8px;
    text-align: center;
    border: 1px solid #ccc;
    border-radius: 50%;
    font-size: 14px;
    -webkit-transition: background-color .2s ease-in-out;
    transition: background-color .2s ease-in-out
}

.ivu-steps .ivu-steps-head-inner>.ivu-steps-icon {
    line-height: 1;
    position: relative
}

.ivu-steps .ivu-steps-head-inner>.ivu-steps-icon.ivu-icon {
    font-size: 24px
}

.ivu-steps .ivu-steps-head-inner>.ivu-steps-icon.ivu-icon-ios-checkmark-empty,
.ivu-steps .ivu-steps-head-inner>.ivu-steps-icon.ivu-icon-ios-close-empty {
    font-weight: 700
}

.ivu-steps .ivu-steps-main {
    margin-top: 2.5px;
    display: inline
}

.ivu-steps .ivu-steps-custom .ivu-steps-title {
    margin-top: 2.5px
}

.ivu-steps .ivu-steps-title {
    display: inline-block;
    margin-bottom: 4px;
    padding-right: 10px;
    font-size: 14px;
    font-weight: 700;
    color: #666;
    background: #fff
}

.ivu-steps .ivu-steps-title>a:first-child:last-child {
    color: #666
}

.ivu-steps .ivu-steps-item-last .ivu-steps-title {
    padding-right: 0;
    width: 100%
}

.ivu-steps .ivu-steps-content {
    font-size: 12px;
    color: #999
}

.ivu-steps .ivu-steps-tail {
    width: 100%;
    padding: 0 10px;
    position: absolute;
    left: 0;
    top: 13px
}

.ivu-steps .ivu-steps-tail>i {
    display: inline-block;
    width: 100%;
    height: 1px;
    vertical-align: top;
    background: #e8eaec;
    border-radius: 1px;
    position: relative
}

.ivu-steps .ivu-steps-tail>i:after {
    content: '';
    width: 0;
    height: 100%;
    background: #e8eaec;
    opacity: 0;
    position: absolute;
    top: 0
}

.ivu-steps.ivu-steps-small .ivu-steps-head-inner {
    width: 18px;
    height: 18px;
    line-height: 16px;
    margin-right: 10px;
    text-align: center;
    border-radius: 50%;
    font-size: 12px
}

.ivu-steps.ivu-steps-small .ivu-steps-head-inner>.ivu-steps-icon.ivu-icon {
    font-size: 16px;
    top: 0
}

.ivu-steps.ivu-steps-small .ivu-steps-main {
    margin-top: 0
}

.ivu-steps.ivu-steps-small .ivu-steps-title {
    margin-bottom: 4px;
    margin-top: 0;
    color: #666;
    font-size: 12px;
    font-weight: 700
}

.ivu-steps.ivu-steps-small .ivu-steps-content {
    font-size: 12px;
    color: #999;
    padding-left: 30px
}

.ivu-steps.ivu-steps-small .ivu-steps-tail {
    top: 8px;
    padding: 0 8px
}

.ivu-steps.ivu-steps-small .ivu-steps-tail>i {
    height: 1px;
    width: 100%;
    border-radius: 1px
}

.ivu-steps .ivu-steps-item.ivu-steps-custom .ivu-steps-head-inner,
.ivu-steps.ivu-steps-small .ivu-steps-item.ivu-steps-custom .ivu-steps-head-inner {
    width: inherit;
    height: inherit;
    line-height: inherit;
    border-radius: 0;
    border: 0;
    background: 0 0
}

.ivu-steps-vertical {
    display: block
}

.ivu-steps-vertical .ivu-steps-item {
    display: block;
    overflow: visible
}

.ivu-steps-vertical .ivu-steps-tail {
    position: absolute;
    left: 13px;
    top: 0;
    height: 100%;
    width: 1px;
    padding: 30px 0 4px 0
}

.ivu-steps-vertical .ivu-steps-tail>i {
    height: 100%;
    width: 1px
}

.ivu-steps-vertical .ivu-steps-tail>i:after {
    height: 0;
    width: 100%
}

.ivu-steps-vertical .ivu-steps-status-finish .ivu-steps-tail>i:after {
    height: 100%
}

.ivu-steps-vertical .ivu-steps-head {
    float: left
}

.ivu-steps-vertical .ivu-steps-head-inner {
    margin-right: 16px
}

.ivu-steps-vertical .ivu-steps-main {
    min-height: 47px;
    overflow: hidden;
    display: block
}

.ivu-steps-vertical .ivu-steps-main .ivu-steps-title {
    line-height: 26px
}

.ivu-steps-vertical .ivu-steps-main .ivu-steps-content {
    padding-bottom: 12px;
    padding-left: 0
}

.ivu-steps-vertical .ivu-steps-custom .ivu-steps-icon {
    left: 4px
}

.ivu-steps-vertical.ivu-steps-small .ivu-steps-custom .ivu-steps-icon {
    left: 0
}

.ivu-steps-vertical.ivu-steps-small .ivu-steps-tail {
    position: absolute;
    left: 9px;
    top: 0;
    padding: 22px 0 4px 0
}

.ivu-steps-vertical.ivu-steps-small .ivu-steps-tail>i {
    height: 100%
}

.ivu-steps-vertical.ivu-steps-small .ivu-steps-title {
    line-height: 18px
}

.ivu-steps-horizontal.ivu-steps-hidden {
    visibility: hidden
}

.ivu-steps-horizontal .ivu-steps-content {
    padding-left: 35px
}

.ivu-steps-horizontal .ivu-steps-item:not(:first-child) .ivu-steps-head {
    padding-left: 10px;
    margin-left: -10px
}

.ivu-modal {
    width: auto !important;
    margin: 0 auto;
    position: relative;
    outline: 0;
    top: 100px
}

.ivu-modal-hidden {
    display: none !important
}

.ivu-modal-wrap {
    position: fixed;
    overflow: auto;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    -webkit-overflow-scrolling: touch;
    outline: 0
}

.ivu-modal-wrap * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent
}

.ivu-modal-mask {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(55, 55, 55, .6);
    height: 100%;
    z-index: 1000
}

.ivu-modal-mask-hidden {
    display: none
}

.ivu-modal-content {
    position: relative;
    background-color: #fff;
    border: 0;
    border-radius: 6px;
    background-clip: padding-box;
    -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, .15);
    box-shadow: 0 4px 12px rgba(0, 0, 0, .15)
}

.ivu-modal-content-no-mask {
    pointer-events: auto
}

.ivu-modal-content-drag {
    position: absolute
}

.ivu-modal-content-drag .ivu-modal-header {
    cursor: move
}

.ivu-modal-content-dragging {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.ivu-modal-header {
    border-bottom: 1px solid #e8eaec;
    padding: 14px 16px;
    line-height: 1
}

.ivu-modal-header p,
.ivu-modal-header-inner {
    display: inline-block;
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 16px;
    color: #17233d;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ivu-modal-close {
    z-index: 1;
    font-size: 12px;
    position: absolute;
    right: 8px;
    top: 8px;
    overflow: hidden;
    cursor: pointer
}

.ivu-modal-close .ivu-icon-ios-close {
    font-size: 31px;
    color: #999;
    -webkit-transition: color .2s ease;
    transition: color .2s ease;
    position: relative;
    top: 1px
}

.ivu-modal-close .ivu-icon-ios-close:hover {
    color: #444
}

.ivu-modal-body {
    padding: 16px;
    font-size: 14px;
    line-height: 1.5
}

.ivu-modal-footer {
    border-top: 1px solid #e8eaec;
    padding: 12px 18px 12px 18px;
    text-align: right
}

.ivu-modal-footer button+button {
    margin-left: 8px;
    margin-bottom: 0
}

.ivu-modal-fullscreen {
    width: 100% !important;
    top: 0;
    bottom: 0;
    position: absolute
}

.ivu-modal-fullscreen .ivu-modal-content {
    width: 100%;
    border-radius: 0;
    position: absolute;
    top: 0;
    bottom: 0
}

.ivu-modal-fullscreen .ivu-modal-body {
    width: 100%;
    overflow: auto;
    position: absolute;
    top: 51px;
    bottom: 61px
}

.ivu-modal-fullscreen-no-header .ivu-modal-body {
    top: 0
}

.ivu-modal-fullscreen-no-footer .ivu-modal-body {
    bottom: 0
}

.ivu-modal-fullscreen .ivu-modal-footer {
    position: absolute;
    width: 100%;
    bottom: 0
}

.ivu-modal-no-mask {
    pointer-events: none
}

@media (max-width:576px) {
    .ivu-modal {
        width: auto !important;
        margin: 10px
    }

    .ivu-modal-fullscreen {
        width: 100% !important;
        margin: 0
    }

    .vertical-center-modal .ivu-modal {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1
    }
}

/* .ivu-modal-confirm {
    padding: 6px 16px 8px
} */

.ivu-modal-body {
    padding: 5px !important;
}
.ivu-modal-confirm-head {
    padding: 0 12px 0 0
}

.ivu-modal-confirm-head-icon {
    display: inline-block;
    font-size: 28px;
    vertical-align: middle;
    position: relative;
    top: -2px
}

.ivu-modal-confirm-head-icon-info {
    color: #2d8cf0
}

.ivu-modal-confirm-head-icon-success {
    color: #19be6b
}

.ivu-modal-confirm-head-icon-warning {
    color: #f90
}

.ivu-modal-confirm-head-icon-error {
    color: #ed4014
}

.ivu-modal-confirm-head-icon-confirm {
    color: #f90
}

.ivu-modal-confirm-head-title {
    display: inline-block;
    vertical-align: middle;
    margin-left: 12px;
    font-size: 16px;
    color: #17233d;
    font-weight: 500
}

.ivu-modal-confirm-body {
    font-size: 14px;
    color: #515a6e;
    position: relative
}

.ivu-modal-confirm-body-render {
    margin: 0;
    padding: 0
}

.ivu-modal-confirm-footer {
    margin-top: 20px;
    text-align: right
}

.ivu-modal-confirm-footer button+button {
    margin-left: 8px;
    margin-bottom: 0
}

.ivu-select {
    display: inline-block;
    width: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    vertical-align: middle;
    color: #515a6e;
    font-size: 14px;
    line-height: normal
}

.ivu-select-selection {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    outline: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
    position: relative;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #dcdee2;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-select-selection-focused,
.ivu-select-selection:hover {
    border-color: #57a3f3
}

.ivu-select-selection-focused .ivu-select-arrow,
.ivu-select-selection:hover .ivu-select-arrow {
    display: inline-block
}

.ivu-select-arrow {
    position: absolute;
    top: 50%;
    right: 8px;
    line-height: 1;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    font-size: 14px;
    color: #808695;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-select-visible .ivu-select-selection {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-select-visible .ivu-select-arrow {
    -webkit-transform: translateY(-50%) rotate(180deg);
    -ms-transform: translateY(-50%) rotate(180deg);
    transform: translateY(-50%) rotate(180deg);
    display: inline-block
}

.ivu-select-disabled .ivu-select-selection {
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc
}

.ivu-select-disabled .ivu-select-selection:hover {
    border-color: #e3e5e8
}

.ivu-select-disabled .ivu-select-selection .ivu-select-arrow {
    color: #ccc
}

.ivu-select-disabled .ivu-select-selection:hover {
    border-color: #dcdee2;
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-select-disabled .ivu-select-selection:hover .ivu-select-arrow {
    display: inline-block
}

.ivu-select-single .ivu-select-selection {
    height: 32px;
    position: relative
}

.ivu-select-single .ivu-select-selection .ivu-select-placeholder {
    color: #c5c8ce
}

.ivu-select-single .ivu-select-selection .ivu-select-placeholder,
.ivu-select-single .ivu-select-selection .ivu-select-selected-value {
    display: block;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-left: 8px;
    padding-right: 24px
}

.ivu-select-multiple .ivu-select-selection {
    padding: 0 24px 0 4px
}

.ivu-select-multiple .ivu-select-selection .ivu-select-placeholder {
    display: block;
    height: 30px;
    line-height: 30px;
    color: #c5c8ce;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-left: 4px;
    padding-right: 22px
}

.ivu-select-default.ivu-select-multiple .ivu-select-selection {
    min-height: 32px
}

.ivu-select-large.ivu-select-single .ivu-select-selection {
    height: 40px
}

.ivu-select-large.ivu-select-single .ivu-select-selection .ivu-select-placeholder,
.ivu-select-large.ivu-select-single .ivu-select-selection .ivu-select-selected-value {
    height: 38px;
    line-height: 38px;
    font-size: 16px
}

.ivu-select-large.ivu-select-multiple .ivu-select-selection {
    min-height: 40px
}

.ivu-select-large.ivu-select-multiple .ivu-select-selection .ivu-select-placeholder,
.ivu-select-large.ivu-select-multiple .ivu-select-selection .ivu-select-selected-value {
    min-height: 38px;
    line-height: 38px;
    font-size: 16px
}

.ivu-select-small.ivu-select-single .ivu-select-selection {
    height: 24px;
    border-radius: 3px
}

.ivu-select-small.ivu-select-single .ivu-select-selection .ivu-select-placeholder,
.ivu-select-small.ivu-select-single .ivu-select-selection .ivu-select-selected-value {
    height: 22px;
    line-height: 22px
}

.ivu-select-small.ivu-select-multiple .ivu-select-selection {
    min-height: 24px;
    border-radius: 3px
}

.ivu-select-small.ivu-select-multiple .ivu-select-selection .ivu-select-placeholder,
.ivu-select-small.ivu-select-multiple .ivu-select-selection .ivu-select-selected-value {
    height: auto;
    min-height: 22px;
    line-height: 22px
}

.ivu-select-input {
    display: inline-block;
    height: 32px;
    line-height: 32px;
    padding: 0 24px 0 8px;
    font-size: 14px;
    outline: 0;
    border: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #515a6e;
    background-color: transparent;
    position: relative;
    cursor: pointer
}

.ivu-select-input::-moz-placeholder {
    color: #c5c8ce;
    opacity: 1
}

.ivu-select-input:-ms-input-placeholder {
    color: #c5c8ce
}

.ivu-select-input::-webkit-input-placeholder {
    color: #c5c8ce
}

.ivu-select-input[disabled] {
    cursor: not-allowed;
    color: #ccc;
    -webkit-text-fill-color: #ccc
}

.ivu-select-single .ivu-select-input {
    width: 100%
}

.ivu-select-large .ivu-select-input,
.ivu-select-large.ivu-select-multiple .ivu-select-input {
    font-size: 16px;
    height: 32px;
    line-height: 32px;
    top: 3px
}

.ivu-select-small .ivu-select-input,
.ivu-select-small.ivu-select-multiple .ivu-select-input {
    height: 18px;
    line-height: 18px;
    top: 2px
}

.ivu-select-multiple .ivu-select-input {
    height: 26px;
    line-height: 26px;
    padding: 0 0 0 4px;
    top: 2px
}

.ivu-select-not-found {
    text-align: center;
    color: #c5c8ce
}

.ivu-select-not-found li:not([class^=ivu-]) {
    margin-bottom: 0
}

.ivu-select-loading {
    text-align: center;
    color: #c5c8ce
}

.ivu-select-multiple .ivu-tag {
    height: 24px;
    line-height: 22px;
    margin: 3px 4px 3px 0;
    max-width: 99%;
    position: relative
}

.ivu-select-multiple .ivu-tag span:not(.ivu-select-max-tag) {
    display: block;
    margin-right: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ivu-select-multiple .ivu-tag i {
    display: block;
    position: absolute;
    right: 4px;
    top: 4px
}

.ivu-select-multiple-tag-hidden {
    margin-right: 0 !important
}

.ivu-select-large.ivu-select-multiple .ivu-tag {
    height: 32px;
    line-height: 30px;
    font-size: 16px
}

.ivu-select-large.ivu-select-multiple .ivu-tag i {
    top: 9px
}

.ivu-select-small.ivu-select-multiple .ivu-tag {
    height: 17px;
    line-height: 15px;
    font-size: 12px;
    padding: 0 6px;
    margin: 3px 4px 2px 0
}

.ivu-select-small.ivu-select-multiple .ivu-tag span {
    margin-right: 14px
}

.ivu-select-small.ivu-select-multiple .ivu-tag i {
    top: 1px;
    right: 2px
}

.ivu-select-dropdown-list {
    min-width: 100%;
    list-style: none
}

.ivu-select .ivu-select-dropdown {
    width: auto
}

.ivu-select-prefix {
    display: inline-block;
    vertical-align: middle
}

.ivu-select-prefix i {
    vertical-align: top
}

.ivu-select-head-with-prefix {
    display: inline-block !important;
    vertical-align: middle
}

.ivu-select-single .ivu-select-prefix {
    padding-left: 4px
}

.ivu-select-multiple .ivu-select-head-with-prefix,
.ivu-select-single .ivu-select-head-with-prefix {
    padding-left: 0 !important
}

.ivu-select-head-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.ivu-select-multiple .ivu-select-head-flex .ivu-select-prefix {
    margin-right: 4px
}

.ivu-select-item {
    margin: 0;
    line-height: normal;
    padding: 7px 16px;
    clear: both;
    color: #515a6e;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    -webkit-transition: background .2s ease-in-out;
    transition: background .2s ease-in-out
}

.ivu-select-item:hover {
    background: #f3f3f3
}

.ivu-select-item-focus {
    background: #f3f3f3
}

.ivu-select-item-disabled {
    color: #c5c8ce;
    cursor: not-allowed
}

.ivu-select-item-disabled:hover {
    color: #c5c8ce;
    background-color: #fff;
    cursor: not-allowed
}

.ivu-select-item-selected,
.ivu-select-item-selected:hover {
    color: #2d8cf0
}

.ivu-select-item-divided {
    margin-top: 5px;
    border-top: 1px solid #e8eaec
}

.ivu-select-item-divided:before {
    content: '';
    height: 5px;
    display: block;
    margin: 0 -16px;
    background-color: #fff;
    position: relative;
    top: -7px
}

.ivu-select-item-enter {
    color: #2d8cf0;
    font-weight: 700;
    float: right
}

.ivu-select-large .ivu-select-item {
    padding: 7px 16px 8px;
    font-size: 14px !important
}

@-moz-document url-prefix() {
    .ivu-select-item {
        white-space: normal
    }
}

.ivu-select-multiple .ivu-select-item {
    position: relative
}

.ivu-select-multiple .ivu-select-item-selected {
    color: rgba(45, 140, 240, .9);
    background: #fff
}

.ivu-select-multiple .ivu-select-item-focus,
.ivu-select-multiple .ivu-select-item-selected:hover {
    background: #f3f3f3
}

.ivu-select-multiple .ivu-select-item-selected.ivu-select-multiple .ivu-select-item-focus {
    color: rgba(40, 123, 211, .91);
    background: #fff
}

.ivu-select-multiple .ivu-select-item-selected:after {
    display: inline-block;
    font-family: Ionicons;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    text-rendering: optimizeLegibility;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    vertical-align: -.125em;
    text-align: center;
    font-size: 24px;
    content: '\F171';
    color: rgba(45, 140, 240, .9);
    position: absolute;
    top: 2px;
    right: 8px
}

.ivu-select-multiple .ivu-select-item-selected.ivu-select-item-disabled {
    color: #c5c8ce
}

.ivu-select-multiple .ivu-select-item-selected.ivu-select-item-disabled:after {
    color: #c5c8ce
}

.ivu-select-multiple .ivu-select-item-selected.ivu-select-item-disabled:hover {
    background-color: #fff
}

.ivu-select-group {
    list-style: none;
    margin: 0;
    padding: 0
}

.ivu-select-group-title {
    padding-left: 8px;
    font-size: 14px;
    color: #999;
    height: 30px;
    line-height: 30px
}

.ivu-form-item-error .ivu-select-selection {
    border: 1px solid #ed4014
}

.ivu-form-item-error .ivu-select-arrow {
    color: #ed4014
}

.ivu-form-item-error .ivu-select-visible .ivu-select-selection {
    border-color: #ed4014;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(237, 64, 20, .2);
    box-shadow: 0 0 0 2px rgba(237, 64, 20, .2)
}

.ivu-select-dropdown {
    width: inherit;
    max-height: 200px;
    overflow: auto;
    margin: 5px 0;
    padding: 5px 0;
    background-color: #fff;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    position: absolute;
    z-index: 900
}

.ivu-select-dropdown-transfer {
    z-index: 1060;
    width: auto
}

.ivu-select-dropdown.ivu-transfer-no-max-height {
    max-height: none
}

.ivu-modal .ivu-select-dropdown {
    position: absolute !important
}

.ivu-split-wrapper {
    position: relative;
    width: 100%;
    height: 100%
}

.ivu-split-pane {
    position: absolute
}

.ivu-split-pane.left-pane,
.ivu-split-pane.right-pane {
    top: 0;
    bottom: 0
}

.ivu-split-pane.left-pane {
    left: 0
}

.ivu-split-pane.right-pane {
    right: 0
}

.ivu-split-pane.bottom-pane,
.ivu-split-pane.top-pane {
    left: 0;
    right: 0
}

.ivu-split-pane.top-pane {
    top: 0
}

.ivu-split-pane.bottom-pane {
    bottom: 0
}

.ivu-split-pane-moving {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.ivu-split-trigger {
    border: 1px solid #dcdee2
}

.ivu-split-trigger-con {
    position: absolute;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    z-index: 10
}

.ivu-split-trigger-bar-con {
    position: absolute;
    overflow: hidden
}

.ivu-split-trigger-bar-con.vertical {
    left: 1px;
    top: 50%;
    height: 32px;
    -webkit-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    transform: translate(0, -50%)
}

.ivu-split-trigger-bar-con.horizontal {
    left: 50%;
    top: 1px;
    width: 32px;
    -webkit-transform: translate(-50%, 0);
    -ms-transform: translate(-50%, 0);
    transform: translate(-50%, 0)
}

.ivu-split-trigger-vertical {
    width: 6px;
    height: 100%;
    background: #f8f8f9;
    border-top: none;
    border-bottom: none;
    cursor: col-resize
}

.ivu-split-trigger-vertical .ivu-split-trigger-bar {
    width: 4px;
    height: 1px;
    background: rgba(23, 35, 61, .25);
    float: left;
    margin-top: 3px
}

.ivu-split-trigger-horizontal {
    height: 6px;
    width: 100%;
    background: #f8f8f9;
    border-left: none;
    border-right: none;
    cursor: row-resize
}

.ivu-split-trigger-horizontal .ivu-split-trigger-bar {
    height: 4px;
    width: 1px;
    background: rgba(23, 35, 61, .25);
    float: left;
    margin-right: 3px
}

.ivu-split-horizontal>.ivu-split-trigger-con {
    top: 50%;
    height: 100%;
    width: 0
}

.ivu-split-vertical>.ivu-split-trigger-con {
    left: 50%;
    height: 0;
    width: 100%
}

.ivu-split .no-select {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.ivu-tooltip {
    display: inline-block
}

.ivu-tooltip-rel {
    display: inline-block;
    position: relative;
    width: inherit
}

.ivu-tooltip-popper {
    display: block;
    visibility: visible;
    font-size: 14px;
    line-height: 1.5;
    position: absolute;
    z-index: 1060
}

.ivu-tooltip-popper[x-placement^=top] {
    padding: 5px 0 8px 0
}

.ivu-tooltip-popper[x-placement^=right] {
    padding: 0 5px 0 8px
}

.ivu-tooltip-popper[x-placement^=bottom] {
    padding: 8px 0 5px 0
}

.ivu-tooltip-popper[x-placement^=left] {
    padding: 0 8px 0 5px
}

.ivu-tooltip-popper[x-placement^=top] .ivu-tooltip-arrow {
    bottom: 3px;
    border-width: 5px 5px 0;
    border-top-color: rgba(70, 76, 91, .9)
}

.ivu-tooltip-popper[x-placement=top] .ivu-tooltip-arrow {
    left: 50%;
    margin-left: -5px
}

.ivu-tooltip-popper[x-placement=top-start] .ivu-tooltip-arrow {
    left: 16px
}

.ivu-tooltip-popper[x-placement=top-end] .ivu-tooltip-arrow {
    right: 16px
}

.ivu-tooltip-popper[x-placement^=right] .ivu-tooltip-arrow {
    left: 3px;
    border-width: 5px 5px 5px 0;
    border-right-color: rgba(70, 76, 91, .9)
}

.ivu-tooltip-popper[x-placement=right] .ivu-tooltip-arrow {
    top: 50%;
    margin-top: -5px
}

.ivu-tooltip-popper[x-placement=right-start] .ivu-tooltip-arrow {
    top: 8px
}

.ivu-tooltip-popper[x-placement=right-end] .ivu-tooltip-arrow {
    bottom: 8px
}

.ivu-tooltip-popper[x-placement^=left] .ivu-tooltip-arrow {
    right: 3px;
    border-width: 5px 0 5px 5px;
    border-left-color: rgba(70, 76, 91, .9)
}

.ivu-tooltip-popper[x-placement=left] .ivu-tooltip-arrow {
    top: 50%;
    margin-top: -5px
}

.ivu-tooltip-popper[x-placement=left-start] .ivu-tooltip-arrow {
    top: 8px
}

.ivu-tooltip-popper[x-placement=left-end] .ivu-tooltip-arrow {
    bottom: 8px
}

.ivu-tooltip-popper[x-placement^=bottom] .ivu-tooltip-arrow {
    top: 3px;
    border-width: 0 5px 5px;
    border-bottom-color: rgba(70, 76, 91, .9)
}

.ivu-tooltip-popper[x-placement=bottom] .ivu-tooltip-arrow {
    left: 50%;
    margin-left: -5px
}

.ivu-tooltip-popper[x-placement=bottom-start] .ivu-tooltip-arrow {
    left: 16px
}

.ivu-tooltip-popper[x-placement=bottom-end] .ivu-tooltip-arrow {
    right: 16px
}

.ivu-tooltip-light.ivu-tooltip-popper {
    display: block;
    visibility: visible;
    font-size: 14px;
    line-height: 1.5;
    position: absolute;
    z-index: 1060
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=top] {
    padding: 7px 0 10px 0
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=right] {
    padding: 0 7px 0 10px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=bottom] {
    padding: 10px 0 7px 0
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=left] {
    padding: 0 10px 0 7px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=top] .ivu-tooltip-arrow {
    bottom: 3px;
    border-width: 7px 7px 0;
    border-top-color: rgba(217, 217, 217, .5)
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=top] .ivu-tooltip-arrow {
    left: 50%;
    margin-left: -7px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=top-start] .ivu-tooltip-arrow {
    left: 16px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=top-end] .ivu-tooltip-arrow {
    right: 16px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=right] .ivu-tooltip-arrow {
    left: 3px;
    border-width: 7px 7px 7px 0;
    border-right-color: rgba(217, 217, 217, .5)
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=right] .ivu-tooltip-arrow {
    top: 50%;
    margin-top: -7px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=right-start] .ivu-tooltip-arrow {
    top: 8px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=right-end] .ivu-tooltip-arrow {
    bottom: 8px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=left] .ivu-tooltip-arrow {
    right: 3px;
    border-width: 7px 0 7px 7px;
    border-left-color: rgba(217, 217, 217, .5)
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=left] .ivu-tooltip-arrow {
    top: 50%;
    margin-top: -7px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=left-start] .ivu-tooltip-arrow {
    top: 8px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=left-end] .ivu-tooltip-arrow {
    bottom: 8px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=bottom] .ivu-tooltip-arrow {
    top: 3px;
    border-width: 0 7px 7px;
    border-bottom-color: rgba(217, 217, 217, .5)
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=bottom] .ivu-tooltip-arrow {
    left: 50%;
    margin-left: -7px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=bottom-start] .ivu-tooltip-arrow {
    left: 16px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement=bottom-end] .ivu-tooltip-arrow {
    right: 16px
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=top] .ivu-tooltip-arrow:after {
    content: " ";
    bottom: 1px;
    margin-left: -7px;
    border-bottom-width: 0;
    border-top-width: 7px;
    border-top-color: #fff
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=right] .ivu-tooltip-arrow:after {
    content: " ";
    left: 1px;
    bottom: -7px;
    border-left-width: 0;
    border-right-width: 7px;
    border-right-color: #fff
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=bottom] .ivu-tooltip-arrow:after {
    content: " ";
    top: 1px;
    margin-left: -7px;
    border-top-width: 0;
    border-bottom-width: 7px;
    border-bottom-color: #fff
}

.ivu-tooltip-light.ivu-tooltip-popper[x-placement^=left] .ivu-tooltip-arrow:after {
    content: " ";
    right: 1px;
    border-right-width: 0;
    border-left-width: 7px;
    border-left-color: #fff;
    bottom: -7px
}

.ivu-tooltip-inner {
    max-width: 250px;
    min-height: 34px;
    padding: 8px 12px;
    color: #fff;
    text-align: left;
    text-decoration: none;
    background-color: rgba(70, 76, 91, .9);
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    white-space: nowrap
}

.ivu-tooltip-inner-with-width {
    white-space: pre-wrap;
    text-align: justify;
    word-wrap: break-word;
    word-break: break-all
}

.ivu-tooltip-light .ivu-tooltip-inner {
    background-color: #fff;
    color: #515a6e
}

.ivu-tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.ivu-tooltip-light .ivu-tooltip-arrow {
    border-width: 8px
}

.ivu-tooltip-light .ivu-tooltip-arrow:after {
    display: block;
    width: 0;
    height: 0;
    position: absolute;
    border-color: transparent;
    border-style: solid;
    content: "";
    border-width: 7px
}

.ivu-poptip {
    display: inline-block
}

.ivu-poptip-rel {
    display: inline-block;
    position: relative
}

.ivu-poptip-title {
    margin: 0;
    padding: 8px 16px;
    position: relative
}

.ivu-poptip-title:after {
    content: '';
    display: block;
    height: 1px;
    position: absolute;
    left: 8px;
    right: 8px;
    bottom: 0;
    background-color: #e8eaec
}

.ivu-poptip-title-inner {
    color: #17233d;
    font-size: 14px;
    font-weight: 500
}

.ivu-poptip-body {
    padding: 8px 16px
}

.ivu-poptip-body-content {
    overflow: auto
}

.ivu-poptip-body-content-word-wrap {
    white-space: pre-wrap;
    text-align: justify
}

.ivu-poptip-body-content-inner {
    color: #515a6e
}

.ivu-poptip-inner {
    width: 100%;
    background-color: #fff;
    background-clip: padding-box;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    white-space: nowrap
}

.ivu-poptip-popper {
    min-width: 150px;
    display: block;
    visibility: visible;
    font-size: 14px;
    line-height: 1.5;
    position: absolute;
    z-index: 1060
}

.ivu-poptip-popper[x-placement^=top] {
    padding: 7px 0 10px 0
}

.ivu-poptip-popper[x-placement^=right] {
    padding: 0 7px 0 10px
}

.ivu-poptip-popper[x-placement^=bottom] {
    padding: 10px 0 7px 0
}

.ivu-poptip-popper[x-placement^=left] {
    padding: 0 10px 0 7px
}

.ivu-poptip-popper[x-placement^=top] .ivu-poptip-arrow {
    bottom: 3px;
    border-width: 7px 7px 0;
    border-top-color: rgba(217, 217, 217, .5)
}

.ivu-poptip-popper[x-placement=top] .ivu-poptip-arrow {
    left: 50%;
    margin-left: -7px
}

.ivu-poptip-popper[x-placement=top-start] .ivu-poptip-arrow {
    left: 16px
}

.ivu-poptip-popper[x-placement=top-end] .ivu-poptip-arrow {
    right: 16px
}

.ivu-poptip-popper[x-placement^=right] .ivu-poptip-arrow {
    left: 3px;
    border-width: 7px 7px 7px 0;
    border-right-color: rgba(217, 217, 217, .5)
}

.ivu-poptip-popper[x-placement=right] .ivu-poptip-arrow {
    top: 50%;
    margin-top: -7px
}

.ivu-poptip-popper[x-placement=right-start] .ivu-poptip-arrow {
    top: 8px
}

.ivu-poptip-popper[x-placement=right-end] .ivu-poptip-arrow {
    bottom: 8px
}

.ivu-poptip-popper[x-placement^=left] .ivu-poptip-arrow {
    right: 3px;
    border-width: 7px 0 7px 7px;
    border-left-color: rgba(217, 217, 217, .5)
}

.ivu-poptip-popper[x-placement=left] .ivu-poptip-arrow {
    top: 50%;
    margin-top: -7px
}

.ivu-poptip-popper[x-placement=left-start] .ivu-poptip-arrow {
    top: 8px
}

.ivu-poptip-popper[x-placement=left-end] .ivu-poptip-arrow {
    bottom: 8px
}

.ivu-poptip-popper[x-placement^=bottom] .ivu-poptip-arrow {
    top: 3px;
    border-width: 0 7px 7px;
    border-bottom-color: rgba(217, 217, 217, .5)
}

.ivu-poptip-popper[x-placement=bottom] .ivu-poptip-arrow {
    left: 50%;
    margin-left: -7px
}

.ivu-poptip-popper[x-placement=bottom-start] .ivu-poptip-arrow {
    left: 16px
}

.ivu-poptip-popper[x-placement=bottom-end] .ivu-poptip-arrow {
    right: 16px
}

.ivu-poptip-popper[x-placement^=top] .ivu-poptip-arrow:after {
    content: " ";
    bottom: 1px;
    margin-left: -7px;
    border-bottom-width: 0;
    border-top-width: 7px;
    border-top-color: #fff
}

.ivu-poptip-popper[x-placement^=right] .ivu-poptip-arrow:after {
    content: " ";
    left: 1px;
    bottom: -7px;
    border-left-width: 0;
    border-right-width: 7px;
    border-right-color: #fff
}

.ivu-poptip-popper[x-placement^=bottom] .ivu-poptip-arrow:after {
    content: " ";
    top: 1px;
    margin-left: -7px;
    border-top-width: 0;
    border-bottom-width: 7px;
    border-bottom-color: #fff
}

.ivu-poptip-popper[x-placement^=left] .ivu-poptip-arrow:after {
    content: " ";
    right: 1px;
    border-right-width: 0;
    border-left-width: 7px;
    border-left-color: #fff;
    bottom: -7px
}

.ivu-poptip-arrow,
.ivu-poptip-arrow:after {
    display: block;
    width: 0;
    height: 0;
    position: absolute;
    border-color: transparent;
    border-style: solid
}

.ivu-poptip-arrow {
    border-width: 8px
}

.ivu-poptip-arrow:after {
    content: "";
    border-width: 7px
}

.ivu-poptip-confirm .ivu-poptip-popper {
    max-width: 300px
}

.ivu-poptip-confirm .ivu-poptip-inner {
    white-space: normal
}

.ivu-poptip-confirm .ivu-poptip-body {
    padding: 16px 16px 8px
}

.ivu-poptip-confirm .ivu-poptip-body .ivu-icon {
    font-size: 16px;
    color: #f90;
    line-height: 18px;
    position: absolute
}

.ivu-poptip-confirm .ivu-poptip-body-message {
    padding-left: 20px
}

.ivu-poptip-confirm .ivu-poptip-footer {
    text-align: right;
    padding: 8px 16px 16px
}

.ivu-poptip-confirm .ivu-poptip-footer button {
    margin-left: 4px
}

.ivu-input {
    display: inline-block;
    width: 100%;
    height: 32px;
    line-height: 1.5;
    padding: 4px 7px;
    font-size: 14px;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    color: #515a6e;
    background-color: #fff;
    background-image: none;
    position: relative;
    cursor: text;
    -webkit-transition: border .2s ease-in-out, background .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out
}

.ivu-input::-moz-placeholder {
    color: #c5c8ce;
    opacity: 1
}

.ivu-input:-ms-input-placeholder {
    color: #c5c8ce
}

.ivu-input::-webkit-input-placeholder {
    color: #c5c8ce
}

.ivu-input:hover {
    border-color: #57a3f3
}

.ivu-input:focus {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-input[disabled],
fieldset[disabled] .ivu-input {
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc
}

.ivu-input[disabled]:hover,
fieldset[disabled] .ivu-input:hover {
    border-color: #e3e5e8
}

textarea.ivu-input {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    vertical-align: bottom;
    font-size: 14px
}

.ivu-input-large {
    font-size: 16px;
    padding: 6px 7px;
    height: 40px
}

.ivu-input-small {
    padding: 1px 7px;
    height: 24px;
    border-radius: 3px
}

.ivu-input-no-border {
    border-radius: 0;
    border-color: transparent
}

.ivu-input-no-border:hover {
    border-color: transparent
}

.ivu-input-no-border:focus {
    border-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-input-no-border[disabled] {
    background-color: transparent
}

.ivu-input-no-border[disabled]:hover {
    border-color: transparent
}

.ivu-input-wrapper {
    display: inline-block;
    width: 100%;
    position: relative;
    vertical-align: middle;
    line-height: normal
}

.ivu-input-icon {
    width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 16px;
    text-align: center;
    color: #808695;
    position: absolute;
    right: 0;
    z-index: 3
}

.ivu-input-hide-icon .ivu-input-icon {
    display: none
}

.ivu-input-icon-validate {
    display: none
}

.ivu-input-icon-clear {
    display: none
}

.ivu-input-wrapper:hover .ivu-input-icon-clear {
    display: inline-block
}

.ivu-input-icon-normal+.ivu-input {
    padding-right: 32px
}

.ivu-input-hide-icon .ivu-input-icon-normal+.ivu-input {
    padding-right: 7px
}

.ivu-input-wrapper-large .ivu-input-icon {
    font-size: 18px;
    height: 40px;
    line-height: 40px
}

.ivu-input-wrapper-small .ivu-input-icon {
    width: 24px;
    font-size: 14px;
    height: 24px;
    line-height: 24px
}

.ivu-input-prefix,
.ivu-input-suffix {
    width: 32px;
    height: 100%;
    text-align: center;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1
}

.ivu-input-prefix i,
.ivu-input-suffix i {
    font-size: 16px;
    line-height: 32px;
    color: #808695
}

.ivu-input-suffix {
    left: auto;
    right: 0
}

.ivu-input-wrapper-small .ivu-input-prefix i,
.ivu-input-wrapper-small .ivu-input-suffix i {
    font-size: 14px;
    line-height: 24px
}

.ivu-input-wrapper-large .ivu-input-prefix i,
.ivu-input-wrapper-large .ivu-input-suffix i {
    font-size: 18px;
    line-height: 40px
}

.ivu-input-with-prefix {
    padding-left: 32px
}

.ivu-input-with-suffix {
    padding-right: 32px
}

.ivu-input-search {
    cursor: pointer;
    padding: 0 16px !important;
    background: #2d8cf0 !important;
    color: #fff !important;
    border-color: #2d8cf0 !important;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    position: relative;
    z-index: 2
}

.ivu-input-search i {
    font-size: 16px
}

.ivu-input-search:hover {
    background: #57a3f3 !important;
    border-color: #57a3f3 !important
}

.ivu-input-search:active {
    background: #2b85e4 !important;
    border-color: #2b85e4 !important
}

.ivu-input-search-icon {
    cursor: pointer;
    -webkit-transition: color .2s ease-in-out;
    transition: color .2s ease-in-out
}

.ivu-input-search-icon:hover {
    color: inherit
}

.ivu-input-search:before {
    content: '';
    display: block;
    width: 1px;
    position: absolute;
    top: -1px;
    bottom: -1px;
    left: -1px;
    background: inherit
}

.ivu-input-wrapper-small .ivu-input-search {
    padding: 0 12px !important
}

.ivu-input-wrapper-small .ivu-input-search i {
    font-size: 14px
}

.ivu-input-wrapper-large .ivu-input-search {
    padding: 0 20px !important
}

.ivu-input-wrapper-large .ivu-input-search i {
    font-size: 18px
}

.ivu-input-with-search:hover .ivu-input {
    border-color: #57a3f3
}

.ivu-input-word-count {
    text-align: center;
    position: absolute;
    right: 7px;
    top: 2px;
    bottom: 2px;
    padding-left: 7px;
    background: #fff;
    z-index: 1;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #808695;
    font-size: 12px
}

.ivu-input-wrapper-disabled .ivu-input-word-count {
    background: #f3f3f3
}

.ivu-input-type-textarea .ivu-input-word-count {
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    top: auto
}

.ivu-input-group {
    display: table;
    width: 100%;
    border-collapse: separate;
    position: relative;
    font-size: 14px
}

.ivu-form-inline .ivu-input-group {
    top: 1px
}

.ivu-input-group-large {
    font-size: 16px
}

.ivu-input-group[class*=col-] {
    float: none;
    padding-left: 0;
    padding-right: 0
}

.ivu-input-group>[class*=col-] {
    padding-right: 8px
}

.ivu-input-group-append,
.ivu-input-group-prepend,
.ivu-input-group>.ivu-input {
    display: table-cell
}

.ivu-input-group-with-prepend .ivu-input,
.ivu-input-group-with-prepend.ivu-input-group-small .ivu-input {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.ivu-input-group-with-append .ivu-input,
.ivu-input-group-with-append.ivu-input-group-small .ivu-input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.ivu-input-group-append .ivu-btn,
.ivu-input-group-prepend .ivu-btn {
    border-color: transparent;
    background-color: transparent;
    color: inherit;
    margin: -6px -7px
}

.ivu-input-group-append,
.ivu-input-group-prepend {
    width: 1px;
    white-space: nowrap;
    vertical-align: middle
}

.ivu-input-group .ivu-input {
    width: 100%;
    float: left;
    margin-bottom: 0;
    position: relative;
    z-index: 2
}

.ivu-input-group-append,
.ivu-input-group-prepend {
    padding: 4px 7px;
    font-size: inherit;
    font-weight: 400;
    line-height: 1;
    color: #515a6e;
    text-align: center;
    background-color: #f8f8f9;
    border: 1px solid #dcdee2;
    border-radius: 4px
}

.ivu-input-group-append .ivu-select,
.ivu-input-group-prepend .ivu-select {
    margin: -5px -7px
}

.ivu-input-group-append .ivu-select-selection,
.ivu-input-group-prepend .ivu-select-selection {
    background-color: inherit;
    margin: -1px;
    border: 1px solid transparent
}

.ivu-input-group-append .ivu-select-visible .ivu-select-selection,
.ivu-input-group-prepend .ivu-select-visible .ivu-select-selection {
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-input-group-prepend,
.ivu-input-group>.ivu-input:first-child,
.ivu-input-group>span>.ivu-input:first-child {
    border-bottom-right-radius: 0 !important;
    border-top-right-radius: 0 !important
}

.ivu-input-group-prepend .ivu--select .ivu--select-selection,
.ivu-input-group>.ivu-input:first-child .ivu--select .ivu--select-selection,
.ivu-input-group>span>.ivu-input:first-child .ivu--select .ivu--select-selection {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.ivu-input-group-prepend {
    border-right: 0
}

.ivu-input-group-append {
    border-left: 0
}

.ivu-input-group-append,
.ivu-input-group>.ivu-input:last-child {
    border-bottom-left-radius: 0 !important;
    border-top-left-radius: 0 !important
}

.ivu-input-group-append .ivu--select .ivu--select-selection,
.ivu-input-group>.ivu-input:last-child .ivu--select .ivu--select-selection {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.ivu-input-group-large .ivu-input,
.ivu-input-group-large>.ivu-input-group-append,
.ivu-input-group-large>.ivu-input-group-prepend {
    font-size: 16px;
    padding: 6px 7px;
    height: 40px
}

.ivu-input-group-small .ivu-input,
.ivu-input-group-small>.ivu-input-group-append,
.ivu-input-group-small>.ivu-input-group-prepend {
    padding: 1px 7px;
    height: 24px;
    border-radius: 3px
}

.ivu-form-item-error .ivu-input {
    border: 1px solid #ed4014
}

.ivu-form-item-error .ivu-input:hover {
    border-color: #ed4014
}

.ivu-form-item-error .ivu-input:focus {
    border-color: #ed4014;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(237, 64, 20, .2);
    box-shadow: 0 0 0 2px rgba(237, 64, 20, .2)
}

.ivu-form-item-error .ivu-input-icon {
    color: #ed4014
}

.ivu-form-item-error .ivu-input-group-append,
.ivu-form-item-error .ivu-input-group-prepend {
    background-color: #fff;
    border: 1px solid #ed4014
}

.ivu-form-item-error .ivu-input-group-append .ivu-select-selection,
.ivu-form-item-error .ivu-input-group-prepend .ivu-select-selection {
    background-color: inherit;
    border: 1px solid transparent
}

.ivu-form-item-error .ivu-input-group-prepend {
    border-right: 0
}

.ivu-form-item-error .ivu-input-group-append {
    border-left: 0
}

.ivu-form-item-error .ivu-transfer .ivu-input {
    display: inline-block;
    width: 100%;
    height: 32px;
    line-height: 1.5;
    padding: 4px 7px;
    font-size: 14px;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    color: #515a6e;
    background-color: #fff;
    background-image: none;
    position: relative;
    cursor: text;
    -webkit-transition: border .2s ease-in-out, background .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out
}

.ivu-form-item-error .ivu-transfer .ivu-input::-moz-placeholder {
    color: #c5c8ce;
    opacity: 1
}

.ivu-form-item-error .ivu-transfer .ivu-input:-ms-input-placeholder {
    color: #c5c8ce
}

.ivu-form-item-error .ivu-transfer .ivu-input::-webkit-input-placeholder {
    color: #c5c8ce
}

.ivu-form-item-error .ivu-transfer .ivu-input:hover {
    border-color: #57a3f3
}

.ivu-form-item-error .ivu-transfer .ivu-input:focus {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-form-item-error .ivu-transfer .ivu-input[disabled],
fieldset[disabled] .ivu-form-item-error .ivu-transfer .ivu-input {
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc
}

.ivu-form-item-error .ivu-transfer .ivu-input[disabled]:hover,
fieldset[disabled] .ivu-form-item-error .ivu-transfer .ivu-input:hover {
    border-color: #e3e5e8
}

textarea.ivu-form-item-error .ivu-transfer .ivu-input {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    vertical-align: bottom;
    font-size: 14px
}

.ivu-form-item-error .ivu-transfer .ivu-input-large {
    font-size: 16px;
    padding: 6px 7px;
    height: 40px
}

.ivu-form-item-error .ivu-transfer .ivu-input-small {
    padding: 1px 7px;
    height: 24px;
    border-radius: 3px
}

.ivu-form-item-error .ivu-transfer .ivu-input-no-border {
    border-radius: 0;
    border-color: transparent
}

.ivu-form-item-error .ivu-transfer .ivu-input-no-border:hover {
    border-color: transparent
}

.ivu-form-item-error .ivu-transfer .ivu-input-no-border:focus {
    border-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-form-item-error .ivu-transfer .ivu-input-no-border[disabled] {
    background-color: transparent
}

.ivu-form-item-error .ivu-transfer .ivu-input-no-border[disabled]:hover {
    border-color: transparent
}

.ivu-form-item-error .ivu-transfer .ivu-input-icon {
    color: #808695
}

.ivu-form-item-validating .ivu-input-icon-validate {
    display: inline-block
}

.ivu-form-item-validating .ivu-input-icon+.ivu-input {
    padding-right: 32px
}

.ivu-slider {
    line-height: normal
}

.ivu-slider-wrap {
    width: 100%;
    height: 4px;
    margin: 16px 0;
    background-color: #e8eaec;
    border-radius: 3px;
    vertical-align: middle;
    position: relative;
    cursor: pointer
}

.ivu-slider-button-wrap {
    width: 18px;
    height: 18px;
    text-align: center;
    background-color: transparent;
    position: absolute;
    top: -5px;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%)
}

.ivu-slider-button-wrap .ivu-tooltip {
    display: block;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.ivu-slider-button {
    width: 12px;
    height: 12px;
    border: 2px solid #57a3f3;
    border-radius: 50%;
    background-color: #fff;
    -webkit-transition: all .2s linear;
    transition: all .2s linear;
    outline: 0
}

.ivu-slider-button-dragging,
.ivu-slider-button:focus,
.ivu-slider-button:hover {
    border-color: #2d8cf0;
    -webkit-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5)
}

.ivu-slider-button:hover {
    cursor: -webkit-grab;
    cursor: grab
}

.ivu-slider-button-dragging,
.ivu-slider-button-dragging:hover {
    cursor: -webkit-grabbing;
    cursor: grabbing
}

.ivu-slider-bar {
    height: 4px;
    background: #57a3f3;
    border-radius: 3px;
    position: absolute
}

.ivu-slider-stop {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #fff;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%)
}

.ivu-slider-marks {
    top: 0;
    left: 12px;
    width: 18px;
    height: 100%
}

.ivu-slider-marks-item {
    position: absolute;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    font-size: 14px;
    color: #808695;
    margin-top: 15px
}

.ivu-slider-disabled {
    cursor: not-allowed
}

.ivu-slider-disabled .ivu-slider-wrap {
    background-color: #ccc;
    cursor: not-allowed
}

.ivu-slider-disabled .ivu-slider-bar {
    background-color: #ccc
}

.ivu-slider-disabled .ivu-slider-button {
    border-color: #ccc
}

.ivu-slider-disabled .ivu-slider-button-dragging,
.ivu-slider-disabled .ivu-slider-button:hover {
    border-color: #ccc
}

.ivu-slider-disabled .ivu-slider-button:hover {
    cursor: not-allowed
}

.ivu-slider-disabled .ivu-slider-button-dragging,
.ivu-slider-disabled .ivu-slider-button-dragging:hover {
    cursor: not-allowed
}

.ivu-slider-input .ivu-slider-wrap {
    width: auto;
    margin-right: 100px
}

.ivu-slider-input .ivu-input-number {
    float: right;
    margin-top: -14px
}

.selectDropDown {
    width: auto;
    padding: 0;
    white-space: nowrap;
    overflow: visible
}

.ivu-cascader {
    line-height: normal
}

.ivu-cascader-rel {
    display: inline-block;
    width: 100%;
    position: relative
}

.ivu-cascader .ivu-input {
    padding-right: 24px;
    display: block;
    cursor: pointer
}

.ivu-cascader-disabled .ivu-input {
    cursor: not-allowed
}

.ivu-cascader-label {
    width: 100%;
    height: 100%;
    line-height: 32px;
    padding: 0 7px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    cursor: pointer;
    font-size: 14px;
    position: absolute;
    left: 0;
    top: 0
}

.ivu-cascader-size-large .ivu-cascader-label {
    line-height: 36px;
    font-size: 14px
}

.ivu-cascader-size-small .ivu-cascader-label {
    line-height: 26px
}

.ivu-cascader .ivu-cascader-arrow:nth-of-type(1) {
    display: none;
    cursor: pointer
}

.ivu-cascader:hover .ivu-cascader-arrow:nth-of-type(1) {
    display: inline-block
}

.ivu-cascader-show-clear:hover .ivu-cascader-arrow:nth-of-type(2) {
    display: none
}

.ivu-cascader-arrow {
    position: absolute;
    top: 50%;
    right: 8px;
    line-height: 1;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    font-size: 14px;
    color: #808695;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-cascader-visible .ivu-cascader-arrow:nth-of-type(2) {
    -webkit-transform: translateY(-50%) rotate(180deg);
    -ms-transform: translateY(-50%) rotate(180deg);
    transform: translateY(-50%) rotate(180deg)
}

.ivu-cascader .ivu-select-dropdown {
    width: auto;
    padding: 0;
    white-space: nowrap;
    overflow: visible
}

.ivu-cascader .ivu-cascader-menu-item {
    margin: 0;
    line-height: normal;
    padding: 7px 16px;
    clear: both;
    color: #515a6e;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    -webkit-transition: background .2s ease-in-out;
    transition: background .2s ease-in-out
}

.ivu-cascader .ivu-cascader-menu-item:hover {
    background: #f3f3f3
}

.ivu-cascader .ivu-cascader-menu-item-focus {
    background: #f3f3f3
}

.ivu-cascader .ivu-cascader-menu-item-disabled {
    color: #c5c8ce;
    cursor: not-allowed
}

.ivu-cascader .ivu-cascader-menu-item-disabled:hover {
    color: #c5c8ce;
    background-color: #fff;
    cursor: not-allowed
}

.ivu-cascader .ivu-cascader-menu-item-selected,
.ivu-cascader .ivu-cascader-menu-item-selected:hover {
    color: #2d8cf0
}

.ivu-cascader .ivu-cascader-menu-item-divided {
    margin-top: 5px;
    border-top: 1px solid #e8eaec
}

.ivu-cascader .ivu-cascader-menu-item-divided:before {
    content: '';
    height: 5px;
    display: block;
    margin: 0 -16px;
    background-color: #fff;
    position: relative;
    top: -7px
}

.ivu-cascader .ivu-cascader-menu-item-enter {
    color: #2d8cf0;
    font-weight: 700;
    float: right
}

.ivu-cascader .ivu-cascader-large .ivu-cascader-menu-item {
    padding: 7px 16px 8px;
    font-size: 14px !important
}

@-moz-document url-prefix() {
    .ivu-cascader .ivu-cascader-menu-item {
        white-space: normal
    }
}

.ivu-cascader .ivu-select-item span {
    color: #ed4014
}

.ivu-cascader-dropdown {
    padding: 5px 0
}

.ivu-cascader-dropdown .ivu-select-dropdown-list {
    max-height: 190px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: auto
}

.ivu-cascader-not-found-tip {
    padding: 5px 0;
    text-align: center;
    color: #c5c8ce
}

.ivu-cascader-not-found-tip li:not([class^=ivu-]) {
    list-style: none;
    margin-bottom: 0
}

.ivu-cascader-not-found .ivu-select-dropdown {
    width: inherit
}

.ivu-cascader-menu {
    display: inline-block;
    min-width: 100px;
    height: 180px;
    margin: 0;
    padding: 5px 0 !important;
    vertical-align: top;
    list-style: none;
    border-right: 1px solid #e8eaec;
    overflow: auto
}

.ivu-cascader-menu:last-child {
    border-right-color: transparent;
    margin-right: -1px
}

.ivu-cascader-menu .ivu-cascader-menu-item {
    position: relative;
    padding-right: 36px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-cascader-menu .ivu-cascader-menu-item i {
    font-size: 12px;
    position: absolute;
    right: 15px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%)
}

.ivu-cascader-menu .ivu-cascader-menu-item-loading {
    margin-top: -6px
}

.ivu-cascader-menu .ivu-cascader-menu-item-active {
    background-color: #f3f3f3;
    color: #2d8cf0
}

.ivu-cascader-transfer {
    z-index: 1060;
    width: auto;
    padding: 0;
    white-space: nowrap;
    overflow: visible
}

.ivu-cascader-transfer .ivu-cascader-menu-item {
    margin: 0;
    line-height: normal;
    padding: 7px 16px;
    clear: both;
    color: #515a6e;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    -webkit-transition: background .2s ease-in-out;
    transition: background .2s ease-in-out
}

.ivu-cascader-transfer .ivu-cascader-menu-item:hover {
    background: #f3f3f3
}

.ivu-cascader-transfer .ivu-cascader-menu-item-focus {
    background: #f3f3f3
}

.ivu-cascader-transfer .ivu-cascader-menu-item-disabled {
    color: #c5c8ce;
    cursor: not-allowed
}

.ivu-cascader-transfer .ivu-cascader-menu-item-disabled:hover {
    color: #c5c8ce;
    background-color: #fff;
    cursor: not-allowed
}

.ivu-cascader-transfer .ivu-cascader-menu-item-selected,
.ivu-cascader-transfer .ivu-cascader-menu-item-selected:hover {
    color: #2d8cf0
}

.ivu-cascader-transfer .ivu-cascader-menu-item-divided {
    margin-top: 5px;
    border-top: 1px solid #e8eaec
}

.ivu-cascader-transfer .ivu-cascader-menu-item-divided:before {
    content: '';
    height: 5px;
    display: block;
    margin: 0 -16px;
    background-color: #fff;
    position: relative;
    top: -7px
}

.ivu-cascader-transfer .ivu-cascader-menu-item-enter {
    color: #2d8cf0;
    font-weight: 700;
    float: right
}

.ivu-cascader-transfer .ivu-cascader-large .ivu-cascader-menu-item {
    padding: 7px 16px 8px;
    font-size: 14px !important
}

@-moz-document url-prefix() {
    .ivu-cascader-transfer .ivu-cascader-menu-item {
        white-space: normal
    }
}

.ivu-cascader-transfer .ivu-select-item span {
    color: #ed4014
}

.ivu-cascader-transfer .ivu-cascader-menu-item {
    padding-right: 24px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-cascader-transfer .ivu-cascader-menu-item-active {
    background-color: #f3f3f3;
    color: #2d8cf0
}

.ivu-form-item-error .ivu-cascader-arrow {
    color: #ed4014
}

.ivu-transfer {
    position: relative;
    line-height: 1.5
}

.ivu-transfer-list {
    display: inline-block;
    width: 180px;
    height: 210px;
    font-size: 14px;
    vertical-align: middle;
    position: relative;
    padding-top: 35px
}

.ivu-transfer-list-with-footer {
    padding-bottom: 35px
}

.ivu-transfer-list-header {
    padding: 8px 16px;
    background: #f9fafc;
    color: #515a6e;
    border: 1px solid #dcdee2;
    border-bottom: 1px solid #e8eaec;
    border-radius: 6px 6px 0 0;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%
}

.ivu-transfer-list-header-title {
    cursor: pointer
}

.ivu-transfer-list-header>span {
    padding-left: 4px
}

.ivu-transfer-list-header-count {
    margin: 0 !important;
    float: right
}

.ivu-transfer-list-body {
    height: 100%;
    border: 1px solid #dcdee2;
    border-top: none;
    border-radius: 0 0 6px 6px;
    position: relative;
    overflow: hidden
}

.ivu-transfer-list-body-with-search {
    padding-top: 34px
}

.ivu-transfer-list-body-with-footer {
    border-radius: 0
}

.ivu-transfer-list-content {
    height: 100%;
    padding: 4px 0;
    overflow: auto
}

.ivu-transfer-list-content-item {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.ivu-transfer-list-content-item>span {
    padding-left: 4px
}

.ivu-transfer-list-content-not-found {
    display: none;
    text-align: center;
    color: #c5c8ce
}

li.ivu-transfer-list-content-not-found:only-child {
    display: block
}

.ivu-transfer-list-body-with-search .ivu-transfer-list-content {
    padding: 6px 0 0
}

.ivu-transfer-list-body-search-wrapper {
    padding: 8px 8px 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0
}

.ivu-transfer-list-search {
    position: relative
}

.ivu-transfer-list-footer {
    border: 1px solid #dcdee2;
    border-top: none;
    border-radius: 0 0 6px 6px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    zoom: 1
}

.ivu-transfer-list-footer:after,
.ivu-transfer-list-footer:before {
    content: "";
    display: table
}

.ivu-transfer-list-footer:after {
    clear: both;
    visibility: hidden;
    font-size: 0;
    height: 0
}

.ivu-transfer-operation {
    display: inline-block;
    margin: 0 16px;
    vertical-align: middle
}

.ivu-transfer-operation .ivu-btn {
    display: block;
    min-width: 24px
}

.ivu-transfer-operation .ivu-btn:first-child {
    margin-bottom: 12px
}

.ivu-transfer-operation .ivu-btn span i,
.ivu-transfer-operation .ivu-btn span span {
    vertical-align: middle
}

.ivu-transfer-list-content-item {
    margin: 0;
    line-height: normal;
    padding: 7px 16px;
    clear: both;
    color: #515a6e;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    -webkit-transition: background .2s ease-in-out;
    transition: background .2s ease-in-out
}

.ivu-transfer-list-content-item:hover {
    background: #f3f3f3
}

.ivu-transfer-list-content-item-focus {
    background: #f3f3f3
}

.ivu-transfer-list-content-item-disabled {
    color: #c5c8ce;
    cursor: not-allowed
}

.ivu-transfer-list-content-item-disabled:hover {
    color: #c5c8ce;
    background-color: #fff;
    cursor: not-allowed
}

.ivu-transfer-list-content-item-selected,
.ivu-transfer-list-content-item-selected:hover {
    color: #2d8cf0
}

.ivu-transfer-list-content-item-divided {
    margin-top: 5px;
    border-top: 1px solid #e8eaec
}

.ivu-transfer-list-content-item-divided:before {
    content: '';
    height: 5px;
    display: block;
    margin: 0 -16px;
    background-color: #fff;
    position: relative;
    top: -7px
}

.ivu-transfer-list-content-item-enter {
    color: #2d8cf0;
    font-weight: 700;
    float: right
}

.ivu-transfer-large .ivu-transfer-list-content-item {
    padding: 7px 16px 8px;
    font-size: 14px !important
}

@-moz-document url-prefix() {
    .ivu-transfer-list-content-item {
        white-space: normal
    }
}

.ivu-table {
    width: inherit;
    height: 100%;
    max-width: 100%;
    overflow: hidden;
    color: #515a6e;
    font-size: 14px;
    background-color: #fff;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.ivu-table-wrapper {
    position: relative;
    overflow: hidden
}

.ivu-table-wrapper-with-border {
    border: 1px solid #dcdee2;
    border-bottom: 0;
    border-right: 0
}

.ivu-table-summary {
    border-top: 1px solid #e8eaec
}

.ivu-table-summary tr td {
    background-color: #f8f8f9
}

.ivu-table-with-summary .ivu-table-tbody tr:last-child td {
    border-bottom: none
}

.ivu-table-resize-line {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 1px;
    border-right: 1px dashed #e8eaec
}

.ivu-table-hide {
    opacity: 0
}

.ivu-table:before {
    content: '';
    width: 100%;
    height: 1px;
    position: absolute;
    left: 0;
    bottom: 0;
    background-color: #dcdee2;
    z-index: 4
}

.ivu-table-border:after {
    content: '';
    width: 1px;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    background-color: #dcdee2;
    z-index: 3
}

.ivu-table-footer,
.ivu-table-title {
    height: 48px;
    line-height: 48px;
    border-bottom: 1px solid #e8eaec
}

.ivu-table-footer {
    border-bottom: none
}

.ivu-table-header {
    overflow: hidden
}

.ivu-table-header thead tr th {
    position: relative;
    height: 100%;
    padding: 8px 0
}

.ivu-table-header-resizable {
    position: absolute;
    width: 10px;
    height: 100%;
    bottom: 0;
    right: -5px;
    cursor: col-resize;
    z-index: 1
}

.ivu-table-overflowX {
    overflow-x: scroll
}

.ivu-table-overflowY {
    overflow-y: scroll
}

.ivu-table-tip {
    overflow-x: auto;
    overflow-y: hidden
}

.ivu-table-with-fixed-top.ivu-table-with-footer .ivu-table-footer {
    border-top: 1px solid #dcdee2
}

.ivu-table-with-fixed-top.ivu-table-with-footer tbody tr:last-child td {
    border-bottom: none
}

.ivu-table td,
.ivu-table th {
    min-width: 0;
    height: 48px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
    text-overflow: ellipsis;
    vertical-align: middle;
    border-bottom: 1px solid #e8eaec
}

.ivu-table th {
    height: 40px;
    white-space: nowrap;
    overflow: hidden;
    background-color: #f8f8f9
}

.ivu-table td {
    background-color: #fff;
    -webkit-transition: background-color .2s ease-in-out;
    transition: background-color .2s ease-in-out
}

td.ivu-table-column-left,
th.ivu-table-column-left {
    text-align: left
}

td.ivu-table-column-center,
th.ivu-table-column-center {
    text-align: center
}

td.ivu-table-column-right,
th.ivu-table-column-right {
    text-align: right
}

.ivu-table table {
    table-layout: fixed
}

.ivu-table-border td,
.ivu-table-border th {
    border-right: 1px solid #e8eaec
}

.ivu-table-cell {
    padding-left: 18px;
    padding-right: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.ivu-table-cell-ellipsis {
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.ivu-table-cell-tooltip {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.ivu-table-cell-tooltip-content {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ivu-table-cell-with-expand {
    height: 47px;
    line-height: 47px;
    padding: 0;
    text-align: center
}

.ivu-table-cell-expand {
    cursor: pointer;
    -webkit-transition: -webkit-transform .2s ease-in-out;
    transition: -webkit-transform .2s ease-in-out;
    transition: transform .2s ease-in-out;
    transition: transform .2s ease-in-out, -webkit-transform .2s ease-in-out
}

.ivu-table-cell-expand i {
    font-size: 14px
}

.ivu-table-cell-expand-expanded {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg)
}

.ivu-table-cell-sort {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.ivu-table-cell-with-selection .ivu-checkbox-wrapper {
    margin-right: 0
}

.ivu-table-cell-tree {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 1px solid #dcdee2;
    border-radius: 2px;
    background-color: #fff;
    line-height: 12px;
    cursor: pointer;
    vertical-align: middle;
    -webkit-transition: color .2s ease-in-out, border-color .2s ease-in-out;
    transition: color .2s ease-in-out, border-color .2s ease-in-out
}

.ivu-table-cell-tree-empty {
    cursor: default;
    color: transparent;
    background-color: transparent;
    border-color: transparent
}

.ivu-table-cell-tree:hover {
    color: #2d8cf0;
    border-color: #2d8cf0
}

.ivu-table-cell-tree-empty:hover {
    color: transparent;
    border-color: transparent
}

.ivu-table-cell-tree-loading {
    cursor: default;
    background-color: transparent;
    border-color: transparent
}

.ivu-table-cell-tree-loading:hover {
    border-color: transparent
}

.ivu-table-cell-tree-level {
    display: inline-block;
    height: 16px
}

.ivu-table-cell-slot-inline {
    display: inline
}

.ivu-table-cell-slot-inline-block {
    display: inline-block
}

.ivu-table-hidden {
    visibility: hidden
}

th .ivu-table-cell {
    display: inline-block;
    word-wrap: normal;
    vertical-align: middle
}

td.ivu-table-expanded-cell {
    padding: 20px 50px;
    background: #f8f8f9
}

.ivu-table-stripe .ivu-table-body tr:nth-child(2n) td,
.ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n) td {
    background-color: #f8f8f9
}

.ivu-table-stripe .ivu-table-body tr.ivu-table-row-hover td,
.ivu-table-stripe .ivu-table-fixed-body tr.ivu-table-row-hover td {
    background-color: #ebf7ff
}

tr.ivu-table-row-hover td {
    background-color: #ebf7ff
}

.ivu-table-large {
    font-size: 16px
}

.ivu-table-large th {
    height: 48px
}

.ivu-table-large td {
    height: 60px
}

.ivu-table-large-footer,
.ivu-table-large-title {
    height: 60px;
    line-height: 60px
}

.ivu-table-large .ivu-table-cell-with-expand {
    height: 59px;
    line-height: 59px
}

.ivu-table-large .ivu-table-cell-with-expand i {
    font-size: 16px
}

.ivu-table-small {
    font-size: 12px
}

.ivu-table-small th {
    height: 32px
}

.ivu-table-small td {
    height: 40px
}

.ivu-table-small-footer,
.ivu-table-small-title {
    height: 40px;
    line-height: 40px
}

.ivu-table-small .ivu-table-cell-with-expand {
    height: 39px;
    line-height: 39px
}

.ivu-table-row-highlight td,
.ivu-table-stripe .ivu-table-body tr.ivu-table-row-highlight:nth-child(2n) td,
.ivu-table-stripe .ivu-table-fixed-body tr.ivu-table-row-highlight:nth-child(2n) td,
tr.ivu-table-row-highlight.ivu-table-row-hover td {
    background-color: #ebf7ff
}

.ivu-table-fixed,
.ivu-table-fixed-right {
    position: absolute;
    top: 0;
    left: 0;
    -webkit-box-shadow: 2px 0 6px -2px rgba(0, 0, 0, .2);
    box-shadow: 2px 0 6px -2px rgba(0, 0, 0, .2)
}

.ivu-table-fixed-right::before,
.ivu-table-fixed::before {
    content: '';
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 4
}

.ivu-table-fixed-right {
    top: 0;
    left: auto;
    right: 0;
    -webkit-box-shadow: -2px 0 6px -2px rgba(0, 0, 0, .2);
    box-shadow: -2px 0 6px -2px rgba(0, 0, 0, .2)
}

.ivu-table-fixed-right-header {
    position: absolute;
    top: -1px;
    right: 0;
    background-color: #f8f8f9;
    border-top: 1px solid #dcdee2;
    border-bottom: 1px solid #e8eaec
}

.ivu-table-fixed-header {
    overflow: hidden
}

.ivu-table-fixed-header thead tr th {
    position: relative;
    height: 100%;
    padding: 8px 0
}

.ivu-table-fixed-body {
    overflow: hidden;
    position: relative;
    z-index: 3
}

.ivu-table-fixed-shadow {
    width: 1px;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    -webkit-box-shadow: 1px 0 6px rgba(0, 0, 0, .2);
    box-shadow: 1px 0 6px rgba(0, 0, 0, .2);
    overflow: hidden;
    z-index: 1
}

.ivu-table-sort {
    display: inline-block;
    width: 14px;
    height: 12px;
    margin-top: -1px;
    vertical-align: middle;
    overflow: hidden;
    cursor: pointer;
    position: relative
}

.ivu-table-sort i {
    display: block;
    height: 6px;
    line-height: 6px;
    overflow: hidden;
    position: absolute;
    color: #c5c8ce;
    -webkit-transition: color .2s ease-in-out;
    transition: color .2s ease-in-out;
    font-size: 16px
}

.ivu-table-sort i:hover {
    color: inherit
}

.ivu-table-sort i.on {
    color: #2d8cf0
}

.ivu-table-sort i:first-child {
    top: 0
}

.ivu-table-sort i:last-child {
    bottom: 0
}

.ivu-table-filter {
    display: inline-block;
    cursor: pointer;
    position: relative
}

.ivu-table-filter i {
    color: #c5c8ce;
    -webkit-transition: color .2s ease-in-out;
    transition: color .2s ease-in-out
}

.ivu-table-filter i:hover {
    color: inherit
}

.ivu-table-filter i.on {
    color: #2d8cf0
}

.ivu-table-filter-list {
    padding: 8px 0 0
}

.ivu-table-filter-list-item {
    padding: 0 12px 8px
}

.ivu-table-filter-list-item .ivu-checkbox-wrapper+.ivu-checkbox-wrapper {
    margin: 0
}

.ivu-table-filter-list-item label {
    display: block
}

.ivu-table-filter-list-item label>span {
    margin-right: 4px
}

.ivu-table-filter-list ul {
    padding-bottom: 8px
}

.ivu-table-filter-list .ivu-table-filter-select-item {
    margin: 0;
    line-height: normal;
    padding: 7px 16px;
    clear: both;
    color: #515a6e;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    -webkit-transition: background .2s ease-in-out;
    transition: background .2s ease-in-out
}

.ivu-table-filter-list .ivu-table-filter-select-item:hover {
    background: #f3f3f3
}

.ivu-table-filter-list .ivu-table-filter-select-item-focus {
    background: #f3f3f3
}

.ivu-table-filter-list .ivu-table-filter-select-item-disabled {
    color: #c5c8ce;
    cursor: not-allowed
}

.ivu-table-filter-list .ivu-table-filter-select-item-disabled:hover {
    color: #c5c8ce;
    background-color: #fff;
    cursor: not-allowed
}

.ivu-table-filter-list .ivu-table-filter-select-item-selected,
.ivu-table-filter-list .ivu-table-filter-select-item-selected:hover {
    color: #2d8cf0
}

.ivu-table-filter-list .ivu-table-filter-select-item-divided {
    margin-top: 5px;
    border-top: 1px solid #e8eaec
}

.ivu-table-filter-list .ivu-table-filter-select-item-divided:before {
    content: '';
    height: 5px;
    display: block;
    margin: 0 -16px;
    background-color: #fff;
    position: relative;
    top: -7px
}

.ivu-table-filter-list .ivu-table-filter-select-item-enter {
    color: #2d8cf0;
    font-weight: 700;
    float: right
}

.ivu-table-filter-list .ivu-table-large .ivu-table-filter-select-item {
    padding: 7px 16px 8px;
    font-size: 14px !important
}

@-moz-document url-prefix() {
    .ivu-table-filter-list .ivu-table-filter-select-item {
        white-space: normal
    }
}

.ivu-table-filter-footer {
    padding: 4px;
    border-top: 1px solid #e8eaec;
    overflow: hidden
}

.ivu-table-filter-footer button:first-child {
    float: left
}

.ivu-table-filter-footer button:last-child {
    float: right
}

.ivu-table-tip table {
    width: 100%
}

.ivu-table-tip table td {
    text-align: center
}

.ivu-table-expanded-hidden {
    visibility: hidden
}

.ivu-table-context-menu {
    position: absolute
}

.ivu-table-popper {
    min-width: 0;
    text-align: left
}

.ivu-table-popper .ivu-poptip-body {
    padding: 0
}

.ivu-dropdown {
    display: inline-block
}

.ivu-dropdown .ivu-select-dropdown {
    overflow: visible;
    max-height: none
}

.ivu-dropdown .ivu-dropdown {
    width: 100%
}

.ivu-dropdown-rel {
    position: relative
}

.ivu-dropdown-rel-user-select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.ivu-dropdown-menu {
    min-width: 100px
}

.ivu-dropdown-transfer {
    width: auto
}

.ivu-dropdown-item-selected,
.ivu-dropdown-item.ivu-dropdown-item-selected:hover {
    background: #f0faff
}

.ivu-dropdown-item {
    margin: 0;
    line-height: normal;
    padding: 7px 16px;
    clear: both;
    color: #515a6e;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    -webkit-transition: background .2s ease-in-out;
    transition: background .2s ease-in-out
}

.ivu-dropdown-item:hover {
    background: #f3f3f3
}

.ivu-dropdown-item-focus {
    background: #f3f3f3
}

.ivu-dropdown-item-disabled {
    color: #c5c8ce;
    cursor: not-allowed
}

.ivu-dropdown-item-disabled:hover {
    color: #c5c8ce;
    background-color: #fff;
    cursor: not-allowed
}

.ivu-dropdown-item-selected,
.ivu-dropdown-item-selected:hover {
    color: #2d8cf0
}

.ivu-dropdown-item-divided {
    margin-top: 5px;
    border-top: 1px solid #e8eaec
}

.ivu-dropdown-item-divided:before {
    content: '';
    height: 5px;
    display: block;
    margin: 0 -16px;
    background-color: #fff;
    position: relative;
    top: -7px
}

.ivu-dropdown-item-enter {
    color: #2d8cf0;
    font-weight: 700;
    float: right
}

.ivu-dropdown-large .ivu-dropdown-item {
    padding: 7px 16px 8px;
    font-size: 14px !important
}

@-moz-document url-prefix() {
    .ivu-dropdown-item {
        white-space: normal
    }
}

.ivu-tabs {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    color: #515a6e;
    zoom: 1
}

.ivu-tabs:after,
.ivu-tabs:before {
    content: "";
    display: table
}

.ivu-tabs:after {
    clear: both;
    visibility: hidden;
    font-size: 0;
    height: 0
}

.ivu-tabs-bar {
    outline: 0
}

.ivu-tabs-ink-bar {
    height: 2px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background-color: #2d8cf0;
    position: absolute;
    left: 0;
    bottom: 1px;
    z-index: 1;
    -webkit-transition: -webkit-transform .3s ease-in-out;
    transition: -webkit-transform .3s ease-in-out;
    transition: transform .3s ease-in-out;
    transition: transform .3s ease-in-out, -webkit-transform .3s ease-in-out;
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0
}

.ivu-tabs-bar {
    border-bottom: 1px solid #dcdee2;
    margin-bottom: 16px
}

.ivu-tabs-nav-container {
    margin-bottom: -1px;
    line-height: 1.5;
    font-size: 14px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    zoom: 1
}

.ivu-tabs-nav-container:after,
.ivu-tabs-nav-container:before {
    content: "";
    display: table
}

.ivu-tabs-nav-container:after {
    clear: both;
    visibility: hidden;
    font-size: 0;
    height: 0
}

.ivu-tabs-nav-container:focus {
    outline: 0
}

.ivu-tabs-nav-container:focus .ivu-tabs-tab-focused {
    border-color: #57a3f3 !important
}

.ivu-tabs-nav-container-scrolling {
    padding-left: 32px;
    padding-right: 32px
}

.ivu-tabs-nav-wrap {
    overflow: hidden;
    margin-bottom: -1px
}

.ivu-tabs-nav-scroll {
    overflow: hidden;
    white-space: nowrap
}

.ivu-tabs-nav-right {
    float: right;
    margin-left: 5px
}

.ivu-tabs-nav-next,
.ivu-tabs-nav-prev {
    width: 32px;
    text-align: center;
    position: absolute;
    line-height: 32px;
    cursor: pointer
}

.ivu-tabs-nav-next i,
.ivu-tabs-nav-prev i {
    font-size: 16px
}

.ivu-tabs-nav-prev {
    left: 0
}

.ivu-tabs-nav-next {
    right: 0
}

.ivu-tabs-nav-scrollable {
    padding: 0 32px
}

.ivu-tabs-nav-scroll-disabled {
    display: none
}

.ivu-tabs-nav {
    padding-left: 0;
    margin: 0;
    float: left;
    list-style: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    -webkit-transition: -webkit-transform .5s ease-in-out;
    transition: -webkit-transform .5s ease-in-out;
    transition: transform .5s ease-in-out;
    transition: transform .5s ease-in-out, -webkit-transform .5s ease-in-out
}

.ivu-tabs-nav:after,
.ivu-tabs-nav:before {
    display: table;
    content: " "
}

.ivu-tabs-nav:after {
    clear: both
}

.ivu-tabs-nav .ivu-tabs-tab-disabled {
    pointer-events: none;
    cursor: default;
    color: #ccc
}

.ivu-tabs-nav .ivu-tabs-tab {
    display: inline-block;
    height: 100%;
    padding: 8px 16px;
    margin-right: 16px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
    text-decoration: none;
    position: relative;
    -webkit-transition: color .3s ease-in-out;
    transition: color .3s ease-in-out
}

.ivu-tabs-nav .ivu-tabs-tab:hover {
    color: #57a3f3
}

.ivu-tabs-nav .ivu-tabs-tab:active {
    color: #2b85e4
}

.ivu-tabs-nav .ivu-tabs-tab .ivu-icon {
    width: 14px;
    height: 14px;
    margin-right: 8px
}

.ivu-tabs-nav .ivu-tabs-tab-active {
    color: #2d8cf0
}

.ivu-tabs-mini .ivu-tabs-nav-container {
    font-size: 14px
}

.ivu-tabs-mini .ivu-tabs-tab {
    margin-right: 0;
    padding: 8px 16px;
    font-size: 12px
}

.ivu-tabs .ivu-tabs-content-animated {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    will-change: transform;
    -webkit-transition: -webkit-transform .3s ease-in-out;
    transition: -webkit-transform .3s ease-in-out;
    transition: transform .3s ease-in-out;
    transition: transform .3s ease-in-out, -webkit-transform .3s ease-in-out
}

.ivu-tabs .ivu-tabs-tabpane {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    width: 100%;
    -webkit-transition: opacity .3s;
    transition: opacity .3s;
    opacity: 1;
    outline: 0
}

.ivu-tabs .ivu-tabs-tabpane-inactive {
    opacity: 0;
    height: 0
}

.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-nav-container {
    height: 32px
}

.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-ink-bar {
    visibility: hidden
}

.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab {
    margin: 0;
    margin-right: 4px;
    height: 31px;
    padding: 5px 16px 4px;
    border: 1px solid #dcdee2;
    border-bottom: 0;
    border-radius: 4px 4px 0 0;
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    background: #f8f8f9
}

.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab-active {
    height: 32px;
    padding-bottom: 5px;
    background: #fff;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    border-color: #dcdee2;
    color: #2d8cf0
}

.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-nav-wrap {
    margin-bottom: 0
}

.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab .ivu-tabs-close {
    width: 0;
    height: 22px;
    font-size: 22px;
    margin-right: 0;
    color: #999;
    text-align: right;
    vertical-align: middle;
    overflow: hidden;
    position: relative;
    top: -1px;
    -webkit-transform-origin: 100% 50%;
    -ms-transform-origin: 100% 50%;
    transform-origin: 100% 50%;
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab .ivu-tabs-close:hover {
    color: #444
}

.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab-active .ivu-tabs-close,
.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab:hover .ivu-tabs-close {
    width: 22px;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    margin-right: -6px
}

.ivu-tabs-context-menu {
    position: absolute
}

.ivu-tabs-no-animation>.ivu-tabs-content {
    -webkit-transform: none !important;
    -ms-transform: none !important;
    transform: none !important
}

.ivu-tabs-no-animation>.ivu-tabs-content>.ivu-tabs-tabpane-inactive {
    display: none
}

.ivu-menu {
    display: block;
    margin: 0;
    padding: 0;
    outline: 0;
    list-style: none;
    color: #515a6e;
    font-size: 14px;
    position: relative;
    z-index: 900
}

.ivu-menu-horizontal {
    height: 60px;
    line-height: 60px
}

.ivu-menu-horizontal.ivu-menu-light:after {
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    background: #dcdee2;
    position: absolute;
    bottom: 0;
    left: 0
}

.ivu-menu-vertical.ivu-menu-light:after {
    content: '';
    display: block;
    width: 1px;
    height: 100%;
    background: #dcdee2;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 1
}

.ivu-menu-light {
    background: #fff
}

.ivu-menu-dark {
    background: #515a6e
}

.ivu-menu-primary {
    background: #2d8cf0
}

.ivu-menu-item {
    display: block;
    outline: 0;
    list-style: none;
    font-size: 14px;
    position: relative;
    z-index: 1;
    cursor: pointer;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

a.ivu-menu-item {
    color: inherit
}

a.ivu-menu-item:active,
a.ivu-menu-item:hover {
    color: inherit
}

.ivu-menu-item>i {
    margin-right: 6px
}

.ivu-menu-submenu-title span>i,
.ivu-menu-submenu-title>i {
    margin-right: 8px
}

.ivu-menu-horizontal .ivu-menu-item,
.ivu-menu-horizontal .ivu-menu-submenu {
    float: left;
    padding: 0 20px;
    position: relative;
    cursor: pointer;
    z-index: 3;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-menu-light.ivu-menu-horizontal .ivu-menu-item,
.ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu {
    height: inherit;
    line-height: inherit;
    border-bottom: 2px solid transparent;
    color: #515a6e
}

.ivu-menu-light.ivu-menu-horizontal .ivu-menu-item-active,
.ivu-menu-light.ivu-menu-horizontal .ivu-menu-item:hover,
.ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu-active,
.ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu:hover {
    color: #2d8cf0;
    border-bottom: 2px solid #2d8cf0
}

.ivu-menu-dark.ivu-menu-horizontal .ivu-menu-item,
.ivu-menu-dark.ivu-menu-horizontal .ivu-menu-submenu {
    color: rgba(255, 255, 255, .7)
}

.ivu-menu-dark.ivu-menu-horizontal .ivu-menu-item-active,
.ivu-menu-dark.ivu-menu-horizontal .ivu-menu-item:hover,
.ivu-menu-dark.ivu-menu-horizontal .ivu-menu-submenu-active,
.ivu-menu-dark.ivu-menu-horizontal .ivu-menu-submenu:hover {
    color: #fff
}

.ivu-menu-primary.ivu-menu-horizontal .ivu-menu-item,
.ivu-menu-primary.ivu-menu-horizontal .ivu-menu-submenu {
    color: #fff
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown {
    min-width: 100%;
    width: auto;
    max-height: none
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item {
    height: auto;
    line-height: normal;
    border-bottom: 0;
    float: none
}

.ivu-menu-item-group {
    line-height: normal
}

.ivu-menu-item-group-title {
    height: 30px;
    line-height: 30px;
    padding-left: 8px;
    font-size: 12px;
    color: #999
}

.ivu-menu-item-group>ul {
    padding: 0 !important;
    list-style: none !important
}

.ivu-menu-vertical .ivu-menu-item,
.ivu-menu-vertical .ivu-menu-submenu-title {
    padding: 14px 24px;
    position: relative;
    cursor: pointer;
    z-index: 1;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-menu-vertical .ivu-menu-item:hover,
.ivu-menu-vertical .ivu-menu-submenu-title:hover {
    color: #2d8cf0
}

.ivu-menu-vertical .ivu-menu-submenu-title-icon {
    position: absolute;
    top: 50%;
    right: 24px;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%)
}

.ivu-menu-submenu-title-icon {
    -webkit-transition: -webkit-transform .2s ease-in-out;
    transition: -webkit-transform .2s ease-in-out;
    transition: transform .2s ease-in-out;
    transition: transform .2s ease-in-out, -webkit-transform .2s ease-in-out
}

.ivu-menu-horizontal .ivu-menu-opened>*>.ivu-menu-submenu-title-icon {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg)
}

.ivu-menu-vertical .ivu-menu-opened>*>.ivu-menu-submenu-title-icon {
    -webkit-transform: translateY(-50%) rotate(180deg);
    -ms-transform: translateY(-50%) rotate(180deg);
    transform: translateY(-50%) rotate(180deg)
}

.ivu-menu-vertical .ivu-menu-submenu-nested {
    padding-left: 20px
}

.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item {
    padding-left: 43px
}

.ivu-menu-vertical .ivu-menu-item-group-title {
    height: 48px;
    line-height: 48px;
    font-size: 14px;
    padding-left: 28px
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-group-title {
    color: rgba(255, 255, 255, .36)
}

.ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu) {
    color: #2d8cf0;
    background: #f0faff;
    z-index: 2
}

.ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu):after {
    content: '';
    display: block;
    width: 2px;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    background: #2d8cf0
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title {
    color: rgba(255, 255, 255, .7)
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu),
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu):hover,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title-active:not(.ivu-menu-submenu),
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title-active:not(.ivu-menu-submenu):hover {
    background: #363e4f
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item:hover,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title:hover {
    color: #fff;
    background: #515a6e
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu),
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title-active:not(.ivu-menu-submenu) {
    color: #2d8cf0
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item:hover {
    color: #fff;
    background: 0 0 !important
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active:hover {
    border-right: none;
    color: #fff;
    background: #2d8cf0 !important
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-child-item-active>.ivu-menu-submenu-title {
    color: #fff
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened {
    background: #363e4f
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened .ivu-menu-submenu-title {
    background: #515a6e
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened .ivu-menu-submenu-has-parent-submenu .ivu-menu-submenu-title {
    background: 0 0
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item {
    margin: 0;
    line-height: normal;
    padding: 7px 16px;
    clear: both;
    color: #515a6e;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    -webkit-transition: background .2s ease-in-out;
    transition: background .2s ease-in-out
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item:hover {
    background: #f3f3f3
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item-focus {
    background: #f3f3f3
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item-disabled {
    color: #c5c8ce;
    cursor: not-allowed
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item-disabled:hover {
    color: #c5c8ce;
    background-color: #fff;
    cursor: not-allowed
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item-selected,
.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item-selected:hover {
    color: #2d8cf0
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item-divided {
    margin-top: 5px;
    border-top: 1px solid #e8eaec
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item-divided:before {
    content: '';
    height: 5px;
    display: block;
    margin: 0 -16px;
    background-color: #fff;
    position: relative;
    top: -7px
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item-enter {
    color: #2d8cf0;
    font-weight: 700;
    float: right
}

.ivu-menu-large .ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item {
    padding: 7px 16px 8px;
    font-size: 14px !important
}

@-moz-document url-prefix() {
    .ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item {
        white-space: normal
    }
}

.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item {
    padding: 7px 16px 8px;
    font-size: 14px !important
}

.ivu-date-picker {
    display: inline-block;
    line-height: normal
}

.ivu-date-picker-rel {
    position: relative
}

.ivu-date-picker .ivu-select-dropdown {
    width: auto;
    padding: 0;
    overflow: visible;
    max-height: none
}

.ivu-date-picker-cells {
    width: 196px;
    margin: 10px;
    white-space: normal
}

.ivu-date-picker-cells span {
    display: inline-block;
    width: 24px;
    height: 24px
}

.ivu-date-picker-cells span em {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    margin: 2px;
    font-style: normal;
    border-radius: 3px;
    text-align: center;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-date-picker-cells-header span {
    line-height: 24px;
    text-align: center;
    margin: 2px;
    color: #c5c8ce
}

.ivu-date-picker-cells-cell:hover em {
    background: #e1f0fe
}

.ivu-date-picker-cells-focused em {
    -webkit-box-shadow: 0 0 0 1px #2d8cf0 inset;
    box-shadow: 0 0 0 1px #2d8cf0 inset
}

span.ivu-date-picker-cells-cell {
    width: 28px;
    height: 28px;
    cursor: pointer
}

.ivu-date-picker-cells-cell-next-month em,
.ivu-date-picker-cells-cell-prev-month em {
    color: #c5c8ce
}

.ivu-date-picker-cells-cell-next-month:hover em,
.ivu-date-picker-cells-cell-prev-month:hover em {
    background: 0 0
}

span.ivu-date-picker-cells-cell-disabled,
span.ivu-date-picker-cells-cell-disabled:hover,
span.ivu-date-picker-cells-cell-week-label,
span.ivu-date-picker-cells-cell-week-label:hover {
    cursor: not-allowed;
    color: #c5c8ce
}

span.ivu-date-picker-cells-cell-disabled em,
span.ivu-date-picker-cells-cell-disabled:hover em,
span.ivu-date-picker-cells-cell-week-label em,
span.ivu-date-picker-cells-cell-week-label:hover em {
    color: inherit;
    background: inherit
}

span.ivu-date-picker-cells-cell-disabled,
span.ivu-date-picker-cells-cell-disabled:hover {
    background: #f7f7f7
}

.ivu-date-picker-cells-cell-today em {
    position: relative
}

.ivu-date-picker-cells-cell-today em:after {
    content: '';
    display: block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #2d8cf0;
    position: absolute;
    top: 1px;
    right: 1px
}

.ivu-date-picker-cells-cell-range {
    position: relative
}

.ivu-date-picker-cells-cell-range em {
    position: relative;
    z-index: 1
}

.ivu-date-picker-cells-cell-range:before {
    content: '';
    display: block;
    background: #e1f0fe;
    border-radius: 0;
    border: 0;
    position: absolute;
    top: 2px;
    bottom: 2px;
    left: 0;
    right: 0
}

.ivu-date-picker-cells-cell-selected em,
.ivu-date-picker-cells-cell-selected:hover em {
    background: #2d8cf0;
    color: #fff
}

span.ivu-date-picker-cells-cell-disabled.ivu-date-picker-cells-cell-selected em {
    background: #c5c8ce;
    color: #f7f7f7
}

.ivu-date-picker-cells-cell-today.ivu-date-picker-cells-cell-selected em:after {
    background: #fff
}

.ivu-date-picker-cells-show-week-numbers {
    width: 226px
}

.ivu-date-picker-cells-month,
.ivu-date-picker-cells-year {
    margin-top: 14px
}

.ivu-date-picker-cells-month span,
.ivu-date-picker-cells-year span {
    width: 40px;
    height: 28px;
    line-height: 28px;
    margin: 10px 12px;
    border-radius: 3px
}

.ivu-date-picker-cells-month span em,
.ivu-date-picker-cells-year span em {
    width: 40px;
    height: 28px;
    line-height: 28px;
    margin: 0
}

.ivu-date-picker-cells-month .ivu-date-picker-cells-cell-focused,
.ivu-date-picker-cells-year .ivu-date-picker-cells-cell-focused {
    background-color: #d5e8fc
}

.ivu-date-picker-header {
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-bottom: 1px solid #e8eaec
}

.ivu-date-picker-header-label {
    cursor: pointer;
    -webkit-transition: color .2s ease-in-out;
    transition: color .2s ease-in-out
}

.ivu-date-picker-header-label:hover {
    color: #2d8cf0
}

.ivu-date-picker-btn-pulse {
    background-color: #d5e8fc !important;
    border-radius: 4px;
    -webkit-transition: background-color .2s ease-in-out;
    transition: background-color .2s ease-in-out
}

.ivu-date-picker-prev-btn {
    float: left
}

.ivu-date-picker-prev-btn-arrow-double {
    margin-left: 10px
}

.ivu-date-picker-prev-btn-arrow-double i:after {
    content: "\F115";
    margin-left: -8px
}

.ivu-date-picker-next-btn {
    float: right
}

.ivu-date-picker-next-btn-arrow-double {
    margin-right: 10px
}

.ivu-date-picker-next-btn-arrow-double i:after {
    content: "\F11F";
    margin-left: -8px
}

.ivu-date-picker-with-range .ivu-picker-panel-body {
    min-width: 432px
}

.ivu-date-picker-with-range .ivu-picker-panel-content {
    float: left
}

.ivu-date-picker-with-range .ivu-picker-cells-show-week-numbers {
    min-width: 492px
}

.ivu-date-picker-with-week-numbers .ivu-picker-panel-body-date {
    min-width: 492px
}

.ivu-date-picker-transfer {
    z-index: 1060;
    max-height: none;
    width: auto
}

.ivu-date-picker-focused input {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-picker-panel-icon-btn {
    display: inline-block;
    width: 20px;
    height: 24px;
    line-height: 26px;
    margin-top: 2px;
    text-align: center;
    cursor: pointer;
    color: #c5c8ce;
    -webkit-transition: color .2s ease-in-out;
    transition: color .2s ease-in-out
}

.ivu-picker-panel-icon-btn:hover {
    color: #2d8cf0
}

.ivu-picker-panel-icon-btn i {
    font-size: 14px
}

.ivu-picker-panel-body-wrapper.ivu-picker-panel-with-sidebar {
    padding-left: 92px
}

.ivu-picker-panel-sidebar {
    width: 92px;
    float: left;
    margin-left: -92px;
    position: absolute;
    top: 0;
    bottom: 0;
    background: #f8f8f9;
    border-right: 1px solid #e8eaec;
    border-radius: 4px 0 0 4px;
    overflow: auto
}

.ivu-picker-panel-shortcut {
    padding: 6px 16px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ivu-picker-panel-shortcut:hover {
    background: #e8eaec
}

.ivu-picker-panel-body {
    float: left
}

.ivu-picker-confirm {
    border-top: 1px solid #e8eaec;
    text-align: right;
    padding: 8px;
    clear: both
}

.ivu-picker-confirm>span {
    color: #2d8cf0;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    float: left;
    padding: 2px 0;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-picker-confirm>span:hover {
    color: #57a3f3
}

.ivu-picker-confirm>span:active {
    color: #2b85e4
}

.ivu-picker-confirm-time {
    float: left
}

.ivu-time-picker-cells {
    min-width: 112px
}

.ivu-time-picker-cells-with-seconds {
    min-width: 168px
}

.ivu-time-picker-cells-list {
    width: 56px;
    max-height: 144px;
    float: left;
    overflow: hidden;
    border-left: 1px solid #e8eaec;
    position: relative
}

.ivu-time-picker-cells-list:hover {
    overflow-y: auto
}

.ivu-time-picker-cells-list:first-child {
    border-left: none;
    border-radius: 4px 0 0 4px
}

.ivu-time-picker-cells-list:last-child {
    border-radius: 0 4px 4px 0
}

.ivu-time-picker-cells-list ul {
    width: 100%;
    margin: 0;
    padding: 0 0 120px 0;
    list-style: none
}

.ivu-time-picker-cells-list ul li {
    width: 100%;
    height: 24px;
    line-height: 24px;
    margin: 0;
    padding: 0 0 0 16px;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    text-align: left;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
    list-style: none;
    -webkit-transition: background .2s ease-in-out;
    transition: background .2s ease-in-out
}

.ivu-time-picker-cells-cell:hover {
    background: #f3f3f3
}

.ivu-time-picker-cells-cell-disabled {
    color: #c5c8ce;
    cursor: not-allowed
}

.ivu-time-picker-cells-cell-disabled:hover {
    color: #c5c8ce;
    background-color: #fff;
    cursor: not-allowed
}

.ivu-time-picker-cells-cell-selected,
.ivu-time-picker-cells-cell-selected:hover {
    color: #2d8cf0;
    background: #f3f3f3
}

.ivu-time-picker-cells-cell-focused {
    background-color: #d5e8fc
}

.ivu-time-picker-header {
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-bottom: 1px solid #e8eaec
}

.ivu-time-picker-with-range .ivu-picker-panel-body {
    min-width: 228px
}

.ivu-time-picker-with-range .ivu-picker-panel-content {
    float: left;
    position: relative
}

.ivu-time-picker-with-range .ivu-picker-panel-content:after {
    content: '';
    display: block;
    width: 2px;
    position: absolute;
    top: 31px;
    bottom: 0;
    right: -2px;
    background: #e8eaec;
    z-index: 1
}

.ivu-time-picker-with-range .ivu-picker-panel-content-right {
    float: right
}

.ivu-time-picker-with-range .ivu-picker-panel-content-right:after {
    right: auto;
    left: -2px
}

.ivu-time-picker-with-range .ivu-time-picker-cells-list:first-child {
    border-radius: 0
}

.ivu-time-picker-with-range .ivu-time-picker-cells-list:last-child {
    border-radius: 0
}

.ivu-time-picker-with-range.ivu-time-picker-with-seconds .ivu-picker-panel-body {
    min-width: 340px
}

.ivu-picker-panel-content .ivu-picker-panel-content .ivu-time-picker-cells {
    min-width: 216px
}

.ivu-picker-panel-content .ivu-picker-panel-content .ivu-time-picker-cells-with-seconds {
    min-width: 216px
}

.ivu-picker-panel-content .ivu-picker-panel-content .ivu-time-picker-cells-with-seconds .ivu-time-picker-cells-list {
    width: 72px
}

.ivu-picker-panel-content .ivu-picker-panel-content .ivu-time-picker-cells-with-seconds .ivu-time-picker-cells-list ul li {
    padding: 0 0 0 28px
}

.ivu-picker-panel-content .ivu-picker-panel-content .ivu-time-picker-cells-list {
    width: 108px;
    max-height: 216px
}

.ivu-picker-panel-content .ivu-picker-panel-content .ivu-time-picker-cells-list:first-child {
    border-radius: 0
}

.ivu-picker-panel-content .ivu-picker-panel-content .ivu-time-picker-cells-list:last-child {
    border-radius: 0
}

.ivu-picker-panel-content .ivu-picker-panel-content .ivu-time-picker-cells-list ul {
    padding: 0 0 192px 0
}

.ivu-picker-panel-content .ivu-picker-panel-content .ivu-time-picker-cells-list ul li {
    padding: 0 0 0 46px
}

.ivu-form .ivu-form-item-label {
    text-align: right;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: #515a6e;
    line-height: 1;
    padding: 10px 12px 10px 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.ivu-form-label-left .ivu-form-item-label {
    text-align: left
}

.ivu-form-label-top .ivu-form-item-label {
    float: none;
    display: inline-block;
    padding: 0 0 10px 0
}

.ivu-form-inline .ivu-form-item {
    display: inline-block;
    margin-right: 10px;
    vertical-align: top
}

.ivu-form-item {
    margin-bottom: 24px;
    vertical-align: top;
    zoom: 1
}

.ivu-form-item:after,
.ivu-form-item:before {
    content: "";
    display: table
}

.ivu-form-item:after {
    clear: both;
    visibility: hidden;
    font-size: 0;
    height: 0
}

.ivu-form-item-content {
    position: relative;
    line-height: 32px;
    font-size: 14px
}

.ivu-form-item .ivu-form-item {
    margin-bottom: 0
}

.ivu-form-item .ivu-form-item .ivu-form-item-content {
    margin-left: 0 !important
}

.ivu-form-item-error-tip {
    position: absolute;
    top: 100%;
    left: 0;
    line-height: 1;
    padding-top: 6px;
    color: #ed4014
}

.ivu-form-item-required .ivu-form-item-label:before {
    content: '*';
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014
}

.ivu-form-hide-required-mark .ivu-form-item-required .ivu-form-item-label:before {
    display: none
}

.ivu-carousel {
    position: relative;
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent
}

.ivu-carousel-list,
.ivu-carousel-track {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.ivu-carousel-list {
    position: relative;
    display: block;
    overflow: hidden;
    margin: 0;
    padding: 0
}

.ivu-carousel-track {
    position: relative;
    top: 100%;
    left: 0;
    display: block;
    overflow: hidden;
    z-index: 1
}

.ivu-carousel-track.higher {
    z-index: 2;
    top: 0
}

.ivu-carousel-item {
    float: left;
    height: 100%;
    min-height: 1px;
    display: block
}

.ivu-carousel-arrow {
    border: none;
    outline: 0;
    padding: 0;
    margin: 0;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    display: none;
    position: absolute;
    top: 50%;
    z-index: 10;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transition: .2s;
    transition: .2s;
    background-color: rgba(31, 45, 61, .11);
    color: #fff;
    text-align: center;
    font-size: 1em;
    font-family: inherit;
    line-height: inherit
}

.ivu-carousel-arrow:hover {
    background-color: rgba(31, 45, 61, .5)
}

.ivu-carousel-arrow>* {
    vertical-align: baseline
}

.ivu-carousel-arrow.left {
    left: 16px
}

.ivu-carousel-arrow.right {
    right: 16px
}

.ivu-carousel-arrow-always {
    display: inherit
}

.ivu-carousel-arrow-hover {
    display: inherit;
    opacity: 0
}

.ivu-carousel:hover .ivu-carousel-arrow-hover {
    opacity: 1
}

.ivu-carousel-dots {
    z-index: 10;
    display: none;
    position: relative;
    list-style: none;
    text-align: center;
    padding: 0;
    width: 100%;
    height: 17px
}

.ivu-carousel-dots-inside {
    display: block;
    position: absolute;
    bottom: 3px
}

.ivu-carousel-dots-outside {
    display: block;
    margin-top: 3px
}

.ivu-carousel-dots li {
    position: relative;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    margin: 0 2px;
    padding: 7px 0;
    cursor: pointer
}

.ivu-carousel-dots li button {
    border: 0;
    cursor: pointer;
    background: #8391a5;
    opacity: .3;
    display: block;
    width: 16px;
    height: 3px;
    border-radius: 1px;
    outline: 0;
    font-size: 0;
    color: transparent;
    -webkit-transition: all .5s;
    transition: all .5s
}

.ivu-carousel-dots li button.radius {
    width: 6px;
    height: 6px;
    border-radius: 50%
}

.ivu-carousel-dots li:hover>button {
    opacity: .7
}

.ivu-carousel-dots li.ivu-carousel-active>button {
    opacity: 1;
    width: 24px
}

.ivu-carousel-dots li.ivu-carousel-active>button.radius {
    width: 6px
}

.ivu-rate {
    display: inline-block;
    margin: 0;
    padding: 0;
    font-size: 20px;
    vertical-align: middle;
    font-weight: 400;
    font-style: normal
}

.ivu-rate-disabled .ivu-rate-star-content:before,
.ivu-rate-disabled .ivu-rate-star:before {
    cursor: default
}

.ivu-rate-disabled .ivu-rate-star:hover {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1)
}

.ivu-rate-star-full,
.ivu-rate-star-zero {
    position: relative
}

.ivu-rate-star-first {
    position: absolute;
    left: 0;
    top: 0;
    width: 50%;
    height: 100%;
    overflow: hidden;
    opacity: 0
}

.ivu-rate-star-first,
.ivu-rate-star-second {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    color: #e9e9e9;
    cursor: pointer
}

.ivu-rate-star-chart {
    display: inline-block;
    margin: 0;
    padding: 0;
    margin-right: 8px;
    position: relative;
    font-family: Ionicons;
    -webkit-transition: all .3s ease;
    transition: all .3s ease
}

.ivu-rate-star-chart:hover {
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1)
}

.ivu-rate-star-chart.ivu-rate-star-full .ivu-rate-star-first,
.ivu-rate-star-chart.ivu-rate-star-full .ivu-rate-star-second {
    color: #f5a623
}

.ivu-rate-star-chart.ivu-rate-star-half .ivu-rate-star-first {
    opacity: 1;
    color: #f5a623
}

.ivu-rate-star {
    display: inline-block;
    margin: 0;
    padding: 0;
    margin-right: 8px;
    position: relative;
    font-family: Ionicons;
    -webkit-transition: all .3s ease;
    transition: all .3s ease
}

.ivu-rate-star:hover {
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1)
}

.ivu-rate-star-content:before,
.ivu-rate-star:before {
    color: #e9e9e9;
    cursor: pointer;
    content: "\F2BF";
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    display: block
}

.ivu-rate-star-content {
    position: absolute;
    left: 0;
    top: 0;
    width: 50%;
    height: 100%;
    overflow: hidden
}

.ivu-rate-star-content:before {
    color: transparent
}

.ivu-rate-star-full:before,
.ivu-rate-star-half .ivu-rate-star-content:before {
    color: #f5a623
}

.ivu-rate-star-full:hover:before,
.ivu-rate-star-half:hover .ivu-rate-star-content:before {
    color: #f7b84f
}

.ivu-rate-text {
    margin-left: 8px;
    vertical-align: middle;
    display: inline-block;
    font-size: 14px
}

.ivu-upload input[type=file] {
    display: none
}

.ivu-upload-list {
    margin-top: 8px
}

.ivu-upload-list-file {
    padding: 4px;
    color: #515a6e;
    border-radius: 4px;
    -webkit-transition: background-color .2s ease-in-out;
    transition: background-color .2s ease-in-out;
    overflow: hidden;
    position: relative
}

.ivu-upload-list-file>span {
    cursor: pointer;
    -webkit-transition: color .2s ease-in-out;
    transition: color .2s ease-in-out
}

.ivu-upload-list-file>span i {
    display: inline-block;
    width: 12px;
    height: 12px;
    color: #515a6e;
    text-align: center
}

.ivu-upload-list-file:hover {
    background: #f3f3f3
}

.ivu-upload-list-file:hover>span {
    color: #2d8cf0
}

.ivu-upload-list-file:hover>span i {
    color: #515a6e
}

.ivu-upload-list-file:hover .ivu-upload-list-remove {
    opacity: 1
}

.ivu-upload-list-remove {
    opacity: 0;
    font-size: 18px;
    cursor: pointer;
    float: right;
    margin-right: 4px;
    color: #999;
    -webkit-transition: all .2s ease;
    transition: all .2s ease
}

.ivu-upload-list-remove:hover {
    color: #444
}

.ivu-upload-select {
    display: inline-block
}

.ivu-upload-drag {
    background: #fff;
    border: 1px dashed #dcdee2;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    -webkit-transition: border-color .2s ease;
    transition: border-color .2s ease
}

.ivu-upload-drag:hover {
    border: 1px dashed #2d8cf0
}

.ivu-upload-dragOver {
    border: 2px dashed #2d8cf0
}

.ivu-tree {
    position: relative
}

.ivu-tree ul {
    list-style: none;
    margin: 0;
    padding: 0;
    font-size: 14px
}

.ivu-tree ul.ivu-dropdown-menu {
    padding: 0
}

.ivu-tree ul li {
    list-style: none;
    margin: 8px 0;
    padding: 0;
    white-space: nowrap;
    outline: 0
}

.ivu-tree ul li.ivu-dropdown-item {
    margin: 0;
    padding: 7px 16px;
    white-space: nowrap
}

.ivu-tree li ul {
    margin: 0;
    padding: 0 0 0 18px
}

.ivu-tree-title {
    display: inline-block;
    margin: 0;
    padding: 0 4px;
    border-radius: 3px;
    cursor: pointer;
    vertical-align: top;
    color: #515a6e;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-tree-title:hover {
    background-color: #eaf4fe
}

.ivu-tree-title-selected,
.ivu-tree-title-selected:hover {
    background-color: #d5e8fc
}

.ivu-tree-arrow {
    cursor: pointer;
    width: 12px;
    text-align: center;
    display: inline-block
}

.ivu-tree-arrow i {
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    font-size: 14px;
    vertical-align: middle
}

.ivu-tree-arrow-open i {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg)
}

.ivu-tree .ivu-checkbox-wrapper {
    margin-right: 4px;
    margin-left: 4px
}

.ivu-tree-context-menu {
    position: absolute
}

.ivu-avatar {
    display: inline-block;
    text-align: center;
    background: #ccc;
    color: #fff;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    vertical-align: middle;
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 50%
}

.ivu-avatar-image {
    background: 0 0
}

.ivu-avatar .ivu-icon {
    position: relative;
    top: -1px
}

.ivu-avatar.ivu-avatar-icon {
    font-size: 18px
}

.ivu-avatar-large {
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%
}

.ivu-avatar-large.ivu-avatar-icon {
    font-size: 24px
}

.ivu-avatar-large .ivu-icon {
    position: relative;
    top: -2px
}

.ivu-avatar-small {
    width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 50%
}

.ivu-avatar-small.ivu-avatar-icon {
    font-size: 14px
}

.ivu-avatar-square {
    border-radius: 4px
}

.ivu-avatar>img {
    width: 100%;
    height: 100%
}

.ivu-color-picker {
    display: inline-block
}

.ivu-color-picker-hide {
    display: none
}

.ivu-color-picker-hide-drop {
    visibility: hidden
}

.ivu-color-picker-disabled {
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #ccc
}

.ivu-color-picker-disabled:hover {
    border-color: #e3e5e8
}

.ivu-color-picker>div:first-child:hover .ivu-input {
    border-color: #57a3f3
}

.ivu-color-picker>div:first-child.ivu-color-picker-disabled:hover .ivu-input {
    border-color: #e3e5e8
}

.ivu-color-picker .ivu-select-dropdown {
    padding: 0
}

.ivu-color-picker-input.ivu-input:focus {
    -webkit-box-shadow: none;
    box-shadow: none
}

.ivu-color-picker-focused {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-color-picker-rel {
    line-height: 0
}

.ivu-color-picker-color {
    width: 18px;
    height: 18px;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);
    border-radius: 2px;
    position: relative;
    top: 2px
}

.ivu-color-picker-color div {
    width: 100%;
    height: 100%;
    -webkit-box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .15);
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .15);
    border-radius: 2px
}

.ivu-color-picker-color-empty {
    background: #fff;
    overflow: hidden;
    text-align: center
}

.ivu-color-picker-color-empty i {
    font-size: 18px;
    vertical-align: baseline
}

.ivu-color-picker-color-focused {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-color-picker-large .ivu-color-picker-color {
    width: 20px;
    height: 20px;
    top: 1px
}

.ivu-color-picker-large .ivu-color-picker-color-empty i {
    font-size: 20px
}

.ivu-color-picker-small .ivu-color-picker-color {
    width: 14px;
    height: 14px;
    top: 3px
}

.ivu-color-picker-small .ivu-color-picker-color-empty i {
    font-size: 14px
}

.ivu-color-picker-picker-wrapper {
    padding: 8px 8px 0
}

.ivu-color-picker-picker-panel {
    width: 240px;
    margin: 0 auto;
    -webkit-box-sizing: initial;
    box-sizing: initial;
    position: relative
}

.ivu-color-picker-picker-alpha-slider,
.ivu-color-picker-picker-hue-slider {
    height: 10px;
    margin-top: 8px;
    position: relative
}

.ivu-color-picker-picker-colors {
    margin-top: 8px;
    overflow: hidden;
    border-radius: 2px;
    -webkit-transition: border .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out
}

.ivu-color-picker-picker-colors:focus {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-color-picker-picker-colors-wrapper {
    display: inline;
    width: 20px;
    height: 20px;
    float: left;
    position: relative
}

.ivu-color-picker-picker-colors-wrapper-color {
    outline: 0;
    display: block;
    position: absolute;
    width: 16px;
    height: 16px;
    margin: 2px;
    cursor: pointer;
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .15);
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .15)
}

.ivu-color-picker-picker-colors-wrapper-circle {
    width: 4px;
    height: 4px;
    -webkit-box-shadow: 0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0, 0, 0, .3), 0 0 1px 2px rgba(0, 0, 0, .4);
    box-shadow: 0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0, 0, 0, .3), 0 0 1px 2px rgba(0, 0, 0, .4);
    border-radius: 50%;
    -webkit-transform: translate(-2px, -2px);
    -ms-transform: translate(-2px, -2px);
    transform: translate(-2px, -2px);
    position: absolute;
    top: 10px;
    left: 10px;
    cursor: pointer
}

.ivu-color-picker-picker .ivu-picker-confirm {
    margin-top: 8px
}

.ivu-color-picker-saturation-wrapper {
    width: 100%;
    padding-bottom: 75%;
    position: relative;
    -webkit-transition: border .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out
}

.ivu-color-picker-saturation-wrapper:focus {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-color-picker-saturation,
.ivu-color-picker-saturation--black,
.ivu-color-picker-saturation--white {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.ivu-color-picker-saturation--white {
    background: -webkit-gradient(linear, left top, right top, from(#fff), to(rgba(255, 255, 255, 0)));
    background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0))
}

.ivu-color-picker-saturation--black {
    background: -webkit-gradient(linear, left bottom, left top, from(#000), to(rgba(0, 0, 0, 0)));
    background: linear-gradient(to top, #000, rgba(0, 0, 0, 0))
}

.ivu-color-picker-saturation-pointer {
    cursor: pointer;
    position: absolute
}

.ivu-color-picker-saturation-circle {
    width: 4px;
    height: 4px;
    -webkit-box-shadow: 0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0, 0, 0, .3), 0 0 1px 2px rgba(0, 0, 0, .4);
    box-shadow: 0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0, 0, 0, .3), 0 0 1px 2px rgba(0, 0, 0, .4);
    border-radius: 50%;
    -webkit-transform: translate(-2px, -2px);
    -ms-transform: translate(-2px, -2px);
    transform: translate(-2px, -2px)
}

.ivu-color-picker-hue {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-radius: 2px;
    background: -webkit-gradient(linear, left top, right top, from(red), color-stop(17%, #ff0), color-stop(33%, #0f0), color-stop(50%, #0ff), color-stop(67%, #00f), color-stop(83%, #f0f), to(red));
    background: linear-gradient(to right, red 0, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, red 100%);
    -webkit-transition: border .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out
}

.ivu-color-picker-hue:focus {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-color-picker-hue-container {
    cursor: pointer;
    margin: 0 2px;
    position: relative;
    height: 100%
}

.ivu-color-picker-hue-pointer {
    z-index: 2;
    position: absolute
}

.ivu-color-picker-hue-picker {
    cursor: pointer;
    margin-top: 1px;
    width: 4px;
    border-radius: 1px;
    height: 8px;
    -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, .6);
    box-shadow: 0 0 2px rgba(0, 0, 0, .6);
    background: #fff;
    -webkit-transform: translateX(-2px);
    -ms-transform: translateX(-2px);
    transform: translateX(-2px)
}

.ivu-color-picker-alpha {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-radius: 2px;
    -webkit-transition: border .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, -webkit-box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, box-shadow .2s ease-in-out;
    transition: border .2s ease-in-out, box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out
}

.ivu-color-picker-alpha:focus {
    border-color: #57a3f3;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, .2);
    box-shadow: 0 0 0 2px rgba(45, 140, 240, .2)
}

.ivu-color-picker-alpha-checkboard-wrap {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    border-radius: 2px
}

.ivu-color-picker-alpha-checkerboard {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==)
}

.ivu-color-picker-alpha-gradient {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-radius: 2px
}

.ivu-color-picker-alpha-container {
    cursor: pointer;
    position: relative;
    z-index: 2;
    height: 100%;
    margin: 0 3px
}

.ivu-color-picker-alpha-pointer {
    z-index: 2;
    position: absolute
}

.ivu-color-picker-alpha-picker {
    cursor: pointer;
    width: 4px;
    border-radius: 1px;
    height: 8px;
    -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, .6);
    box-shadow: 0 0 2px rgba(0, 0, 0, .6);
    background: #fff;
    margin-top: 1px;
    -webkit-transform: translateX(-2px);
    -ms-transform: translateX(-2px);
    transform: translateX(-2px)
}

.ivu-color-picker-confirm {
    margin-top: 8px;
    position: relative;
    border-top: 1px solid #e8eaec;
    text-align: right;
    padding: 8px;
    clear: both
}

.ivu-color-picker-confirm-color {
    position: absolute;
    top: 11px;
    left: 8px
}

.ivu-color-picker-confirm-color-editable {
    top: 8px;
    right: 110px
}

.ivu-auto-complete .ivu-select-not-found {
    display: none
}

.ivu-auto-complete .ivu-icon-ios-close {
    display: none
}

.ivu-auto-complete:hover .ivu-icon-ios-close {
    display: inline-block
}

.ivu-auto-complete.ivu-select-dropdown {
    max-height: none
}

.ivu-auto-complete div,
.ivu-auto-complete:focus {
    outline: 0
}

.ivu-divider {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #515a6e;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    list-style: none;
    background: #e8eaec
}

.ivu-divider,
.ivu-divider-vertical {
    margin: 0 8px;
    display: inline-block;
    height: .9em;
    width: 1px;
    vertical-align: middle;
    position: relative;
    top: -.06em
}

.ivu-divider-horizontal {
    display: block;
    height: 1px;
    width: 100%;
    min-width: 100%;
    margin: 24px 0;
    clear: both
}

.ivu-divider-horizontal.ivu-divider-with-text-center,
.ivu-divider-horizontal.ivu-divider-with-text-left,
.ivu-divider-horizontal.ivu-divider-with-text-right {
    display: table;
    white-space: nowrap;
    text-align: center;
    background: 0 0;
    font-weight: 500;
    color: #17233d;
    font-size: 16px;
    margin: 16px 0
}

.ivu-divider-horizontal.ivu-divider-with-text-center:after,
.ivu-divider-horizontal.ivu-divider-with-text-center:before,
.ivu-divider-horizontal.ivu-divider-with-text-left:after,
.ivu-divider-horizontal.ivu-divider-with-text-left:before,
.ivu-divider-horizontal.ivu-divider-with-text-right:after,
.ivu-divider-horizontal.ivu-divider-with-text-right:before {
    content: '';
    display: table-cell;
    position: relative;
    top: 50%;
    width: 50%;
    border-top: 1px solid #e8eaec;
    -webkit-transform: translateY(50%);
    -ms-transform: translateY(50%);
    transform: translateY(50%)
}

.ivu-divider-horizontal.ivu-divider-small.ivu-divider-with-text-center,
.ivu-divider-horizontal.ivu-divider-small.ivu-divider-with-text-left,
.ivu-divider-horizontal.ivu-divider-small.ivu-divider-with-text-right {
    font-size: 14px;
    margin: 8px 0
}

.ivu-divider-horizontal.ivu-divider-with-text-left .ivu-divider-inner-text,
.ivu-divider-horizontal.ivu-divider-with-text-right .ivu-divider-inner-text {
    display: inline-block;
    padding: 0 10px
}

.ivu-divider-horizontal.ivu-divider-with-text-left:before {
    top: 50%;
    width: 5%
}

.ivu-divider-horizontal.ivu-divider-with-text-left:after {
    top: 50%;
    width: 95%
}

.ivu-divider-horizontal.ivu-divider-with-text-right:before {
    top: 50%;
    width: 95%
}

.ivu-divider-horizontal.ivu-divider-with-text-right:after {
    top: 50%;
    width: 5%
}

.ivu-divider-inner-text {
    display: inline-block;
    padding: 0 24px
}

.ivu-divider-dashed {
    background: 0 0;
    border-top: 1px dashed #e8eaec
}

.ivu-divider-horizontal.ivu-divider-with-text-left.ivu-divider-dashed,
.ivu-divider-horizontal.ivu-divider-with-text-right.ivu-divider-dashed,
.ivu-divider-horizontal.ivu-divider-with-text.ivu-divider-dashed {
    border-top: 0
}

.ivu-divider-horizontal.ivu-divider-with-text-left.ivu-divider-dashed:after,
.ivu-divider-horizontal.ivu-divider-with-text-left.ivu-divider-dashed:before,
.ivu-divider-horizontal.ivu-divider-with-text-right.ivu-divider-dashed:after,
.ivu-divider-horizontal.ivu-divider-with-text-right.ivu-divider-dashed:before,
.ivu-divider-horizontal.ivu-divider-with-text.ivu-divider-dashed:after,
.ivu-divider-horizontal.ivu-divider-with-text.ivu-divider-dashed:before {
    border-style: dashed none none
}

.ivu-divider-plain.ivu-divider-with-text,
.ivu-divider-plain.ivu-divider-with-text-left,
.ivu-divider-plain.ivu-divider-with-text-right {
    color: #515a6e;
    font-weight: 400;
    font-size: 14px
}

.ivu-anchor {
    position: relative;
    padding-left: 2px
}

.ivu-anchor-wrapper {
    overflow: auto;
    padding-left: 4px;
    margin-left: -4px
}

.ivu-anchor-ink {
    position: absolute;
    height: 100%;
    left: 0;
    top: 0
}

.ivu-anchor-ink:before {
    content: ' ';
    position: relative;
    width: 2px;
    height: 100%;
    display: block;
    background-color: #e8eaec;
    margin: 0 auto
}

.ivu-anchor-ink-ball {
    display: inline-block;
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 2px solid #2d8cf0;
    background-color: #fff;
    left: 50%;
    -webkit-transition: top .2s ease-in-out;
    transition: top .2s ease-in-out;
    -webkit-transform: translate(-50%, 2px);
    -ms-transform: translate(-50%, 2px);
    transform: translate(-50%, 2px)
}

.ivu-anchor.fixed .ivu-anchor-ink .ivu-anchor-ink-ball {
    display: none
}

.ivu-anchor-link {
    padding: 8px 0 8px 16px;
    line-height: 1
}

.ivu-anchor-link-title {
    display: block;
    position: relative;
    -webkit-transition: all .3s;
    transition: all .3s;
    color: #515a6e;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 8px
}

.ivu-anchor-link-title:only-child {
    margin-bottom: 0
}

.ivu-anchor-link-active>.ivu-anchor-link-title {
    color: #2d8cf0
}

.ivu-anchor-link .ivu-anchor-link {
    padding-top: 6px;
    padding-bottom: 6px
}

.ivu-time-with-hash {
    cursor: pointer
}

.ivu-time-with-hash:hover {
    text-decoration: underline
}

.ivu-cell {
    position: relative;
    overflow: hidden
}

.ivu-cell-link,
.ivu-cell-link:active,
.ivu-cell-link:hover {
    color: inherit
}

.ivu-cell-icon {
    display: inline-block;
    margin-right: 4px;
    font-size: 14px;
    vertical-align: middle
}

.ivu-cell-icon:empty {
    display: none
}

.ivu-cell-main {
    display: inline-block;
    vertical-align: middle
}

.ivu-cell-title {
    line-height: 24px;
    font-size: 14px
}

.ivu-cell-label {
    line-height: 1.2;
    font-size: 12px;
    color: #808695
}

.ivu-cell-selected .ivu-cell-label {
    color: inherit
}

.ivu-cell-selected,
.ivu-cell.ivu-cell-selected:hover {
    background: #f0faff
}

.ivu-cell-footer {
    display: inline-block;
    position: absolute;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    top: 50%;
    right: 16px;
    color: #515a6e
}

.ivu-cell-with-link .ivu-cell-footer {
    right: 32px
}

.ivu-cell-selected .ivu-cell-footer {
    color: inherit
}

.ivu-cell-arrow {
    display: inline-block;
    position: absolute;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    top: 50%;
    right: 16px;
    font-size: 14px
}

.ivu-cell:focus {
    background: #f3f3f3;
    outline: 0
}

.ivu-cell-selected:focus {
    background: rgba(40, 123, 211, .91)
}

.ivu-cell {
    margin: 0;
    line-height: normal;
    padding: 7px 16px;
    clear: both;
    color: #515a6e;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    -webkit-transition: background .2s ease-in-out;
    transition: background .2s ease-in-out
}

.ivu-cell:hover {
    background: #f3f3f3
}

.ivu-cell-focus {
    background: #f3f3f3
}

.ivu-cell-disabled {
    color: #c5c8ce;
    cursor: not-allowed
}

.ivu-cell-disabled:hover {
    color: #c5c8ce;
    background-color: #fff;
    cursor: not-allowed
}

.ivu-cell-selected,
.ivu-cell-selected:hover {
    color: #2d8cf0
}

.ivu-cell-divided {
    margin-top: 5px;
    border-top: 1px solid #e8eaec
}

.ivu-cell-divided:before {
    content: '';
    height: 5px;
    display: block;
    margin: 0 -16px;
    background-color: #fff;
    position: relative;
    top: -7px
}

.ivu-cell-enter {
    color: #2d8cf0;
    font-weight: 700;
    float: right
}

.ivu-cell-large .ivu-cell {
    padding: 7px 16px 8px;
    font-size: 14px !important
}

@-moz-document url-prefix() {
    .ivu-cell {
        white-space: normal
    }
}

.ivu-drawer {
    width: auto;
    height: 100%;
    position: fixed;
    top: 0
}

.ivu-drawer-inner {
    position: absolute
}

.ivu-drawer-left {
    left: 0
}

.ivu-drawer-right {
    right: 0
}

.ivu-drawer-hidden {
    display: none !important
}

.ivu-drawer-wrap {
    position: fixed;
    overflow: auto;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    -webkit-overflow-scrolling: touch;
    outline: 0
}

.ivu-drawer-wrap-inner {
    position: absolute;
    overflow: hidden
}

.ivu-drawer-wrap-dragging {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.ivu-drawer-wrap * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent
}

.ivu-drawer-mask {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(55, 55, 55, .6);
    height: 100%;
    z-index: 1000
}

.ivu-drawer-mask-hidden {
    display: none
}

.ivu-drawer-mask-inner {
    position: absolute
}

.ivu-drawer-content {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    background-color: #fff;
    border: 0;
    background-clip: padding-box;
    -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, .15);
    box-shadow: 0 4px 12px rgba(0, 0, 0, .15)
}

.ivu-drawer-content-no-mask {
    pointer-events: auto
}

.ivu-drawer-header {
    border-bottom: 1px solid #e8eaec;
    padding: 14px 16px;
    line-height: 1
}

.ivu-drawer-header p,
.ivu-drawer-header-inner {
    display: inline-block;
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 16px;
    color: #17233d;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ivu-drawer-close {
    z-index: 1;
    font-size: 12px;
    position: absolute;
    right: 8px;
    top: 8px;
    overflow: hidden;
    cursor: pointer
}

.ivu-drawer-close .ivu-icon-ios-close {
    font-size: 31px;
    color: #999;
    -webkit-transition: color .2s ease;
    transition: color .2s ease;
    position: relative;
    top: 1px
}

.ivu-drawer-close .ivu-icon-ios-close:hover {
    color: #444
}

.ivu-drawer-body {
    width: 100%;
    height: calc(100% - 51px);
    padding: 16px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
    position: absolute;
    overflow: auto
}

.ivu-drawer-no-header .ivu-drawer-body {
    height: 100%
}

.ivu-drawer-no-mask {
    pointer-events: none
}

.ivu-drawer-no-mask .ivu-drawer-drag {
    pointer-events: auto
}

.ivu-drawer-drag {
    top: 0;
    height: 100%;
    width: 0;
    position: absolute
}

.ivu-drawer-drag-left {
    right: 0
}

.ivu-drawer-drag-move-trigger {
    width: 8px;
    height: 100px;
    line-height: 100px;
    position: absolute;
    top: 50%;
    background: #f3f3f3;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    border-radius: 4px/6px;
    -webkit-box-shadow: 0 0 1px 1px rgba(0, 0, 0, .2);
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, .2);
    cursor: col-resize
}

.ivu-drawer-drag-move-trigger-point {
    display: inline-block;
    width: 50%;
    -webkit-transform: translateX(50%);
    -ms-transform: translateX(50%);
    transform: translateX(50%)
}

.ivu-drawer-drag-move-trigger-point i {
    display: block;
    border-bottom: 1px solid silver;
    padding-bottom: 2px
}

.ivu-breadcrumb {
    color: #999;
    font-size: 14px
}

.ivu-breadcrumb a {
    color: #515a6e;
    -webkit-transition: color .2s ease-in-out;
    transition: color .2s ease-in-out
}

.ivu-breadcrumb a:hover {
    color: #57a3f3
}

.ivu-breadcrumb>span:last-child {
    font-weight: 700;
    color: #515a6e
}

.ivu-breadcrumb>span:last-child .ivu-breadcrumb-item-separator {
    display: none
}

.ivu-breadcrumb-item-separator {
    margin: 0 8px;
    color: #dcdee2
}

.ivu-breadcrumb-item-link>.ivu-icon+span {
    margin-left: 4px
}

.ivu-list {
    position: relative
}

.ivu-list-items {
    margin: 0;
    padding: 0;
    list-style: none
}

.ivu-list-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 12px 0
}

.ivu-list-item-content {
    color: #515a6e
}

.ivu-list-item-meta {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    font-size: 0
}

.ivu-list-item-meta-avatar {
    margin-right: 16px
}

.ivu-list-item-meta-content {
    -webkit-box-flex: 1;
    -ms-flex: 1 0;
    flex: 1 0
}

.ivu-list-item-meta-title {
    font-weight: 500;
    margin-bottom: 4px;
    color: #515a6e;
    font-size: 14px;
    line-height: 22px
}

.ivu-list-item-meta-title>a {
    color: #515a6e;
    -webkit-transition: all .2s;
    transition: all .2s
}

.ivu-list-item-meta-title>a:hover {
    color: #2d8cf0
}

.ivu-list-item-meta-description {
    color: rgba(0, 0, 0, .45);
    font-size: 14px;
    line-height: 22px
}

.ivu-list-item-action {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    margin-left: 48px;
    padding: 0;
    font-size: 0;
    list-style: none
}

.ivu-list-item-action>li {
    position: relative;
    display: inline-block;
    padding: 0 8px;
    color: rgba(0, 0, 0, .45);
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    cursor: pointer
}

.ivu-list-item-action>li:after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 14px;
    margin-top: -7px;
    background-color: #e8eaec
}

.ivu-list-item-action>li:first-child {
    padding-left: 0
}

.ivu-list-item-action>li:last-child:after {
    display: none
}

.ivu-list-header {
    background: 0 0
}

.ivu-list-footer {
    background: 0 0
}

.ivu-list-footer,
.ivu-list-header {
    padding-top: 12px;
    padding-bottom: 12px
}

.ivu-list-split .ivu-list-item {
    border-bottom: 1px solid #e8eaec
}

.ivu-list-split .ivu-list-item:last-child {
    border-bottom: none
}

.ivu-list-split .ivu-list-header {
    border-bottom: 1px solid #e8eaec
}

.ivu-list-split .ivu-list-footer {
    border-top: 1px solid #e8eaec
}

.ivu-list-large .ivu-list-item {
    padding-top: 16px;
    padding-bottom: 16px
}

.ivu-list-small .ivu-list-item {
    padding-top: 8px;
    padding-bottom: 8px
}

.ivu-list-vertical .ivu-list-item {
    -webkit-box-align: initial;
    -ms-flex-align: initial;
    align-items: initial
}

.ivu-list-vertical .ivu-list-item-main {
    display: block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.ivu-list-vertical .ivu-list-item-extra {
    margin-left: 40px
}

.ivu-list-vertical .ivu-list-item-meta {
    margin-bottom: 16px
}

.ivu-list-vertical .ivu-list-item-meta-title {
    margin-bottom: 12px;
    color: rgba(0, 0, 0, .85);
    font-size: 16px;
    line-height: 24px
}

.ivu-list-vertical .ivu-list-item-action {
    margin-top: 16px;
    margin-left: auto
}

.ivu-list-vertical .ivu-list-item-action>li {
    padding: 0 16px
}

.ivu-list-vertical .ivu-list-item-action>li:first-child {
    padding-left: 0
}

.ivu-list-item-no-flex {
    display: block
}

.ivu-list:not(.ivu-list-vertical) .ivu-list-item-no-flex .ivu-list-item-action {
    float: right
}

.ivu-list-bordered {
    border: 1px solid #dcdee2;
    border-radius: 6px
}

.ivu-list-bordered .ivu-list-header {
    padding-right: 24px;
    padding-left: 24px
}

.ivu-list-bordered .ivu-list-footer {
    padding-right: 24px;
    padding-left: 24px
}

.ivu-list-bordered .ivu-list-item {
    padding-right: 24px;
    padding-left: 24px;
    border-bottom: 1px solid #e8eaec
}

.ivu-list-bordered .ivu-list-pagination {
    margin: 16px 24px
}

.ivu-list-bordered.ivu-list-small .ivu-list-item {
    padding-right: 16px;
    padding-left: 16px
}

.ivu-list-bordered.ivu-list-small .ivu-list-footer,
.ivu-list-bordered.ivu-list-small .ivu-list-header {
    padding: 8px 16px
}

.ivu-list-bordered.ivu-list-large .ivu-list-footer,
.ivu-list-bordered.ivu-list-large .ivu-list-header {
    padding: 16px 24px
}

@media screen and (max-width:768px) {
    .ivu-list-item-action {
        margin-left: 24px
    }

    .ivu-list-vertical .ivu-list-item-extra {
        margin-left: 24px
    }
}

@media screen and (max-width:576px) {
    .ivu-list-item {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap
    }

    .ivu-list-item-action {
        margin-left: 12px
    }

    .ivu-list-vertical .ivu-list-item {
        -ms-flex-wrap: wrap-reverse;
        flex-wrap: wrap-reverse
    }

    .ivu-list-vertical .ivu-list-item-main {
        min-width: 220px
    }

    .ivu-list-vertical .ivu-list-item-extra {
        margin: auto auto 16px
    }
}