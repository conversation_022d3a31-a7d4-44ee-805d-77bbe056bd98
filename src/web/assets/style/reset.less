* {
	scrollbar-arrow-color: #f0f5fb;
	/*立体滚动条亮边的颜色*/
	scrollbar-highlight-color: #f0f5fb;
	/*滚动条空白部分的颜色*/
	scrollbar-shadow-color: #f0f5fb;
	/*立体滚动条阴影的颜色*/
	scrollbar-darkshadow-color: #f0f5fb;
	/*立体滚动条强阴影的颜色*/
	scrollbar-track-color: #fff;
	/*立体滚动条背景颜色*/
	scrollbar-base-color: #d3e2f1;
	/*滚动条的基本颜色*/
	scrollbar-face-color: #d3e2f1;
}
::-webkit-scrollbar {
	/*滚动条整体部分，其中的属性有width,height,background,border等（就和一个块级元素一样）（位置1）*/
	width: 5px;
	height: 5px;
}

::-webkit-scrollbar-button {
	/*滚动条两端的按钮，可以用display:none让其不显示，也可以添加背景图片，颜色改变显示效果（位置2）*/
	// background: #F0F5FB;
	display: none;
}

::-webkit-scrollbar-track {
	/*外层轨道，可以用display:none让其不显示，也可以添加背景图片，颜色改变显示效果（位置3）*/
	// background: #F0F5FB;
	display: none;
}

::-webkit-scrollbar-track-piece {
	/*内层轨道，滚动条中间部分（位置4）*/
	// background: #F0F5FB;
	background: #fff;
}

::-webkit-scrollbar-thumb {
	/*滚动条里面可以拖动的那部分（位置5）*/
	/* background:#D3E2F1; */
	// background: rgb(163, 182, 207);
	background: #cfd9f0;
	border-radius: 3px;
}

::-webkit-scrollbar-corner {
	/*边角（位置6）*/
	background: #f0f5fb;
}

::-webkit-scrollbar-resizer {
	/*定义右下角拖动块的样式（位置7）*/
	background: #f0f5fb;
}
