.card {
	background-color: #fff;
	border: 1px solid #e9edf5;
	border-radius: 4px;
	transition: all 0.5s ease;
	padding: 10px;
	display: flex;
	flex-direction: column;
	overflow: auto;
	&:hover {
		box-shadow: 0 2px 10px 0 rgb(7 31 88 / 20%);
	}
	.top {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		.title {
			min-width: 50px;
			height: 20px;
			line-height: 20px;
			border-left: 5px solid #2b5fda;
			padding-left: 8px;
			margin-right: 20px;
			&.wt-80 {
				min-width: 80px;
			}
		}
		.btns {
			display: flex;
			line-height: 30px;
			align-items: flex-start;
			flex-flow: row wrap;
			position: relative;
			margin-top: 10px;
			.item {
				padding: 0 0 10px 0;
				display: flex;
				.lable {
					width: 110px;
					text-align-last: justify;
				}
				/deep/ .gxx-input-container {
					&.wt-100 {
						width: 100px;
					}
					&.wt-80 {
						width: 80px;
					}
				}
				/deep/ .gxx-input-container,
				.gxx-switch-container,
				.gxx-select-wrap {
					margin-right: 20px;
				}
			}
		}
	}
	.bottom {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: auto;
	}
	.save-btn {
		cursor: pointer;
		width: 400px;
		height: 30px;
		line-height: 30px;
		margin: 0 auto;
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 16px;
		color: #ffffff;
		text-align: center;
		background: #13ba5a;
		border-radius: 4px 4px 4px 4px;
	}
}
