// 菜单组件样式重置
.gxx-menu--vertical {
	.gxx-menu {
		background: linear-gradient(224deg, #f4f8fd 0%, #e7f4fd 100%);
		box-shadow: none;
		color: #2b3646;
	}
	.gxx-menu-item {
		color: #2b3646;
		&:focus,
		&:hover,
		&.is-active {
			background-color: #cfe6fd;
			color: inherit;
		}
	}
}


// 按钮样式重置
.gxx-button {
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: normal;
	font-size: 16px;
	color: #FFFFFF;
	font-style: normal;
	text-transform: none;
}
.gxx-button-default {
	background-color: #0089FF;
	&:hover {
		background-color: #007EEA;
	}
	&:active {
		background-color: #006ED6;
	}
}
.gxx-button-info {
	color: #5F709A;
	border: 1px solid #CFD9F0;
	&:hover {
		background-color: #CFD9F0;
	}
	&:active {
		background-color: #BDC7E0;
	}
}
