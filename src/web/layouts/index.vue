<template>
	<div class="layouts">
		<Header></Header>
		<div class="main">
			<Menu :isCollapse="isCollapse" @setCollapse="setCollapse"></Menu>
			<div class="box">
				<router-view></router-view>
			</div>
		</div>
	</div>
</template>

<script>
import Header from './head/index'
import Menu from './menu/index'
export default {
	name: 'Layouts',
	components: {
		Header,
		Menu
	},
	data() {
		return {
			isCollapse: false
		}
	},
	methods: {
		setCollapse() {
			this.isCollapse = !this.isCollapse
		}
	}
}
</script>

<style lang="less" scoped>
.layouts {
	height: 100%;
	flex: 1;
	display: flex;
	flex-direction: column;
	background: #f0f5ff;
	.main {
		flex: 1;
		height: calc(100% - 55px);
		display: flex;
		.box {
			overflow: auto;
			display: flex;
			flex: 1;
			margin: 20px;
			border: 1px solid #e9edf5;
			border-radius: 4px;
			background-color: #fff;
			position: relative;
			background: url('~@/web/assets/images/main_right_bottom_bg.png') no-repeat right bottom;
			background-color: #fff;
		}
	}
}
</style>
