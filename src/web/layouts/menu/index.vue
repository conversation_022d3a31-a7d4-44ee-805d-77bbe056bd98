<template>
	<div class="left-menu" :class="{ wt: isCollapse }">
		<gxx-menu unique-opened :default-active="currentRouteUrl" :collapse="isCollapse" class="menu-vertical-demo">
			<template v-for="route in routes">
				<template v-if="route.children && route.children.length">
					<gxx-submenu :key="route.url" :index="route.url" :popper-append-to-body="true">
						<div class="submenu-item" slot="title">
							<div class="submenu-icon" :class="[!isCollapse ? 'mr' : '', route.icon]"></div>
							<span>{{ route.title }}</span>
							<div v-show="!isCollapse" class="submenu-arrow"></div>
							<!-- <img src="@/web/assets/images/arrow_right.png" class="submenu-arrow" /> -->
						</div>
						<gxx-menu-item v-for="childRoute in route.children" :index="childRoute.url" :key="childRoute.url" @click="routeChange(childRoute.url)">
							<div v-if="isActive(childRoute.url)" class="submenu-tag"></div>
							<span>{{ childRoute.title }}</span>
						</gxx-menu-item>
					</gxx-submenu>
				</template>
				<template v-else>
					<gxx-menu-item :key="route.url" :index="route.url" @click="routeChange(route.url)">
						<div v-if="isActive(route.url)" class="submenu-tag"></div>
						<div class="submenu-icon" :class="[!isCollapse ? 'mr' : '', route.icon]"></div>
						<span>{{ route.title }}</span>
					</gxx-menu-item>
				</template>
			</template>
		</gxx-menu>
		<div class="collapse" @click="setCollapse"></div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
// import { getProperty } from '@/web/api/index'
const routePrex = '/operation/'
export default {
	name: 'Menu',
	props: {
		isCollapse: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		...mapGetters({
			menuList: 'menu/menuList',
			accessUser: 'user/userInfo'
		}),
		currentRouteUrl() {
			return this.$route.path
		}
	},
	watch: {
		menuList: {
			handler() {
				this.initMenu()
			},
			deep: true
		}
	},
	data() {
		return {
			routes: []
		}
	},
	created() {
		this.initMenu()
	},
	methods: {
		initMenu() {
			const newRotes = this.handleRoutes(this.menuList)
			this.routes = this.handleMenu(newRotes)
		},
		setCollapse() {
			this.$emit('setCollapse')
		},
		handleRoutes(routes) {
			const newRotes = []
			for (let i = 0; i < routes.length; i++) {
				const item = routes[i]
				const children = item.children
				if (children && children.length) {
					return this.handleRoutes(children)
				}
				const meta = item.meta || {}
				if (meta.isMenu && meta.role && meta.role.includes(Number(this.accessUser.role))) {
					newRotes.push(item)
				}
			}
			return newRotes
		},
		handleMenu(routes) {
			const newRotes = []
			const temp = {}
			for (let i = 0; i < routes.length; i++) {
				const item = routes[i]
				const route = {
					url: routePrex + item.path,
					title: item.meta?.title || '',
					icon: item.meta?.icon || ''
				}
				if (item.meta && item.meta.isChild) {
					route.title = item.meta.parentTitle
					if (!temp[route.title]) {
						temp[route.title] = []
					}
					temp[route.title].push({ ...route, title: item.meta.title })
					if (temp[route.title].length == 1) {
						newRotes.push(route)
					}
				} else {
					newRotes.push(route)
				}
			}
			for (let i = 0; i < newRotes.length; i++) {
				const item = newRotes[i]
				if (temp[item.title]) {
					item.children = temp[item.title]
				}
			}
			return newRotes
		},
		// getProperty() {
		// 	const loadings = this.$GxxLoading({ txt: '正在加载中，请稍等...', isCloseBtn: true })
		// 	const prop = 'sysPlatform'
		// 	getProperty(prop)
		// 		.then((res) => {
		// 			const { data } = res
		// 			this.routes[1].isShow = data[prop] != 'Windows'
		// 		})
		// 		.catch(() => {
		// 			this.$baseTip({ message: '获取系统属性失败', type: 'error' })
		// 			this.routes[1].isShow = false
		// 		})
		// 		.finally(() => {
		// 			loadings.close()
		// 		})
		// },
		routeChange(routeUrl) {
			if (this.currentRouteUrl === routeUrl) {
				return
			}
			this.$router.push(routeUrl)
		},
		isActive(routeUrl) {
			return this.currentRouteUrl === routeUrl
		}
	}
}
</script>

<style lang="less" scoped>
@import url('~@/common/assets/sprites/menu.less');
.left-menu {
	height: 100%;
	width: 200px;
	position: relative;
	&.wt {
		width: 50px;
		.collapse::before {
			content: '>>';
		}
	}
	.menu-vertical-demo {
		height: 100%;
		background: url('~@/web/assets/images/menu_bottom.png') no-repeat center bottom, linear-gradient(224deg, #f4f8fd 0%, #e7f4fd 100%);
		overflow: hidden auto;
		position: relative;
		border-right: solid 1px #cee0f0;
		.submenu-arrow {
			content: url('~@/web/assets/images/arrow_right.png');
			position: absolute;
			top: 50%;
			right: 20px;
			margin-top: -10px;
			transition: transform 0.3s;
		}
		.is-opened {
			.submenu-arrow {
				content: url('~@/web/assets/images/arrow_down.png');
			}
		}
		.gxx-submenu .submenu-item {
			display: flex;
			align-items: center;
		}
		.gxx-menu-item {
			display: flex;
			align-items: center;
			height: 50px;
			color: #2b3646;
			&.is-active,
			&:hover {
				background-color: #cfe6fd;
			}
		}
		.submenu-tag {
			width: 4px;
			height: 50px;
			background: linear-gradient(180deg, #70caff 0%, #87a9fc 100%);
			position: absolute;
			left: 0px;
		}
		.submenu-icon {
			width: 18px;
			height: 18px;
			margin-left: 0;
			&.sprite-menu-leftbar_info {
				.sprite-menu-leftbar_info;
			}
			&.sprite-menu-leftbar_maintain {
				.sprite-menu-leftbar_maintain;
			}
			&.sprite-menu-leftbar_config {
				.sprite-menu-leftbar_config;
			}
			&.sprite-menu-leftbar_network {
				.sprite-menu-leftbar_network;
			}
			&.sprite-menu-leftbar_device {
				.sprite-menu-leftbar_device;
			}
			&.mr {
				margin-right: 10px;
			}
		}
		/deep/ .gxx-submenu__title {
			color: #2b3646;
			padding: initial;
			display: flex;
			&.is-active {
				background-color: #cfe6fd;
			}
		}
	}
	/deep/ .gxx-submenu__icon-arrow.gxx-svg-icon {
		display: none;
	}
	.collapse {
		z-index: 999;
		position: absolute;
		top: 12px;
		left: calc(100% + 1px);
		width: 28px;
		height: 28px;
		background: #f4f8fd;
		border: 1px solid #cee0f0;
		border-left: none;
		border-top-right-radius: 50%;
		border-bottom-right-radius: 50%;
		cursor: pointer;
		&::before {
			content: '<<';
			width: 100%;
			position: absolute;
			top: 50%;
			left: 2px;
			opacity: 0.3;
			transform: translateY(-50%);
			letter-spacing: -5px;
			font-size: 18px;
		}
	}
}
</style>
