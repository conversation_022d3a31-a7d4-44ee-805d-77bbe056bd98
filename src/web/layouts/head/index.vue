<template>
	<div class="head">
		<div class="top-left">
			<!-- <img src="~@/common/assets/images/logo.png" class="logo" alt="logo" /> -->
			<img src="~@/web/assets/images/header.png" class="bg" />
			<h1 class="title">{{ logoTitle }}</h1>
		</div>
		<div class="top-right">
			<div class="user-view">
				<div class="user-info">
					<img v-if="userInfo.avatar" :src="userInfo.avatar" class="avatar" alt="avatar" />
					<img v-else src="~@/web/assets/images/portrait.png" class="avatar" alt="avatar" />
					<span class="user-name" :title="userInfo.name">{{ userInfo.name }}</span>
					<img src="~@/web/assets/images/arrow_user.png" class="user-caret-icons el-icon-caret-bottom" />
					<i class="user-caret-icons el-icon-caret-top"></i>
				</div>
				<ul class="user-function">
					<li @click="openPersonalCenter">
						<i class="layout-user"></i>
						<span>个人中心</span>
					</li>
					<li @click="evVersion">
						<i class="layout-version"></i>
						<span>系统版本</span>
					</li>
					<li @click="logout">
						<i class="layout-out"></i>
						<span>退出</span>
					</li>
				</ul>
			</div>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import version from '../components/version'
import personalCenter from '../components/personal_center'
export default {
	name: 'Head',
	computed: {
		...mapGetters({
			userInfo: 'user/userInfo'
		})
	},
	data() {
		return {
			logoTitle: ''
		}
	},
	created() {
		this.getTerminalConfigInfo()
	},
	mounted() {
		this.$EventBus.$on('update-title', this.updateLogoTitle)
	},
	methods: {
		updateLogoTitle(title) {
			this.logoTitle = title
		},
		getTerminalConfigInfo() {
			this.$store.dispatch('config/getTerminalConfigInfo').then((data) => {
				this.logoTitle = data.operationInfo?.logoName || this.logoTitle
			})
		},
		//打开个人中心
		openPersonalCenter() {
			this.$GxxDialog({
				title: '个人中心',
				showBtn: true,
				wrapClass: 'common-dialog',
				component: personalCenter
			})
		},
		// 退出系统
		logout() {
			this.$store.dispatch('user/removeToken')
			// this.$router.push('Login')
		},
		// 查看系统版本
		evVersion() {
			this.$GxxDialog({
				title: '系统版本',
				showBtn: false,
				wrapClass: 'common-dialog',
				component: version
			})
		}
	},
	beforeDestroy() {
		this.$EventBus.$off('update-title', this.updateLogoTitle)
	}
}
</script>

<style lang="less" scoped>
@import url('~@/common/assets/sprites/layout.less');
.head {
	height: 55px;
	width: 100%;
	background: linear-gradient(90deg, #2b5fda, #3174f0);
	box-sizing: border-box;
	padding: 0 0 0 16px;
	position: relative;
	display: flex;
	justify-content: space-between;
	.top-left {
		line-height: 55px;
		display: flex;
		align-items: center;
		position: relative;
		.bg {
			position: absolute;
			z-index: 1;
		}
		.title {
			font-size: 24px;
			font-weight: bold;
			padding-left: 8px;
			min-width: 150px;
			color: #fff;
			letter-spacing: 10px;
			text-shadow: 0px 3px 6px rgba(0, 38, 128, 0.4);
			z-index: 2;
		}
	}
	.top-right {
		line-height: 55px;
		align-items: center;
		position: absolute;
		// right: 20px;
		right: 0;
		top: 0;
		z-index: 1;
		.user-view {
			position: relative;
			background-image: url('~@/web/assets/images/top_right_bg.png');
			background-size: 100% 100%;
			padding-right: 20px;
			&:hover {
				background-image: url('~@/web/assets/images/top_right_bg_hover.png');
			}
			.user-info {
				cursor: pointer;
				padding-left: 10px;
				.avatar {
					width: 30px;
					height: 30px;
					border-radius: 100%;
					vertical-align: middle;
					border: 2px solid #fff;
					margin-top: -4px;
				}
				.user-name {
					font-size: 16px;
					color: #dff0ff;
					padding-left: 5px;
					display: inline-block;
					vertical-align: middle;
					width: 60px;
					text-overflow: ellipsis;
					overflow: hidden;
					white-space: nowrap;
					margin-top: -4px;
				}
				.user-caret-icons {
					font-size: 14px;
					// color: #d5eeff;
					color: #fff;
					margin-top: -3px;
					vertical-align: middle;
					&.el-icon-caret-top {
						display: none;
					}
				}
			}
			.user-function {
				width: 193px;
				position: absolute;
				// right: -7px;
				right: 13px;
				top: 56px;
				border: 1px solid #e9edf5;
				background-color: #fff;
				display: none;
				border-radius: 5px;
				box-shadow: 0px 4px 8px 0px rgba(87, 109, 159, 0.2);
				padding: 5px 0;
				z-index: 2;
				&::before,
				&::after {
					position: absolute;
					right: 5px;
					content: '';
					border-style: solid;
					border-width: 8px;
				}
				&::before {
					top: -17px;
					border-color: transparent transparent rgba(87, 109, 159, 0.2) transparent;
				}
				&::after {
					top: -16px;
					border-color: transparent transparent #fff transparent;
				}
				li {
					font-size: 16px;
					color: #2b3346;
					padding: 0 15px;
					line-height: 30px;
					cursor: pointer;
					position: relative;
					i,
					span {
						vertical-align: middle;
						margin-left: 5px;
					}
					.layout-user {
						.sprite-layout-user_normal;
					}

					.layout-version {
						.sprite-layout-version_normal;
					}
					.layout-out {
						.sprite-layout-out_normal;
					}

					&:hover {
						color: #fff;
						background: #4b81ff;
						.layout-user {
							.sprite-layout-user_hover;
						}

						.layout-version {
							.sprite-layout-version_hover;
						}
						.layout-out {
							.sprite-layout-out_hover;
						}
					}
				}
			}
			&:hover {
				.user-info {
					.user-caret-icons {
						&.el-icon-caret-bottom {
							transform: rotate(180deg);
						}
					}
				}
				.user-function {
					display: block;
				}
			}
		}
	}
}
</style>
