<template>
	<gxx-form :model="form" :label-width="80" :label-position="'justify'" class="form" ref="userForm" :showColon="true" :rules="rules">
		<gxx-form-item label="原始密码" prop="oldPassword">
			<gxx-input v-model="form.oldPassword" type="password"></gxx-input>
		</gxx-form-item>
		<gxx-form-item label="新密码" prop="newPassword">
			<gxx-input v-model="form.newPassword" type="password"></gxx-input>
		</gxx-form-item>
		<gxx-form-item label="确认密码" prop="confirmNewPassword">
			<gxx-input v-model="form.confirmNewPassword" type="password"></gxx-input>
		</gxx-form-item>
	</gxx-form>
</template>

<script>
import Crypto from '@/common/libs/crypto'
export default {
	name: 'UpdatePassword',
	props: ['params'],
	data() {
		return {
			rules: {
				oldPassword: [{ required: true, message: '原始密码不能为空', trigger: 'blur' }],
				newPassword: [
					{ required: true, message: '新密码不能为空', trigger: 'blur' }
					// { required: true, message: '规则: 8-16位，数字、字母、特殊符号至少包含三种', trigger: 'blur', pattern: /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[\W_]+).{8,16}$/ }
				],
				confirmNewPassword: [
					{ required: true, message: '确认密码不能为空', trigger: 'blur' },
					{
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (value !== this.form.newPassword) {
								callback(new Error('两次密码不一致'))
							} else {
								callback()
							}
						}
					}
				]
			},
			form: {
				oldPassword: '',
				newPassword: '',
				confirmNewPassword: ''
			}
		}
	},
	created() {
		Object.assign(this.form, this.params)
	},
	methods: {
		confirm() {
			return new Promise((resolve, reject) => {
				this.$refs.userForm.validate((bool) => {
					if (!bool) {
						return resolve(false)
					}
					const oldPassword = Crypto.aesEncrypt(this.form.oldPassword)
					const newPassword = Crypto.aesEncrypt(this.form.newPassword)
					this.$http.userApi.updatePassword({ ...this.form, oldPassword, newPassword, confirmNewPassword: newPassword }).then((res) => {
						const { code } = res
						if (code == 200) {
							this.$baseTip({ message: '更新成功', type: 'success' })
							resolve(true)
						}
					})
				})
			})
		}
	}
}
</script>

<style lang="less" scoped>
.form {
	display: flex;
	flex-direction: column;
	padding: 10px 20px 0;
	/deep/ .form-item-content {
		flex: 1;
		.gxx-input-container {
			width: 100%;
		}
	}
}
</style>
