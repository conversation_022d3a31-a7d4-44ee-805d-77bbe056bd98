<template>
	<div class="version-body">
		<ul>
			<li>
				<span class="label">产品名称</span>
				<span class="clone">:</span>
				{{ logoName }}
			</li>
			<li>
				<span class="label">系统版本号</span>
				<span class="clone">:</span>
				{{ version }}
			</li>
		</ul>
	</div>
</template>

<script>
import store from '@/web/store'
export default {
	data() {
		return {
			logoName: '',
			version: ''
		}
	},
	created() {
		this.getTerminalConfigInfo()
	},
	methods: {
		getTerminalConfigInfo() {
			store.dispatch('config/getTerminalConfigInfo').then((data) => {
				this.logoName = data.operationInfo?.logoName || this.logoName
				this.version = `V${data.version}` || this.version
			})
		}
	}
}
</script>

<style lang="less" scoped>
.version-body {
	padding: 0 35px 20px;
	li {
		line-height: 35px;
		span {
			display: inline-block;
			text-align: justify;
			text-align-last: justify;
			text-justify: distribute-all-lines;
		}
		.clone {
			margin: 0 10px 0 5px;
		}
		.label {
			width: 100px;
		}
	}
}
</style>
