<template>
	<div class="personal-center-body">
		<div class="container">
			<div class="left">
				<ul>
					<li @click="setAction(item.action)" v-for="item in actionList" :key="item.action" :class="{ active: currentAction == item.action }">{{ item.name }}</li>
				</ul>
			</div>
			<div class="right">
				<div class="right-head">
					<h3>{{ actionName }}</h3>
				</div>
				<div class="right-body" v-show="currentAction == 'personalInfo'">
					<div class="item">
						<span class="label">账号</span>
						<span class="clone">:</span>
						{{ info.loginId }}
					</div>
					<div class="item">
						<span class="label">名称</span>
						<span class="clone">:</span>
						{{ info.name }}
					</div>
					<div class="item">
						<span class="label">注册时间</span>
						<span class="clone">:</span>
						{{ info.createTime }}
					</div>
					<div class="item">
						<span class="label">登录时间</span>
						<span class="clone">:</span>
						{{ info.loginTime }}
					</div>
				</div>
				<div class="right-body" v-show="isChangePassword">
					<update-password ref="updatePasswordRef" :params="info"></update-password>
				</div>
			</div>
		</div>
		<div class="common-dialog-footer tools">
			<gxx-button type="info" @click="cancel">取消</gxx-button>
			<gxx-button @click="confirm">确定</gxx-button>
		</div>
	</div>
</template>

<script>
import UpdatePassword from './update_password'
import store from '@/web/store'
export default {
	name: 'PersonalCenter',
	components: {
		UpdatePassword
	},
	computed: {
		actionName() {
			return this.actionList.find((item) => item.action == this.currentAction)?.name || ''
		},
		isChangePassword() {
			return this.currentAction === 'changePassword'
		}
	},
	data() {
		return {
			actionList: [
				{
					name: '基础信息',
					action: 'personalInfo'
				},
				{
					name: '修改密码',
					action: 'changePassword'
				}
			],
			currentAction: 'personalInfo',
			info: store.getters['user/userInfo']
		}
	},
	methods: {
		setAction(action) {
			this.currentAction = action
		},
		cancel() {
			this.$parent.close('cancel')
		},
		async confirm() {
			if (this.isChangePassword) {
				const bool = await this.$refs.updatePasswordRef.confirm()
				if (!bool) {
					return
				}
			}
			this.$parent.close('confirm')
		}
	}
}
</script>

<style lang="less" scoped>
.personal-center-body {
	width: 600px;
	padding: 0 35px 20px;
	.container {
		display: flex;
		padding-bottom: 20px;
		.left {
			width: 25%;
			padding-right: 15px;
			li {
				height: 40px;
				line-height: 40px;
				border: 1px solid #80cbff;
				border-top: 0;
				cursor: pointer;
				text-align: center;
				color: #0394f9;
				&:hover {
					background-color: #dff2ff;
				}
				&:first-child {
					border-top: 1px solid #80cbff;
					border-radius: 4px 4px 0 0;
				}
				&:last-child {
					border-radius: 0 0 4px 4px;
				}
				&.active {
					background-color: #0394f9;
					color: #fff;
				}
			}
		}
		.right {
			flex: 1;
			padding-left: 20px;
			.right-head {
				padding-top: 8px;
				padding-bottom: 10px;
				font-size: 20px;
				position: relative;
				&::before {
					content: '';
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					height: 1px;
					color: #cee0f0;
					background-color: #cee0f0;
				}
			}
			.right-body {
				.item {
					height: 40px;
					line-height: 40px;
					span {
						display: inline-block;
						text-align: justify;
						text-align-last: justify;
						text-justify: distribute-all-lines;
					}
					.clone {
						margin: 0 10px 0 5px;
					}
					.label {
						width: 100px;
					}
				}
			}
		}
	}
	.tools {
		text-align: center;
		.gxx-button + .gxx-button {
			margin-left: 20px;
		}
	}
}
</style>
