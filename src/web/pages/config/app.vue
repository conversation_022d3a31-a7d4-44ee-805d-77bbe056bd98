<template>
	<div class="app-config" v-partial-loading="isLoad">
		<config v-if="isShow" :params="{ isWeb: true, faceDeviceList, configParams }" ref="appConfigRef"></config>
		<div class="x-dialog-footer">
			<gxx-button @click="updateConfig">更新配置</gxx-button>
		</div>
	</div>
</template>

<script>
import Config from '@/components/setConfig'
import { setConfigInfo, getConfigInfo } from '@/web/api/index'
export default {
	name: 'AppConfig',
	components: {
		Config
	},
	data() {
		return {
			isShow: false,
			isLoad: false,
			faceDeviceList: [],
			configParams: {}
		}
	},
	created() {
		this.init()
	},
	methods: {
		init() {
			this.isLoad = true
			getConfigInfo()
				.then((res) => {
					const { code, data } = res
					this.faceDeviceList = code == 200 ? data.cameraList : []
					Object.assign(this.configParams, code == 200 ? data.config : {})
				})
				.finally(() => {
					this.isLoad = false
					this.isShow = true
				})
		},
		updateConfig() {
			const appConfigRef = this.$refs.appConfigRef
			if (!appConfigRef) {
				return
			}
			const isValidate = appConfigRef.validate('confirm')
			if (!isValidate) {
				return
			}
			this.isLoad = true
			setConfigInfo({ ...this.configParams, ...appConfigRef.configParams })
				.then((res) => {
					const { code, msg } = res
					code == 200 && this.$GxxMessage({ message: msg, type: 'success' })
				})
				.finally(() => {
					this.isLoad = false
				})
		}
	}
}
</script>

<style lang="less" scoped>
.app-config {
	width: 100%;
	overflow: auto;
	padding-top: 20px;
}
</style>
