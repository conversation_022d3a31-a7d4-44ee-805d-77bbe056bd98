<template>
	<div class="terminal-config" v-partial-loading="isLoad">
		<network-config v-if="isShow" :params="{ isWeb: true, configParams }" ref="networkConfigRef"></network-config>
		<div class="x-dialog-footer">
			<gxx-button @click="updateConfig">更新配置</gxx-button>
		</div>
	</div>
</template>

<script>
import NetworkConfig from '@/components/config/network'
import { setTerminalConfigInfo, getTerminalConfigInfo } from '@/web/api/index'
export default {
	name: 'TerminalConfig',
	components: {
		NetworkConfig
	},
	data() {
		return {
			isShow: false,
			isLoad: false,
			configParams: {
				localIp: '',
				localDNS: '',
				localGetway: '',
				localNetmask: ''
			}
		}
	},
	created() {
		this.init()
	},
	methods: {
		init() {
			this.isLoad = true
			getTerminalConfigInfo()
				.then((res) => {
					const { code, data } = res
					Object.assign(this.configParams, code == 200 ? data : {})
				})
				.finally(() => {
					this.isLoad = false
					this.isShow = true
				})
		},
		updateConfig() {
			const networkConfigRef = this.$refs.networkConfigRef
			if (!networkConfigRef) {
				return
			}
			const isValidate = networkConfigRef.validate('confirm')
			if (!isValidate) {
				return
			}
			this.isLoad = true
			setTerminalConfigInfo(networkConfigRef.configParams)
				.then((res) => {
					const { code, msg } = res
					code == 200 && this.$GxxMessage({ message: msg, type: 'success' })
				})
				.finally(() => {
					this.isLoad = false
				})
		}
	}
}
</script>

<style lang="less" scoped>
.terminal-config {
	width: 100%;
	overflow: auto;
	padding: 20px 0;
	.config-module {
		display: flex;
		flex-direction: column;
		margin: 0 20px 0;
		.module-content {
			margin-top: 10px;
			display: flex;
			width: 100%;
			flex-wrap: wrap;
			.content-row {
				display: flex;
				height: 30px;
				line-height: 30px;
				width: calc(50% - 20px);
				position: relative;
				margin-bottom: 20px;
				.row-label {
					width: 150px;
					text-align: right;
					padding-left: 8px;
					text-align: justify;
					text-align-last: justify;
					font-size: 16px;
					&.required::after {
						content: '*';
						color: red;
						position: absolute;
						top: 0;
						left: 0;
					}
				}
				.row-colon {
					margin-left: 5px;
					margin-right: 10px;
				}
				> span {
					display: flex;
				}
				/deep/ .gxx-input-container,
				.gxx-select-wrap {
					flex: 1;
					width: 100% !important;
				}
				/deep/ .gxx-popover__reference-wrapper {
					display: flex;
					justify-content: center;
					align-items: center;
					margin-right: 5px;
					.doubt {
						width: 20px;
						height: 20px;
						line-height: 20px;
						border-radius: 50%;
						text-align: center;
						background-color: #515a6e;
						color: #fff;
					}
				}
				&:nth-child(2n) {
					margin-left: 40px;
				}
			}
		}
	}
}
</style>
