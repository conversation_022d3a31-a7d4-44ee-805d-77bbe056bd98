<template>
	<div class="business-config">
		<div class="config-body">
			<component v-for="routeName in routeNameList" :ref="routeName" :key="routeName" :is="routeName" :terminalConfig="data" :baudRateOptions="baudRateOptions" :comOptions="comOptions" @getCameraList="getCameraList" @getPrinterList="getPrinterList" @changeMainCabinet="changeMainCabinet"></component>
		</div>
		<div class="config-footer">
			<gxx-button @click="save">保存</gxx-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import WebsocketMixin from '@/web/mixins/websocket'
import { baudRateOptions } from '@/common/libs/options'
import filterTerminalRoutes from '@/web/router/modules/other_router'
import Camera from './components/camera'
import HighSpeedScanner from './components/high_speed_scanner'
import Printer from './components/printer'
import Light from './components/light'
import Lock from './components/lock'
import Rfid from './components/rfid'
import QrCode from './components/qr_code'
import WristbandDrawer from './components/wristband_drawer'
import Wristband from './components/wristband'
import IdCard from './components/id_card'
import FrequencyCardReader from './components/frequency_card_reader'
import SignFinger from './components/sign_finger'
import ContactVitalSigns from './components/contact_vital_signs'
import NonContactVitalSigns from './components/non_contact_vital_signs'
import business from './components/business'
export default {
	name: 'BusinessConfig',
	mixins: [WebsocketMixin],
	components: {
		Camera,
		HighSpeedScanner,
		Printer,
		Light,
		Lock,
		Rfid,
		QrCode,
		WristbandDrawer,
		Wristband,
		IdCard,
		FrequencyCardReader,
		SignFinger,
		ContactVitalSigns,
		NonContactVitalSigns,
		business
	},
	computed: {
		...mapGetters('config', ['itemNumber'])
	},
	data() {
		return {
			routeNameList: [],
			comOptions: [], // 串口列表
			baudRateOptions // 波特率列表
		}
	},
	created() {
		this.routeNameList = filterTerminalRoutes(this.itemNumber)
			.map((route) => {
				if (route.path !== 'business-config') {
					return route.name
				}
			})
			.filter(Boolean)
		this.routeNameList.unshift('business')
		const usbRouteNameList = ['Camera', 'HighSpeedScanner', 'Printer']
		const isLinkUsb = this.routeNameList.some((item) => usbRouteNameList.includes(item))
		if (isLinkUsb) {
			this.initConnect('usb')
		}
		const comRouteNameList = ['Light', 'Lock', 'Rfid', 'QrCode', 'WristbandDrawer', 'ContactVitalSigns']
		const isGetCom = this.routeNameList.some((item) => comRouteNameList.includes(item))
		if (isGetCom) {
			this.getComList()
		}
	},
	methods: {
		changeMainCabinet(val) {
			this.$refs.Lock && this.$refs.Lock[0].changeDataManufacturer(val.value)
			this.$refs.Light && this.$refs.Light[0].changeDataManufacturer(val.value)
		},
		// 初始化usb
		handleOpen() {
			this.ws.initUsb()
		},
		// 处理message数据
		handleMessage(message) {
			const { code, msg, data } = message
			if (code != 0) {
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			if (data.flag !== 0) {
				return this.$baseTip({ type: 'error', message: data?.message || msg })
			}
			for (const key in this.$refs) {
				this.$refs[key][0].handleMessage && this.$refs[key][0].handleMessage(message)
			}
		},
		// 获取串口列表
		getComList() {
			this.$http.hardwareApi.getSerialPortList().then((res) => {
				if (res.code == 200) {
					this.comOptions = res.data ? res.data : []
				}
			})
		},
		// 获取摄像头列表
		getCameraList() {
			this.ws && this.ws.getCameraList()
		},
		// 获取打印机列表
		getPrinterList() {
			this.ws && this.ws.getPrinterList()
		},
		// 确认操作
		async save() {
			let bool = true
			const configInfo = {}
			for (const key in this.$refs) {
				const config = await this.$refs[key][0].validate()
				if (!config) {
					bool = false
					break
				}
				Object.assign(configInfo, config)
			}
			if (!bool) {
				return false
			}
			this.$store.dispatch('config/setTerminalConfigInfo', { ...this.data, ...configInfo })
		}
	}
}
</script>

<style lang="less" scoped>
.business-config {
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 20px;
	.config-body {
		flex: 1;
		width: 100%;
		height: 100%;
		overflow: auto;
		/deep/ .gxx-form-container {
			.gxx-form-item-container {
				width: 50%;
				.label {
					min-width: 150px;
				}
			}
		}
	}
	.config-footer {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 30px;
	}
}
</style>
