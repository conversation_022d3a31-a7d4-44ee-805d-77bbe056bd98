<template>
	<gxx-form ref="wristbandConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="手环读卡器配置">
			<gxx-form-item label="类型" prop="manufacturer">
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="wristbandManufacturerOptions" v-model="formData.manufacturer"></gxx-select>
			</gxx-form-item>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { wristbandManufacturerOptions, wristbandManufacturerDic } from '@/common/libs/options'
export default {
	name: 'Wristband',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		}
	},
	data() {
		return {
			wristbandManufacturerOptions,
			formData: {
				manufacturer: wristbandManufacturerDic.USB
			},
			rules: {
				manufacturer: [{ required: true, message: '请选择类型', trigger: 'blur change' }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			const { wristbandInfo } = this.terminalConfig
			Object.assign(this.formData, wristbandInfo)
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['wristbandConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						wristbandInfo: {
							...this.terminalConfig.wristbandInfo,
							manufacturer: Number(this.formData.manufacturer)
						}
					})
				})
			})
		}
	}
}
</script>
