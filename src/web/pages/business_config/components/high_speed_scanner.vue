<template>
	<gxx-form ref="highSpeedScannerConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="高拍仪配置">
			<gxx-form-item label="高拍仪" prop="deviceId">
				<gxx-select :enableEmpty="false" labelField="label" valueField="deviceId" :option="cameraList" v-model="formData.deviceId"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="高拍仪旋转角度">
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="cameraRotateOptions" v-model="formData.rotateX"></gxx-select>
			</gxx-form-item>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { cameraRotateOptions, cameraRotateDic } from '@/common/libs/options'
export default {
	name: 'HighSpeedScanner',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		}
	},
	data() {
		return {
			cameraList: [],
			cameraRotateOptions,
			formData: {
				name: '',
				rotateX: String(cameraRotateDic.ROTATE_0),
				deviceId: ''
			},
			rules: {
				deviceId: [{ required: true, message: '请选择高拍仪', trigger: 'blur change' }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			if (this.terminalConfig.highSpeedScannerInfo) {
				Object.assign(this.formData, { ...this.terminalConfig.highSpeedScannerInfo })
			}
		},
		handleMessage(message) {
			const { action, data } = message
			switch (action) {
				case 'init':
					data.flag === 0 && this.$emit('getCameraList')
					break
				case 'getVideoDevice':
					this.cameraList = data.result || []
					break
				default:
					break
			}
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['highSpeedScannerConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						highSpeedScannerInfo: {
							...this.terminalConfig.highSpeedScannerInfo,
							name: this.formData.name,
							rotateX: Number(this.formData.rotateX),
							deviceId: this.formData.deviceId
						}
					})
				})
			})
		}
	}
}
</script>
