<template>
	<gxx-form ref="frequencyCardReaderConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="多频读卡器配置">
			<gxx-form-item label="类型" prop="manufacturer">
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="frequencyCardReaderManufacturerOptions" v-model="formData.manufacturer"></gxx-select>
			</gxx-form-item>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { frequencyCardReaderManufacturerOptions, frequencyCardReaderManufacturerDic } from '@/common/libs/options'
export default {
	name: 'FrequencyCardReader',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		}
	},
	data() {
		return {
			frequencyCardReaderManufacturerOptions,
			formData: {
				manufacturer: frequencyCardReaderManufacturerDic.USB
			},
			rules: {
				manufacturer: [{ required: true, message: '请选择类型', trigger: 'blur change' }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			if (this.terminalConfig.frequencyCardReaderInfo) {
				Object.assign(this.formData, { ...this.terminalConfig.frequencyCardReaderInfo })
			}
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['frequencyCardReaderConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						frequencyCardReaderInfo: {
							...this.terminalConfig.frequencyCardReaderInfo,
							manufacturer: Number(this.formData.manufacturer)
						}
					})
				})
			})
		}
	}
}
</script>
