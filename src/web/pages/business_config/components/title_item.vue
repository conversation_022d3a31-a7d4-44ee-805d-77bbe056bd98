<template>
	<div class="TitleItem">
		<div class="title">
			<span class="gxx-subtitle text">{{ titleText }}</span>
			<div class="right-content">
				<slot name="rightContent"></slot>
			</div>
		</div>
		<div class="item-content">
			<slot></slot>
		</div>
	</div>
</template>

<script>
export default {
	name: 'TitleItem',
	props: {
		titleText: {
			type: String,
			default: ''
		}
	}
}
</script>

<style lang="less" scoped>
.TitleItem {
	width: 100%;
	.title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
		width: 100%;
		height: 30px;
		line-height: 30px;
		font-size: 18px;
	}
	.item-content {
		display: flex;
		flex-wrap: wrap;
	}
}
</style>
