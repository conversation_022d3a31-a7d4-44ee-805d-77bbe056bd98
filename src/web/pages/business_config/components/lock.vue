<template>
	<gxx-form :model="configInfo" showColon>
		<TitleItem titleText="锁控配置">
			<gxx-button slot="rightContent" v-if="isShowAddCabinetConfigBtn" type="title-icon" icon="icon-biaoticaozuoanniu-zengjia" @click="addCabinetConfig"></gxx-button>
			<div class="cabinet-config-list" v-for="(info, index) in configInfoList" :key="index">
				<div class="x-dialog-close gxx-icon-close close-btn" v-if="index != 0" @click="removeCabinetConfig(index)"></div>
				<gxx-form-item label="类型">
					<gxx-select @change="changeManufacturer(index)" :enableFilter="false" :enableEmpty="false" :option="lockManufacturerOptions" v-model="info.manufacturer"></gxx-select>
				</gxx-form-item>
				<gxx-form-item label="连接地址">
					<gxx-select v-if="isCom(info.manufacturer)" :enableEmpty="false" :option="comOptions" v-model="info.path"></gxx-select>
					<gxx-input v-else v-model="info.path"></gxx-input>
				</gxx-form-item>
				<gxx-form-item label="波特率" v-if="isCom(info.manufacturer)">
					<gxx-select v-model="info.baudRate" :option="baudRateOptions"></gxx-select>
				</gxx-form-item>
				<gxx-form-item label="柜子编号">
					<gxx-select :multiple="true" :enableEmpty="false" :option="cabinetList" v-model="info.cabinetNum"></gxx-select>
				</gxx-form-item>
				<gxx-form-item label="锁板拨码">
					<gxx-input v-model="info.lockPlate" placeholder="例如:1;2,3"></gxx-input>
				</gxx-form-item>
				<gxx-form-item label="单锁板最大控制数">
					<gxx-input v-model="info.maxNum" placeholder="例如:18;21,15"></gxx-input>
				</gxx-form-item>
			</div>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { createCabinetList, lockManufacturerOptions, lockManufacturerDic } from '@/common/libs/options'
export default {
	name: 'Lock',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		},
		comOptions: {
			type: Array,
			default: () => []
		},
		baudRateOptions: {
			type: Array,
			default: () => []
		}
	},
	computed: {
		isShowAddCabinetConfigBtn() {
			return this.configInfoList.length < this.lockManufacturerOptions.length
		}
	},
	data() {
		return {
			cabinetList: createCabinetList(),
			lockManufacturerOptions,
			configInfoList: [],
			configInfo: {
				manufacturer: lockManufacturerDic.K32_W,
				path: '',
				baudRate: 0,
				cabinetNum: '',
				lockPlate: 0,
				maxNum: 6
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			const { lockInfo } = this.terminalConfig
			if (!lockInfo) {
				return this.configInfoList.push({ ...this.configInfo })
			}
			const obj = {}
			for (const key in lockInfo) {
				const lock = lockInfo[key]
				lock.manufacturer = lock.manufacturer || lockManufacturerDic.K32_W
				if (obj[lock.manufacturer]) {
					obj[lock.manufacturer].lockPlate += lock.lockPlate ? `;${lock.lockPlate}` : ''
					obj[lock.manufacturer].maxNum += lock.maxNum ? `;${lock.maxNum}` : ''
					obj[lock.manufacturer].cabinetNum += `,${key}`
				} else {
					obj[lock.manufacturer] = { ...lock, cabinetNum: key }
				}
			}
			this.$set(this, 'configInfoList', Object.values(obj))
		},
		isCom(manufacturer) {
			return [lockManufacturerDic.K32_S, lockManufacturerDic.J88_S, lockManufacturerDic.D99_S].includes(Number(manufacturer))
		},
		// 获取第一个未配置的lock
		getNotSelectedFirstLockManufacturer() {
			const lockManufacturerList = this.configInfoList.map((item) => {
				return item.manufacturer
			})
			const notSelectedLockManufacturer = this.lockManufacturerOptions.filter((item) => {
				return !lockManufacturerList.includes(String(item.value))
			})
			if (!notSelectedLockManufacturer.length) {
				return ''
			}
			return notSelectedLockManufacturer[0]
		},
		addCabinetConfig() {
			const lock = this.getNotSelectedFirstLockManufacturer()
			this.configInfoList.push({ ...lock?.config, manufacturer: lock.value })
		},
		removeCabinetConfig(index) {
			this.configInfoList.splice(index, 1)
		},
		changeManufacturer(index) {
			const lock = this.configInfoList[index]
			const item = this.configInfoList.find((item, i) => {
				return index != i && item.manufacturer == lock.manufacturer
			})
			if (item) {
				lock.manufacturer = ''
				return this.$baseTip({ message: '该锁控类型已配置！', type: 'error' })
			}
			lock.cabinetNum = lock.cabinetNum || ''
			const lockItem = lockManufacturerOptions.find((item) => item.value == lock.manufacturer)
			for (const key in lockItem?.config) {
				lock[key] = lock[key] ? lock[key] : lockItem?.config[key]
			}
		},
		validate() {
			return new Promise((resolve, reject) => {
				let bool = true
				let cabinetNum = ''
				const lockInfo = {}
				for (let i = 0; i < this.configInfoList.length; i++) {
					const item = this.configInfoList[i]
					if (!item.path) {
						this.$baseTip({ message: `请配置第${i + 1}个锁控连接地址信息！`, type: 'error' })
						bool = false
						break
					}
					if (this.isCom(item.manufacturer) && !item.baudRate) {
						this.$baseTip({ message: `请配置第${i + 1}个锁控波特率信息！`, type: 'error' })
						bool = false
						break
					}
					if (!item.cabinetNum) {
						this.$baseTip({ message: `请配置第${i + 1}个锁控柜子编号信息！`, type: 'error' })
						bool = false
						break
					} else {
						// 验证柜子编号是否重复
						const isRepeat = item.cabinetNum.split(',').some((num) => {
							return cabinetNum.indexOf(num) != -1
						})
						if (isRepeat) {
							this.$baseTip({ message: `请配置第${i + 1}个锁控柜子编号信息和其他柜子编号重复！`, type: 'error' })
							bool = false
							break
						}
						cabinetNum += item.cabinetNum
					}
					if (String(item.lockPlate).length == 0) {
						this.$baseTip({ message: `请配置第${i + 1}个锁控锁板拨码信息！`, type: 'error' })
						bool = false
						break
					}
					if (!item.maxNum) {
						this.$baseTip({ message: `请配置第${i + 1}个锁控单锁板最大控制数信息！`, type: 'error' })
						bool = false
						break
					}
					const cabinetNumList = item.cabinetNum.split(',')
					const lockPlateList = String(item.lockPlate).split(';')
					const maxNumList = String(item.maxNum).split(';')
					if (cabinetNumList.length != lockPlateList.length || cabinetNumList.length != maxNumList.length) {
						this.$baseTip({ message: `请配置第${i + 1}个锁控配置信息错误！`, type: 'error' })
						bool = false
						break
					}
					// 组装数据
					for (let j = 0; j < cabinetNumList.length; j++) {
						lockInfo[cabinetNumList[j]] = {
							isMain: i == 0 && j == 0,
							manufacturer: Number(item.manufacturer),
							path: item.path,
							baudRate: Number(item.baudRate) || 9600,
							lockPlate: lockPlateList[j],
							maxNum: maxNumList[j]
						}
					}
				}
				resolve(bool ? { lockInfo } : false)
			})
		}
	}
}
</script>

<style lang="less" scoped>
/deep/ .cabinet-config-list {
	position: relative;
	display: flex;
	flex-wrap: wrap;
	width: 100%;
	margin-bottom: 10px;
	.close-btn {
		z-index: 1;
	}
}
</style>
