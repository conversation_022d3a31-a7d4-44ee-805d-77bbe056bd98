<template>
	<gxx-form ref="businessConfig" :model="formData" showColon :rules="rules" :labelWidth="150">
		<TitleItem titleText="基础配置">
			<gxx-form-item label="通信模式" prop="communicationMode">
				<gxx-select v-model="formData.communicationMode" :enableFilter="false" :enableEmpty="false" :option="communicationModeOptions"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="场所名称" prop="placeName">
				<gxx-input v-model="formData.placeName"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="终端标题">
				<gxx-input v-model="operationInfo.terminalName"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="操作时间" prop="operationTime">
				<gxx-input v-model="formData.operationTime"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="使用人员" prop="userType" v-if="formData.communicationMode == communicationModeDic.OFFLINE">
				<gxx-select v-model="formData.userType" :enableFilter="false" :enableEmpty="false" :option="userTypeList"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="登录方式" prop="loginMethod">
				<gxx-select v-model="formData.loginMethod" :enableFilter="false" :enableEmpty="false" multiple :option="loginMethodList"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="版权" class="form-item-all">
				<gxx-input v-model="operationInfo.copyright" style="width: 400px"></gxx-input>
			</gxx-form-item>
		</TitleItem>
		<TitleItem titleText="模式配置">
			<gxx-form-item label="服务ip" prop="serviceIp" v-if="formData.communicationMode == communicationModeDic.ONLINE">
				<gxx-input v-model="formData.serviceIp"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="服务端口" prop="httpPort" v-if="formData.communicationMode == communicationModeDic.ONLINE">
				<gxx-input v-model="formData.httpPort"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="人脸阈值" prop="facialThreshold" v-if="formData.communicationMode == communicationModeDic.OFFLINE">
				<gxx-input v-model="formData.facialThreshold"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="指纹阈值" prop="fingerThreshold" v-if="formData.communicationMode == communicationModeDic.OFFLINE">
				<gxx-input v-model="formData.fingerThreshold"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="双重认证" prop="twoFactorAuthentication" v-if="formData.communicationMode == communicationModeDic.ONLINE || formData.userType !== '1'">
				<gxx-radio value="0" v-model="formData.twoFactorAuthentication">不开启</gxx-radio>
				<gxx-radio value="1" v-model="formData.twoFactorAuthentication">开启</gxx-radio>
			</gxx-form-item>
			<gxx-form-item label="离区拍照" prop="outsideTake" v-if="formData.communicationMode == communicationModeDic.ONLINE">
				<gxx-radio value="0" v-model="formData.outsideTake">不开启</gxx-radio>
				<gxx-radio value="1" v-model="formData.outsideTake">开启</gxx-radio>
			</gxx-form-item>
			<gxx-form-item label="播放地址" prop="outsideTakeHttp" v-if="formData.communicationMode == communicationModeDic.ONLINE && formData.outsideTake">
				<gxx-input v-model="formData.outsideTakeHttp"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="通道id" prop="channelId" v-if="formData.communicationMode == communicationModeDic.ONLINE && formData.outsideTake">
				<gxx-input v-model="formData.channelId"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="签名捺印" prop="signatureSeal" v-if="formData.communicationMode == communicationModeDic.ONLINE || formData.userType !== '1'">
				<gxx-radio :value="false" v-model="formData.signatureSeal">不开启</gxx-radio>
				<gxx-radio :value="true" v-model="formData.signatureSeal">开启</gxx-radio>
			</gxx-form-item>
			<gxx-form-item label="定位归还" prop="locaterReturn" v-if="formData.communicationMode == communicationModeDic.ONLINE">
				<gxx-radio :value="false" v-model="formData.locaterReturn">不开启</gxx-radio>
				<gxx-radio :value="true" v-model="formData.locaterReturn">开启</gxx-radio>
			</gxx-form-item>
			<gxx-form-item label="主屏类型" prop="mainScreenType">
				<gxx-select v-model="formData.mainScreenType" :enableFilter="false" :enableEmpty="false" :option="mainScreenList"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="同步地址" prop="dataAddress" v-if="formData.communicationMode != communicationModeDic.ONLINE && formData.mainScreenType != 1">
				<gxx-input v-model="formData.dataAddress"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="主序列号" prop="mainSerialNumber" v-if="formData.communicationMode == communicationModeDic.ONLINE && formData.mainScreenType == '3'">
				<gxx-input v-model="formData.mainSerialNumber"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="主柜型号" prop="mainCabinetModel">
				<div class="select-box">
					<div class="xh">
						<gxx-select v-model="formData.mainCabinetModel" :enableFilter="false" :enableEmpty="false" :option="mainCabinetList" @change="changeMainCabinet"></gxx-select>
					</div>
					<div class="text">柜格{{ getNumByValue(formData.mainCabinetModel, mainCabinetList) }}</div>
				</div>
			</gxx-form-item>
			<gxx-form-item label="副柜型号" class="form-item-all">
				<div class="select-box" v-for="(item, idx) in secondaryCabinetModel" :key="idx">
					<div class="xh">
						<gxx-select :option="secondaryCabinetList" select-title="副柜选择" v-model="secondaryCabinetModel[idx]" disabled></gxx-select>
					</div>
					<div class="text">柜格{{ getNumByValue(secondaryCabinetModel[idx], secondaryCabinetList) }}</div>
					<div class="del" v-if="secondaryCabinetModel.length > 0" @click="delSecondary(idx)"></div>
				</div>
				<gxx-button @click="addSecondary">+新增副柜</gxx-button>
			</gxx-form-item>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from '../components/title_item'
import { mainCabinetList, secondaryCabinetList, communicationModeDic, communicationModeOptions } from '@/common/libs/options'
export default {
	name: 'Business',
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		}
	},
	components: {
		TitleItem
	},
	data() {
		return {
			formData: {},
			operationInfo: {},
			userTypeList: [
				{ value: '1', label: '工作人员' },
				{ value: '2', label: '非工作人员' }
			],
			mainScreenList: [
				{ value: '1', label: '单面柜' },
				{ value: '2', label: '双面柜-入口' },
				{ value: '3', label: '双面柜-出口' }
			],
			mainCabinetList,
			secondaryCabinetList,
			communicationModeDic,
			communicationModeOptions,
			secondaryCabinetModel: [],
			cameraList: [],
			rotateYList: [
				{
					value: 0,
					label: '0度'
				},
				{
					value: 1,
					label: '180度'
				}
			],
			facialServiceList: [
				{ value: '1', label: 'GSN-人脸服务YJY款' },
				{ value: '2', label: '对接第三方' }
			],
			rules: {
				communicationMode: [{ required: true, message: '请选择通信模式', trigger: 'blur' }],
				operationTime: [{ required: true, message: '请填写操作时间', trigger: 'blur' }],
				userType: [{ required: true, message: '请选择使用人员', trigger: 'blur' }],
				serviceIp: [{ required: true, message: '请填写服务ip', trigger: 'blur' }],
				httpPort: [{ required: true, message: '请填写服务端口', trigger: 'blur' }],
				facialThreshold: [{ required: true, message: '请填写人脸阈值', trigger: 'blur' }],
				fingerThreshold: [{ required: true, message: '请填写指纹阈值', trigger: 'blur' }],
				twoFactorAuthentication: [{ required: true, message: '请选择双重认证', trigger: 'blur' }],
				mainScreenType: [{ required: true, message: '请选择主屏类型', trigger: 'blur' }],
				mainCabinetModel: [{ required: true, message: '请选择主柜型号', trigger: 'blur' }],
				dataAddress: [{ required: true, message: '请填写同步地址', trigger: 'blur' }],
				mainSerialNumber: [{ required: true, message: '请填写主序列号', trigger: 'blur' }]
			},
			loginMethodAllList: [
				{ value: '1', label: '账号' },
				{ value: '2', label: '刷脸' },
				{ value: '3', label: '刷卡' },
				{ value: '4', label: '指纹' },
				{ value: '5', label: '手势' },
				{ value: '6', label: '手环' }
			]
		}
	},
	computed: {
		loginMethodList() {
			if (this.formData.communicationMode == communicationModeDic.ONLINE) {
				return this.loginMethodAllList.filter((item) => item.value != 4)
			} else {
				return this.loginMethodAllList.filter((item) => item.value != 3 && item.value != 6)
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		changeMainCabinet(value) {
			this.secondaryCabinetModel = []
			this.$emit('changeMainCabinet', value)
		},
		getNumByValue(id, list) {
			const obj = list.find((item) => item.value === id)
			return obj ? obj.gf : undefined
		},
		delSecondary(idx) {
			this.secondaryCabinetModel.splice(idx, 1)
		},
		addSecondary() {
			const id = this.formData.mainCabinetModel
			this.secondaryCabinetModel.push(id)
		},
		initData() {
			const { businessInfo, operationInfo } = this.terminalConfig
			Object.assign(this.operationInfo, operationInfo)
			Object.assign(this.formData, businessInfo)
			this.formData = businessInfo
			this.secondaryCabinetModel = businessInfo.secondaryCabinetModel ? businessInfo.secondaryCabinetModel.split(',') : []
		},
		// 校验配置项
		validate() {
			return new Promise((resolve) => {
				this.$refs['businessConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					this.formData.secondaryCabinetModel = this.secondaryCabinetModel ? this.secondaryCabinetModel.join(',') : this.formData.secondaryCabinetModel
					const valueToNumMap = new Map(this.secondaryCabinetList.map((item) => [item.value, item.gf]))
					const matchedNums = this.secondaryCabinetModel.map((value) => {
						const gf = valueToNumMap.get(value)
						return gf !== undefined ? gf.toString() : 'null'
					})
					this.formData.secondaryCabinetModelNum = matchedNums.join(',')
					resolve({
						businessInfo: this.formData,
						operationInfo: this.operationInfo
					})
				})
			})
		}
	}
}
</script>

<style lang="less" scoped>
.select-box {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
	.text {
		margin: 0 20px;
	}
	.del {
		width: 16px;
		height: 16px;
		background: url('../../../assets/images/icon_del.png') no-repeat;
		background-size: 100% 100%;
		cursor: pointer;
	}
}
</style>
