<template>
	<gxx-form ref="qrCodeConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="二维码配置">
			<gxx-form-item label="类型" prop="manufacturer">
				<gxx-select :enableFilter="false" @change="changeManufacturer" :enableEmpty="false" :option="qrCodeManufacturerOptions" v-model="formData.manufacturer"></gxx-select>
			</gxx-form-item>
			<template v-if="isCom">
				<gxx-form-item label="连接地址" prop="path">
					<gxx-select v-model="formData.path" :enableEmpty="false" :option="comOptions"></gxx-select>
				</gxx-form-item>
				<gxx-form-item label="波特率" prop="baudRate">
					<gxx-select :enableEmpty="false" :option="baudRateOptions" v-model="formData.baudRate"></gxx-select>
				</gxx-form-item>
			</template>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { qrCodeManufacturerOptions, qrCodeManufacturerDic } from '@/common/libs/options'
export default {
	name: 'QrCode',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		},
		comOptions: {
			type: Array,
			default: () => []
		},
		baudRateOptions: {
			type: Array,
			default: () => []
		}
	},
	computed: {
		isCom() {
			return this.formData.manufacturer == qrCodeManufacturerDic.COM
		}
	},
	data() {
		return {
			qrCodeManufacturerOptions,
			formData: {
				manufacturer: qrCodeManufacturerDic.USB,
				path: '',
				baudRate: 0
			},
			rules: {
				manufacturer: [{ required: true, message: '请选择类型', trigger: 'blur change' }],
				path: [{ required: true, message: '请选择连接地址', trigger: 'blur change' }],
				baudRate: [{ required: true, message: '请选择波特率', trigger: 'blur change' }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			if (this.terminalConfig.qrCodeInfo) {
				Object.assign(this.formData, { ...this.terminalConfig.qrCodeInfo })
			}
		},
		changeManufacturer(item) {
			if (item.config) {
				this.formData.baudRate = item.config.baudRate || this.formData.baudRate
				this.formData.path = item.config.path || this.formData.path
			}
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['qrCodeConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						qrCodeInfo: {
							...this.terminalConfig.qrCodeInfo,
							manufacturer: Number(this.formData.manufacturer),
							path: this.formData.path,
							baudRate: Number(this.formData.baudRate)
						}
					})
				})
			})
		}
	}
}
</script>
