<template>
	<gxx-form ref="wristbandDrawerConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="手环抽屉配置">
			<gxx-form-item label="连接地址" prop="path">
				<gxx-select v-model="formData.path" :enableEmpty="false" :option="comOptions"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="波特率" prop="baudRate">
				<gxx-select :enableEmpty="false" :option="baudRateOptions" v-model="formData.baudRate"></gxx-select>
			</gxx-form-item>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
export default {
	name: 'WristbandDrawer',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		},
		comOptions: {
			type: Array,
			default: () => []
		},
		baudRateOptions: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			formData: {
				path: '',
				baudRate: 9600
			},
			rules: {
				baudRate: [{ required: true, message: '请选择连接地址', trigger: 'blur change' }],
				path: [{ required: true, message: '请选择波特率', trigger: 'blur change' }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			if (this.terminalConfig.wristbandDrawerInfo) {
				Object.assign(this.formData, { ...this.terminalConfig.wristbandDrawerInfo })
			}
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['wristbandDrawerConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						wristbandDrawerInfo: {
							...this.terminalConfig.wristbandDrawerInfo,
							path: this.formData.path,
							baudRate: Number(this.formData.baudRate)
						}
					})
				})
			})
		}
	}
}
</script>
