<template>
	<gxx-form ref="signFingerConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="签名捺印配置">
			<gxx-form-item label="类型" prop="boardType">
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="signBoardTypeOptions" v-model="formData.boardType" @change="changeBoardType"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="连接地址" prop="path">
				<gxx-input v-model="formData.path" placeholder="ws://localhost:8899/ESS"></gxx-input>
			</gxx-form-item>
			<template v-if="isShowIpAndPort">
				<gxx-form-item label="ip" prop="ip">
					<gxx-input v-model="formData.ip" placeholder="127.0.0.1"></gxx-input>
				</gxx-form-item>
				<gxx-form-item label="port" prop="port">
					<gxx-input v-model="formData.port" placeholder="45008"></gxx-input>
				</gxx-form-item>
			</template>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { signBoardTypeOptions, signBoardManufacturerDic, signBoardTypeDic, signBoardPath } from '@/common/libs/options'
export default {
	name: 'SignFinger',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		}
	},
	computed: {
		isShowIpAndPort() {
			return [signBoardTypeDic.TYPE_1, signBoardTypeDic.TYPE_4].includes(String(this.boardType))
		}
	},
	data() {
		return {
			signBoardTypeOptions,
			formData: {
				manufacturer: signBoardManufacturerDic.SELF_DEVELOP,
				boardType: signBoardTypeDic.TYPE_1,
				path: signBoardPath,
				ip: '',
				port: 0
			},
			rules: {
				boardType: [{ required: true, message: '请选择类型', trigger: 'blur change' }],
				path: [{ required: true, message: '请输入连接地址', trigger: 'blur' }],
				ip: [{ required: true, message: '请输入ip', trigger: 'blur', pattern: /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/ }],
				port: [{ required: true, message: '请输入port', trigger: 'blur', pattern: /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/ }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			if (this.terminalConfig.signFingerInfo) {
				Object.assign(this.formData, { ...this.terminalConfig.signFingerInfo })
			}
		},
		changeBoardType(item) {
			if (item.config) {
				this.formData.path = item.config.path || this.formData.path
				if (this.isShowIpAndPort) {
					this.formData.ip = item.config.ip || this.formData.ip
					this.formData.port = item.config.port || this.formData.port
				}
			}
		},
		validSignBoardType(boardType) {
			return Object.keys(signBoardTypeDic).includes(String(boardType))
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['signFingerConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						signFingerInfo: {
							...this.terminalConfig.signFingerInfo,
							manufacturer: Number(this.formData.manufacturer),
							boardType: Number(this.formData.boardType),
							path: this.formData.path,
							ip: this.formData.ip,
							port: Number(this.formData.port)
						}
					})
				})
			})
		}
	}
}
</script>
