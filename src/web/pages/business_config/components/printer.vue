<template>
	<gxx-form ref="printerConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="打印机配置">
			<gxx-form-item label="类型" prop="type">
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="printTypeOptions" v-model="formData.type"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="打印机" prop="name">
				<gxx-select v-model="formData.name" :enableEmpty="false" :option="printerList"></gxx-select>
			</gxx-form-item>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { printTypeOptions, printTypeDic } from '@/common/libs/options'
export default {
	name: 'Printer',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		}
	},
	data() {
		return {
			printTypeOptions,
			printerList: [],
			formData: {
				name: '',
				type: printTypeDic.COLOR
			},
			rules: {
				type: [{ required: true, message: '请选择类型', trigger: 'blur change' }],
				name: [{ required: true, message: '请选择打印机', trigger: 'blur change' }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			const { printerInfo } = this.terminalConfig
			Object.assign(this.formData, printerInfo)
		},
		handleMessage(message) {
			const { action, data } = message
			switch (action) {
				case 'init':
					data.flag === 0 && this.$emit('getPrinterList')
					break
				case 'getPrinterList':
					const list = data.flag === 0 ? data.result : []
					this.printerList = list.map((item) => {
						if (item.isDefault) {
							this.formData.name = this.formData.name || item.name || ''
						}
						return { label: item.name, value: item.name }
					})
					break
				default:
					break
			}
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['printerConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						printerInfo: {
							...this.terminalConfig.printerInfo,
							name: this.formData.name,
							type: this.formData.type
						}
					})
				})
			})
		}
	}
}
</script>
