<template>
	<gxx-form ref="cameraConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="摄像头配置">
			<gxx-form-item label="彩色摄像头" prop="colorCameraDeviceId">
				<gxx-select :enableEmpty="false" labelField="label" valueField="deviceId" :option="cameraList" v-model="formData.colorCameraDeviceId"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="彩色摄像头旋转角度">
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="cameraRotateOptions" v-model="formData.colorCameraRotateX"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="黑白摄像头">
				<gxx-select :enableEmpty="false" labelField="label" valueField="deviceId" :option="cameraList" v-model="formData.blackCameraDeviceId"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="黑白摄像头旋转角度">
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="cameraRotateOptions" v-model="formData.blackCameraRotateX"></gxx-select>
			</gxx-form-item>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { cameraRotateOptions, cameraRotateDic } from '@/common/libs/options'
export default {
	name: 'Camera',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		}
	},
	data() {
		return {
			cameraList: [],
			cameraRotateOptions,
			formData: {
				colorCameraName: '',
				colorCameraRotateX: String(cameraRotateDic.ROTATE_0),
				colorCameraDeviceId: '',
				blackCameraName: '',
				blackCameraRotateX: String(cameraRotateDic.ROTATE_0),
				blackCameraDeviceId: ''
			},
			rules: {
				colorCameraDeviceId: [{ required: true, message: '请选择彩色摄像头', trigger: 'blur change' }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			const { colorCameraInfo, blackCameraInfo } = this.terminalConfig
			if (colorCameraInfo) {
				Object.assign(this.formData, {
					colorCameraName: colorCameraInfo.name,
					colorCameraDeviceId: colorCameraInfo.deviceId,
					colorCameraRotateX: String(colorCameraInfo.rotateX)
				})
			}
			if (blackCameraInfo) {
				Object.assign(this.formData, {
					blackCameraName: blackCameraInfo.name,
					blackCameraDeviceId: blackCameraInfo.deviceId,
					blackCameraRotateX: String(blackCameraInfo.rotateX)
				})
			}
		},
		handleMessage(message) {
			const { action, data } = message
			switch (action) {
				case 'init':
					data.flag === 0 && this.$emit('getCameraList')
					break
				case 'getVideoDevice':
					this.cameraList = data.result || []
					break
				default:
					break
			}
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['cameraConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						colorCameraInfo: {
							...this.terminalConfig.colorCameraInfo,
							name: this.formData.colorCameraName,
							rotateX: Number(this.formData.colorCameraRotateX),
							deviceId: this.formData.colorCameraDeviceId
						},
						blackCameraInfo: {
							...this.terminalConfig.blackCameraInfo,
							name: this.formData.blackCameraName,
							rotateX: Number(this.formData.blackCameraRotateX),
							deviceId: this.formData.blackCameraDeviceId
						}
					})
				})
			})
		}
	}
}
</script>
