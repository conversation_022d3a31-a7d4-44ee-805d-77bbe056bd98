<template>
	<gxx-form ref="lightConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="补光灯配置">
			<gxx-form-item label="类型" prop="manufacturer">
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="lightManufacturerOptions" v-model="formData.manufacturer"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="连接地址" prop="path">
				<gxx-select v-if="isCom" v-model="formData.path" :enableEmpty="false" :option="comOptions"></gxx-select>
				<gxx-input v-else v-model="formData.path"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="波特率" v-if="isCom" prop="baudRate">
				<gxx-select :enableEmpty="false" :option="baudRateOptions" v-model="formData.baudRate"></gxx-select>
			</gxx-form-item>
			<template v-if="isShowIpt">
				<gxx-form-item label="锁板" prop="address">
					<gxx-input v-model="formData.address"></gxx-input>
				</gxx-form-item>
				<gxx-form-item label="锁号" prop="door">
					<gxx-input v-model="formData.door"></gxx-input>
				</gxx-form-item>
			</template>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { lightManufacturerOptions, lightManufacturerDic } from '@/common/libs/options'
export default {
	name: 'Light',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		},
		comOptions: {
			type: Array,
			default: () => []
		},
		baudRateOptions: {
			type: Array,
			default: () => []
		}
	},
	computed: {
		isCom() {
			return [lightManufacturerDic.K32_S, lightManufacturerDic.SELF_DEVELOP, lightManufacturerDic.D99_S].includes(Number(this.formData.manufacturer))
		},
		isShowIpt() {
			return this.formData.manufacturer == lightManufacturerDic.D99_S
		}
	},
	data() {
		return {
			lightManufacturerOptions,
			formData: {
				manufacturer: lightManufacturerDic.K32_S,
				path: 'COM1',
				baudRate: 0,
				address: '1',
				door: 12
			},
			rules: {
				manufacturer: [{ required: true, message: '请选择类型', trigger: 'blur change' }],
				path: [{ required: true, message: '请配置连接地址', trigger: 'blur change' }],
				baudRate: [{ required: true, message: '请选择波特率', trigger: 'blur change' }],
				address: [{ required: true, message: '请输入锁板', trigger: 'blur' }],
				door: [{ required: true, message: '请输入锁号', trigger: 'blur' }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			if (this.terminalConfig.supplementLightInfo) {
				Object.assign(this.formData, { ...this.terminalConfig.supplementLightInfo })
			}
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['lightConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						supplementLightInfo: {
							...this.terminalConfig.supplementLightInfo,
							manufacturer: Number(this.formData.manufacturer),
							path: this.formData.path,
							baudRate: Number(this.formData.baudRate),
							address: this.formData.address,
							door: this.formData.door
						}
					})
				})
			})
		}
	}
}
</script>
