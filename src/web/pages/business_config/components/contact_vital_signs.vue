<template>
	<gxx-form ref="contactVitalSignsConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="接触式体征检测配置">
			<gxx-form-item label="类型" prop="manufacturer">
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="contactVitalSignsManufacturerOptions" v-model="formData.manufacturer" @change="changeManufacturer"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="连接地址" prop="path">
				<gxx-select v-model="formData.path" :enableEmpty="false" :option="comOptions"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="波特率" prop="baudRate">
				<gxx-select v-model="formData.baudRate" :enableEmpty="false" :option="baudRateOptions"></gxx-select>
			</gxx-form-item>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { contactVitalSignsManufacturerOptions, contactVitalSignsManufacturerDic } from '@/common/libs/options'
export default {
	name: 'ContactVitalSigns',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		},
		comOptions: {
			type: Array,
			default: () => []
		},
		baudRateOptions: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			contactVitalSignsManufacturerOptions,
			formData: {
				manufacturer: contactVitalSignsManufacturerDic.TYPE_1,
				path: '',
				baudRate: 0
			},
			rules: {
				manufacturer: [{ required: true, message: '请选择类型', trigger: 'blur change' }],
				path: [{ required: true, message: '请选择连接地址', trigger: 'blur change' }],
				baudRate: [{ required: true, message: '请选择波特率', trigger: 'blur change' }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			if (this.terminalConfig.contactVitalSignsInfo) {
				Object.assign(this.formData, { ...this.terminalConfig.contactVitalSignsInfo })
			}
		},
		changeManufacturer(item) {
			if (item.config) {
				this.formData.path = item.config.path || this.formData.path
				this.formData.baudRate = item.config.baudRate || this.formData.baudRate
			}
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['contactVitalSignsConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						contactVitalSignsInfo: {
							...this.terminalConfig.contactVitalSignsInfo,
							manufacturer: Number(this.formData.manufacturer),
							path: this.formData.path,
							baudRate: Number(this.formData.baudRate)
						}
					})
				})
			})
		}
	}
}
</script>
