<template>
	<gxx-form :model="configInfo" showColon>
		<TitleItem titleText="RFID配置">
			<gxx-button slot="rightContent" v-if="isShowAddCabinetConfigBtn" type="title-icon" icon="icon-biaoticaozuoanniu-zengjia" @click="addCabinetConfig"></gxx-button>
			<div class="cabinet-config-list" v-for="(info, index) in configInfoList" :key="index">
				<div class="x-dialog-close gxx-icon-close close-btn" v-if="index != 0" @click="removeCabinetConfig(index)"></div>
				<gxx-form-item label="类型">
					<gxx-select @change="changeManufacturer(index)" :enableFilter="false" :enableEmpty="false" :option="rfidManufacturerOptions" v-model="info.manufacturer"></gxx-select>
				</gxx-form-item>
				<gxx-form-item label="连接地址">
					<gxx-select v-if="isCom(info.manufacturer)" :enableEmpty="false" :option="comOptions" v-model="info.path"></gxx-select>
					<gxx-input v-else v-model="info.path"></gxx-input>
				</gxx-form-item>
				<gxx-form-item label="波特率" v-if="isCom(info.manufacturer)">
					<gxx-select v-model="info.baudRate" :option="baudRateOptions"></gxx-select>
				</gxx-form-item>
				<gxx-form-item label="柜子编号">
					<gxx-select :multiple="true" :enableEmpty="false" :option="cabinetList" v-model="info.cabinetNum"></gxx-select>
				</gxx-form-item>
				<gxx-form-item label="通讯地址" v-if="isShowAddress(info.manufacturer)">
					<gxx-input v-if="isCom(info.manufacturer)" v-model="info.address" placeholder="例如1;2"></gxx-input>
					<gxx-input v-else v-model="info.address" placeholder="例如127.0.0.1:4001;127.0.0.1:4002'"></gxx-input>
				</gxx-form-item>
				<gxx-form-item label="单板最大控制数">
					<gxx-input v-model="info.maxNum" placeholder="例如:18;36"></gxx-input>
				</gxx-form-item>
			</div>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { createCabinetList, rfidManufacturerOptions, rfidManufacturerDic } from '@/common/libs/options'
export default {
	name: 'RFID',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		},
		comOptions: {
			type: Array,
			default: () => []
		},
		baudRateOptions: {
			type: Array,
			default: () => []
		}
	},
	computed: {
		isShowAddCabinetConfigBtn() {
			return this.configInfoList.length < this.rfidManufacturerOptions.length
		}
	},
	data() {
		return {
			cabinetList: createCabinetList(),
			rfidManufacturerOptions,
			configInfoList: [],
			configInfo: {
				manufacturer: rfidManufacturerDic.K32_1,
				path: '',
				baudRate: 0,
				cabinetNum: '',
				address: 0,
				maxNum: 6
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			const { rfidInfo } = this.terminalConfig
			if (!rfidInfo) {
				return this.configInfoList.push({ ...this.configInfo })
			}
			const obj = {}
			for (const key in rfidInfo) {
				const rfid = rfidInfo[key]
				rfid.manufacturer = rfid.manufacturer || rfidManufacturerDic.K32_1
				if (obj[rfid.manufacturer]) {
					obj[rfid.manufacturer].address += rfid.address ? `;${rfid.address}` : ''
					obj[rfid.manufacturer].maxNum += rfid.maxNum ? `;${rfid.maxNum}` : ''
					obj[rfid.manufacturer].cabinetNum += `,${key}`
				} else {
					obj[rfid.manufacturer] = { ...rfid, cabinetNum: key }
				}
			}
			this.$set(this, 'configInfoList', Object.values(obj))
		},
		isCom(manufacturer) {
			return [rfidManufacturerDic.D99_1, rfidManufacturerDic.D99_2].includes(Number(manufacturer))
		},
		isShowAddress(manufacturer) {
			return [rfidManufacturerDic.K32_2, rfidManufacturerDic.D99_1, rfidManufacturerDic.D99_2].includes(Number(manufacturer))
		},
		// 获取第一个未配置的RFID
		getNotSelectedFirstRfidManufacturer() {
			const rfidManufacturerList = this.configInfoList.map((item) => {
				return item.manufacturer
			})
			const notSelectedLockManufacturer = this.rfidManufacturerOptions.filter((item) => {
				return !rfidManufacturerList.includes(String(item.value))
			})
			if (!notSelectedLockManufacturer.length) {
				return null
			}
			return notSelectedLockManufacturer[0]
		},
		addCabinetConfig() {
			const rfid = this.getNotSelectedFirstRfidManufacturer()
			if (rfid) {
				this.configInfoList.push({ ...rfid?.config, manufacturer: rfid.value })
			}
		},
		removeCabinetConfig(index) {
			this.configInfoList.splice(index, 1)
		},
		changeManufacturer(index) {
			const rfid = this.configInfoList[index]
			const item = this.configInfoList.find((item, i) => {
				return index != i && item.manufacturer == rfid.manufacturer
			})
			if (item) {
				rfid.manufacturer = ''
				return this.$baseTip({ message: '该RFID类型已配置！', type: 'error' })
			}
			rfid.cabinetNum = rfid.cabinetNum || ''
			const rfidItem = rfidManufacturerOptions.find((item) => item.value == rfid.manufacturer)
			for (const key in rfidItem?.config) {
				rfid[key] = rfid[key] ? rfid[key] : rfidItem?.config[key]
			}
		},
		validate() {
			return new Promise((resolve) => {
				let bool = true
				let cabinetNum = ''
				const rfidInfo = {}
				for (let i = 0; i < this.configInfoList.length; i++) {
					const item = this.configInfoList[i]
					if (!item.path) {
						this.$baseTip({ message: `请配置第${i + 1}个RFID连接地址信息！`, type: 'error' })
						bool = false
						break
					}
					if (this.isCom(item.manufacturer) && !item.baudRate) {
						this.$baseTip({ message: `请配置第${i + 1}个RFID波特率信息！`, type: 'error' })
						bool = false
						break
					}
					if (!item.cabinetNum) {
						this.$baseTip({ message: `请配置第${i + 1}个RFID柜子编号信息！`, type: 'error' })
						bool = false
						break
					} else {
						// 验证柜子编号是否重复
						const isRepeat = item.cabinetNum.split(',').some((num) => {
							return cabinetNum.indexOf(num) != -1
						})
						if (isRepeat) {
							this.$baseTip({ message: `请配置第${i + 1}个RFID柜子编号信息和其他柜子编号重复！`, type: 'error' })
							bool = false
							break
						}
						cabinetNum += item.cabinetNum
					}
					const isShowAddress = this.isShowAddress(item.manufacturer)
					if (isShowAddress) {
						if (String(item.address).length == 0) {
							this.$baseTip({ message: `请配置第${i + 1}个RFID锁板拨码信息！`, type: 'error' })
							bool = false
							break
						}
					} else {
						item.address = ''
					}
					if (!item.maxNum) {
						this.$baseTip({ message: `请配置第${i + 1}个RFID单锁板最大控制数信息！`, type: 'error' })
						bool = false
						break
					}
					const cabinetNumList = item.cabinetNum.split(',')
					const addressList = String(item.address).split(';')

					const maxNumList = String(item.maxNum).split(';')
					if ((isShowAddress && cabinetNumList.length != addressList.length) || cabinetNumList.length != maxNumList.length) {
						this.$baseTip({ message: `请配置第${i + 1}个RFID配置信息错误！`, type: 'error' })
						bool = false
						break
					}
					// 组装数据
					for (let j = 0; j < cabinetNumList.length; j++) {
						rfidInfo[cabinetNumList[j]] = {
							isMain: i == 0 && j == 0,
							manufacturer: Number(item.manufacturer),
							path: item.path,
							baudRate: Number(item.baudRate) || 9600,
							address: isShowAddress ? addressList[j] : '',
							maxNum: Number(maxNumList[j])
						}
					}
				}
				resolve(bool ? { rfidInfo } : false)
			})
		}
	}
}
</script>

<style lang="less" scoped>
/deep/ .cabinet-config-list {
	position: relative;
	display: flex;
	flex-wrap: wrap;
	width: 100%;
	margin-bottom: 10px;
	.close-btn {
		z-index: 1;
	}
}
</style>
