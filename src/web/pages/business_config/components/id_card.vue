<template>
	<gxx-form ref="idCardConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="证件读卡器配置">
			<gxx-form-item label="类型" prop="manufacturer">
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="idCardManufacturerOptions" v-model="formData.manufacturer"></gxx-select>
			</gxx-form-item>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { idCardManufacturerOptions, idCardManufacturerDic } from '@/common/libs/options'
export default {
	name: 'IdCard',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		}
	},
	data() {
		return {
			idCardManufacturerOptions,
			formData: {
				manufacturer: idCardManufacturerDic.USB
			},
			rules: {
				manufacturer: [{ required: true, message: '请选择类型', trigger: 'blur change' }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			const { idCardInfo } = this.terminalConfig
			Object.assign(this.formData, idCardInfo)
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['idCardConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						idCardInfo: {
							...this.terminalConfig.idCardInfo,
							manufacturer: Number(this.formData.manufacturer)
						}
					})
				})
			})
		}
	}
}
</script>
