<template>
	<gxx-form ref="nonContactVitalSignsConfig" :model="formData" :rules="rules" showColon>
		<TitleItem titleText="非接触式体征检测配置">
			<gxx-form-item label="类型" prop="manufacturer">
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="nonContactVitalSignsManufacturerOptions" v-model="formData.manufacturer" @change="changeManufacturer"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="连接地址" prop="path">
				<gxx-input v-model="formData.path" placeholder="127.0.0.1:7382"></gxx-input>
			</gxx-form-item>
		</TitleItem>
	</gxx-form>
</template>

<script>
import TitleItem from './title_item'
import { nonContactVitalSignsManufacturerOptions, nonContactVitalSignsManufacturerDic } from '@/common/libs/options'
export default {
	name: 'NonContactVitalSigns',
	components: {
		TitleItem
	},
	props: {
		terminalConfig: {
			type: Object,
			default: () => {},
			required: true
		}
	},
	data() {
		return {
			nonContactVitalSignsManufacturerOptions,
			formData: {
				manufacturer: nonContactVitalSignsManufacturerDic.TYPE_1,
				path: ''
			},
			rules: {
				manufacturer: [{ required: true, message: '请选择类型', trigger: 'blur change' }],
				path: [{ required: true, message: '请输入连接地址', trigger: 'blur' }]
			}
		}
	},
	created() {
		this.initData()
	},
	methods: {
		initData() {
			const { nonContactVitalSignsInfo } = this.terminalConfig
			Object.assign(this.formData, nonContactVitalSignsInfo)
		},
		changeManufacturer(item) {
			if (item.config) {
				this.formData.path = item.config.path || this.formData.path
			}
		},
		validate() {
			return new Promise((resolve) => {
				this.$refs['nonContactVitalSignsConfig'].validate((bool) => {
					if (!bool) {
						resolve(false)
						return
					}
					resolve({
						nonContactVitalSignsInfo: {
							...this.terminalConfig.nonContactVitalSignsInfo,
							manufacturer: Number(this.formData.manufacturer),
							path: this.formData.path
						}
					})
				})
			})
		}
	}
}
</script>
