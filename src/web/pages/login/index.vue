<template>
	<div class="login">
		<img class="logo" src="./assets/images/logo.png" />
		<div class="container">
			<div class="box">
				<p class="title" :title="operationInfo.logoName">{{ operationInfo.logoName }}</p>
				<div class="form">
					<div class="group account-box">
						<span class="label">账户</span>
						<div class="input-group">
							<img class="icon" src="./assets/images/icon_account.png" />
							<gxx-input class="ipt" type="text" placeholder="请输入账号" v-model="user.loginId" />
						</div>
					</div>
					<div class="group password-box">
						<span class="label">密码</span>
						<div class="input-group">
							<img class="icon" src="./assets/images/icon_password.png" />
							<gxx-input class="ipt" type="password" placeholder="请输入密码" @keydown.enter.native="login" v-model="user.password" />
						</div>
					</div>
					<div class="tool">
						<div class="password">
							<gxx-checkbox class="check-box" v-model="rememberPassword">记住密码</gxx-checkbox>
						</div>
						<div class="tip">忘记密码请联系管理员</div>
					</div>
					<div class="btn">
						<gxx-button class="submit-btn" @click="login">登录</gxx-button>
					</div>
				</div>
				<p class="version">{{ version }}</p>
			</div>
		</div>
		<div class="footer">
			<p class="name" v-if="operationInfo.name">{{ operationInfo.name }}</p>
			<p class="copyright" v-if="operationInfo.copyright">{{ operationInfo.copyright }}</p>
		</div>
	</div>
</template>

<script>
import Crypto from '@/common/libs/crypto'
export default {
	name: 'Login',
	data() {
		return {
			version: 'V1.0',
			rememberPassword: false,
			rememberPasswordKey: 'remember',
			user: {
				loginId: '',
				password: ''
			},
			isLoad: false,
			operationInfo: {
				logoName: '',
				name: '',
				copyright: ''
			}
		}
	},
	created() {
		this.getTerminalConfigInfo()
		const rememberStr = localStorage.getItem(this.rememberPasswordKey)
		if (rememberStr) {
			const user = JSON.parse(Crypto.aesDecrypt(rememberStr))
			this.rememberPassword = user.rememberPassword
			this.user.loginId = user.loginId
			this.user.password = user.password
		}
	},
	methods: {
		getTerminalConfigInfo() {
			this.$store.dispatch('config/getTerminalConfigInfo').then((data) => {
				if (data) {
					Object.assign(this.operationInfo, data.operationInfo)
					this.version = `V${data.version}` || this.version
				}
			})
		},
		valid() {
			if (!this.user.loginId) {
				this.$baseTip({ message: '请输入帐号!', type: 'warning' })
				return false
			} else if (!this.user.password) {
				this.$baseTip({ message: '请输入密码!', type: 'warning' })
				return false
			}
			return true
		},
		login() {
			if (this.isLoad || !this.valid()) {
				return
			}
			this.isLoad = true
			const params = {
				loginId: this.user.loginId,
				password: Crypto.aesEncrypt(this.user.password)
			}
			this.$http.userApi
				.login(params)
				.then((res) => {
					const { data, code } = res
					if (code === 200) {
						if (this.rememberPassword) {
							localStorage.setItem(this.rememberPasswordKey, Crypto.aesEncrypt(JSON.stringify({ loginId: this.user.loginId, password: this.user.password, rememberPassword: this.rememberPassword })))
						} else {
							localStorage.removeItem(this.rememberPasswordKey)
						}
						this.$store.commit('user/setToken', data.token)
						this.$store.commit('user/setUserInfo', data)
						this.$router.push('/operation')
					}
				})
				.finally(() => {
					this.isLoad = false
				})
		}
	}
}
</script>

<style lang="less" scoped>
.login {
	width: 100vw;
	height: 100vh;
	position: relative;
	overflow: hidden;
	background: url('./assets/images/back.png') no-repeat center center;
	background-size: cover;
	.logo {
		position: absolute;
		top: 40px;
		left: 40px;
	}
	.container {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 238px;
		width: 500px;
		height: 604px;
		background: #ffffff;
		box-shadow: 0 5px 30px 1px rgba(95, 112, 154, 0.2);
		border-radius: 8px;
		.box {
			padding: 50px 42px 20px;
			p.title {
				width: 100%;
				height: 42px;
				font-weight: bold;
				font-size: 32px;
				color: #2b3646;
				text-align: center;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.form {
				padding: 0 8px;
				margin: 55px 0;
				.group {
					display: flex;
					flex-direction: column;
					justify-content: flex-start;
					align-items: flex-start;
					.label {
						height: 24px;
						line-height: 24px;
						font-size: 18px;
						color: #2b3646;
						margin-bottom: 10px;
					}
					.input-group {
						width: 100%;
						height: 50px;
						background: #ffffff;
						position: relative;
						.icon {
							position: absolute;
							top: 15px;
							left: 15px;
							width: 18.37px;
							height: 19.9px;
						}
						/deep/ .ipt {
							width: 100%;
							height: 50px;
							line-height: 50px;
							border-radius: 8px;
							input {
								border: 1px solid #cfd9f0;
								background-color: inherit;
								padding-left: 48px;
								&:focus {
									box-shadow: 0 3px 8px 1px rgba(7, 118, 224, 0.45);
									border: 2px solid #0089ff;
								}
							}
							.clear {
								line-height: 20px;
							}
						}
					}
				}
				.account-box {
					margin-bottom: 32px;
				}
				.password-box {
					margin-bottom: 16px;
				}
				.tool {
					display: flex;
					justify-content: space-between;
					margin-bottom: 77px;
					.password {
						/deep/ .check-box.gxx-checkbox-checked {
							.gxx-checkbox-icon {
								background: #0089ff;
								border-radius: 4px;
								border-color: #0089ff;
							}
							.gxx-checkbox-content {
								margin-left: 3px;
							}
						}
					}
					.tip {
						font-size: 14px;
						color: #5f709a;
					}
				}
				.btn {
					display: flex;
					justify-content: center;
					.submit-btn {
						display: flex;
						justify-content: center;
						width: 100%;
						height: 50px;
						background: linear-gradient(152deg, #0f95fd 0%, #0089ff 100%);
						border-radius: 8px;
						font-size: 20px;
						color: #ffffff;
						cursor: pointer;
					}
				}
			}
			p.version {
				height: 19px;
				line-height: 19px;
				font-size: 14px;
				color: #a2acc6;
				position: relative;
				text-align: center;
				&::before,
				&::after {
					content: '';
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 70px;
					height: 1px;
					background: #a2acc6;
				}
				&::before {
					left: 58px;
				}
				&::after {
					right: 58px;
				}
			}
		}
	}
	.footer {
		width: 100%;
		position: absolute;
		bottom: 36px;
		display: flex;
		justify-content: center;
		flex-direction: column;
		p {
			text-align: center;
			font-size: 16px;
			font-style: normal;
			text-transform: none;
			color: #2b3646;
		}
		.copyright {
			margin-top: 8px;
			color: #5f709a;
		}
	}
}
</style>
