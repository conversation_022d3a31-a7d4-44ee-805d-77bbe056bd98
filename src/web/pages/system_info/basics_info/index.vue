<template>
	<div class="basics-info" v-partial-loading="isLoad">
		<div class="container">
			<img class="bg" src="~@/web/assets/images/basics_info_bg.png" alt="" />
			<div class="search-box">
				<!-- <gxx-input class="ipt" @keyup.enter.native="checkItemNumber" v-model="itemNumber" placeholder="请输入料号搜索" />
				<gxx-button class="btn" @click="checkItemNumber">搜索</gxx-button> -->
				<gxx-select v-if="isRole" width="640px" @change="showDialog" v-model="itemNumber" :labelFieldFormat="itemNumberLabelFieldFormat" valueField="itemNumber" :option="allItemNumber" placeholder="请选择料号"></gxx-select>
			</div>
			<div class="content-box">
				<div class="config">
					<div class="item" v-for="item in configList" :key="item.key">
						<span class="label">{{ item.label }}:</span>
						<p class="value">{{ itemNumberInfo[item.key] || '--' }}</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import ItemNumberDialog from './item_number_dialog/index'
export default {
	name: 'BasicsInfo',
	computed: {
		...mapGetters({
			config: 'config/data',
			storeItemNumber: 'config/itemNumber',
			userInfo: 'user/userInfo',
			roles: 'user/roles'
		}),
		isRole() {
			return [this.roles.sysAdmin, this.roles.admin].includes(Number(this.userInfo.role))
		}
	},
	data() {
		return {
			configList: [
				{
					label: '料号',
					key: 'itemNumber'
				},
				{
					label: '名称',
					key: 'name'
				},
				{
					label: '型号',
					key: 'model'
				},
				{
					label: '代码',
					key: 'code'
				},
				{
					label: '版本',
					key: 'version'
				},
				{
					label: '标识',
					key: 'tag'
				},
				{
					label: '硬件',
					key: 'hardwareInfo'
				},
				{
					label: '嵌软',
					key: 'embededSoftware'
				},
				// {
				// 	label: '固件',
				// 	key: 'firmware'
				// },
				{
					label: '授权状态',
					key: 'authState'
				}
			],
			itemNumberInfo: {
				itemNumber: '',
				name: '',
				model: '',
				code: '',
				version: '',
				tag: '',
				hardwareInfo: '',
				embededSoftware: '',
				// firmware: '',
				authState: ''
			},
			itemNumber: '',
			isLoad: false,
			dialog: null,
			allItemNumber: []
		}
	},
	created() {
		this.getSystemProperties()
		this.getDeviceAuthInfo()
		this.getAllItemNumber()
		this.$store.dispatch('config/getTerminalConfigInfo').then((res) => {
			Object.assign(this.itemNumberInfo, { ...res.itemNumberInfo, embededSoftware: res.version ? `V${res.version}` : '' })
		})
	},
	methods: {
		getAllItemNumber() {
			this.$http.itemNumberApi.getAllItemNumber().then(
				(res) => {
					const { code, data } = res
					this.allItemNumber = code == 200 ? data : []
				},
				(err) => {
					this.allItemNumber = []
				}
			)
		},
		itemNumberLabelFieldFormat(item) {
			return `${item.itemNumber}(${item.name || '未知'})`
		},
		getSystemProperties() {
			this.isLoad = true
			this.$http.systemApi
				.getSystemProperties('sysVersion,memory,cpuName')
				.then((res) => {
					const { code, data } = res
					if (code == 200) {
						Object.assign(this.itemNumberInfo, { hardwareInfo: `${data.cpuName} ${this.MbToGb(data.memory)}` })
					}
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		MbToGb(mb) {
			return `${Math.ceil(Number(mb) / 1024)}GB`
		},
		getDeviceAuthInfo() {
			this.$http.appApi.getDeviceAuthInfo().then((res) => {
				const { code } = res
				Object.assign(this.itemNumberInfo, { authState: `${code == 200 ? '已' : '未'}授权` })
			})
		},
		/**
		 * 根据料号查询配置信息
		 */
		// checkItemNumber() {
		// 	if (!this.itemNumber) {
		// 		return
		// 	}
		// 	this.isLoad = true
		// 	this.$http.itemNumberApi
		// 		.checkItemNumber({ itemNumber: this.itemNumber })
		// 		.then((res) => {
		// 			const { data, code } = res
		// 			if (code === 200) {
		// 				this.showDialog(data)
		// 			}
		// 		})
		// 		.finally(() => {
		// 			this.isLoad = false
		// 		})
		// },
		showDialog(itemNumberInfo) {
			this.dialog = this.$GxxDialog({
				title: '产品详情',
				component: ItemNumberDialog,
				componentParams: itemNumberInfo
			})
			this.dialog.on.then((res) => {
				this.dialog = null
				if (res.type != 'confirm' || itemNumberInfo.itemNumber == this.storeItemNumber) {
					this.itemNumber = ''
					return
				}
				const itemNumberConfig = itemNumberInfo.config ? JSON.parse(itemNumberInfo.config) : {}
				this.saveConfig({
					...itemNumberConfig,
					itemNumberInfo: {
						name: itemNumberInfo.name,
						itemNumber: itemNumberInfo.itemNumber,
						model: itemNumberInfo.model,
						version: itemNumberInfo.version,
						code: itemNumberInfo.code,
						tag: itemNumberInfo.tag
					}
				})
			})
		},
		saveConfig(config) {
			this.$store.dispatch('config/setTerminalConfigInfo', config).then((res) => {
				if (res) {
					window.location.reload()
				}
			})
		}
	},
	beforeDestroy() {
		this.dialog && this.dialog.close()
		this.dialog = null
	}
}
</script>

<style lang="less" scoped>
.basics-info {
	display: flex;
	width: 100%;
	height: 100%;
	justify-content: center;
	.container {
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
		.bg {
			width: 354px;
			height: 200px;
			margin-top: 20px;
		}
		.search-box {
			display: flex;
			margin-bottom: 20px;
			// /deep/ .ipt {
			// 	position: relative;
			// 	width: 640px;
			// 	height: 40px;
			// 	background: #ffffff;
			// 	border-radius: 4px;
			// 	margin-right: 10px;
			// 	input {
			// 		padding-left: 40px;
			// 	}
			// 	&::before {
			// 		content: '';
			// 		width: 18px;
			// 		height: 18px;
			// 		position: absolute;
			// 		top: 12px;
			// 		bottom: 10px;
			// 		left: 13px;
			// 		background: url('~@/web/assets/images/icon_search.png') no-repeat center center;
			// 	}
			// }
			/deep/ .gxx-select-wrap {
				margin-right: 20px;
				.select-input {
					padding-left: 10px;
					height: 40px;
					line-height: 40px;
				}
			}
			.btn {
				display: flex;
				justify-content: center;
				width: 80px;
				height: 40px;
				background: #0089ff;
				border-radius: 4px;
			}
		}
		.content-box {
			width: 1000px;
			background: #f9fafd;
			border-radius: 4px;
			border: 1px solid #cee0f0;
			.config {
				margin: 28px 160px;
				display: flex;
				flex-flow: row wrap;
				justify-content: flex-start;
				align-items: center;
				align-content: center;
				.item {
					width: calc(50% - 10px);
					display: flex;
					justify-content: flex-start;
					align-items: center;
					font-size: 16px;
					height: 24px;
					margin: 12px 5px;
					.label {
						color: #5f709a;
					}
					.value {
						flex: 1;
						margin: 0 16px;
						color: #2b3646;
						text-overflow: ellipsis;
						overflow: hidden;
						white-space: nowrap;
					}
				}
			}
		}
	}
}
</style>
