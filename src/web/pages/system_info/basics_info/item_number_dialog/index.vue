<template>
	<div class="item-number-dialog">
		<div class="content-box">
			<div class="item">
				<p class="label">料号：</p>
				<p class="value">{{ params.itemNumber }}</p>
			</div>
			<div class="item">
				<p class="label">名称：</p>
				<p class="value">{{ params.name }}</p>
			</div>
			<div class="item">
				<p class="label">型号：</p>
				<p class="value">{{ params.model }}</p>
			</div>
			<div class="item">
				<p class="label">版本：</p>
				<p class="value">{{ params.version }}</p>
			</div>
			<div class="item">
				<p class="label">代码：</p>
				<p class="value">{{ params.code }}</p>
			</div>
			<div class="item">
				<p class="label">标识：</p>
				<p class="value">{{ params.tag }}</p>
			</div>
		</div>
		<div class="footer-box">
			<gxx-button type="info" @click="close('cancel')">取消</gxx-button>
			<gxx-button @click="close('confirm')">确认</gxx-button>
		</div>
	</div>
</template>

<script>
export default {
	name: 'ItemNumberDialog',
	props: ['params'],
	methods: {
		close(type) {
			this.$parent.close(type)
		}
	}
}
</script>

<style lang="less" scoped>
.item-number-dialog {
	padding: 10px 20px;
	.content-box {
		width: 410px;
		display: flex;
		flex-direction: column;
		.item {
			height: 40px;
			line-height: 40px;
			font-size: 22px;
			display: flex;
			.value {
				flex: 1;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}
		}
	}
	.footer-box {
		margin-top: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
		.gxx-button + .gxx-button {
			margin-left: 20px;
		}
	}
}
</style>
