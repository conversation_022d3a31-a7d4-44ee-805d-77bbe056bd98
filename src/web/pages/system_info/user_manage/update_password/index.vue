<template>
	<div class="update-password-dialog">
		<update-password ref="updatePasswordRef" :params="params"></update-password>
		<div class="common-dialog-footer tools">
			<gxx-button type="info" @click="cancel">取消</gxx-button>
			<gxx-button @click="confirm">确定</gxx-button>
		</div>
	</div>
</template>

<script>
import UpdatePassword from '@/web/layouts/components/update_password'
export default {
	name: 'UpdatePasswordDialog',
	components: {
		UpdatePassword
	},
	props: ['params'],
	methods: {
		cancel() {
			this.$parent.close('cancel')
		},
		async confirm() {
			const bool = await this.$refs.updatePasswordRef.confirm()
			if (!bool) {
				return
			}
			this.$parent.close('confirm')
		}
	}
}
</script>

<style lang="less" scoped>
.update-password-dialog {
	width: 450px;
	padding-bottom: 20px;
	.tools {
		text-align: center;
		.gxx-button + .gxx-button {
			margin-left: 20px;
		}
	}
}
</style>
