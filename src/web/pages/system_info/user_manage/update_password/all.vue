<template>
	<div class="update-password-dialog">
		<gxx-form :model="form" :label-width="80" :label-position="'justify'" class="form" ref="userForm" :showColon="true" :rules="rules">
			<gxx-form-item label="新密码" prop="newPassword">
				<gxx-input v-model="form.newPassword" type="password"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="确认密码" prop="confirmNewPassword">
				<gxx-input v-model="form.confirmNewPassword" type="password"></gxx-input>
			</gxx-form-item>
		</gxx-form>
		<div class="common-dialog-footer tools">
			<gxx-button type="info" @click="cancel">取消</gxx-button>
			<gxx-button @click="confirm">确定</gxx-button>
		</div>
	</div>
</template>

<script>
import Crypto from '@/common/libs/crypto'
export default {
	name: 'UpdatePasswordAllDialog',
	props: ['params'],
	data() {
		return {
			form: {},
			userData: {},
			rules: {
				newPassword: [{ required: true, message: '新密码不能为空', trigger: 'blur' }],
				confirmNewPassword: [{ required: true, message: '确认密码不能为空', trigger: 'blur' }]
			}
		}
	},
	methods: {
		cancel() {
			this.$parent.close('cancel')
		},
		async confirm() {
			if (this.form.newPassword == this.form.confirmNewPassword) {
				const password = Crypto.aesEncrypt(this.form.newPassword)
				this.$http.userApi.update({ ...this.userData, password }).then((res) => {
					const { code } = res
					if (code == 200) {
						this.$baseTip({ message: '更新成功', type: 'success' })
						this.$parent.close('confirm')
					}
				})
			} else {
				this.$baseTip.error('两次新密码输入不一致，请重新输入!')
			}
		}
	},
	created() {
		this.$http.userApi.findInfoById({ id: this.params.id }).then((res) => {
			this.userData = res.data
		})

		// Object.assign(this.form, this.params)
	}
}
</script>

<style lang="less" scoped>
.update-password-dialog {
	width: 450px;
	padding-bottom: 20px;
	.tools {
		text-align: center;
		.gxx-button + .gxx-button {
			margin-left: 20px;
		}
	}
}
</style>
