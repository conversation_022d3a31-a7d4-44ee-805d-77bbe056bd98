<template>
	<div class="message-todo-wrap">
		<div class="search-container">
			<div class="search-item">
				<div class="search-item-label">账号</div>
				<span class="search-item-colon">:</span>
				<gxx-input v-model="requestParams.loginId"></gxx-input>
			</div>
			<div class="search-item">
				<div class="search-item-label">名称</div>
				<span class="search-item-colon">:</span>
				<gxx-input v-model="requestParams.name"></gxx-input>
			</div>
			<div class="search-item">
				<div class="search-button">
					<gxx-button @click="searchList">搜索</gxx-button>
				</div>
				<div class="search-button">
					<gxx-button type="info" @click="clearSearch">清空</gxx-button>
				</div>
			</div>
			<div class="right-corner-btn">
				<div class="btn-item-wrap" v-if="config?.businessInfo?.communicationMode != communicationModeDic.ONLINE" @click="fileModel = true">
					<i class="btn-icon btn-icon-add"></i>
					<div class="btn-title">导入用户</div>
				</div>
				<!-- <div class="btn-item-wrap" @click="handleUser()">
					<i class="btn-icon btn-icon-add"></i>
					<div class="btn-title">添加</div>
				</div> -->
			</div>
		</div>
		<div class="table-container">
			<gxx-table :data="tableData" :isFixedHead="true" :maxHeight="650">
				<gxx-table-column label="序号" type="index" width="60"></gxx-table-column>
				<gxx-table-column label="头像">
					<template v-slot="row">
						<gxx-image :preview-src-list="[row.faceImg]" class="avatar" fit="cover" :src="row.faceImg"></gxx-image>
					</template>
				</gxx-table-column>
				<gxx-table-column label="账号" prop="loginId"></gxx-table-column>
				<gxx-table-column label="名称" prop="name"></gxx-table-column>
				<gxx-table-column label="角色" prop="roleCn"></gxx-table-column>
				<gxx-table-column label="年龄" prop="age" width="50"></gxx-table-column>
				<gxx-table-column label="性别" prop="sexCn" width="50"></gxx-table-column>
				<gxx-table-column label="注册时间" prop="createTime" width="180"></gxx-table-column>
				<gxx-table-column label="登录时间" prop="loginTime" width="180"></gxx-table-column>
				<gxx-table-column label="更新时间" prop="updateTime" width="180"></gxx-table-column>
				<gxx-table-column label="操作" width="360">
					<template slot-scope="row">
						<gxx-button plain @click="resetPassword(row.id)">重置密码</gxx-button>
						<gxx-button plain @click="handleUser(row)">编辑</gxx-button>
						<gxx-button plain @click="updatePassword(row.id)">修改密码</gxx-button>
						<gxx-button plain v-show="row.loginId != 'gly'" type="danger" @click="deleteRow(row.id)" v-if="isShowDeleteBtn(row.role)">删除</gxx-button>
					</template>
				</gxx-table-column>
			</gxx-table>
			<div class="pagination-content">
				<gxx-pagination :total="total" @changeSize="changeSize" @change="getTableData" :pageSize="requestParams.size" v-model="requestParams.page"></gxx-pagination>
			</div>
		</div>
		<gxx-dialog v-model="fileModel" width="720px" height="600px" :showFooter="false">
			<div class="item-box">
				<div class="export-template" @click="exportExcel()">导入用户模板.xlsx</div>
				<div class="item">
					<p class="item-label">批量导入用户</p>
					<span class="item-colon">:</span>
					<div class="upload-form">
						<gxx-input :disabled="true" v-model="importUserListName"></gxx-input>
						<gxx-button class="btn" @click="getImport()">{{ importUserListName ? '重新上传' : '上传' }}</gxx-button>
						<i v-if="importUserListName" class="iconfont icon-shurukuang-tishizhengque upload-icon"></i>
						<input ref="fileInput" type="file" @change="submitFile" v-show="false" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
					</div>
				</div>
				<div class="item">
					<p class="item-label">批量导入头像</p>
					<span class="item-colon">:</span>
					<div class="upload-form">
						<gxx-input :disabled="true" v-model="importFaceListName"></gxx-input>
						<gxx-button class="btn" @click="getFaceImport()">{{ importFaceListName ? '重新上传' : '上传' }}</gxx-button>
						<i v-if="importFaceListName" class="iconfont icon-shurukuang-tishizhengque upload-icon"></i>
						<input ref="fileFaceInput" type="file" @change="submitFaceFile" v-show="false" accept=".zip" />
					</div>
				</div>
			</div>
			<div class="log-box" v-if="userLogList.length > 0">
				总共导入用户{{ userLogList.length }}， 成功{{ userLogList.filter((log) => log.code === 200).length }} 失败{{ userLogList.filter((log) => log.code != 200).length }} 失败详情{{
					userLogList
						.filter((log) => log.code != 200)
						.map((log) => log.user)
						.join(', ')
				}}
			</div>
		</gxx-dialog>
	</div>
</template>

<script>
import { communicationModeDic } from '@/common/libs/options'
import UserDialog from './user/index'
import UpdatePasswordAllDialog from './update_password/all'
import { mapGetters } from 'vuex'
const XLSX = require('xlsx')
export default {
	name: 'User',
	computed: {
		...mapGetters({
			userInfo: 'user/userInfo',
			roles: 'user/roles',
			config: 'config/data'
		})
	},
	data() {
		return {
			communicationModeDic,
			tableData: [],
			total: 0,
			requestParams: {
				size: 20,
				page: 1,
				loginId: '',
				name: '',
				userType: '1,2'
			},
			fileModel: false,
			importUserListName: '',
			uploadStatus: false,
			importFaceListName: '',
			userLogList: []
		}
	},
	mounted() {
		this.getTableData()
	},
	methods: {
		getFaceImport() {
			this.$refs.fileFaceInput.value = []
			this.$refs.fileFaceInput.click()
		},
		getImport() {
			this.$refs.fileInput.value = []
			this.$refs.fileInput.click()
		},
		// 用户照片导入
		submitFaceFile() {
			const file = this.$refs.fileFaceInput.files[0]
			const formData = new FormData()
			formData.append('file', file)
			this.$http.userApi
				.faceListImport(formData)
				.then((res) => {
					const { code } = res
					if (code == 200) {
						this.importFaceListName = file.name
						this.clearSearch()
					}
				})
				.catch((err) => {
					console.log(err)
				})
		},
		// 用户导入
		submitFile() {
			const file = this.$refs.fileInput.files[0]
			const formData = new FormData()
			formData.append('file', file)
			this.$http.userApi
				.userListImport(formData)
				.then((res) => {
					const { code, data } = res
					if (code == 200) {
						this.importUserListName = file.name
						this.userLogList = data
						this.clearSearch()
					}
				})
				.catch((err) => {
					console.log(err)
				})
		},
		exportExcel() {
			const wb = XLSX.utils.book_new() // 创建一个新的工作簿
			// 创建一个只包含中文标题的数组的数组
			const headers = [['账号', '名称', '警号']]
			// 将标题转换为工作表
			const ws_headers = XLSX.utils.aoa_to_sheet(headers)
			// 将工作表添加到工作簿中
			XLSX.utils.book_append_sheet(wb, ws_headers, 'TemplateSheet')
			// 导出Excel文件
			XLSX.writeFile(wb, '用户模板.xlsx')
		},
		searchList() {
			this.requestParams.page = 1
			this.getTableData()
		},
		clearSearch() {
			const options = this.$options.data()
			Object.assign(this.requestParams, options.requestParams)
			this.getTableData()
		},
		getTableData() {
			this.$http.userApi.pageList(this.requestParams).then((response) => {
				const { data, code } = response
				if (code === 200) {
					const tableData = data?.data || []
					this.tableData = tableData.map((item) => {
						return item
					})
					this.total = data.total || 0
				}
			})
		},
		changeSize(pageSize) {
			this.requestParams.size = pageSize
			this.getTableData()
		},
		resetPassword(id) {
			this.$GxxPrompt.info({
				type: 'warning',
				message: '确定要重置密码？',
				callback: (res) => {
					if (res.type !== 'rightBtn') {
						return
					}
					this.resetPasswordApi(id)
				}
			})
		},
		resetPasswordApi(id) {
			this.$http.userApi.resetPassword(id).then((res) => {
				const { code, data } = res
				if (code !== 200) {
					return
				}
				this.$GxxPrompt.info({
					type: 'success',
					message: `重置成功<br />新密码：${data}`
				})
			})
		},
		updatePassword(id) {
			if (!id) {
				return
			}
			this.$GxxDialog({
				title: '修改密码',
				showHeader: true,
				showFooter: false,
				component: UpdatePasswordAllDialog,
				componentParams: { id }
			})
		},
		handleUser(row) {
			this.$GxxDialog({
				title: row?.id ? '编辑' : '添加',
				showHeader: true,
				showFooter: false,
				component: UserDialog,
				componentParams: { ...row }
			}).on.then((res) => {
				res.type == 'confirm' && this.getTableData()
			})
		},
		deleteRow(id) {
			this.$GxxPrompt.info({
				type: 'warning',
				message: '确定要删除吗？',
				callback: (res) => {
					res.type === 'rightBtn' && this.deleteUer(id)
				}
			})
		},
		deleteUer(id) {
			this.$http.userApi.delete({ id }).then((res) => {
				const { code } = res
				if (code === 200) {
					this.$baseTip({ message: '删除成功', type: 'success' })
					this.getTableData()
				}
			})
		},
		isShowDeleteBtn(role) {
			if (this.userInfo.role == this.roles.sysAdmin) {
				return true
			}
			return ![this.roles.sysAdmin, this.roles.admin].includes(Number(role))
		}
	}
}
</script>

<style scoped lang="less">
@import url('~@/common/assets/sprites/common.less');
.message-todo-wrap {
	flex: 1;
	display: flex;
	flex-direction: column;
	position: relative;
	.search-container {
		padding: 20px 20px 10px;
		display: flex;
		flex-flow: row wrap;
		justify-content: flex-start;
		align-items: center;
		align-content: center;
		.search-item {
			display: flex;
			align-items: center;
			padding: 0 10px 10px 10px;
			color: #2b3646;
			.search-item-label {
				width: 70px;
				text-align: justify;
				text-align-last: justify;
				font-size: 16px;
				font-family: MicrosoftYaHei;
				text-align: right;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.search-item-colon {
				margin-left: 5px;
				margin-right: 10px;
			}
			& > :nth-last-child(1) {
				flex: 1;
				width: 100% !important;
			}
			.search-button {
				margin: 0 10px;
			}
		}
		.right-corner-btn {
			margin-left: auto;
			.btn-item-wrap {
				cursor: pointer;
				display: flex;
				align-items: center;
				line-height: 18px;
				.btn-icon-add {
					.sprite-common-add;
				}
				.btn-title {
					vertical-align: middle;
					margin-left: 5px;
				}
			}
		}
	}
	.table-container {
		padding: 0 20px 48px;
		height: 100%;
		.gxx-table-container {
			height: 100%;
			/deep/ .gxx-image.avatar {
				width: 30px;
				height: 30px;
				margin: 5px auto;
				display: flex;
				img,
				.gxx-image-error-icon {
					height: inherit;
				}
			}
		}
	}
}
.item-box {
	padding: 10px 0 10px 10px;
	.export-template {
		cursor: pointer;
		&:hover {
			color: #00a9ff;
		}
	}
	.gxx-button + .gxx-button {
		margin-left: 20px;
	}
	// .gxx-button-default {
	// 	color: rgba(0, 137, 255, 1);
	// 	border-radius: 4px;
	// 	border: 1px solid #cfd9f0;
	// 	background: #ffffff;
	// 	&:hover {
	// 		background: #0089ff;
	// 		color: #fff;
	// 	}
	// }
	.item {
		height: 50px;
		display: flex;
		align-items: center;
		.item-label {
			width: 130px;
			text-align-last: justify;
			display: inline-block;
			position: relative;
		}
		.item-colon {
			margin: 0 10px;
		}
		.item-value {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			&.wt-400 {
				width: 400px;
			}
		}
		.item-btn {
			margin-left: 20px;
		}
	}
}
.upload-form {
	display: flex;
	position: relative;
	align-items: center;
	.upload-icon {
		margin-left: 16px;
		color: #13ba5a;
	}
	/deep/ .gxx-input-container {
		width: 400px;
		input {
			border-top-right-radius: 0px;
			border-bottom-right-radius: 0px;
		}
	}

	.btn {
		border-top-left-radius: 0px;
		border-bottom-left-radius: 0px;
	}
	.gxx-progress {
		margin-left: 10px;
	}
}
</style>
