<template>
	<div class="update-user-dialog">
		<gxx-form :model="form" :label-width="80" :label-position="'justify'" class="form" ref="userForm" :showColon="true" :rules="rules">
			<gxx-form-item label="账号" prop="loginId">
				<gxx-input v-model="form.loginId" :disabled="isEdit"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="密码" v-if="!isEdit" prop="password">
				<gxx-input v-model="form.password" type="password"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="名称" prop="name">
				<gxx-input v-model="form.name"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="身份证号码" prop="certificateNumber">
				<gxx-input v-model="form.certificateNumber"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="角色" v-if="!isEdit" prop="role">
				<gxx-select width="100%" :option="roleOptions" v-model="form.role" :enableEmpty="false" :enableFilter="false"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="年龄" prop="age">
				<gxx-input v-model="form.age"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="性别">
				<gxx-select width="100%" :option="SEX_DIC" v-model="form.sex" :enableEmpty="false" :enableFilter="false"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="头像">
				<div class="avatar-btn">
					<gxx-image :preview-src-list="[form.faceImg]" class="avatar" fit="cover" :src="form.faceImg"></gxx-image>
					<gxx-uploader :autoUpload="false" @change="change" :accept="acceptType" :showFileList="false">
						<gxx-button>{{ form.faceImg ? '重新上传' : '上传图片' }}</gxx-button>
					</gxx-uploader>
				</div>
			</gxx-form-item>
		</gxx-form>
		<div class="common-dialog-footer tools">
			<gxx-button type="info" @click="cancel">取消</gxx-button>
			<gxx-button @click="confirm">确定</gxx-button>
		</div>
	</div>
</template>

<script>
import Crypto from '@/common/libs/crypto'
import store from '@/web/store'
export default {
	name: 'UpdateUser',
	props: ['params'],
	computed: {
		isEdit() {
			return !!this.params.id
		},
		roleOptions() {
			const { roles, userInfo } = store.state.user
			if ([roles.sysAdmin].includes(Number(userInfo.role))) {
				return this.ROLE_DIC
			} else {
				return this.ROLE_DIC.filter((item) => ![roles.sysAdmin, roles.admin].includes(Number(item.value)))
			}
		}
	},
	data() {
		return {
			acceptType: '.jpg,.jpeg,.png',
			rules: {
				loginId: [{ required: true, message: '账号不能为空', trigger: 'blur' }],
				password: [
					{ required: true, message: '密码不能为空', trigger: 'blur' },
					{ required: true, message: '规则: 8-16位，数字、字母、特殊符号至少包含三种', trigger: 'blur', pattern: /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[\W_]+).{8,16}$/ }
				],
				name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
				role: [{ required: true, message: '角色不能为空', trigger: 'blur' }],
				age: [{ message: '年龄只能包含数字，且0~129', trigger: 'blur', pattern: /^(?:1[0-2][0-9]|0?[0-9]{1,2})$/ }]
			},
			ROLE_DIC: [],
			SEX_DIC: [],
			form: {
				loginId: '',
				password: '',
				name: '',
				role: store.state.user.roles.user,
				age: '',
				sex: '',
				faceImg: ''
			}
		}
	},
	created() {
		this.getDicInfo()
		Object.assign(this.form, this.params)
	},
	methods: {
		getDicInfo() {
			const params = { type: 'SEX_DIC,ROLE_DIC' }
			this.$http.dicApi.getDicInfo(params).then((res) => {
				const { data, code } = res
				if (code != 200) {
					return
				}
				params.type.split(',').forEach((type) => {
					this[type] = data[type]
					if (!this.isEdit) {
						this.form.sex = this.SEX_DIC[0].value
					}
				})
			})
		},
		change(file, fileList) {
			// reader读取文件
			const fileReader = new FileReader()
			fileReader.onload = (e) => {
				this.form.faceImg = e.target.result
			}
			fileReader.onerror = () => {
				this.form.faceImg = ''
			}
			fileReader.readAsDataURL(file.raw)
		},
		cancel() {
			this.$parent.close('cancel')
		},
		confirm() {
			this.$refs.userForm.validate((bool) => {
				if (!bool) {
					return
				}
				const password = this.form.password
				if (!this.isEdit) {
					this.form.password = Crypto.aesEncrypt(password)
				}
				this.$http.userApi[this.isEdit ? 'update' : 'register'](this.form).then((res) => {
					const { code } = res
					if (code == 200) {
						this.$baseTip({ message: '保存成功', type: 'success' })
						this.$emit('getReturn', this.form)
						this.$parent.close('confirm')
					} else {
						this.form.password = password
					}
				})
			})
		}
	}
}
</script>

<style lang="less" scoped>
.update-user-dialog {
	width: 450px;
	padding-bottom: 20px;
	.form {
		display: flex;
		flex-direction: column;
		padding: 10px 20px 0;
		/deep/.form-item-content {
			flex: 1;
			.gxx-input-container {
				width: 100%;
			}
			.avatar-btn {
				display: flex;
				align-items: center;
				.avatar {
					flex: 1;
					margin-right: 20px;
				}
			}
		}
	}
	.tools {
		text-align: center;
		.gxx-button + .gxx-button {
			margin-left: 20px;
		}
	}
}
</style>
