<template>
	<div class="add-dialog">
		<gxx-form :model="form" :label-width="80" :label-position="'justify'" class="add-dialog-form" ref="userForm" :showColon="true" :rules="rules">
			<gxx-form-item label="账号" prop="loginId">
				<gxx-input v-model="form.loginId" :disabled="isEdit"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="名称" prop="name">
				<gxx-input v-model="form.name"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="年龄">
				<gxx-input v-model="form.age"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="性别">
				<gxx-select width="100%" :option="sexOptions" v-model="form.sex" :enableEmpty="false" :enableFilter="false"></gxx-select>
			</gxx-form-item>
			<gxx-form-item label="头像">
				<div class="avatar-btn">
					<gxx-image :preview-src-list="[form.faceImg]" class="avatar" fit="cover" :src="form.faceImg"></gxx-image>
					<gxx-uploader :autoUpload="false" @change="change" :accept="acceptType" :showFileList="false">
						<gxx-button>{{ form.faceImg ? '重新上传' : '上传图片' }}</gxx-button>
					</gxx-uploader>
				</div>
			</gxx-form-item>
		</gxx-form>
		<div class="common-dialog-footer tools">
			<gxx-button type="info" @click="cancle">取消</gxx-button>
			<gxx-button @click="confirm">确定</gxx-button>
		</div>
	</div>
</template>

<script>
export default {
	name: 'handleUsers',
	props: ['params'],
	computed: {
		isEdit() {
			return !!this.params.id
		}
	},
	data() {
		return {
			acceptType: '.jpg,.jpeg,.png',
			rules: {
				loginId: [{ required: true, message: '账号不能为空', trigger: 'blur' }],
				name: [{ required: true, message: '名称不能为空', trigger: 'blur' }]
			},
			sexOptions: [],
			form: {
				loginId: '',
				name: '',
				age: '',
				sex: '1',
				faceImg: '',
				certificateNumber: ''
			}
		}
	},
	created() {
		this.getDicInfo()
		Object.assign(this.form, this.params)
	},
	methods: {
		getDicInfo() {
			const params = { type: 'SEX_DIC' }
			this.$http.dicApi.getDicInfo(params).then((res) => {
				const { data, code } = res
				if (code === 200) {
					this.sexOptions = data[params.type]
				}
			})
		},
		change(file) {
			// reader读取文件
			const fileReader = new FileReader()
			fileReader.onload = (e) => {
				this.form.faceImg = e.target.result
			}
			fileReader.onerror = () => {
				this.form.faceImg = ''
			}
			fileReader.readAsDataURL(file.raw)
		},
		cancle() {
			this.$parent.close('cancel')
		},
		confirm() {
			this.$refs.userForm.validate((bool) => {
				if (!bool) {
					return
				}
				this.$http.userApi[this.isEdit ? 'update' : 'register'](this.form).then((res) => {
					const { code } = res
					if (code == 200) {
						this.$baseTip({ message: '保存成功', type: 'success' })
						this.$emit('getReturn', this.form)
						this.$parent.close('confirm')
					}
				})
			})
		}
	}
}
</script>

<style lang="less" scoped>
.add-dialog {
	width: 450px;
	padding-bottom: 20px;
	.add-dialog-form {
		display: flex;
		flex-direction: column;
		padding: 10px 20px 0;
		/deep/.form-item-content {
			flex: 1;
			.gxx-input-container {
				width: 100%;
			}
			.avatar-btn {
				display: flex;
				align-items: center;
				.avatar {
					flex: 1;
					margin-right: 20px;
				}
			}
		}
	}
	.tools {
		text-align: center;
		.gxx-button + .gxx-button {
			margin-left: 20px;
		}
	}
}
</style>
