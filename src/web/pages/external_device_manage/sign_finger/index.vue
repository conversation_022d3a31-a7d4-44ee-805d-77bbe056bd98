<template>
	<div class="card sign-finger">
		<div class="top">
			<div class="btns">
				<template v-if="linkStatus == 1">
					<gxx-button plain type="default" @click="handle('onlySign')">仅签名</gxx-button>
					<gxx-button plain type="default" @click="handle('onlyFinger')">仅捺印</gxx-button>
					<gxx-button plain type="default" @click="handle('signFinger')">签名捺印</gxx-button>
					<gxx-button type="default" @click="destroySignFinger">销毁</gxx-button>
				</template>
				<template v-else>
					<div class="item">
						<p class="label">类型</p>
						<gxx-select v-model="signFingerObj.boardType" :enableEmpty="false" :option="signBoardTypeOptions" @change="changeBoardType"></gxx-select>
					</div>
					<template v-if="isShowIpAndPort">
						<div class="item">
							<p class="label wt-60">ip</p>
							<gxx-input v-model="signFingerObj.ip" placeholder="127.0.0.1"></gxx-input>
						</div>
						<div class="item">
							<p class="label wt-60">port</p>
							<gxx-input class="wt-100" v-model="signFingerObj.port" placeholder="45008"></gxx-input>
						</div>
					</template>
					<div class="item">
						<p class="label">连接地址</p>
						<gxx-input class="wt-300" v-model="signFingerObj.path" placeholder="ws://localhost:8899/ESS"></gxx-input>
					</div>
					<gxx-button type="default" @click="initSignFinger" :loading="linkStatus == 0">初始化</gxx-button>
				</template>
			</div>
		</div>
		<div class="bottom">
			<div class="item">
				<div class="gxx-subtitle">仅签名：</div>
				<gxx-image class="image" :src="signImage" fit="contain" :preview-src-list="[signImage]"></gxx-image>
			</div>
			<div class="item">
				<div class="gxx-subtitle">仅捺印：</div>
				<gxx-image class="image" :src="fingerImage" fit="contain" :preview-src-list="[fingerImage]"></gxx-image>
			</div>
			<div class="item">
				<div class="gxx-subtitle">签名捺印：</div>
				<gxx-image class="image" :src="signFingerImage" fit="contain" :preview-src-list="[signFingerImage]"></gxx-image>
			</div>
		</div>
		<!-- <div class="save-btn" @click="saveConfig">保存参数</div> -->
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import { signBoardTypeOptions, signBoardManufacturerDic, signBoardTypeDic, signBoardPath } from '@/common/libs/options'
const BASE64_PREFIX = 'data:image/*;base64,'
export default {
	name: 'SignFinger',
	mixins: [WebsocketMixin],
	computed: {
		isShowIpAndPort() {
			return [signBoardTypeDic.TYPE_1, signBoardTypeDic.TYPE_4].includes(String(this.signFingerObj.boardType))
		}
	},
	data() {
		return {
			linkStatus: -1, // -1: 未连接 0: loading 连接中 1: 连接正常
			signFingerObj: {
				manufacturer: signBoardManufacturerDic.SELF_DEVELOP,
				path: signBoardPath, // 连接智能助手地址
				boardType: signBoardTypeDic.TYPE_2,
				ip: '',
				port: 0
			},
			signImage: '',
			fingerImage: '',
			signFingerImage: '',
			signBoardTypeOptions
		}
	},
	created() {
		this.initData()
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'error', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('signFinger')
		},
		// 获取配置信息
		initData() {
			const { signFingerInfo } = this.data
			if (signFingerInfo) {
				Object.assign(this.signFingerObj, signFingerInfo)
			}
		},
		// 返回数据处理
		handleMessage(message) {
			const { action, code, msg, data } = message
			if (code != 0) {
				if (['init', 'close', 'error'].includes(action)) {
					this.linkStatus = -1
				}
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			this.$baseTip({ type: data.flag === 0 ? 'success' : 'error', message: data?.message || msg })
			const { signFingerData } = data
			switch (action) {
				case 'init':
					if (data.flag === 0) {
						this.linkStatus = 1
						// this.setTerminalConfigInfoApi()
					} else {
						this.linkStatus = -1
					}
					break
				case 'close':
					data.flag === 0 && (this.linkStatus = -1)
					break
				case 'signOK':
					this.handleSignImage(signFingerData)
					break
				case 'fingerOK':
					this.handleFingerImage(signFingerData)
					break
				case 'signFingerOK':
					this.handleSignFingerImage(signFingerData)
					break
				default:
					break
			}
		},
		handleSignImage(signFingerData) {
			const signInfo = signFingerData[0]?.signInfo || {}
			this.signImage = signInfo.signPngData ? BASE64_PREFIX + signInfo.signPngData : ''
		},
		handleFingerImage(signFingerData) {
			const fingerInfo = signFingerData[0]?.fingerInfo || {}
			this.fingerImage = fingerInfo.fingerData ? BASE64_PREFIX + fingerInfo.fingerData : ''
		},
		handleSignFingerImage(signFingerData) {
			this.handleSignImage(signFingerData)
			this.handleFingerImage(signFingerData)
			if (!this.signImage || !this.fingerImage) {
				return
			}
			const canvas = document.createElement('canvas')
			const ctx = canvas.getContext('2d')
			const loadImage = (base64, callback) => {
				const image = new Image()
				image.src = base64
				image.onload = () => {
					callback && callback(image)
				}
			}
			loadImage(this.signImage, (image) => {
				canvas.width = image.width
				canvas.height = image.height
				ctx.drawImage(image, 0, 0)
				loadImage(this.fingerImage, (image) => {
					ctx.drawImage(image, 0, 0)
					this.signFingerImage = canvas.toDataURL('image/png')
				})
			})
		},
		changeBoardType(item) {
			if (item.config) {
				this.signFingerObj.path = item.config.path || this.signFingerObj.path
				if (this.isShowIpAndPort) {
					this.signFingerObj.ip = item.config.ip || this.signFingerObj.ip
					this.signFingerObj.port = item.config.port || this.signFingerObj.port
				}
			}
		},
		validSignBoardType(boardType) {
			return Object.keys(signBoardTypeDic).includes(String(boardType))
		},
		initSignFinger() {
			const boardType = Number(this.signFingerObj.boardType)
			if (!this.validSignBoardType(boardType)) {
				return this.$baseTip({ type: 'warning', message: '请选择请签名板类型' })
			}
			if (this.isShowIpAndPort) {
				if (!this.signFingerObj.ip) {
					return this.$baseTip({ type: 'warning', message: '请输入IP' })
				}
				if (!this.signFingerObj.port) {
					return this.$baseTip({ type: 'warning', message: '请输入端口' })
				}
			}
			const params = {
				manufacturer: Number(this.signFingerObj.manufacturer),
				boardType,
				path: this.signFingerObj.path,
				ip: this.signFingerObj.ip,
				port: Number(this.signFingerObj.port)
			}
			if (!params.path) {
				return this.$baseTip({ type: 'warning', message: '请输入连接地址' })
			}
			this.linkStatus = 0
			this.ws.initSignFinger(params)
		},
		handle(type) {
			if (!this.ws) {
				return this.connect()
			}
			if (!this.validSignBoardType(this.signFingerObj.boardType)) {
				return this.$baseTip({ type: 'warning', message: '请选择请签名板类型' })
			}
			this.ws[type]()
		},
		destroySignFinger() {
			this.ws && this.ws.destroySignFinger()
		}
		// 保存配置信息
		// saveConfig() {
		// 	this.$prompt.info({
		// 		type: 'warning',
		// 		message: '是否要保存配置信息',
		// 		callback: (data) => {
		// 			if (data.type === 'rightBtn') {
		// 				this.setTerminalConfigInfoApi()
		// 			}
		// 		}
		// 	})
		// },
		// setTerminalConfigInfoApi() {
		// 	const terminalConfig = JSON.parse(JSON.stringify(this.data))
		// 	const signFingerInfo = terminalConfig.signFingerInfo
		// 	if (signFingerInfo) {
		// 		Object.assign(signFingerInfo, this.signFingerObj)
		// 	}
		// 	this.$store.dispatch('config/setTerminalConfigInfo', { ...terminalConfig, signFingerInfo })
		// }
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
.sign-finger {
	width: 100%;
	&.card .item .label.wt-60 {
		width: 60px;
	}
	.wt-300 {
		width: 300px;
	}
	.bottom {
		flex-direction: unset;
		align-items: flex-start;
		flex-flow: row wrap;
		.item {
			margin-bottom: 20px;
			margin-right: 20px;
			.gxx-subtitle {
				margin: 10px 0;
			}
			.image {
				width: 500px;
				height: 250px;
				/deep/ .gxx-image__error {
					height: inherit;
				}
			}
		}
	}
}
</style>
