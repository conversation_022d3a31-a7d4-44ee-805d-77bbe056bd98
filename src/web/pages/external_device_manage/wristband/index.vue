<template>
	<div class="card new-reader">
		<div class="top">
			<div class="btns">
				<gxx-button v-if="!linkStatus" type="default" @click="initWristband">初始化</gxx-button>
				<gxx-button v-else type="default" @click="destroyWristband">销毁</gxx-button>
				<gxx-button type="default" @click="getWristband">读取</gxx-button>
			</div>
		</div>
		<div class="bottom">
			<gxx-table :data="msgList" isFixedHead :maxHeight="700">
				<gxx-table-column type="index" label="序号" width="80"></gxx-table-column>
				<gxx-table-column prop="code" label="code"></gxx-table-column>
				<gxx-table-column prop="msg" label="消息"></gxx-table-column>
				<gxx-table-column prop="cardNo" label="卡号"></gxx-table-column>
				<gxx-table-column prop="IcCardNo" label="RFID编号"></gxx-table-column>
			</gxx-table>
		</div>
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
export default {
	name: 'Wristband',
	mixins: [WebsocketMixin],
	data() {
		return {
			linkStatus: false,
			msgList: [] //消息列表
		}
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'error', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('usb')
		},
		// 初始化usb
		initWristband() {
			if (!this.ws) {
				return this.connect()
			}
			this.ws.initUsb()
		},
		// 销毁
		destroyWristband() {
			if (!this.ws) {
				this.linkStatus = false
				return
			}
			this.ws.destroyUsb()
		},
		// 返回数据处理
		handleMessage(message) {
			const { action, code, msg, data } = message
			this.addMsgList({ code, msg, ...data, ...data.result })
			if (code != 0) {
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			this.$baseTip({ type: data.flag === 0 ? 'success' : 'error', message: data?.message || msg })
			switch (action) {
				case 'close':
					this.linkStatus = data.flag !== 0
					break
				default:
					break
			}
		},
		// 获取手环信息
		getWristband() {
			if (!this.ws) {
				return this.connect()
			}
			this.ws.getWristband()
		},
		// 消息添加到列表
		addMsgList(obj) {
			this.msgList.unshift(obj)
		}
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
.card-image {
	width: 100px;
	height: 100px;
}
</style>
