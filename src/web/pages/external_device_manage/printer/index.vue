<template>
	<div class="card printer">
		<div class="top">
			<div class="btns">
				<div class="item">
					<p class="label">打印机</p>
					<gxx-select :enable-empty="false" :enable-filter="false" :option="printerList" v-model="printerObj.name"></gxx-select>
				</div>
				<div class="item">
					<p class="label">类型</p>
					<gxx-select :enable-empty="false" :enable-filter="false" :option="printTypeOptions" v-model="printerObj.type"></gxx-select>
				</div>
				<gxx-button @click="printTest" type="default">打印测试页</gxx-button>
			</div>
		</div>
		<div class="bottom">
			<gxx-table :data="msgList" isFixedHead :maxHeight="700">
				<gxx-table-column type="index" label="序号" width="80"></gxx-table-column>
				<gxx-table-column prop="code" label="code"></gxx-table-column>
				<gxx-table-column prop="msg" label="消息"></gxx-table-column>
			</gxx-table>
		</div>
		<!-- 保存按钮 -->
		<!-- <div class="save-btn" @click="saveConfig">保存参数</div> -->
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import { printTypeOptions, printTypeDic } from '@/common/libs/options'
import { throttle } from '@/common/libs/util'
export default {
	name: 'Printer',
	mixins: [WebsocketMixin],
	data() {
		return {
			msgList: [], //消息列表
			printerList: [], // 打印机列表
			printerObj: {
				name: '', // 打印机名称
				type: printTypeDic.COLOR // 打印类型
			},
			printTypeOptions,
			timer: null // 计时器
		}
	},
	created() {
		this.initData()
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'error', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('usb')
		},
		// 获取配置信息
		initData() {
			const { printerInfo } = this.data
			if (printerInfo) {
				Object.assign(this.printerObj, printerInfo)
			}
		},
		// 初始化usb
		handleOpen() {
			this.ws.initUsb()
		},
		// 返回数据处理
		handleMessage(message) {
			const { code, msg, action, data } = message
			this.addMsgList({ code, msg })
			if (code != 0) {
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			if (data.flag !== 0) {
				return this.$baseTip({ type: 'error', message: data.message || msg })
			}
			switch (action) {
				case 'init':
					this.getPrinterList()
					break
				case 'getPrinterList':
					const list = data.flag === 0 ? data.result : []
					// 获取打印机列表
					this.printerList = list.map((item) => {
						if (item.isDefault) {
							this.printerObj.name = this.printerObj.name || item.name
						}
						return { label: item.name, value: item.name }
					})
					break
				case 'print':
					// this.setTerminalConfigInfoApi()
					this.$baseTip({ type: 'success', message: data.message || msg })
					break
				default:
					break
			}
		},
		// 消息添加到列表
		addMsgList(value) {
			this.msgList.unshift(value)
		},
		// 打印文件
		printTest: throttle(function () {
			if (!this.ws) {
				return this.connect()
			}
			if (!this.printerObj.name) {
				this.$baseTip({ type: 'warning', message: '请先选择打印机' })
				return false
			}
			const params = {
				printName: this.printerObj.name,
				type: 'text',
				buffer: '打印测试！',
				isColorPrint: this.printerObj.type == printTypeDic.COLOR
			}
			this.ws.printFile(params)
		}),
		// 获取打印机列表
		getPrinterList() {
			this.ws && this.ws.getPrinterList()
		}
		// 保存配置
		// saveConfig() {
		// 	this.$prompt.info({
		// 		type: 'warning',
		// 		message: '是否要保存配置信息',
		// 		callback: (data) => {
		// 			if (data.type === 'rightBtn') {
		// 				this.setTerminalConfigInfoApi()
		// 			}
		// 		}
		// 	})
		// },
		// setTerminalConfigInfoApi() {
		// 	let terminalConfig = JSON.parse(JSON.stringify(this.data))
		// 	let info = JSON.parse(JSON.stringify(this.printerObj))
		// 	terminalConfig['printerInfo'] = info
		// 	this.$store.dispatch('config/setTerminalConfigInfo', terminalConfig)
		// }
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
</style>
