<template>
	<div class="high-speed-scanner">
		<div class="action-info">
			<ul class="action-list">
				<li @click="cameraAction(item)" :class="['action-list-item', item.isActive ? 'action-list-item-active' : '']" v-for="item in cameraActionList" :key="item.id">{{ item.name }}</li>
			</ul>
			<ul class="camera-info">
				<li class="camera-info-item" v-for="item in cameraInfoList" :key="item.id">
					<span class="info-label">{{ item.name }}：</span>
					<span class="info-content">{{ highSpeedScannerInfo[item.id] || '--' }}</span>
				</li>
			</ul>
		</div>
		<div class="preview">
			<div class="empty" v-if="!imgUrl">
				<img class="empty-img" :src="cameraStatusInfo[cameraStatus].img" />
				<span class="empty-text">{{ cameraStatusInfo[cameraStatus].text }}</span>
			</div>
			<gxx-image class="image" v-if="imgUrl" :src="imgUrl" :preview-src-list="[imgUrl]"></gxx-image>
		</div>
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import { throttle } from '@/common/libs/util'
export default {
	name: 'HighSpeedScanner',
	mixins: [WebsocketMixin],
	data() {
		return {
			cameraActionList: [
				{
					id: 'controlCamera',
					name: '打开',
					isActive: false // 判断是否打开
				},
				{
					id: 'rotateCamera',
					name: '旋转'
				},
				{
					id: 'takePhoto',
					name: '抓拍'
				}
			],
			cameraInfoList: [
				{
					id: 'name',
					name: '名称'
				}
			],
			highSpeedScannerInfo: {
				title: '',
				name: '',
				deviceId: '',
				rotateX: 0,
				rotateY: 0
			},
			cameraStatus: 'close',
			cameraStatusInfo: {
				// 状态  close未打开  open已打开  error打开失败
				open: {
					text: '已打开高拍仪',
					img: require('@/web/assets/images/camera/camera_open.png')
				},
				close: {
					text: '暂未打开高拍仪',
					img: require('@/web/assets/images/camera/camera_close.png')
				},
				error: {
					text: '高拍仪异常',
					img: require('@/web/assets/images/camera/camera_error.png')
				}
			},
			imgUrl: '' // 图片展示
		}
	},
	created() {
		this.initData()
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'error', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('usb')
		},
		// 获取设备信息
		initData() {
			const { highSpeedScannerInfo } = this.data
			if (highSpeedScannerInfo) {
				Object.assign(this.highSpeedScannerInfo, highSpeedScannerInfo)
			}
		},
		// 摄像头功能操作
		cameraAction: throttle(function (item) {
			if (!this.ws) {
				return this.connect()
			}
			switch (item.id) {
				case 'controlCamera':
					if (!item.isActive) {
						this.openCamera()
					} else {
						this.closeCamera()
					}
					break
				case 'rotateCamera':
					this.isOpenCamera(() => {
						this.rotateCamera()
					})
					break
				case 'takePhoto':
					this.isOpenCamera(() => {
						this.takePhoto()
					})
					break
				default:
					break
			}
		}),
		// 判断是否打开高拍仪
		isOpenCamera(callback) {
			const flag = this.cameraStatus === 'open'
			if (flag) {
				callback && callback()
			} else {
				this.$baseTip({ type: 'warning', message: '请先打开高拍仪' })
			}
		},
		// 初始化usb
		handleOpen() {
			this.ws.initUsb()
		},
		// 处理message数据
		handleMessage(message) {
			const { action, code, msg, data } = message
			if (code != 0) {
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			if (data.flag !== 0) {
				return this.$baseTip({ type: 'error', message: data?.message || msg })
			}
			switch (action) {
				case 'openCamera':
					// 打开
					this.cameraStatus = 'open'
					this.cameraActionList.forEach((item) => {
						if (item.id === 'controlCamera') {
							item.isActive = !item.isActive
							item.name = '关闭'
						}
					})
					this.$baseTip({ message: '打开成功', type: 'success' })
					break
				case 'rotateCamera':
					// 旋转
					this.$baseTip({ message: '旋转成功', type: 'success' })
					break
				case 'captureImage':
					// 抓拍
					this.imgUrl = data.result
					this.$baseTip({ message: '抓拍成功', type: 'success' })
					break
				case 'closeCamera':
					// 关闭
					this.resetCameraInfo()
					this.imgUrl = ''
					this.$baseTip({ message: '关闭成功', type: 'success' })
					break
				default:
					break
			}
		},
		resetCameraInfo() {
			this.cameraStatus = 'close'
			this.cameraActionList.forEach((item) => {
				if (item.id === 'controlCamera') {
					item.isActive = !item.isActive
					item.name = '打开'
				}
			})
		},
		// 打开高拍仪
		openCamera() {
			const deviceId = this.highSpeedScannerInfo.deviceId
			if (!deviceId) {
				return this.$baseTip({ message: '请先配置高拍仪', type: 'error' })
			}
			this.ws.openCamera(deviceId, 1920, 1080)
		},
		// 关闭高拍仪
		closeCamera() {
			if (!this.ws) {
				return this.resetCameraInfo()
			}
			this.ws.closeCamera()
		},
		// 旋转
		rotateCamera() {
			this.highSpeedScannerInfo.rotateX === 2 ? (this.highSpeedScannerInfo.rotateX = 0.5) : (this.highSpeedScannerInfo.rotateX += 0.5)
			this.ws.rotateCamera(this.highSpeedScannerInfo.rotateX, this.highSpeedScannerInfo.rotateY)
		},
		// 抓拍
		takePhoto() {
			this.ws.captureImage()
		}
	},
	beforeDestroy() {
		this.closeCamera()
	}
}
</script>

<style lang="less" scoped>
.high-speed-scanner {
	width: 100%;
	display: flex;
	flex-direction: column;
	padding: 20px;
	.action-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
		.action-list {
			display: flex;
			.action-list-item {
				cursor: pointer;
				min-width: 108px;
				margin-right: 20px;
				line-height: 30px;
				text-align: center;
				font-family: MicrosoftYaHei, MicrosoftYaHei;
				font-weight: normal;
				font-size: 16px;
				color: #0089ff;
				font-style: normal;
				text-transform: none;
				background: #ffffff;
				border-radius: 4px 4px 4px 4px;
				border: 1px solid #cfd9f0;
				&:hover {
					background-color: #0089ff;
					color: #ffffff;
				}
			}
			.action-list-item-active {
				background-color: #0089ff;
				color: #ffffff;
			}
		}
		.camera-info {
			display: flex;
			align-items: center;
			.camera-info-item {
				white-space: nowrap;
				margin-right: 60px;
				.info-label {
					font-family: Source Han Sans SC, Source Han Sans SC;
					font-weight: 400;
					font-size: 16px;
					color: #5f709a;
				}
				.info-content {
					max-width: 200px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					font-family: Source Han Sans SC, Source Han Sans SC;
					font-weight: 400;
					font-size: 16px;
					color: #2b3646;
				}
				&:last-child {
					margin: 0;
				}
			}
		}
	}
	.preview {
		flex: 1;
		display: flex;
		justify-content: center;
		position: relative;
		width: 100%;
		background: #f9fafd;
		border-radius: 0px 0px 0px 0px;
		border: 1px solid #cfd9f0;
		.empty {
			display: flex;
			justify-content: center;
			flex-wrap: wrap;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 100px;
			.empty-img {
				margin: 0 auto;
			}
			.empty-text {
				display: block;
				font-family: Microsoft YaHei, Microsoft YaHei;
				font-weight: 400;
				font-size: 14px;
				color: #a2acc6;
				text-align: center;
				margin-top: 10px;
			}
		}
		.image {
			height: 100%;
			width: 100%;
		}
	}
}
</style>
