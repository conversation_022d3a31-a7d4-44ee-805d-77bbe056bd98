<template>
	<div class="card new-reader">
		<div class="top">
			<div class="btns">
				<gxx-button v-if="!linkStatus" type="default" @click="initIdCardReader">初始化</gxx-button>
				<gxx-button v-else type="default" @click="destroyIdCardReader">销毁</gxx-button>
				<gxx-button type="default" @click="getIdCard">读取</gxx-button>
			</div>
		</div>
		<div class="bottom">
			<gxx-table :data="msgList" isFixedHead :maxHeight="700">
				<gxx-table-column type="index" label="序号" width="80"></gxx-table-column>
				<gxx-table-column prop="code" label="code"></gxx-table-column>
				<gxx-table-column prop="msg" label="message"></gxx-table-column>
				<gxx-table-column prop="cardno" label="身份证号"></gxx-table-column>
				<gxx-table-column prop="born" label="出生日期"></gxx-table-column>
				<gxx-table-column prop="address" label="户籍地址"></gxx-table-column>
				<gxx-table-column prop="name" label="名字"></gxx-table-column>
				<gxx-table-column prop="nation" label="民族"></gxx-table-column>
				<gxx-table-column prop="sex" label="性别"></gxx-table-column>
				<gxx-table-column label="电子照">
					<template v-slot="row">
						<template v-if="row.photobase64">
							<gxx-image class="card-image" fit="cover" :src="getImagePath(row.photobase64)" :preview-src-list="[getImagePath(row.photobase64)]"></gxx-image>
						</template>
						<template v-else>
							<span>无</span>
						</template>
					</template>
				</gxx-table-column>
			</gxx-table>
		</div>
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
export default {
	name: 'IdCard',
	mixins: [WebsocketMixin],
	data() {
		return {
			linkStatus: false,
			msgList: [] // 消息列表
		}
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'error', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('usb')
		},
		getImagePath(base64) {
			const base64Reg = /^data:image\/\w+;base64,/
			if (base64Reg.test(base64)) {
				return base64
			}
			return `data:image/*;base64,${base64}`
		},
		// 初始化usb
		initIdCardReader() {
			if (!this.ws) {
				return this.connect()
			}
			this.ws.initUsb()
		},
		// 销毁
		destroyIdCardReader() {
			if (!this.ws) {
				this.linkStatus = false
				return
			}
			this.ws.destroyUsb()
		},
		// 返回数据处理
		handleMessage(message) {
			const { action, code, msg, data } = message
			this.msgList.unshift({ code, msg, ...data, ...data.result })
			if (code != 0) {
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			this.$baseTip({ type: data.flag === 0 ? 'success' : 'error', message: data?.message || msg })
			switch (action) {
				case 'close':
					this.linkStatus = data.flag !== 0
					break
				default:
					break
			}
		},
		// 获取身份证
		getIdCard() {
			if (!this.ws) {
				return this.connect()
			}
			this.ws.getIdCard()
		}
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
/deep/ .gxx-image.card-image {
	width: 30px;
	height: 30px;
	margin: 5px auto;
	display: flex;
	img,
	.gxx-image-error-icon {
		height: inherit;
	}
}
</style>
