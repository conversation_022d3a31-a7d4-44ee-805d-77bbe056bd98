<template>
	<div class="card lock">
		<div class="top">
			<div class="btns">
				<template v-if="linkStatus == 1">
					<div class="item">
						<p class="label">锁板</p>
						<gxx-select v-model="address" :enableEmpty="false" :option="addressList" @change="changeAddress"></gxx-select>
					</div>
					<div class="item">
						<p class="label">柜号</p>
						<gxx-select v-model="door" :enableEmpty="false" :option="doorOptions"></gxx-select>
					</div>
					<gxx-button plain type="default" @click="openLock">开柜</gxx-button>
					<gxx-button plain type="default" @click="getLockStatus">查询</gxx-button>
					<gxx-button type="default" @click="destroyLock">销毁</gxx-button>
				</template>
				<template v-else>
					<div class="item">
						<p class="label">类型</p>
						<gxx-select v-model="lockObj.manufacturer" :enableEmpty="false" :option="lockManufacturerOptions" @change="changeManufacturer"></gxx-select>
					</div>
					<div class="item">
						<p class="label">连接地址</p>
						<gxx-select v-if="isCom" v-model="lockObj.path" :enableEmpty="false" :option="comOptions"></gxx-select>
						<gxx-input v-else v-model="lockObj.path"></gxx-input>
					</div>
					<div class="item" v-if="isCom">
						<p class="label">波特率</p>
						<gxx-select v-model="lockObj.baudRate" :enableEmpty="false" :option="baudRateOptions"></gxx-select>
					</div>
					<gxx-button type="default" @click="initLock" :loading="linkStatus == 0">初始化</gxx-button>
				</template>
			</div>
		</div>
		<div class="bottom">
			<gxx-table :data="msgList" isFixedHead :maxHeight="700">
				<gxx-table-column type="index" label="序号" width="80"></gxx-table-column>
				<gxx-table-column prop="code" label="code"></gxx-table-column>
				<gxx-table-column prop="msg" label="消息"></gxx-table-column>
				<gxx-table-column prop="address" label="锁板号"></gxx-table-column>
				<gxx-table-column prop="door" label="柜号"></gxx-table-column>
				<gxx-table-column prop="resultMsg" label="结果"></gxx-table-column>
			</gxx-table>
		</div>
		<!-- 保存按钮 -->
		<!-- <div class="save-btn" @click="saveConfig">保存参数</div> -->
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import { lockManufacturerOptions, baudRateOptions, lockManufacturerDic, createNumberList } from '@/common/libs/options'
export default {
	name: 'Lock',
	mixins: [WebsocketMixin],
	computed: {
		isCom() {
			return [lockManufacturerDic.K32_S, lockManufacturerDic.J88_S, lockManufacturerDic.D99_S].includes(Number(this.lockObj.manufacturer))
		},
		addressList() {
			return this.addressOptions.filter((item) => item.manufacturer == this.lockObj.manufacturer)
		}
	},
	data() {
		return {
			msgList: [], // 消息列表
			linkStatus: -1, // -1: 未连接 0: loading 连接中 1: 连接正常
			lockObj: {
				manufacturer: lockManufacturerDic.K32_W, // 锁控厂商
				path: '', // 锁控串口 || 锁控websocket连接地址
				baudRate: 0 // 锁控波特率
			},
			lockManufacturerOptions, // 锁控厂商类型列表
			baudRateOptions, // 波特率列表
			comOptions: [], // 串口列表
			address: 1, // 锁板号
			door: 1, // 柜号
			addressOptions: [], // 所有锁板号列表
			doorOptions: [] // 柜号列表
		}
	},
	created() {
		this.initData()
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'warning', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('lock')
		},
		// 获取配置信息
		initData() {
			const { lockInfo } = this.data
			if (!lockInfo) {
				this.getSerialPortList()
				return
			}
			const manufacturerSet = new Set()
			const pathList = []
			const baudRateList = []
			let addressOptions = []
			for (const key in lockInfo) {
				const item = lockInfo[key]
				const maxNumList = item.maxNum.split(',')
				addressOptions = addressOptions.concat(
					item.lockPlate.split(',').map((lockPlate, i) => {
						return { value: lockPlate, label: lockPlate, maxNum: Number(maxNumList[i]), manufacturer: item.manufacturer }
					})
				)
				if (item.isMain) {
					Object.assign(this.lockObj, { ...item })
				}
				if (!manufacturerSet.has(item.manufacturer)) {
					pathList.push({ value: item.path, label: item.path })
					item.baudRate && baudRateList.push({ value: item.baudRate, label: item.baudRate })
				}
				manufacturerSet.add(item.manufacturer)
			}
			this.lockManufacturerOptions = lockManufacturerOptions.filter((item) => manufacturerSet.has(item.value))
			this.comOptions = pathList
			this.baudRateOptions = baudRateList
			this.addressOptions = addressOptions
			this.createDoorList(addressOptions[0]?.maxNum)
		},
		// 创建柜号列表
		createDoorList(maxNum) {
			this.doorOptions = createNumberList(maxNum)
		},
		// 切换锁板号
		changeAddress(item) {
			this.createDoorList(item.maxNum)
		},
		// 获取串口列表
		getSerialPortList() {
			this.$http.hardwareApi.getSerialPortList().then((res) => {
				if (res.code == 200) {
					this.comOptions = res.data ? res.data : []
				}
			})
		},
		// 返回数据处理
		handleMessage(message) {
			const { action, code, msg, data } = message
			this.addMsgList({ code, msg, ...data })
			if (code != 0) {
				if (['init', 'close', 'error'].includes(action)) {
					this.linkStatus = -1
				}
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			this.$baseTip({ type: data.flag === 0 ? 'success' : 'error', message: data?.message || msg })
			switch (action) {
				case 'init':
					if (data.flag === 0) {
						this.linkStatus = 1
						// this.setTerminalConfigInfoApi()
					} else {
						this.linkStatus = -1
					}
					break
				case 'close':
					data.flag === 0 && (this.linkStatus = -1)
					break
				default:
					break
			}
		},
		// 切换厂商
		changeManufacturer(item) {
			const { lockInfo } = this.data
			if (lockInfo) {
				for (const key in lockInfo) {
					const lockItem = lockInfo[key]
					if (lockItem.manufacturer == item.value) {
						Object.assign(item.config, { ...lockItem })
						break
					}
				}
			}
			if (item.config) {
				this.lockObj.path = item.config.path || this.lockObj.path
			}
			if (this.isCom) {
				this.lockObj.baudRate = item.config?.baudRate || this.lockObj.baudRate
				if (!this.lockObj.path) {
					this.lockObj.path = this.comOptions.length ? this.comOptions[0].value : ''
				}
			}
		},
		// 初始化锁控
		initLock() {
			const params = {
				manufacturer: Number(this.lockObj.manufacturer),
				path: this.lockObj.path,
				baudRate: Number(this.lockObj.baudRate)
			}
			if (!params.path) {
				return this.$baseTip({ type: 'warning', message: `请${this.isCom ? '选择' : '输入'}连接地址` })
			}
			if (this.isCom && !params.baudRate) {
				return this.$baseTip({ type: 'warning', message: '请选择波特率' })
			}
			this.linkStatus = 0
			this.ws.initLock(params)
		},
		// 打开柜门
		openLock() {
			if (!this.ws) {
				return this.connect()
			}
			if (!this.address || !this.door) {
				return this.$baseTip({ type: 'error', message: '请检查锁板和柜号信息' })
			}
			// 校验数据
			const addressList = String(this.address).split(';')
			if (addressList.length == 0) {
				return this.$baseTip({ type: 'error', message: '请检查锁板信息' })
			}
			const doorList = String(this.door).split(';')
			if (doorList.length == 0) {
				return this.$baseTip({ type: 'error', message: '请检查柜号信息' })
			}
			if (addressList.length != doorList.length) {
				return this.$baseTip({ type: 'error', message: '请检查锁板和柜号信息是否一致' })
			}
			const openLockList = []
			// 组装数据
			addressList.forEach((item, index) => {
				openLockList.unshift({ address: Number(item), door: Number(doorList[index]) })
			})
			const params = {
				openLockList
			}
			this.ws.openLock(params)
		},
		// 获取柜门状态
		getLockStatus() {
			if (!this.ws) {
				return this.connect()
			}
			if (isNaN(Number(this.address)) || isNaN(Number(this.door))) {
				return this.$baseTip({ type: 'error', message: '请输入正确的锁号和柜门号，仅支持单个查询' })
			}
			const params = {
				address: this.address,
				door: this.door
			}
			this.ws.getLockStatus(params)
		},
		destroyLock() {
			this.ws && this.ws.destroyLock()
		},
		// 消息添加到列表
		addMsgList(obj) {
			this.msgList.unshift(obj)
		}
		// 保存配置信息
		// saveConfig() {
		// 	this.$prompt.info({
		// 		type: 'warning',
		// 		message: '是否要保存配置信息',
		// 		callback: (data) => {
		// 			if (data.type === 'rightBtn') {
		// 				this.setTerminalConfigInfoApi()
		// 			}
		// 		}
		// 	})
		// },
		// setTerminalConfigInfoApi() {
		// 	const terminalConfig = JSON.parse(JSON.stringify(this.data))
		// 	const lockInfo = terminalConfig.lockInfo
		// 	if (lockInfo) {
		// 		for (const key in lockInfo) {
		// 			const item = lockInfo[key]
		// 			if (item.isMain) {
		// 				Object.assign({ ...item }, { ...this.lockObj, baudRate: Number(this.lockObj.baudRate) })
		// 			}
		// 		}
		// 	}
		// 	this.$store.dispatch('config/setTerminalConfigInfo', { ...terminalConfig, lockInfo })
		// }
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
.card .top .btns .item .label {
	width: 130px;
}
</style>
