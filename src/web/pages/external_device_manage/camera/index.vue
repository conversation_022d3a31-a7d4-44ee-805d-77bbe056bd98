<template>
	<div class="camera">
		<ul class="camera-tab">
			<li :class="['camera-tab-item', item.title === cameraInfo.title ? 'camera-tab-item-active' : '']" v-for="item in cameraList" :key="item.id" @click="selectCamera(item)">{{ item.title }}</li>
		</ul>
		<div class="main-content">
			<!-- 拍照预览 -->
			<div class="camera-preview">
				<div class="preview">
					<div class="preview-top">
						<ul class="action-list">
							<li @click="cameraAction(item)" :class="['action-list-item', item.isActive ? 'action-list-item-active' : '']" v-for="item in cameraActionList" :key="item.id">{{ item.name }}</li>
						</ul>
						<ul class="camera-info">
							<li class="camera-info-item" v-for="item in cameraInfoList" :key="item.id">
								<span class="info-label">{{ item.name }}：</span>
								<span class="info-content">{{ getCameraConfigItemInfo(item.id) }}</span>
							</li>
						</ul>
					</div>
					<div class="photo">
						<div class="empty" v-if="!imgUrl">
							<img class="empty-img" :src="cameraStatusInfo[cameraStatus].img" />
							<span class="empty-text">{{ cameraStatusInfo[cameraStatus].text }}</span>
						</div>
						<img v-if="imgUrl" :src="imgUrl" />
					</div>
				</div>
			</div>
			<!-- 保存按钮 -->
			<!-- <div class="save-btn" @click="saveConfig">保存参数</div> -->
		</div>
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import { cameraRotateOptions } from '@/common/libs/options'
import { throttle } from '@/common/libs/util'
export default {
	name: 'Camera',
	mixins: [WebsocketMixin],
	data() {
		return {
			cameraList: [], // 摄像头列表
			cameraActionList: [
				// 摄像头功能列表
				{
					id: 'controlCamera',
					name: '打开摄像头',
					isActive: false // 判断是否打开摄像头
				},
				{
					id: 'rotateCamera',
					name: '旋转摄像头'
				},
				{
					id: 'faceCompare',
					name: '人脸识别'
				},
				{
					id: 'takePhoto',
					name: '抓取原图'
				}
			],
			cameraInfoList: [
				// 摄像头信息列表
				{
					id: 'name',
					name: '摄像头名称'
				},
				{
					id: 'rotateX',
					name: '旋转角度'
				}
				// {
				// 	id: 'resolution',
				// 	name: '分辨率'
				// },
				// {
				// 	id: 'fps',
				// 	name: '帧率'
				// }
			],
			cameraInfo: {
				// 摄像头信息
				title: '',
				name: '',
				rotateX: 0,
				rotateY: 0,
				resolution: '',
				fps: ''
			},
			cameraStatus: 'close',
			cameraStatusInfo: {
				// 摄像头状态  close未开启  open已开启  error打开失败
				open: {
					text: '已打开摄像头',
					img: require('@/web/assets/images/camera/camera_open.png')
				},
				close: {
					text: '暂未打开摄像头',
					img: require('@/web/assets/images/camera/camera_close.png')
				},
				error: {
					text: '摄像头异常',
					img: require('@/web/assets/images/camera/camera_error.png')
				}
			},
			imgUrl: '', // 图片展示
			isFace: false // 判断是否进行人脸识别
		}
	},
	created() {
		this.initData()
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'error', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('usb')
		},
		// 获取设备信息
		initData() {
			const handle = (key, title) => {
				const cameraInfo = this.data[key]
				if (cameraInfo && typeof cameraInfo === 'object') {
					cameraInfo.id = key
					cameraInfo.title = title
					this.cameraList.push(cameraInfo)
				}
			}
			handle('colorCameraInfo', '彩色摄像头')
			handle('blackCameraInfo', '黑白摄像头')
			Object.assign(this.cameraInfo, { ...this.cameraList[0] })
		},
		getCameraConfigItemInfo(key) {
			let value = this.cameraInfo[key]
			if (key == 'rotateX') {
				value = cameraRotateOptions.find((item) => item.value == this.cameraInfo[key])?.label
			}
			return value || '--'
		},
		// 选择相机
		selectCamera(value) {
			Object.assign(this.cameraInfo, value)
			if (this.cameraStatus === 'open') {
				this.ws.closeCamera()
			}
			this.imgUrl = ''
		},
		// 摄像头功能操作
		cameraAction: throttle(function (item) {
			if (!this.ws) {
				return this.connect()
			}
			switch (item.id) {
				case 'controlCamera':
					if (!item.isActive) {
						this.openCamera()
					} else {
						this.closeCamera()
					}
					break
				case 'rotateCamera':
					this.isOpenCamera(() => {
						this.rotateCamera()
					})
					break
				case 'faceCompare':
					this.isOpenCamera(() => {
						this.faceCompare()
					})
					break
				case 'takePhoto':
					this.isOpenCamera(() => {
						this.takePhoto()
					})
					break
				default:
					break
			}
		}),
		// 判断是否开启摄像头
		isOpenCamera(callback) {
			const flag = this.cameraStatus === 'open'
			if (flag) {
				callback && callback()
			} else {
				this.$baseTip({ type: 'warning', message: '请先打开摄像头' })
			}
		},
		// 初始化usb
		handleOpen() {
			this.ws.initUsb()
		},
		// 处理message数据
		handleMessage(message) {
			const { action, code, msg, data } = message
			if (code != 0) {
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			if (data.flag !== 0) {
				return this.$baseTip({ type: 'error', message: data?.message || msg })
			}
			switch (action) {
				case 'openCamera':
					// 打开摄像头
					this.cameraStatus = 'open'
					this.cameraActionList.forEach((item) => {
						if (item.id === 'controlCamera') {
							item.isActive = !item.isActive
							item.name = '关闭摄像头'
						}
					})
					// this.setTerminalConfigInfoApi()
					this.$baseTip({ message: '打开摄像头成功', type: 'success' })
					break
				case 'rotateCamera':
					// 旋转摄像头
					// this.setTerminalConfigInfoApi()
					this.$baseTip({ message: '旋转成功', type: 'success' })
					break
				case 'captureImage':
					// 抓拍/人脸识别, 如果是人脸识别，抓拍后还要进行接口请求
					if (this.isFace) {
						this.getFacePoint(data.result)
					} else {
						this.imgUrl = data.result
						this.$baseTip({ message: '摄像头抓拍成功', type: 'success' })
					}
					break
				case 'closeCamera':
					// 关闭摄像头
					this.resetCameraInfo()
					this.isFace = false
					this.imgUrl = ''
					this.$baseTip({ message: '关闭摄像头成功', type: 'success' })
					break
				default:
					break
			}
		},
		resetCameraInfo() {
			this.cameraStatus = 'close'
			this.cameraActionList.forEach((item) => {
				if (item.id === 'controlCamera') {
					item.isActive = !item.isActive
					item.name = '开启摄像头'
				}
			})
		},
		// 打开摄像头
		openCamera() {
			const deviceId = this.cameraInfo.deviceId
			if (!deviceId) {
				return this.$baseTip({ message: '请先配置摄像头', type: 'error' })
			}
			this.ws.openCamera(deviceId, 500, 500)
		},
		// 关闭摄像头
		closeCamera() {
			if (!this.ws) {
				return this.resetCameraInfo()
			}
			this.ws.closeCamera()
		},
		// 抓拍图片
		takePhoto() {
			this.ws.captureImage()
		},
		// 旋转图片
		rotateCamera() {
			this.cameraInfo.rotateX === 1.5 ? (this.cameraInfo.rotateX = 0) : (this.cameraInfo.rotateX += 0.5)
			// 修改后需要保存到数组中
			this.cameraList.forEach((item) => {
				if (item.name === this.cameraInfo.name) {
					Object(item, this.cameraInfo)
				}
			})
			this.ws.rotateCamera(this.cameraInfo.rotateX, this.cameraInfo.rotateY)
		},
		// 人脸比对
		faceCompare() {
			this.isFace = true
			this.takePhoto()
		},
		// 获取人脸识别坐标
		getFacePoint(baseSrc) {
			const params = {
				type: 1, // 默认传
				base64: baseSrc
			}
			this.$http.userApi
				.getFaceDetectInfo(params)
				.then((res) => {
					const { code, data, msg } = res
					if (code === 200 && data) {
						this.cutImageByFace(data, baseSrc)
						this.$baseTip({ type: 'success', message: '人脸识别成功' })
					} else {
						this.$baseTip({ type: 'warning', message: msg })
					}
				})
				.finally(() => {
					this.isFace = false
				})
		},
		// 人脸识别边框绘制
		cutImageByFace(faceResult, baseSrc) {
			const canvas = document.createElement('canvas')
			let imgW,
				imgH = 0
			const img = new Image()
			img.src = baseSrc
			img.onload = () => {
				imgW = img.width
				imgH = img.height
				canvas.width = imgW
				canvas.height = imgH
				const ctx = canvas.getContext('2d')
				ctx.drawImage(img, 0, 0, imgW, imgH)
				ctx.strokeStyle = '#ff0000'
				ctx.lineWidth = 4
				const { x, y, width, height } = faceResult
				ctx.strokeRect(x, y, width, height)
				const url = canvas.toDataURL('image/png')
				this.imgUrl = url
			}
		}
		// 保存配置
		// saveConfig() {
		// 	this.$prompt.info({
		// 		type: 'warning',
		// 		message: '是否要保存配置信息？',
		// 		callback: (res) => {
		// 			if (res.type === 'rightBtn') {
		// 				this.setTerminalConfigInfoApi()
		// 			}
		// 		}
		// 	})
		// },
		// setTerminalConfigInfoApi() {
		// 	let terminalConfig = JSON.parse(JSON.stringify(this.data))
		// 	let info = JSON.parse(JSON.stringify(this.cameraInfo))
		// 	delete info.id
		// 	const key = this.cameraInfo.id
		// 	terminalConfig[key] = info
		// 	this.$store.dispatch('config/setTerminalConfigInfo', terminalConfig)
		// }
	},
	beforeDestroy() {
		this.closeCamera()
	}
}
</script>

<style lang="less" scoped>
.camera {
	width: 100%;
	height: 100%;
	.camera-tab {
		display: flex;
		width: 100%;
		background-color: #fafbff;
		.camera-tab-item {
			cursor: pointer;
			min-width: 140px;
			height: 40px;
			line-height: 40px;
			text-align: center;
			font-family: Microsoft YaHei, Microsoft YaHei;
			font-weight: 400;
			font-size: 18px;
			color: #5f709a;
			font-style: normal;
			text-transform: none;
			border-top: 3px solid rgba(0, 0, 0, 0);
			&:hover {
				background: #ffffff;
			}
		}
		.camera-tab-item-active {
			border-top: 3px solid #0089ff;
			background: #ffffff;
			font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
			font-weight: bold;
			font-size: 18px;
			color: #2b3346;
			font-style: normal;
			text-transform: none;
		}
	}
	.main-content {
		width: 100%;
		padding: 20px;
		box-sizing: border-box;
		.camera-preview {
			display: flex;
			width: 100%;
			margin-bottom: 20px;

			.preview {
				width: 100%;
				.preview-top {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 10px;
					.action-list {
						display: flex;
						.action-list-item {
							cursor: pointer;
							min-width: 108px;
							margin-right: 20px;
							line-height: 30px;
							text-align: center;
							font-family: MicrosoftYaHei, MicrosoftYaHei;
							font-weight: normal;
							font-size: 16px;
							color: #0089ff;
							font-style: normal;
							text-transform: none;
							background: #ffffff;
							border-radius: 4px 4px 4px 4px;
							border: 1px solid #cfd9f0;
							&:hover {
								background-color: #0089ff;
								color: #ffffff;
							}
						}
						.action-list-item-active {
							background-color: #0089ff;
							color: #ffffff;
						}
					}
					.camera-info {
						display: flex;
						align-items: center;
						.camera-info-item {
							white-space: nowrap;
							margin-right: 60px;
							.info-label {
								font-family: Source Han Sans SC, Source Han Sans SC;
								font-weight: 400;
								font-size: 16px;
								color: #5f709a;
							}
							.info-content {
								max-width: 200px;
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
								font-family: Source Han Sans SC, Source Han Sans SC;
								font-weight: 400;
								font-size: 16px;
								color: #2b3646;
							}
							&:last-child {
								margin: 0;
							}
						}
					}
				}
				.photo {
					display: flex;
					justify-content: center;
					position: relative;
					width: 100%;
					height: 650px;
					background: #f9fafd;
					border-radius: 0px 0px 0px 0px;
					border: 1px solid #cfd9f0;
					.empty {
						display: flex;
						justify-content: center;
						flex-wrap: wrap;
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						width: 100px;
						.empty-img {
							margin: 0 auto;
						}
						.empty-text {
							display: block;
							font-family: Microsoft YaHei, Microsoft YaHei;
							font-weight: 400;
							font-size: 14px;
							color: #a2acc6;
							text-align: center;
							margin-top: 10px;
						}
					}
					img {
						height: 100%;
					}
				}
			}
		}
		.save-btn {
			cursor: pointer;
			width: 400px;
			height: 30px;
			line-height: 30px;
			margin: 0 auto;
			font-family: MicrosoftYaHei, MicrosoftYaHei;
			font-weight: normal;
			font-size: 16px;
			color: #ffffff;
			text-align: center;
			background: #13ba5a;
			border-radius: 4px 4px 4px 4px;
		}
	}
}
</style>
