<template>
	<div class="card wristband-cabinet">
		<div class="top">
			<div class="btns">
				<template v-if="linkStatus == 1">
					<gxx-button plain type="default" @click="openLock">开柜</gxx-button>
					<gxx-button type="default" @click="destroyLock">销毁</gxx-button>
				</template>
				<template v-else>
					<div class="item">
						<p class="label">类型</p>
						<gxx-select :enableEmpty="false" :enableFilter="false" v-model="wristbandDrawerInfoObj.manufacturer" :option="wristbandDrawerManufacturerOptions" @change="changeManufacturer"></gxx-select>
					</div>
					<div class="item">
						<p class="label">串口</p>
						<gxx-select v-model="wristbandDrawerInfoObj.path" :enableEmpty="false" :option="comOptions"></gxx-select>
					</div>
					<div class="item">
						<p class="label">波特率</p>
						<gxx-select v-model="wristbandDrawerInfoObj.baudRate" :enableEmpty="false" :option="baudRateOptions"></gxx-select>
					</div>
					<gxx-button type="default" @click="initLock" :loading="linkStatus == 0">初始化</gxx-button>
				</template>
			</div>
		</div>
		<div class="bottom">
			<gxx-table :data="msgList" isFixedHead :maxHeight="700">
				<gxx-table-column type="index" label="序号" width="80"></gxx-table-column>
				<gxx-table-column prop="code" label="code"></gxx-table-column>
				<gxx-table-column prop="msg" label="消息"></gxx-table-column>
				<gxx-table-column prop="result" label="结果"></gxx-table-column>
			</gxx-table>
		</div>
		<!-- 保存按钮 -->
		<!-- <div class="save-btn" @click="saveConfig">保存参数</div> -->
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import { baudRateOptions, wristbandDrawerManufacturerDic, wristbandDrawerManufacturerOptions } from '@/common/libs/options'
export default {
	name: 'WristbandDrawer',
	mixins: [WebsocketMixin],
	computed: {
		isCom() {
			return this.wristbandDrawerInfoObj.manufacturer == wristbandDrawerManufacturerDic.COM
		}
	},
	data() {
		return {
			msgList: [], // 消息列表
			linkStatus: -1, // -1: 未连接 0: loading 连接中 1: 连接正常
			wristbandDrawerManufacturerOptions,
			wristbandDrawerInfoObj: {
				manufacturer: wristbandDrawerManufacturerDic.COM,
				path: '', // 锁控串口 || 锁控
				baudRate: 0 // 锁控波特率
			},
			baudRateOptions, // 波特率列表
			comOptions: [] // 串口列表
		}
	},
	created() {
		this.getSerialPortList()
		this.initData()
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'error', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('controlLock')
		},
		// 获取配置信息
		initData() {
			const { wristbandDrawerInfo } = this.data
			if (wristbandDrawerInfo) {
				Object.assign(this.wristbandDrawerInfoObj, wristbandDrawerInfo)
			}
		},
		// 获取串口列表
		getSerialPortList() {
			this.$http.hardwareApi.getSerialPortList().then((res) => {
				if (res.code == 200) {
					this.comOptions = res.data ? res.data : []
				}
			})
		},
		// 修改扫描器类型
		changeManufacturer(item) {
			if (item.config) {
				this.wristbandDrawerInfoObj.path = item.config.path || this.wristbandDrawerInfoObj.path
				if (this.isCom) {
					this.wristbandDrawerInfoObj.baudRate = item.config.baudRate || this.wristbandDrawerInfoObj.baudRate
				}
			}
		},
		// 返回数据处理
		handleMessage(message) {
			const { code, msg, data, action } = message
			this.addMsgList({ code, msg, ...data })
			if (code != 0) {
				this.linkStatus = -1
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			this.$baseTip({ type: data.flag === 0 ? 'success' : 'error', message: data?.message || msg })
			switch (action) {
				case 'init':
					if (data.flag === 0) {
						// this.setTerminalConfigInfoApi()
						this.linkStatus = 1
					}
					break
				case 'close':
					data.flag === 0 && (this.linkStatus = -1)
					break
				default:
					break
			}
		},
		// 初始化锁控
		initLock() {
			if (!this.ws) {
				return this.connect()
			}
			const params = {
				manufacturer: Number(this.wristbandDrawerInfoObj.manufacturer),
				path: this.wristbandDrawerInfoObj.path,
				baudRate: Number(this.wristbandDrawerInfoObj.baudRate)
			}
			if (this.isCom && !params.baudRate) {
				this.$baseTip({ type: 'warning', message: '请选择波特率' })
				return
			}
			if (!params.path) {
				this.$baseTip({ type: 'warning', message: '请选择串口号' })
				return
			}
			this.linkStatus = 0
			this.ws.initLock(params)
		},
		// 打开柜门
		openLock() {
			if (!this.ws) {
				return this.connect()
			}
			this.ws.openLock()
		},
		destroyLock() {
			if (!this.ws) {
				this.linkStatus = -1
				return
			}
			this.ws.destroyLock()
		},
		// 消息添加到列表
		addMsgList(obj) {
			this.msgList.unshift(obj)
		}
		// 保存配置信息
		// saveConfig() {
		// 	this.$prompt.info({
		// 		type: 'warning',
		// 		message: '是否要保存配置信息',
		// 		callback: (data) => {
		// 			if (data.type === 'rightBtn') {
		// 				this.setTerminalConfigInfoApi()
		// 			}
		// 		}
		// 	})
		// },
		// setTerminalConfigInfoApi() {
		// 	let terminalConfig = JSON.parse(JSON.stringify(this.data))
		// 	let info = JSON.parse(JSON.stringify(this.wristbandDrawerInfoObj))
		// 	info.baudRate = Number(info.baudRate)
		// 	terminalConfig['wristbandDrawerInfo'] = info
		// 	this.$store.dispatch('config/setTerminalConfigInfo', terminalConfig)
		// }
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
.card .top .btns .item .label {
	width: 130px;
}
</style>
