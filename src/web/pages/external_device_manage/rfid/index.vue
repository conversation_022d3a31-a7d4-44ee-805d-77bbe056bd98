<template>
	<div class="card rfid">
		<div class="top">
			<div class="btns">
				<template v-if="linkStatus == 1">
					<div class="item">
						<p class="label">地址</p>
						<gxx-select v-model="address" :enableEmpty="false" :option="addressList" @change="changeAddress"></gxx-select>
					</div>
					<div class="item">
						<p class="label">柜号</p>
						<gxx-select v-model="door" :enableEmpty="false" :option="doorOptions"></gxx-select>
					</div>
					<div class="item" v-if="lightColorOptions.length" key="lightColor">
						<p class="label">灯光</p>
						<gxx-select v-model="lightColor" :enableEmpty="false" :option="lightColorOptions"></gxx-select>
					</div>
					<template v-if="showMoreConfig">
						<div class="item">
							<p class="label">绿灯</p>
							<gxx-select v-model="greenRfidColor" :enableEmpty="false" :option="lightColorNumberOptions"></gxx-select>
						</div>
						<div class="item">
							<p class="label">红灯</p>
							<gxx-select v-model="redRfidColor" :enableEmpty="false" :option="lightColorNumberOptions"></gxx-select>
						</div>
						<div class="item">
							<p class="label">通道数</p>
							<gxx-select v-model="channels" :enableEmpty="false" :option="channelsOptions"></gxx-select>
						</div>
						<gxx-button plain type="default" @click="setOrangeColor">设置黄灯比例</gxx-button>
						<gxx-button plain type="default" @click="setPollingChannels">设置通道</gxx-button>
					</template>
					<gxx-button plain type="default" @click="setLight">设置灯光</gxx-button>
					<gxx-button plain type="default" @click="checkRfid">查询</gxx-button>
					<gxx-button type="default" @click="destroyRfid">销毁</gxx-button>
				</template>
				<template v-else>
					<div class="item">
						<p class="label">类型</p>
						<gxx-select v-model="rfidObj.manufacturer" :enableEmpty="false" :option="rfidManufacturerOptions" @change="changeManufacturer"></gxx-select>
					</div>
					<div class="item">
						<p class="label">连接地址</p>
						<gxx-select v-if="showRfidPath" v-model="rfidObj.path" :enableEmpty="false" :option="comOptions"></gxx-select>
						<gxx-input v-else v-model="rfidObj.path" placeholder="ws://127.0.0.1:21233/rfid"></gxx-input>
					</div>
					<div class="item" v-if="showRfidPath">
						<p class="label">波特率</p>
						<gxx-select v-model="rfidObj.baudRate" :enableEmpty="false" :option="baudRateOptions"></gxx-select>
					</div>
					<gxx-button type="default" @click="initRfid" :loading="linkStatus == 0">初始化</gxx-button>
				</template>
			</div>
		</div>
		<div class="bottom">
			<gxx-table :data="msgList" isFixedHead :maxHeight="700">
				<gxx-table-column type="index" label="序号" width="80"></gxx-table-column>
				<gxx-table-column prop="code" label="code"></gxx-table-column>
				<gxx-table-column prop="msg" label="消息"></gxx-table-column>
				<gxx-table-column prop="address" label="地址"></gxx-table-column>
				<gxx-table-column prop="door" label="柜号"></gxx-table-column>
				<gxx-table-column prop="result" label="结果"></gxx-table-column>
			</gxx-table>
		</div>
		<!-- 保存按钮 -->
		<!-- <div class="save-btn" @click="saveConfig">保存参数</div> -->
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import {
	// eslint-disable
	rfidManufacturerOptions,
	baudRateOptions,
	rfidManufacturerDic,
	rfidManufacturerK32Light,
	rfidManufacturerD99Light,
	createColorList,
	createChannelsList,
	k32LightColorOptions,
	d99LightColorOptions,
	createNumberList
} from '@/common/libs/options'
export default {
	name: 'Rfid',
	mixins: [WebsocketMixin],
	computed: {
		showRfidPath() {
			return this.isShowComConfig(this.rfidObj.manufacturer)
		},
		showMoreConfig() {
			return [rfidManufacturerDic.D99_1].includes(Number(this.rfidObj.manufacturer))
		},
		addressList() {
			return this.addressOptions.filter((item) => item.manufacturer == this.rfidObj.manufacturer)
		}
	},
	data() {
		return {
			msgList: [], // 消息列表
			linkStatus: -1, // -1: 未连接 0: loading 连接中 1: 连接正常
			rfidObj: {
				manufacturer: rfidManufacturerDic.K32_1, // 厂商
				path: '', // 串口 ||  websocket连接地址
				baudRate: 0 // 波特率
			},
			address: 1, // 锁板号(DC使用)
			door: 1, // 柜号(DC使用)
			addressOptions: [], // 所有地址列表
			doorOptions: [], // 柜号列表
			rfidManufacturerOptions, // 厂商类型列表
			baudRateOptions, // 波特率列表
			comOptions: [], // 串口列表
			lightColorOptions: [], // 灯颜色列表
			lightColorNumberOptions: createColorList(),
			channelsOptions: createChannelsList(),
			lightColor: '',
			greenRfidColor: 10,
			redRfidColor: 10,
			channels: 10
		}
	},
	created() {
		this.initData()
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'warning', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('rfid')
		},
		// 获取配置信息
		initData() {
			const { rfidInfo } = this.data
			if (!rfidInfo) {
				this.getSerialPortList()
				return
			}
			console.log(rfidInfo)

			const manufacturerSet = new Set()
			const pathList = []
			const baudRateList = []
			let addressOptions = []
			for (const key in rfidInfo) {
				const item = rfidInfo[key]
				addressOptions = addressOptions.concat(
					item.address.split(';').map((address, i) => {
						return { value: address, label: address, maxNum: Number(item.maxNum), manufacturer: item.manufacturer }
					})
				)
				if (item.isMain) {
					Object.assign(this.rfidObj, { ...item })
				}
				if (!manufacturerSet.has(item.manufacturer)) {
					pathList.push({ value: item.path, label: item.path })
					item.baudRate && baudRateList.push({ value: item.baudRate, label: item.baudRate })
				}
				manufacturerSet.add(item.manufacturer)
			}
			this.rfidManufacturerOptions = rfidManufacturerOptions.filter((item) => manufacturerSet.has(item.value))
			this.comOptions = pathList
			this.baudRateOptions = baudRateList
			this.addressOptions = addressOptions
			this.createDoorList(addressOptions[0]?.maxNum)
		},
		// 创建柜号列表
		createDoorList(maxNum) {
			this.doorOptions = createNumberList(maxNum)
		},
		// 切换地址
		changeAddress(item) {
			this.createDoorList(item.maxNum)
		},
		// 获取串口列表
		getSerialPortList() {
			this.$http.hardwareApi.getSerialPortList().then((res) => {
				if (res.code == 200) {
					this.comOptions = res.data ? res.data : []
				}
			})
		},
		// 返回数据处理
		handleMessage(message) {
			const { code, msg, data, action } = message
			this.addMsgList({
				code,
				msg,
				...data,
				result: data.result ? (typeof data.result !== 'string' ? data.result.join(',') || data.result.toString() : data.result) : ''
			})
			if (code != 0) {
				if (['init', 'close', 'error'].includes(action)) {
					this.linkStatus = -1
				}
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			this.$baseTip({ type: data.flag === 0 ? 'success' : 'error', message: data?.message || msg })
			switch (action) {
				case 'init':
					if (data.flag === 0) {
						this.linkStatus = 1
						// this.setTerminalConfigInfoApi()
					} else {
						this.linkStatus = -1
					}
					break
				case 'close':
					data.flag === 0 && (this.linkStatus = -1)
					break
				default:
					break
			}
		},
		// 切换厂商
		changeManufacturer(item) {
			const { rfidInfo } = this.data
			if (rfidInfo) {
				for (const key in rfidInfo) {
					const rfidItem = rfidInfo[key]
					if (rfidItem.manufacturer == item.value) {
						Object.assign(item.config, { ...rfidItem })
						break
					}
				}
			}
			if (item.config) {
				this.rfidObj.path = item.config.path || this.rfidObj.path
			}
			if (this.isShowComConfig(item.value)) {
				this.rfidObj.baudRate = item.config?.baudRate || this.rfidObj.baudRate
				if (!this.rfidObj.path) {
					this.rfidObj.path = this.comOptions.length ? this.comOptions[0].value : ''
				}
			}
			if (!this.showRfidPath) {
				this.lightColor = rfidManufacturerK32Light.GREEN
				this.lightColorOptions = k32LightColorOptions
			} else if (this.showMoreConfig) {
				this.lightColor = rfidManufacturerD99Light.NONE
				this.lightColorOptions = d99LightColorOptions
			}
		},
		// 是否串口协议
		isShowComConfig(manufacturer) {
			return [rfidManufacturerDic.D99_1, rfidManufacturerDic.D99_2].includes(Number(manufacturer))
		},
		// 初始化rfid
		initRfid() {
			const params = {
				manufacturer: Number(this.rfidObj.manufacturer),
				path: this.rfidObj.path,
				baudRate: this.rfidObj.baudRate
			}
			const isCom = this.isShowComConfig(params.manufacturer)
			if (!params.path) {
				return this.$baseTip({ type: 'warning', message: `请${isCom ? '选择' : '输入'}连接地址` })
			}
			if (isCom && !params.baudRate) {
				return this.$baseTip({ type: 'warning', message: '请选择波特率' })
			}
			this.linkStatus = 0
			this.ws.initRfid(params)
		},
		// 检测rfid
		checkRfid() {
			if (!this.ws) {
				return this.connect()
			}
			const params = {
				door: this.door,
				address: this.address
			}
			this.ws.checkRfid(params)
		},
		// 设置灯光
		setLight() {
			if (!this.ws) {
				return this.connect()
			}
			const params = {
				door: this.door,
				address: this.address,
				lightType: this.lightColor
			}
			this.ws.setLight(params)
		},
		// 设置黄灯
		setOrangeColor() {
			if (!this.ws) {
				return this.connect()
			}
			const params = {
				address: this.address,
				green: this.greenRfidColor,
				red: this.redRfidColor
			}
			this.ws.setOrangeColor(params)
		},
		// 设置轮询通道数
		setPollingChannels() {
			if (!this.ws) {
				return this.connect()
			}
			const params = {
				address: this.address,
				channels: this.channels
			}
			this.ws.setPollingChannels(params)
		},
		// 销毁rfid
		destroyRfid() {
			this.ws && this.ws.destroyRfid()
		},
		// 消息添加到列表
		addMsgList(obj) {
			this.msgList.unshift(obj)
		}
		// 保存配置信息
		// saveConfig() {
		// 	this.$prompt.info({
		// 		type: 'warning',
		// 		message: '是否要保存配置信息',
		// 		callback: (data) => {
		// 			if (data.type === 'rightBtn') {
		// 				this.setTerminalConfigInfoApi()
		// 			}
		// 		}
		// 	})
		// },
		// setTerminalConfigInfoApi() {
		// 	let terminalConfig = JSON.parse(JSON.stringify(this.data))
		// 	const rfidInfo = terminalConfig.rfidInfo
		// 	if (rfidInfo) {
		// 		for (const key in rfidInfo) {
		// 			const item = rfidInfo[key]
		// 			if (item.isMain) {
		// 				Object.assign({ ...item }, { ...this.rfidObj, baudRate: Number(this.rfidObj.baudRate) })
		// 			}
		// 		}
		// 	}
		// 	this.$store.dispatch('config/setTerminalConfigInfo', { ...terminalConfig, rfidInfo })
		// }
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
.card .top .btns .item .label {
	width: 90px;
}
</style>
