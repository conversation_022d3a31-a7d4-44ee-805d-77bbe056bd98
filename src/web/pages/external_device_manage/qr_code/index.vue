<template>
	<div class="card qrcode">
		<div class="top">
			<div class="btns">
				<div class="item">
					<p class="label">类型</p>
					<gxx-select :enable-empty="false" :enable-filter="false" v-model="qrCodeObj.manufacturer" :option="qrCodeManufacturerOptions" @change="changeManufacturer"></gxx-select>
				</div>
				<template v-if="isCom">
					<div class="item">
						<p class="label">连接地址</p>
						<gxx-select :enable-empty="false" v-model="qrCodeObj.path" :option="comOptions"></gxx-select>
					</div>
					<div class="item">
						<p class="label">波特率</p>
						<gxx-select :enable-empty="false" v-model="qrCodeObj.baudRate" :option="baudRateOptions"></gxx-select>
					</div>
				</template>
				<gxx-button v-if="!linkStatus" type="default" @click="initQrCode">初始化</gxx-button>
				<gxx-button v-else type="default" @click="destroyQrCode">销毁</gxx-button>
			</div>
		</div>
		<div class="bottom">
			<gxx-table :data="msgList" isFixedHead :maxHeight="700">
				<gxx-table-column type="index" label="序号" width="80"></gxx-table-column>
				<gxx-table-column prop="code" label="code"></gxx-table-column>
				<gxx-table-column prop="msg" label="消息"></gxx-table-column>
				<gxx-table-column prop="result" label="结果"></gxx-table-column>
			</gxx-table>
		</div>
		<!-- 保存按钮 -->
		<!-- <div class="save-btn" @click="saveConfig">保存参数</div> -->
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import { qrCodeManufacturerOptions, baudRateOptions, qrCodeManufacturerDic } from '@/common/libs/options'
export default {
	name: 'QrCode',
	mixins: [WebsocketMixin],
	computed: {
		isCom() {
			return this.qrCodeObj.manufacturer == qrCodeManufacturerDic.COM
		}
	},
	data() {
		return {
			msgList: [],
			linkStatus: false, // 判断是否初始化
			qrCodeObj: {
				path: '', // 二维码串口
				manufacturer: qrCodeManufacturerDic.USB, // 二维码类型
				baudRate: 0 // 波特率
			},
			comOptions: [], // 串口列表
			qrCodeManufacturerOptions, // 二维码扫描仪类型
			baudRateOptions // 波特率列表
		}
	},
	created() {
		this.getSerialPortList()
		this.initData()
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'error', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('qrcode')
		},
		// 获取串口列表
		getSerialPortList() {
			this.$http.hardwareApi.getSerialPortList().then((res) => {
				if (res.code == 200) {
					this.comOptions = res.data ? res.data : []
				}
			})
		},
		// 获取配置信息
		initData() {
			const qrCodeInfo = this.data
			if (qrCodeInfo) {
				Object.assign(this.qrCodeObj, qrCodeInfo)
			}
		},
		// 初始化连接
		initQrCode() {
			const params = {
				manufacturer: Number(this.qrCodeObj.manufacturer),
				path: this.qrCodeObj.path,
				baudRate: Number(this.qrCodeObj.baudRate)
			}
			if (this.isCom) {
				if (!params.path) {
					return this.$baseTip({ type: 'error', message: '请选择连接地址' })
				}
				if (!params.baudRate) {
					return this.$baseTip({ type: 'error', message: '请选择波特率' })
				}
			}
			this.linkStatus = 0
			this.ws.initQrCode(params)
		},
		// 数据处理
		handleMessage(message) {
			const { code, msg, data, action } = message
			this.addMsgList({ code, msg, ...data })
			if (code != 0) {
				if (['init', 'close', 'error'].includes(action)) {
					this.linkStatus = false
				}
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			this.$openMessagePrompt({ isAutoClose: true, icon: data.flag === 0 ? 'success' : 'error', msg: data?.message || msg })
			switch (action) {
				case 'init':
					if (data.flag != 0) {
						return
					}
					// this.setTerminalConfigInfoApi()
					// USB需要添加监听
					if (this.qrCodeObj.manufacturer == qrCodeManufacturerDic.USB) {
						this.addListener()
					} else {
						this.linkStatus = true
					}
					break
				case 'addKeyDownListener':
					this.linkStatus = true
					break
				case 'removeKeyDownListener':
					this.linkStatus = false
					this.ws.destroyUsb()
					break
				case 'close':
					this.linkStatus = false
					break
				default:
					break
			}
		},
		// 修改扫描器类型
		changeManufacturer(item) {
			if (item.config) {
				this.qrCodeObj.path = item.config.path || this.qrCodeObj.path
				if (this.isCom) {
					this.qrCodeObj.baudRate = item.config.baudRate || this.qrCodeObj.baudRate
				}
			}
		},
		// 消息添加到列表
		addMsgList(value) {
			this.msgList.unshift(value)
		},
		// 添加监听
		addListener() {
			if (this.linkStatus) {
				return this.$baseTip({ type: 'warning', message: '请勿重复初始化' })
			}
			this.ws.addKeyDownListener()
		},
		// 移除监听
		removeListener() {
			this.ws && this.ws.removeKeyDownListener()
		},
		// 销毁qrcode
		destroyQrCode() {
			if (this.qrCodeObj.manufacturer == qrCodeManufacturerDic.USB) {
				this.linkStatus = false
				this.removeListener()
				return
			}
			if (!this.ws) {
				this.linkStatus = false
				return
			}
			this.ws.destroyQrCode()
		}
		// 保存配置信息
		// saveConfig() {
		// 	this.$prompt.info({
		// 		type: 'warning',
		// 		message: '是否要保存配置信息',
		// 		callback: (data) => {
		// 			if (data.type === 'rightBtn') {
		// 				this.setTerminalConfigInfoApi()
		// 			}
		// 		}
		// 	})
		// },
		// setTerminalConfigInfoApi() {
		// 	let terminalConfig = JSON.parse(JSON.stringify(this.data))
		// 	let info = JSON.parse(JSON.stringify(this.qrCodeObj))
		// 	info.baudRate = Number(info.baudRate)
		// 	terminalConfig['qrCodeInfo'] = info
		// 	this.$store.dispatch('config/setTerminalConfigInfo', terminalConfig)
		// }
	},
	beforeDestroy() {
		this.removeListener()
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
.card .top .btns .gxx-select-wrap {
	margin-right: 10px;
}
</style>
