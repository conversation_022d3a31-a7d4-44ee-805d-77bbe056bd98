<template>
	<div class="card light">
		<div class="top">
			<div class="btns">
				<template v-if="linkStatus == 1">
					<template v-if="isShowIpt">
						<div class="item">
							<p class="label">锁板</p>
							<gxx-input class="wt-100" v-model="lightObj.address"></gxx-input>
						</div>
						<div class="item">
							<p class="label">锁号</p>
							<gxx-input class="wt-100" v-model="lightObj.door"></gxx-input>
						</div>
					</template>
					<div class="item">
						<gxx-switch v-model="lightStatus" @change="handleLight" :showSwitchTips="true" activeText="已开启" inactiveText="已关闭"></gxx-switch>
						<gxx-button type="default" @click="destroyLight">销毁</gxx-button>
					</div>
				</template>
				<template v-else>
					<div class="item">
						<p class="label">类型</p>
						<gxx-select v-model="lightObj.manufacturer" :enableEmpty="false" :option="lightManufacturerOptions" @change="changeManufacturer"></gxx-select>
					</div>
					<div class="item">
						<p class="label">连接地址</p>
						<gxx-select v-if="isCom" v-model="lightObj.path" :enableEmpty="false" :option="comOptions"></gxx-select>
						<gxx-input v-else v-model="lightObj.path"></gxx-input>
					</div>
					<div class="item" v-if="isCom">
						<p class="label">波特率</p>
						<gxx-select v-model="lightObj.baudRate" :enableEmpty="false" :option="baudRateOptions"></gxx-select>
					</div>
					<gxx-button type="default" @click="initLight" :loading="linkStatus == 0">初始化</gxx-button>
				</template>
			</div>
		</div>
		<div class="bottom">
			<gxx-table :data="msgList" isFixedHead :maxHeight="700">
				<gxx-table-column type="index" label="序号" width="80"></gxx-table-column>
				<gxx-table-column prop="code" label="code"></gxx-table-column>
				<gxx-table-column prop="msg" label="消息"></gxx-table-column>
				<gxx-table-column prop="result" label="结果"></gxx-table-column>
			</gxx-table>
		</div>
		<!-- 保存按钮 -->
		<!-- <div class="save-btn" @click="saveConfig">保存参数</div> -->
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import { lightManufacturerOptions, baudRateOptions, lightManufacturerDic } from '@/common/libs/options'
export default {
	name: 'Light',
	mixins: [WebsocketMixin],
	computed: {
		isCom() {
			return [lightManufacturerDic.K32_S, lightManufacturerDic.SELF_DEVELOP, lightManufacturerDic.D99_S].includes(Number(this.lightObj.manufacturer))
		},
		isShowIpt() {
			return this.lightObj.manufacturer == lightManufacturerDic.D99_S
		}
	},
	data() {
		return {
			msgList: [], // 消息列表
			linkStatus: -1, // -1: 未连接 0: loading 连接中 1: 连接正常
			lightStatus: false, // 开启状态
			lightObj: {
				manufacturer: lightManufacturerDic.K32_S, // 厂商
				path: '', // 串口 || websocket连接地址
				baudRate: 0, // 波特率
				address: 1, // 锁板号(D99_S 使用)
				door: 1 // 柜号(D99_S 使用)
			},
			lightManufacturerOptions, // 厂商类型列表
			baudRateOptions, // 波特率列表
			comOptions: [] // 串口列表
		}
	},
	created() {
		this.initData()
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'warning', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('light')
		},
		// 获取配置信息
		initData() {
			const { supplementLightInfo } = this.data
			if (!supplementLightInfo) {
				this.getSerialPortList()
				return
			}
			Object.assign(this.lightObj, supplementLightInfo)
			this.lightManufacturerOptions = lightManufacturerOptions.filter((item) => supplementLightInfo.manufacturer == item.value)
			this.baudRateOptions = [{ value: this.lightObj.baudRate, label: this.lightObj.baudRate }]
			this.comOptions = [{ value: this.lightObj.path, label: this.lightObj.path }]
		},
		// 获取串口列表
		getSerialPortList() {
			this.$http.hardwareApi.getSerialPortList().then((res) => {
				if (res.code == 200) {
					this.comOptions = res.data ? res.data : []
				}
			})
		},
		// 返回数据处理
		handleMessage(message) {
			const { code, msg, data, action } = message
			this.addMsgList({ code, msg, ...data })
			if (code != 0) {
				if (['init', 'close', 'error'].includes(action)) {
					this.linkStatus = -1
				} else {
					this.lightStatus = !this.lightStatus
				}
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			this.$baseTip({ type: data.flag === 0 ? 'success' : 'error', message: data?.message || msg })
			switch (action) {
				case 'init':
					if (data.flag === 0) {
						this.linkStatus = 1
						// this.setTerminalConfigInfoApi()
					} else {
						this.linkStatus = -1
					}
					break
				case 'close':
					data.flag === 0 && (this.linkStatus = -1)
					break
				case 'openLight':
				case 'closeLight':
					data.flag === 0 && (this.lightStatus = action === 'openLight')
					break
				default:
					break
			}
		},
		// 切换厂商
		changeManufacturer(item) {
			const { supplementLightInfo } = this.data
			if (supplementLightInfo) {
				Object.assign(item.config, { ...supplementLightInfo })
			}
			if (item.config) {
				this.lightObj.path = item.config.path || this.lightObj.path
			}
			if (this.isCom) {
				this.lightObj.baudRate = item.config?.baudRate || this.lightObj.baudRate
				if (!this.lightObj.path) {
					this.lightObj.path = this.comOptions.length ? this.comOptions[0].value : ''
				}
			}
		},
		// 初始化补光灯
		initLight() {
			const params = {
				manufacturer: Number(this.lightObj.manufacturer),
				path: this.lightObj.path,
				baudRate: Number(this.lightObj.baudRate)
			}
			if (!params.path) {
				return this.$baseTip({ type: 'warning', message: `请${this.isCom ? '选择' : '输入'}连接地址` })
			}
			if (this.isCom && !params.baudRate) {
				return this.$baseTip({ type: 'warning', message: '请选择波特率' })
			}
			this.linkStatus = 0
			this.ws.initLight(params)
		},
		// 控制补光灯
		handleLight(bool) {
			if (!this.ws) {
				return this.connect()
			}
			const handle = (params) => {
				this.ws[bool ? 'openLight' : 'closeLight'](params)
			}
			if (this.isShowIpt) {
				const doorList = this.lightObj.door?.split(',') || []
				doorList.forEach((door) => {
					handle({ address: this.lightObj.address, door })
				})
			} else {
				handle()
			}
		},
		//销毁补光灯
		destroyLight() {
			this.ws && this.ws.destroyLight()
		},
		// 消息添加到列表
		addMsgList(obj) {
			this.msgList.unshift(obj)
		}
		// 保存配置信息
		// saveConfig() {
		// 	this.$prompt.info({
		// 		type: 'warning',
		// 		message: '是否要保存配置信息',
		// 		callback: (data) => {
		// 			if (data.type === 'rightBtn') {
		// 				this.setTerminalConfigInfoApi()
		// 			}
		// 		}
		// 	})
		// },
		// setTerminalConfigInfoApi() {
		// 	let terminalConfig = JSON.parse(JSON.stringify(this.data))
		// 	let info = JSON.parse(JSON.stringify(this.lightObj))
		// 	info.baudRate = Number(info.baudRate)
		// 	terminalConfig['supplementLightInfo'] = info
		// 	this.$store.dispatch('config/setTerminalConfigInfo', terminalConfig)
		// }
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
</style>
