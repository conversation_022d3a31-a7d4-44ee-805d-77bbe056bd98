<template>
	<div class="card frequency-reader">
		<div class="top">
			<div class="btns">
				<gxx-button v-if="!linkStatus" type="default" @click="initFrequencyCardReader">初始化</gxx-button>
				<gxx-button v-else type="default" @click="destroyFrequencyCardReader">销毁</gxx-button>
			</div>
		</div>
		<div class="bottom">
			<gxx-table :data="msgList" isFixedHead :maxHeight="700">
				<gxx-table-column type="index" label="序号" width="80"></gxx-table-column>
				<gxx-table-column prop="code" label="code"></gxx-table-column>
				<gxx-table-column prop="msg" label="消息"></gxx-table-column>
				<gxx-table-column prop="result" label="结果"></gxx-table-column>
			</gxx-table>
		</div>
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
export default {
	name: 'FrequencyCardReader',
	mixins: [WebsocketMixin],
	data() {
		return {
			msgList: [],
			linkStatus: false // 未添加监听
		}
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'error', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('usb')
		},
		// 数据处理
		handleMessage(message) {
			const { action, code, msg, data } = message
			this.addMsgList({ code, msg, ...data })
			if (code != 0) {
				this.linkStatus = false
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			this.$baseTip({ type: data.flag === 0 ? 'success' : 'error', message: data?.message || msg })
			switch (action) {
				case 'init':
					data.flag === 0 && this.ws.addKeyDownListener()
					break
				case 'addKeyDownListener':
					this.linkStatus = data.flag === 0
					break
				case 'removeKeyDownListener':
					if (data.flag === 0) {
						this.linkStatus = false
						this.ws.destroyUsb()
					}
					break
				// case 'keyDownValue':
				// 	break
				case 'close':
					data.flag === 0 && this.closeConnect()
					break
				default:
					break
			}
		},
		// 消息添加到列表
		addMsgList(value) {
			this.msgList.unshift(value)
		},
		// 添加监听
		initFrequencyCardReader() {
			if (!this.ws) {
				return this.connect()
			}
			if (this.linkStatus) {
				return this.$baseTip({ type: 'warning', message: '请勿重复初始化' })
			}
			this.ws.initUsb()
		},
		// 移除监听
		destroyFrequencyCardReader() {
			this.linkStatus = false
			this.ws && this.ws.removeKeyDownListener()
		}
	},
	beforeDestroy() {
		this.destroyFrequencyCardReader()
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
.card .top .btns .gxx-select-wrap {
	margin-right: 10px;
}
</style>
