<template>
	<div class="card lock">
		<div class="top">
			<div class="btns">
				<template v-if="linkStatus != 1">
					<div class="item">
						<p class="label">类型</p>
						<gxx-select v-model="vitalSignsObj.manufacturer" :enableEmpty="false" :option="nonContactVitalSignsManufacturerOptions" @change="changeManufacturer"></gxx-select>
					</div>
					<div class="item">
						<p class="label">连接地址</p>
						<gxx-input v-model="vitalSignsObj.path"></gxx-input>
					</div>
					<gxx-button type="default" @click="initVitalSigns" :loading="linkStatus == 0">初始化</gxx-button>
				</template>
				<gxx-button v-else type="default" @click="destroyVitalSigns">销毁</gxx-button>
			</div>
		</div>
		<div class="bottom">
			<gxx-table :data="msgList" isFixedHead :maxHeight="700">
				<gxx-table-column type="index" label="序号" width="80"></gxx-table-column>
				<gxx-table-column prop="code" label="code"></gxx-table-column>
				<gxx-table-column prop="msg" label="消息"></gxx-table-column>
				<gxx-table-column label="是否检测人员">
					<template v-slot="row">
						{{ row.isCheckPerson ? '是' : '否' }}
					</template>
				</gxx-table-column>
				<gxx-table-column prop="heartRate" label="心率"></gxx-table-column>
				<gxx-table-column prop="spo2" label="血氧"></gxx-table-column>
				<gxx-table-column prop="br" label="呼吸"></gxx-table-column>
			</gxx-table>
		</div>
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import { nonContactVitalSignsManufacturerOptions, nonContactVitalSignsManufacturerDic } from '@/common/libs/options'
export default {
	name: 'NonContactVitalSigns',
	mixins: [WebsocketMixin],
	data() {
		return {
			nonContactVitalSignsManufacturerOptions,
			msgList: [], // 消息列表
			linkStatus: -1, // -1: 未连接 0: loading 连接中 1: 连接正常
			vitalSignsObj: {
				manufacturer: nonContactVitalSignsManufacturerDic.TYPE_1,
				path: ''
			}
		}
	},
	created() {
		this.initData()
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'error', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('nonContactVitalSigns')
		},
		// 获取配置信息
		initData() {
			const { nonContactVitalSignsInfo } = this.data
			if (nonContactVitalSignsInfo) {
				Object.assign(this.vitalSignsObj, nonContactVitalSignsInfo)
			}
		},
		changeManufacturer(item) {
			if (item.config) {
				this.vitalSignsObj.path = item.config.path || this.vitalSignsObj.path
			}
		},
		// 返回数据处理
		handleMessage(message) {
			const { action, code, msg, data } = message
			this.msgList.unshift({ code, msg, ...data })
			if (code != 0) {
				if (['init', 'close', 'error'].includes(action)) {
					this.linkStatus = -1
				}
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			if (data.flag != 0) {
				this.$baseTip({ type: 'error', message: data?.message || msg })
			}
			switch (action) {
				case 'init':
					this.linkStatus = data.flag === 0 ? 1 : -1
					break
				case 'close':
					data.flag === 0 && (this.linkStatus = -1)
					break
				default:
					break
			}
		},
		// 初始化
		initVitalSigns() {
			this.linkStatus = 0
			this.ws.initNonContactVitalSigns(this.vitalSignsObj)
		},
		destroyVitalSigns() {
			if (!this.ws) {
				this.linkStatus = -1
				return
			}
			this.ws.closeNonContactVitalSigns()
		}
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
</style>
