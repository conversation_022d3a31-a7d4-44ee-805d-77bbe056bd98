<template>
	<div class="card lock">
		<div class="top">
			<div class="btns">
				<template v-if="isInitLink">
					<div class="item">
						<p class="label">类型</p>
						<gxx-select v-model="vitalSignsObj.manufacturer" :enableEmpty="false" :option="contactVitalSignsManufacturerOptions" @change="changeManufacturer"></gxx-select>
					</div>
					<div class="item">
						<p class="label">连接地址</p>
						<gxx-select v-model="vitalSignsObj.path" :enableEmpty="false" :option="comOptions"></gxx-select>
					</div>
					<div class="item">
						<p class="label">波特率</p>
						<gxx-select v-model="vitalSignsObj.baudRate" :enableEmpty="false" :option="baudRateOptions"></gxx-select>
					</div>
					<gxx-button type="default" @click="initVitalSigns" :loading="linkStatus == 0">初始化</gxx-button>
				</template>
				<gxx-button v-else type="default" @click="destroyVitalSigns" :loading="linkStatus == 2">销毁</gxx-button>
			</div>
		</div>
		<div class="bottom">
			<gxx-table :data="msgList" isFixedHead :maxHeight="700">
				<gxx-table-column type="index" label="序号" width="80"></gxx-table-column>
				<gxx-table-column prop="code" label="code"></gxx-table-column>
				<gxx-table-column prop="action" label="指令"></gxx-table-column>
				<gxx-table-column prop="msg" label="消息"></gxx-table-column>
				<gxx-table-column label="是否检测人员">
					<template v-slot="row">
						{{ row.isCheckPerson ? '是' : '否' }}
					</template>
				</gxx-table-column>
				<gxx-table-column prop="heartRate" label="心率"></gxx-table-column>
				<gxx-table-column prop="spo2" label="血氧"></gxx-table-column>
				<gxx-table-column prop="bk" label="微循环"></gxx-table-column>
				<gxx-table-column prop="systolicPressure" label="收缩压"></gxx-table-column>
				<gxx-table-column prop="diastolicPressure" label="舒张压"></gxx-table-column>
			</gxx-table>
		</div>
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import { contactVitalSignsManufacturerOptions, baudRateOptions, contactVitalSignsManufacturerDic } from '@/common/libs/options'
export default {
	name: 'ContactVitalSigns',
	mixins: [WebsocketMixin],
	computed: {
		isInitLink() {
			return this.linkStatus == -1 || this.linkStatus == 0
		}
	},
	data() {
		return {
			contactVitalSignsManufacturerOptions,
			baudRateOptions,
			comOptions: [], // 串口列表
			msgList: [], // 消息列表
			linkStatus: -1, // -1: 未连接 0: loading 连接中 1: 连接正常 2: 销毁中
			vitalSignsObj: {
				manufacturer: contactVitalSignsManufacturerDic.TYPE_1,
				path: '',
				baudRate: 0
			}
		}
	},
	created() {
		this.getSerialPortList()
		this.initData()
	},
	mounted() {
		this.connect(true)
	},
	methods: {
		connect(isTip) {
			if (!isTip) {
				this.$baseTip({ type: 'error', message: '正在连接设备，请稍后重新操作' })
			}
			this.initConnect('contactVitalSigns')
		},
		// 获取配置信息
		initData() {
			const { contactVitalSignsInfo } = this.data
			if (contactVitalSignsInfo) {
				Object.assign(this.vitalSignsObj, contactVitalSignsInfo)
			}
		},
		// 获取串口列表
		getSerialPortList() {
			this.$http.hardwareApi.getSerialPortList().then((res) => {
				if (res.code == 200) {
					this.comOptions = res.data ? res.data : []
				}
			})
		},
		changeManufacturer(item) {
			if (item.config) {
				this.vitalSignsObj.path = item.config.path || this.vitalSignsObj.path
				this.vitalSignsObj.baudRate = item.config.baudRate || this.vitalSignsObj.baudRate
			}
		},
		// 返回数据处理
		handleMessage(message) {
			const { action, code, msg, data } = message
			this.msgList.unshift({ action, code, msg, ...data })
			if (code != 0) {
				if (['init', 'close', 'error'].includes(action)) {
					this.linkStatus = -1
				}
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			if (data.flag != 0) {
				this.$baseTip({ type: 'error', message: data?.message || msg })
			}
			switch (action) {
				case 'init':
					this.linkStatus = data.flag === 0 ? 1 : -1
					if (this.linkStatus == 1) {
						this.ws.startCollection()
					}
					break
				case 'close':
					data.flag === 0 && (this.linkStatus = -1)
					break
				case 'endCollection':
					data.flag === 0 && this.ws.closeContactVitalSigns()
					break
				default:
					break
			}
		},
		// 初始化
		initVitalSigns() {
			if (!this.vitalSignsObj.path) {
				return this.$baseTip({ type: 'warning', message: '请选择连接地址' })
			}
			this.linkStatus = 0
			this.ws.initContactVitalSigns({ ...this.vitalSignsObj, manufacturer: Number(this.vitalSignsObj.manufacturer), baudRate: Number(this.vitalSignsObj.baudRate) })
		},
		destroyVitalSigns() {
			if (!this.ws) {
				this.linkStatus = -1
				return
			}
			this.linkStatus = 2
			this.ws.endCollection()
		}
	}
}
</script>

<style lang="less" scoped>
@import '~@/web/assets/style/common_device.less';
</style>
