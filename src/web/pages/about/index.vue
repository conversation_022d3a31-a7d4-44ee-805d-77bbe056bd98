<template>
	<div class="about" v-partial-loading="isLoad">
		<div class="info">
			<div class="card" v-for="(item, index) in infos" :key="index">
				<gxx-iconfont  @click="refresh(index)" class="icon" type="icon-datuyulan-nishizhenxuanzhuan"></gxx-iconfont>
				<div class="label">{{ item.label }}</div>
				<div class="value" :title="propertyData[item.valueKey]">{{ propertyData[item.valueKey] || (isNaN(propertyData[item.valueKey])?'--':0) }}</div>
				<div class="time">更新时间：{{ item.time || '--' }}</div>
			</div>
		</div>
		<div class="btns">
			<gxx-button icon="icon-biaoticaozuoanniu-shuaxin" @click="getProperty">刷新</gxx-button>
			<gxx-button icon="icon-datuyulan-nishizhenxuanzhuan" @click="reboot">重启设备</gxx-button>
			<gxx-button icon="icon-datuyulan-shunshizhenxuanzhuan" @click="rebootApp">重启应用</gxx-button>
			<gxx-button icon="icon-xialakongjian-xiazai" @click="downloadLog">下载日志</gxx-button>
			<gxx-button icon="icon-zuocecaidan-xiazaikeluzhongxin" @click="downUpgrade">下发升级</gxx-button>
			<gxx-button icon="icon-xialakongjian-tuichu" @click="upload">上传升级</gxx-button>
		</div>
	</div>
</template>

<script>
import uploadDialog from './upload/index'
import { reloadApp, getLogs, reboot, getProperty, downUpgrade } from '@/web/api/index'
export default {
	name: 'about',
	data() {
		return {
			infos: [
				{
					label: '系统平台',
					valueKey: 'sysPlatform',
					time: ''
				},
				{
					label: '系统架构',
					valueKey: 'sysArch',
					time: ''
				},
				{
					label: '系统版本',
					valueKey: 'sysVersion',
					time: ''
				},
				{
					label: '总内存',
					valueKey: 'memory',
					time: ''
				},
				{
					label: '使用内存',
					valueKey: 'used',
					time: ''
				},
				{
					label: '空闲内存',
					valueKey: 'free',
					time: ''
				},
				{
					label: 'cpu名称',
					valueKey: 'cpuName',
					time: ''
				},
				{
					label: 'cpu使用率',
					valueKey: 'cpuUsed',
					time: ''
				},
				{
					label: 'CPU核心线程数',
					valueKey: 'threadNumber',
					time: ''
				},
				{
					label: '设备名称',
					valueKey: 'name',
					time: ''
				},
				{
					label: '设备IP',
					valueKey: 'ip',
					time: ''
				},
				{
					label: '设备MAC',
					valueKey: 'mac',
					time: ''
				},
				{
					label: '设备型号',
					valueKey: 'model',
					time: ''
				},
				{
					label: '软件版本',
					valueKey: 'version',
					time: ''
				},
				{
					label: '空闲柜子数量',
					valueKey: 'idleBox',
					time: ''
				},
				{
					label: '柜子总数量',
					valueKey: 'totalBox',
					time: ''
				}
			],
			propertyData: {
				name: '',
				version: '',
				threadNumber: '',
				memory: '',
				free: '',
				used: '',
				cpuUsed: '',
				cpuName: '',
				sysVersion: '',
				sysPlatform: '',
				sysArch: '',
				ip: '',
				mac: '',
				idleBox: '',
				totalBox: '',
				model: ''
			},
			isLoad: false
		}
	},
	created() {
		this.getProperty()
	},
	methods: {
		getProperty(prop = '', callback) {
			this.isLoad = true
			getProperty(prop)
				.then((res) => {
					const { code, data } = res
					if (code != 200) {
						return
					}
					if (callback) {
						callback(data)
					} else {
						this.handleData(data)
					}
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		handleData(data) {
			const memory = this.MbToGb(data.memory)
			const used = this.MbToGb(data.used)
			const free = this.MbToGb(data.free)
			const cpuUsed = this.addPercentage(data.cpuUsed)
			Object.assign(this.propertyData, data, { memory, used, free, cpuUsed })
			const updateTime = this.getDateTime()
			this.infos.forEach((item) => {
				item.time = updateTime
			})
		},
		MbToGb(mb) {
			return parseFloat(mb / 1024).toFixed(2) + 'GB'
		},
		addPercentage(value) {
			return value + '%'
		},
		refresh(index) {
			const item = this.infos[index]
			this.getProperty(item.valueKey, (data) => {
				if (['memory', 'used', 'free'].includes(item.valueKey)) {
					this.propertyData[item.valueKey] = this.MbToGb(data[item.valueKey])
				} else if (item.valueKey == 'cpuUsed') {
					this.propertyData[item.valueKey] = this.addPercentage(data[item.valueKey])
				} else {
					this.propertyData[item.valueKey] = data[item.valueKey]
				}
				const updateTime = this.getDateTime()
				item.time = updateTime
			})
		},
		getDateTime() {
			const date = new Date()
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const hours = String(date.getHours()).padStart(2, '0')
			const minutes = String(date.getMinutes()).padStart(2, '0')
			const seconds = String(date.getSeconds()).padStart(2, '0')
			return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
		},
		reboot() {
			this.isLoad = true
			reboot()
				.then((res) => {
					const { code } = res
					if (code == 200) {
						window.location.reload()
					}
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		rebootApp() {
			this.isLoad = true
			reloadApp()
				.then((res) => {
					const { code } = res
					if (code == 200) {
						window.location.reload()
					}
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		downloadLog() {
			this.isLoad = true
			getLogs()
				.then((res) => {
					this.createDownload(res, 'log.zip')
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		createDownload(res, fileName) {
			const fileReader = new FileReader()
			fileReader.readAsDataURL(res)
			fileReader.onload = (e) => {
				let a = document.createElement('a')
				a.download = fileName
				a.href = e.target.result
				document.body.appendChild(a)
				a.click()
				document.body.removeChild(a)
			}
		},
		downUpgrade() {
			this.isLoad = true
			downUpgrade()
				.then((res) => {
					const { code, msg } = res
					this.$GxxMessage({ message: msg, type: code == 200 ? 'success' : 'error' })
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		upload() {
			this.$GxxDialog({
				title: '上传',
				showHeader: true,
				showFooter: false,
				component: uploadDialog,
				componentParams: { sysPlatform: this.propertyData.sysPlatform }
			})
		}
	}
}
</script>

<style lang="less" scoped>
.about {
	width: 100%;
	display: flex;
	flex-direction: column;
	padding: 10px;
	.info {
		flex: 1;
		overflow-y: auto;
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-content: flex-start;
		.card {
			position: relative;
			min-width: 200px;
			width: calc(25% - 42px);
			height: 120px;
			display: flex;
			align-items: center;
			justify-content: space-around;
			flex-direction: column;
			background-color: #fff;
			border: 1px solid #e9edf5;
			border-radius: 4px;
			-webkit-transition: all 0.5s ease;
			transition: all 0.5s ease;
			margin: 10px;
			padding: 10px;
			.icon {
				width: 15px;
				height: 15px;
				position: absolute;
				top: 10px;
				right: 10px;
				cursor: pointer;
			}
			&:hover {
				box-shadow: 0 2px 10px 0 rgb(7 31 88 / 20%);
			}
			.label,
			.value {
				font-size: 20px;
				color: #2b3346;
				font-weight: 700;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				width: 100%;
			}
			.time {
				width: 100%;
				font-size: 14px;
				color: #2b3346;
			}
		}
	}
	.btns {
		margin-top: 10px;
		display: flex;
		justify-content: center;
		align-items: center;
		.gxx-button + .gxx-button,
		.gxx-upload-body {
			margin-left: 20px;
		}
	}
}
</style>
