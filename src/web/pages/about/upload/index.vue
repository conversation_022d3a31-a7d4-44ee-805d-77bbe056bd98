<template>
	<div class="add-dialog" v-partial-loading="isLoad">
		<gxx-form :model="form" ref="form" :rules="rules" :label-width="100" :label-position="'justify'" class="add-dialog-form" :showColon="true">
			<gxx-form-item label="上传文件" prop="fileName">
				<gxx-input :disabled="true" v-model="form.fileName" :class="form.fileName ? 'pr-92' : 'pr-62'"></gxx-input>
				<gxx-uploader :action="uploadUrl" :accept="accept" :disabled="uploadStatus" :showFileList="false" :uploadError="uploadError" :uploadSuccess="uploadSuccess" :uploadProcess="uploadProcess" :beforeUpload="beforeUpload">
					<gxx-button :disabled="uploadStatus" class="btn">{{ form.fileName ? '重新上传' : '上传' }}</gxx-button>
				</gxx-uploader>
				<gxx-progress v-show="uploadStatus" :percentage="percentage" color="#2B5FDA" :stroke-width="2" :show-text="false"></gxx-progress>
			</gxx-form-item>
		</gxx-form>
		<div class="common-dialog-footer tools">
			<gxx-button type="info" @click="cancel">取消</gxx-button>
			<gxx-button @click="comfrim" :disabled="isDisabled">确定</gxx-button>
		</div>
	</div>
</template>

<script>
import { upgrade, uploadUrl } from '@/web/api/index'
export default {
	name: 'UploadPackage',
	computed: {
		accept() {
			if (this.params.sysPlatform) {
				return this.params.sysPlatform == 'Windows' ? '.exe' : '.deb'
			}
			return '.exe,.deb'
		},
		isDisabled() {
			return !this.form.fileName || this.uploadStatus
		}
	},
	props: {
		params: {
			type: Object
		}
	},
	data() {
		return {
			isLoad: false,
			uploadUrl,
			form: {
				fileName: ''
			},
			rules: {
				fileName: [{ required: true, message: '请上传文件', trigger: 'change' }]
			},
			// 进度条
			percentage: 0,
			// 上传状态
			uploadStatus: false
		}
	},
	methods: {
		/**
		 * 描述：确定上传
		 */
		comfrim() {
			this.$refs.form.validate((valid) => {
				if (!valid) {
					return
				}
				this.isLoad = true
				upgrade(this.form)
					.then((res) => {
						const { code } = res
						code == 200 && window.location.reload()
					})
					.finally(() => {
						this.isLoad = false
					})
			})
		},
		/**
		 * 描述：文件上传之前函数
		 */
		beforeUpload(file) {
			this.uploadStatus = true
			return true
		},
		/**
		 * 描述：文件上传成功返回文件信息
		 */
		uploadSuccess(response, file, fileList) {
			const { code, msg, data } = response
			this.form = data
			if (code == 200) {
				this.$GxxMessage({ message: msg, type: 'success' })
			} else {
				this.$GxxMessage({ message: '文件上传失败，请重试...', type: 'error' })
			}
		},
		/**
		 * 描述：错误回调
		 */
		uploadError(file) {
			this.uploadStatus = false
			this.percentage = 0
			this.$GxxMessage({ message: '文件上传失败，请重试...', type: 'error' })
		},
		/**
		 * 描述：进度条函数
		 */
		uploadProcess(ev) {
			this.percentage = ev.percent
			if (ev.percent === 100) {
				setTimeout(() => {
					this.uploadStatus = false
					this.percentage = 0
				}, 2000)
			}
		},
		/**
		 * 描述：取消
		 */
		cancel() {
			this.$parent.close('cancel')
		}
	}
}
</script>

<style lang="less" scoped>
.add-dialog {
	width: 450px;
	padding-bottom: 20px;
	.add-dialog-form {
		display: flex;
		flex-direction: column;
		padding: 10px 20px 0;
		/deep/.form-item-content {
			flex: 1;
			.gxx-input-container {
				width: 100%;
				&.pr-62 {
					input {
						padding-right: 62px;
					}
				}
				&.pr-92 {
					input {
						padding-right: 92px;
					}
				}
			}
			.gxx-upload-body {
				position: absolute;
				right: 0;
				top: 1px;
				.btn {
					border-top-left-radius: 0px;
					border-bottom-left-radius: 0px;
				}
			}
			.gxx-progress {
				.gxx-line-progress {
					.gxx-progress-bar__outer {
						width: 100% !important;
					}
				}
			}
		}
	}
	.tools {
		text-align: center;
	}
}
</style>
