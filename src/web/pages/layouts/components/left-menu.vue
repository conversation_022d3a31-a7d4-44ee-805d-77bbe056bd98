<template>
	<div class="left-menu" :class="{ wt: isCollapse }">
		<gxx-menu :collapse="isCollapse" unique-opened class="menu-vertical-demo">
			<div class="bottom-bg"></div>
			<template v-for="(route, index) in tabList">
				<gxx-menu-item :key="index" v-if="route.isShow" @click="tabChange(route.key)" :class="[currentRouteKey == route.key ? 'active' : '']">
					<gxx-iconfont :type="route.iconName" class="submenu-icon" :class="{ mr: !isCollapse }"></gxx-iconfont>
					<span>{{ route.title }}</span>
					<gxx-iconfont type="icon-riqixuanzeqi-nian-youjiantou" class="right-icon" v-if="!isCollapse && currentRouteKey == route.key"></gxx-iconfont>
				</gxx-menu-item>
			</template>
		</gxx-menu>
	</div>
</template>

<script>
import { getProperty } from '@/web/api/index'
export default {
	name: 'LeftMenu',
	props: {
		isCollapse: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			currentRouteKey: '/operation/appConfig',
			tabList: [
				{
					key: '/operation/appConfig',
					title: '应用配置',
					iconName: 'icon-zuocecaidan-shishiyunhangzhuangtai',
					isShow: true
				},
				{
					key: '/operation/terminalConfig',
					title: '网络配置',
					iconName: 'icon-anniu-renyuanhuodongguiji',
					isShow: false
				},
				{
					key: '/operation/about',
					title: '关于',
					iconName: 'icon-xialakongjian-xitongbanben',
					isShow: true
				}
			]
		}
	},
	created() {
		this.currentRouteKey = this.$route.path
		this.getProperty()
	},
	methods: {
		getProperty() {
			const loadings = this.$GxxLoading({ txt: '正在加载中，请稍等...', isCloseBtn: true })
			const prop = 'sysArch'
			getProperty(prop)
				.then((res) => {
					const { data } = res
					this.tabList[1].isShow = !['ia32','x64'].includes(data[prop]) //!= 'Windows'
				})
				.catch(() => {
					this.$GxxMessage({ message: '获取系统属性失败', type: 'error' })
					this.tabList[1].isShow = false
				})
				.finally(() => {
					loadings.close()
				})
		},
		tabChange(routeKey) {
			if (this.currentRouteKey == routeKey) {
				return
			}
			this.currentRouteKey = routeKey
			this.$router.push(routeKey)
		}
	}
}
</script>

<style lang="less" scoped>
.left-menu {
	height: 100%;
	width: 200px;
	overflow: hidden;
	&.wt {
		width: 60px;
	}
	.menu-vertical-demo {
		height: 100%;
		background: linear-gradient(0deg, #12254e, #0d3183);
		overflow: hidden auto;
		.gxx-menu-item {
			display: flex;
			align-items: center;
			height: 50px;
		}
		.is-active {
			background-color: transparent;
			color: #8da0ce;
		}
		.active {
			font-weight: 700;
			color: #fff;
			justify-content: flex-start;
			position: relative;
			.right-icon {
				position: absolute;
				right: 10px;
				top: 50%;
				transform: translateY(-50%);
				width: 18px;
				height: 18px;
			}
		}
		.submenu-icon {
			width: 18px;
			height: 18px;
			margin-left: 0;
			&.mr {
				margin-right: 10px;
			}
		}
	}
	.bottom-bg {
		position: absolute;
		bottom: 0;
		left: 0;
		height: 200px;
		width: 200px;
		background-image: url('~@/web/assets/images/menu_bottom.png');
		background-size: 100% 100%;
		-webkit-transition: all 0.3s ease-out;
		transition: all 0.3s ease-out;
	}
}
</style>
