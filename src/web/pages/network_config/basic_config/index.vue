<template>
	<div class="basic-config">
		<gxx-tab class="header-box" v-model="tabValue" :list="tabList"></gxx-tab>
		<div class="content-box">
			<component v-if="componentName" ref="childRef" :terminalConfig="config" :is="componentName" isShowSaveBtn></component>
		</div>
		<div class="footer-box">
			<gxx-button @click="save" v-if="isShowSaveBtn">保存</gxx-button>
		</div>
	</div>
</template>

<script>
import TcpIp from './tcp_ip/index'
import Port from './port/index'
import Iot from './iot/index'
import { mapGetters } from 'vuex'
import { systemArchStr } from '@/config'
const TAB_KEY = {
	TCP_IP: 'TcpIp',
	PORT: 'Port',
	IOT: 'Iot'
}
export default {
	name: 'BasicConfig',
	components: {
		TcpIp,
		Port,
		Iot
	},
	computed: {
		...mapGetters({
			config: 'config/data'
		}),
		componentName() {
			return this.tabList.find((item) => item.key === this.tabValue)?.key || TAB_KEY.TCP_IP
		},
		isShowSaveBtn() {
			return SYSTEM_ARCH == systemArchStr.arm64 || this.componentName != TAB_KEY.TCP_IP
		}
	},
	data() {
		return {
			tabValue: TAB_KEY.TCP_IP,
			tabList: [
				{
					key: TAB_KEY.TCP_IP,
					title: 'TCP/IP'
				},
				{
					key: TAB_KEY.PORT,
					title: '端口'
				},
				{
					key: TAB_KEY.IOT,
					title: '云平台'
				}
			]
		}
	},
	methods: {
		async save() {
			const childRef = this.$refs.childRef
			if (!childRef) {
				return
			}
			const res = await childRef.validate()
			if (!res) {
				return
			}
			this.$prompt.info({
				type: 'warning',
				message: '确认保存配置？',
				callback: (data) => {
					if (data.type !== 'rightBtn') {
						return
					}
					childRef.save()
				}
			})
		}
	}
}
</script>

<style lang="less" scoped>
.basic-config {
	display: flex;
	flex-direction: column;
	width: 100%;
	/deep/.content-box {
		flex: 1;
		height: 100%;
		background: #f9fafd;
		border: 1px solid #cfd9f0;
		margin: 20px 20px 0;
		padding: 20px;
		overflow: auto;
		.gxx-form-item-container {
			height: 40px;
			line-height: 40px;
			margin-bottom: 30px;
			.label {
				height: inherit;
				line-height: inherit;
			}
			.form-item-content {
				height: inherit;
				.gxx-input-container {
					height: inherit;
					width: 300px;
					.clear {
						line-height: 20px;
					}
				}
			}
		}
		.gxx-subtitle {
			margin-bottom: 20px;
		}
	}
	.footer-box {
		margin: 20px auto;
		height: 30px;
		display: flex;
		.gxx-button {
			background: #13ba5a;
			width: 400px;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}
</style>
