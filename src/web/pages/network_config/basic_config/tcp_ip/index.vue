<template>
	<div class="tcp-ip" v-partial-loading="isLoad">
		<gxx-form :model="configParams" ref="basicForm" :labelWidth="130" :rules="rules" :showColon="true">
			<gxx-form-item label="设备IPv4地址" prop="ip">
				<gxx-input :disabled="isShowSaveBtn" v-model="configParams.ip" placeholder="127.0.0.1"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="IPv4子网掩码" prop="netmask">
				<gxx-input :disabled="isShowSaveBtn" v-model="configParams.netmask" placeholder="*************"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="IPv4默认网关" prop="gateway">
				<gxx-input :disabled="isShowSaveBtn" v-model="configParams.gateway" placeholder="127.0.0.1"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="物理地址" prop="mac">
				<gxx-input disabled v-model="configParams.mac"></gxx-input>
			</gxx-form-item>
			<div class="gxx-subtitle">DNS服务器配置</div>
			<gxx-form-item label="首选DNS服务器" prop="dns">
				<gxx-input disabled v-model="configParams.dns" placeholder="127.0.0.1"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="备用DNS服务器" prop="dns2">
				<gxx-input disabled v-model="configParams.dns2" placeholder="127.0.0.1"></gxx-input>
			</gxx-form-item>
		</gxx-form>
	</div>
</template>

<script>
import { isIP } from '@/common/libs/util'
const ipValid = (rule, value, callback) => {
	if (!value) {
		return callback(new Error('请配置信息！'))
	}
	if (!isIP(value)) {
		return callback(new Error('格式不正确！'))
	}
	callback()
}
export default {
	name: 'TcpIp',
	props: {
		isShowSaveBtn: Boolean
	},
	data() {
		return {
			isLoad: false,
			configParams: {
				ip: '127.0.0.1',
				dns: '',
				dns2: '',
				gateway: '127.0.0.1',
				netmask: '*************',
				mac: ''
			},
			rules: {
				ip: [{ required: true, trigger: 'blur', validator: ipValid }],
				gateway: [{ required: true, message: '请配置网关信息！', trigger: 'blur' }],
				netmask: [{ required: true, trigger: 'blur', validator: ipValid }]
			}
		}
	},
	created() {
		this.init()
	},
	methods: {
		init() {
			this.isLoad = true
			this.$http.systemApi
				.getNetworkInfo()
				.then((res) => {
					const { code, data } = res
					if (code === 200) {
						Object.assign(this.configParams, { ip: data.ip, dns: data.dns[0], dns2: data.dns[1], gateway: data.gateway, netmask: data.netmask, mac: data.mac })
					}
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		validate() {
			return new Promise((resolve, reject) => {
				this.$refs['basicForm'].validate((bool) => {
					resolve(bool)
				})
			})
		},
		save() {
			this.isLoad = true
			this.$http.systemApi
				.setNetworkInfo(this.configParams)
				.then((res) => {
					const { code, msg } = res
					if (code === 200) {
						this.$baseTip({ type: 'success', message: msg })
					}
				})
				.finally(() => {
					this.isLoad = false
				})
		}
	}
}
</script>
