<template>
	<div class="port" v-partial-loading="isLoad">
		<gxx-form :model="configParams" ref="portForm" :labelWidth="140" :rules="rules" :showColon="true">
			<gxx-form-item label="后台HTTP服务端口" prop="backHttpServerPort">
				<gxx-input v-model="configParams.backHttpServerPort" placeholder="80"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="后台WS服务端口" prop="backWebSocketServerPort">
				<gxx-input v-model="configParams.backWebSocketServerPort" placeholder="80"></gxx-input>
			</gxx-form-item>
		</gxx-form>
	</div>
</template>

<script>
import { isPort } from '@/common/libs/util'
const portValid = (rule, value, callback) => {
	if (!value) {
		return callback(new Error('请配置服务端口信息！'))
	}
	if (!isPort(value)) {
		return callback(new Error('格式不正确！'))
	}
	callback()
}
export default {
	name: 'Port',
	props: {
		terminalConfig: Object
	},
	data() {
		return {
			isLoad: false,
			configParams: {
				backHttpServerPort: '',
				backWebSocketServerPort: ''
			},
			rules: {
				backHttpServerPort: [{ required: true, validator: portValid, trigger: 'blur' }],
				backWebSocketServerPort: [{ required: true, validator: portValid, trigger: 'blur' }]
			}
		}
	},
	created() {
		Object.assign(this.configParams, this.terminalConfig)
	},
	methods: {
		validate() {
			return new Promise((resolve, reject) => {
				this.$refs['portForm'].validate((bool) => {
					resolve(bool)
				})
			})
		},
		save() {
			this.isLoad = true
			this.$store.dispatch('config/setTerminalConfigInfo', { ...this.terminalConfig, ...this.configParams }).finally(() => {
				this.isLoad = false
			})
		}
	}
}
</script>
