<template>
	<div class="port" v-partial-loading="isLoad">
		<gxx-form :model="configParams" ref="iotForm" :labelWidth="80" :rules="rules" :showColon="true">
			<gxx-form-item label="产品名称" prop="productName">
				<gxx-input v-model="configParams.productName" placeholder="XXX(IP)" :min="1" :maxlength="32"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="IP地址" prop="ip">
				<gxx-input v-model="configParams.ip" placeholder="127.0.0.1"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="服务端口" prop="port">
				<gxx-input v-model="configParams.port" placeholder="17314"></gxx-input>
			</gxx-form-item>
			<gxx-form-item label="产品编码" prop="productKey">
				<gxx-input v-model="configParams.productKey"></gxx-input>
			</gxx-form-item>
		</gxx-form>
	</div>
</template>

<script>
import { isIP, isPort } from '@/common/libs/util'
const ipValid = (rule, value, callback) => {
	if (!value) {
		return callback(new Error('请配置IP地址信息！'))
	}
	if (!isIP(value)) {
		return callback(new Error('格式不正确！'))
	}
	callback()
}
const portValid = (rule, value, callback) => {
	if (!value) {
		return callback(new Error('请配置服务端口信息！'))
	}
	if (!isPort(value)) {
		return callback(new Error('格式不正确！'))
	}
	callback()
}
const productKeyValid = (rule, value, callback) => {
	if (!value) {
		return callback(new Error('产品编码不能为空！'))
	}
	callback()
}
export default {
	name: 'Iot',
	props: {
		terminalConfig: Object
	},
	data() {
		return {
			isLoad: false,
			configParams: {
				port: '',
				ip: '',
				productKey: '',
				productName: '' // name(ip)
			},
			rules: {
				ip: [{ validator: ipValid, trigger: 'blur' }],
				port: [{ validator: portValid, trigger: 'blur' }],
				productKey: [{ validator: productKeyValid, trigger: 'blur' }],
				productName: [{ required: true, message: '产品名称不能为空！', trigger: 'blur' }]
			}
		}
	},
	created() {
		if (this.terminalConfig.iotInfo) {
			for (const key in this.configParams) {
				this.configParams[key] = this.terminalConfig.iotInfo[key]
			}
		}

		if (!this.configParams.productName) {
			this.getNetworkInfo()
		}
	},
	methods: {
		getNetworkInfo() {
			this.$http.systemApi.getNetworkInfo().then((res) => {
				const { code, data } = res
				if (isSuccessCode(code)) {
					const { itemNumberInfo = {}, operationInfo = {} } = this.terminalConfig
					const name = itemNumberInfo.name || operationInfo.terminalName || ''
					this.configParams.productName = `${name}(${data.ip})`
				}
			})
		},
		validate() {
			return new Promise((resolve, reject) => {
				const values = Object.values(this.configParams)
				const allEmpty = values.every((item) => !item)
				if (allEmpty) {
					this.rules = {}
					resolve(true)
				}
				const allFilled = values.every((item) => !!item)
				if (allFilled) {
					this.rules = {}
					resolve(true)
				}
				resolve(false)
				this.$refs['iotForm'].validate(() => {
					this.rules = this.$options.data().rules
				})
			})
		},
		save() {
			this.isLoad = true
			this.$store.dispatch('config/setTerminalConfigInfo', { ...this.terminalConfig, iotInfo: this.configParams }).finally(() => {
				this.isLoad = false
			})
		}
	}
}
</script>
