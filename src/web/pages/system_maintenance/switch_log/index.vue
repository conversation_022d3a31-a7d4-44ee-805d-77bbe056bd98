<template>
	<div class="switch-log">
		<div class="search-container">
			<div class="search-item">
				<div class="search-item-label">操作人</div>
				<span class="search-item-colon">:</span>
				<gxx-input v-model="requestParams.operatorName"></gxx-input>
			</div>
			<div class="search-item">
				<div class="search-item-label">开柜时间</div>
				<span class="search-item-colon">:</span>
				<gxx-date-picker v-model="time1" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" type="daterange"></gxx-date-picker>
			</div>
			<div class="search-item">
				<div class="search-button">
					<gxx-button @click="searchList">搜索</gxx-button>
				</div>
				<div class="search-button">
					<gxx-button type="info" @click="clearSearch">清空</gxx-button>
				</div>
			</div>
		</div>
		<div class="table-container">
			<gxx-table :data="tableData" :isFixedHead="true" :maxHeight="650">
				<gxx-table-column label="序号" type="index" width="60"></gxx-table-column>
				<gxx-table-column label="柜门" prop="sidesName"></gxx-table-column>
				<gxx-table-column label="操作类型" prop="operatorType"></gxx-table-column>
				<gxx-table-column label="操作人" prop="operatorName"></gxx-table-column>
				<gxx-table-column label="操作人类型" prop="operatorName">
					<template v-slot="row">
						<span>{{ typeObj[row.userType] }}</span>
					</template>
				</gxx-table-column>
				<gxx-table-column label="开柜时间" prop="openTime" width="180"></gxx-table-column>
				<gxx-table-column label="关柜时间" prop="closeTime" width="180"></gxx-table-column>
			</gxx-table>
			<div class="pagination-content">
				<gxx-pagination :total="total" @changeSize="changeSize" @change="getTableData" :pageSize="requestParams.size" v-model="requestParams.page"></gxx-pagination>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	name: 'SwitchLog',
	data() {
		return {
			tableData: [],
			total: 0,
			requestParams: {
				size: 20,
				page: 1,
				operatorName: '',
				startTime: '',
				endTime: ''
			},
			time1: '',
			typeObj: {
				1: '管理员',
				2: '工作人员',
				3: '非工作人员'
			}
		}
	},
	mounted() {
		this.getTableData()
	},
	methods: {
		searchList() {
			this.requestParams.startTime = this.time1?.length ? `${this.time1[0]} 00:00:00` : ''
			this.requestParams.endTime = this.time1?.length ? `${this.time1[1]} 23:59:59` : ''
			this.requestParams.page = 1
			this.getTableData()
		},
		clearSearch() {
			const options = this.$options.data()
			Object.assign(this.requestParams, options.requestParams)
			this.time1 = ''
			this.getTableData()
		},
		getTableData() {
			this.$http.appApi.getAccessRecords(this.requestParams).then((response) => {
				const { data, code } = response
				if (code === 200) {
					const tableData = data?.data || []
					this.tableData = tableData.map((item) => {
						return item
					})
					this.total = data.total || 0
				}
			})
		},
		changeSize(pageSize) {
			this.requestParams.size = pageSize
			this.getTableData()
		}
	}
}
</script>

<style scoped lang="less">
.switch-log {
	flex: 1;
	display: flex;
	flex-direction: column;
	position: relative;
	.search-container {
		padding: 20px 20px 10px;
		display: flex;
		flex-flow: row wrap;
		justify-content: flex-start;
		align-items: center;
		align-content: center;
		.search-item {
			display: flex;
			align-items: center;
			padding: 0 10px 10px 10px;
			color: #2b3646;
			.search-item-label {
				width: 70px;
				text-align: justify;
				text-align-last: justify;
				font-size: 16px;
				font-family: MicrosoftYaHei;
				text-align: right;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.search-item-colon {
				margin-left: 5px;
				margin-right: 10px;
			}
			.search-button {
				margin: 0 10px;
			}
		}
	}
	.table-container {
		padding: 0 20px 48px;
		height: 100%;
		.gxx-table-container {
			height: 100%;
		}
	}
}
</style>
