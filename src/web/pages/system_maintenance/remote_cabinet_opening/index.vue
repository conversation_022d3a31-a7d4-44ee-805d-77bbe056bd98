<template>
	<div class="remote-cabinet-opening">
		<div class="content-box">
			<gxx-carousel height="710px" type="card" :loop="false" :autoplay="false">
				<gxx-carousel-item v-for="(item, index) in tableData" :key="index">
					<cabinet-table :tableObj="item" :tableUserData="tableUserData" @selected="openLock"></cabinet-table>
				</gxx-carousel-item>
			</gxx-carousel>
		</div>
	</div>
</template>

<script>
import WebsocketMixin from '@/web/mixins/websocket'
import { mapGetters } from 'vuex'
import CabinetTable from './cabinet_table/index'
export default {
	name: 'RemoteCabinetOpening',
	components: {
		CabinetTable
	},
	mixins: [WebsocketMixin],
	computed: {
		...mapGetters({
			config: 'config/data'
		})
	},
	data() {
		return {
			tableUserData: [],
			tableData: [],
			wristbandDrawerInfoObj: {},
			clickFlag: false
		}
	},
	methods: {
		// 初始化锁控
		initLock() {
			const info = this.config.lockInfo.A
			const params = {}
			if (!info.baudRate) {
				this.$baseTip({ type: 'warning', message: '波特率未配置请联系工作人员' })
				return
			}
			if (!info.path) {
				this.$baseTip({ type: 'warning', message: '串口号未配置请联系工作人员' })
				return
			}
			Object.assign(params, {
				manufacturer: info.manufacturer,
				path: info.path,
				baudRate: info.baudRate
			})
			this.ws.initLock(params)
		},
		// 打开柜门
		openLock(item) {
			if (!this.clickFlag) {
				return this.$baseTip({ type: 'warning', message: '正在连接设备，请稍后重新操作' })
			}
			const openLockList = this.parseStringToArray(item.num)
			const params = {
				openLockList
			}
			this.ws.openLock(params)
		},
		parseStringToArray(input) {
			const [letter, number] = input
				.match(/([A-Z])(\d+)/)
				.slice(1)
				.map((part) => parseInt(part, 10) || part)
			const lockInfo = this.config.lockInfo
			let index = 0
			let door = number
			const maxNumArr = lockInfo[letter]?.maxNum.split(',') || []
			for (let i = 0; i < maxNumArr.length; i++) {
				if (door > maxNumArr[i]) {
					door = door - maxNumArr[i]
				} else {
					index = i
					break
				}
			}
			const lockPlateArr = lockInfo[letter]?.lockPlate.split(',') || []
			const address = lockPlateArr[index]
			const result = [{ address, door }]
			return result
		},
		handleMessage(message) {
			const { action, code, msg, data } = message
			if (code != 0) {
				return this.$baseTip({ type: 'error', message: data?.message || msg || '数据异常！' })
			}
			switch (action) {
				case 'init':
					if (data.flag === 0) {
						this.clickFlag = true
					}
					break
				default:
					break
			}
		},
		handleOpen() {
			this.initLock()
		}
	},
	created() {
		this.$http.appApi.getSidesAllList().then((res) => {
			this.tableData = res.data
		})
		this.$http.appApi.getIscdsUserList().then((res) => {
			this.tableUserData = res.data
		})
	},
	mounted() {
		this.initConnect('lock')
	}
}
</script>
<style lang="less" scoped>
.remote-cabinet-opening {
	width: 100%;
	padding: 20px;
	.content-box {
		margin: 0 auto;
		width: 1000px;
	}
}
</style>
