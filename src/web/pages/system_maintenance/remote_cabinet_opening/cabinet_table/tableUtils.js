const TableModel = {
	4: {
		19: { id: 4, maxRow: 9, maxCol: 3, length: 20, maxRowsPerCol: [9, 2, 9], merge: { 2: [11], 7: [10] }, screen: 10, heightType: { 2: [2, 3, 13, 14], 3: [1, 8, 9, 12, 19, 20] }, widthType: { 2: [10] } },
		26: { id: 4, maxRow: 9, maxCol: 3, length: 26, maxRowsPerCol: [9, 8, 9], merge: { 2: [17] }, heightType: { 2: [2, 3, 11, 12], 3: [1, 8, 9, 10, 18, 25, 26] }, widthType: { 2: [10, 11, 12, 13, 14, 15, 16, 17] } }
	},
	3: {
		18: { id: 3, maxRow: 18, maxCol: 2, maxRowsPerCol: [1, 18], length: 19, merge: { 18: [1] }, screen: 1 },
		36: { id: 3, maxRow: 18, maxCol: 2, maxRowsPerCol: [18, 18], length: 36 }
	},
	2: {
		21: { id: 2, maxRow: 9, maxCol: 3, length: 22, maxRowsPerCol: [9, 4, 9], merge: { 6: [10] }, screen: 10, heightType: { 2: [7, 8, 9, 11, 12, 13, 20, 21, 22] }, widthType: { 2: [10, 11, 12, 13] } },
		27: { id: 2, maxRow: 9, maxCol: 3, length: 27, maxRowsPerCol: [9, 9, 9], heightType: { 2: [7, 8, 9, 16, 17, 18, 25, 26, 27] }, widthType: { 2: [10, 11, 12, 13, 14, 15, 16, 17, 18] } }
	},
	1: {
		2: { id: 1, maxRow: 2, maxCol: 2, length: 3, maxRowsPerCol: [2, 1], horizontalMerge: { 2: [1] }, screen: 1, heightType: { 4: [3, 2], 8: [1] } },
		16: { id: 1, maxRow: 8, maxCol: 2, length: 16, maxRowsPerCol: [8, 8], heightType: { 2: [1, 2, 9, 10], 3: [8, 16] } }
	}
}
const BoxStyle = {
	min: {
		height: '700px',
		width: '500px'
	}
}
const HeightType = {
	4: {
		min: {
			1: '30px',
			2: '60px',
			3: '90px'
		}
	},
	3: {
		min: {
			1: '30px'
		}
	},
	2: {
		min: {
			1: '60px',
			2: '75px'
		}
	},
	1: {
		min: {
			1: '50px',
			2: '100px',
			3: '150px',
			4: '200px',
			8: '400px'
		}
	}
}
// 宽度按照比例算，需要减去左右加起来40，然后根据分几个就对于的减去（n-1）*20的间隙距离。
const WidthType = {
	4: {
		min: {
			1: '115px',
			2: '180px'
		}
	},
	3: {
		min: {
			1: '200px'
		}
	},
	2: {
		min: {
			1: '111px',
			2: '162px'
		}
	},
	1: {
		min: {
			1: '200px'
		}
	}
}
function getTypeValue(obj, num, typeKey) {
	for (const type in obj[typeKey]) {
		if (obj[typeKey][type].includes(num)) {
			return Number(type)
		}
	}
	return 1
}
// 辅助函数
function generateTableData(obj, name, reversed = false) {
	const arr = []
	let num = 1
	const maxRowsPerCol = reversed ? [...obj.maxRowsPerCol].reverse() : obj.maxRowsPerCol
	const maxCol = reversed ? obj.maxCol - 1 : 0
	const colIncrement = reversed ? -1 : 1
	for (let i = maxCol; reversed ? i >= 0 : i < obj.maxCol; i += colIncrement) {
		let addMerge = 0
		for (let j = 0; j < obj.maxRow; j++) {
			const merge = getTypeValue(obj, num, 'merge') // 处理纵向合并
			const heightType = getTypeValue(obj, num, 'heightType') // 处理高度类型
			const widthType = getTypeValue(obj, num, 'widthType') // 处理宽度类型
			const colSpan = getTypeValue(obj, num, 'horizontalMerge') // 处理横向合并
			const filteredItems = arr.filter((item) => item.rowPos == j)
			const lastItem = filteredItems[filteredItems.length - 1]
			const addColSpan = lastItem ? lastItem.colSpan - 1 : 0
			if (maxRowsPerCol[i] > j) {
				const item = {
					num: obj.screen == num ? `${name}000` : name + (num > obj.screen ? (num - 1).toString().padStart(2, '0') : num.toString().padStart(2, '0')),
					rowPos: j + addMerge + addColSpan,
					colPos: i,
					merge,
					colSpan,
					widthType,
					heightType
				}
				arr.push(item)
				num++
			}
			if (merge > 1) {
				addMerge = addMerge + merge - 1
			}
		}
	}
	const tableMatrix = Array.from({ length: obj.maxRow }, () => Array(obj.maxCol).fill({}))
	arr.forEach((item) => {
		const { rowPos, colPos } = item
		tableMatrix[rowPos][colPos] = item
	})
	return tableMatrix
}
// 方法定义
const TableUtils = {
	generateList(obj, name) {
		return generateTableData(obj, name)
	}
}

// 导出常量和方法
export { TableModel, TableUtils, HeightType, WidthType, BoxStyle }
