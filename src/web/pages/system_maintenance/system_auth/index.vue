<template>
	<div class="system-auth">
		<div class="item">
			<span>授权状态：</span>
			<gxx-radio v-for="item in authStateOptions" :key="item.value" disabled v-model="authObj.state" :value="item.value">{{ item.label }}</gxx-radio>
		</div>
		<div class="item" v-if="isAuthorized">
			<span>授权时间：</span>
			<span>{{ authObj.startTime }}</span>
			--
			<span>{{ authObj.endTime }}</span>
		</div>
		<div class="item">
			<span>授权文件：</span>
			<upload-file accept=".dat" inputClass="wt-250" @uploadSuccess="uploadAuthFileSuccess" v-model="authObj.fileName" @uploadError="uploadError"></upload-file>
		</div>
		<div class="item">
			<span>授权设备信息：</span>
			<gxx-button type="default" @click="exportDevicesInfo">导出</gxx-button>
		</div>
	</div>
</template>

<script>
import { authStateDic, authStateOptions } from '@/common/libs/options'
import UploadFile from '@/web/components/upload/index'
export default {
	name: 'SystemAuth',
	components: {
		UploadFile
	},
	computed: {
		isAuthorized() {
			return this.authObj.state == authStateDic.AUTHORIZED
		}
	},
	data() {
		return {
			authStateOptions,
			authObj: {
				state: authStateDic.UNAUTHORIZED,
				fileName: '',
				startTime: '',
				endTime: ''
			}
		}
	},
	created() {
		this.getDeviceAuthInfo()
	},
	methods: {
		/**
		 * 获取已经上传的硬件授权文件名称
		 */
		getDeviceAuthInfo() {
			this.$http.appApi.getDeviceAuthInfo().then((res) => {
				const { code, data } = res
				if (!isSuccessCode(code)) {
					this.authObj = this.$options.data().authObj
					return
				}
				this.authObj.fileName = data.fileName
				this.authObj.state = authStateDic.AUTHORIZED
				this.authObj.startTime = data.periodBegin || ''
				this.authObj.endTime = data.periodEnd || ''
			})
		},
		/**
		 * 描述：文件上传成功回调
		 */
		uploadAuthFileSuccess(data) {
			this.$http.fileApi.copyAuthFile(data).then((res) => {
				const { code } = res
				if (!isSuccessCode(code)) {
					this.authObj.fileName = ''
					return
				}
				this.getDeviceAuthInfo()
			})
		},
		uploadError() {
			this.$baseTip({ message: '文件上传失败，请重试...', type: 'error' })
		},
		/**
		 * 导出授权设备信息
		 */
		exportDevicesInfo() {
			this.$http.appApi.exportDevicesInfo().then((res) => {
				let index = res.filePath.lastIndexOf('\\')
				if (index == -1) {
					index = res.filePath.lastIndexOf('/')
				}
				const fileName = res.filePath.substring(index + 1)
				delete res.filePath
				this.createDownload(res, fileName)
			})
		},
		createDownload(res, fileName) {
			const fileReader = new FileReader()
			fileReader.onload = (e) => {
				const a = document.createElement('a')
				a.download = fileName
				a.href = e.target.result
				document.body.appendChild(a)
				a.click()
				document.body.removeChild(a)
			}
			fileReader.readAsDataURL(res)
		}
	}
}
</script>

<style lang="less" scoped>
.system-auth {
	padding: 20px;
	.item {
		height: 50px;
		display: flex;
		align-items: center;
		position: relative;
		label {
			width: 120px;
			text-align: justify;
			text-align-last: justify;
		}
		/deep/ .wt-250 {
			width: 250px;
		}
	}
}
</style>
