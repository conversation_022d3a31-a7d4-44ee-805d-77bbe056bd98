<template>
	<div ref="uploadForm" class="upload-form">
		<gxx-input :disabled="true" v-model="fileName" :class="fileName ? 'pr-92' : 'pr-62'"></gxx-input>
		<gxx-uploader :action="url" :accept="accept" :disabled="uploadStatus" :showFileList="false" :uploadError="uploadError" :uploadSuccess="uploadSuccess" :uploadProcess="uploadProcess" :beforeUpload="beforeUpload">
			<gxx-button :disabled="uploadStatus" class="btn">{{ fileName ? '重新上传' : '上传' }}</gxx-button>
		</gxx-uploader>
		<gxx-progress v-show="uploadStatus" :percentage="percentage" color="#2B5FDA" :stroke-width="2" :show-text="false"></gxx-progress>
	</div>
</template>

<script>
import { uploadUrl } from '@/config'
export default {
	name: 'Upload',
	computed: {
		isDisabled() {
			return !this.fileName || this.uploadStatus
		}
	},
	props: {
		url: {
			type: String,
			default: uploadUrl
		},
		accept: {
			type: String
		}
	},
	data() {
		return {
			fileName: '',
			// 进度条
			percentage: 0,
			// 上传状态
			uploadStatus: false
		}
	},
	methods: {
		/**
		 * 描述：文件上传之前函数
		 */
		beforeUpload(file) {
			this.uploadStatus = true
			return true
		},
		/**
		 * 描述：文件上传成功返回文件信息
		 */
		uploadSuccess(response, file, fileList) {
			const { code, msg, data } = response
			if (code == 200) {
				this.fileName = data.fileName
				this.$baseTip({ message: msg, type: 'success' })
				this.$emit('uploadSuccess', data)
			} else {
				this.uploadError()
			}
		},
		/**
		 * 描述：错误回调
		 */
		uploadError(file) {
			this.uploadStatus = false
			this.percentage = 0
			this.$baseTip({ message: '文件上传失败，请重试...', type: 'error' })
			this.$emit('upload-error')
		},
		/**
		 * 描述：进度条函数
		 */
		uploadProcess(ev) {
			this.percentage = ev.percent
			if (ev.percent === 100) {
				let timer = setTimeout(() => {
					this.uploadStatus = false
					this.percentage = 0
					clearTimeout(timer)
					timer = null
				}, 2000)
			}
		}
	}
}
</script>

<style lang="less" scoped>
.upload-form {
	display: flex;
	position: relative;
	/deep/ .gxx-input-container {
		width: 400px;
		input {
			border-top-right-radius: 0px;
			border-bottom-right-radius: 0px;
		}
		&.pr-62 {
			input {
				padding-right: 62px;
			}
		}
		&.pr-92 {
			input {
				padding-right: 92px;
			}
		}
	}
	.gxx-upload-body {
		.btn {
			border-top-left-radius: 0px;
			border-bottom-left-radius: 0px;
		}
	}
	.gxx-progress {
		.gxx-line-progress {
			.gxx-progress-bar__outer {
				width: 100% !important;
			}
		}
	}
}
</style>
