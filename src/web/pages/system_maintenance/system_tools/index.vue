<template>
	<div class="system-tools" v-partial-loading="isLoad">
		<div class="info-box">
			<div class="gxx-subtitle">工具</div>
			<div class="item-box">
				<!-- <gxx-button @click="reboot" v-if="isRole">重启设备</gxx-button> -->
				<gxx-button @click="rebootApp">重启应用</gxx-button>
				<gxx-button @click="exportLog">导出日志</gxx-button>
				<gxx-button @click="exportFile('config')" v-if="isRole">导出配置</gxx-button>
				<gxx-button @click="exportFile('db')" v-if="isRole">导出数据库</gxx-button>
				<!-- <gxx-button @click="uninstallApp" v-if="isUninstallAppBtn">卸载应用</gxx-button> -->
			</div>
		</div>
		<div class="info-box" v-if="isRole">
			<div class="gxx-subtitle">配置</div>
			<div class="item-box">
				<div class="item" v-for="(item, index) in configList" :key="index">
					<p class="item-label">{{ item.label }}</p>
					<span class="item-colon">:</span>
					<template v-if="item.type === 'input'">
						<gxx-input class="item-value wt-400" v-model="configData[item.valueKey]"></gxx-input>
					</template>
					<gxx-button class="item-btn" @click="saveConfigItem(item)">保存</gxx-button>
				</div>
			</div>
			<div class="gxx-subtitle">文件上传</div>
			<div class="item-box">
				<div class="item">
					<p class="item-label">安装应用</p>
					<span class="item-colon">:</span>
					<div class="upload-box">
						<upload-file :accept="installPackageAccept" @uploadSuccess="uploadInstallPackageSuccess" @uploadError="uploadError"></upload-file>
					</div>
				</div>
				<div class="item">
					<p class="item-label">更新配置文件</p>
					<span class="item-colon">:</span>
					<div class="upload-box">
						<upload-file accept=".json" @uploadSuccess="uploadConfigSuccess" @uploadError="uploadError"></upload-file>
					</div>
				</div>
				<div class="item">
					<p class="item-label">更新数据库</p>
					<span class="item-colon">:</span>
					<div class="upload-box">
						<upload-file accept=".db" @uploadSuccess="uploadDbSuccess" @uploadError="uploadError"></upload-file>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import UploadFile from '@/web/components/upload/index'
import UninstallAppDialog from './uninstall/index'
import { systemPlatformStr, systemArchStr } from '@/config'
export default {
	name: 'SystemTools',
	components: {
		UploadFile
	},
	computed: {
		...mapGetters({
			userInfo: 'user/userInfo',
			roles: 'user/roles',
			data: 'config/data'
		}),
		isRole() {
			return [this.roles.sysAdmin, this.roles.admin].includes(Number(this.userInfo.role))
		},
		isUninstallAppBtn() {
			return this.isRole && SYSTEM_PLATFORM == systemPlatformStr.linux && SYSTEM_ARCH == systemArchStr.arm64
		}
	},
	data() {
		return {
			isLoad: false,
			installPackageAccept: SYSTEM_PLATFORM == systemPlatformStr.linux ? '.deb' : '.exe',
			configList: [
				{
					label: '管理系统标题',
					valueKey: 'logoName',
					type: 'input'
				},
				{
					label: '客户端标题',
					valueKey: 'terminalName',
					type: 'input'
				},
				{
					label: '版权方',
					valueKey: 'name',
					type: 'input'
				},
				{
					label: '版权信息',
					valueKey: 'copyright',
					type: 'input'
				}
			],
			configData: {
				logoName: '',
				terminalName: '',
				name: '',
				copyright: ''
			}
		}
	},
	created() {
		this.initConfig()
	},
	methods: {
		exportLog() {
			this.isLoad = true
			this.$http.fileApi
				.getLogs()
				.then((res) => {
					const fileName = this.getFileNameByFilePath(res.filePath)
					delete res.filePath
					this.createDownload(res, fileName)
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		initConfig() {
			this.$store.dispatch('config/getTerminalConfigInfo').then((data) => {
				Object.assign(this.configData, data.operationInfo)
				this.$EventBus.$emit('update-title', data.operationInfo.logoName)
			})
		},
		reboot() {
			this.$prompt.info({
				type: 'warning',
				message: '是否要重启设备？',
				callback: (res) => {
					res.type === 'rightBtn' && this.rebootSystemApi()
				}
			})
		},
		rebootSystemApi() {
			this.isLoad = true
			this.$http.systemApi
				.rebootSystem()
				.then((res) => {
					const { code } = res
					if (code == 200) {
						window.location.reload()
					}
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		rebootApp() {
			this.$prompt.info({
				type: 'warning',
				message: '是否要重启应用？',
				callback: (res) => {
					res.type == 'rightBtn' && this.rebootAppApi()
				}
			})
		},
		rebootAppApi() {
			this.isLoad = true
			this.$http.appApi
				.rebootApp()
				.then((res) => {
					const { code } = res
					if (code == 200) {
						window.location.reload()
					}
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		exportFile(type) {
			const map = {
				log: 'getLogs',
				config: 'exportConfigFile',
				db: 'exportDbFile'
			}
			this.isLoad = true
			this.$http.appApi[map[type]]()
				.then((res) => {
					const fileName = this.getFileNameByFilePath(res.filePath)
					delete res.filePath
					this.createDownload(res, fileName)
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		getFileNameByFilePath(filePath) {
			let index = filePath.lastIndexOf('\\')
			if (index == -1) {
				index = filePath.lastIndexOf('/')
			}
			const fileName = filePath.substring(index + 1)
			return fileName
		},
		createDownload(res, fileName) {
			const fileReader = new FileReader()
			fileReader.readAsDataURL(res)
			fileReader.onload = (e) => {
				const a = document.createElement('a')
				a.download = fileName
				a.href = e.target.result
				document.body.appendChild(a)
				a.click()
				document.body.removeChild(a)
			}
		},
		uploadInstallPackageSuccess(data) {
			this.$http.appApi.installPackage(data).then((res) => {
				const { code, msg } = res
				this.$baseTip({ message: msg, type: code == 200 ? 'success' : 'error' })
			})
		},
		uploadConfigSuccess(data) {
			this.$http.fileApi.copyConfigFile(data).then((res) => {
				const { code, msg } = res
				if (code != 200) {
					return this.$baseTip({ message: msg, type: 'error' })
				}
				this.$baseTip({ message: msg, type: 'success' })
				this.initConfig()
			})
		},
		uploadDbSuccess(data) {
			this.$http.fileApi.copyDbFile(data).then((res) => {
				const { code, msg } = res
				this.$baseTip({ message: msg, type: code == 200 ? 'success' : 'error' })
			})
		},
		uploadError() {
			this.$baseTip({ message: '文件上传失败，请重试...', type: 'error' })
		},
		uninstallApp() {
			this.$GxxDialog({
				title: '卸载应用',
				showHeader: true,
				showFooter: false,
				component: UninstallAppDialog
			})
		},
		saveConfigItem(item) {
			const isUpdateLogoName = item.valueKey === 'logoName'
			if (!this.configData[item.valueKey]) {
				if (isUpdateLogoName) {
					return this.$baseTip({ type: 'warning', message: '管理系统标题不能为空' })
				}
				if (item.valueKey === 'terminalName') {
					return this.$baseTip({ type: 'warning', message: '客户端标题不能为空' })
				}
			}
			const configParams = {
				operationInfo: {
					...this.data.operationInfo,
					[item.valueKey]: this.configData[item.valueKey]
				}
			}
			this.$store.dispatch('config/setTerminalConfigInfo', { ...this.data, ...configParams }).then((res) => {
				if (!res) {
					return
				}
				if (isUpdateLogoName) {
					this.$EventBus.$emit('update-title', this.configData[item.valueKey])
					this.$store.dispatch('config/updateTitle', this.configData[item.valueKey])
				}
			})
		}
	}
}
</script>

<style lang="less" scoped>
.system-tools {
	padding: 20px;
	.info-box {
		.item-box {
			padding: 10px 0 10px 10px;
			.gxx-button + .gxx-button {
				margin-left: 20px;
			}
			.gxx-button-default {
				color: rgba(0, 137, 255, 1);
				border-radius: 4px;
				border: 1px solid #cfd9f0;
				background: #ffffff;
				&:hover {
					background: #0089ff;
					color: #fff;
				}
			}
			.item {
				height: 50px;
				display: flex;
				align-items: center;
				.item-label {
					width: 130px;
					text-align-last: justify;
					display: inline-block;
					position: relative;
				}
				.item-colon {
					margin: 0 10px;
				}
				.item-value {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					&.wt-400 {
						width: 400px;
					}
				}
				.item-btn {
					margin-left: 20px;
				}
			}
		}
	}
}
</style>
