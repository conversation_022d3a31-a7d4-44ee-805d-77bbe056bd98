<template>
	<div class="system-props" v-partial-loading="isLoad">
		<div class="content-box">
			<div class="info-box" v-for="(info, index) in infoList" :key="index">
				<div class="gxx-subtitle">{{ info.title }}</div>
				<div class="item-box">
					<div class="item" v-for="(item, index) in info.list" :key="index">
						<p class="item-label">{{ item.label }}</p>
						<span class="item-colon">:</span>
						<gxx-progress v-if="item.isShowRefresh" :percentage="getPercentage(item)" :status-text="getStatusText(item)" :status="getStatus(item)" width="250" text-type="bottom"></gxx-progress>
						<p v-else class="item-value">
							{{ propertyData[item.valueKey] || '--' }}
							<span v-if="propertyData[item.valueKey] && item.unit">{{ item.unit }}</span>
						</p>
						<div class="icon-refresh" v-if="item.isShowRefresh" @click="refresh(item)" title="刷新"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { formatDate } from '@/common/libs/util'
export default {
	name: 'SystemProps',
	computed: {
		...mapGetters({
			userInfo: 'user/userInfo'
		})
	},
	data() {
		return {
			infoList: [
				{
					title: '系统',
					list: [
						{
							label: '操作系统',
							valueKey: 'sysPlatform'
						},
						{
							label: '系统架构',
							valueKey: 'sysArch'
						},
						{
							label: '系统版本',
							valueKey: 'sysVersion'
						}
					]
				},
				{
					title: '内存',
					list: [
						{
							label: '总内存',
							valueKey: 'memory',
							unit: 'GB'
						},
						{
							label: '内存使用率',
							valueKey: 'used',
							isShowRefresh: true
						},
						{
							label: '空闲内存率',
							valueKey: 'free',
							isShowRefresh: true
						}
					]
				},
				{
					title: 'CPU',
					list: [
						{
							label: 'cpu名称',
							valueKey: 'cpuName'
						},
						{
							label: 'cpu使用率',
							valueKey: 'cpuUsed',
							isShowRefresh: true
						},
						{
							label: 'cpu核心线程数',
							valueKey: 'threadNumber',
							unit: '核'
						}
					]
				},
				{
					title: '磁盘',
					list: [
						{
							label: '磁盘总容量',
							valueKey: 'diskTotal',
							unit: 'GB'
						},
						{
							label: '磁盘使用率',
							valueKey: 'diskUsed',
							isShowRefresh: true
						},
						{
							label: '磁盘空闲率',
							valueKey: 'diskFree',
							isShowRefresh: true
						}
					]
				}
			],
			propertyData: {
				threadNumber: 0,
				memory: 0,
				free: 0,
				used: 0,
				cpuUsed: 0,
				cpuName: '',
				sysVersion: '',
				sysPlatform: '',
				sysArch: '',
				diskTotal: 0,
				diskUsed: 0,
				diskFree: 0
			},
			isLoad: false
		}
	},
	created() {
		this.getProperty()
	},
	methods: {
		getProperty(prop = '', callback) {
			this.isLoad = true
			this.$http.systemApi
				.getSystemProperties(prop)
				.then((res) => {
					const { code, data } = res
					if (code != 200) {
						return
					}
					if (callback) {
						callback(data)
					} else {
						this.handleData(data)
					}
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		handleData(data) {
			const memory = this.MbToGb(data.memory, 0)
			const used = this.MbToGb(data.used)
			const free = this.MbToGb(data.free)
			const diskTotal = this.MbToGb(data.diskTotal, 0)
			const diskUsed = this.MbToGb(data.diskUsed)
			const diskFree = this.MbToGb(data.diskFree)
			Object.assign(this.propertyData, data, { memory, used, free, diskTotal, diskUsed, diskFree })
		},
		MbToGb(mb, num = 2) {
			return parseFloat(Number(mb) / 1024).toFixed(num)
		},
		refresh(item) {
			this.getProperty(item.valueKey, (data) => {
				if (['used', 'free', 'diskUsed', 'diskFree'].includes(item.valueKey)) {
					this.propertyData[item.valueKey] = this.MbToGb(data[item.valueKey])
				} else if (['memory', 'diskTotal'].includes(item.valueKey)) {
					this.propertyData[item.valueKey] = this.MbToGb(data[item.valueKey], 0)
				} else {
					this.propertyData[item.valueKey] = data[item.valueKey]
				}
				const updateTime = formatDate()
				item.time = updateTime
			})
		},
		getPercentage(item) {
			if (item.valueKey == 'cpuUsed') {
				return this.propertyData[item.valueKey] || 0
			}
			if (['used', 'free'].includes(item.valueKey)) {
				return parseFloat(((this.propertyData[item.valueKey] * 100) / this.propertyData.memory).toFixed(2)) || 0
			}
			if (['diskUsed', 'diskFree'].includes(item.valueKey)) {
				return parseFloat(((this.propertyData[item.valueKey] * 100) / this.propertyData.diskTotal).toFixed(2)) || 0
			}
			return 0
		},
		getStatusText(item) {
			if (['used', 'free', 'diskUsed', 'diskFree'].includes(item.valueKey)) {
				return `${this.propertyData[item.valueKey]}GB`
			}
			return ''
		},
		getStatus(item) {
			const percentage = this.getPercentage(item)
			if (percentage <= 30) {
				return 'success'
			} else if (percentage > 30 && percentage <= 50) {
				return 'default'
			} else if (percentage > 50 && percentage <= 80) {
				return 'warning'
			} else if (percentage > 80) {
				return 'exception'
			}
		}
	}
}
</script>

<style lang="less" scoped>
.system-props {
	width: 100%;
	display: flex;
	flex-direction: column;
	padding: 20px;
	.content-box {
		flex: 1;
		height: 100%;
		.info-box {
			font-size: 18px;
			.item-box {
				padding: 5px 0 10px 10px;
				.item {
					height: 40px;
					display: flex;
					align-items: center;
					.item-label {
						width: 130px;
						text-align-last: justify;
						display: inline-block;
						position: relative;
					}
					.item-colon {
						margin: 0 10px;
					}
					.item-value {
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
					.icon-refresh {
						margin-left: 10px;
						width: 30px;
						height: 30px;
						background-image: url('~@/web/assets/images/refresh.png');
						background-position: center center;
						background-repeat: no-repeat;
						cursor: pointer;
						&:hover {
							background-image: url('~@/web/assets/images/refresh_hover.png');
						}
					}
				}
			}
		}
	}
}
</style>
