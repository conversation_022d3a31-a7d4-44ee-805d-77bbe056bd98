<template>
	<div class="uninstall-app">
		<gxx-form :model="form" ref="form" :rules="rules" :label-width="100" :label-position="'justify'" :showColon="true">
			<gxx-form-item label="应用名称" prop="appName">
				<gxx-select v-model="form.appName" width="300px" :enableEmpty="false" :option="appList"></gxx-select>
			</gxx-form-item>
		</gxx-form>
		<div class="tools">
			<gxx-button type="info" @click="cancel">取消</gxx-button>
			<gxx-button type="default" @click="confirm">卸载</gxx-button>
		</div>
	</div>
</template>

<script>
export default {
	name: 'UninstallApp',
	data() {
		return {
			isLoad: false,
			form: {
				appName: ''
			},
			rules: {
				appName: [{ required: true, message: '请选择应用名称', trigger: 'change blur' }]
			},
			appList: [
				{
					label: '随身物品柜(cupboard-static)',
					value: 'cupboard-static'
				}
				// {
				// 	label: '案管智能案卷柜(ims-imt-static)',
				// 	value: 'ims-imt-static'
				// },
				// {
				// 	label: '案管自助终端(sst-client)',
				// 	value: 'sst-client'
				// },
				// {
				// 	label: '案管登记终端(ram-client)',
				// 	value: 'ram-client'
				// },
				// {
				// 	label: '物管智能物管柜(pym-client)',
				// 	value: 'pym-client'
				// },
				// {
				// 	label: '办案自助终端(cshTerminal)',
				// 	value: 'cshTerminal'
				// },
				// {
				// 	label: '缉毒管控终端(utterminal)',
				// 	value: 'utterminal'
				// },
				// {
				// 	label: '接报警终端(ptsterminal)',
				// 	value: 'ptsterminal'
				// }
			]
		}
	},
	methods: {
		confirm() {
			this.$refs.form.validate((valid) => {
				if (!valid) {
					return
				}
				this.isLoad = true
				this.$http.appApi
					.uninstallApp(this.form)
					.then((res) => {
						const { code, msg } = res
						this.$baseTip({ message: msg, type: code == 200 ? 'success' : 'error' })
						code == 200 && this.$parent.close('confirm')
					})
					.finally(() => {
						this.isLoad = false
					})
			})
		},
		cancel() {
			this.$parent.close('cancel')
		}
	}
}
</script>

<style lang="less" scoped>
.uninstall-app {
	width: 450px;
	padding-bottom: 20px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
</style>
