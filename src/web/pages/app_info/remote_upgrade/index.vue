<template>
	<div class="other">
		<gxx-form :model="propertyData" ref="form" :label-width="100" :label-position="'justify'" :showColon="true">
			<gxx-form-item label="现版本">
				<gxx-input :disabled="true" v-model="propertyData.version"></gxx-input>
			</gxx-form-item>
		</gxx-form>
		<!-- <div class="gxx-subtitle">工具</div> -->
		<div class="btns">
			<!-- <gxx-button type="default" @click="reboot" v-if="isRole">重启设备</gxx-button> -->
			<gxx-button type="default" @click="rebootApp">重启应用</gxx-button>
			<gxx-button type="default" @click="downloadLog">下载日志</gxx-button>
			<gxx-button type="default" @click="upload">上传并安装应用包</gxx-button>
			<!-- <gxx-button type="default" @click="uninstallApp" v-if="isRole && propertyData.sysPlatform == 'linux' && propertyData.sysPlatform == 'arm64'">卸载应用</gxx-button> -->
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import uploadDialog from './upload'
import uninstallAppDialog from './uninstall'
export default {
	name: 'RemoteUpgrade',
	computed: {
		...mapGetters({
			userInfo: 'user/userInfo',
			roles: 'user/roles'
		}),
		isRole() {
			return [this.roles.sysAdmin, this.roles.admin].includes(Number(this.userInfo.role))
		}
	},
	data() {
		return {
			propertyData: {}
		}
	},
	methods: {
		refresh(item) {
			this.$emit('refresh', item)
		},
		reboot() {
			this.$prompt.info({
				type: 'warning',
				message: '是否要重启设备？',
				callback: (data) => {
					if (data.type !== 'rightBtn') {
						return
					}
					this.isLoad = true
					this.$http.systemApi
						.rebootSystem()
						.then((res) => {
							const { code } = res
							if (code == 200) {
								window.location.reload()
							}
						})
						.finally(() => {
							this.isLoad = false
						})
				}
			})
		},
		rebootApp() {
			this.$prompt.info({
				type: 'warning',
				message: '是否要重启应用？',
				callback: (data) => {
					if (data.type !== 'rightBtn') {
						return
					}
					this.isLoad = true
					this.$http.appApi
						.rebootApp()
						.then((res) => {
							const { code } = res
							if (code == 200) {
								window.location.reload()
							}
						})
						.finally(() => {
							this.isLoad = false
						})
				}
			})
		},
		downloadLog() {
			this.isLoad = true
			this.$http.fileApi
				.getLogs()
				.then((res) => {
					let index = res.filePath.lastIndexOf('\\')
					if (index == -1) {
						index = res.filePath.lastIndexOf('/')
					}
					const fileName = res.filePath.substring(index + 1)
					delete res.filePath
					this.createDownload(res, fileName)
				})
				.finally(() => {
					this.isLoad = false
				})
		},
		createDownload(res, fileName) {
			const fileReader = new FileReader()
			fileReader.readAsDataURL(res)
			fileReader.onload = (e) => {
				const a = document.createElement('a')
				a.download = fileName
				a.href = e.target.result
				document.body.appendChild(a)
				a.click()
				document.body.removeChild(a)
			}
		},
		upload() {
			this.$GxxDialog({
				title: '上传',
				showHeader: true,
				showFooter: false,
				component: uploadDialog,
				componentParams: { sysPlatform: this.propertyData.sysPlatform }
			}).on.then((res) => {
				if (res.type == 'confirm') {
					const index = this.infos.findIndex((item) => item.valueKey == 'version')
					index > -1 && this.refresh(index)
				}
			})
		},
		uninstallApp() {
			this.$GxxDialog({
				title: '卸载应用',
				showHeader: true,
				showFooter: false,
				component: uninstallAppDialog
			})
		}
	},
	created() {
		this.$http.systemApi
			.getSystemProperties()
			.then((res) => {
				const { code, data } = res
				if (code != 200) {
					return
				}
				this.propertyData = data
			})
			.finally(() => {
				this.isLoad = false
			})
	}
}
</script>

<style lang="less" scoped>
.other {
	padding: 40px;
	.btns {
		display: flex;
		margin-top: 20px;
		.gxx-button + .gxx-button {
			margin-left: 20px;
		}
		.gxx-button-default {
			color: rgba(0, 137, 255, 1);
			border-radius: 4px;
			border: 1px solid #cfd9f0;
			background: #ffffff;
			&:hover {
				background: #0089ff;
				color: #fff;
			}
		}
	}
}
</style>
