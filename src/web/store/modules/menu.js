import api from '@/web/api/index'
import routes from '@/web/router/modules/default'
import filterTerminalRoutes from '@/web/router/modules/other_router'

const state = {
	menuList: routes, // 路由菜单
	isSetMenu: false // 是否设置菜单
}
const getters = {
	menuList: (state) => state.menuList,
	isSetMenu: (state) => state.isSetMenu
}
const mutations = {
	// 设置路由菜单
	setMenuList(state, data) {
		state.menuList = data
		state.isSetMenu = true
	},
	setIsSetMenu(state, data) {
		state.setIsSetMenu = data
	}
}
const actions = {
	// 根据终端类型展示对应的菜单
	getItemNumberToShowMenu({ rootState, commit }) {
		return new Promise(async (resolve, reject) => {
			let itemNumber = rootState.config.itemNumber
			if (!itemNumber) {
				// 如果没有获取到设备类型，从接口获取
				const { data, code } = await api.appApi.getTerminalConfigInfo()
				if (code === 200) {
					itemNumber = data.itemNumberInfo.itemNumber || ''
					commit('config/setItemNumber', itemNumber, { root: true })
				} else {
					reject([])
				}
			}
			const newRoutes = filterTerminalRoutes(itemNumber)
			const menuList = JSON.parse(JSON.stringify(state.menuList))
			menuList.forEach((item) => {
				if (item.name === 'Operation' && item.children) {
					item.children = [...item.children, ...newRoutes]
				}
			})
			commit('setMenuList', menuList)
			resolve(newRoutes)
		})
	}
}

export default { state, getters, actions, mutations }
