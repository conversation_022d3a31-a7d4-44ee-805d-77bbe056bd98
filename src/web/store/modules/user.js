import { tokenName, userInfo, roles, loginRouterPath } from '@/config'
import router from '@/web/router'

const state = {
	token: localStorage.getItem(tokenName) || '',
	userInfo: localStorage.getItem(userInfo) ? JSON.parse(localStorage.getItem(userInfo)) : {},
	roles
}
const getters = {
	token: (state) => state.token,
	userInfo: (state) => state.userInfo,
	roles: (state) => state.roles
}
const mutations = {
	setToken(state, token) {
		state.token = token
		localStorage.setItem(tokenName, token)
	},
	removeToken(state) {
		state.token = ''
		localStorage.removeItem(tokenName)
	},
	setUserInfo(state, info) {
		state.userInfo = info
		localStorage.setItem(userInfo, JSON.stringify(info))
	},
	removeUserInfo(state) {
		state.userInfo = null
		localStorage.removeItem(userInfo)
	}
}
const actions = {
	setToken({ commit }, token) {
		commit('setToken', token)
	},
	removeToken({ commit }) {
		commit('removeToken')
		router.push(loginRouterPath)
	},
	setUserInfo({ commit }, data) {
		commit('setUserInfo', data)
	},
	removeUserInfo({ commit }) {
		commit('removeUserInfo')
	}
}

export default { state, getters, actions, mutations }
