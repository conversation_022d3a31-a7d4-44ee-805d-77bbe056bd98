import api from '@/web/api/index'
import Vue from 'vue'

const state = {
	data: {},
	itemNumber: '' // 终端料号
}
const getters = {
	data: (state) => state.data,
	itemNumber: (state) => state.itemNumber
}
const mutations = {
	setData(state, data) {
		Object.assign(state.data, data)
	},
	removeData(state) {
		state.data = {}
	},
	setItemNumber(state, data) {
		state.itemNumber = data
	}
}
const actions = {
	getTerminalConfigInfo({ commit }) {
		return new Promise(async (resolve, reject) => {
			const { data, code } = await api.appApi.getTerminalConfigInfo()
			if (code === 200) {
				const itemNumberInfo = data.itemNumberInfo || {}
				const title = `高新兴智能硬件运维管理系统`
				data.logoName = title
				const titleDom = document.querySelector('title')
				titleDom.innerText = title
				commit('setItemNumber', itemNumberInfo.itemNumber)
				commit('setData', data)
				resolve(data)
			} else {
				reject(null)
			}
		})
	},
	setTerminalConfigInfo({ commit }, config) {
		return new Promise((resolve, reject) => {
			api.appApi.setTerminalConfigInfo(config).then((res) => {
				if (res.code === 200) {
					commit('setData', config)
					Vue.prototype.$baseTip({ message: '配置保存成功!', type: 'success' })
					resolve(true)
				} else {
					reject()
				}
			})
		})
	},
	removeData({ commit }) {
		commit('removeData')
	}
}

export default { state, getters, actions, mutations }
