import Vue from 'vue'
import App from './App.vue'
import store from '@/web/store'
import router from '@/web/router'
import GosuncnUI from 'gosuncn-ui'
import 'gosuncn-ui/lib/style/index.css'
import '@/common/assets/style/reset.less'
import '@/web/assets/style/reset.less'
import '@/web/assets/style/common.less'
import api from '@/web/api'
import { successCode } from '@/config'
Vue.use(GosuncnUI)
Vue.prototype.$baseTip = GosuncnUI.GxxMessage
Vue.prototype.$prompt = GosuncnUI.GxxPrompt
Vue.config.productionTip = false
Vue.prototype.$http = api
Vue.prototype.$EventBus = new Vue()

window.isSuccessCode = (code) => {
	return successCode.includes(code)
}
new Vue({
	store,
	router,
	render: (h) => h(App)
}).$mount('#appWeb')
