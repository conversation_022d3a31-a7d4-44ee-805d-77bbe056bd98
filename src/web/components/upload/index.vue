<template>
	<div ref="uploadForm" class="upload-form">
		<gxx-input :class="inputClass" :disabled="true" v-model="currentValue"></gxx-input>
		<gxx-uploader :action="url" :accept="accept" :disabled="uploadStatus" :showFileList="false" :uploadError="uploadError" :uploadSuccess="uploadSuccess" :uploadProcess="uploadProcess" :beforeUpload="beforeUpload">
			<gxx-button :disabled="uploadStatus" class="btn">{{ currentValue ? '重新上传' : '上传' }}</gxx-button>
		</gxx-uploader>
		<gxx-progress v-show="uploadStatus" :percentage="percentage" width="50" text-type="inside" type="circle" color="#3A74FF"></gxx-progress>
	</div>
</template>

<script>
import { uploadUrl } from '@/config'
import { delay } from '@/common/libs/util'
export default {
	name: 'Upload',
	computed: {
		isDisabled() {
			return !this.currentValue || this.uploadStatus
		}
	},
	props: {
		url: {
			type: String,
			default: uploadUrl
		},
		accept: {
			type: String,
			default: ''
		},
		value: {
			type: String,
			default: ''
		},
		inputClass: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			currentValue: this.value,
			// 进度条
			percentage: 0,
			// 上传状态
			uploadStatus: false
		}
	},
	watch: {
		value(val) {
			this.currentValue = val
		}
	},
	methods: {
		/**
		 * 描述：文件上传之前函数
		 */
		beforeUpload(file) {
			this.uploadStatus = true
			return true
		},
		/**
		 * 描述：文件上传成功返回文件信息
		 */
		uploadSuccess(response, file, fileList) {
			const { code, data } = response
			if (code == 200) {
				this.currentValue = data.fileName
				this.$emit('uploadSuccess', data)
			} else {
				this.uploadError()
			}
		},
		/**
		 * 描述：错误回调
		 */
		uploadError(file) {
			this.uploadStatus = false
			this.percentage = 0
			this.$emit('uploadError')
		},
		/**
		 * 描述：进度条函数
		 */
		async uploadProcess(ev) {
			this.percentage = Math.floor(ev.percent)
			if (this.percentage === 100) {
				await delay(2000)
				this.uploadStatus = false
				this.percentage = 0
			}
		}
	}
}
</script>

<style lang="less" scoped>
.upload-form {
	display: flex;
	position: relative;
	align-items: center;
	/deep/ .gxx-input-container {
		width: 400px;
		input {
			border-top-right-radius: 0px;
			border-bottom-right-radius: 0px;
		}
	}
	.gxx-upload-body {
		.btn {
			border-top-left-radius: 0px;
			border-bottom-left-radius: 0px;
		}
	}
	.gxx-progress {
		margin-left: 10px;
	}
}
</style>
