import { mapGetters } from 'vuex'
import Usb from '@/common/libs/hardware/usb'
import QrCode from '@/common/libs/hardware/qr_code'
import ControlLock from '@/common/libs/hardware/control_lock'
import Light from '@/common/libs/hardware/light'
import Lock from '@/common/libs/hardware/lock'
import Rfid from '@/common/libs/hardware/rfid'
import SignFinger from '@/common/libs/hardware/sign_finger'
import ContactVitalSigns from '@/common/libs/hardware/contact_vital_signs'
import NonContactVitalSigns from '@/common/libs/hardware/non_contact_vital_signs'
export default {
	computed: {
		...mapGetters('config', ['data'])
	},
	data() {
		return {
			ws: null
		}
	},
	methods: {
		// 初始化连接
		initConnect(type) {
			const map = {
				usb: Usb,
				qrcode: QrCode,
				controlLock: ControlLock,
				light: Light,
				lock: Lock,
				rfid: Rfid,
				signFinger: SignFinger,
				contactVitalSigns: ContactVitalSigns,
				nonContactVitalSigns: NonContactVitalSigns
			}
			if (!map[type]) {
				return
			}
			this.ws = new map[type]({ ip: window.location.hostname, port: this.data.backWebSocketServerPort })
			this.addWsListeners()
			this.ws.init()
		},
		addWsListeners() {
			if (!this.ws) {
				return
			}
			this.ws.on('handleBack', (res) => {
				const { type, msg, data } = res
				if (type === 'open') {
					this.handleOpen()
				} else if (type === 'close') {
					this.handleClose()
				} else if (type === 'error') {
					this.handleError(msg)
				} else if (type === 'message') {
					data && this.handleMessage(data)
				}
			})
		},
		// 处理open数据
		handleOpen() {},
		// 处理close数据
		handleClose() {
			this.ws = null
		},
		// 处理error数据
		handleError(msg) {
			this.$baseTip({ type: 'error', message: msg })
			this.ws = null
		},
		// 处理message数据
		handleMessage() {},
		// 关闭连接
		closeConnect() {
			this.ws && this.ws.close()
		}
	},
	beforeDestroy() {
		this.closeConnect()
	}
}
