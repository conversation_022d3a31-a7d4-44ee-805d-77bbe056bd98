const uac = '/api'
import request from '@/web/libs/request'
const systemApi = {
	/**
	 * 获取终端网络信息
	 * @returns Promise
	 */
	getNetworkInfo() {
		return request({
			url: `${uac}/system/getNetworkInfo`,
			method: 'get'
		})
	},
	/**
	 * 保存终端网络信息
	 * @returns Promise
	 */
	setNetworkInfo(data) {
		return request({
			url: `${uac}/system/setNetworkInfo`,
			method: 'post',
			data
		})
	},
	/**
	 * 重启设备
	 * @returns Promise
	 */
	rebootSystem() {
		return request({
			url: `${uac}/system/rebootSystem`,
			method: 'get'
		})
	},
	closeSystem() {
		return request({
			url: `${uac}/system/closeSystem`,
			method: 'get'
		})
	},
	getAllDisplays() {
		return request({
			url: `${uac}/system/getAllDisplays`,
			method: 'get'
		})
	},
	pingIp(params) {
		return request({
			url: `${uac}/system/pingIp`,
			method: 'get',
			params
		})
	},
	handlePanel(params) {
		return request({
			url: `${uac}/system/handlePanel`,
			method: 'get',
			params
		})
	},
	handleDisplayMode(params) {
		return request({
			url: `${uac}/system/handleDisplayMode`,
			method: 'get',
			params
		})
	},
	handleMouseCursorMode(params) {
		return request({
			url: `${uac}/system/handleMouseCursorMode`,
			method: 'get',
			params
		})
	},
	/**
	 * 获取终端和app 部分属性信息
	 * @param {String} props
	 * @returns Promise
	 */
	getSystemProperties(searchProps) {
		return request({
			url: `${uac}/system/getSystemProperties`,
			method: 'get',
			params: {
				searchProps
			}
		})
	}
}
export default systemApi
