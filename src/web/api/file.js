const uac = '/api'
import request from '@/web/libs/request'
const fileApi = {
	/**
	 * 下载日志
	 * @returns Promise
	 */
	getLogs() {
		return request({
			url: `${uac}/file/getLogs`,
			method: 'get',
			responseType: 'blob'
		})
	},
	/**
	 * 上传文件接口
	 * @param {object} data 文件流格式
	 */
	fileUploader(data, headers) {
		return request({
			url: `${uac}/upload`,
			method: 'post',
			headers: { 'Content-Type': 'multipart/form-data', ...headers },
			data
		})
	},
	/**
	 * 拷贝授权文件
	 * @param {object} data
	 * @param {String} data.fileName
	 */
	copyAuthFile(data) {
		return request({
			url: `${uac}/file/copyAuthFile`,
			method: 'post',
			data
		})
	},
	/**
	 * 拷贝配置文件
	 * @param {object} data
	 * @param {String} data.fileName
	 */
	copyConfigFile(data) {
		return request({
			url: `${uac}/file/copyConfigFile`,
			method: 'post',
			data
		})
	},
	/**
	 * 拷贝数据库文件
	 * @param {object} data
	 * @param {String} data.fileName
	 */
	copyDbFile(data) {
		return request({
			url: `${uac}/file/copyDbFile`,
			method: 'post',
			data
		})
	}
}
export default fileApi
