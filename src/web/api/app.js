import request from '@/web/libs/request'
const uac = '/api'

const appApi = {
	/**
	 * 重启设备
	 * @returns Promise
	 */
	rebootApp() {
		return request({
			url: `${uac}/app/rebootApp`,
			method: 'get'
		})
	},
	/**
	 * 卸载app
	 * @param {object} data
	 * @param {string} data.appName
	 * @returns Promise
	 */
	uninstallApp(data) {
		return request({
			url: `${uac}/app/uninstallApp`,
			method: 'post',
			data
		})
	},

	/**
	 * 获取配置信息
	 * @returns Promise
	 */
	getTerminalConfigInfo() {
		return request({
			url: `${uac}/app/getTerminalConfigInfo`,
			method: 'get'
		})
	},
	/**
	 * 保存配置信息
	 * @returns Promise
	 */
	setTerminalConfigInfo(data) {
		return request({
			url: `${uac}/app/setTerminalConfigInfo`,
			method: 'post',
			data,
			headers: {
				'Content-Type': 'application/json'
			}
		})
	},
	closeApp() {
		return request({
			url: `${uac}/app/closeApp`,
			method: 'get'
		})
	},
	/**
	 * 安装文件
	 * @returns Promise
	 */
	installPackage(data) {
		return request({
			url: `${uac}/app/installPackage`,
			method: 'post',
			data
		})
	},
	/**
	 * 导出授权设备信息
	 */
	exportDevicesInfo() {
		return request({
			url: `${uac}/app/exportDevicesInfo`,
			method: 'get',
			responseType: 'blob'
		})
	},
	/**
	 * 获取已经上传的硬件授权文件名称
	 * @returns Promise
	 */
	getDeviceAuthInfo() {
		return request({
			url: `${uac}/app/getDeviceAuthInfo`,
			method: 'get'
		})
	},
	/**
	 * 导出配置文件
	 */
	exportConfigFile() {
		return request({
			url: `${uac}/app/exportConfigFile`,
			method: 'get',
			responseType: 'blob'
		})
	},
	/**
	 * 导出数据库文件
	 */
	exportDbFile() {
		return request({
			url: `${uac}/app/exportDbFile`,
			method: 'get',
			responseType: 'blob'
		})
	},
	// 获取所有柜子(大类) // 根据配置信息的参数自动生成的
	getSidesAllList: () => {
		return request({
			url: `${uac}/business/getSidesAllList`,
			method: 'get'
		})
	},
	// 获取所有使用中的柜格
	getIscdsUserList: () => {
		return request({
			url: `${uac}/business/getIscdsUserList`,
			method: 'get'
		})
	},
	// 获取开柜记录
	getAccessRecords: (params) => {
		return request({
			url: `${uac}/business/getAccessRecords`,
			method: 'get',
			params
		})
	}
}
export default appApi
