import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/web/pages/layouts/index'

Vue.use(VueRouter)

const router = new VueRouter({
	base: '',
	mode: 'hash',
	scrollBehavior: () => ({
		y: 0
	}),
	routes: [
		{
			path: '*',
			redirect: '/operation'
		},
		{
			path: '/operation',
			name: 'operation',
			component: Layout,
			redirect: '/operation/appConfig',
			children: [
				// 应用配置
				{
					path: 'appConfig',
					name: 'AppConfig',
					component: () => import('@/web/pages/config/app')
				},
				// 终端配置
				{
					path: 'terminalConfig',
					name: 'TerminalConfig',
					component: () => import('@/web/pages/config/terminal')
				},
				// 关于
				{
					path: 'about',
					name: 'about',
					component: () => import('@/web/pages/about/index')
				}
			]
		}
	]
})

router.beforeEach((to, from, next) => {
	next()
})

router.afterEach((to) => {
	window.scrollTo(0, 0)
})

export default router
