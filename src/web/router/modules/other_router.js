import store from '@/web/store'
import { roles, itemNumberDic } from '@/config'

const routers = [
	// 外设管理 - 人脸摄像头
	{
		path: 'external-device-manage/camera',
		name: 'Camera',
		component: () => import('@/web/pages/external_device_manage/camera/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
				itemNumberDic.sswpg_itemNumber_1
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '人脸摄像头',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user]
		}
	},
	// 外设管理 - 补光灯
	{
		path: 'external-device-manage/light',
		name: 'Light',
		component: () => import('@/web/pages/external_device_manage/light/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
				itemNumberDic.sswpg_itemNumber_1
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '补光灯',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 外设管理 - 证件读卡器
	{
		path: 'external-device-manage/id-card',
		name: 'IdCard',
		component: () => import('@/web/pages/external_device_manage/id_card/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '证件读卡器',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 外设管理 - 手环读卡器
	{
		path: 'external-device-manage/wristband',
		name: 'Wristband',
		component: () => import('@/web/pages/external_device_manage/wristband/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '手环读卡器',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 外设管理 - 打印机
	{
		path: 'external-device-manage/printer',
		name: 'Printer',
		component: () => import('@/web/pages/external_device_manage/printer/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '打印机',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 外设管理 - 锁控
	{
		path: 'external-device-manage/lock',
		name: 'Lock',
		component: () => import('@/web/pages/external_device_manage/lock/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
				itemNumberDic.sswpg_itemNumber_1
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '锁控',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 外设管理 - 二维码读卡器
	{
		path: 'external-device-manage/qr-code',
		name: 'QrCode',
		component: () => import('@/web/pages/external_device_manage/qr_code/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '二维码读卡器',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 外设管理 - 多频读卡器
	{
		path: 'external-device-manage/frequency-card-reader',
		name: 'FrequencyCardReader',
		component: () => import('@/web/pages/external_device_manage/frequency_card_reader/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '多频读卡器',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 外设管理 - rfid
	{
		path: 'external-device-manage/rfid',
		name: 'Rfid',
		component: () => import('@/web/pages/external_device_manage/rfid/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: 'RFID',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 外设管理 - 手环抽屉柜
	{
		path: 'external-device-manage/wristband-drawer',
		name: 'WristbandDrawer',
		component: () => import('@/web/pages/external_device_manage/wristband_drawer/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '手环抽屉柜',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 外设管理 - 签名捺印
	{
		path: 'external-device-manage/sign-finger',
		name: 'SignFinger',
		component: () => import('@/web/pages/external_device_manage/sign_finger/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
				itemNumberDic.sswpg_itemNumber_1
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '签名捺印',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 外设管理 - 高拍仪
	{
		path: 'external-device-manage/high-speed-scanner',
		name: 'HighSpeedScanner',
		component: () => import('@/web/pages/external_device_manage/high_speed_scanner/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '高拍仪',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user]
		}
	},
	// 外设管理 - 接触式体征检测
	{
		path: 'external-device-manage/contact-vital-signs',
		name: 'ContactVitalSigns',
		component: () => import('@/web/pages/external_device_manage/contact_vital_signs/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '接触式体征检测',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 外设管理 - 非接触式体征检测
	{
		path: 'external-device-manage/non-contact-vital-signs',
		name: 'NonContactVitalSigns',
		component: () => import('@/web/pages/external_device_manage/non_contact_vital_signs/index'),
		meta: {
			type: [
				// 外设和料号绑定关系
			],
			isMenu: true,
			isChild: true,
			parentTitle: '外设管理',
			title: '非接触式体征检测',
			icon: 'sprite-menu-leftbar_device',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 业务配置
	{
		path: 'business-config',
		name: 'BusinessConfig',
		component: () => import('@/web/pages/business_config/index'),
		meta: {
			type: 'common', // 料号无关，通用配置
			isMenu: true,
			isChild: false,
			title: '业务配置',
			icon: 'sprite-menu-leftbar_config',
			role: [roles.sysAdmin, roles.admin, roles.user]
		}
	}
]

// 根据料号筛选路由
function filterTerminalRoutes(itemNumber) {
	const routesList = []
	if (!itemNumber) {
		return routesList
	}
	const accessUser = store.getters['user/userInfo']
	for (let i = 0; i < routers.length; i++) {
		const item = routers[i]
		const meta = item.meta || {}
		if (!meta.type) {
			continue
		}
		if (meta.type === 'common' || meta.type.includes(itemNumber)) {
			if (meta.role && meta.role.includes(Number(accessUser.role))) {
				routesList.push(item)
			}
		}
	}
	return routesList
}

export default filterTerminalRoutes
