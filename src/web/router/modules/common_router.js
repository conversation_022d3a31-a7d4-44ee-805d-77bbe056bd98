import { roles } from '@/config'

const CommonRouters = [
	// 系统信息模块 - 基础信息
	{
		path: 'system-info/basics-info',
		name: 'BasicsInfo',
		component: () => import('@/web/pages/system_info/basics_info/index'),
		meta: {
			isMenu: true,
			isChild: true,
			parentTitle: '系统信息',
			title: '基础信息',
			icon: 'sprite-menu-leftbar_info',
			role: [roles.sysAdmin, roles.admin, roles.user, roles.test]
		}
	},
	// 系统信息模块 - 用户管理
	{
		path: 'system-info/user-manage',
		name: 'UserManage',
		component: () => import('@/web/pages/system_info/user_manage/index'),
		meta: {
			isMenu: true,
			isChild: true,
			parentTitle: '系统信息',
			title: '用户管理',
			icon: 'sprite-menu-leftbar_info',
			role: [roles.sysAdmin, roles.admin]
		}
	},
	// 系统维护 - 系统工具
	{
		path: 'system-tools',
		name: 'SystemTools',
		component: () => import('@/web/pages/system_maintenance/system_tools/index'),
		meta: {
			isMenu: true,
			isChild: true,
			parentTitle: '系统维护',
			title: '系统工具',
			icon: 'sprite-menu-leftbar_maintain',
			role: [roles.sysAdmin, roles.admin, roles.user]
		}
	},
	// 系统维护 - 系统属性
	{
		path: 'system-props',
		name: 'SystemProps',
		component: () => import('@/web/pages/system_maintenance/system_props/index'),
		meta: {
			isMenu: true,
			isChild: true,
			parentTitle: '系统维护',
			title: '系统属性',
			icon: 'sprite-menu-leftbar_maintain',
			role: [roles.sysAdmin, roles.admin, roles.user]
		}
	},
	// 系统维护 - 系统授权
	{
		path: 'system-auth',
		name: 'SystemAuth',
		component: () => import('@/web/pages/system_maintenance/system_auth/index'),
		meta: {
			isMenu: true,
			isChild: true,
			parentTitle: '系统维护',
			title: '系统授权',
			icon: 'sprite-menu-leftbar_maintain',
			role: [roles.sysAdmin, roles.admin]
		}
	},
	// 系统维护 - 远程开关柜
	{
		path: 'remote-cabinet-opening',
		name: 'RemoteCabinetOpening',
		component: () => import('@/web/pages/system_maintenance/remote_cabinet_opening/index'),
		meta: {
			isMenu: true,
			isChild: true,
			parentTitle: '系统维护',
			title: '远程开关柜',
			icon: 'sprite-menu-leftbar_config',
			role: [roles.sysAdmin, roles.admin]
		}
	},
	// 系统维护 - 开关柜日志
	{
		path: 'switch-log',
		name: 'SwitchLog',
		component: () => import('@/web/pages/system_maintenance/switch_log/index'),
		meta: {
			isMenu: true,
			isChild: true,
			parentTitle: '系统维护',
			title: '开关柜日志',
			icon: 'sprite-menu-leftbar_config',
			role: [roles.sysAdmin, roles.admin]
		}
	},
	// 网络配置 - 基本配置
	{
		path: 'network-config/basic-config',
		name: 'NetworkConfig',
		component: () => import('@/web/pages/network_config/basic_config/index'),
		meta: {
			isMenu: true,
			isChild: true,
			parentTitle: '网络配置',
			title: '基本配置',
			icon: 'sprite-menu-leftbar_network',
			role: [roles.sysAdmin, roles.admin]
		}
	}
	// 网络配置 - 高级配置
	// {
	// 	path: 'network-config/advanced-config',
	// 	name: 'NetworkConfig',
	// 	component: () => import('@/web/pages/network_config/advanced_config/index'),
	// 	meta: {
	// 		isMenu: true,
	// 		isChild: true,
	// 		parentTitle: '网络配置',
	// 		title: '高级配置',
	// 		icon: 'sprite-menu-leftbar_network',
	// 		role: [roles.sysAdmin, roles.admin]
	// 	}
	// }
	// 后台管理
	// {
	// 	path: 'app_info/remote_upgrade',
	// 	name: 'RemoteUpgrade',
	// 	component: () => import('@/web/pages/app_info/remote_upgrade/index'),
	// 	meta: {
	// 		isMenu: true,
	// 		isChild: true,
	// 		parentTitle: '后台管理',
	// 		title: '工具',
	// 		icon: 'sprite-menu-leftbar_config',
	// 		role: [roles.sysAdmin, roles.admin]
	// 	}
	// },
]

export default CommonRouters
