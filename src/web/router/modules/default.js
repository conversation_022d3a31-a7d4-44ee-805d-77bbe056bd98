import Layouts from '@/web/layouts/index'
import CommonRouters from './common_router'

const routes = [
	{
		path: '*',
		redirect: '/login',
		meta: {
			isMenu: false
		}
	},
	{
		path: '/login',
		name: 'Login',
		component: () => import('@/web/pages/login/index'),
		meta: {
			isMenu: false
		}
	},
	{
		path: '/operation',
		name: 'Operation',
		component: Layouts,
		redirect: '/operation/system-info/basics-info',
		children: CommonRouters
	}
]

export default routes
