import Vue from 'vue'
import VueRouter from 'vue-router'
import { publicPath, routerMode } from '@/config'
import routes from './modules/default'
import store from '@/web/store'
import { loginRouterPath, routerPrefixPath, routerPrefixName } from '@/config/index'
Vue.use(VueRouter)

const originPush = VueRouter.prototype.push
VueRouter.prototype.push = function (location, resolve, reject) {
	if (resolve && reject) {
		originPush.call(this, location, resolve, reject)
	} else {
		originPush.call(
			this,
			location,
			() => {},
			() => {}
		)
	}
}

const router = new VueRouter({
	base: publicPath,
	mode: routerMode,
	scrollBehavior: () => ({
		y: 0
	}),
	routes
})

router.beforeEach(async (to, from, next) => {
	const hasToken = store.getters['user/token']
	const loginRoute = loginRouterPath
	if (!hasToken) {
		if (to.path !== loginRoute) {
			return next(`${loginRoute}?redirect=${to.path}`)
		} else {
			return next()
		}
	}
	const accessUser = store.getters['user/userInfo']
	if (!accessUser.id) {
		return store.dispatch('user/removeToken')
	}
	if (to.path === loginRoute) {
		return next(routerPrefixPath)
	}
	// 判断是否已经设置了菜单
	const isSetMenu = store.getters['menu/isSetMenu']
	if (isSetMenu) {
		return next()
	}
	const newRoutes = await store.dispatch('menu/getItemNumberToShowMenu')
	newRoutes.forEach((item) => {
		router.addRoute(routerPrefixName, item)
	})
	next({ ...to, replace: true })
})

router.afterEach((to) => {
	window.scrollTo(0, 0)
})

export default router
