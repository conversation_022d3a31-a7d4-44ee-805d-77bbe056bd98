import axios from 'axios'
import Vue from 'vue'
import qs from 'qs'
import { successCode, contentType, requestTimeout, baseURL, invalidCode, noPermissionCode } from '@/config'
import store from '@/web/store'

/**
 * <AUTHOR> @description 处理code异常
 * @param {Number} code
 * @param {String} msg
 */
const handleCode = (code, msg) => {
	switch (code) {
		case invalidCode:
		case noPermissionCode:
			Vue.prototype.$baseTip({ message: msg || `接口${code}异常`, type: 'error' })
			const token = store.getters['user/token'] || ''
			if (token) {
				store
					.dispatch('user/removeToken')
					.then(() => {
						location.reload()
					})
					.catch(() => {})
			}
			break
		default:
			Vue.prototype.$baseTip({ message: msg || `接口${code}异常`, type: 'error' })
			break
	}
}

const instance = axios.create({
	baseURL,
	timeout: requestTimeout,
	headers: {
		'Content-Type': contentType
	}
})

instance.interceptors.request.use(
	(config) => {
		const token = store.getters['user/token'] || ''
		config.headers['requestId'] = new Date().getTime()
		config.headers['Authorization'] = token
		if (config.data && config.headers['Content-Type'] === 'application/x-www-form-urlencoded;charset=UTF-8') config.data = qs.stringify(config.data)
		return config
	},
	(error) => {
		return Promise.reject(error)
	}
)

instance.interceptors.response.use(
	(response) => {
		const { data, headers } = response
		const { code, msg } = data
		if (!(successCode.includes(code) || (data && !code))) {
			handleCode(code, msg)
		}
		const contentType = headers['content-type']
		const isStream = contentType == 'application/octet-stream'
		if (isStream) {
			// 获取文件在服务器的路径
			const contentDisposition = headers['content-disposition']
			const filePath = contentDisposition.match(/filename=([^;]+)/)[1]
			data.filePath = filePath
		}
		return data
	},
	(error) => {
		const { response, message } = error
		if (error.response && error.response.data) {
			const { status, data } = response
			handleCode(status, data.msg || message)
		} else {
			let { message } = error
			if (message === 'Network Error') {
				message = '接口连接异常'
			}
			if (message.includes('timeout')) {
				message = '接口请求超时'
			}
			if (message.includes('Request failed with status code')) {
				const code = message.substr(message.length - 3)
				message = `接口${code}异常`
			}
			Vue.prototype.$baseTip({ message: message || `接口未知异常`, type: 'error' })
		}
		return Promise.reject(error)
	}
)

export default instance
