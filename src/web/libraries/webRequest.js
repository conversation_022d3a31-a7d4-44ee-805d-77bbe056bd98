import { GxxMessage } from 'gosuncn-ui'
import axios from 'axios'
// 操作正常Code数组
const successCode = [200, 0]

/**
 * <AUTHOR> @description 处理code异常
 * @param {*} code
 * @param {*} msg
 */
const handleCode = (code, msg) => {
	switch (code) {
		default:
			GxxMessage({ message: msg || `后端接口${code}异常`, type: 'error' })
			break
	}
}
const instance = axios.create({
	baseURL: '',
	timeout: 30000,
	headers: {
		'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
	}
})
instance.interceptors.request.use(
	(config) => {
		config.headers['requestId'] = new Date().getTime()
		return config
	},
	(error) => {
		return Promise.reject(error)
	}
)

instance.interceptors.response.use(
	(response) => {
		const { data } = response
		let { code, msg } = data
		if (!(successCode.includes(code) || (data && !code))) {
			handleCode(code, msg)
		}
		return data
	},
	(error) => {
		const { response, message } = error
		if (error.response && error.response.data) {
			const { status, data } = response
			handleCode(status, data.msg || message)
		} else {
			let { message } = error
			if (message === 'Network Error') {
				message = '终端接口连接异常'
			}
			if (message.includes('timeout')) {
				message = '终端接口请求超时'
			}
			if (message.includes('Request failed with status code')) {
				const code = message.substr(message.length - 3)
				message = '终端接口' + code + '异常'
			}
			GxxMessage({ message: message || `终端接口未知异常`, type: 'error' })
		}
		return Promise.reject(error)
	}
)

export default instance
