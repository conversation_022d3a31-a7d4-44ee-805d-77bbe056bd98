/**
 * @description 导出默认通用配置
 */
const setting = {
	// 开发以及部署时的URL
	publicPath: '',
	// 生产环境构建文件的目录名
	outputDir: 'dist',
	// 放置生成的静态资源 (js、css、img、fonts) 的 (相对于 outputDir 的) 目录。
	assetsDir: 'public',
	// 路由模式，可选值为 history 或 hash
	routerMode: 'hash',
	lintOnSave: true,
	aesSecretKey: 'SUNDUN01SUNDUN01',
	aesSecretIv: 'ABCDEF0123456789',
	tokenName: 'access_token',
	userInfo: 'userInfo',
	uploadUrl: '/api/file/upload',
	roles: {
		sysAdmin: 1, // 超级管理员
		admin: 2, // 管理员
		user: 3 // 用户
	},
	// 路由前缀名称
	routerPrefixName: 'Operation',
	// 路由前缀路径
	routerPrefixPath: '/operation',
	// 登录路由路径
	loginRouterPath: '/login',
	// 首页路由路径
	homeRouterPath: 'system-info/basics-info',
	// 业务配置路由名称
	businessConfigRouteName: 'BusinessConfig',
	// 料号配置
	itemNumberDic: {
		csh_itemNumber_1: '2030-00559',
		csh_itemNumber_2: '2030-00560',
		csh_itemNumber_3: '2030-00451',
		csh_itemNumber_4: '2030-00450',
		csh_itemNumber_5: '2030-00290',
		csh_itemNumber_6: '2030-00325',
		csh_itemNumber_7: '2030-00324',
		csh_itemNumber_8: '8010-00011',
		ims_itemNumber_1: '8010-00107',
		ims_itemNumber_2: '8010-00074',
		ims_sst_itemNumber_1: '2030-00486',
		supcon_itemNumber_1: '2030-00599',
		sswpg_itemNumber_1: '8010-00203'
	},
	systemPlatformStr: {
		linux: 'linux',
		win: 'win32'
	},
	systemArchStr: {
		x64: 'x64',
		arm64: 'arm64',
		ia32: 'ia32'
	}
}
module.exports = setting
