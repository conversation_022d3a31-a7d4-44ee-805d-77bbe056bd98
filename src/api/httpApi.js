const moduleFiles = require.context('./modules', false, /\.js$/)
const systemConfig = JSON.parse(localStorage.getItem('serverConfig') || '{}')
const backVersion = systemConfig.serverVersion ? 'v' + systemConfig.serverVersion : 'v5'

const apiModules = moduleFiles.keys().reduce((modules, path) => {
	const moduleName = path.replace(/^\.\/(.*)\.\w+$/, '$1')
	const module = moduleFiles(path).default
	modules = {
		...modules,
		...{
			[moduleName]: fillApiModuleObj(module)
		}
	}
	return modules
}, {})

function fillApiModuleObj(module) {
	const { urlMap, apiMethodsMap } = module
	try {
		const apiModule = {}
		Object.keys(apiMethodsMap).forEach((methodName) => {
			try {
				const apiUrl = urlMap[methodName][backVersion]
				apiModule[methodName] = apiMethodsMap[methodName](apiUrl)
			} catch (e) {
				console.error(`封装api调用方法${methodName}失败`, e)
			}
		})
		return apiModule
	} catch (e) {
		console.error('组装api模块对象失败', e)
	}
}
export default apiModules
