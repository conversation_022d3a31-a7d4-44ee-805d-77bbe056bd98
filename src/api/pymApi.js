const pym = '/pym-sys'
const bus = '/pym-bus'

export default {
    getGlobalConfig: pym + '/cmd/terminal/queryCabinetConfByCabinetCode',
    // 1.首页库存情况（首页数量情况）
    getStatisticsInfo: pym + '/cmd/terminal/getStatisticsInfo',
    // 2.物品柜终端获取副柜信息(总量)
    GET_CABINETSIDE: pym + '/cmd/terminal/getCabinetSide',
    // 3.通过柜号获取内部物品列表（总量）
    getWpxxDetail: pym + '/cmd/terminal/getWpxxDetail',
    // 账号和密码登录
    getAdminInfo: pym + '/cmd/terminal/getAdminInfo',
    // 获取物品存放柜号
    getAssignCabinet: pym + '/cmd/terminal/getAssignCabinet',
    // 获取物品柜待取出列表
    getSelectWpxxList: pym + '/cmd/terminal/getSelectWpxxList',
    // 获取副柜信息地址
    GET_SIDEDETAIL: pym + '/cmd/terminal/getSideDetail',
    // 获取柜子故障信息地址
    getCabinetFault: pym + '/cmd/terminal/getCabinetFault',
    // 保存存入信息
    saveDepositWpxx: pym + '/cmd/terminal/saveDepositWpxx',
    // 保存取出信息
    saveTakeOutWpxx: pym + '/cmd/terminal/saveTakeOutWpxx',
    // 保存物品柜故障信息
    saveCabinetFault: pym + '/cmd/terminal/saveCabinetFault',
    // 获取柜子sideId
    getSideByCabinetId: pym + '/pym/cabinet/getSideByCabinetId',
    // 通过柜子编码获取柜子信息
    getCabinetByCabinetCode: pym + '/pym/cabinet/getCabinetByCabinetCode',
    // 获取柜子结构信息
    getCabinetDetail: pym + '/pym/cabinet/getCabinetDetail',
    // 获取暂时入库物品列表
    get_wpxx_page_data: bus + '/bus/wpxx/getWpxxPageData',
    // 暂存入库
    save_wpzc_batch: bus + '/bus/wpzc/saveWpzcBatch',
    // 取件码获取物品信息
    get_zcwp_by_take_code: bus + '/bus/wpzc/getZcwpByTakeCode',
    // 暂存出库
    save_wpzcCk: bus + '/bus/wpzc/saveWpzcCk',
    // 根据wpid获取物品信息
    get_wpxx_by_id: bus + '/bus/wpxx/getWpxxById',
    // 根据rfidCode获取物品信息
    getOperGeneralList: bus + '/bus/wpxx/getOperGeneralList',
    // 物管中心-获取静态资源请求地址
    sys_static_res_url: pym + '/static/res/'
}
