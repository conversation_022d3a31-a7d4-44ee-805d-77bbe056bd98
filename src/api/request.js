import axios from 'axios'
import qs from 'qs'
import { getToken, getNewToken, dealOn401 } from '@/libs/utils'
import { logger } from '@/libs/log'

function getTokenEvent() {
    const token = getNewToken()
    if (token && token !== 'undefined') {
        return token
    } else {
        return getToken()
    }
}
/**
 * <AUTHOR> @description 处理code异常
 * @param {*} code
 * @param {*} msg
 */
const handleCode = (code, msg) => {
    switch (code) {
        case 402:
            dealOn401()
            break
        case 401:
            dealOn401()
            break
        default:
            dealOn401('服务异常，请联系管理员，谢谢！')
            break
    }
}
const instance = axios.create({
    // baseURL,
    timeout: 30000,
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
})
instance.interceptors.request.use(
    (config) => {
        const token = getTokenEvent()
        if (token && config.method == 'post' && config.data) {
            config.data['access_token'] = token
        }
        if (token && config.method == 'get' && config.url.indexOf('.js') == -1) {
            if (!config.params) config.params = {}
            config.params['access_token'] = token
        }

        // 这里会过滤所有为空、0、false的key，如果不需要请自行注释
        // if (config.data) config.data = Vue.prototype.$baseLodash.pickBy(config.data, Vue.prototype.$baseLodash.identity)
        if (config.data && config.headers['Content-Type'] === 'application/x-www-form-urlencoded;charset=UTF-8') config.data = qs.stringify(config.data)
        // if (loadingInstance) loadingInstance.close()
        // if (config.debounce) loadingInstance = Vue.prototype.$loading()
        logger(config.url + '接口请求参数：', config.data)
        return config
    },
    (error) => {
        return Promise.reject(error)
    }
)
instance.interceptors.response.use(
    (response) => {
        // if (loadingInstance) loadingInstance.close()

        const { data, config } = response
        let { code, msg, retcode } = data
		code = code || retcode
        // 操作正常Code数组
        const codeVerificationArray = [200, 0]

        // 是否操作正常

        if (config.url === '/bsp-uac/oauth/token' && code === 400) {
            return data
        }
        if (!(codeVerificationArray.includes(code) || (data && !code))) {
            handleCode(code, msg)
        }
        return data
    },
    (error) => {
        // if (loadingInstance) loadingInstance.close()
        const { response, message } = error
        if (error.response && error.response.data) {
            const { status, data } = response
            handleCode(status, data.msg || message)
        } else {
            let { message } = error
            if (message === 'Network Error') {
                message = '后端接口连接异常'
            }
            if (message.includes('timeout')) {
                message = '后端接口请求超时'
            }
            if (message.includes('Request failed with status code')) {
                const code = message.substr(message.length - 3)
                message = '后端接口' + code + '异常'
            }
            dealOn401('网络或服务异常，请联系管理员，谢谢！')
            // Vue.prototype.$baseTip(message || `后端接口未知异常`, 'error')
        }
        logger('接口响应异常：', error)
        return Promise.reject(error)
    }
)

export default instance
