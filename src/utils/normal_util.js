/**
 * 比较版本号
 * @param {String} preVersion 当前版本号
 * @param {String} crrentVersion 远程版本号
 * @returns {Number}  大于0 升级
 */
export const versionCompare = (preVersion, lastVersion) => {
	console.log('当前版本', preVersion, '远程版本', lastVersion)
	const regexp = /^((\d\.+){3,})+((\d{4}\.\d{2}\.\d{2}\.\d{4})$)/
	if (!regexp.test(preVersion) || !regexp.test(lastVersion)) {
		console.log('版本号格式不正确')
		return false
	}
	const lenDiff = lastVersion.replace(/\./g, '').length - preVersion.replace(/\./g, '').length
	if (lenDiff != 0) {
		if (lenDiff > 0) {
			let preVersionArr = preVersion.split('.')
			for (let i = 0; i < lenDiff; i++) {
				preVersionArr.splice(i + 3, 0, '0')
			}
			preVersion = preVersionArr.join('.')
		} else {
			let lastVersionArr = lastVersion.split('.')
			for (let i = 0; i < -lenDiff; i++) {
				lastVersionArr.splice(i + 3, 0, '0')
			}
			lastVersion = lastVersionArr.join('.')
		}
	}
	let prev = preVersion.replace(/\./g, '')
	let last = lastVersion.replace(/\./g, '')
	const maxLen = Math.max(prev.length, last.length)
	if (prev.length < maxLen) {
		prev += '0'
	}
	if (last.length < maxLen) {
		last += '0'
	}
	prev = Number(prev)
	last = Number(last)
	return last - prev
}

/**
 * 深度拷贝数据
 * @param {Object} obj
 */
export const deepClone = (obj) => {
	return new Promise((resolve, reject) => {
		const { port1, port2 } = new MessageChannel()
		port1.postMessage(obj)
		port2.onmessage = (msg) => {
			resolve(msg.data)
		}
	})
}

/**
 * 获取设备信息
 */
export const getMediaDevices = () => {
	return new Promise((resolve, reject) => {
		if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
			resolve([])
		} else {
			navigator.mediaDevices
				.enumerateDevices()
				.then((devices) => {
					const deviceList = devices.filter((item) => {
						return item.kind == 'videoinput'
					})
					resolve(deviceList)
				})
				.catch((err) => {
					console.log('获取摄像头设备失败，err:', err)
					resolve([])
				})
		}
	})
}
