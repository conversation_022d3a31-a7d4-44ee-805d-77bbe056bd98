// 补光灯
const code = {
    0: '连接正常',
    1: '连接异常',
    2: '发送指令正常',
    3: '发送指令失败',
    4: '关闭正常',
    5: '关闭失败'
}
class Light {
    constructor(url, callback) {
        this.url = url
        this.ws = null
        this.callback = callback || null
        this.init()
    }
    init() {
        if (!this.url) {
            this.callback &&
				this.callback('open', {
				    code: 1,
				    msg: code[1]
				})
            return
        }
        try {
            this.ws = new WebSocket(this.url)
            // 连接成功建立的回调方法
            this.ws.onopen = (e) => {
                this.callback &&
					this.callback('open', {
					    code: 0,
					    msg: code[0]
					})
            }
            // 连接发生错误的回调方法
            this.ws.onerror = () => {
                this.callback &&
					this.callback('error', {
					    code: 1,
					    msg: '补光灯通信服务连接失败'
					})
            }
            // 接收到消息的回调方法
            this.ws.onmessage = (e) => {
                try {
                    const { success, msg } = JSON.parse(e.data)
                    this.callback &&
						this.callback('message', {
						    code: success ? 2 : 3,
						    msg: msg || (success ? code[2] : code[3])
						})
                } catch (error) {
                    this.callback &&
						this.callback('message', {
						    code: 3,
						    msg: '数据解析异常'
						})
                }
            }
        } catch (error) {
            this.callback &&
				this.callback('open', {
				    code: 1,
				    msg: code[1]
				})
            this.ws = null
        }
    }
    send(actiontype) {
        if (this.ws == null || this.ws.readyState != 1) {
            this.ws && this.ws.close()
            this.ws = null
            this.callback &&
				this.callback('error', {
				    code: 1,
				    msg: code[1]
				})
        } else {
            if (['closelight', 'openlight'].includes(actiontype)) {
                this.ws.send(JSON.stringify({ actiontype }))
            } else {
                this.callback &&
					this.callback('error', {
					    code: 3,
					    msg: code[3]
					})
            }
        }
    }
    close() {
        if (this.ws != null && this.ws.readyState == 1) {
            this.ws.send(JSON.stringify({ actiontype: 'closelight' }))
            this.ws && this.ws.close()
            this.callback &&
				this.callback('open', {
				    code: 4,
				    msg: code[4]
				})
        } else {
            this.callback &&
				this.callback('open', {
				    code: 5,
				    msg: code[5]
				})
        }
        this.callback = null
        this.ws = null
    }
}

export default Light
