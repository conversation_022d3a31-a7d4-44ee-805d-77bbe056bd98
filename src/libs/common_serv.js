export default {
	/**
	 * 处理摄像人旋转
	 *
	 * @param {Object} videoStyle video样式，容器宽高
	 * @param {String} videoType 摄像头选择类型
	 * @param {String} canvasDom  canvas dom元素
	 * @param {String}  videoDom 摄像头流dom元素
	 */
	handleCameraCof(videoStyle, videoType, canvasDom, videoDom) {
		const canvas = document.getElementById(canvasDom)
		if (canvas === null) {
			return canvas
		}
		const context = canvas.getContext('2d')
		canvas.width = videoStyle.width
		canvas.height = videoStyle.height
		if (['Y', 'y'].includes(videoType.horizontal) && videoType.rotate) {
			context.scale(-1, 1)
			context.rotate((-videoType.rotate * Math.PI) / 180)
			context.drawImage(document.getElementById(videoDom), -videoStyle.height, -videoStyle.width, videoStyle.height, videoStyle.width)
		} else if (videoType.rotate) {
			context.translate(canvas.width * 0.5, canvas.height * 0.5)
			context.rotate((videoType.rotate * Math.PI) / 180)
			context.drawImage(document.getElementById(videoDom), -canvas.height / 2, -canvas.width / 2, canvas.height, canvas.width)
		} else if (['Y', 'y'].includes(videoType.horizontal)) {
			context.scale(-1, 1)
			context.drawImage(document.getElementById(videoDom), -videoStyle.width, 0, videoStyle.width, videoStyle.height)
		} else {
			context.drawImage(document.getElementById(videoDom), 0, 0, videoStyle.width, videoStyle.height)
		}
		return canvas
	},
	/**
	 * 语音播报方法
	 * @param {string}} text
	 */
	voicebroadcast(text) {
		const msg = new SpeechSynthesisUtterance(text)
		msg.volume = 1
		msg.rate = 1
		msg.pitch = 1.5
		window.speechSynthesis.speak(msg)
	}
}
