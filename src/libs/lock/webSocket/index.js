// webSocket open lock
const code = {
    0: '连接正常',
    1: '连接异常',
    2: '发送指令正常',
    3: '发送指令失败',
    4: '关闭正常'
}

class LockWebSocket {
    constructor(url, callback) {
        this.url = url
        this.ws = null
        this.callback = callback || null
        this.init()
    }
    init() {
        if (!this.url) {
            this.callback &&
				this.callback('open', {
				    code: 1,
				    msg: code[1]
				})
            return
        }
        try {
            this.ws = new WebSocket(this.url)
            // 连接成功建立的回调方法
            this.ws.onopen = (e) => {
                this.callback &&
					this.callback('open', {
					    code: 0,
					    msg: code[0]
					})
            }
            // 连接发生错误的回调方法
            this.ws.onerror = () => {
                this.callback &&
					this.callback('error', {
					    code: 1,
					    msg: code[1]
					})
            }
            // 接收到消息的回调方法
            this.ws.onmessage = (e) => {
                try {
                    this.callback &&
						this.callback('message', {
						    code: 2,
						    msg: code[2],
						    data: JSON.parse(e.data)
						})
                } catch (error) {
                    this.callback &&
						this.callback('message', {
						    code: 3,
						    msg: '数据解析异常',
						    data: {}
						})
                }
            }
            // 连接关闭的回调方法
            this.ws.onclose = () => {
                this.callback &&
					this.callback('close', {
					    code: 4,
					    msg: code[4]
					})
            }
        } catch (error) {
            this.callback &&
				this.callback('open', {
				    code: 1,
				    msg: code[1]
				})
            this.ws = null
        }
    }
    send(data) {
        if (this.ws == null || this.ws.readyState != 1) {
            this.ws && this.ws.close()
            this.ws = null
            this.callback &&
				this.callback('error', {
				    code: 1,
				    msg: code[1]
				})
        } else {
            const jsTicks = new Date().getTime()
            const text = {
                actionType: data.actionType,
                box: data.box,
                door: data.door,
                checkCode: '11',
                requestTime: jsTicks
            }
            this.ws.send(JSON.stringify(text))
        }
    }
    close() {
        this.ws && this.ws.close()
        this.ws = null
    }
}

export default LockWebSocket
