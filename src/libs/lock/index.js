const { EventEmitter } = require('events')
const ipcRenderer = window.electronAPI?.ipcRenderer || null
import LockWebSocket from './webSocket/index'
import Vue from 'vue'
import { logger } from '@/libs/log'
const { cabinetToBoxAndDoor } = require('./util')
let terminalConfig

const errorCode = {
	0: '配置错误',
	1: '参数异常'
}

class Lock extends EventEmitter {
	constructor() {
		super()
		this.linkObj = null
	}
	/**
	 * 初始化
	 * @returns
	 */
	init() {
		terminalConfig = Vue.prototype.$store.getters.getTerminalConfig
		if (!this.manufacturerValid() || !this.lockComValid() || !this.lockControlProtocolValid()) {
			return
		}
		switch (Number(terminalConfig.lockType)) {
			case 1:
				if (terminalConfig.lockControlProtocol == 1) {
					const regex = /^(ws:\/\/\d+\.\d+\.\d+\.\d+:)/
					const match = terminalConfig.lockCom.match(regex)
					if (match && match[1]) {
						this.linkObj = new LockWebSocket(terminalConfig.lockCom, (type, data) => {
							this.emit(type, this.handleData(data))
						})
					} else {
						logger(`lock: LockWebSocket 正则校验出错，当前配置为：${terminalConfig.lockCom}`)
						this.error()
					}
				} else if (terminalConfig.lockControlProtocol == 2) {
					this.serialportComm()
				}
				break
			case 2:
			case 3:
				this.serialportComm()
				break
			default:
				break
		}
	}
	/**
	 * 初始化异常
	 */
	error() {
		this.emit(
			'error',
			this.handleData({
				code: 0,
				msg: errorCode[0]
			})
		)
	}
	/**
	 * 串口通信
	 * @returns
	 */
	serialportComm() {
		if (!this.isNodeValid(false)) {
			this.linkObj = null
			return
		}
		ipcRenderer.send('handle-lock', { type: 'init', com: terminalConfig.lockCom })
		ipcRenderer.on('lock-callback-result', (event, arg) => {
			if (arg.type == 'open') {
				this.linkObj = arg.code == 0
			}
			this.emit(arg.type, this.handleData(arg))
		})
	}
	/**
	 * 处理数据
	 * @returns Object
	 */
	handleData(arg) {
		const newData = {
			code: arg.code,
			msg: arg.msg,
			result: '', // 状态
			box: '', // 锁板
			door: '', // 锁号
			actionType: ''
		}
		const info = arg.data
		if (info) {
			newData.result = info.result >= -1 ? parseInt(info.result) : ''
			newData.actionType = info.actiontype || ''
			newData.box = parseInt(info.box || 0)
			newData.door = parseInt(info.door || 1)
			if (newData.box || newData.box == 0) {
				// 处理不同起始锁板配置的锁板号
				newData.box = parseInt(newData.box) - parseInt(terminalConfig.lockPlateStartIndex) + 1
			}
		}
		return newData
	}
	/**
	 * 操作
	 * @param {String} actionType 操作类型 checklock openlock openlight closelight
	 * @param {String} cabinet 柜号 例如：A01 A01-1
	 * @returns
	 */
	handle(actionType, cabinet) {
		if (!this.linkValid() || !this.actionTypeValid(actionType) || !this.manufacturerValid() || !this.lockControlProtocolValid()) {
			return
		}
		const { box, door, success } = this.handleCabinet(cabinet)
		if (!success) {
			return
		}
		switch (Number(terminalConfig.lockType)) {
			case 1:
				if (terminalConfig.lockControlProtocol == 1) {
					this.linkObj.send({ actionType, box, door })
				} else if (terminalConfig.lockControlProtocol == 2) {
					if (this.isNodeValid() && ['checklock', 'openlock'].includes(actionType)) {
						// 开柜 or 检测柜子状态
						ipcRenderer.send('handle-lock', { type: actionType, lockPlate: box, lockNumber: door })
					}
				}
				break
			case 2:
			case 3:
				if (this.isNodeValid()) {
					// 开柜 or 检测柜子状态
					if (['checklock', 'openlock'].includes(actionType)) {
						ipcRenderer.send('handle-lock', { type: actionType, lockPlate: box, lockNumber: door })
					}
				}
				break
			default:
				break
		}
	}
	/**
	 * DC主柜 开启/关闭 补光灯
	 * @param {String} type open or close
	 */
	handleLight(type, lockPlate, lockNumber) {
		if (!this.linkValid() || !this.lockControlProtocolValid()) {
			return
		}
		if (terminalConfig.lockType == 3) {
			ipcRenderer.send('handle-lock', { type: 'setlight', actionType: type, lockPlate, lockNumber })
		}
	}
	/**
	 * 关闭通信
	 */
	commonClose() {
		ipcRenderer.send('handle-lock', { type: 'close' })
		ipcRenderer.removeAllListeners('lock-callback-result')
	}
	/**
	 * 销毁事件监听
	 */
	removeAllEventListeners() {
		this.removeAllListeners('open')
		this.removeAllListeners('close')
		this.removeAllListeners('message')
		this.removeAllListeners('error')
	}
	/**
	 * 销毁
	 * @returns
	 */
	destroyed() {
		this.removeAllEventListeners()
		if ((terminalConfig.lockType == 1 && terminalConfig.lockControlProtocol == 2) || [2, 3].includes(Number(terminalConfig.lockType))) {
			ipcRenderer.removeAllListeners('lock-callback-result')
		}
		if (!this.linkValid(false)) {
			this.linkObj = null
			return
		}
		switch (Number(terminalConfig.lockType)) {
			case 1:
				if (terminalConfig.lockControlProtocol == 1) {
					this.linkObj.close()
				} else if (terminalConfig.lockControlProtocol == 2) {
					this.commonClose()
				}
				break
			case 2:
			case 3:
				this.commonClose()
				break
			default:
				break
		}
		this.linkObj = null
	}
	/**
	 * 锁控协议校验
	 */
	lockControlProtocolValid() {
		if ([1, 2].includes(Number(terminalConfig.lockControlProtocol))) {
			return true
		}
		logger(`lock: 锁控协议校验出错，当前配置为：${terminalConfig.lightControlProtocol}`)
		this.error()
		return false
	}
	/**
	 * 锁控COM校验
	 * @returns Boolean
	 */
	lockComValid() {
		if (terminalConfig.lockCom) {
			return true
		}
		logger('lock: 锁控COM校验出错，当前未配置com')
		this.error()
		return false
	}
	/**
	 * 连接状态校验
	 * @param {Boolean} isReturn 是否回调
	 * @returns Boolean
	 */
	linkValid(isReturn = true) {
		if (!this.linkObj) {
			logger('lock: 连接状态校验出错，当前未连接状态')
			isReturn &&
				this.emit(
					'error',
					this.handleData({
						code: 1,
						msg: '连接异常'
					})
				)
			return false
		}
		return true
	}
	/**
	 * 柜号转换
	 * @param {String} cabinet 柜号 例如：A01 A01-1
	 * @returns Boolean
	 */
	handleCabinet(cabinet) {
		// 根据柜号cabinet 计算锁板lockPlate 和 锁号 lockNumber
		const index = cabinet.indexOf('-')
		if (index != -1) cabinet = cabinet.substring(0, index)
		const arr = cabinetToBoxAndDoor(cabinet)
		if (arr && arr.length == 2) {
			// 处理不同起始锁板配置的锁板号
			arr[0] = arr[0] + parseInt(terminalConfig.lockPlateStartIndex) - 1
			return { box: arr[0], door: arr[1], success: true }
		}
		return { box: '', door: '', success: false }
	}
	/**
	 * 操作类型校验
	 * @param {String} actionType 操作类型 checklock openlock setlight
	 * @returns Boolean
	 */
	actionTypeValid(actionType) {
		if (['checklock', 'openlock', 'setlight'].includes(actionType)) {
			return true
		}
		logger(`lock: 操作类型校验出错，当前配置为：${actionType}`)
		this.emit(
			'error',
			this.handleData({
				code: 1,
				msg: errorCode[1],
				data: {
					result: '',
					box: '',
					door: '',
					actionType
				}
			})
		)
		return false
	}
	/**
	 * 厂商配置校验
	 * @returns Boolean
	 */
	manufacturerValid() {
		if ([1, 2, 3].includes(Number(terminalConfig.lockType))) {
			return true
		}
		logger(`lock: 厂商配置校验出错，当前配置为：${terminalConfig.lockType}`)
		this.error()
		return false
	}
	/**
	 * 运行环境校验
	 * @param {Boolean} isReturn 是否回调
	 * @returns Boolean
	 */
	isNodeValid(isReturn = true) {
		if (ipcRenderer) {
			return true
		}
		logger('lock: 运行环境校验出错，当前配置为：非node环境')
		isReturn &&
			this.emit(
				'message',
				this.handleData({
					code: 0,
					msg: '非node环境'
				})
			)
		return false
	}
}

export default Lock
