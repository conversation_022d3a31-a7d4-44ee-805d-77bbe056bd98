import router from '@/router'
import { modalInfo } from '../modal'
import Vue from 'vue'

function getCabinetConfigList() {
    const cabinetConfig = JSON.parse(localStorage.getItem('serverConfig'))
    const config = cabinetConfig.cabinetConfig // 柜子配置规则
    return config.split(';')
}

function handleError(text) {
    modalInfo('返回首页', 'tipsIcon2', text, (val) => {
        if (val) {
            try {
                router.replace('/home')
            } catch (err) {
                Vue.prototype.logger('返回首页-异常', err)
                modalInfo('重试', 'tipsIcon2', '返回首页-异常', () => {
                    try {
                        router.push('/home')
                    } catch (e) {
                        Vue.prototype.logger('返回首页-异常', e)
                        dealError()
                    }
                })
            }
        }
    })
}
/**
 * 因测试偶现柜号返回首页失败问题，兼容错误
 * */
function dealError() {
    modalInfo('重启程序', 'tipsIcon2', '代码异常', (val) => {
        const ipcRenderer = window.electronAPI?.ipcRenderer
        ipcRenderer?.send('app-rebort')
    })
}

/**
 * 旧转换规则
 * @param {String} cabinet 锁号
 * @returns [box, door]
 */
export function cabinetToBoxAndDoor(cabinet) {
    const name = cabinet.slice(0, 1).toUpperCase()
    const num = parseInt(cabinet.slice(1, cabinet.length))
    const chartCode = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    const index = chartCode.indexOf(name)
    const box = getCabinetConfigList()
    function data(box, index) {
        let aaa = 0
        let bbb = ''
        box.forEach((item, i) => {
            if (i <= index) {
                aaa += 1
                if (item.indexOf(',') > -1) {
                    const arr = item.split(',')
                    if (i == index) {
                        if (num > Number(arr[0])) {
                            aaa += 1
                        }
                    } else {
                        aaa += 1
                    }
                }
            }
        })
        const item = box[index]
        if (item) {
            if (item.indexOf(',') == -1) {
                if (num > Number(item)) {
                    handleError('柜号不存在！')
                    return []
                } else {
                    bbb = num
                }
            } else {
                const arr = item.split(',')
                if (num > Number(arr[0])) {
                    if (num > Number(arr[0]) + Number(arr[1])) {
                        handleError('柜号不存在！')
                        return []
                    } else {
                        bbb = num - Number(arr[0])
                    }
                } else {
                    bbb = num
                }
            }
        } else {
            handleError('柜号不存在！')
            return []
        }
        return [aaa, bbb]
    }
    return data(box, index)
}

/**
 * 锁板和锁号 转 柜号
 * 根据配置文件与传入的box（列），door（行），拿到完整的柜号：例：A01
 * @param {Number} box 锁板
 * @param {Number} door 锁号
 * @returns cabinet
 */
export function getGHEvent(box, door) {
    return boxAndDoorToCabinet(box, door)
}

/**
 * 锁板和锁号 转 柜号
 * @param {Number} box 锁板
 * @param {Number} door 锁号
 * @returns cabinet
 */
function boxAndDoorToCabinet(box, door) {
    door = door + ''
    if (door.indexOf('0') == 0) {
        door = door.substring(1)
    }
    const boxArr = getCabinetConfigList()
    const chartCode = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    const newBoxArr = chartCode.splice(0, boxArr.length)
    const newBoxObjArr = []
    boxArr.forEach((item, index) => {
        let obj = {}
        if (newBoxObjArr.length > 0) {
            const cols = newBoxObjArr[index - 1].cols
            if (item.indexOf(',') !== -1) {
                obj = {
                    name: newBoxArr[index],
                    cols: [cols[cols.length > 1 ? 1 : 0] + 1, cols[cols.length > 1 ? 1 : 0] + 2],
                    count: item.split(',')[0]
                }
            } else {
                obj = {
                    name: newBoxArr[index],
                    cols: [cols[0] + 1]
                }
            }
        } else {
            if (item.indexOf(',') !== -1) {
                obj = {
                    name: newBoxArr[index],
                    cols: [index + 1, index + 2],
                    count: item.split(',')[0]
                }
            } else {
                obj = {
                    name: newBoxArr[index],
                    cols: [index + 1]
                }
            }
        }
        newBoxObjArr.push(obj)
    })
    let gh = ''
    newBoxObjArr.forEach((item) => {
        if (item.cols.indexOf(Number(box)) == 0) {
            gh = item.name + (Number(door) < 10 ? '0' + door : door)
        }
        if (item.cols.indexOf(Number(box)) == 1) {
            const newDoor = Number(door) + Number(item.count) < 10 ? '0' + (Number(door) + Number(item.count)) : Number(door) + Number(item.count)
            gh = item.name + newDoor
        }
    })
    return gh
}
