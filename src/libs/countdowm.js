export default class CountDown {
    constructor(count) {
        this.count = count
        this.timer = null
    }
    actionDown(fn, finishFn) {
        this.count = JSON.parse(localStorage.getItem('serverConfig') || '{}').loginOutTime || 120
        this.timer = setInterval(() => {
            this.count--
            if (this.count == 0) {
                clearInterval(this.timer)
                this.timer = null
                // 倒计时结束回调
                fn && fn(this.count)
                finishFn && finishFn()
            } else {
                fn && fn(this.count)
            }
        }, 1000)
    }
    clear() {
        this.timer && clearInterval(this.timer)
        this.timer = null
        console.log('_999', '清除了')
    }
    action(...arg) {
        this.clear()
        this.actionDown(...arg)
    }
}
