export default {
    parents(el, parentSelector) {
        if (parentSelector === undefined) {
            parentSelector = document
        }
        const parents = []
        let p = el.parentNode
        while (p && p !== parentSelector) {
            const o = p
            parents.push(o)
            p = o.parentNode
        }
        parents.push(parentSelector)
        return parents
    },
    querySelector(domArr, domFlag) {
        const ret = []
        if (!domArr || !domArr.length) {
            return ret
        }
        if (!domFlag || !domFlag.length) {
            return ret
        }
        const flag = domFlag.substr(0, 1)
        for (let i = 0; i < domArr.length; i++) {
            if (flag == '.') {
                const classNameArr = domArr[i].className && domArr[i].className.split(' ')
                if (classNameArr && classNameArr.length) {
                    classNameArr.forEach((element) => {
                        if (element == domFlag.substr(1, domFlag.length - 1)) {
                            ret.push(domArr[i])
                        }
                    })
                }
            }
            if (flag == '#' && domArr[i].id == domFlag.substr(1, domFlag.length - 1)) {
                ret.push(domArr[i])
            }
        }
        return ret
    },
    isExistImage(url) {
        return new Promise((resolve) => {
            let img = new Image()
            img.onload = function() {
                if (this.complete == true) {
                    resolve(true)
                    img = null
                }
            }
            img.onerror = function() {
                resolve(false)
                img = null
            }
            img.src = url
        })
    }
}
