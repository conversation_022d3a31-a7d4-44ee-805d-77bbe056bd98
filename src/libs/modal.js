import iView from 'view-design'
const serverConfig = JSON.parse(localStorage.getItem('serverConfig'))

export function modalInfo(okText, className, tipsText, callback) {
    var modalContent = `<div class="tipsBox"><i class="${className}"></i><p class="tipsText">${tipsText}</p></div>`
    iView.Modal.info({
        title: false,
        width: '540',
        content: modalContent,
        className: 'tipsWrap',
        transfer: false,
        okText: okText,
        onOk: () => {
            callback && callback(true)
        }
    })
    return callback
}

// 弹出框倒计时
export function modalConfirm(okText, cancelText, className, tipsText, callback, countDown) {
    const id = new Date().getTime()
    var modalContent = `<div class="tipsBox" id="${id}"><i class="${className}"></i><p class="tipsText">${tipsText}</p></div>`
    var timer = ''
    if (countDown) {
        timer && clearInterval(timer)
        let count = +serverConfig.loginOutTime
        setTimeout(() => {
            try {
                const modal = document.getElementById(id).parentNode.parentNode.parentNode
                const primaryBtn = modal.querySelector('.ivu-btn-primary')
                primaryBtn.style.width = 'auto'
                const primary = primaryBtn.querySelector('span')
                timer = setInterval(() => {
                    if (count > 1) {
                        count--
                        primary.innerHTML = `${okText}(${count}S)`
                    } else {
                        timer && clearInterval(timer)
                        callback && callback(true)
                        iView.Modal.remove()
                    }
                }, 1000)
            } catch (err) {
                console.log(err)
            }
        }, 2000)
    }
    iView.Modal.confirm({
        title: false,
        width: '540',
        content: modalContent,
        className: 'tipsWrap',
        okText: okText,
        cancelText: cancelText,
        transfer: false,
        onOk: () => {
            timer && clearInterval(timer)
            callback && callback(true)
        },
        onCancel: () => {
            timer && clearInterval(timer)
            callback && callback(false)
        }
    })
    return callback
}
