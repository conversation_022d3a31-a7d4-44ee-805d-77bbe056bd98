// 业务场景中，分柜门和柜锁
// 柜门：在物管中心中配置柜号，每个柜子从A-Z排序，柜门从数字排序
// 柜锁：在物管柜硬件中每个柜门连接一个锁口，每个柜锁上限锁口默认20个，不属于物管中心配置，每个柜锁都排满一个才到下一个

// 默认物管柜锁锁板上限柜门是配置里设置
// const code = 'B21'
// const config = '10,3;10,10;10,10;10,10'
import { modalInfo } from './modal'
import Router from 'vue-router'
const router = new Router({ mode: 'hash' })

const letter = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']

function format(config) {
    // 柜子列表
    const cabinets = config.split(';')
    // 每个柜子柜门数
    const cabinetsObj = {}
    cabinets.map((item, i) => {
        const cabinetNum = item.split(',').reduce((result, num) => Number(result) + Number(num))
        cabinetsObj[letter[i]] = cabinetNum
    })
    return cabinetsObj
}
export function filterGh(code) {
    const serverConfig = JSON.parse(localStorage.getItem('serverConfig'))
    const cabinetsMap = format(serverConfig.cabinetConfig)
    console.log('cabinetsMap', cabinetsMap)
    const letter = code.slice(0, 1)
    let num = Number(code.slice(1, 3) || '0')
    let count = 0
    if (!cabinetsMap[letter] || !num) {
        console.log('柜号不存在！')
        modalInfo('返回首页', 'tipsIcon2', '柜号不存在！', (val) => {
            if (val) {
                router.replace('/home')
                window.location.reload()
            }
        })
        return
    }
    if (cabinetsMap[letter] < num) {
        console.log('柜号不存在！')
        modalInfo('返回首页', 'tipsIcon2', '柜号不存在！', (val) => {
            if (val) {
                router.replace('/home')
                window.location.reload()
            }
        })
        return
    }

    try {
        const cabinetNumArr = serverConfig.cabinetConfig.replaceAll(';', ',').split(',')
        const LOCKNUM = cabinetNumArr.map((num) => Number(num))
        const limit = Math.max.apply(null, LOCKNUM)
        Object.keys(cabinetsMap).forEach((_letter) => {
            // 算出每个柜子占用几个柜格
            if (_letter == letter) {
                count += Math.ceil(num / limit)
                // 计算num柜门数超不超锁板插口数最大，超出锁板加1，取余数
                num = num % limit || num
                console.log(_letter, count, num)
                throw new Error()
            } else {
                const lockerNum = Math.ceil(cabinetsMap[_letter] / limit)
                count += lockerNum
            }
        })
    } catch (e) {
        return [count, num]
    }
}

export const filterLightCom = (lockPlateStartIndex) => {
    const serverConfig = JSON.parse(localStorage.getItem('serverConfig'))
    const ACabinet = serverConfig.cabinetConfig.split(';')[0]
    const fristCabinetNum = ACabinet.split(',')[0]
    const lightLeftCom = [Number(lockPlateStartIndex), Number(fristCabinetNum) + 1]
    const lightRightCom = [Number(lockPlateStartIndex), Number(fristCabinetNum) + 2]
    return [lightLeftCom, lightRightCom]
}
