const path = require('path')
const spritesmithTasks = require('./spritesmith.config.js') // 雪碧图插件config
const { dependencies, shortcutName, devWebPort } = require('./package.json')
const { backHttpServerPort } = require('./appdb/serverConfig.json')
const { outputDir, assetsDir, lintOnSave } = require('./src/config')
const whiteListedModules = ['vue']
const resolve = (dir) => path.join(__dirname, dir)

module.exports = {
	outputDir,
	publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
	assetsDir,
	crossorigin: '',
	filenameHashing: true,
	parallel: false,
	// eslint-loader 是否在保存的时候检查
	lintOnSave,
	// 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建
	productionSourceMap: false,
	pages: {
		client: {
			entry: 'src/client/main.js',
			template: 'public/client.html',
			filename: 'client.html',
			title: shortcutName,
			externals: [...Object.keys(dependencies || {}).filter((d) => !whiteListedModules.includes(d))],
			chunks: ['chunk-vendors', 'chunk-common', 'client']
		},
		web: {
			entry: 'src/web/main.js',
			template: 'public/web.html',
			filename: 'web.html',
			title: '',
			externals: [...Object.keys(dependencies || {}).filter((d) => !whiteListedModules.includes(d))],
			chunks: ['chunk-vendors', 'chunk-common', 'web']
		}
	},
	css: {
		// 非*.module.[ext]也可以视为css modules模块
		requireModuleExtension: true,
		// 是否为 CSS 开启 source map
		sourceMap: false,
		loaderOptions: {
			css: {
				// 这里的选项会传递给 css-loader
			},
			postcss: {
				// 这里的选项会传递给 postcss-loader
			}
		}
	},
	pluginOptions: {
		electronBuilder: {
			nodeIntegration: true
		}
	},
	chainWebpack: (config) => {
		config.externals({
			'./cptable': 'var cptable'
		})
		config.plugin('define').tap((args) => {
			args[0].SYSTEM_PLATFORM = JSON.stringify(process.platform)
			args[0].SYSTEM_ARCH = JSON.stringify(process.arch)
			return args
		})
	},
	configureWebpack: (config) => {
		config.resolve.alias['@'] = resolve('src')
		// config.output.libraryTarget = 'commonjs2'
		config.performance = {
			maxEntrypointSize: 10000000,
			maxAssetSize: 30000000
		}
		if (process.env.NODE_ENV === 'production') {
			// 为生产环境修改配置...
			config.mode = 'production'
			config.devtool = ''
			config.optimization.minimizer[0].options.terserOptions.compress.warnings = false
			config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true
			config.optimization.minimizer[0].options.terserOptions.compress.drop_debugger = true
			config.optimization.minimizer[0].options.terserOptions.compress.pure_funcs = ['console.log']
		} else {
			// 为开发环境修改配置...
			config.mode = 'development'
			config.devtool = 'source-map'
		}
		// 雪碧图相关
		config.plugins.push(...spritesmithTasks)
	},
	devServer: {
		host: '0.0.0.0',
		port: devWebPort,
		proxy: {
			'/smart': {
				target: 'http://*************:8700',
				changeOrigin: true,
				pathRewrite: {
					'^/smart': '/smart'
				}
			},
			'/api': {
				target: `http://127.0.0.1:${backHttpServerPort}`,
				changeOrigin: true,
				pathRewrite: {
					'^/api': '/api'
				}
			}
		}
	}
}
