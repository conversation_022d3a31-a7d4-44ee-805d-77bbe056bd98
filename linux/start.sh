#!/bin/sh

#安装路径
CURRENT_DIR=$(dirname `readlink -f $0`)

# 获取CPU架构
cpu_arch=$(uname -m)

# 获取当前的LD_LIBRARY_PATH值
CURRENT_LD_LIB_PATH=$LD_LIBRARY_PATH

# 定义要添加的新路径
NEW_LD_LIB_PATH=""

if [ "$cpu_arch" = "x86_64" ]; then
	NEW_LD_LIB_PATH="${CURRENT_DIR}/resources/extraResources/linux/x64/face_sdk:${CURRENT_DIR}/resources/extraResources/linux/x64/face_sdk/models/:${CURRENT_DIR}/resources/extraResources/linux/x64/hs_auth/:${CURRENT_DIR}/resources/extraResources/linux/x64/finger_sdk/"
elif [ "$cpu_arch" = "aarch64" ]; then
	NEW_LD_LIB_PATH="${CURRENT_DIR}/resources/extraResources/linux/arm64/face_sdk:${CURRENT_DIR}/resources/extraResources/linux/arm64/face_sdk/models/:${CURRENT_DIR}/resources/extraResources/linux/arm64/hs_auth/:${CURRENT_DIR}/resources/extraResources/linux/arm64/finger_sdk/"
fi

# 检查是否已经包含新路径, 避免重复添加, 导出LD_LIBRARY_PATH环境变量
if [ ":$CURRENT_LD_LIB_PATH:" != *":$NEW_LD_LIB_PATH:"* ]; then
	export LD_LIBRARY_PATH="$NEW_LD_LIB_PATH:$CURRENT_LD_LIB_PATH"
else
	export LD_LIBRARY_PATH="$CURRENT_LD_LIB_PATH"
fi

# 启动程序
${CURRENT_DIR}/$1 --no-sandbox
