#!/bin/bash

# 配置项 BEGIN ---------------------------------------------

# 版本号
VERSION=$1
# 应用名称
APP_NAME=$2
#deb打包文件路径
PROJ_MKPKG_DIR=../linux
# 指定安装目录
APP_INSTALL_DIR=/opt/gosuncn/$2
# 桌面快捷方式名称
DESKTOP_NAME=$4
# 是否开机自启
IS_LOGIN_OPEN=$5

# 免安装包目录
PROJ_ROOT=""
# 获取CPU架构
cpu_arch_type=$(uname -m)
# 目前支持的CPU架构：arm64、amd64、armv7l
CPU_ARCH=$3
# 当前目录
CURRENT_DIR=$(dirname `readlink -f $0`)
PROJ_RELEASE_DIR=${CURRENT_DIR}/release  #确保release目录结尾，防止误删系统目录
# 授权so文件路径
AUTH_FILE_PATH=""

if [ "$cpu_arch_type" == "x86_64" ]; then
	CPU_ARCH="amd64"
	PROJ_ROOT=../$3/linux-unpacked
	AUTH_FILE_PATH=/resources/extraResources/linux/x64/hs_auth/GSdmidecode
elif [ "$cpu_arch_type" = "aarch64" ]; then
	CPU_ARCH="arm64"
	PROJ_ROOT=../$3/linux-arm64-unpacked
	AUTH_FILE_PATH=/resources/extraResources/linux/arm64/hs_auth/GSdmidecode
elif [ "$cpu_arch_type" = "armv7l" ]; then
	CPU_ARCH="armhf"
	PROJ_ROOT=../$3/linux-armv7l-unpacked
else
	echo "Unsupported CPU architecture: $cpu_arch_type"
	exit 1
fi

# 配置项 END ---------------------------------------------

echo "指定包文件名" &&
APP_DEBIAN_NAME=${APP_NAME}_${CPU_ARCH}_${VERSION}.deb

echo "删除release目录" &&
if [ -d "${PROJ_RELEASE_DIR}/../release" ]; then
	rm -rf ${PROJ_RELEASE_DIR}/../release
fi &&

echo "重新创建release目录" &&
mkdir -p ${PROJ_RELEASE_DIR} &&
cp -rpdf ${PROJ_ROOT}/* ${PROJ_RELEASE_DIR} &&

desktop=`cat $HOME/.config/user-dirs.dirs | grep DESKTOP | tail -1 |cut -d '=' -f 2 | sed 's/\"//g'` &&
desktop=`eval echo $desktop` &&

echo "删除DEBIAN目录" &&
if [ -d "${PROJ_MKPKG_DIR}/DEBIAN" ]; then
	rm -rf ${PROJ_MKPKG_DIR}/DEBIAN
fi &&

echo "重新创建DEBIAN目录" &&
mkdir ${PROJ_MKPKG_DIR}/DEBIAN &&

echo "生成control脚本" &&
cat ${PROJ_MKPKG_DIR}/scripts/control.template | sed "s/VERSION/${VERSION}/g" | sed "s/APPNAME/${APP_NAME}/g" | sed "s/CPUARCH/${CPU_ARCH}/g" > ${PROJ_MKPKG_DIR}/DEBIAN/control &&

echo "生成postinst文件" &&
cat ${PROJ_MKPKG_DIR}/scripts/postinst.template | sed "s/INSTALLDIR/${APP_INSTALL_DIR////\\/}/g" | sed "s/DESKTOPFILE/\\/usr\\/share\\/applications\\/${APP_NAME}.desktop/g" | sed "s/APPNAME/${APP_NAME}/g" | sed "s/DESKTOPLINK/${desktop////\\/}\\/${APP_NAME}.desktop/g" > ${PROJ_MKPKG_DIR}/DEBIAN/postinst &&

echo "生成prerm脚本" &&
cat ${PROJ_MKPKG_DIR}/scripts/prerm.template | sed "s/INSTALLDIR/${APP_INSTALL_DIR////\\/}/g" | sed "s/DESKTOPFILE/\\/usr\\/share\\/applications\\/${APP_NAME}.desktop/g" | sed "s/APPNAME/${APP_NAME}/g" | sed "s/DESKTOPLINK/${desktop////\\/}\\/${APP_NAME}.desktop/g" > ${PROJ_MKPKG_DIR}/DEBIAN/prerm &&

echo "生成postrm脚本" &&
cat ${PROJ_MKPKG_DIR}/scripts/postrm.template | sed "s/INSTALLDIR/${APP_INSTALL_DIR////\\/}/g" | sed "s/DESKTOPFILE/\\/usr\\/share\\/applications\\/${APP_NAME}.desktop/g" | sed "s/APPNAME/${APP_NAME}/g" | sed "s/DESKTOPLINK/${desktop////\\/}\\/${APP_NAME}.desktop/g" > ${PROJ_MKPKG_DIR}/DEBIAN/postrm &&

echo "按DPKG打包命令，创建运行目录结构" &&
mkdir -p ${PROJ_RELEASE_DIR}/..${APP_INSTALL_DIR} &&
mv ${PROJ_RELEASE_DIR}/* ${PROJ_RELEASE_DIR}/..${APP_INSTALL_DIR} &&
mv ${PROJ_RELEASE_DIR}/..${APP_INSTALL_DIR:0:`expr index ${APP_INSTALL_DIR:1} "/"`} ${PROJ_RELEASE_DIR} &&

echo "按DPKG打包命令，创建DEBIAN目录" &&
chmod 0755 -R ${PROJ_MKPKG_DIR}/DEBIAN &&
mv ${PROJ_MKPKG_DIR}/DEBIAN ${PROJ_RELEASE_DIR} &&

echo "使能直接命令行运行" &&
mkdir -p ${PROJ_RELEASE_DIR}/usr/bin &&
cd ${PROJ_RELEASE_DIR}/usr/bin &&
ln -s ${APP_INSTALL_DIR}/start.sh ${APP_NAME} &&

echo "使能创建开始菜单快捷方式" &&
mkdir -p ${PROJ_RELEASE_DIR}/usr/share/applications &&
cat ${PROJ_RELEASE_DIR}/../scripts/index.desktop.template | sed "s/INSTALLDIR/${APP_INSTALL_DIR////\\/}/g" | sed "s/DESKTOPNAME/${DESKTOP_NAME}/g" | sed "s/APPNAME/${APP_NAME}/g" > ${PROJ_RELEASE_DIR}/usr/share/applications/${APP_NAME}.desktop &&
chmod +x ${PROJ_RELEASE_DIR}/usr/share/applications/${APP_NAME}.desktop &&

echo "文件授权" &&
sudo chown root ${PROJ_RELEASE_DIR}${APP_INSTALL_DIR}/chrome-sandbox &&
sudo chmod 4755 ${PROJ_RELEASE_DIR}${APP_INSTALL_DIR}/chrome-sandbox &&
sudo chmod 777 ${PROJ_RELEASE_DIR}${APP_INSTALL_DIR}/start.sh &&

# 判断授权so文件是否存在
echo ${PROJ_RELEASE_DIR}${APP_INSTALL_DIR}${AUTH_FILE_PATH} &&
if [ -f "${PROJ_RELEASE_DIR}${APP_INSTALL_DIR}${AUTH_FILE_PATH}" ]; then
	echo "授权so文件存在"
	sudo chown root:bin ${PROJ_RELEASE_DIR}${APP_INSTALL_DIR}${AUTH_FILE_PATH}
	sudo chmod u+s ${PROJ_RELEASE_DIR}${APP_INSTALL_DIR}${AUTH_FILE_PATH}
fi &&

echo "使能创建桌面快捷方式" &&
mkdir -p ${PROJ_RELEASE_DIR}/${desktop} &&
cd ${PROJ_RELEASE_DIR}/${desktop} &&
cp -f ${PROJ_RELEASE_DIR}/usr/share/applications/${APP_NAME}.desktop ${APP_NAME}.desktop &&

if [ "$IS_LOGIN_OPEN" = "true" ]; then
	echo "使能开机自启动"
	mkdir -p ${PROJ_RELEASE_DIR}/etc/xdg/autostart
	cp -f ${PROJ_RELEASE_DIR}/usr/share/applications/${APP_NAME}.desktop ${PROJ_RELEASE_DIR}/etc/xdg/autostart/${APP_NAME}.desktop
fi &&

mkdir -p ${PROJ_RELEASE_DIR}/etc/udev/rules.d &&
echo "拷贝.rules文件 到 /etc/udev/rules.d目录下" &&
cd ${PROJ_RELEASE_DIR}/../${PROJ_MKPKG_DIR} &&
cp -f index.rules ${PROJ_RELEASE_DIR}/etc/udev/rules.d/${APP_NAME}.rules &&

echo "开始打包" &&
cd ${PROJ_RELEASE_DIR}/.. &&
dpkg -b ${PROJ_RELEASE_DIR} ${APP_DEBIAN_NAME} &&

echo "打包完成"
