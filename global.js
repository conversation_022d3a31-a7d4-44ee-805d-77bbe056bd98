const { name, isCreateWindow, isLoginOpen } = require('./package.json')
const { app, crashReporter } = require('electron')
const path = require('path')
const fs = require('fs')
const { SYSTEM_PLATFORM_STR_DIC } = require('./hardware/enum')
const AppController = require('./server/back/controller/app')
const Logger = require('./hardware/log/index')
const log = new Logger()
const platform = process.platform

let basePath = process.cwd()
let extraResources
let appIcon
let fileSaveDirPath = basePath

if (!app.isPackaged) {
	extraResources = path.join(basePath, 'extraResources')
	appIcon = path.join(basePath, `public/icon.${platform == SYSTEM_PLATFORM_STR_DIC.WIN ? 'ico' : 'png'}`)
	if (platform === SYSTEM_PLATFORM_STR_DIC.WIN) {
		process.on('message', (data) => {
			if (data === 'graceful-exit') {
				app.quit()
			}
		})
	} else {
		process.on('SIGTERM', () => {
			app.quit()
		})
	}
} else {
	basePath = path.dirname(app.getPath('exe'))
	extraResources = path.join(process.resourcesPath, 'extraResources')
	appIcon = path.join(basePath, `icon.${platform == SYSTEM_PLATFORM_STR_DIC.WIN ? 'ico' : 'png'}`)
	fileSaveDirPath = platform == SYSTEM_PLATFORM_STR_DIC.WIN ? 'C:\\' : '/opt/gosuncn/'
	if (isLoginOpen && platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
		// 开机自启
		app.setLoginItemSettings({
			openAtLogin: true,
			openAsHidden: false,
			path: process.execPath,
			args: ['--processStart', `"${path.basename(process.execPath)}"`]
		})
	}
}
//低版本删除文件夹
function deleteFolderRecursively(folderPath) {
	if (fs.existsSync(folderPath)) {
		fs.readdirSync(folderPath).forEach((file, index) => {
			const curPath = path.join(folderPath, file)
			if (fs.lstatSync(curPath).isDirectory()) {
				// 递归删除文件夹
				deleteFolderRecursively(curPath)
			} else {
				// 删除文件
				fs.unlinkSync(curPath)
			}
		})
		// 删除空文件夹
		fs.rmdirSync(folderPath)
	} else {
		console.log('No such directory!')
	}
}
// 添加监听器
function addApplicationListener() {
	// 监听主进程是否崩溃
	app.setPath('temp', path.join(global.basePath, 'crash'))
	crashReporter.start({
		productName: name,
		companyName: 'gosuncn',
		submitURL: '',
		uploadToServer: false
	})
	process.on('uncaughtException', (error) => {
		console.log(error.stack)
		log.error(`uncaughtException => ${JSON.stringify(error)}`)
		app.isPackaged && AppController.rebootApp()
	})
	/**
	 * @param {Object} details
	 * @param {String} reason  - 渲染进程消失的原因。 可选值：
	 * clean-exit - 以零为退出代码退出的进程
	 * abnormal-exit - 以非零退出代码退出的进程
	 * killed - 进程发送一个SIGTERM，否则是被外部杀死的。
	 * crashed - 进程崩溃
	 * oom - 进程内存不足
	 * launch-failed - 进程从未成功启动
	 * integrity-failure - 窗口代码完整性检查失败
	 * @param {Integer} exitCode - 进程的退出代码，除非在 reason 是 launch-failed 的情况下， exitCode 将是一个平台特定的启动失败错误代码。
	 */
	// 渲染器进程意外消失时触发
	app.on('render-process-gone', (event, webContents, details) => {
		log.error(`renderer-process-crashed, reason => ${JSON.stringify(details)}`)
		app.isPackaged && AppController.rebootApp()
	})
}

// 创建目录
function createDir(dir) {
	if (!fs.existsSync(dir)) {
		fs.mkdirSync(dir, { recursive: true })
	}
}

function hasOwnProperty() {
	return Object.prototype.hasOwnProperty.call(...arguments)
}

function handleConfigObj(appConfig, commonConfig) {
	function deepMerge(target, source) {
		if (typeof target !== 'object' || target === null) {
			return source
		}
		if (Array.isArray(target)) {
			if (Array.isArray(source)) {
				return [...new Set(target.concat(source))]
			}
			return source
		}
		for (const key in source) {
			if (!hasOwnProperty(source, key)) {
				continue
			}
			if (typeof source[key] === 'object' && source[key] !== null) {
				if (!target[key] || typeof target[key] !== 'object') {
					target[key] = {}
				}
				target[key] = deepMerge(target[key], source[key])
			} else {
				target[key] = target[key] || source[key]
			}
		}
		return target
	}
	const mergedConfig = deepMerge(commonConfig, appConfig)
	// 基础数据类型字段 使用commonConfig的值
	const special = ['version']
	for (const key in commonConfig) {
		if (special.includes(key)) {
			mergedConfig[key] = appConfig[key]
			continue
		}
		if (hasOwnProperty(commonConfig, key) && typeof commonConfig[key] !== 'object') {
			mergedConfig[key] = commonConfig[key]
		}
	}
	return mergedConfig
}

// 获取配置文件信息
function handleServerConfig() {
	const serverConfig = {}
	const appConfigPath = path.join(global.basePath, 'appdb/serverConfig.json')
	const isAppConfigFile = fs.existsSync(appConfigPath)
	if (!app.isPackaged) {
		Object.assign(serverConfig, JSON.parse(fs.readFileSync(appConfigPath)))
		return serverConfig
	}
	const isCommonConfigFile = fs.existsSync(global.serverConfigPath)
	if (isAppConfigFile && isCommonConfigFile) {
		const appConfig = JSON.parse(fs.readFileSync(appConfigPath))
		const commonConfig = JSON.parse(fs.readFileSync(global.serverConfigPath))
		Object.assign(serverConfig, handleConfigObj(appConfig, commonConfig))
		fs.unlinkSync(appConfigPath)
	} else if (isAppConfigFile && !isCommonConfigFile) {
		Object.assign(serverConfig, JSON.parse(fs.readFileSync(appConfigPath)))
		fs.unlinkSync(appConfigPath)
	} else if (isCommonConfigFile && !isAppConfigFile) {
		Object.assign(serverConfig, JSON.parse(fs.readFileSync(global.serverConfigPath)))
	} else {
		log.warn('未找到配置文件')
	}
	// 保存配置文件
	fs.writeFileSync(global.serverConfigPath, JSON.stringify(serverConfig, null, 4))
	return serverConfig
}

// 处理数据库文件
function handleDbFile() {
	// 数据库文件路径
	if (fs.existsSync(global.databasePath)) {
		log.info('数据库文件已存在')
		return
	}
	log.info('未找到数据库文件，使用默认数据库')
	// 复制数据库文件
	if (fs.existsSync(global.appDatabasePath)) {
		fs.copyFileSync(global.appDatabasePath, global.databasePath)
	} else {
		log.warn('未找到默认数据库文件')
	}
}

// 删除升级安装包目录
function delPackageDir() {
	try {
		if (fs.existsSync(global.packagePath)) {
			const state = fs.statSync(global.packagePath)
			if (state.isDirectory()) {
				deleteFolderRecursively(global.packagePath)
			}
		}
	} catch (error) {
		log.info('删除升级安装包目录失败：', error.toString())
	}
}

function initGlobal() {
	global.isCreateWindow = isCreateWindow
	global.appIcon = appIcon
	global.basePath = basePath
	global.extraResources = extraResources
	const configDirName = 'appdb'
	const databaseFileName = 'app.db'
	global.appDatabasePath = path.join(global.basePath, configDirName, databaseFileName)
	global.userTableName = 'user'
	global.itemNumberTableName = 'item_number'
	addApplicationListener()
	// 文件存储目录
	global.fileSaveDirPath = path.join(fileSaveDirPath, configDirName)
	// 应用升级安装包目录
	global.packagePath = path.join(global.fileSaveDirPath, `${name}-package`)
	// 质检报告存储目录
	global.reportPath = path.join(global.fileSaveDirPath, 'report')
	// 数据库文件路径
	global.databasePath = path.join(global.fileSaveDirPath, databaseFileName)
	// 硬件授权文件路径
	global.licenseDeviceDatPath = path.join(global.fileSaveDirPath, 'license.dat')
	// 授权设备信息采集存储文件路径
	global.licenseDeviceInfoPath = path.join(global.fileSaveDirPath, 'deviceInfo.info')
	// 配置文件路径
	global.serverConfigPath = path.join(global.fileSaveDirPath, 'serverConfig.json')
	// IOT 授权文件
	global.iotAuthFilePath = path.join(global.fileSaveDirPath, 'iot_auth.lic')
	// 创建目录
	createDir(global.fileSaveDirPath)
	createDir(global.reportPath)
	// 配置文件读取
	global.serverConfig = handleServerConfig()
	// 删除升级包目录
	delPackageDir()
	// 定时删除本地日志文件
	log.deleteLog(global.serverConfig.logSaveDay)
	log.scanFile(global.serverConfig.logSaveDay)
	handleDbFile()
}

module.exports = initGlobal()
