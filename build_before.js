const fs = require('fs')
const path = require('path')
const { name, version, smallVersion, shortcutName, serverPort, isLoginOpen } = require('./package.json')
const { AUTH_OBJ_DIC } = require('./server/back/enum')
const { SYSTEM_PLATFORM_STR_DIC, SYSTEM_ARCH_STR_DIC } = require('./hardware/enum')

const isCreateWindow = !process.argv.includes('--server')
const arch = process.arch
const platform = process.platform
let packVersion

const initVersion = () => {
	const date = new Date()
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	const hours = String(date.getHours()).padStart(2, '0')
	const min = String(date.getMinutes()).padStart(2, '0')
	packVersion = `${version}.${smallVersion ? `${smallVersion}.` : ''}${year}.${month}.${day}.${hours}${min}`
}

const dashToCamel = (str, letter = '-') => {
	return str
		.split(letter)
		.map(function (item) {
			return item.charAt(0).toUpperCase() + item.slice(1)
		})
		.join('')
}

const writePackageVersion = () => {
	const pkgPath = path.join(__dirname, 'package.json')
	let pjson = fs.readFileSync(pkgPath)
	pjson = JSON.parse(pjson)
	pjson.actualVersion = packVersion
	pjson.isCreateWindow = isCreateWindow
	let appName = name
	let appShortcutName = shortcutName
	if (!isCreateWindow) {
		appName = 'gosuncn-server'
		appShortcutName = appName
	}
	pjson.name = appName
	pjson.shortcutName = appShortcutName
	pjson.description = appName.replace(/-/g, ' ')
	pjson.build.appId = appName.replace(/-/g, '.')
	pjson.build.productName = dashToCamel(appName)
	if (platform == SYSTEM_PLATFORM_STR_DIC.LINUX) {
		pjson.scripts.mkdeb = `
		sudo chown root:bin ./extraResources/linux/${arch}/hs_auth/GSdmidecode &&
		 sudo chmod u+s ./extraResources/linux/${arch}/hs_auth/GSdmidecode &&
		 cd ./linux && sudo chmod +x ./*.sh &&
		 sed -i 's/\r$//' ./*.rules ./*.sh ./scripts/* &&
		 ./mkdeb.sh ${packVersion} ${appName} ${pjson.build.directories.output} ${appShortcutName} ${isLoginOpen}
		`.replace(/\n|\t/g, '')
	} else {
		pjson.build.nsis.shortcutName = appShortcutName
		pjson.build.win.target[0].arch = [arch]
		pjson.build.win.artifactName = `${appName}_${arch}_${packVersion}.exe`
	}
	fs.writeFileSync(pkgPath, JSON.stringify(pjson, null, 2))
}

const delDllSdk = () => {
	const resourcesDir = path.join(process.cwd(), 'extraResources')
	let platformSourcesDirPath = ''
	let archDirSourcesDirPath = ''
	if (platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
		platformSourcesDirPath = path.join(resourcesDir, SYSTEM_PLATFORM_STR_DIC.LINUX)
		archDirSourcesDirPath = path.join(resourcesDir, platform, arch == SYSTEM_ARCH_STR_DIC.IA32 ? SYSTEM_ARCH_STR_DIC.X64 : SYSTEM_ARCH_STR_DIC.IA32)
	} else if (platform == SYSTEM_PLATFORM_STR_DIC.LINUX) {
		platformSourcesDirPath = path.join(resourcesDir, SYSTEM_PLATFORM_STR_DIC.WIN)
		archDirSourcesDirPath = path.join(resourcesDir, platform, arch == SYSTEM_ARCH_STR_DIC.ARM64 ? SYSTEM_ARCH_STR_DIC.X64 : SYSTEM_ARCH_STR_DIC.ARM64)
	}
	if (platformSourcesDirPath && fs.existsSync(platformSourcesDirPath)) {
		console.log(`删除目录：${platformSourcesDirPath}`)
		deleteFolderRecursively(platformSourcesDirPath)
	}
	if (archDirSourcesDirPath && fs.existsSync(archDirSourcesDirPath)) {
		console.log(`删除目录：${archDirSourcesDirPath}`)
		deleteFolderRecursively(archDirSourcesDirPath)
	}
}

const updateServerConfig = () => {
	const serverConfigPath = path.join(__dirname, 'appdb/serverConfig.json')
	let config = fs.readFileSync(serverConfigPath)
	config = JSON.parse(config)
	config.version = packVersion
	config.backHttpServerPort = serverPort
	config.authKey = isCreateWindow ? AUTH_OBJ_DIC.APP : AUTH_OBJ_DIC.THIRD
	fs.writeFileSync(serverConfigPath, JSON.stringify(config, null, 2))
}

const delFile = () => {
	const delPathList = ['license.dat', 'deviceInfo.info', 'iot_auth.lic', 'report', `${name}-package`]
	delPathList.forEach((item) => {
		const filePath = path.join(process.cwd(), 'appdb', item)
		if (!fs.existsSync(filePath)) return
		const isFile = fs.statSync(filePath).isFile()
		if (isFile) {
			console.log(`删除${filePath}文件`)
			fs.unlinkSync(filePath)
		} else {
			console.log(`删除${filePath}目录`)
			deleteFolderRecursively(filePath)
		}
	})
}
//低版本删除文件夹
function deleteFolderRecursively(folderPath) {
	if (fs.existsSync(folderPath)) {
		fs.readdirSync(folderPath).forEach((file, index) => {
			const curPath = path.join(folderPath, file)
			if (fs.lstatSync(curPath).isDirectory()) {
				// 递归删除文件夹
				deleteFolderRecursively(curPath)
			} else {
				// 删除文件
				fs.unlinkSync(curPath)
			}
		})
		// 删除空文件夹
		fs.rmdirSync(folderPath)
	} else {
		console.log('No such directory!')
	}
}
initVersion()
writePackageVersion()
delDllSdk()
updateServerConfig()
delFile()
