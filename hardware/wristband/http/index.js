const WristbandCommon = require('../index')
const { ACTION_TYPE_DIC } = require('../../enum')
const http = require('../../lib/request')
const uac = 'http://127.0.0.1:8989/api'

class Wristband extends WristbandCommon {
	constructor() {
		super()
	}
	// 发送请求获取数据
	#sendMessage(url) {
		return http({
			method: 'get',
			url: uac + url
		})
	}
	// 查询M1
	findM1Card() {
		const timestamp = new Date().getTime()
		const url = `/M1Reset?&waitTime=3&_t=${timestamp}`
		const res = this.#sendMessage(url)
		res.then(
			(res) => {
				if (res.retcode == 0) {
					this.#authenM1Card()
				} else {
					this.handleSendMessageErrorByAction(ACTION_TYPE_DIC.READ_WRISTBAND)
				}
			},
			() => {
				this.handleSendMessageErrorByAction(ACTION_TYPE_DIC.READ_WRISTBAND)
			}
		)
	}
	// M1卡认证
	#authenM1Card() {
		const url = '/M1AuthenKey?KeyType=0&BlockNo=0&Key=FFFFFFFFFFFF'
		const res = this.#sendMessage(url)
		res.then(
			(res) => {
				if (res.retcode == 0) {
					this.#getM1Card()
				} else {
					this.handleSendMessageErrorByAction(ACTION_TYPE_DIC.READ_WRISTBAND)
				}
			},
			() => {
				this.handleSendMessageErrorByAction(ACTION_TYPE_DIC.READ_WRISTBAND)
			}
		)
	}
	// 获取M1卡信息
	async #getM1Card() {
		const result = {
			cardNo: '',
			IcCardNo: ''
		}
		const flag = {
			cardNo: false,
			IcCardNo: true
		}
		const url = '/M1ReadBlock?BlockNo=60'
		const res = this.#sendMessage(url)
		res.then(
			(res) => {
				if (res.retcode == 0) {
					result.cardNo = parseInt(res.data).toString()
					flag.cardNo = true
				}
				if (flag.cardNo && flag.IcCardNo) {
					this.handleSuccessByAction(ACTION_TYPE_DIC.READ_WRISTBAND, result)
				} else {
					this.handleSuccessByAction(ACTION_TYPE_DIC.READ_WRISTBAND)
				}
			},
			() => {
				this.handleSendMessageErrorByAction(ACTION_TYPE_DIC.READ_WRISTBAND)
			}
		)
	}
	/**
	 * 销毁
	 */
	close() {
		this.removeAllEventListener()
	}
}

module.exports = Wristband
