const { EventEmitter } = require('events')
const { CODE_DIC, CODE_ENUM, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../enum')

class WristbandC<PERSON>mon extends EventEmitter {
	constructor() {
		super()
	}
	handleSuccessByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.OK,
			msg: CODE_DIC[CODE_ENUM.OK],
			data: {
				flag: DATA_CODE_ENUM.ACTION_SUCCESS,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_SUCCESS],
				...data
			}
		})
	}
	handleSendMessageErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.SEND_MESSAGE_ERROR,
			msg: CODE_DIC[CODE_ENUM.SEND_MESSAGE_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}
	// 返回数据
	#callback(data) {
		this.emit('wristband-callback', data)
	}
	removeAllEventListener() {
		this.removeAllListeners('wristband-callback')
	}
}

module.exports = WristbandCommon
