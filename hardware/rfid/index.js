const { EventEmitter } = require('events')
const Logger = require('../log/index')
const log = new Logger('rfid')
const { CODE_DIC, CODE_ENUM, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../enum')

class <PERSON><PERSON>d<PERSON><PERSON><PERSON> extends EventEmitter {
	constructor() {
		super()
		this.logger = log
		this.overTime = 10 * 1000 // 检测超时时间
	}

	handleSuccessByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.OK,
			msg: CODE_DIC[CODE_ENUM.OK],
			data: {
				flag: DATA_CODE_ENUM.ACTION_SUCCESS,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_SUCCESS],
				...data
			}
		})
	}

	handleLinkErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.LINK_ERROR,
			msg: CODE_DIC[CODE_ENUM.LINK_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleParamsErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.PARAMS_ERROR,
			msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleSendMessageErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.SEND_MESSAGE_ERROR,
			msg: CODE_DIC[CODE_ENUM.SEND_MESSAGE_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleParseErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.PARSE_ERROR,
			msg: CODE_DIC[CODE_ENUM.PARSE_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleNoResponseErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.NO_RESPONSE,
			msg: CODE_DIC[CODE_ENUM.NO_RESPONSE],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleCloseErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.CLOSE_ERROR,
			msg: CODE_DIC[CODE_ENUM.CLOSE_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleInOperationErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.IN_OPERATION,
			msg: CODE_DIC[CODE_ENUM.IN_OPERATION],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	#callback(data) {
		this.emit('rfid-callback', data)
	}

	removeAllEventListener() {
		this.removeAllListeners('rfid-callback')
	}
}

module.exports = RfidCommon
