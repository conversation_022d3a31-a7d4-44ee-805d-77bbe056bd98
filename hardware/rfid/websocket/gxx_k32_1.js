// 酷安 - RFID高频
const RfidWsCommon = require('./common')
const { JSLock } = require('../../lib/util_class')
const { ACTION_TYPE_DIC } = require('../../enum')
const defaultUrlMap = {
	win32_x64: 'ws://localhost:21233/rfid',
	win32_ia32: 'ws://localhost:21233/rfid',
	linux_x64: 'ws://localhost:21233/Fre',
	linux_arm64: 'ws://localhost:21233/Fre'
}

class RFID extends RfidWsCommon {
	constructor() {
		super()
		this.jsLock = new JSLock() // 检测线程锁
		this.resultLock = new JSLock() // 查询结果锁
		this.isEnding = true // 当前检测是否结束
		this.result = [] // 当前柜检测到的rfid码
		this.config = {} // 当前检测柜门信息
	}

	// 连接rfid服务
	init(url) {
		this.initWebscoket(url || defaultUrlMap[`${process.platform}_${process.arch}`], (res) => {
			this.receiveMessage(res) // 监听ws返回信息
		})
	}

	/**
	 * 检测单个柜格 rfid
	 */
	checkRfidCode(config) {
		if (!this.isEnding) {
			return this.handleInOperationErrorByAction(config.action || ACTION_TYPE_DIC.CHECK_RFID, config)
		}
		this.#checkFun(() => {
			this.config = config
		})
	}

	async #checkFun(fun) {
		if (!this.isEnding) {
			this.logger.info('尚有检测任务，线程锁上锁...')
			await this.jsLock.lock()
			this.logger.info('上一个检测任务结束，线程锁解锁...')
		}

		fun()

		// 检测指令
		const msg = this.#makeDir()
		this.isEnding = false
		this.result = {} // 清空读取结果值
		this.sendMessage({ ...msg, action: ACTION_TYPE_DIC.CHECK_RFID })
		const startTime = Date.now()
		let checkTimer = setInterval(() => {
			if (this.isEnding || Date.now() - startTime >= this.overTime) {
				clearInterval(checkTimer)
				checkTimer = null
				this.isEnding = false
				this.resultLock.unlock()
				this.logger.info(`${this.config.address}-${this.config.door}检测接收到结果，解除锁定...`)
			}
		}, 500)

		this.logger.info(`${this.config.address}-${this.config.door}检测锁定，检测结果查询中...`)
		await this.resultLock.lock()
		this.jsLock.unlock()
	}

	// 接收ws返回信息
	receiveMessage(res) {
		this.logger.info(`ws res: ${res}`)
		let resultObj = {}
		this.isEnding = false
		try {
			resultObj = JSON.parse(res)
		} catch (error) {
			this.result = ''
			return this.handleParseErrorByAction(this.config.action || ACTION_TYPE_DIC.CHECK_RFID, this.config)
		}
		const { actiontype, result, box, door } = resultObj
		if (['out', 'in'].includes(actiontype)) {
			return
		}
		// 检测正常
		if (actiontype == 'goods') {
			// 检测结果
			this.result = result
		} else {
			this.result = ''
		}
		this.handleSuccessByAction(this.config.action || ACTION_TYPE_DIC.CHECK_RFID, {
			address: box,
			door,
			result: this.result
		})
	}

	/**
	 * 组装指令
	 * @returns
	 */
	#makeDir() {
		return {
			action: ACTION_TYPE_DIC.CHECK_RFID,
			Actiontype: 'query',
			Box: this.config.address,
			Door: this.config.door,
			RequestTime: Date.now()
		}
	}

	// 关闭连接
	close() {
		this.isEnding = true
		this.result = []
		this.closeWebsocket()
	}
}

module.exports = RFID
