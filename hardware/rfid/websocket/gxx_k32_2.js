// 酷安 - RFID超高频
const RfidWsCommon = require('./common')
const { JSLock } = require('../../lib/util_class')
const { ACTION_TYPE_DIC } = require('../../enum')
const defaultUrlMap = {
	win32_x64: 'ws://localhost:21232/highrfid',
	win32_ia32: 'ws://localhost:21232/highrfid',
	linux_x64: 'ws://localhost:21232/highFre',
	linux_arm64: 'ws://localhost:21232/highFre'
}

class RFID extends RfidWsCommon {
	constructor() {
		super()
		this.jsLock = new JSLock() // 检测线程锁
		this.resultLock = new JSLock() // 查询结果锁
		this.isEnding = true // 当前检测是否结束
		this.result = {} // 当前柜检测到的rfid码
		this.config = {} // 当前检测柜门信息
	}

	// 连接rfid服务
	init(url) {
		this.initWebscoket(url || defaultUrlMap[`${process.platform}_${process.arch}`], (res) => {
			this.receiveMessage(res) // 监听ws返回信息
		})
	}

	/**
	 * 检测单个柜格 rfid
	 * @param {Object} config
	 */
	checkRfidCode(config) {
		if (!this.isEnding) {
			return this.handleInOperationErrorByAction(config.action || ACTION_TYPE_DIC.CHECK_RFID, config)
		}
		this.#checkFun(() => {
			this.config = config
		})
	}

	/**
	 * 检测单柜所有柜格 rfid
	 * @returns Promise
	 */
	async #checkFun(fun) {
		if (!this.isEnding) {
			this.logger.info('尚有检测任务，柜线程锁上锁...')
			await this.jsLock.lock()
			this.logger.info('上一个检测任务结束，线程锁解锁...')
		}

		fun()

		// 检测指令
		const msg = this.#makeDir()
		if (!msg) {
			return this.handleParamsErrorByAction(this.config.action || ACTION_TYPE_DIC.CHECK_RFID, this.config)
		}
		this.isEnding = false
		this.result = {}
		this.sendMessage({ ...msg, action: ACTION_TYPE_DIC.CHECK_RFID })
		const startTime = Date.now()
		let checkTimer = setInterval(() => {
			if (this.isEnding || Date.now() - startTime >= this.overTime) {
				clearInterval(checkTimer)
				checkTimer = null
				this.isEnding = false
				this.resultLock.unlock()
				this.logger.info('柜检测接收到结果，解除锁定...')
			}
		}, 300)

		this.logger.info('柜检测锁定，检测结果查询中...')
		await this.resultLock.lock()
		this.jsLock.unlock()
	}

	// 接收ws返回信息
	receiveMessage(res) {
		this.logger.info(`ws res: ${res}`)
		let resultObj = {}
		try {
			resultObj = JSON.parse(res)
		} catch (error) {
			this.isEnding = true
			this.result = {}
			return this.handleParseErrorByAction(this.config.action || ACTION_TYPE_DIC.CHECK_RFID, this.config)
		}
		const { success, data, result, isfinished, door } = resultObj
		const curCheckDoor = `${this.config.address}-${door}`
		if (!success) {
			// 没有检测到rfid码
			this.isEnding = true
			this.result = {}
			// 读卡器连接失败等报错
			if (data === null) {
				this.handleNoResponseErrorByAction(this.config.action || ACTION_TYPE_DIC.CHECK_RFID, {
					address: this.config.box,
					door: this.config.door,
					result: [],
					resultMsg: result
				})
			}
			return
		} else {
			// 检测到柜中有rfid码
			if (!this.result[curCheckDoor]) {
				this.result[curCheckDoor] = new Set()
			}
			data.forEach((item) => {
				this.result[curCheckDoor].add(item.cardid)
			})
		}
		// 检测结束
		if (isfinished == 1) {
			if (this.result[curCheckDoor]) {
				this.result[curCheckDoor] = Array.from(this.result[curCheckDoor])
			}
			this.isEnding = true
			this.handleSuccessByAction(this.config.action || ACTION_TYPE_DIC.CHECK_RFID, {
				address: this.config.address,
				door,
				result: this.result[`${this.config.address}-${this.config.door}`]
			})
		}
	}

	/**
	 * 组装指令
	 */
	#makeDir() {
		const wsUrl = this.config.address
		if (!wsUrl) {
			this.isEnding = true
			return this.handleParamsErrorByAction(this.config.action || ACTION_TYPE_DIC.CHECK_RFID, this.config)
		}
		try {
			const [host, port] = wsUrl.split(':')
			const jsTicks = new Date().getTime()
			const msg = {
				action: ACTION_TYPE_DIC.CHECK_RFID,
				actiontype: 'querycard', // querycard or queryall
				ip: host,
				port,
				RequestTime: jsTicks,
				IntervalTime: this.config.hightRfidCheckInterval || 200, // 超高频RFID检测返回值间隔时间
				trytimes: this.config.hightRfidCheckTryTimes || 5, // 超高频RFID检测次数
				door: this.config.door // actiontype=='queryall' 多个柜用逗号隔开
			}
			return msg
		} catch (error) {
			this.logger.error(`解析参数异常，${error}`)
			return null
		}
	}

	/**
	 * 结束检测
	 */
	close() {
		this.isEnding = true
		this.config = {}
		this.result = {}
		this.closeWebsocket()
	}
}

module.exports = RFID
