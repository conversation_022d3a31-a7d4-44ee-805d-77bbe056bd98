const RfidSerialPortCommon = require('./common')
const { ACTION_TYPE_DIC } = require('../../enum')

// 迪川 - 五门柜 - 高频
class Rfid extends RfidSerialPortCommon {
	constructor() {
		super()
		this.baudRate = 19200 // 波特率
	}
	/**
	 * 查询具体锁板的柜子RFID code
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 */
	checkRfidCode(config) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)
		const dirObj = {
			lockPlate,
			lockNumber,
			dir: [0x88, lockPlate, 0x80, 0x01, lockNumber],
			action: ACTION_TYPE_DIC.CHECK_RFID,
			delay: 100
		}
		this.runList.push(dirObj)
		!this.isRun && this.run()
	}
	/**
	 * 设置柜子灯光颜色
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 * @param {String} config.lightType 灯光颜色
	 */
	setLight(config) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)
		const lightType = parseInt(config.lightType)
		const dirObj = {
			lockPlate,
			lockNumber,
			dir: [0x88, lockPlate, 0x8c, 0x02, lockNumber, lightType],
			action: ACTION_TYPE_DIC.SET_DOOR_LIGHT,
			delay: 50
		}
		this.runList.push(dirObj)
		!this.isRun && this.run()
	}
	/**
	 * 设置黄灯颜色
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.green 绿色灯光值
	 * @param {Number} config.red 红色灯光值
	 */
	setOrangeColor(config) {
		const lockPlate = parseInt(config.address)
		const green = parseInt(config.green)
		const red = parseInt(config.red)
		const dirObj = {
			lockPlate,
			dir: [0x88, lockPlate, 0x8d, 0x02, green, red],
			action: ACTION_TYPE_DIC.SET_ORANGE_COLOR,
			delay: 50
		}
		this.runList.push(dirObj)
		!this.isRun && this.run()
	}
	/**
	 * 设置轮询通道数
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.channels 通道数
	 */
	setPollingChannels(config) {
		const lockPlate = parseInt(config.address)
		const channels = parseInt(config.channels)
		const dirObj = {
			lockPlate,
			dir: [0x88, lockPlate, 0x7a, 0x01, channels],
			action: ACTION_TYPE_DIC.SET_POLLING_CHANNELS,
			delay: 50
		}
		this.runList.push(dirObj)
		!this.isRun && this.run()
	}
	/**
	 * 接收串口返回数据
	 */
	receiveMessage() {
		this.port.on('readable', () => {
			this.isAwaitResult = false
			const res = this.port.read()
			this.backData = this.backData.concat([...res])
			this.logger.info('data:', JSON.stringify(this.backData))
			// 实际 code 数据长度
			const dataLen = parseInt(this.backData[3]) || 0
			if (this.backData[0] == 0x86 && this.backData[dataLen + 5] == 0x66 && this.backData.length >= dataLen + 6) {
				const dirObj = this.currentDirObj || {}
				const actionType = this.getActionType(dirObj.action)
				if (actionType) {
					this.handleSuccessByAction(actionType, {
						address: dirObj.lockPlate,
						door: dirObj.lockNumber,
						result: dataLen > 0 ? this.handleMessage(this.backData.slice(4, 4 + dataLen)) : ''
					})
					this.currentDirObj = null
				}
				this.backData = []
			}
		})
	}
	/**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
	dealCommand(command) {
		const verifyCode = command.reduce((total, value) => {
			return total ^ value
		}, 0)
		command.push(verifyCode)
		command.push(0x68)
		const newCommand = command.map((num) => {
			return this.decToHex(num)
		})
		const cmdBuffer = Buffer.from(newCommand.join(''), 'hex')
		return cmdBuffer
	}
	/**
	 * 返回数据处理
	 * @param {Array} bytes 数据数组
	 * @returns status
	 */
	handleMessage(bytes) {
		const hexResult = Number(String.fromCharCode(...bytes)).toString(16)
		return hexResult ? this.hexReverse(hexResult.padStart(hexResult.length + (hexResult.length % 2), '0')) : ''
	}
	/**
	 * 十六进制翻转（大小端互换）
	 *
	 * @param hex 16进制字符串，不带0x
	 * @return {*}
	 */
	hexReverse(hex) {
		hex = hex.replace(/\s/g, '').replace(/(.{2})/g, '$1 ')
		hex = hex.split(' ').reverse().join('')
		return hex
	}
}

module.exports = Rfid
