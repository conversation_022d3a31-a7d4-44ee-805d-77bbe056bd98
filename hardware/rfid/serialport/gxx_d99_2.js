const RfidSerialPortCommon = require('./common')
const { ACTION_TYPE_DIC } = require('../../enum')

// 迪川 - 超高频
class Rfid extends RfidSerialPortCommon {
	constructor() {
		super()
		this.baudRate = 115200 // 波特率
	}
	/**
	 * 查询具体锁板的柜子RFID code
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 */
	checkRfidCode(config) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)
		const dir = [0x5a, 0x00, 0x01, 0x22, 0x10, lockPlate, 0x00, 0x0a, 0x00, 0x00, 0x00, 0x01, 0x01, 0x17, 0x00, 0x00, 0x13, 0x88]
		const bytes = Number(1 << (lockNumber - 1))
			.toString(16)
			.padStart(8, '0')
		for (let i = 0; i < 8; i++) {
			if (i % 2 != 0) {
				const index = 8 + parseInt(i / 2)
				dir[index] = parseInt(`0x${bytes.substring(i - 1, i + 1)}`)
			}
		}
		/** 对接文档说明
		 * 解析：5A0001021000080000000101020006ED08
		 * 5A：帧头
		 * 00010210：协议控制字
		 * 0008：数据长度
		 * 00000001：天线端口，1号天线
		 * 01：连续读取 (00:单次读取)
		 * 02：TID读取参数PID
		 * 00：TID读取模式
		 * 06：TID读取长度
		 * ED08：crc校验码
		 */
		const dirObj = {
			lockPlate,
			lockNumber,
			dir,
			action: ACTION_TYPE_DIC.CHECK_RFID,
			delay: 500
		}
		this.runList.push(dirObj)
		!this.timer && this.run()
	}
	/**
	 * 接收串口返回数据
	 */
	receiveMessage() {
		let portData = ''
		this.port.on('readable', async () => {
			this.isAwaitResult = false
			const res = this.port.read()
			// 数据帧头标记5A
			if (portData == '' && res[0] != 0x5a) {
				return
			}
			// 数据十进制转十六进制
			for (let i = 0; i < res.length; i++) {
				portData += this.decToHex(res[i])
			}
			this.logger.info('portData:', portData)
			// 16：数据固定长度
			if (portData.length <= 16) {
				return
			}
			portData = (await this.#analysis(portData)) || ''
		})
	}
	/**
	 * 数据解析
	 */
	#analysis(portData) {
		return new Promise(async (resolve) => {
			/**
			 * 协议控制字：
			 * 00013200: 数据标记
			 * 00013201：结束标记,代表结束读写器主动上传一条读操作结束的通知
			 */
			const protocolControlWord = portData.substring(2, 10)
			switch (protocolControlWord) {
				case '00013200':
					const dataLen = parseInt(`0x${portData.substring(12, 16)}`) * 2 || 0
					const total = 20 + dataLen
					if (portData.length < total) {
						return resolve(portData)
					}
					this.backData.push(portData)
					// 截取超出部分
					portData = portData.substring(total, portData.length) || ''
					if (portData) {
						resolve(await this.#analysis(portData))
					} else {
						resolve(portData)
					}
					break
				case '00013201':
					// 22：结束标记数据长度
					if (portData.length < 22) {
						return resolve(portData)
					}
					/**
					 * flag
					 * 0: 单次操作完成
					 * 1: 收到停止指令
					 * 2: 硬件故障导致读卡中断
					 */
					const flag = parseInt(`0x${portData.substring(16, 18)}`) || 0
					if (flag == 2) {
						this.backData = []
						this.#sendData()
						return resolve()
					}
					const result = {}
					this.backData.forEach((item) => {
						// 实际 code 数据长度
						const codeLen = parseInt(`0x${item.substring(16, 20)}`) * 2 || 0
						const code = item.substring(20, 20 + codeLen).toLocaleUpperCase()
						// 天线标记
						const num = parseInt(`0x${item.substring(codeLen + 24, codeLen + 26)}`)
						if (result[num]) {
							result[num].push(code)
						} else {
							result[num] = [code]
						}
					})
					this.#sendData(result)
					resolve()
					break
				default:
					resolve()
					break
			}
		})
	}
	/**
	 * 返回数据到业务处理
	 */
	#sendData(result = {}) {
		const dirObj = this.currentDirObj || {}
		const actionType = dirObj.action ? this.getActionType(dirObj.action) : ''
		if (actionType) {
			this.handleSuccessByAction(actionType, {
				address: dirObj.lockPlate,
				door: dirObj.lockNumber,
				result: result && result[dirObj.lockNumber] ? [...new Set(result[dirObj.lockNumber])] : []
			})
			this.currentDirObj = null
		}
	}
	/**
	 * crc 校验码
	 * @param {Array} pchMsg
	 * @param {Number} wDataLen
	 * @returns
	 */
	#CRC16_CCITT(pchMsg, wDataLen) {
		let wCRC = 0
		for (let j = 0; j < wDataLen; j++) {
			const chChar = pchMsg[j]
			wCRC = wCRC ^ (chChar << 8)
			for (let i = 0; i < 8; i++) {
				if (wCRC & 0x8000) {
					wCRC = (wCRC << 1) ^ 0x1021 // CRC_16_CCITT的值是0x1021
				} else {
					wCRC <<= 1
				}
			}
		}
		return wCRC & 0xffff // 保留16位结果
	}
	/**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
	dealCommand(command) {
		// 换算crc校验码
		const dir = command.slice(1, command.length)
		const CRC16 = this.#CRC16_CCITT(dir, dir.length).toString(16)
		const verifyCode = CRC16.padStart(4, '0')
		command.push(verifyCode.substring(0, 2))
		command.push(verifyCode.substring(2, 4))
		// 组装指令
		const newCommand = command.map((num) => {
			return this.decToHex(num)
		})
		const cmdBuffer = Buffer.from(newCommand.join(''), 'hex')
		this.logger.info('发送指令成功: ', newCommand.join(''))
		return cmdBuffer
	}
}

module.exports = Rfid
