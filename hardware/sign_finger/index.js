const { EventEmitter } = require('events')
const Logger = require('../log/index')
const log = new Logger('sign-finger')
const { CODE_DIC, CODE_ENUM, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../enum')

class Sign<PERSON><PERSON><PERSON><PERSON><PERSON> extends EventEmitter {
	static EVENT_NAME = 'sign-finger-callback'
	constructor() {
		super()
		this.logger = log
	}

	handleSuccessByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.OK,
			msg: CODE_DIC[CODE_ENUM.OK],
			data: {
				flag: DATA_CODE_ENUM.ACTION_SUCCESS,
				message: data?.message || DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_SUCCESS],
				...data
			}
		})
	}

	handleLinkErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.LINK_ERROR,
			msg: CODE_DIC[CODE_ENUM.LINK_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: data?.message || DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleParamsErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.PARAMS_ERROR,
			msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: data?.message || DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleSendMessageErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.SEND_MESSAGE_ERROR,
			msg: CODE_DIC[CODE_ENUM.SEND_MESSAGE_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: data?.message || DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleParseErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.PARSE_ERROR,
			msg: CODE_DIC[CODE_ENUM.PARSE_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: data?.message || DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	#callback(data) {
		this.emit(SignFingerCommon.EVENT_NAME, data)
	}

	removeAllEventListener() {
		this.removeAllListeners(SignFingerCommon.EVENT_NAME)
	}
}

module.exports = SignFingerCommon
