const SignFingerCommon = require('../index')
const EssSignFinger = require('./ess')
const HwSignFinger = require('./hw')
const { ACTION_TYPE_DIC } = require('../../enum')

class SignFinger extends SignFingerCommon {
	#ws = null
	init(config) {
		const boardType = Number(config.boardType)
		if (Object.values(EssSignFinger.BOARD_TYPE_DIC).includes(boardType)) {
			this.#ws = new EssSignFinger()
		} else if (Object.values(HwSignFinger.BOARD_TYPE_DIC).includes(boardType)) {
			this.#ws = new HwSignFinger()
		} else {
			this.handleParamsErrorByAction(ACTION_TYPE_DIC.INIT)
			return
		}
		this.#ws.on(SignFingerCommon.EVENT_NAME, (arg) => {
			this.emit(SignFingerCommon.EVENT_NAME, arg)
			if (arg.action === ACTION_TYPE_DIC.CLOSE) {
				this.removeAllEventListener()
			}
		})
		this.#ws.init(config)
	}
	handle(config) {
		if (!this.#ws) {
			this.handleLinkErrorByAction(config.action)
			return
		}
		this.#ws.handle(config)
	}
	close() {
		if (!this.#ws) {
			this.handleLinkErrorByAction(ACTION_TYPE_DIC.CLOSE)
			this.removeAllEventListener()
			return
		}
		this.#ws.close()
	}
}

module.exports = SignFinger
