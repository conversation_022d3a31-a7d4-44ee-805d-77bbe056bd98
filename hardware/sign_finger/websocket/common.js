const WebSocket = require('ws')
const Logger = require('../../log/index')
const log = new Logger('sign-finger')
const { ACTION_TYPE_DIC } = require('../../enum')

class SignFingerWsCommon {
	#url
	#callback
	#ws = null
	#logger = log
	constructor(url, callback) {
		this.#url = url
		this.#callback = callback
		this.#initWebSocket()
	}

	#initWebSocket() {
		this.#ws = new WebSocket(this.#url)
		this.#ws.on(ACTION_TYPE_DIC.OPEN, () => {
			this.#logger.info('助手回复信息open')
			this.#callback && this.#callback(ACTION_TYPE_DIC.OPEN)
		})
		this.#ws.on(ACTION_TYPE_DIC.MESSAGE, (e) => {
			try {
				const obj = e.toString()
				this.#logger.info('助手回复信息message：')
				this.#callback && this.#callback(ACTION_TYPE_DIC.MESSAGE, JSON.parse(obj))
			} catch (error) {
				this.#callback && this.#callback(ACTION_TYPE_DIC.MESSAGE, null)
			}
		})
		this.#ws.on(ACTION_TYPE_DIC.ERROR, (e) => {
			this.#logger.info('助手回复信息error：', e.toString())
			this.#callback && this.#callback(ACTION_TYPE_DIC.ERROR)
		})
		this.#ws.on(ACTION_TYPE_DIC.CLOSE, () => {
			this.#logger.info('助手回复信息close')
			this.#ws = null
			this.#callback && this.#callback(ACTION_TYPE_DIC.CLOSE)
		})
	}
	sendMessage(options) {
		if (!this.#ws) {
			this.#callback && this.#callback(options.action)
			return
		}
		let msg
		if (typeof options === 'string') {
			msg = options
		} else {
			msg = JSON.stringify(options)
		}
		this.#ws.send(msg)
	}
	closeWebSocket() {
		if (!this.#ws) {
			this.#callback && this.#callback(ACTION_TYPE_DIC.CLOSE)
			return
		}
		this.#ws.close()
	}
}

module.exports = SignFingerWsCommon
