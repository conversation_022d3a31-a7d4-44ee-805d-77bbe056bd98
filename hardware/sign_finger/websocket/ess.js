const SignFingerCommon = require('../index')
const SignFingerWsCommon = require('./common')
const { CODE_ENUM, ACTION_TYPE_DIC } = require('../../enum')
const timeout = '600S'

class EssSignFinger extends SignFingerCommon {
	static BOARD_TYPE_DIC = {
		NET: 0, // 网络模式（GXX-B10款）
		SCREEN: 1, // 投屏模式（GXX-A10款）
		MULTI_USB: 2, // 多模态设备USB模式（GXX-D10款）
		MULTI_NET: 3 // 多模态设备网络模式（GXX-D10款）
	}
	#handleAction = ''
	#ws = null
	#config = {
		boardType: EssSignFinger.BOARD_TYPE_DIC.SCREEN
	}
	#isInit = false
	init(config) {
		const { path = 'ws://localhost:8899', action, boardType } = config
		this.#handleAction = action
		if (!this.#validBoardType(boardType)) {
			return this.handleParamsErrorByAction(this.#handleAction)
		}
		Object.assign(this.#config, config)
		this.#ws = new SignFingerWsCommon(`${path}/ESS`, (type, res) => this.#receiveMessage(type, res))
	}
	handle(config) {
		this.#handleAction = config.action
		const msg = this.#makeDir(config)
		if (!msg) {
			this.handleParamsErrorByAction(config.action)
			return
		}
		this.#ws.sendMessage(msg)
	}
	close() {
		if (this.#ws) {
			this.handle({ action: ACTION_TYPE_DIC.CLOSE })
			this.#ws.closeWebSocket()
		}
		this.#handleAction = ''
		this.#config = null
		this.#isInit = false
		this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		this.removeAllEventListener()
	}
	#validBoardType(boardType) {
		return Object.values(EssSignFinger.BOARD_TYPE_DIC).includes(Number(boardType))
	}
	#makeDir(config) {
		const boardType = Number(config.boardType)
		let msg = {
			action: config.action,
			reqId: Date.now()
		}
		switch (config.action) {
			case ACTION_TYPE_DIC.OPEN:
				/**
				 * 参数说明：
				 * boardType: 签名板类型
				 * ip: 网络模式时，签名板ip地址
				 * port: 网络模式时，签名板监听端口号
				 */
				msg.boardType = this.#config.boardType
				if ([EssSignFinger.BOARD_TYPE_DIC.TYPE_1, EssSignFinger.BOARD_TYPE_DIC.TYPE_4].includes(msg.boardType)) {
					msg.ip = config.ip
					msg.port = Number(config.port)
				}
				break
			case ACTION_TYPE_DIC.CLOSE:
				/**
				 * 参数说明：
				 * type:
				 * 0: 退出正在签名状态（如果正在预览、录制，将退出预览，并且不保存录制文件），不断开签名助手与签名板之间的连接
				 * 1: 退出正在签名状态，并断开签名助手与签名板的连接
				 * 2: 退出或重启整个签名板进程（会退出签正在签名状态，并断开签名助手与签名板的连接，并且退出或重启签名板程序）
				 */
				if ([0, 1, 2].includes(Number(config.type))) {
					msg.type = Number(config.type)
				} else {
					msg.type = 0
				}
				break
			case ACTION_TYPE_DIC.ONLY_SIGN:
				/**
				 * 参数说明：
				 * timeout: 目前支持的时间单位为秒（S）和毫秒（MS），比如"600S"或"600000MS", 默认600S
				 * 超时后处理：退出正在捺印状态（如果正在录制，将退出预览，并且不保存录制文件），不断开签名助手与签名板连接；
				 */
				/**
				 * 消息回复:
				 * 签名板上会打开签名界面 => action: startSign, code: 0
				 * 如果正在签名，再次发送其它指令请求 => action: signBusy, code:403
				 * 在仅签名页面点击[取消]后 => action: cancelSign, code: 440
				 * 签名板上点击[确认]后 => action: signOK, code: 0, data: { httpServerPrefix, signFingerData: [{ signInfo: { signGifData, signGifPath, signHeight, signPngData, signPngPath, signWidth } }] }
				 */
				msg.timeout = config.timeout || timeout
				break
			case ACTION_TYPE_DIC.ONLY_FINGER:
				/**
				 * 参数说明：
				 * timeout: 目前支持的时间单位为秒（S）和毫秒（MS），比如"600S"或"600000MS", 默认600S
				 * 超时后处理：退出正在捺印状态（如果正在录制，将退出预览，并且不保存录制文件），不断开签名助手与签名板连接；
				 */
				/**
				 * 消息回复:
				 * 签名板上会打开捺印界面 => action: startFinger, code: 0
				 * 如果正在捺印，业务系统再次发送其它指令请求 => action: fingerBusy, code:410
				 * 在捺印页面点击[取消]后 => action: cancelFinger, code: 411
				 * 签名板上点击[确认]后 => action: fingerOK, code: 0, data: { httpServerPrefix, signFingerData: [{ fingerInfo: { fingerData, fingerGenData, fingerHeight, fingerPath, fingerWidth } }] }
				 */
				msg.timeout = config.timeout || timeout
				break
			case ACTION_TYPE_DIC.SIGN_FINGER:
				/**
				 * 参数说明：
				 * timeout: 目前支持的时间单位为秒（S）和毫秒（MS），比如"600S"或"600000MS", 默认600S
				 * 超时后处理：退出正在捺印状态（如果正在录制，将退出预览，并且不保存录制文件），不断开签名助手与签名板连接；
				 */
				/**
				 * 消息回复:
				 * 签名板上会打开签名捺印界面 => action: startSignFinger, code: 0
				 * 如果正在签名捺印，业务系统再次发送其它指令请求 => action: signFingerBusy, code:412
				 * 在签名捺印页面点击[取消]后 => action: cancelSignFinger, code: 413
				 * 签名板上点击[确认]后 => action: signFingerOK, code: 0, data: {
				 * httpServerPrefix,
				 * signFingerData: [{
				 * 	signInfo: { signGifData, signGifPath, signHeight, signPngData, signPngPath, signWidth },
				 * 	fingerInfo: { fingerData, fingerGenData, fingerHeight, fingerPath, fingerWidth }
				 * }]
				 * }
				 */
				msg.timeout = config.timeout || timeout
				break
			case ACTION_TYPE_DIC.PDF_ONLY_PREVIEW:
				/**
				 * 参数说明：
				 * srcPdfUrl: 待签名源PDF文件路径（支持本地路径、Http路径）
				 * srcPdfPath: 待签名源PDF文件BASE64格式
				 * srcPdfUrl和srcPdfPath二选一， 优先使用srcPdfUrl字段
				 */
				/**
				 * 消息回复:
				 * 签名板上打开PDF文件后 => action: downloadPDF, code: 0
				 * 在PDF页面点击[退出]后 => action: cancelPreview, code: 425
				 */
				msg.srcPdfUrl = config.srcPdfUrl || ''
				msg.srcPdfBase64 = config.srcPdfBase64 || ''
				msg.timeout = config.timeout || timeout
				break
			case ACTION_TYPE_DIC.PDF_SIGN_PREVIEW:
				/**
				 * 参数说明：
				 * srcPdfUrl: 待签名源PDF文件路径（支持本地路径、Http路径）
				 * srcPdfPath: 待签名源PDF文件BASE64格式
				 * srcPdfUrl和srcPdfPath二选一， 优先使用srcPdfUrl字段
				 * dstPdfFilename: 签名完成后pdf文件名（带后缀名）, 不传时，取当前时间戳作为文件名
				 * forceReadAll: 是否强制看完PDF才能签名捺印（如果没有浏览到PDF最后一页，签名捺印按钮不可用）, 默认false
				 * enableRealityScene: 是否启用实景签名, 默认true
				 * BJCA: 北京CA认证
				 * signers: 签名人信息（目前仅在BJCA中用到）
				 * eviMgrSystemParams: 签名捺印（存证）管理系统参数
				 */
				/**
				 * 消息回复:
				 * 签名板上打开PDF文件后 => action: downloadPDF, code: 0
				 * 在签名板上点击签名或捺印后 => action: startPdfSignFinger, code: 0
				 * 如果正在PDF签名捺印，业务系统再次发送其它指令请求 => action: pdfSignFingerBusy, code: 414
				 * 在PDF页面签名捺印后，点击[提交]，会将签名后PDF保存到本地，同时回复签名捺印数据 => action: pdfSignFingerOK, code: 0, data: {
				 * httpServerPrefix: 1：本地文件（比如录像文件）映射为Http地址时的前缀 2：Http客户端可通过地址：httpServerPrefix + recordUrl，下载录像文件,
				 * pdfData: 签名完成后的PDF文件内容，BASE64格式,
				 * pdfUrl: 签名完成后的PDF文件本地绝对路径,
				 * signFingerData: [{
				 * 	signInfo: {
				 * 		signGifData: 签名轨迹动画GIF文件内容，BASE64格式,
				 * 		signGifPath: 签名轨迹动画GIF文件本地绝对路径,
				 * 		signHeight: 签名高度,
				 * 		signPage: 签名页码,
				 * 		signPngData: 签名PNG图片内容，BASE格式,
				 * 		signPngPath: 签名PNG图片本地绝对路径,
				 * 		signWidth: 签名宽度,
				 * 		signX: 签名位置X坐标,
				 * 		signY: 签名位置Y坐标
				 * 	},
				 * 	fingerInfo: {
				 * 		fingerData: 指纹数据，BASE64格式,
				 * 		fingerGenData: 指纹特征值,
				 * 		fingerHeight: 指纹高度,
				 * 		fingerPage: 捺印页码,
				 * 		fingerPath: 指纹图片文件本地绝对路径,
				 * 		fingerWidth: 指纹宽度,
				 * 		fingerX: 指纹位置X坐标,
				 * 		fingerY: 指纹位置Y坐标
				 * 	}
				 * }]
				 * }
				 */
				msg.srcPdfUrl = config.srcPdfUrl || ''
				msg.srcPdfPath = config.srcPdfPath || ''
				msg.dstPdfFilename = config.dstPdfFilename || ''
				msg.forceReadAll = config.forceReadAll
				msg.enableRealityScene = config.enableRealityScene
				msg.BJCA = config.BJCA || ''
				msg.signers = config.signers || ''
				msg.eviMgrSystemParams = config.eviMgrSystemParams || ''
				msg.timeout = config.timeout || timeout
				break
			case ACTION_TYPE_DIC.CHECK_SIGN_BOARD_STATUS:
				/**
				 * 消息回复:
				 * code:
				 * 0：空闲
				 * 403：正在签名
				 * 410：正在捺印
				 * 412：正在签名捺印
				 * 414：正在PDF签名捺印
				 * 428：正在PDF仅预览
				 * data: {
				 * 	cameras: [] =>摄像头数量（名称）,
				 * 	vender: 签名板厂商信息,
				 * 	pid: 产品型号信息,
				 * 	sn: 签名板唯一序列号
				 * }
				 */
				break
			case ACTION_TYPE_DIC.START_VIDEO_PREVIEW:
				/**
				 * 消息回复:
				 * 成功开启视频预览 => action: 'startVideo', code: 0, data: { videoNum: 'video1' }
				 * 多路摄像头时，data字段中的videoNum依次为video1、video2，多次返回
				 * 如果没有查询到签名板摄像头 => action: 'startVideo', code: 425
				 */
				break
			case ACTION_TYPE_DIC.STOP_VIDEO_PREVIEW:
				/**
				 * 消息回复:
				 * 成功开启视频预览 => action: 'stopVideo', code: 0, data: { videoNum: 'video1' }
				 * 多路摄像头时，data字段中的videoNum依次为video1、video2，多次返回
				 */
				break
			case ACTION_TYPE_DIC.START_RECORD:
				/**
				 * 参数说明：
				 * recordName0: 第一路摄像头录像文件名
				 * recordName1: 第二路摄像头录像文件名
				 * delay: none/pdf_opening/signing/fingering/signing_or_fingering, 默认none
				 * 	none: 不延时，即时录制
				 * 	pdf_opening: 延时到pdf在签名板上打开才开始录制
				 * 	signing: 延时到进入签名模式才开始录制
				 * 	fingering: 延时到进入捺印模式才开始录制
				 * 	signing_or_fingering: 延时到进入签名或捺印模式才开始录制
				 */
				/**
				 * 消息回复:
				 * 成功开启视频预览 => action: 'startRecord', code: 0, data: { videoNum: 'video1' }
				 * 多路摄像头时，data字段中的videoNum依次为video1、video2，多次返回
				 */
				msg.recordName0 = config.recordName0 || '录像1.mp4'
				msg.recordName1 = config.recordName1 || '录像2.mp4'
				msg.delay = config.delay || 'none'
				break
			case ACTION_TYPE_DIC.STOP_RECORD:
				/**
				 * 参数说明：
				 * save: 是否保存录像文件到本地, 默认false
				 * upload: 录像文件上传服务器参数Object
				 * {
				 * 	method："post"或"get"，默认post
				 * 	url：Http服务器的上传服务地址
				 * 	paramList：提供给上传服务的参数列表，除fileFieldName字段之外，其它均为键值对形式。（fileFieldName字段表示录像文件存储到业务系统Http服务器上的文件名的字段名称，录像文件不用传，因为startRecord指令中已经传过了）
				 * 	compress：是否需要将所有录像文件打成一个压缩包，取值false或true，默认false
				 * 	delCacheIfSuccess：上传成功后，是否删除本地缓存的录像文件，取值false或true，默认false
				 * }
				 */
				/**
				 * 消息回复:
				 * 成功停止录制 => action: 'stopRecord', code: 0, data: { videoNum: 'video1' }
				 * 下载录像文件到本地，下载完成后 => action: 'receiveRecord', code: 0, data: { httpServerPrefix, videoNum, url }
				 * 传入自动上传参数并上传成功后 => action: 'uploadRecord', code: 0, data: { response: { body, reson }, videoNum }
				 * 上传失败后 => action: 'uploadRecord', code: 449, data: { response: { body, reson }, videoNum }
				 * 多路摄像头时，data字段中的videoNum依次为video1、video2，多次返回
				 */
				msg.save = config.save
				msg.upload = config.upload || {}
				break
			case ACTION_TYPE_DIC.SET_LOCAL_DIR:
				/**
				 * 参数说明：
				 * boardType: 参考 OPEN
				 * dir: 本地绝对路径（用于存储签名捺印后文件，比如录像文件、签名捺印图片、PDF等）
				 */
				/**
				 * 消息回复:
				 * 成功时 => action: 'setLocalDir', code: 0
				 * 失败时 => action: 'setLocalDir', code: 439
				 */
				if (this.#validBoardType(boardType)) {
					msg.boardType = boardType
				} else {
					msg.boardType = EssSignFinger.BOARD_TYPE_DIC.TYPE_1
				}
				msg.dir = config.dir || ''
				break
			case ACTION_TYPE_DIC.CHECK_LOCAL_DIR:
				/**
				 * 参数说明：
				 * boardType: 参考 OPEN
				 */
				/**
				 * 消息回复:
				 * 成功时 => action: 'localDir', code: 0
				 */
				if (this.#validBoardType(boardType)) {
					msg.boardType = boardType
				} else {
					msg.boardType = EssSignFinger.BOARD_TYPE_DIC.TYPE_1
				}
				break
			default:
				msg = null
				break
		}
		return msg
	}
	#receiveMessage(type, res) {
		switch (type) {
			case ACTION_TYPE_DIC.OPEN:
				this.#isInit = true
				this.handle({ ...this.#config, action: ACTION_TYPE_DIC.OPEN })
				break
			case ACTION_TYPE_DIC.MESSAGE:
				if (!res) {
					return this.handleParseErrorByAction(this.#handleAction)
				}
				const { code, data, message } = res
				let { action } = res
				if (action === ACTION_TYPE_DIC.OPEN) {
					action = ACTION_TYPE_DIC.INIT
				}
				if (code != CODE_ENUM.OK) {
					return this.handleSendMessageErrorByAction(action, { ...data, message })
				}
				this.handleSuccessByAction(action, { ...data, message })
				break
			case ACTION_TYPE_DIC.ERROR:
				if (this.#isInit) {
					this.handleSendMessageErrorByAction(this.#handleAction)
				} else {
					this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
				}
				break
			case ACTION_TYPE_DIC.CLOSE:
				this.#ws = null
				break
			default:
				break
		}
	}
}

module.exports = EssSignFinger
