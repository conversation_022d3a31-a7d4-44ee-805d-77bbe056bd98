const SignFingerCommon = require('../index')
const SignFingerWsCommon = require('./common')
const FingerLibs = require('../libs/index')
const { CODE_ENUM, ACTION_TYPE_DIC } = require('../../enum')
const { delay } = require('../../tools')

class HwSignFinger extends SignFingerCommon {
	static BOARD_TYPE_DIC = {
		HW: 4 // 汉王 签名捺印
	}
	#handleAction = ''
	#signWs = null
	// #fingerWs = null
	#finger = null
	#config = {
		boardType: HwSignFinger.BOARD_TYPE_DIC.HW
	}
	#isInitSign = false
	#isInitFinger = false
	init(config) {
		const { boardType, path = 'ws://localhost:8899', action } = config
		this.#handleAction = action
		if (!this.#validBoardType(boardType)) {
			return this.handleParamsErrorByAction(this.#handleAction)
		}
		Object.assign(this.#config, config)
		this.#signWs = new SignFingerWsCommon(`${path}/pensign`, (type, res) => this.#receiveSignMessage(type, res))
		// this.#finger = new SignFingerWsCommon(`${path}/finger`, (type, res) => this.#receiveFingerMessage(type, res))
		this.#finger = new FingerLibs()
		this.#isInitFinger = this.#finger.init()
		if (this.#isInitFinger) {
			if (this.#isInitSign) {
				this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
			}
		} else {
			this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
			this.#finger = null
		}
	}
	handle(config) {
		this.#handleAction = config.action
		if (this.#handleAction === ACTION_TYPE_DIC.ONLY_SIGN) {
			this.#signWs.sendMessage(ACTION_TYPE_DIC.OPEN)
		} else if (this.#handleAction === ACTION_TYPE_DIC.ONLY_FINGER) {
			// this.#fingerWs.sendMessage(ACTION_TYPE_DIC.OPEN)
			this.#handleFinger()
		} else if (this.#handleAction === ACTION_TYPE_DIC.CLOSE_DEVICES) {
			this.#handleFingerCloseDevices()
		} else {
			this.handleParamsErrorByAction(this.#handleAction)
		}
	}
	close() {
		if (this.#signWs) {
			this.#signWs.closeWebSocket()
		}
		// if (this.#fingerWs) {
		// 	this.#fingerWs.closeWebSocket()
		// }
		if (this.#finger) {
			this.#finger.close()
		}
		this.#handleAction = ''
		this.#config = null
		this.#isInitSign = false
		this.#isInitFinger = false
		this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		this.removeAllEventListener()
	}
	#validBoardType(boardType) {
		return Object.values(HwSignFinger.BOARD_TYPE_DIC).includes(Number(boardType))
	}
	#receiveSignMessage(type, res) {
		switch (type) {
			case ACTION_TYPE_DIC.OPEN:
				this.#isInitSign = true
				if (this.#isInitFinger) {
					this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
				}
				break
			case ACTION_TYPE_DIC.MESSAGE:
				if (!res) {
					return this.handleParseErrorByAction(this.#handleAction)
				}
				const { code, action, data, message } = res
				if (code != CODE_ENUM.OK) {
					return this.handleSendMessageErrorByAction(ACTION_TYPE_DIC.ONLY_SIGN)
				}
				if (action == ACTION_TYPE_DIC.SIGN_FINGER_OK) {
					this.handleSuccessByAction(ACTION_TYPE_DIC.SIGN_FINGER_OK, {
						httpServerPrefix: '',
						signFingerData: [
							{
								signInfo: {
									signGifData: '',
									signGifPath: '',
									signHeight: '',
									signPngData: data,
									signPngPath: '',
									signWidth: ''
								}
							}
						],
						message
					})
				} else {
					this.handleSuccessByAction(action)
				}
				break
			case ACTION_TYPE_DIC.ERROR:
				if (this.#isInitSign) {
					this.handleSendMessageErrorByAction(this.#handleAction)
				} else {
					this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
					this.#signWs = null
				}
				break
			case ACTION_TYPE_DIC.CLOSE:
				this.#signWs = null
				break
			default:
				break
		}
	}
	// #receiveFingerMessage(type, res) {
	// 	switch (type) {
	// 		case ACTION_TYPE_DIC.OPEN:
	// 			this.#isInitFinger = true
	// 			if (this.#isInitSign) {
	// 				this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
	// 			}
	// 			break
	// 		case ACTION_TYPE_DIC.MESSAGE:
	// 			if (!res) {
	// 				return this.handleParseErrorByAction(this.#handleAction)
	// 			}
	// 			const { code, action, data, message } = res
	// 			if (code != CODE_ENUM.OK) {
	// 				return this.handleSendMessageErrorByAction(ACTION_TYPE_DIC.ONLY_FINGER)
	// 			}
	// 			if (action === ACTION_TYPE_DIC.SIGN_FINGER_OK) {
	// 				this.handleSuccessByAction(ACTION_TYPE_DIC.FINGER_OK, {
	// 					httpServerPrefix: '',
	// 					signFingerData: [
	// 						{
	// 							fingerInfo: {
	// 								fingerData: data.paImage || '',
	// 								fingerGenData: '',
	// 								fingerHeight: '',
	// 								fingerPath: '',
	// 								fingerWidth: ''
	// 							}
	// 						}
	// 					],
	// 					message
	// 				})
	// 			} else if (action === ACTION_TYPE_DIC.START_SIGN_FINGER) {
	// 				this.handleSuccessByAction(ACTION_TYPE_DIC.START_FINGER)
	// 			} else {
	// 				this.handleSuccessByAction(action)
	// 			}
	// 			break
	// 		case ACTION_TYPE_DIC.ERROR:
	// 			if (this.#isInitFinger) {
	// 				this.handleSendMessageErrorByAction(this.#handleAction)
	// 			} else {
	// 				this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
	// 				this.#fingerWs = null
	// 			}
	// 			break
	// 		case ACTION_TYPE_DIC.CLOSE:
	// 			this.#fingerWs = null
	// 			break
	// 		default:
	// 			break
	// 	}
	// }
	#handleFingerCloseDevices() {
		if (!this.#isInitFinger) {
			return
		}
		this.#finger.closeDevices()
		this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE_DEVICES)
	}
	#handleFinger() {
		if (!this.#isInitFinger) {
			return
		}
		const bool = this.#finger.openDevices()
		if (!bool) {
			this.handleSendMessageErrorByAction(this.#handleAction)
			return
		}
		this.handleSuccessByAction(ACTION_TYPE_DIC.START_FINGER)
		this.#getFingerInfo()
	}
	async #getFingerInfo() {
		if (!this.#isInitFinger) {
			return
		}
		const res = this.#finger.getFingerInfo()
		if (!res) {
			await delay(200)
			this.#getFingerInfo()
			return
		}
		const { feature = '', image = '' } = res
		this.handleSuccessByAction(ACTION_TYPE_DIC.FINGER_OK, {
			httpServerPrefix: '',
			signFingerData: [
				{
					fingerInfo: {
						fingerData: image || '',
						fingerFeature: feature || '',
						fingerGenData: '',
						fingerHeight: '',
						fingerPath: '',
						fingerWidth: ''
					}
				}
			],
			message: ''
		})
	}
}

module.exports = HwSignFinger
