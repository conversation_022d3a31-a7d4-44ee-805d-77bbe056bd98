const { EventEmitter } = require('events')
const path = require('path')
const ffi = require('ffi-napi')
const ref = require('ref-napi')
const ArrayType = require('ref-array-di')(ref)
const { handleFilePath } = require('../../tools')
const { SYSTEM_PLATFORM_STR_DIC, SYSTEM_ARCH_STR_DIC } = require('../../enum')
const platform = process.platform
const Logger = require('../../log/index')
const log = new Logger('finger')
const fingerDllPath = handleFilePath(path.join(global.extraResources, platform, process.arch, '/finger_sdk/', platform == SYSTEM_PLATFORM_STR_DIC.WIN ? 'GXXFingerprintSDK.dll' : 'libGXXFingerprintSDK.so'))

class FingerLibs extends EventEmitter {
	#fingerMethods = null
	#isOpenDevice = false
	constructor() {
		if (FingerLibs.instance) {
			return FingerLibs.instance
		}
		super()
		FingerLibs.instance = this
	}
	#initFingerMethods() {
		if (platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
			process.env.PATH += `${path.delimiter}${fingerDllPath.substring(0, fingerDllPath.lastIndexOf('/'))}`
		} else if (platform == SYSTEM_ARCH_STR_DIC.ARM64) {
			process.env.PATH += `${path.delimiter}${path.join('/usr/lib/aarch64-linux-gnu')}`
		} else {
			process.env.PATH += `${path.delimiter}${path.join('/usr/lib/x86_64-linux-gnu')}`
		}
		this.#fingerMethods = new ffi.Library(fingerDllPath, {
			// 初始化
			FINGERPRINT_InitSDK: [ref.types.int, []],
			FINGERPRINT_OpenDev: [ref.types.int, []],
			FINGERPRINT_CloseDev: [ref.types.int, []],
			// 获取指纹图片和特征值
			FINGERPRINT_Capture: [ref.types.int, ['pointer', 'pointer', 'pointer', 'pointer']],
			// 特征值比对
			FINGERPRINT_FeatureMatch: [ref.types.int, [ref.types.CString, ref.types.CString]]
		})
	}
	init() {
		if (this.#fingerMethods) {
			log.info('已经初始化SDK')
			return true
		}
		this.#initFingerMethods()
		const ret = this.#fingerMethods.FINGERPRINT_InitSDK()
		log.info(`初始化,ret=${ret}`)
		if (ret != 0) {
			log.info('初始化SDK失败')
			return false
		}
		return true
	}
	openDevices() {
		if (this.#isOpenDevice) {
			log.info('已经打开设备')
			return true
		}
		if (this.#fingerMethods === null) {
			this.#initFingerMethods()
		}
		const ret = this.#fingerMethods.FINGERPRINT_OpenDev()
		if (ret != 0) {
			log.info(`打开设备失败,ret=${ret}`)
			this.#isOpenDevice = false
			return false
		}
		this.#isOpenDevice = true
		return true
	}
	closeDevices() {
		if (!this.#isOpenDevice) {
			log.info('未打开设备')
			return
		}
		if (this.#fingerMethods === null) {
			this.#isOpenDevice = false
			return
		}
		const ret = this.#fingerMethods.FINGERPRINT_CloseDev()
		if (ret != 0) {
			log.info(`关闭设备失败,ret=${ret}`)
		} else {
			this.#isOpenDevice = false
		}
	}
	getFingerInfo() {
		if (this.#fingerMethods === null) {
			this.#initFingerMethods()
		}
		const imageSize = 150 * 1024
		const fingerImageSizePointer = ref.alloc(ref.types.int)
		const fingerImagePointer = ref.alloc(ArrayType(ref.types.char, imageSize))
		const featureSize = 4 * 1024
		const fingerFeatureSizePointer = ref.alloc(ref.types.int)
		const fingerFeaturePointer = ref.alloc(ArrayType(ref.types.char, featureSize))
		const ret = this.#fingerMethods.FINGERPRINT_Capture(fingerImagePointer, fingerImageSizePointer, fingerFeaturePointer, fingerFeatureSizePointer)
		if (ret != 0) {
			log.info(`获取指纹信息失败,ret=${ret}`)
			return null
		}
		const featureLen = fingerFeatureSizePointer.deref()
		const imageLen = fingerImageSizePointer.deref()
		log.info(`特征值长度：${featureLen}, base64长度：${imageLen}`)
		return {
			feature: fingerFeaturePointer.toString().substring(0, featureLen),
			image: fingerImagePointer.toString().substring(0, imageLen)
		}
	}
	fingerFeatureComparison(f1, f2) {
		if (this.#fingerMethods === null) {
			this.#initFingerMethods()
		}
		const ret = this.#fingerMethods.FINGERPRINT_FeatureMatch(f1, f2)
		log.info('指纹比对ret=', ret)
		return ret
	}
	close() {
		this.closeDevices()
		this.#fingerMethods = null
	}
}

module.exports = FingerLibs
