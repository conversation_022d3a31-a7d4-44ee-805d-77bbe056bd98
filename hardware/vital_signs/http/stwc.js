const VitalSignsCommon = require('../index')
const http = require('../../lib/request')
const { ACTION_TYPE_DIC } = require('../../enum')
let requestUrl = '127.0.0.1:7382'

class VitalSigns extends VitalSignsCommon {
	#timer = null
	init(options) {
		requestUrl = options.path || requestUrl
		this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
		this.#timer = setInterval(() => {
			this.#phGetResult()
		}, 1000)
	}
	#sendMessage(url) {
		return http({
			method: 'get',
			url
		})
	}
	#phGetResult() {
		const url = `http://${requestUrl}/ph/v1/phGetResult?unitCode=111111&version=v1.1.3`
		let promise = this.#sendMessage(url)
		promise
			.then(
				(res) => {
					const { actioncode, data } = res
					this.logger.info(`STWC HTTP res: ${JSON.stringify(res)}`)
					if (actioncode != 'SUCCESS') {
						this.handleSendMessageErrorByAction(ACTION_TYPE_DIC.VITAL_SIGNS_VALUE, { isCheckPerson: false, heartRate: 0, spo2: 0, br: 0 })
					} else {
						/**
						 * have_face: 是否检测到人脸
						 * 1: 有人脸
						 * 0: 未检测到人脸
						 */
						this.handleSuccessByAction(ACTION_TYPE_DIC.VITAL_SIGNS_VALUE, { isCheckPerson: data.have_face == 1, heartRate: data.hr || 0, spo2: data.spo2 || 0, br: data.br || 0 })
					}
				},
				(err) => {
					this.logger.error(`STWC HTTP err: ${JSON.stringify(err)}`)
					this.handleSendMessageErrorByAction(ACTION_TYPE_DIC.VITAL_SIGNS_VALUE, { isCheckPerson: false, heartRate: 0, spo2: 0, br: 0 })
				}
			)
			.finally(() => {
				promise = null
			})
	}
	close() {
		if (this.#timer) {
			clearInterval(this.#timer)
		}
		this.#timer = null
		this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		this.removeAllEventListener()
	}
}

module.exports = VitalSigns
