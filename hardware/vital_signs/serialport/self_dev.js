const SerialPort = require('serialport')
const { getSerialPortList, delay } = require('../../tools')
const VitalSignsCommon = require('../index')
const { ACTION_TYPE_DIC } = require('../../enum')

class VitalSigns extends VitalSignsCommon {
	#path = ''
	#newPath = ''
	#port = null // 串口连接对象
	#port1 = null // 串口连接对象
	#baudRate = 38400 // 波特率
	#runList = [] // 执行数组
	#isRun = false // 是否执行
	#dataTimer = null // 硬件模块-数据推送定时器
	#comList = [] // 记录未open-usb前的串口信息
	#isStartCollection = false // 是否已开启采集信息
	/**
	 * 初始化
	 */
	async init(options) {
		this.#path = options.path
		this.#baudRate = Number(options.baudRate) || this.#baudRate
		this.#comList = (await getSerialPortList()).map((item) => item.value)
		const { success, port } = await this.#openPort(this.#path)
		if (!success) {
			return this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
		}
		this.#port1 = port
		this.#port1Message()
		this.#port1Handle({ actionType: ACTION_TYPE_DIC.OPEN_USB, action: ACTION_TYPE_DIC.INIT })
	}
	/**
	 * 打开串口
	 */
	#openPort(path) {
		return new Promise((resolve) => {
			const port = new SerialPort(path, { baudRate: this.#baudRate }, (err) => {
				if (err) {
					this.logger.info(`${path}串口连接失败：${JSON.stringify(err)}`)
				}
				resolve({ port: !err ? port : null, success: !err })
			})
		})
	}
	/**
	 * 关闭串口
	 */
	#closePort(port) {
		return new Promise((resolve) => {
			if (!port) {
				resolve(false)
			}
			port.close((err) => {
				if (err) {
					this.logger.info(`销毁连接失败: ${err}`)
				}
				resolve(!err)
			})
		})
	}
	/**
	 * 串口1操作指令
	 */
	#port1Handle(options) {
		if (!this.#port1) {
			return
		}
		const dirObj = {
			dir: [],
			action: options.action,
			port: 'port1'
		}
		switch (options.actionType) {
			case ACTION_TYPE_DIC.OPEN_USB:
				dirObj.dir = [0xa0, 0x01, 0x03, 0xa4]
				break
			case ACTION_TYPE_DIC.CLOSE_USB:
				dirObj.dir = [0xa0, 0x01, 0x02, 0xa3]
				break
			default:
				break
		}
		this.#runList.push(dirObj)
		!this.#isRun && this.#run()
	}
	/**
	 * 串口1接收数据
	 */
	#port1Message() {
		let backData = []
		this.#port1.on('readable', async () => {
			const res = this.#port1.read()
			if (backData) {
				backData = backData.concat([...res])
			} else {
				backData = [...res]
			}
			this.logger.info(`${this.#path} data: ${JSON.stringify(backData)}`)
			// 开启第一路USB开关并反馈：[0xa0, 0x01, 0x01, 0xa4]
			if (backData.length >= 4 && backData[0] == 160 && backData[1] == 1 && backData[2] == 1 && backData[3] == 162) {
				backData = []
				this.#openPort1After()
			}
			// 关闭第一路USB开关并反馈：[0xa0, 0x01, 0x00, 0xa1]
			if (backData.length >= 4 && backData[0] == 160 && backData[1] == 1 && backData[2] == 0 && backData[3] == 161) {
				backData = []
				await this.#closePort(this.#port1)
			}
		})
		this.#port1.on('close', () => {
			this.logger.info(`close ${this.#path}`)
			this.#closePort1After()
		})
	}
	/**
	 * 串口1 USB通电后
	 */
	async #openPort1After() {
		// 延时目的：等待设备通电完成
		await delay(500)
		this.#newPath = await this.#findPort()
		if (!this.#newPath) {
			this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
			this.#closePort(this.#port1)
			return
		}
		const { success, port } = await this.#openPort(this.#newPath)
		if (!success) {
			this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
			this.#closePort(this.#port1)
			return
		}
		this.#port = port
		this.#portMessage()
		this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
	}
	/**
	 * 查找串口
	 */
	#findPort() {
		return new Promise(async (resolve) => {
			if (this.#newPath) {
				return resolve(this.#newPath)
			}
			const comList = (await getSerialPortList()).map((item) => item.value)
			const newPath = comList.find((com) => !this.#comList.includes(com))
			if (newPath) {
				return resolve(newPath)
			}
			const index = comList.findIndex((com) => com == this.#path)
			if (index == -1 || index == comList.length - 1) {
				return resolve('')
			}
			resolve(comList[comList.length - 1])
		})
	}
	/**
	 * 串口1 USB断电后 / 串口1关闭
	 */
	async #closePort1After() {
		this.#port1 = null
		this.#path = ''
		this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		if (this.#port) {
			await this.#closePort(this.#port)
		}
		this.#isStartCollection = false
		this.#runList = []
		this.#comList = []
		this.#isRun = false
		this.removeAllEventListener()
		this.#closeTimer()
	}
	/**
	 * 串口操作指令
	 */
	handle(options) {
		if (!this.#port) {
			return
		}
		const dirObj = {
			dir: [],
			action: options.action,
			port: 'port'
		}
		switch (options.action) {
			case ACTION_TYPE_DIC.START_COLLECTION:
				if (this.#isStartCollection) {
					return
				}
				this.#isStartCollection = true
				dirObj.dir = [0x8a]
				break
			case ACTION_TYPE_DIC.END_COLLECTION:
				if (!this.#isStartCollection) {
					return
				}
				this.#isStartCollection = false
				dirObj.dir = [0x88]
				break
			default:
				break
		}
		if (!dirObj.dir.length) {
			return
		}
		this.#runList.push(dirObj)
		!this.#isRun && this.#run()
	}
	/**
	 * 销毁串口
	 */
	async close() {
		if (this.#isStartCollection) {
			this.handle({ actionType: ACTION_TYPE_DIC.END_COLLECTION })
		}
		const bool = await this.#closePort(this.#port)
		if (!bool) {
			return this.handleCloseErrorByAction(ACTION_TYPE_DIC.CLOSE)
		}
	}
	/**
	 * 执行指令
	 */
	async #run() {
		if (this.#runList.length === 0) {
			this.#isRun = false
			return
		}
		this.#isRun = true
		const dirObj = JSON.parse(JSON.stringify(this.#runList[0]))
		const bool = await this.#sendMessage(dirObj)
		this.#runList.shift()
		if (dirObj.action == ACTION_TYPE_DIC.END_COLLECTION) {
			if (bool) {
				this.#closeTimer()
				return this.handleSuccessByAction(dirObj.action)
			} else {
				return this.handleSendMessageErrorByAction(dirObj.action)
			}
		}
		this.#run()
	}
	/**
	 * 向串口发送数据
	 * @param {Object} dirObj
	 * @returns Promise
	 */
	#sendMessage(dirObj) {
		return new Promise((resolve) => {
			let port = this.#port
			if (dirObj.port == 'port1') {
				port = this.#port1
			}
			if (!port) {
				this.handleLinkErrorByAction(dirObj.action)
				return resolve(false)
			}
			port.write(this.#dealCommand(dirObj.dir), (err) => {
				if (err) {
					this.logger.info(`发送指令: ${dirObj.dir} 失败: ${JSON.stringify(err)}`)
					this.handleSendMessageErrorByAction(dirObj.action)
				}
				resolve(!err)
			})
		})
	}
	/**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
	#dealCommand(command) {
		const newCommand = command.map((num) => {
			return this.#decToHex(num)
		})
		const cmdBuffer = Buffer.from(newCommand.join(''), 'hex')
		this.logger.info('指令:', JSON.stringify(cmdBuffer))
		return cmdBuffer
	}
	/**
	 * 十进制 转 十六进制字符串
	 * @param {Number} num
	 * @returns
	 */
	#decToHex(num) {
		let numHex = num.toString(16)
		numHex.length < 2 && (numHex = `0${numHex}`)
		return numHex
	}
	/**
	 * 接收串口返回数据
	 */
	#portMessage() {
		let backData = []
		this.#createTimer()
		this.#port.on('readable', () => {
			this.#closeTimer()
			const res = this.#port.read()
			if (backData) {
				backData = backData.concat([...res])
			} else {
				backData = [...res]
			}
			if (backData.length >= 88 && backData[0] == 0xff) {
				const state = backData[87] // 模块状态state:当模块检测区未检测到人体时，该字节的bit2会置1(起始为bit0)
				const bit = (state >> 2) & 1
				const isCheckPerson = bit != 1 // 是否检测到有人体
				const data = {
					isCheckPerson,
					heartRate: backData[65] || 0, // 心率
					spo2: backData[66] || 0, // 血氧
					bk: backData[67] || 0, // 微循环
					systolicPressure: backData[71] || 0, // 收缩压
					diastolicPressure: backData[72] || 0 // 舒张压
				}
				this.logger.info(`${this.#newPath} data: ${JSON.stringify(data)}`)
				backData = []
				this.handleSuccessByAction(ACTION_TYPE_DIC.VITAL_SIGNS_VALUE, data)
			}
			this.#createTimer()
		})
		this.#port.on('close', () => {
			this.logger.info(`close ${this.#newPath}`)
			this.#runList = []
			this.#isRun = false
			this.#port = null
			this.#newPath = ''
			this.#port1Handle({ actionType: ACTION_TYPE_DIC.CLOSE_USB, action: ACTION_TYPE_DIC.CLOSE })
		})
	}
	/**
	 * 创建定时器
	 */
	#createTimer() {
		this.#closeTimer()
		this.#dataTimer = setInterval(() => {
			this.close()
		}, 30 * 1000)
	}
	/**
	 * 销毁定时器
	 */
	#closeTimer() {
		this.#dataTimer && clearInterval(this.#dataTimer)
		this.#dataTimer = null
	}
}

module.exports = VitalSigns
