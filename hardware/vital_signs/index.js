const { EventEmitter } = require('events')
const Logger = require('../log/index')
const log = new Logger('vital-signs')
const { CODE_DIC, CODE_ENUM, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../enum')

class VitalSigns<PERSON><PERSON><PERSON> extends EventEmitter {
	constructor() {
		super()
		this.logger = log
	}

	handleSuccessByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.OK,
			msg: CODE_DIC[CODE_ENUM.OK],
			data: {
				flag: DATA_CODE_ENUM.ACTION_SUCCESS,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_SUCCESS],
				...data
			}
		})
	}

	handleLinkErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.LINK_ERROR,
			msg: CODE_DIC[CODE_ENUM.LINK_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleSendMessageErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.SEND_MESSAGE_ERROR,
			msg: CODE_DIC[CODE_ENUM.SEND_MESSAGE_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleParamsErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.PARAMS_ERROR,
			msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	handleCloseErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.CLOSE_ERROR,
			msg: CODE_DIC[CODE_ENUM.CLOSE_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}

	#callback(data) {
		this.emit('vital-signs-callback', data)
	}

	removeAllEventListener() {
		this.removeAllListeners('vital-signs-callback')
	}
}

module.exports = VitalSignsCommon
