const SerialPort = require('serialport')
const QrCodeCommon = require('../index')
const { ACTION_TYPE_DIC } = require('../../enum')

class QrCode extends QrCodeCommon {
	constructor() {
		super()
		this.port = null
		this.com = ''
		this.baudRate = 9600
		this.qrCodeVal = ''
	}
	/**
	 * 初始化
	 * @param {String} com com
	 * @param {Number} baudRate 波特率
	 */
	init(com, baudRate) {
		this.com = com
		this.baudRate = parseInt(baudRate)
		this.port = new SerialPort(this.com, { baudRate: this.baudRate, dataBits: 8 }, (err) => {
			if (err) {
				this.logger.error('串口连接失败：', JSON.stringify(err))
				return this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
			}
			this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
			this.#receiveMessage()
		})
	}
	/**
	 * 接收串口返回数据
	 */
	#receiveMessage() {
		this.port.on('data', (data) => {
			this.logger.info('data:', data)
			const bytes = new Uint8Array(
				data
					.toString('hex')
					.match(/.{1,2}/g)
					.map((byte) => parseInt(byte, 16))
			)
			const decoder = new TextDecoder('utf-8')
			const hexStr = decoder.decode(bytes).replace('\r', '')
			this.qrCodeVal += hexStr
			if (!this.qrCodeVal.endsWith('\n')) {
				return
			}
			this.handleSuccessByAction(ACTION_TYPE_DIC.MESSAGE, { result: this.qrCodeVal })
			this.qrCodeVal = ''
		})
	}
	/**
	 * 销毁
	 */
	close() {
		if (!this.port) {
			return this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		}
		this.port.close((error) => {
			if (error) {
				this.logger.info('销毁连接成功')
				return this.handleCloseErrorByAction(ACTION_TYPE_DIC.CLOSE)
			}
			this.logger.info('销毁连接成功')
			this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
			this.port = null
			this.removeAllEventListener()
			this.qrCodeVal = ''
		})
	}
}

module.exports = QrCode
