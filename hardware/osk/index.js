const ffi = require('ffi-napi')
const path = require('path')
const { handleFilePath } = require('../tools')
const { SYSTEM_PLATFORM_STR_DIC } = require('../enum')
const platform = process.platform

class Osk {
	constructor() {
		if (Osk.instance) {
			return
		}
		Osk.instance = null
	}
	init() {
		if (platform != SYSTEM_PLATFORM_STR_DIC.WIN) {
			return
		}
		const oskDllPath = handleFilePath(path.join(global.extraResources, platform, process.arch, 'osk', 'CallOsk.dll'))
		Osk.instance = new ffi.Library(oskDllPath, {
			// 文件内的方法和参数类型
			CallOsk: ['int', []]
		})
	}
	open() {
		if (Osk.instance) {
			Osk.instance.CallOsk()
		} else {
			this.init()
		}
	}
}

module.exports = Osk
