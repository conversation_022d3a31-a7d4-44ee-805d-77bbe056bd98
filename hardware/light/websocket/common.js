const LightCommon = require('../index')
const WebSocket = require('ws')
const { ACTION_TYPE_DIC } = require('../../enum')

class LightWsCommon extends LightCommon {
	constructor() {
		super()
		this.ws = null
	}

	// 初始化websocket
	initWebscoket(wsUrl, callback) {
		if (!wsUrl) {
			return this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
		}
		this.ws = new WebSocket(wsUrl)

		//连接成功建立的回调方法
		this.ws.on('open', () => {
			this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
		})

		//接收到消息的回调方法
		this.ws.on('message', (e) => {
			const obj = e.toString()
			callback && callback(obj)
		})

		//连接发生错误的回调方法
		this.ws.on('error', () => {
			this.handleLinkErrorByAction(ACTION_TYPE_DIC.ERROR)
		})

		//连接关闭的回调方法
		this.ws.on('close', () => {
			this.ws = null
			this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
			this.removeAllEventListener()
		})
	}

	// 发送数据
	sendMessage(msg) {
		if (!this.ws) {
			return this.handleLinkErrorByAction(msg.action)
		}
		const text = JSON.stringify(msg)
		this.ws.send(text)
	}

	/**
	 * 接收返回数据
	 */
	receiveMessage() {}

	// 关闭连接
	closeWebsocket() {
		if (!this.ws) {
			return this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		}
		this.ws.close()
	}
}

module.exports = LightWsCommon
