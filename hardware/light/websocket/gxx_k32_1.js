// 酷安 - 补光灯
const LightWsCommon = require('./common')
const { ACTION_TYPE_DIC, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../../enum')
const defaultUrlMap = {
	win32_x64: 'ws://localhost:24246/light',
	win32_ia32: 'ws://localhost:24246/light',
	linux_x64: 'ws://localhost:8540/light',
	linux_arm64: 'ws://localhost:8540/light'
}

class Light extends LightWsCommon {
	constructor() {
		super()
		this.handleAction = ''
	}
	// 连接补光灯服务
	init(url) {
		this.initWebscoket(url || defaultUrlMap[`${process.platform}_${process.arch}`], (res) => {
			this.receiveMessage(res) // 监听ws返回信息
		})
	}
	/**
	 * 开/关 补光灯
	 * @param {Object} config
	 * @param {String} config.action 开 / 关
	 */
	handle(config) {
		this.handleAction = config.action
		const msg = this.#makeDir(config.action)
		this.sendMessage(msg)
	}
	// 组装指令
	#makeDir(actionType) {
		const msg = {
			action: actionType,
			ActionType: '',
			RequestTime: Date.now()
		}
		switch (actionType) {
			case ACTION_TYPE_DIC.OPEN_LIGHT:
			case ACTION_TYPE_DIC.CLOSE_LIGHT:
				msg.ActionType = actionType.toLowerCase()
				break
			default:
				msg.ActionType = ''
				break
		}
		return msg
	}
	// 接收补光灯ws返回信息
	receiveMessage(res) {
		let data = {}
		try {
			data = JSON.parse(res)
		} catch (error) {
			return this.handleParseErrorByAction(this.handleAction)
		}
		const { success } = data
		const result = `补光灯${this.handleAction === ACTION_TYPE_DIC.CLOSE_LIGHT ? '关闭' : '开启'}${success ? '成功' : '失败'}`
		this.handleSuccessByAction(this.handleAction, {
			flag: success ? DATA_CODE_ENUM.ACTION_SUCCESS : DATA_CODE_ENUM.ACTION_ERROR,
			message: success ? DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_SUCCESS] : DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
			result
		})
	}
	// 关闭补光灯ws连接
	close() {
		this.closeWebsocket()
	}
}

module.exports = Light
