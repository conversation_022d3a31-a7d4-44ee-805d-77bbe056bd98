const LightCommon = require('../index')
const SerialPort = require('serialport')
const { ACTION_TYPE_DIC } = require('../../enum')

class LightSerialPortCommon extends LightCommon {
	constructor() {
		super()
		this.port = null
		this.baudRate = 9600
		this.com = ''
	}
	/**
	 * 初始化
	 * @param {String} com com
	 * @param {Number} baudRate 波特率
	 */
	init(com, baudRate) {
		this.com = com
		this.baudRate = parseInt(baudRate)
		this.port = new SerialPort(this.com, { baudRate: this.baudRate }, (err) => {
			if (err) {
				this.logger.info('串口连接失败：', err)
				this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
			} else {
				this.logger.info('端口打开成功！')
				this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
			}
		})
	}
	handle() {}
	/**
	 * 销毁
	 */
	close() {}
}

module.exports = LightSerialPortCommon
