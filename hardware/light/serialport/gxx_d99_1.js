const LightSerialPortCommon = require('./common')
const { ACTION_TYPE_DIC } = require('../../enum')

// 迪川
class Light extends LightSerialPortCommon {
	constructor() {
		super()
		this.baudRate = 9600 // 比特率
	}
	/**
	 * 开/关 补光灯
	 * @param {Object} config
	 * @param {String} config.action 开 / 关
	 * @param {Number} config.address 锁板地址
	 * @param {Number} config.door 锁号
	 */
	handle(config) {
		if (!this.port) {
			return this.handleLinkErrorByAction(config.action)
		}
		const actionType = config.action
		const lockPlate = parseInt(config.address)
		const lockNumber = parseInt(config.door)
		if (lockNumber) {
			this.sendMessage({ action: config.action, dir: [0x88, lockPlate, 0x4a, 0x04, lockNumber, 0xff, 0xff, actionType == ACTION_TYPE_DIC.OPEN_LIGHT ? 0x01 : 0x00] })
		} else {
			this.handleParamsErrorByAction(config.action)
		}
	}
	/**
	 * 向串口发送数据
	 * @param {Object} dir
	 * @returns Promise
	 */
	sendMessage(args) {
		this.port.write(this.dealCommand(args.dir), (err) => {
			const result = `补光灯${args.action === ACTION_TYPE_DIC.CLOSE_LIGHT ? '关闭' : '开启'}${!err ? '成功' : '失败'}`
			if (err) {
				this.logger.error(`发送指令失败: `, JSON.stringify(err))
				this.handleSendMessageErrorByAction(args.action, { result })
			} else {
				this.logger.info('发送指令成功: ', JSON.stringify(args.dir))
				this.handleSuccessByAction(args.action, { result })
			}
		})
	}
	/**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
	dealCommand(command) {
		const verifyCode = command.reduce((total, value) => {
			return total ^ value
		}, 0)
		command.push(verifyCode)
		command.push(0x68)
		const newCommand = command.map((num) => {
			return this.decToHex(num)
		})
		const cmdBuffer = Buffer.from(newCommand.join(''), 'hex')
		return cmdBuffer
	}
	/**
	 * 十进制 转 十六进制字符串
	 * @param {Number} num
	 * @returns
	 */
	decToHex(num) {
		let numHex = num.toString(16)
		numHex.length < 2 && (numHex = `0${numHex}`)
		return numHex
	}
	close() {
		if (!this.port) {
			return this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		}
		this.port.close((error) => {
			if (error) {
				this.handleCloseErrorByAction(ACTION_TYPE_DIC.CLOSE)
			} else {
				this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
				this.port = null
				this.removeAllEventListener()
			}
		})
	}
}

module.exports = Light
