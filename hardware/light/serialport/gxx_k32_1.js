const LightSerialPortCommon = require('./common')
const { ACTION_TYPE_DIC } = require('../../enum')

const dir = {
	openLight: '01050000FF008C3A',
	closeLight: '010500000000CDCA'
}

class Light extends LightSerialPortCommon {
	constructor() {
		super()
		this.baudRate = 9600 // 比特率
	}
	/**
	 * 开/关 补光灯
	 * @param {Object} config
	 * @param {String} config.action 开 / 关
	 */
	handle(config) {
		if (!this.port) {
			return this.handleLinkErrorByAction(config.action)
		}
		switch (config.action) {
			case ACTION_TYPE_DIC.OPEN_LIGHT:
			case ACTION_TYPE_DIC.CLOSE_LIGHT:
				this.port.write(dir[config.action], 'hex', (err) => {
					const result = `补光灯${config.action === ACTION_TYPE_DIC.CLOSE_LIGHT ? '关闭' : '开启'}${!err ? '成功' : '失败'}`
					if (err) {
						this.handleSendMessageErrorByAction(config.action, { result })
					} else {
						this.handleSuccessByAction(config.action, { result })
					}
				})
				break
			default:
				this.handleParamsErrorByAction(config.action)
				break
		}
	}
	close() {
		if (!this.port) {
			return this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		}
		this.port.close((error) => {
			if (error) {
				this.handleCloseErrorByAction(ACTION_TYPE_DIC.CLOSE)
			} else {
				this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
				this.port = null
				this.removeAllEventListener()
			}
		})
	}
}

module.exports = Light
