const { CODE_ENUM, ACTION_TYPE_DIC } = require('../enum')
const KuanSpLockServer = require('./serialport/gxx_k32_1')
const kuanSpLockServer = new KuanSpLockServer()
const KuanWsLockServer = require('./websocket/gxx_k32_1')
const kuanWsLockServer = new KuanWsLockServer()
const JiJiaSpLockServer = require('./serialport/gxx_j88_1')
const jiJiaSpLockServer = new JiJiaSpLockServer()
const DiChuanSpLockServer = require('./serialport/gxx_d99_1')
const diChuanSpLockServer = new DiChuanSpLockServer()
const { EventEmitter } = require('events')
const lockMap = {
	1: kuanSpLockServer,
	2: jiJiaSpLockServer,
	3: diChuanSpLockServer,
	4: kuanWsLockServer
}

class onlyLock extends EventEmitter {
	#lock = null
	constructor() {
		if (onlyLock.instance) {
			return onlyLock.instance
		}
		super()
		onlyLock.instance = this
	}
	initLink(params) {
		// 查询主柜配置信息
		let mainLockInfo = {}
		for (const key in global.serverConfig.lockInfo) {
			if (global.serverConfig.lockInfo[key].isMain) {
				mainLockInfo = global.serverConfig.lockInfo[key]
			}
		}
		params = Object.assign(mainLockInfo, params)
		if (!params.path || !Object.keys(lockMap).includes(String(params.manufacturer))) {
			return this.#callback({ action: ACTION_TYPE_DIC.INIT, code: CODE_ENUM.PARAMS_ERROR })
		}
		if (this.#lock) {
			return this.#callback({ action: ACTION_TYPE_DIC.INIT, code: CODE_ENUM.OK })
		}
		this.#lock = lockMap[params.manufacturer]
		this.#lock.on('lock-callback', (arg) => {
			const { action, code } = arg
			if (action == ACTION_TYPE_DIC.INIT) {
				code != CODE_ENUM.OK && this.removeListeners()
			} else if (action == ACTION_TYPE_DIC.CLOSE) {
				this.removeListeners()
			}
			this.#callback(arg)
		})
		this.#lock.init(params.path, params.baudRate)
	}
	openLock(params) {
		if (!this.#lock) {
			return this.#callback({ action: params.action, code: CODE_ENUM.PARAMS_ERROR })
		}
		const openLockList = params.openLockList || []
		if (openLockList.length == 0) {
			return this.#callback({ action: params.action, code: CODE_ENUM.PARAMS_ERROR })
		}
		openLockList.forEach((item) => {
			if (String(item.address).length > 0 && item.door > 0) {
				item.action = params.action
				this.#lock.openLock(item)
			} else {
				this.#callback({ action: params.action, code: CODE_ENUM.PARAMS_ERROR })
			}
		})
	}
	checkLockStatus(params) {
		if (!this.#lock) {
			return this.#callback({ action: params.action, code: CODE_ENUM.PARAMS_ERROR })
		}
		if (String(params.address).length > 0 && params.door > 0) {
			this.#lock.checkLockStatus(params)
		} else {
			this.#callback({ action: params.action, code: CODE_ENUM.PARAMS_ERROR })
		}
	}
	closeLink() {
		if (this.#lock) {
			this.#lock.close()
		} else {
			this.#callback({ action: ACTION_TYPE_DIC.CLOSE, code: CODE_ENUM.OK })
		}
	}
	removeListeners() {
		this.#lock.removeAllListeners('lock-callback')
		this.#lock = null
	}
	#callback(data) {
		this.emit('only-lock-callback', data)
	}
}

module.exports = onlyLock
