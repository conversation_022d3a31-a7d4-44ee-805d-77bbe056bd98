const LockCommon = require('../index')
const SerialPort = require('serialport')
const { ACTION_TYPE_DIC, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../../enum')
const { delay } = require('../../tools')

class LockSerialPortCommon extends LockCommon {
	#delay = 0
	#path = ''
	constructor() {
		super()
		this.port = null // 串口连接对象
		this.baudRate = 57600 // 比特率
		this.currentDirObj = null // 当前执行指令对象
		this.backData = [] // 数据结果
	}
	/**
	 * 初始化
	 * @param {String} path com
	 * @param {Number} baudRate 波特率
	 */
	init(path, baudRate) {
		this.#path = path
		this.baudRate = parseInt(baudRate)
		this.port = new SerialPort(this.#path, { baudRate: this.baudRate }, (err) => {
			if (err) {
				this.logger.error('串口连接失败：', JSON.stringify(err))
				this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
			} else {
				this.logger.info('端口打开成功！')
				this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
				this.receiveMessage()
			}
		})
	}
	/**
	 * 执行指令
	 */
	async run() {
		if (this.runList.length === 0) {
			this.isRun = false
			return
		}
		this.isRun = true
		const dirObj = JSON.parse(JSON.stringify(this.runList[0]))
		this.runList.shift()
		await delay(dirObj.delay || this.#delay)
		const isSend = await this.#sendMessage(dirObj)
		if (!isSend) {
			this.run()
			return
		}
		const actionType = this.getActionType(dirObj.action)
		this.isAwaitResult = !!actionType
		if (!this.isAwaitResult) {
			this.run()
			return
		}
		this.currentDirObj = JSON.parse(JSON.stringify(dirObj))
		const bool = await this.addInterval()
		if (!bool) {
			this.currentDirObj = null
			this.handleNoResponseErrorByAction(actionType, {
				address: dirObj.lockPlate,
				door: dirObj.lockNumber,
				result: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			})
		}
		this.run()
	}
	/**
	 * getActionType
	 * @param {String} type
	 * @returns String
	 */
	getActionType(type) {
		let actionType = ''
		switch (type) {
			case ACTION_TYPE_DIC.CHECK_LOCK:
				actionType = ACTION_TYPE_DIC.CHECK_LOCK
				break
			case ACTION_TYPE_DIC.OPEN_LOCK_CHECK:
				actionType = ACTION_TYPE_DIC.OPEN_LOCK
				break
			default:
				break
		}
		return actionType
	}
	/**
	 * 接收串口返回数据
	 */
	receiveMessage() {}
	/**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
	dealCommand() {}
	/**
	 * 向串口发送数据
	 * @param {Object} dirObj
	 * @returns Promise
	 */
	#sendMessage(dirObj) {
		return new Promise((resolve) => {
			const { action, dir } = dirObj
			if (!this.port) {
				this.handleLinkErrorByAction(action, {
					address: dirObj.lockPlate,
					door: dirObj.lockNumber,
					result: DATA_CODE_ENUM.ACTION_ERROR,
					resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
				})
				return resolve(false)
			}
			const newDir = this.dealCommand(dir)
			this.port.write(newDir, (err) => {
				if (err) {
					this.logger.error(`发送指令: ${newDir} 失败: ${JSON.stringify(err)}`)
					this.handleSendMessageErrorByAction(action, {
						address: dirObj.lockPlate,
						door: dirObj.lockNumber,
						result: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
						resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
					})
				}
				resolve(!err)
			})
		})
	}
	/**
	 * 十进制 转 十六进制字符串
	 * @param {Number} num
	 * @returns
	 */
	decToHex(num) {
		let numHex = num.toString(16)
		numHex.length < 2 && (numHex = `0${numHex}`)
		return numHex
	}
	/**
	 * 销毁
	 */
	close() {
		if (!this.port) {
			this.currentDirObj = null
			this.backData = []
			return this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		}
		this.port.close((error) => {
			if (error) {
				this.logger.info('销毁连接失败:', JSON.stringify(error))
				this.handleCloseErrorByAction(ACTION_TYPE_DIC.CLOSE)
			} else {
				this.logger.info('销毁连接成功')
				this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
				this.port = null
				this.currentDirObj = null
				this.backData = []
				this.removeAllEventListener()
			}
		})
	}
}
module.exports = LockSerialPortCommon
