const LightSerialPortCommon = require('./common')
const { ACTION_TYPE_DIC, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../../enum')

class Lock extends LightSerialPortCommon {
	#doorCount = 21 // 一个锁板最大柜数
	constructor() {
		super()
		this.baudRate = 9600 // 比特率
	}
	/**
	 * 开锁
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 */
	openLock(config) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)
		if (lockNumber >= this.#doorCount || lockNumber < 0) {
			return this.handleParamsErrorByAction(ACTION_TYPE_DIC.OPEN_LOCK, {
				address: lockPlate,
				door: lockNumber,
				result: DATA_CODE_ENUM.ACTION_ERROR,
				resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			})
		}
		const dirObj = {
			lockPlate,
			lockNumber,
			dir: [0x88, lockPlate, 0x8a, 0x04, lockNumber, 0x00, 0x00, 0x00],
			action: ACTION_TYPE_DIC.OPEN_LOCK,
			delay: 500
		}
		this.runList.push(dirObj)
		this.checkLockStatus(config, ACTION_TYPE_DIC.OPEN_LOCK_CHECK)
	}
	/**
	 * 查询具体锁板的柜子状态
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 * @param {String} action
	 */
	checkLockStatus(config, action) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)
		if (lockNumber >= this.#doorCount || lockNumber < 0) {
			return this.handleParamsErrorByAction(action || ACTION_TYPE_DIC.CHECK_LOCK, {
				address: lockPlate,
				door: lockNumber,
				result: DATA_CODE_ENUM.ACTION_ERROR,
				resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			})
		}
		const dirObj = {
			lockPlate,
			lockNumber,
			dir: [0x88, lockPlate, 0x80, 0x01, lockNumber],
			action: action || ACTION_TYPE_DIC.CHECK_LOCK,
			delay: 800
		}
		this.runList.push(dirObj)
		!this.isRun && this.run()
	}
	/**
	 * 开/关灯
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 * @param {String} color 灯光颜色 red or green
	 */
	handleLight(config) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)
		if (!lockNumber) {
			return this.handleParamsErrorByAction(ACTION_TYPE_DIC.SET_LOCK_LIGHT, {
				address: lockPlate,
				door: lockNumber,
				result: DATA_CODE_ENUM.ACTION_ERROR,
				resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			})
		}
		const dirList = []
		const lightDirMap = {
			openRed: [0x88, lockPlate, 0x4a, 0x04, lockNumber, 0xff, 0xff, 0x01], // 开红灯
			closeRed: [0x88, lockPlate, 0x4a, 0x04, lockNumber, 0xff, 0xff, 0x00], // 关红灯
			openGreen: [0x88, lockPlate, 0x7a, 0x04, lockNumber, 0xff, 0xff, 0x01], // 开绿灯
			closeGreen: [0x88, lockPlate, 0x7a, 0x04, lockNumber, 0xff, 0xff, 0x00] // 关绿灯
		}
		switch (config.color) {
			case 'red':
				dirList.push(lightDirMap.closeGreen)
				dirList.push(lightDirMap.openRed)
				break
			case 'green':
				dirList.push(lightDirMap.closeRed)
				dirList.push(lightDirMap.openGreen)
				break
			default:
				dirList.push(lightDirMap.closeRed)
				dirList.push(lightDirMap.closeGreen)
				break
		}
		dirList.forEach((item) => {
			const dirObj = {
				lockPlate,
				lockNumber,
				dir: item,
				action: ACTION_TYPE_DIC.SET_LOCK_LIGHT,
				delay: 50
			}
			this.runList.push(dirObj)
			!this.isRun && this.run()
		})
	}
	/**
	 * 接收串口返回数据
	 */
	receiveMessage() {
		this.port.on('readable', () => {
			this.isAwaitResult = false
			const res = this.port.read()
			this.backData = this.backData.concat([...res])
			this.logger.info('data:', JSON.stringify(this.backData))
			const dataLen = 6 + (this.backData[3] || 0)
			if (this.backData[0] == 0x86 && this.backData.length >= dataLen && this.backData[dataLen - 1] == 0x66) {
				const dirObj = this.currentDirObj || {}
				const actionType = this.getActionType(dirObj.action)
				if (actionType) {
					const result = this.backData[5] == 0x00 ? 1 : 0
					this.handleSuccessByAction(actionType, {
						address: this.backData[1],
						door: this.backData[4],
						result,
						resultMsg: result == 1 ? '柜门已开启' : '柜门已关闭'
					})
					this.currentDirObj = null
				}
				this.backData = []
			}
		})
	}
	/**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
	dealCommand(command) {
		const verifyCode = command.reduce((total, value) => {
			return total ^ value
		}, 0)
		command.push(verifyCode)
		command.push(0x68)
		const newCommand = command.map((num) => {
			return this.decToHex(num)
		})
		const cmdBuffer = Buffer.from(newCommand.join(''), 'hex')
		return cmdBuffer
	}
}

module.exports = Lock
