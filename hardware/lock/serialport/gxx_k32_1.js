const LightSerialPortCommon = require('./common')
const { ACTION_TYPE_DIC, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../../enum')

class Lock extends LightSerialPortCommon {
	#doorCount = 18 // 一个锁板最大柜数
	constructor() {
		super()
		this.baudRate = 9600 // 比特率
	}
	/**
	 * 开锁
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 */
	openLock(config) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)
		if (lockNumber >= this.#doorCount || lockNumber < 0) {
			return this.handleParamsErrorByAction(ACTION_TYPE_DIC.OPEN_LOCK, {
				address: lockPlate,
				door: lockNumber,
				result: DATA_CODE_ENUM.ACTION_ERROR,
				resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			})
		}
		const dirObj = {
			lockPlate,
			lockNumber,
			dir: [0x8a, lockPlate, lockNumber, 0x11],
			action: ACTION_TYPE_DIC.OPEN_LOCK,
			delay: 500
		}
		this.runList.push(dirObj)
		this.checkLockStatus(config, ACTION_TYPE_DIC.OPEN_LOCK_CHECK)
	}
	/**
	 * 查询具体锁板的柜子状态
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 * @param {String} action
	 */
	checkLockStatus(config, action) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)
		if (lockNumber >= this.#doorCount || lockNumber < 0) {
			return this.handleParamsErrorByAction(action || ACTION_TYPE_DIC.CHECK_LOCK, {
				address: lockPlate,
				door: lockNumber,
				result: DATA_CODE_ENUM.ACTION_ERROR,
				resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			})
		}
		const dirObj = {
			lockPlate,
			lockNumber,
			dir: [0x80, lockPlate, lockNumber, 0x33],
			action: action || ACTION_TYPE_DIC.CHECK_LOCK,
			delay: 500
		}
		this.runList.push(dirObj)
		!this.isRun && this.run()
	}
	/**
	 * 接收串口返回数据
	 */
	receiveMessage() {
		this.port.on('readable', () => {
			const res = this.port.read()
			if (![0x80, 0x8a].includes(res[0]) && !this.backData) {
				this.logger.info('串口回调数据异常:', JSON.stringify(res))
				this.#sendData()
				return
			}
			this.backData = this.backData.concat([...res])
			this.logger.info('data:', JSON.stringify(this.backData))
			if (this.backData.length >= 5) {
				this.#sendData(parseInt(this.backData[1]), parseInt(this.backData[2]), parseInt(this.backData[3]))
			}
		})
	}
	#sendData(box, door, result = null) {
		const dirObj = this.currentDirObj || {}
		const actionType = this.getActionType(dirObj.action)
		if (actionType) {
			result = result ? (result == parseInt(0x11) ? 1 : 0) : -1
			this.handleSuccessByAction(actionType, {
				address: box || dirObj.lockPlate,
				door: door || dirObj.lockNumber,
				result,
				resultMsg: result != -1 ? (result == 1 ? '柜门已开启' : '柜门已关闭') : '操作超时'
			})
			this.currentDirObj = null
		}
		this.backData = []
	}
	/**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
	dealCommand(command) {
		const verifyCode = command.reduce((total, value) => {
			return total ^ value
		}, 0)
		command.push(verifyCode)
		const newCommand = command.map((num) => {
			return this.decToHex(num)
		})
		const cmdBuffer = Buffer.from(newCommand.join(''), 'hex')
		return cmdBuffer
	}
}

module.exports = Lock
