const LightSerialPortCommon = require('./common')
const { ACTION_TYPE_DIC, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../../enum')

class Lock extends LightSerialPortCommon {
	#doorCount = 18 // 一个锁板最大柜数
	constructor() {
		super()
		this.baudRate = 57600 // 比特率
	}
	/**
	 * 开锁
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 */
	openLock(config) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)
		if (lockNumber >= this.#doorCount || lockNumber < 0) {
			return this.handleParamsErrorByAction(ACTION_TYPE_DIC.OPEN_LOCK, {
				address: lockPlate,
				door: lockNumber,
				result: DATA_CODE_ENUM.ACTION_ERROR,
				resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			})
		}
		const dirObj = {
			lockPlate,
			lockNumber,
			dir: [0x5a, 0xa5, 0x05, lockPlate, 0xa1, lockNumber, 0x00, 0x00],
			action: ACTION_TYPE_DIC.OPEN_LOCK,
			delay: 500
		}
		this.runList.push(dirObj)
		this.checkLockStatus(config, ACTION_TYPE_DIC.OPEN_LOCK_CHECK)
	}
	/**
	 * 查询具体锁板的柜子状态
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 * @param {String} action
	 */
	checkLockStatus(config, action) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)
		if (lockNumber >= this.#doorCount || lockNumber < 0) {
			return this.handleParamsErrorByAction(action || ACTION_TYPE_DIC.CHECK_LOCK, {
				address: lockPlate,
				door: lockNumber,
				result: DATA_CODE_ENUM.ACTION_ERROR,
				resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			})
		}
		const dirObj = {
			lockPlate,
			lockNumber,
			dir: [0x5a, 0xa5, 0x05, lockPlate, 0xa2, lockNumber, 0x00, 0x00],
			action: action || ACTION_TYPE_DIC.CHECK_LOCK,
			delay: 500
		}
		this.runList.push(dirObj)
		!this.isRun && this.run()
	}
	/**
	 * 接收串口返回数据
	 */
	receiveMessage() {
		this.port.on('readable', () => {
			this.isAwaitResult = false
			const res = this.port.read()
			this.backData = this.backData.concat([...res])
			this.logger.info('data:', JSON.stringify(this.backData))
			if (this.backData[0] == 0x5d && this.backData[1] == 0xd5 && this.backData.length >= 13) {
				let status = this.handleMessage(this.backData.slice(6, 9))
				const dirObj = this.currentDirObj || {}
				status = status.filter((item) => dirObj.lockNumber == item.lockNumber) || []
				const actionType = this.getActionType(dirObj.action)
				if (actionType) {
					const result = status[0].status == 1
					this.handleSuccessByAction(actionType, {
						address: this.backData[3],
						door: status[0].lockNumber,
						result,
						resultMsg: result == 1 ? '柜门已开启' : '柜门已关闭'
					})
					this.currentDirObj = null
				}
				this.backData = []
			}
		})
	}
	/**
	 * 返回数据处理
	 * @param {Array} bytes 数据数组
	 * @returns status
	 */
	handleMessage(bytes) {
		// 字节序倒序
		const reversed = (bytes[0] & 0xff) | ((bytes[1] & 0xff) << 8) | ((bytes[2] & 0xff) << 16)
		this.logger.info('返回数据处理:', JSON.stringify(bytes), reversed.toString(2))
		// 将低 doorCount 位转换为位，并从最低位开始返回开关状态
		const status = []
		for (let i = 0; i < this.doorCount; i++) {
			const bit = (reversed >> i) & 1
			const lockStatus = {
				lockNumber: i + 1,
				status: bit
			}
			status.push(lockStatus)
		}
		return status
	}
	/**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
	dealCommand(command) {
		const verifyCode = command.slice(2).reduce((total, value) => {
			return total ^ value
		}, 0)
		command.push(verifyCode)
		const newCommand = command.map((num) => {
			return this.decToHex(num)
		})
		const cmdBuffer = Buffer.from(newCommand.join(''), 'hex')
		return cmdBuffer
	}
}

module.exports = Lock
