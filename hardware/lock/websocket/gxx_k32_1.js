// 酷安 - 锁控
const LockWsCommon = require('./common')
const { ACTION_TYPE_DIC, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../../enum')
const defaultUrlMap = {
	win32_x64: 'ws://localhost:24245/locker',
	win32_ia32: 'ws://localhost:24245/locker',
	linux_x64: 'ws://localhost:8540/locker',
	linux_arm64: 'ws://localhost:8540/locker'
}

/* Lock锁控类
 * 实例可监听事件：
 * init-success 连接开锁服务成功
 * error 锁控服务报错
 * message 锁控ws服务返回消息
 * status-change 当前操作的门锁状态
 */
class Lock extends LockWsCommon {
	constructor() {
		super()
		this.handleAction = ''
	}

	// 连接开锁服务
	init(url) {
		this.initWebscoket(url || defaultUrlMap[`${process.platform}_${process.arch}`], (res) => {
			this.receiveMessage(res) // 监听ws返回信息
		})
	}

	/**
	 * 开锁
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 */
	openLock(config) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)

		if (!lockNumber) {
			return this.handleParamsErrorByAction(config.action, {
				address: lockPlate,
				door: lockNumber,
				result: DATA_CODE_ENUM.ACTION_ERROR,
				resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			})
		}
		const msg = this.#makeDir(config.action, lockPlate, lockNumber)
		this.runList.push(msg)
		!this.isRun && this.run()
	}

	/**
	 * 查询具体锁板的柜子状态
	 * @param {Object} config
	 * @param {Number} config.address 串行设备地址
	 * @param {Number} config.door 天线
	 */
	checkLockStatus(config) {
		const lockNumber = parseInt(config.door)
		const lockPlate = parseInt(config.address)
		if (!lockNumber) {
			return this.handleParamsErrorByAction(config.action, {
				address: lockPlate,
				door: lockNumber,
				result: DATA_CODE_ENUM.ACTION_ERROR,
				resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			})
		}
		const msg = this.#makeDir(config.action, lockPlate, lockNumber)
		this.runList.push(msg)
		!this.isRun && this.run()
	}

	// 组装指令
	#makeDir(type, lockPlate, lockNumber) {
		const msg = {
			action: type,
			ActionType: '',
			Box: lockPlate,
			Door: lockNumber,
			CheckCode: '11',
			RequestTime: Date.now()
		}
		switch (type) {
			case ACTION_TYPE_DIC.OPEN_LOCK:
			case ACTION_TYPE_DIC.CHECK_LOCK:
				msg.ActionType = type.toLowerCase()
				break
			default:
				msg.ActionType = ''
				break
		}
		this.handleAction = msg.ActionType
		return msg
	}

	// 接收锁控ws返回信息
	receiveMessage(res) {
		let data = {}
		try {
			data = JSON.parse(res)
		} catch (error) {
			return this.handleParseErrorByAction(this.handleAction)
		}
		const { result, box, door, actiontype } = data
		this.handleSuccessByAction(actiontype == 'checklock' ? ACTION_TYPE_DIC.CHECK_LOCK : ACTION_TYPE_DIC.OPEN_LOCK, {
			address: box,
			door,
			result,
			resultMsg: result == 1 ? '柜门已开启' : '柜门已关闭'
		})
	}

	// 关闭连接
	close() {
		this.closeWebsocket()
	}
}

module.exports = Lock
