const LockCommon = require('../index')
const WebSocket = require('ws')
const { ACTION_TYPE_DIC, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../../enum')

class LockWsCommon extends LockCommon {
	constructor() {
		super()
		this.ws = null
	}

	// 初始化websocket
	initWebscoket(wsUrl, callback) {
		if (!wsUrl) {
			return this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
		}
		this.ws = new WebSocket(wsUrl)

		//连接成功建立的回调方法
		this.ws.on('open', () => {
			this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
		})

		//接收到消息的回调方法
		this.ws.on('message', (e) => {
			this.isAwaitResult = false
			const obj = e.toString()
			callback && callback(obj)
		})

		//连接发生错误的回调方法
		this.ws.on('error', () => {
			this.isAwaitResult = false
			this.handleLinkErrorByAction(ACTION_TYPE_DIC.ERROR)
		})

		//连接关闭的回调方法
		this.ws.on('close', () => {
			this.ws = null
			this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
			this.removeAllEventListener()
		})
	}

	async run() {
		if (this.runList.length === 0) {
			this.isRun = false
			return
		}
		this.isRun = true
		const dirObj = JSON.parse(JSON.stringify(this.runList[0]))
		this.runList.shift()
		const isSend = await this.#sendMessage(dirObj)
		if (!isSend) {
			this.run()
			return
		}
		this.isAwaitResult = true
		const bool = await this.addInterval()
		if (!bool) {
			this.handleNoResponseErrorByAction(dirObj.action, {
				address: dirObj.Box,
				door: dirObj.Door,
				result: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			})
		}
		this.run()
	}

	// 发送数据
	#sendMessage(msg) {
		return new Promise(async (resolve) => {
			if (!this.ws) {
				this.handleLinkErrorByAction(msg.action, {
					address: msg.Box,
					door: msg.Door,
					result: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
					resultMsg: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
				})
				return resolve(false)
			}
			const text = JSON.stringify(msg)
			this.ws.send(text)
			resolve(true)
		})
	}

	/**
	 * 接收返回数据
	 */
	receiveMessage() {}

	// 关闭连接
	closeWebsocket() {
		if (!this.ws) {
			return this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		}
		this.ws.close()
	}
}

module.exports = LockWsCommon
