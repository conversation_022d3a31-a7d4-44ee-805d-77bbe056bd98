const { app } = require('electron')
const { exec } = require('child_process')
const SerialPort = require('serialport')
const dns = require('dns')
const os = require('os')
const si = require('systeminformation')
const Logger = require('./log/index')
const { actualVersion } = require('../package.json')
const { SYSTEM_PLATFORM_STR_DIC } = require('./enum')
const log = new Logger('hardware-tools')
const platform = process.platform
const zeroRegex = /(?:[0]{1,2}[:-]){5}[0]{1,2}/
const systemProperty = {}

function handleFilePath(filePath) {
	if (process.platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
		return filePath.replace(/\\/g, '/')
	} else {
		return filePath
	}
}

function linuxGetSerialPortList() {
	return new Promise((resolve, reject) => {
		exec(`ls -l /dev/tty* | awk '{print $NF}'`, (err, stdout, stderr) => {
			if (err) {
				reject(err)
			}
			const ports = []
			stdout.split('\n').filter((port) => {
				ports.push({ path: port })
			})
			resolve(ports)
		})
	})
}

function winGetSerialPortList() {
	return SerialPort.list()
}

/**
 * 获取串口列表
 * @returns {Promise}
 */
function getSerialPortList() {
	return new Promise((resolve, reject) => {
		let p = process.platform == 'win32' ? winGetSerialPortList() : linuxGetSerialPortList()
		p.then((ports) => {
			const list = ports.map((port) => {
				return { label: port.path, value: port.path }
			})
			resolve(list)
		})
			.catch((err) => {
				resolve([])
			})
			.finally(() => {
				p = null
			})
	})
}

/**
 * 延时器
 */
function delay(time = 1000) {
	return new Promise((resolve) => {
		let timer = setTimeout(() => {
			clearTimeout(timer)
			timer = null
			resolve()
		}, time)
	})
}

/**
 * 创建一个单例包装器
 * @param {Function} className - 要被包装成单例的类的构造函数
 * @returns {Function} - 返回一个代理函数，用于创建单例实例
 * 此函数的目的是确保给定的类在整个应用程序中只有一个实例
 * 它通过使用代理来拦截对原始类构造函数的调用，并确保每次调用都返回同一个实例
 */
function singleton(className) {
	// 存储单例实例
	let instance = null
	// 创建一个代理来包装原始类
	const proxy = new Proxy(className, {
		// 自定义构造函数行为
		constructor(target, args) {
			// 如果实例不存在，则创建新实例
			if (!instance) {
				instance = Reflect.construct(target, args)
			}
			// 返回现有的单例实例
			return instance
		}
	})
	// 设置代理的构造函数为其自身，以保持原型链的完整性
	proxy.prototype.constructor = proxy
	// 返回代理，外部将通过这个代理来创建和访问单例实例
	return proxy
}

function getDnsInfo() {
	return dns.getServers()
}

function getDefaultGateway() {
	return new Promise((resolve, reject) => {
		let command
		if (platform == SYSTEM_PLATFORM_STR_DIC.LINUX) {
			command = "ip route show | grep default | awk '{print $3}'"
		} else if (platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
			command = 'route print | find "0.0.0.0"'
		} else {
			log.error('Unsupported platform.')
			return resolve('')
		}
		exec(command, (error, stdout, stderr) => {
			if (error) {
				log.error(`exec error: ${error}`)
				return resolve('')
			}
			const str = stdout.trim()
			if (platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
				resolve(parseStr(str))
			} else {
				resolve(str)
			}
		})
	})
}

function parseStr(str) {
	const items = str.trim().split(/\s+/)
	let result = ''
	for (let i = 2; i < items.length; i = i + 5) {
		if (!['On-link'].includes(items[i])) {
			result = items[i]
			break
		}
	}
	return result
}

async function defaultHandler(part) {
	const networkInfo = { ip: part.address || '', mac: part.mac || '', netmask: part.netmask || '', gateway: '', dns: [] }
	if (networkInfo.ip) {
		networkInfo.dns = getDnsInfo()
	}
	networkInfo.gateway = await getDefaultGateway()
	return Promise.resolve(networkInfo)
}

function getNetworkInfo(handler = defaultHandler) {
	const list = os.networkInterfaces()
	const partList = []
	for (const [_, parts] of Object.entries(list)) {
		if (!parts) continue
		for (const part of parts) {
			if (part.family == 'IPv4') {
				partList.push(part)
			}
		}
	}
	if (!partList.length) return handler({})
	for (let i = 0; i < partList.length; i++) {
		const part = partList[i]
		if (!part.internal && zeroRegex.test(part.mac) === false) {
			return handler(part)
		}
	}
	const somePart = partList.filter((item) => item.internal)
	return handler(somePart[0] || {})
}
/**
 * 获取ip、mac、netmask地址
 */
function getIpMacInfo() {
	return getNetworkInfo((part) => {
		return part || {}
	})
}

function getOtherInfo() {
	const version = systemProperty.version || actualVersion || app.getVersion() // SDK版本
	const { address: ip, mac } = getIpMacInfo()
	Object.assign(systemProperty, { version, ip, mac })
	return { version, ip, mac }
}

function getCpuInfo() {
	return new Promise(async (res, rej) => {
		if (systemProperty.threadNumber && systemProperty.cpuName) {
			res({ threadNumber: systemProperty.threadNumber, cpuName: systemProperty.cpuName })
		} else {
			const cpu = await si.cpu()
			const threadNumber = cpu.cores // CPU核心线程数
			const cpuName = `${cpu.manufacturer} ${cpu.brand}` // cpu名称
			Object.assign(systemProperty, { threadNumber, cpuName })
			res({ threadNumber, cpuName })
		}
	})
}

function getMemInfo() {
	return new Promise(async (res, rej) => {
		const mem = await si.mem()
		const memory = numToMB(mem.total) // 总内存
		const free = numToMB(mem.free) // 空闲内存
		const used = numToMB(mem.used) // 使用内存
		Object.assign(systemProperty, { memory })
		res({ memory, free, used })
	})
}

function getOsInfo() {
	return new Promise(async (res, rej) => {
		if (systemProperty.sysVersion && systemProperty.sysPlatform && systemProperty.sysArch) {
			res({ sysVersion: systemProperty.sysVersion, sysPlatform: systemProperty.sysPlatform, sysArch: systemProperty.sysArch })
		} else {
			const osInfo = await si.osInfo()
			const sysVersion = osInfo.distro // 系统版本
			const sysPlatform = osInfo.platform // 系统平台
			const sysArch = osInfo.arch // 系统架构
			Object.assign(systemProperty, { sysVersion, sysPlatform, sysArch })
			res({ sysVersion, sysPlatform, sysArch })
		}
	})
}

function getCpuUsedInfo() {
	return new Promise(async (res, rej) => {
		const currentLoad = await si.currentLoad()
		const cpuUsed = Math.floor(currentLoad.currentLoad) // cpu使用率
		res({ cpuUsed })
	})
}

function getDiskInfo() {
	return new Promise(async (res, rej) => {
		const diskInfo = await si.fsSize()
		let diskTotal = 0 // 磁盘总大小
		let diskUsed = 0 // 磁盘已使用大小
		let diskFree = 0 // 磁盘空闲大小
		diskInfo.forEach((item) => {
			diskTotal += item.size
			diskUsed += item.used
			diskFree += item.available
		})
		diskTotal = numToMB(diskTotal)
		diskUsed = numToMB(diskUsed)
		diskFree = numToMB(diskFree)
		Object.assign(systemProperty, { diskTotal })
		res({ diskTotal, diskUsed, diskFree })
	})
}

function numToMB(num) {
	return (num / 1024 / 1024).toFixed(2)
}

/**
 * 获取系统属性 CPU、温度、CPU 占用率、CPU 平均占用率、CPU 最大占用率、内存占用率等
 * @param {Array<String>} props
 * @returns {Promise}
 */
async function getSystemProperties(props) {
	props = props ? props.split(',') : []
	let result = {}
	if (!props.length) {
		const otherInfo = getOtherInfo()
		const cpuInfo = await getCpuInfo()
		const memInfo = await getMemInfo()
		const osInfo = await getOsInfo()
		const cpuUsedInfo = await getCpuUsedInfo()
		const diskInfo = await getDiskInfo()
		result = { ...otherInfo, ...cpuInfo, ...memInfo, ...osInfo, ...cpuUsedInfo, ...diskInfo }
		log.info(`getSystemInfo: ${JSON.stringify(result)}`)
		return Promise.resolve(result)
	}
	const obj = { ...systemProperty }
	for (let i = 0; i < props.length; i++) {
		const prop = props[i]
		if (obj[prop]) {
			continue
		}
		if (['version', 'ip', 'mac'].includes(prop)) {
			const otherInfo = getOtherInfo()
			Object.assign(obj, otherInfo)
		} else if (['cpuName', 'threadNumber'].includes(prop)) {
			const cpuInfo = await getCpuInfo()
			Object.assign(obj, cpuInfo)
		} else if (['memory', 'free', 'used'].includes(prop)) {
			const memInfo = await getMemInfo()
			Object.assign(obj, memInfo)
		} else if (['sysVersion', 'sysPlatform', 'sysArch'].includes(prop)) {
			const osInfo = await getOsInfo()
			Object.assign(obj, osInfo)
		} else if (['cpuUsed'].includes(prop)) {
			const cpuUsedInfo = await getCpuUsedInfo()
			Object.assign(obj, cpuUsedInfo)
		} else if (['diskTotal', 'diskUsed', 'diskFree'].includes(prop)) {
			const diskInfo = await getDiskInfo()
			Object.assign(obj, diskInfo)
		}
	}
	// 过滤出只需查询的数据
	props.forEach((prop) => {
		result[prop] = obj[prop]
	})
	log.info(`getSystemInfo: ${JSON.stringify(result)}`)
	return Promise.resolve(result)
}
module.exports = { handleFilePath, getSerialPortList, delay, singleton, getNetworkInfo, getIpMacInfo, getSystemProperties, getOsInfo }
