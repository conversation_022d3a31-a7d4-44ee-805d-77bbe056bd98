const { app } = require('electron')
const electronLog = require('electron-log')
const fs = require('fs')
const path = require('path')
const { SYSTEM_PLATFORM_STR_DIC } = require('../enum')
const logParentDir = 'Log'
if (app.isPackaged) {
	global.logFilePath = path.join(process.platform === SYSTEM_PLATFORM_STR_DIC.WIN ? 'C:\\' : '/opt/gosuncn', logParentDir)
} else {
	global.logFilePath = path.join(process.cwd(), logParentDir)
}
const oneTime = 1 * 24 * 60 * 60 * 1000
let timer = null

/**
 * 日志类（默认放入main文件夹）
 * 传入前缀pathSuffix代表新创建一个文件夹存放日志文件, 创建一个新electronLog实例，
 * 否则不创建新的electronLog实例
 */
class Log {
	constructor(pathSuffix = '') {
		this.logger = pathSuffix ? electronLog.create('anotherInstance') : electronLog
		const logPath = path.join(this.#getPath(pathSuffix || 'main'), this.#getLogFileName())
		this.logger.transports.file.level = 'debug'
		this.logger.transports.file.maxSize = 1002430 // 10M
		this.logger.transports.file.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}]{scope} {text}'
		this.logger.transports.file.resolvePath = () => logPath
	}

	// 日志文件目录
	#getPath(pathSuffix) {
		let logPath = global.logFilePath
		if (!fs.existsSync(logPath)) {
			fs.mkdirSync(logPath)
		}
		if (pathSuffix) {
			logPath = path.join(logPath, pathSuffix)
			if (!fs.existsSync(logPath)) {
				fs.mkdirSync(logPath)
			}
		}
		return logPath
	}

	// 日志文件名称
	#getLogFileName() {
		let date = new Date()
		date = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
		return `${date}.log`
	}

	// 删除日志文件
	deleteLog(logSaveDay = 3) {
		function del(logPath) {
			try {
				const dirArr = fs.readdirSync(logPath) || []
				for (let i = 0; i < dirArr.length; i++) {
					const item = dirArr[i]
					const fullPath = path.join(logPath, item)
					const stats = fs.statSync(fullPath)
					const isDirectory = stats.isDirectory()
					if (isDirectory) {
						// 删除文件夹
						del(fullPath)
						continue
					}
					// 删除文件
					const logDay = (new Date().getTime() - stats.birthtime.getTime()) / oneTime
					if (logDay <= logSaveDay) {
						continue
					}
					fs.unlink(fullPath, (error) => {
						if (error) {
							this.error(error, '删除日志文件出错')
						}
					})
				}
			} catch (e) {
				this.error(e, '删除日志文件异常')
			}
		}
		del(global.logFilePath)
	}

	scanFile(logSaveDay = 3) {
		this.clearTimer()
		timer = setInterval(() => {
			this.deleteLog(logSaveDay)
		}, logSaveDay * oneTime)
	}

	clearTimer() {
		if (timer) {
			clearInterval(timer)
			timer = null
		}
	}

	info() {
		this.logger.info.apply(this, arguments)
	}

	warn() {
		this.logger.warn.apply(this, arguments)
	}

	error() {
		this.logger.error.apply(this, arguments)
	}

	debug() {
		this.logger.debug.apply(this, arguments)
	}

	verbose() {
		this.logger.verbose.apply(this, arguments)
	}

	silly() {
		this.logger.silly.apply(this, arguments)
	}
}

module.exports = Log
