const ControlLockCommon = require('../index')
const SerialPort = require('serialport')
const { ACTION_TYPE_DIC } = require('../../enum')

class ControlLock extends ControlLockCommon {
	constructor() {
		super()
		this.port = null
		this.baudRate = 9600
		this.com = ''
	}
	init(com, baudRate) {
		this.com = com
		this.baudRate = parseInt(baudRate)
		this.logger.info(this.com, this.baudRate)
		this.port = new SerialPort(this.com, { baudRate: this.baudRate }, (err) => {
			if (err) {
				this.logger.info('串口连接失败：', err)
				this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
			} else {
				this.logger.info('端口打开成功！')
				this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
			}
		})
	}
	handle(action) {
		if (!this.port) {
			return this.handleLinkErrorByAction(action)
		}
		switch (action) {
			case ACTION_TYPE_DIC.OPEN_LOCK:
				this.openLock()
				break
			default:
				this.handleParamsErrorByAction(action)
				break
		}
	}
	openLock() {
		this.port.write('01050000ff008c3a', 'hex', (err) => {
			const result = err ? '操作超时' : '柜门已开启'
			if (err) {
				return this.handleParamsErrorByAction(ACTION_TYPE_DIC.OPEN_LOCK, { result })
			} else {
				this.handleSuccessByAction(ACTION_TYPE_DIC.OPEN_LOCK, { result })
			}
		})
	}
	close() {
		if (!this.port) {
			return this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		}
		this.port.close((error) => {
			if (error) {
				this.logger.error('销毁电锁控连接失败')
				return this.handleCloseErrorByAction(ACTION_TYPE_DIC.CLOSE)
			}
			this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
			this.port = null
			this.removeAllEventListener()
		})
	}
}

module.exports = ControlLock
