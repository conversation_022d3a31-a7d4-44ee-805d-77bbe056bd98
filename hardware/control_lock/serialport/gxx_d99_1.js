const ControlLockCommon = require('../index')
const SerialPort = require('serialport')
const { ACTION_TYPE_DIC } = require('../../enum')

class ControlLock extends ControlLockCommon {
	constructor() {
		super()
		this.port = null
		this.baudRate = 9600
		this.com = ''
	}
	init(com, baudRate) {
		this.com = com
		this.baudRate = parseInt(baudRate)
		this.logger.info(this.com, this.baudRate)
		this.port = new SerialPort(this.com, { baudRate: this.baudRate }, (err) => {
			if (err) {
				this.logger.info('串口连接失败：', err)
				this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
			} else {
				this.logger.info('端口打开成功！')
				this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
				this.runList = []
				this.receiveMessage()
			}
		})
	}
	openLock() {
		if (!this.port) {
			return this.handleLinkErrorByAction(ACTION_TYPE_DIC.OPEN_LOCK)
		}
		this.sendMessage({ action: ACTION_TYPE_DIC.OPEN_LOCK, dir: [0x88, 0x01, 0x8b, 0x03, 0x13, 0x16, 0x01] })
	}
	closeLock() {
		if (!this.port) {
			return this.handleLinkErrorByAction(ACTION_TYPE_DIC.CLOSE_LOCK)
		}
		this.sendMessage({ action: ACTION_TYPE_DIC.CLOSE_LOCK, dir: [0x88, 0x01, 0x8b, 0x03, 0x13, 0x16, 0x02] })
	}
	sendMessage(args) {
		this.port.write(this.dealCommand(args.dir), (err) => {
			const result = `柜门${args.action === ACTION_TYPE_DIC.OPEN_LOCK ? '开启' : '关闭'}${!err ? '成功' : '失败'}`
			if (err) {
				this.logger.error(`发送指令失败: `, JSON.stringify(err))
				this.handleSendMessageErrorByAction(args.action, { result })
			} else {
				this.runList.push(args.action)
				this.logger.info('发送指令成功: ', JSON.stringify(args.dir))
				// this.handleSuccessByAction(args.action, { result })
			}
		})
	}
	/**
	 * 接收串口返回数据
	 */
	receiveMessage() {
		this.port.on('readable', () => {
			const res = this.port.read()
			this.backData = this.backData.concat([...res])
			this.logger.info('DC锁返回data:', JSON.stringify(this.backData))
			const lastItem = this.runList[this.runList.length - 1]
			const result = this.backData[5] == 0x00 ? 1 : 0
			const msg = `柜门${lastItem === ACTION_TYPE_DIC.OPEN_LOCK ? '开启' : '关闭'}${result == 1 ? '成功' : '失败'}`
			this.handleSuccessByAction(lastItem, {
				result,
				resultMsg: msg
			})
			this.runList.shift()
			this.currentDirObj = null
			this.backData = []
		})
	}
	/**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
	dealCommand(command) {
		const verifyCode = command.reduce((total, value) => {
			return total ^ value
		}, 0)
		command.push(verifyCode)
		command.push(0x68)
		const newCommand = command.map((num) => {
			return this.decToHex(num)
		})
		const cmdBuffer = Buffer.from(newCommand.join(''), 'hex')
		return cmdBuffer
	}
	/**
	 * 十进制 转 十六进制字符串
	 * @param {Number} num
	 * @returns
	 */
	decToHex(num) {
		let numHex = num.toString(16)
		numHex.length < 2 && (numHex = `0${numHex}`)
		return numHex
	}
	close() {
		if (!this.port) {
			return this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		}
		this.port.close((error) => {
			if (error) {
				this.logger.error('销毁电锁控连接失败')
				return this.handleCloseErrorByAction(ACTION_TYPE_DIC.CLOSE)
			}
			this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
			this.port = null
			this.removeAllEventListener()
		})
	}
}

module.exports = ControlLock
