const CODE_ENUM = {
	OK: 0,
	LINK_ERROR: 1,
	SEND_MESSAGE_ERROR: 2,
	CLOSE_ERROR: 3,
	PARAMS_ERROR: 4,
	NO_RESPONSE: 5,
	PARSE_ERROR: 6,
	IN_OPERATION: 7,
	IS_ACCESS: 8,
	IS_NOT_ACCESS: 9
}

const CODE_DIC = {
	[CODE_ENUM.OK]: '连接正常',
	[CODE_ENUM.LINK_ERROR]: '连接异常',
	[CODE_ENUM.SEND_MESSAGE_ERROR]: '发送指令失败',
	[CODE_ENUM.CLOSE_ERROR]: '关闭失败',
	[CODE_ENUM.PARAMS_ERROR]: '参数缺失或配置错误',
	[CODE_ENUM.NO_RESPONSE]: '发送指令后无数据返回',
	[CODE_ENUM.PARSE_ERROR]: '数据解析失败',
	[CODE_ENUM.IN_OPERATION]: '操作中',
	[CODE_ENUM.IS_ACCESS]: '已授权',
	[CODE_ENUM.IS_NOT_ACCESS]: '未授权'
}

const ACTION_TYPE_DIC = {
	INIT: 'init',
	CLOSE: 'close',
	ERROR: 'error',
	MESSAGE: 'message',
	OPEN: 'open',
	GET_VIDEO_DEVICE: 'getVideoDevice', // 获取设备（摄像头等）列表
	OPEN_CAMERA: 'openCamera', // 打开摄像头
	ROTATE_CAMERA: 'rotateCamera', // 旋转摄像头
	CAPTURE_IMAGE: 'captureImage', // 抓拍图像
	CLOSE_CAMERA: 'closeCamera', // 打开摄像头
	GET_PRINTER_LIST: 'getPrinterList', // 获取打印机列表
	PRINT: 'print', // 打印文件
	ADD_KEY_DOWN_LISTENER: 'addKeyDownListener', // 添加键盘监听事件
	REMOVE_KEY_DOWN_LISTENER: 'removeKeyDownListener', // 移除键盘监听事件
	KEY_DOWN_VALUE: 'keyDownValue', // 键盘回车值
	OPEN_LOCK: 'openLock', // 开柜
	CLOSE_LOCK: 'closeLock', // 关柜
	OPEN_LOCK_CHECK: 'openLockAfterCheck', // 发送开柜锁控指令后，检测柜门锁状态
	CHECK_LOCK: 'checkLock', // 检测柜门锁状态
	OPEN_LIGHT: 'openLight', // 开启补光灯
	CLOSE_LIGHT: 'closeLight', // 关闭补光灯
	CHECK_RFID: 'checkRfid', // 检测 RFID
	SET_LOCK_LIGHT: 'setLockLight', // 设置锁灯颜色
	SET_DOOR_LIGHT: 'setDoorLight', // 设置五门柜灯颜色
	SET_ORANGE_COLOR: 'setOrangeColor', // 设置黄灯颜色（红绿灯比例）
	SET_POLLING_CHANNELS: 'setPollingChannels', // 设置通道数
	BATCH_CONNECT_COM: 'batchConnectCom', // 批量测试串口
	READ_ID_CARD: 'readIdCard', // 身份证读卡器
	READ_WRISTBAND: 'readWristband', // 手环读卡器
	ONLY_SIGN: 'onlySign', // 仅签名
	ONLY_FINGER: 'onlyFinger', // 仅捺印
	SIGN_FINGER: 'signFinger', // 签名捺印
	START_SIGN_FINGER: 'startSign', // 汉王 开始签名/捺印通用返回
	START_FINGER: 'startFinger', // 开始捺印 汉王
	SIGN_FINGER_OK: 'signOK', // 汉王 完成签名/捺印通用返回
	FINGER_OK: 'fingerOK', // 完成捺印 汉王
	PDF_ONLY_PREVIEW: 'onlyPreview', // PDF仅预览
	PDF_SIGN_PREVIEW: 'clickSignEx', // PDF签名预览
	CHECK_SIGN_BOARD_STATUS: 'devStatus', // 查询签名板状态
	START_VIDEO_PREVIEW: 'startVideo', // 启动视频预览
	STOP_VIDEO_PREVIEW: 'stopVideo', // 关闭视频预览
	START_RECORD: 'startRecord', // 启动录制
	STOP_RECORD: 'stopRecord', // 停止录制
	SET_LOCAL_DIR: 'setLocalDir', // 设置本地存储路径
	CHECK_LOCAL_DIR: 'localDir', // 查询本地存储路径
	OPEN_USB: 'openUsb', // 打开USB开关
	CLOSE_USB: 'closeUsb', // 关闭USB开关
	START_COLLECTION: 'startCollection', // 开启采集指令
	END_COLLECTION: 'endCollection', // 结束采集指令
	VITAL_SIGNS_VALUE: 'vitalSignsValue', // 生命体征检测数据
	CLOSE_DEVICES: 'closeDevices' // 关闭设备
}

const DATA_CODE_ENUM = {
	ACTION_ERROR: -1,
	ACTION_SUCCESS: 0
}

const DATA_CODE_DIC = {
	[DATA_CODE_ENUM.ACTION_ERROR]: '操作失败',
	[DATA_CODE_ENUM.ACTION_SUCCESS]: '操作成功'
}

const SYSTEM_PLATFORM_STR_DIC = {
	LINUX: 'linux',
	WIN: 'win32'
}
const SYSTEM_ARCH_STR_DIC = {
	X64: 'x64',
	ARM64: 'arm64',
	IA32: 'ia32'
}

module.exports = {
	CODE_ENUM,
	CODE_DIC,
	DATA_CODE_ENUM,
	DATA_CODE_DIC,
	ACTION_TYPE_DIC,
	SYSTEM_PLATFORM_STR_DIC,
	SYSTEM_ARCH_STR_DIC
}
