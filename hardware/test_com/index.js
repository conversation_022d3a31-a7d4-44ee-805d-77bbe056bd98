const { EventEmitter } = require('events')
const SerialPort = require('serialport')
const { getSerialPortList } = require('../tools')
const Logger = require('../log/index')
const { CODE_DIC, CODE_ENUM, ACTION_TYPE_DIC, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../enum')
const log = new Logger('test-com')

class TestCom extends EventEmitter {
	constructor() {
		super()
		this.logger = log
	}
	async getSerialPortList() {
		const list = await getSerialPortList()
		this.logger.info('串口端口列表：', JSON.stringify(list))
		this.callback({
			action: ACTION_TYPE_DIC.MESSAGE,
			code: CODE_ENUM.OK,
			msg: CODE_DIC[CODE_ENUM.OK],
			data: {
				flag: DATA_CODE_ENUM.ACTION_SUCCESS,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_SUCCESS],
				result: list
			}
		})
	}
	/**
	 * 批量连接COM
	 * @param {Array<String>} pathList com List
	 * @param {Array<Number>} baudRateList 波特率 List
	 */
	batchConnect(pathList, baudRateList) {
		const result = []
		pathList.forEach((com, i) => {
			baudRateList.forEach((baudRate, j) => {
				const port = new SerialPort(com, { baudRate: parseInt(baudRate) }, (err) => {
					result.push({
						type: ACTION_TYPE_DIC.BATCH_CONNECT_COM,
						code: !!err ? CODE_ENUM.LINK_ERROR : CODE_ENUM.LINK_OK,
						msg: CODE_DIC[!!err ? CODE_ENUM.LINK_ERROR : CODE_ENUM.LINK_OK],
						data: {
							flag: !!err ? DATA_CODE_ENUM.ACTION_ERROR : DATA_CODE_ENUM.ACTION_SUCCESS,
							message: !!err ? DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR] : DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_SUCCESS]
						}
					})
					if (!err) {
						this.close(port)
					}
					if (i == pathList.length - 1 && j == baudRateList.length - 1) {
						this.callback({
							action: ACTION_TYPE_DIC.BATCH_CONNECT_COM,
							code: CODE_ENUM.OK,
							msg: CODE_DIC[CODE_ENUM.OK],
							data: {
								flag: DATA_CODE_ENUM.ACTION_SUCCESS,
								message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_SUCCESS],
								result
							}
						})
					}
				})
			})
		})
	}
	close(port) {
		port.close((error) => {
			if (error) {
				this.logger.info('关闭串口失败:', JSON.stringify(error))
			}
			port = null
		})
	}
	callback(data) {
		this.emit('test-com-callback', data)
	}
}
module.exports = TestCom
