const winPrint = require('pdf-to-printer').print
const linuxPrint = require('unix-print').print
const { delay } = require('../tools')
const Logger = require('../log/index')
const log = new Logger('printer')
const { SYSTEM_PLATFORM_STR_DIC } = require('../enum')
const platform = process.platform

class Printer {
	print(params) {
		return new Promise(async (resolve) => {
			if (!params.printName || !params.fileUrl) {
				resolve(false)
			}
			try {
				if (platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
					await winPrint(params.fileUrl, { printer: params.printName, monochrome: !params.isColorPrint })
				} else {
					await linuxPrint(params.fileUrl, params.printName)
				}
				await delay(2500)
				resolve(true)
				log.info(`${params.fileUrl}文件打印成功`)
			} catch (e) {
				resolve(false)
				log.error(`${params.fileUrl}文件打印失败：${e}`)
			}
		})
	}
}

module.exports = Printer
