const IdCardCommon = require('../index')
const http = require('../../lib/request')
const uac = 'http://127.0.0.1:8989/api'
const { ACTION_TYPE_DIC } = require('../../enum')

class IdCard extends IdCardCommon {
	// 发送请求获取数据
	#sendMessage(url) {
		return http({
			method: 'get',
			url: uac + url
		})
	}
	// 获取IdCard消息
	getIdCard() {
		const url = '/ReadMsg?&waitTime=5'
		let promise = this.#sendMessage(url)
		promise
			.then(
				(res) => {
					this.logger.info(`getIdCard res: ${res}`)
					if (res.retcode.startsWith('0x90 0x') && res.cardno) {
						this.handleSuccessByAction(ACTION_TYPE_DIC.READ_ID_CARD, res)
					} else {
						this.handleSendMessageErrorByAction(ACTION_TYPE_DIC.READ_ID_CARD, res)
					}
				},
				(err) => {
					this.logger.error(`getIdCard error: ${err}`)
					this.handleSendMessageErrorByAction(ACTION_TYPE_DIC.READ_ID_CARD)
				}
			)
			.finally(() => {
				promise = null
			})
	}
	/**
	 * 销毁
	 */
	close() {
		this.removeAllEventListener()
	}
}

module.exports = IdCard
