const path = require('path')
// const fs = require('fs')
const ffi = require('ffi-napi')
const ref = require('ref-napi')
const StructType = require('ref-struct-di')(ref)
const ArrayType = require('ref-array-di')(ref)
const { nativeImage } = require('electron')
const { handleFilePath } = require('../tools')
const { SYSTEM_PLATFORM_STR_DIC } = require('../enum')
const Logger = require('../log/index')
const log = new Logger('face')
const platform = process.platform
const faceDllPath = handleFilePath(path.join(global.extraResources, platform, process.arch, '/face_sdk/', platform == SYSTEM_PLATFORM_STR_DIC.WIN ? 'GoNcFaceRegSDK.dll' : 'libGoNcFaceRegSDK.so'))

class GxxFaceApi {
	#faceMethods = null
	#faceRecognitionInitId = -1
	#faceRecognitionDetectorId = -1
	// #importId = -1
	#phHandle = ref.alloc('longlong', 0) // 人脸检测器出参
	// 浮点类型的点坐标
	#GoPoint2f = StructType({
		fX: 'float',
		fY: 'float'
	})
	// 人脸姿态
	#FacePose = StructType({
		fHorizontal_threshold: 'float'
	})
	// 人脸检测参数
	#FaceDetectFilterParams = StructType({
		nX: 'int',
		nY: 'int',
		nWidth: 'int',
		nHeight: 'int',
		nMin_size: 'int',
		nMax_size: 'int',
		pose_thresold: this.#FacePose,
		nStream_type: 'int'
	})
	// 人脸区域，实际坐标值
	#FaceTrackedRect = StructType({
		nX: 'int',
		nY: 'int',
		nWidth: 'int',
		nHeight: 'int',
		nID: 'int',
		face_points: ArrayType(this.#GoPoint2f, 5)
	})
	// 人脸比对结果
	// #PersonSearchInfo = StructType({
	// 	person_id_: 'int',
	// 	similarity_: 'float'
	// })
	// #PersonSearchInfo
	constructor() {
		this.#initFaceMethods()
	}
	/**
	 * 初始化人脸检测方法
	 */
	#initFaceMethods() {
		if (platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
			process.env.PATH += `${path.delimiter}${faceDllPath.substring(0, faceDllPath.lastIndexOf('/'))}`
		}
		this.#faceMethods = new ffi.Library(faceDllPath, {
			// 初始化
			GoNc_FaceRecognition_Init: ['int', ['string']],
			// 人脸识别器创建
			GoNc_FaceRecognition_Create: ['int', ['string', 'pointer']],
			// 人脸检测信息
			GoNc_FaceRecognition_FaceDetect: ['int', ['int64', 'pointer', 'int', 'int', 'pointer', 'int', 'pointer', 'pointer']],
			// 活体检测
			GoNc_FaceRecognition_AntiSpoofingProcess: ['int', ['int64', 'pointer', 'int', 'int', 'pointer', 'int', 'pointer']],
			// 导入人脸库
			GoNc_FaceRecognition_ImportPersons: ['int', ['int64', 'pointer', 'int']],
			// 人脸特征提取
			GoNc_FaceRecognition_GetFaceFeature: ['int', ['int64', 'pointer', 'int', 'int', 'pointer', 'int', 'pointer', 'pointer']],
			// 人脸比对 1:1
			GoNc_FaceRecognition_CompareFeature: ['int', ['int64', 'pointer', 'pointer', 'pointer', 'pointer', 'pointer']],
			// 人脸比对 1:N
			GoNc_FaceRecognition_SearchPerson: ['int', ['int64', 'pointer', 'int', 'float', 'int', 'pointer', 'pointer']],
			// 添加人脸信息到人脸库
			GoNc_FaceRecognition_AddPerson: ['int', ['int64', 'pointer', 'float']],
			// 更新人脸库中指定人脸信息
			GoNc_FaceRecognition_UpdatePersonFeature: ['int', ['int64', 'pointer']],
			// 删除人脸库中指定人脸信息
			GoNc_FaceRecognition_DeletePerson: ['int', ['int64', 'int']],
			// 清除人脸库
			GoNc_FaceRecognition_ClearPersons: ['void', ['int64']],
			// 人脸识别器销毁
			GoNc_FaceRecognition_Release: ['int', ['int64']],
			// 反初始化
			GoNc_FaceRecognition_CleanUp: ['int', []]
		})
		this.#initFaceDetector()
		this.#createFaceDetector()
	}
	/**
	 * 初始化人脸检测资源
	 */
	#initFaceDetector() {
		if (this.#faceRecognitionInitId !== 0) {
			this.#faceRecognitionInitId = this.#faceMethods.GoNc_FaceRecognition_Init('')
			log.info('初始化人脸检测资源结果=', this.#faceRecognitionInitId)
			if (this.#faceRecognitionInitId != 0) {
				throw new Error('初始化人脸检测资源失败')
			}
		}
	}
	/**
	 * 创建人脸检测器
	 */
	#createFaceDetector() {
		const modelsPath = path.join(faceDllPath.substring(0, faceDllPath.lastIndexOf('/')), 'models/')
		if (this.#faceRecognitionDetectorId !== 0) {
			this.#faceRecognitionDetectorId = this.#faceMethods.GoNc_FaceRecognition_Create(modelsPath, this.#phHandle)
			log.info('创建人脸检测器结果', this.#faceRecognitionDetectorId)
			if (this.#faceRecognitionDetectorId != 0) {
				throw new Error('创建人脸检测器失败')
			}
		}
	}
	/**
	 * 导入人脸库
	 */
	// importFaceLibrary() {
	// 	if (this.#importId == 0) {
	// 		return
	// 	}
	// 	let arr = []
	// 	// 遍历文件夹文件
	// 	const dirArr = fs.readdirSync(global.faceImagePath) || []
	// 	for (let i = 0; i < dirArr.length; i++) {
	// 		const file = dirArr[i]
	// 		// 获取文件路径
	// 		const filePath = path.join(global.faceImagePath, file)
	// 		// 判断是否为文件
	// 		const stats = fs.statSync(filePath)
	// 		const isFile = stats.isFile()
	// 		if (!isFile) {
	// 			continue
	// 		}
	// 		// 获取文件后缀
	// 		const ext = path.extname(filePath)
	// 		// 判断是否为图片文件
	// 		if (!(ext && ['.jpeg', '.jpg', '.png'].includes(ext.toLowerCase()))) {
	// 			continue
	// 		}
	// 		const options = this.#getImageInfoByFilePath(filePath)
	// 		let feature = this.getFaceFeature(options)
	// 		if (!feature) {
	// 			continue
	// 		}
	// 		feature = JSON.parse(feature)
	// 		arr.push({
	// 			person_id_: feature.ppF_len,
	// 			face_feature: feature.ppF
	// 		})
	// 	}
	// 	const faceNum = arr.length || 0
	// 	log.info('导入人脸库人数=', faceNum)
	// 	if (!faceNum) {
	// 		return
	// 	}
	// 	const hHandle = this.#phHandle.deref()
	// 	this.#importId = this.#faceMethods.GoNc_FaceRecognition_ImportPersons(hHandle, new Buffer.from(arr), faceNum)
	// 	log.info('导入人脸库结果=', this.#importId)
	// }
	/**
	 * 获取人脸图片检测信息
	 * @param {String} filePath
	 * @returns {Object}
	 */
	#getImageInfoByFilePath(filePath) {
		const image = nativeImage.createFromPath(filePath)
		const { width, height } = image.getSize()
		const imageData = image.getBitmap()
		const imgBuf = this.#getImgBuf(imageData)
		return { imgBuf, width, height }
	}
	/**
	 * 获取人脸图片检测信息
	 * @param {String} base64
	 * @returns {Object}
	 */
	getImageInfoByDataUrl(base64) {
		const image = nativeImage.createFromDataURL(base64)
		const { width, height } = image.getSize()
		const imageData = image.getBitmap()
		const imgBuf = this.#getImgBuf(imageData)
		return { imgBuf, width, height }
	}
	/**
	 * bgr格式buffer
	 * @param {Array} imageData
	 * @returns {Buffer}
	 */
	#getImgBuf(imageData) {
		const len = imageData.length / 4
		const array = []
		for (let i = 0; i < len; i++) {
			const r = imageData[i * 4 + 0]
			const g = imageData[i * 4 + 1]
			const b = imageData[i * 4 + 2]
			array.push(b)
			array.push(g)
			array.push(r)
		}
		return new Buffer.from(array)
	}
	/**
	 * 人脸特征提取
	 * @param {Object} options
	 * @param {Buffer} options.imgBuf
	 * @param {Number} options.width
	 * @param {Number} options.height
	 * @returns {Object}
	 */
	getFaceFeature(options) {
		const faceRects = this.#getFaceDetect(options)
		if (faceRects) {
			const { imgBuf, width, height } = options
			const hHandle = this.#phHandle.deref()
			const nNum_threads = 1
			const ppFeature = ref.alloc(ref.refType(ArrayType('char', 512)))
			const pnFeature_len = ref.alloc('int')
			const pFace_rc = faceRects
			this.#faceMethods.GoNc_FaceRecognition_GetFaceFeature(hHandle, imgBuf, width, height, pFace_rc, nNum_threads, ppFeature, pnFeature_len)
			const result = { ppF: ref.deref(ppFeature.deref()), ppF_len: pnFeature_len.deref() }
			this.#clearMemory(pnFeature_len)
			this.#clearMemory(ppFeature)
			return JSON.stringify(result)
		}
		return ''
	}
	/**
	 * 获取人脸检测信息
	 * @param {Object} options
	 * @param {Buffer} options.imgBuf
	 * @param {Number} options.width
	 * @param {Number} options.height
	 * @returns {Object}
	 */
	#getFaceDetect(options) {
		const { imgBuf, width, height } = options
		const hHandle = this.#phHandle.deref()
		const pnFace_count = ref.alloc('int')
		const nNum_threads = 1
		const ppFace_rc_arr = ref.alloc(ref.refType(this.#FaceTrackedRect))
		const pDet_filter_params = new this.#FaceDetectFilterParams({
			nX: 0,
			nY: 0,
			nWidth: width,
			nHeight: height,
			nMin_size: 40,
			nMax_size: 5000,
			pose_thresold: { fHorizontal_threshold: 0.75 },
			nStream_type: 1
		})
		const ret = this.#faceMethods.GoNc_FaceRecognition_FaceDetect(hHandle, imgBuf, width, height, pDet_filter_params.ref(), nNum_threads, ppFace_rc_arr, pnFace_count)
		let result = null
		if (ret != 0) {
			log.info(`人脸检测失败：${ret}`)
			return result
		}
		const faceCountValue = pnFace_count.deref()
		this.#clearMemory(pnFace_count)
		log.info('ret==', ret, '人脸个数：', faceCountValue)
		if (faceCountValue > 0) {
			result = ppFace_rc_arr.deref()
		}
		this.#clearMemory(ppFace_rc_arr)
		return result
	}
	/**
	 * 获取图片人脸位置信息
	 * @param {String} base64
	 * @returns {Object}
	 */
	getImageFaceInfo(base64) {
		if (!base64) {
			return null
		}
		const options = this.getImageInfoByDataUrl(base64)
		const rectPointer = this.#getFaceDetect(options)
		if (!rectPointer) {
			return null
		}
		const rect = rectPointer.deref()
		return { x: rect.nX, y: rect.nY, width: rect.nWidth, height: rect.nHeight }
	}
	/**
	 * 检测人脸是否为活体
	 * @param {String} base64
	 * @returns {Object}
	 */
	faceASP(base64) {
		const options = this.getImageInfoByDataUrl(base64)
		const faceRects = this.#getFaceDetect(options)
		if (faceRects) {
			const hHandle = this.#phHandle.deref()
			const asResult = ref.alloc('int') // 0:活体，1: 假体
			const nNum_threads = 1
			const FacesRC = faceRects
			this.#faceMethods.GoNc_FaceRecognition_AntiSpoofingProcess(hHandle, options.imgBuf, options.width, options.height, FacesRC, nNum_threads, asResult)
			log.info('活体识别结果(0-活体、1-假体):', asResult.deref())
			return asResult.deref() == 0
		}
		return false
	}
	/**
	 * 人脸比对 1:1
	 * @param {Object} firstFaceFeature
	 * @param {Object} secondFaceFeature
	 * @returns {Object}
	 */
	faceCompareFeature(firstFaceFeature, secondFaceFeature) {
		const fpSimilarity = ref.alloc('float')
		const szFeature1 = ref.alloc(ArrayType('char', 512), firstFaceFeature.ppF)
		const szFeature2 = ref.alloc(ArrayType('char', 512), secondFaceFeature.ppF)
		const npFeature_len1 = ref.alloc('int', firstFaceFeature.ppF_len)
		const npFeature_len2 = ref.alloc('int', secondFaceFeature.ppF_len)
		try {
			const hHandle = this.#phHandle.deref()
			const ret = this.#faceMethods.GoNc_FaceRecognition_CompareFeature(hHandle, szFeature1, npFeature_len1, szFeature2, npFeature_len2, fpSimilarity)
			log.info(ret, '人脸比对结果')
			const similarity = fpSimilarity.deref()
			log.info('人脸比对相似度', similarity)
			return similarity < 1 ? Number(similarity.toFixed(4)) : similarity
		} catch (error) {
			log.error(error)
			return 0
		} finally {
			this.#clearMemory(fpSimilarity)
			this.#clearMemory(szFeature1)
			this.#clearMemory(szFeature2)
			this.#clearMemory(npFeature_len1)
			this.#clearMemory(npFeature_len2)
		}
	}
	/**
	 * 人脸比对 1:N
	 * @param {Object} options
	 * @param {String} options.base64
	 * @param {Number} options.thresold
	 * @param {Number} options.maxNum
	 * @returns {Object}
	 */
	// faceCompareManyByBase64(options) {
	// 	let { base64, thresold, maxNum } = options
	// 	thresold = thresold || 0.01
	// 	maxNum = maxNum || 10
	// 	const faceOptions = this.getImageInfoByDataUrl(base64)
	// 	let faceFeature = this.getFaceFeature(faceOptions)
	// 	if (!faceFeature) {
	// 		return null
	// 	}
	// 	faceFeature = JSON.parse(faceFeature)
	// 	try {
	// 		const hHandle = this.#phHandle.deref()
	// 		const feature = ref.alloc(ArrayType('char', 512), faceFeature.ppF)
	// 		const searchedResult = ref.alloc(ref.refType(this.#PersonSearchInfo))
	// 		const resultNum = ref.alloc('int')
	// 		const ret = this.#faceMethods.GoNc_FaceRecognition_SearchPerson(hHandle, feature, faceFeature.ppF_len, thresold, maxNum, searchedResult, resultNum)
	// 		if (ret != 0) {
	// 			log.info('人脸比对失败：', ret)
	// 			return { num: 0, result: [] }
	// 		}
	// 		const num = resultNum.deref()
	// 		const result = searchedResult.deref() || []
	// 		log.info('人脸比对结果：', num, result)
	// 		return { num, result }
	// 	} catch (error) {
	// 		return { num: 0, result: [] }
	// 	}
	// }
	/**
	 * 销毁人脸检测器
	 */
	destroyFaceDetector() {
		if (this.#faceRecognitionDetectorId === 0) {
			const hHandle = this.#phHandle.deref()
			const ret = this.#faceMethods.GoNc_FaceRecognition_Release(hHandle)
			log.info('销毁人脸识别检测器GoNc_FaceRecognition_Release：', ret)
			ret == 0 && (this.#faceRecognitionDetectorId = -1)
		}
		if (this.#faceRecognitionInitId === 0) {
			const ret = this.#faceMethods.GoNc_FaceRecognition_CleanUp()
			log.info('销毁人脸识别检测器GoNc_FaceRecognition_CleanUp：', ret)
			ret == 0 && (this.#faceRecognitionInitId = -1)
		}
		if (this.#phHandle) {
			this.#clearMemory(this.#phHandle)
		}
		this.#faceMethods = null
	}
	// 释放内存
	#clearMemory(obj) {
		if (typeof ref.free === 'function') {
			ref.free(obj)
		} else {
			obj.fill(0)
		}
	}
}

module.exports = GxxFaceApi
