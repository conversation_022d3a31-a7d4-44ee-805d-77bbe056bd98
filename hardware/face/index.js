const GXXFaceApi = require('./gxx_face_1')

class Face {
	static GXX_FACE_1 = 1 // 1: GXX-FACE-1
	#currentType = 1
	constructor(type = 1) {
		if (this.#currentType == type && Face.instance) {
			return
		}
		if (Face.instance) {
			this.#destroy()
		}
		this.#currentType = type
		this.#init()
	}

	#init() {
		if (!this.#validateType(this.#currentType)) {
			return
		}
		this.#destroy()
		if (this.#currentType == 1) {
			Face.instance = new GXXFaceApi()
			// Face.instance.importFaceLibrary()
		}
	}

	#validateType(type) {
		return [Face.GXX_FACE_1].includes(Number(type))
	}

	#destroy() {
		if (Face.instance) {
			Face.instance.destroyFaceDetector()
			Face.instance = null
		}
	}

	/**
	 * 获取人脸检测信息
	 * @param {String} base64
	 * @returns {Object}
	 */
	getFaceDetectInfo(base64) {
		if (!Face.instance) {
			return null
		}
		return Face.instance.getImageFaceInfo(base64)
	}

	/**
	 * 获取人脸特征
	 * @param {String} base64
	 * @returns {String}
	 */
	getFaceFeature(base64) {
		if (!Face.instance) {
			return ''
		}
		const options = Face.instance.getImageInfoByDataUrl(base64)
		const faceFeature = Face.instance.getFaceFeature(options)
		return faceFeature
	}

	/**
	 * 人脸比对 1:N
	 * @param {Object} options
	 * @param {String} options.base64
	 * @param {Number} options.thresold
	 * @param {Number} options.maxNum
	 * @param {Array} personList // 数据库存储的人员列表信息
	 * @returns {Object}
	 */
	faceCompareManyByBase64(options, personList) {
		const result = []
		if (!Face.instance || personList.length == 0) {
			return result
		}
		// 提取第一张人脸特征
		let firstFaceFeature = this.getFaceFeature(options.base64)
		if (!firstFaceFeature) {
			return result
		}
		firstFaceFeature = JSON.parse(firstFaceFeature)
		const thresold = global.serverConfig.businessInfo.facialThreshold
		options.thresold = thresold ? thresold / 100 : 0.82
		options.maxNum = options.maxNum || 1

		for (let i = 0; i < personList.length; i++) {
			const person = personList[i]
			let similarity = 0
			try {
				similarity = Face.instance.faceCompareFeature(firstFaceFeature, JSON.parse(person.feature))
			} catch (error) {
				continue
			}
			if (similarity >= options.thresold) {
				result.push({ ...person, similarity })
			}
		}
		result.sort((a, b) => b.similarity - a.similarity)
		return result.slice(0, options.maxNum)
	}
}

module.exports = Face
