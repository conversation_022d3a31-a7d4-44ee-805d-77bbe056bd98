const path = require('path')
const fs = require('fs')
const ffi = require('ffi-napi')
const cryptoJS = require('crypto-js')
const { handleFilePath, singleton, getIpMacInfo, getOsInfo } = require('../tools')
const { SYSTEM_PLATFORM_STR_DIC } = require('../enum')
const Logger = require('../log/index')
const log = new Logger('hs-auth')
const platform = process.platform

class HSAuth {
	#authMethods = null
	static authInfo = {
		state: false,
		fileName: '',
		periodBegin: '',
		periodEnd: ''
	}
	// 初始化人脸检测方法
	#initAuthMethods() {
		const hsAuthDllPath = handleFilePath(path.join(global.extraResources, platform, process.arch, 'hs_auth', platform == SYSTEM_PLATFORM_STR_DIC.WIN ? 'HSAuth.dll' : 'libHSAuth.so'))
		if (platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
			process.env.PATH += `${path.delimiter}${hsAuthDllPath.substring(0, hsAuthDllPath.lastIndexOf('/'))}`
		}
		this.#authMethods = new ffi.Library(hsAuthDllPath, {
			// 自动识别设备型号，检查是否已授权
			AuthorizeSn: ['int', ['string', 'string', 'string', 'string']]
		})
	}
	/**
	 * 生成设备授权文件内容
	 * @returns {String}
	 */
	#createdKey() {
		return new Promise(async (resolve, reject) => {
			const { mac } = getIpMacInfo()
			const { sysPlatform, sysArch } = await getOsInfo()
			const value = cryptoJS.enc.Utf8.parse(`${mac}-${sysPlatform}-${sysArch}`)
			const key = cryptoJS.enc.Utf8.parse('devices encrypt public key')
			const encrypt = cryptoJS.AES.encrypt(value, key, { mode: cryptoJS.mode.ECB, padding: cryptoJS.pad.Pkcs7 })
			resolve(encrypt.toString())
		})
	}
	// 校验授权
	checkLicense() {
		return new Promise(async (resolve, reject) => {
			if (!this.#authMethods) {
				this.#initAuthMethods()
			}
			try {
				const licensePath = global.licenseDeviceDatPath
				if (!fs.existsSync(licensePath)) {
					log.info('未检测到授权文件', 'checkLicense-校验授权失败')
					HSAuth.authInfo.state = false
					return resolve({ success: false, message: '未检测到授权文件' })
				}
				let periodBegin = Buffer.alloc(9)
				let periodEnd = Buffer.alloc(9)
				const result = this.#authMethods.AuthorizeSn(licensePath, await this.#createdKey(), periodBegin, periodEnd)
				periodBegin = periodBegin.toString('utf8').replace(/\0/g, '')
				periodEnd = periodEnd.toString('utf8').replace(/\0/g, '') // 99991231： 永久有效
				if (periodEnd === '99991231') {
					periodEnd = '无限期'
				}
				log.info('checkLicense-校验授权结果:', result, periodBegin, periodEnd)
				const isSuccess = result === 0
				Object.assign(HSAuth.authInfo, {
					state: isSuccess,
					fileName: isSuccess ? global.licenseDeviceDatPath.substring(global.licenseDeviceDatPath.lastIndexOf('/')) : '',
					periodBegin,
					periodEnd
				})
				resolve({ success: isSuccess, message: `授权文件校验${isSuccess ? '通过' : '失败'}` })
			} catch (err) {
				log.info('checkLicense-校验授权失败:', err)
				Object.assign(HSAuth.authInfo, { state: false, fileName: '' })
				resolve({ success: false, message: '未检测到授权文件' })
			}
		})
	}
	// 采集设备信息
	collectDevicesInfo() {
		return new Promise(async (resolve, reject) => {
			try {
				const licensePath = global.licenseDeviceInfoPath
				fs.writeFileSync(licensePath, await this.#createdKey())
				const fileStream = fs.createReadStream(licensePath)
				resolve(fileStream)
			} catch (error) {
				log.error(`导出设备信息文件失败: ${error.toString()}`)
				resolve(null)
			}
		})
	}
}

module.exports = singleton(HSAuth)
