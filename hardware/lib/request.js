const axios = require('axios')

/**
 * 请求失败后的错误统一处理
 * @param {String} msg [请求失败的消息]
 */
const errorHandle = () => {}

// 创建axios实例
const instance = axios.create({
	baseURL: '',
	timeout: 30000,
	withCredentials: true,
	headers: {
		'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
	}
})

// 添加请求拦截器
instance.interceptors.request.use(
	(config) => {
		config.headers['Content-Type'] = 'application/json'
		return config
	},
	(error) => {
		return Promise.reject(error)
	}
)

// 响应拦截器即异常处理
instance.interceptors.response.use(
	(response) => {
		if (response.data) {
			return response.data
		}
		return Promise.reject()
	},
	(error) => {
		return Promise.reject(error.response && error.response.data)
	}
)

function request(config) {
	return new Promise((resolve, reject) => {
		instance(config)
			.then((response) => {
				resolve(response)
			})
			.catch((error) => {
				errorHandle(error && (error.msg || error.retmsg))
				reject(error)
			})
	})
}

module.exports = function http(config) {
	return request(config)
}
