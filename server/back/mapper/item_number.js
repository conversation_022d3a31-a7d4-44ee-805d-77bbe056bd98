const ItemNumberMapper = {
	findAllItemNumber: () => {
		return new Promise((resolve) => {
			const query = `SELECT * FROM ${global.itemNumberTableName} WHERE isDeleted == ?`
			global.SqliteDb.executeQuery(query, ['N'], (rows, err) => {
				if (err) {
					resolve({ success: false, data: null })
				} else {
					resolve({ success: true, data: rows })
				}
			})
		})
	},
	findInfoByItemNumber: (itemNumber) => {
		return new Promise((resolve) => {
			const query = `SELECT * FROM ${global.itemNumberTableName} WHERE itemNumber == ? AND isDeleted == ?`
			global.SqliteDb.executeQuery(query, [itemNumber, 'N'], (rows, err) => {
				if (err) {
					resolve({ success: false, data: null })
				} else {
					resolve({ success: true, data: rows[0] })
				}
			})
		})
	}
}

module.exports = ItemNumberMapper
