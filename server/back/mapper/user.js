const { v4: uuidv4 } = require('uuid')
const { SEX_ENUM } = require('../enum')
const commonMapper = require('./business')
const UserMapper = {
	selectPoliceNumber: (policeNumber) => {
		return new Promise((resolve, reject) => {
			const query = `SELECT * FROM ${global.userTableName} WHERE policeNumber = ? AND isDeleted = ?`
			global.SqliteDb.executeQuery(query, [policeNumber, 'N'], (rows, err) => {
				if (err) {
					reject(err)
				}
				resolve(rows.length ? rows[0] : null)
			})
		})
	},
	select: (user) => {
		return new Promise((resolve, reject) => {
			const query = `SELECT * FROM ${global.userTableName} WHERE loginId = ? AND isDeleted = ?`
			global.SqliteDb.executeQuery(query, [user.loginId, 'N'], (rows, err) => {
				if (err) {
					reject(err)
				}
				rows.map((row) => {
					row.sexCn = SEX_ENUM[row.sex]
				})
				resolve(rows.length ? rows[0] : null)
			})
		})
	},
	findInfoById: (id) => {
		return new Promise((resolve, reject) => {
			const query = `SELECT * FROM ${global.userTableName} WHERE id = ? AND isDeleted = ?`
			global.SqliteDb.executeQuery(query, [id, 'N'], (rows, err) => {
				if (err) {
					reject(err)
				}
				rows.map((row) => {
					row.sexCn = SEX_ENUM[row.sex]
				})
				resolve(rows.length ? rows[0] : false)
			})
		})
	},
	updateLoginTime: (user) => {
		return new Promise((resolve) => {
			global.SqliteDb.updateData(global.userTableName, { loginTime: user.loginTime }, `id == '${user.id}'`, (result) => {
				resolve(result)
			})
		})
	},
	register: (user) => {
		const uuid = user.id || uuidv4()
		const params = {
			id: uuid,
			loginId: user.loginId,
			password: user.password,
			name: user.name,
			age: user.age,
			sex: user.sex,
			role: user.role,
			avatar: user.avatar,
			feature: user.feature,
			createTime: user.createTime,
			updateTime: user.updateTime,
			associatedPoliceId: user.associatedPoliceId,
			associatedPoliceName: user.associatedPoliceName,
			certificateNumber: user.certificateNumber,
			policeNumber: user.policeNumber,
			associatedIscds: user.associatedIscds,
			userType: user.userType,
			faceImg: user.faceImg,
			fingerprint: user.fingerprint,
			isDeleted: 'N',
			associatedPoliceNumber: user.associatedPoliceNumber
		}
		return new Promise((resolve, reject) => {
			global.SqliteDb.insertData(global.userTableName, params, (err, result) => {
				if (err) {
					reject(err)
				} else {
					if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
						const synchronizeData = {
							type: 'insertData',
							tableName: global.userTableName,
							data: params
						}
						commonMapper.httpSynchronizeData(synchronizeData)
					}
					resolve(result)
				}
			})
		})
	},
	resetPassword: (user) => {
		return new Promise((resolve) => {
			global.SqliteDb.updateData(
				global.userTableName,
				{
					password: user.password,
					updateTime: user.updateTime
				},
				`id == '${user.id}'`,
				(result, err) => {
					if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
						const synchronizeData = {
							type: 'updateData',
							tableName: global.userTableName,
							data: {
								password: user.password,
								updateTime: user.updateTime
							},
							query: `id == '${user.id}'`
						}
						commonMapper.httpSynchronizeData(synchronizeData)
					}
					resolve(!err)
				}
			)
		})
	},
	updatePassword: (user) => {
		return new Promise((resolve, reject) => {
			global.SqliteDb.updateData(
				global.userTableName,
				{
					password: user.password,
					updateTime: user.updateTime
				},
				`id == ${user.id}`,
				(result, err) => {
					if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
						const synchronizeData = {
							type: 'updateData',
							tableName: global.userTableName,
							data: {
								password: user.password,
								updateTime: user.updateTime
							},
							query: `id == '${user.id}'`
						}
						commonMapper.httpSynchronizeData(synchronizeData)
					}
					resolve(!err)
				}
			)
		})
	},
	update: (user) => {
		return new Promise((resolve) => {
			// 构建一个不包含未传递或为空密码的更新对象
			const updateData = {
				name: user.name,
				age: user.age,
				sex: user.sex,
				avatar: user.avatar,
				feature: user.feature,
				updateTime: user.updateTime,
				certificateNumber: user.certificateNumber,
				faceImg: user.faceImg
			}
			// 如果传递了gesture且不为空，则添加到更新对象中
			if (user.gesture != null && user.gesture !== '') {
				updateData.gesture = user.gesture
			}
			// 如果传递了password且不为空，则添加到更新对象中
			if (user.password != null && user.password !== '') {
				updateData.password = user.password
			}
			// 如果传递了fingerprint且不为空，则添加到更新对象中
			if (user.fingerprint != null && user.fingerprint !== '') {
				updateData.fingerprint = user.fingerprint
			}
			global.SqliteDb.updateData(global.userTableName, updateData, `id == '${user.id}'`, (result, err) => {
				if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
					const synchronizeData = {
						type: 'updateData',
						tableName: global.userTableName,
						data: updateData,
						query: `id == '${user.id}'`
					}
					commonMapper.httpSynchronizeData(synchronizeData)
				}
				resolve(!err)
			})
		})
	},
	delete: (id) => {
		return new Promise((resolve) => {
			global.SqliteDb.deleteData(global.userTableName, `id == '${id}'`, (result, err) => {
				if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
					const synchronizeData = {
						type: 'deleteData',
						tableName: global.userTableName,
						query: `id == '${id}'`
					}
					commonMapper.httpSynchronizeData(synchronizeData)
				}
				resolve(!err)
			})
		})
	},
	pageList: (page, size, user) => {
		return new Promise(async (resolve) => {
			let condition = 'isDeleted == "N"'
			if (user.name) {
				condition += ` AND (name like "%${user.name}%" OR policeNumber like "%${user.name}%")`
			}
			if (user.loginId) {
				condition += ` AND loginId like "%${user.loginId}%"`
			}
			if (user.sex) {
				condition += ` AND sex == "${user.sex}"`
			}
			if (user.age) {
				condition += ` AND age == "${user.age}"`
			}
			if (user.userType) {
				const userTypeArray = user.userType.includes(',') ? user.userType.split(',') : [user.userType]
				// 使用 IN 子句来匹配多个 userType
				condition += ` AND userType IN (${userTypeArray.map((type) => `"${type}"`).join(', ')})`
			}
			if (user.associatedPoliceId) {
				condition += ` AND (id == "${user.associatedPoliceId}" OR associatedPoliceId == "${user.associatedPoliceId}")`
			}
			try {
				const { data, total, totalPages } = await global.SqliteDb.getPagedData(global.userTableName, page, size, condition, 'id, loginId, name, age, sex, age, avatar, faceImg, role, loginTime, createTime, updateTime, userType, certificateNumber, associatedPoliceId, policeNumber')
				data.map((row) => {
					row.sexCn = SEX_ENUM[row.sex]
					delete row.password
				})
				resolve({ data, total, totalPages, success: true })
			} catch (error) {
				resolve({ data: [], total: 0, totalPages: 0, success: false })
			}
		})
	},
	getPersonList: () => {
		return new Promise((resolve) => {
			const query = `SELECT id, feature FROM ${global.userTableName} WHERE feature NOT NULL AND feature != '' AND isDeleted = ?`
			global.SqliteDb.executeQuery(query, ['N'], (rows, err) => {
				if (err) {
					resolve([])
				}
				rows.map((row) => {
					row.sexCn = SEX_ENUM[row.sex]
				})
				resolve(rows)
			})
		})
	},
	getFeaturePersonList: () => {
		return new Promise((resolve) => {
			const query = `SELECT id, fingerprint FROM ${global.userTableName} WHERE fingerprint NOT NULL AND fingerprint != '' AND isDeleted = ?`
			global.SqliteDb.executeQuery(query, ['N'], (rows, err) => {
				if (err) {
					resolve([])
				}
				rows.map((row) => {
					row.sexCn = SEX_ENUM[row.sex]
				})
				resolve(rows)
			})
		})
	},
	gestureSelect: (user) => {
		return new Promise((resolve) => {
			const query = `SELECT * FROM ${global.userTableName} WHERE gesture = ? AND isDeleted = ?`
			global.SqliteDb.executeQuery(query, [user.gesture, 'N'], (rows) => {
				resolve(rows)
			})
		})
	},
	updateFaceImg: (user) => {
		return new Promise((resolve, reject) => {
			global.SqliteDb.updateData(
				global.userTableName,
				{
					faceImg: user.faceImg,
					feature: user.feature
				},
				`loginId == '${user.loginId}'`,
				(result, err) => {
					if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
						const synchronizeData = {
							type: 'updateData',
							tableName: global.userTableName,
							data: {
								faceImg: user.faceImg,
								feature: user.feature
							},
							query: `loginId == '${user.loginId}'`
						}
						commonMapper.httpSynchronizeData(synchronizeData)
					}
					resolve(!err)
				}
			)
		})
	}
}

module.exports = UserMapper
