const { v4: uuidv4 } = require('uuid')
const http = require('../../../hardware/lib/request')
const fs = require('fs')
function synchronizeHttp(url) {
	return http({
		method: 'get',
		url,
		headers: {
			'auth-key': 'app'
		},
		params: {
			size: 9999
		}
	})
}

const commonMapper = {
	synchronizeALLData() {
		const arr = [synchronizeHttp(`http://${global.serverConfig.businessInfo.dataAddress}/api/user/allUserList`), synchronizeHttp(`http://${global.serverConfig.businessInfo.dataAddress}/api/business/getSidesAllList`), synchronizeHttp(`http://${global.serverConfig.businessInfo.dataAddress}/api/business/getIscdsUserList`), synchronizeHttp(`http://${global.serverConfig.businessInfo.dataAddress}/api/business/getAccessRecords`), synchronizeHttp(`http://${global.serverConfig.businessInfo.dataAddress}/api/app/getTerminalConfigInfo`)]
		Promise.all(arr).then(async ([res1, res2, res3, res4, res5]) => {
			const sanitizedRes3Data = res3.data.map(({ faceImg, ...rest }) => rest)
			const tableMap = {
				user: res1.data,
				sides: res2.data,
				sides_user: sanitizedRes3Data,
				access_records: res4.data.data
			}
			const { businessInfo, lockInfo } = res5.data
			const { mainCabinetModel, mainCabinetModelNum, secondaryCabinetModel, secondaryCabinetModelNum } = businessInfo
			const oldConfig = JSON.parse(fs.readFileSync(global.serverConfigPath))

			// 合并新配置
			const newBusinessInfo = {
				...oldConfig.businessInfo,
				mainCabinetModel,
				mainCabinetModelNum,
				secondaryCabinetModel,
				secondaryCabinetModelNum
			}
			const newConfig = {
				...oldConfig,
				businessInfo: newBusinessInfo,
				lockInfo
			}
			// 写入新配置
			fs.writeFileSync(global.serverConfigPath, JSON.stringify(newConfig))
			// 更新全局配置
			global.serverConfig = newConfig

			for (const key in tableMap) {
				global.SqliteDb.copyData(key).then(() => {
					tableMap[key].forEach((element) => {
						global.SqliteDb.insertData(key, element)
					})
				})
			}
		})
	},
	synchronizeData(data) {
		return new Promise(async (resolve) => {
			// console.log('传递的data', data)
			if (data.type == 'insertData') {
				global.SqliteDb.insertData(data.tableName, data.data)
			}
			if (data.type == 'updateData') {
				global.SqliteDb.updateData(data.tableName, data.data, data.query)
			}
			if (data.type == 'deleteData') {
				global.SqliteDb.deleteData(data.tableName, data.query)
			}
		})
	},
	httpSynchronizeData(data) {
		return http({
			method: 'POST',
			url: `http://${global.serverConfig.businessInfo.dataAddress}/api/business/synchronizeData`,
			data,
			headers: {
				'auth-key': 'app',
				'Content-Type': 'application/json'
			}
		})
	},
	//查询一个表的所有数据
	findAllList: (name) => {
		return new Promise((resolve) => {
			const sortMapping = {
				A: 1,
				B: 2,
				C: 3,
				D: 4,
				E: 5
			}
			let caseStatement = 'CASE name '
			for (const [letter, number] of Object.entries(sortMapping)) {
				caseStatement += `WHEN '${letter}' THEN ${number} `
			}
			caseStatement += 'ELSE 999 END'

			// 构建查询语句
			const query = `SELECT * FROM ${name} ORDER BY ${caseStatement}`
			// const query = `SELECT * FROM ${name}`
			global.SqliteDb.executeQuery(query, [], (rows, err) => {
				if (err) {
					resolve({ success: false, data: null })
				} else {
					resolve({ success: true, data: rows })
				}
			})
		})
	},
	//分页数据
	findPageist: (name, params) => {
		return new Promise(async (resolve) => {
			const orderBy = 'ORDER BY openTime DESC'
			let condition = ''
			for (const key in params) {
				if (['page', 'size'].indexOf(key) === -1 && params[key]) {
					const value = params[key]
					if (condition) {
						condition += ' AND '
					}
					if (key == 'startTime') {
						condition += `openTime >= '${value}'`
					} else if (key == 'endTime') {
						condition += `openTime <= '${value}'`
					} else if (key == 'operatorName') {
						condition += `${key} like "%${value}%"`
					} else if (key == 'operatorType') {
						const safeValue = value
							.split(',')
							.map((v) => `'${v.trim()}'`)
							.join(',')
						condition += `operatorType IN (${safeValue})`
					} else {
						condition += `${key} = '${value}'`
					}
				}
			}
			try {
				const { page = 1, size = 10 } = params
				const { data, total, totalPages } = await global.SqliteDb.getPagedData(name, page, size, condition, '*', orderBy)
				resolve({ data, total, totalPages, success: true })
			} catch (error) {
				resolve({ data: [], total: 0, totalPages: 0, success: false })
			}
		})
	},
	//用户关联表，根据userId从用户那边拿照片
	findIscdsUserList: (tableName) => {
		return new Promise((resolve, reject) => {
			const query1 = `SELECT * FROM ${tableName} WHERE isDeleted = 'N'`
			global.SqliteDb.executeQuery(query1, [], (rows1, err1) => {
				if (err1) {
					return reject({ success: false, error: err1 })
				}
				const promises = rows1.map((item) => {
					return new Promise((innerResolve, innerReject) => {
						const query2 = `SELECT faceImg FROM user WHERE id = ? AND isDeleted = ?`
						global.SqliteDb.executeQuery(query2, [item.userId, 'N'], (rows2, err2) => {
							if (err2) {
								return innerReject({ success: false, error: err2 })
							}
							if (rows2.length > 0) {
								item.faceImg = rows2[0].faceImg
								innerResolve(item)
							} else {
								item.faceImg = null
								innerResolve(item)
							}
						})
					})
				})
				Promise.all(promises)
					.then((updatedRows) => {
						resolve({ success: true, data: updatedRows })
					})
					.catch((error) => {
						reject({ success: false, error })
					})
			})
		})
	},
	//查询一个表的存在的所有数据
	findExistAllList: (name, order = false) => {
		return new Promise((resolve) => {
			const defaultOrderDirection = 'ASC'
			const orderClause = order ? `ORDER BY CASE userType WHEN 1 THEN 1 WHEN 2 THEN 2 WHEN 3 THEN 3 END ${defaultOrderDirection}` : ''
			const query = `SELECT * FROM ${name} WHERE isDeleted = 'N' ${orderClause}`
			global.SqliteDb.executeQuery(query, [], (rows, err) => {
				if (err) {
					resolve({ success: false, data: null })
				} else {
					resolve({ success: true, data: rows })
				}
			})
		})
	},
	//修改表的数据
	updateList: (name, id, data) => {
		return new Promise((resolve) => {
			global.SqliteDb.updateData(name, data, `id == '${id}'`, (result, err) => {
				if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
					const synchronizeData = {
						type: 'updateData',
						query: `id == '${id}'`,
						tableName: name,
						data
					}
					commonMapper.httpSynchronizeData(synchronizeData)
				}
				resolve(!err)
			})
		})
	},
	// 修改柜子列表
	updateSidesList: (data) => {
		const condition = `1 = 1`
		global.SqliteDb.deleteData('sides', condition)
		const defaultSides = { id: uuidv4(), name: 'A', cabinetType: '1', cabinetId: data.mainCabinetModel, gf: data.mainCabinetModelNum }
		global.SqliteDb.insertData('sides', defaultSides)
		const arr1 = data.secondaryCabinetModel.split(',')
		const arr2 = data.secondaryCabinetModelNum.split(',')
		const changeLength = data.secondaryCabinetModel ? arr1.length : 0
		for (let i = 0; i < changeLength; i++) {
			const name = String.fromCharCode(65 + i + 1)
			const defaultSides = { id: uuidv4(), name, cabinetType: '2', cabinetId: arr1[i], gf: arr2[i] }
			global.SqliteDb.insertData('sides', defaultSides)
		}
		///这里的同步再想想
	},
	//新增柜格关联人员
	addIscdsUserList: (data) => {
		data.id = uuidv4()
		return new Promise((resolve, reject) => {
			global.SqliteDb.insertData('sides_user', data, (err, result) => {
				if (err) {
					reject(err)
				} else {
					if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
						const synchronizeData = {
							type: 'insertData',
							tableName: 'sides_user',
							data
						}
						commonMapper.httpSynchronizeData(synchronizeData)
					}
					resolve(result)
				}
			})
		})
	},

	//批量删除
	batchDelete: (name, idsLabel, idsValue) => {
		return new Promise((resolve) => {
			const safeValue = idsValue
				.split(',')
				.map((v) => `'${v.trim()}'`)
				.join(',')
			const query = `${idsLabel} in (${safeValue})`
			global.SqliteDb.deleteData(name, query, (result, err) => {
				if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
					const synchronizeData = {
						type: 'deleteData',
						tableName: name,
						query
					}
					commonMapper.httpSynchronizeData(synchronizeData)
				}
				resolve(!err)
			})
		})
	},
	batchFlagDelete: (name, idsLabel, idsValue) => {
		return new Promise((resolve) => {
			const safeValue = idsValue
				.split(',')
				.map((v) => `'${v.trim()}'`)
				.join(',')
			const query = `isDeleted = 'N' AND ${idsLabel} in (${safeValue})`
			const data = { isDeleted: 'Y' }
			global.SqliteDb.updateData(name, data, query, (result, err) => {
				if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
					const synchronizeData = {
						type: 'updateData',
						tableName: name,
						data,
						query
					}
					console.log(synchronizeData, 'synchronizeData')
					commonMapper.httpSynchronizeData(synchronizeData)
				}
				resolve(!err)
			})
		})
	},
	//更新关联的柜格
	updateIscdsList: (params) => {
		const sidesNames = params.sidesNames.split(',')
		const query = `SELECT * FROM sides_user WHERE userId = '${params.userId}'`
		global.SqliteDb.executeQuery(query, [], (rows) => {
			// 进行差异化处理
			const existingNames = rows.map((item) => item.sidesName)
			const addArr = sidesNames.filter((item) => !existingNames.includes(item))
			const delArr = existingNames.filter((item) => !sidesNames.includes(item))
			// 删除
			const data = { isDeleted: 'Y' }
			const condition = `isDeleted = 'N' AND sidesName IN (${delArr.map((name) => `'${name}'`).join(',')})`
			global.SqliteDb.updateData('sides_user', data, condition)
			if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
				const synchronizeData = {
					type: 'updateData',
					tableName: 'sides_user',
					query: condition,
					data
				}
				console.log('synchronizeData', synchronizeData)
				commonMapper.httpSynchronizeData(synchronizeData)
			}
			//增加
			for (let i = 0; i < addArr.length; i++) {
				const defaultSides = {
					id: uuidv4(),
					sidesName: addArr[i],
					userType: params.userType,
					userId: params.userId,
					userName: params.userName,
					isDeleted: 'N'
				}
				global.SqliteDb.insertData('sides_user', defaultSides)
				if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
					const synchronizeData = {
						type: 'insertData',
						tableName: 'sides_user',
						data: defaultSides
					}
					commonMapper.httpSynchronizeData(synchronizeData)
				}
			}
		})
	},
	//新增记录
	addList: (name, data) => {
		data = { ...data, id: data.id || uuidv4() }
		return new Promise((resolve, reject) => {
			global.SqliteDb.insertData(name, data, (err, result) => {
				if (err) {
					reject(err)
				} else {
					if (global.serverConfig.businessInfo.mainScreenType != 1 && global.serverConfig.businessInfo.dataAddress) {
						const synchronizeData = {
							type: 'insertData',
							tableName: name,
							data
						}
						commonMapper.httpSynchronizeData(synchronizeData)
					}
					resolve(result)
				}
			})
		})
	},
	// 删除所有非管理员用户
	deleteUserNoAdmin: () => {
		return new Promise((resolve, reject) => {
			const condition1 = `userType != 1`
			const data = { isDeleted: 'Y' }
			global.SqliteDb.updateData('user', data, condition1, (result, err) => {
				if (err) {
					return reject(err)
				}
				const condition2 = `isDeleted = 'N'`
				global.SqliteDb.updateData('sides_user', data, condition2)
				resolve(!err)
			})
		})
	}
}

module.exports = commonMapper
