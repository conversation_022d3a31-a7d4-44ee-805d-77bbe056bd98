const { CODE_DIC, CODE_ENUM } = require('../enum')
const DIC = require('../dic')
const Response = require('../response/index')

const dicController = {
	getDicInfo: (res, params) => {
		let data = {}
		if (!params.type) {
			data = DIC
		} else {
			const types = params.type.split(',')
			types.forEach((type) => {
				data[type] = DIC[type.toUpperCase()] || []
			})
		}
		Response.result(res, { code: CODE_ENUM.OK, msg: CODE_DIC[CODE_ENUM.OK], data })
	}
}

module.exports = dicController
