const SystemService = require('../service/system')
const Response = require('../response/index')

const SystemController = {
	getNetworkInfo: async (res) => {
		const R = await SystemService.getNetworkInfo()
		Response.result(res, R)
	},
	setNetworkInfo: async (res, body) => {
		const R = await SystemService.setNetworkInfo(body)
		Response.result(res, R)
	},
	rebootSystem: async (res) => {
		const R = await SystemService.rebootSystem()
		Response.result(res, R)
	},
	closeSystem: async (res) => {
		const R = await SystemService.closeSystem()
		Response.result(res, R)
	},
	getAllDisplays: (res) => {
		Response.result(res, SystemService.getAllDisplays())
	},
	pingIp: async (res, params) => {
		const R = await SystemService.pingIp(params.ip)
		Response.result(res, R)
	},
	handlePanel: async (res, params) => {
		const R = await SystemService.handlePanel(params.type)
		Response.result(res, R)
	},
	handleDisplayMode: async (res, params) => {
		const R = await SystemService.handleDisplayMode(params.mode)
		Response.result(res, R)
	},
	handleMouseCursorMode: async (res, params) => {
		const R = await SystemService.handleMouseCursorMode(params.mode)
		Response.result(res, R)
	},
	getSystemProperties: async (res, params) => {
		const data = await SystemService.getSystemProperties(params.searchProps)
		Response.result(res, { data })
	}
}

module.exports = SystemController
