const itemNumberService = require('../service/item_number')
const Response = require('../response/index')

const itemNumberController = {
	getAllItemNumber: async (res) => {
		const R = await itemNumberService.getAllItemNumber()
		Response.result(res, R)
	},
	checkItemNumber: async (res, params) => {
		const R = await itemNumberService.checkItemNumber(params)
		Response.result(res, R)
	}
}

module.exports = itemNumberController
