const { CODE_ENUM } = require('../enum')
const FileService = require('../service/file')
const Response = require('../response/index')

const FileController = {
	getLogs: async (res) => {
		const archiveName = 'log.zip'
		const fileStream = await FileService.getLogs(archiveName)
		if (!fileStream) {
			Response.result(res, { code: CODE_ENUM.SERVER_ERROR, msg: '获取日志失败' })
		}
		fileStream.pipe(res)
		Response.resultFileStream(res, fileStream, { 'Content-Disposition': `attachment; filename=${archiveName}` })
	},
	upload: async (res, body, req) => {
		const boundary = req.headers['content-type'].split('; ')[1].replace('boundary=', '')
		const R = await FileService.upload(boundary, body)
		Response.result(res, R)
	},
	saveReport: (res, body) => {
		const { fileName } = body
		const R = FileService.saveReport(fileName)
		Response.result(res, R)
	},
	copyAuthFile: (res, body) => {
		const { fileName } = body
		const R = FileService.copyAuthFile(fileName)
		Response.result(res, R)
	},
	copyConfigFile: (res, body) => {
		const { fileName } = body
		const R = FileService.copyConfigFile(fileName)
		Response.result(res, R)
	},
	copyDbFile: (res, body) => {
		const { fileName } = body
		const R = FileService.copyDbFile(fileName)
		Response.result(res, R)
	}
}

module.exports = FileController
