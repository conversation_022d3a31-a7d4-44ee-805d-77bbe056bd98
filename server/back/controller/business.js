const businessService = require('../service/business')
const Response = require('../response/index')
const { ipcMain } = require('electron')

const businessController = {
	//获取所有柜子
	getSidesAllList: async (res) => {
		const R = await businessService.getSidesAllList()
		Response.result(res, R)
	},
	//获取所有正在使用的柜格
	getIscdsUserList: async (res) => {
		const R = await businessService.getIscdsUserList()
		Response.result(res, R)
	},
	//获取柜格统计数据
	getIscdsUserNum: async (res) => {
		const R = await businessService.getIscdsUserNum()
		Response.result(res, R)
	},
	//新增用户柜子关系
	addIscdsUserList: async (res, params) => {
		const R = await businessService.addIscdsUserList(params)
		Response.result(res, R)
	},
	//新增存取记录
	addAccessRecords: async (res, params) => {
		const R = await businessService.addAccessRecords(params)
		Response.result(res, R)
	},
	//存取记录
	getAccessRecords: async (res, params) => {
		const R = await businessService.getAccessRecords(params)
		Response.result(res, R)
	},
	//删除用户柜子关系
	delIscdsUserList: async (res, params) => {
		const R = await businessService.delIscdsUserList(params.ids)
		Response.result(res, R)
	},
	//新增用户柜子关系
	updateIscdsUserStatus: async (res, params) => {
		const R = await businessService.updateIscdsUserStatus(params)
		Response.result(res, R)
	},
	//远程开柜 只开柜与业务开柜
	remoteOpenLock: async (res, params) => {
		const webContents = global.mainWin.webContents
		webContents.send('remote-open-lock', params)
		ipcMain.on('remote-open-lock-result', (event, arg) => {
			Response.result(res, arg)
			ipcMain.removeAllListeners('remote-open-lock-result')
		})
		// if (params.onlyOpen) {
		// 	const R = await businessService.remoteLock(params)
		// 	Response.result(res, R)
		// } else {
		// 	const webContents = global.mainWin.webContents
		// 	webContents.send('remote-open-lock', params)
		// 	ipcMain.on('remote-open-lock-result', (event, arg) => {
		// 		Response.result(res, arg)
		// 		ipcMain.removeAllListeners('remote-open-lock-result')
		// 	})
		// }
	},
	//同步数据
	synchronizeData: async (res, params) => {
		const R = await businessService.synchronizeData(params)
		Response.result(res, R)
	},
	//同步所有数据
	synchronizeAllData: async (res, params) => {
		const R = await businessService.synchronizeAllData(params)
		Response.result(res, R)
	},
	// 改变线上模式的socket连接状态
	connectionSocket: async (res, params) => {
		const R = await businessService.connectionSocket(params)
		Response.result(res, R)
	}
}

module.exports = businessController
