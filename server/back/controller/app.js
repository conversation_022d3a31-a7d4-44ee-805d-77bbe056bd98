const { CODE_ENUM, CODE_DIC } = require('../enum')
const appService = require('../service/app')
const Response = require('../response/index')
const { getFileNameByPath } = require('../tools/index')

const appController = {
	getTerminalConfigInfo: async (res) => {
		const R = await appService.getTerminalConfigInfo()
		Response.result(res, R)
	},
	setTerminalConfigInfo: (res, body) => {
		Response.result(res, appService.setTerminalConfigInfo(body))
	},
	uninstallApp: async (res, body) => {
		const R = await appService.uninstallApp(body.appName)
		Response.result(res, R)
	},
	rebootApp: async (res) => {
		const R = await appService.rebootApp()
		if (res) {
			Response.result(res, R)
		} else {
			return Promise.resolve(R)
		}
	},
	closeApp: (res) => {
		Response.result(res, { code: CODE_ENUM.OK, msg: CODE_DIC[CODE_ENUM.OK] })
		appService.closeApp()
	},
	installPackage: async (res, body) => {
		const R = await appService.installPackage(body)
		Response.result(res, R)
	},
	openDevTools: (res) => {
		if (!global.mainWin) {
			Response.result(res, { code: CODE_ENUM.SERVER_ERROR, msg: CODE_DIC[CODE_ENUM.SERVER_ERROR] })
			return
		}
		global.mainWin.webContents.openDevTools()
		Response.result(res, { code: CODE_ENUM.OK, msg: '打开控制台成功' })
	},
	setFullScreen: (res, body) => {
		const { model } = body
		if (!global.mainWin) {
			Response.result(res, { code: CODE_ENUM.SERVER_ERROR, msg: CODE_DIC[CODE_ENUM.SERVER_ERROR] })
			return
		}
		global.mainWin.setFullScreen(model == 1)
		Response.result(res, { code: CODE_ENUM.OK, msg: CODE_DIC[CODE_ENUM.OK] })
	},
	initIotServerLink: async (res, body) => {
		const R = await appService.initIotServerLink(body)
		if (res) {
			Response.result(res, R)
		} else {
			return Promise.resolve(R)
		}
	},
	closeIotServerLink: async (res) => {
		const R = await appService.closeIotServerLink()
		if (res) {
			Response.result(res, R)
		} else {
			return Promise.resolve(R)
		}
	},
	exportDevicesInfo: async (res) => {
		const fileName = getFileNameByPath(global.licenseDeviceInfoPath)
		try {
			const fileStream = await appService.exportDevicesInfo()
			if (!fileStream) {
				throw new Error('获取设备授权信息失败')
			}
			fileStream.pipe(res)
			Response.resultFileStream(res, fileStream, { 'Content-Disposition': `attachment; filename=${fileName}` })
		} catch (error) {
			Response.resultFileStream(res, null, { 'Content-Disposition': `attachment; filename=${fileName}` })
		}
	},
	getDeviceAuthInfo: async (res) => {
		const R = await appService.getDeviceAuthInfo()
		Response.result(res, R)
	},
	exportConfigFile: (res) => {
		const fileName = getFileNameByPath(global.serverConfigPath)
		const fileStream = appService.exportConfigFile()
		fileStream.pipe(res)
		Response.resultFileStream(res, fileStream, { 'Content-Disposition': `attachment; filename=${fileName}` })
	},
	exportDbFile: (res) => {
		const fileName = getFileNameByPath(global.databasePath)
		const fileStream = appService.exportDbFile()
		fileStream.pipe(res)
		Response.resultFileStream(res, fileStream, { 'Content-Disposition': `attachment; filename=${fileName}` })
	},
	getDesktopCapturerSources: async (res) => {
		const R = await appService.getDesktopCapturerSources()
		Response.result(res, R)
	},
	upgradeApp: async (res, body) => {
		appService.upgradeApp(body)
		Response.result(res, { code: CODE_ENUM.OK, msg: '正在更新中请稍后！' })
	}
}

module.exports = appController
