const HardwareService = require('../service/hardware')
const Response = require('../response/index')

const HardwareController = {
	openSoftKeyboard: async (res) => {
		const R = await HardwareService.openSoftKeyboard()
		Response.result(res, R)
	},
	closeSoftKeyboard: async (res) => {
		const R = await HardwareService.closeSoftKeyboard()
		Response.result(res, R)
	},
	getSerialPortList: async (res) => {
		const R = await HardwareService.getSerialPortList()
		Response.result(res, R)
	},
	batchTestSerialPort: async (res, body) => {
		const R = await HardwareService.batchTestSerialPort(body)
		Response.result(res, R)
	},
	openControlLock: async (res, body) => {
		const { com, baudRate } = body
		const R = await HardwareService.openControlLock(com, baudRate)
		Response.result(res, R)
	}
}

module.exports = HardwareController
