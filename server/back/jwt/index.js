const jwt = require('jsonwebtoken')
const secret = '0123456789ABCDEF' // 密钥
const expir = 60 * 30 // 30min(token过期的时间)

function generateRandomHex36() {
	return Math.random().toString(36).slice(-10)
}

// 生成token
function generateToken() {
	return jwt.sign({ key: generateRandomHex36() }, secret, { expiresIn: expir })
}

// 校验token
function verifyToken(token) {
	try {
		return jwt.verify(token, secret)
	} catch {
		return false
	}
}

module.exports = { verifyToken, generateToken }
