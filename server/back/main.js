const electron = require('electron')
const net = require('net')
const path = require('path')
const express = require('express')
const cors = require('cors')
const url = require('url')
const serveStatic = require('serve-static')
const SQLiteDB = require('./sqlite/index')
const devHandler = require('./sqlite/dev')
const prodHandler = require('./sqlite/prod')
const WebSocketServer = require('./webSocket/index')
const SystemController = require('./controller/system')
const AppController = require('./controller/app')
const UserController = require('./controller/user')
const FileController = require('./controller/file')
const HardwareController = require('./controller/hardware')
const itemNumberController = require('./controller/item_number')
const dicController = require('./controller/dic')
const businessController = require('./controller/business')
const Response = require('./response/index')
const Request = require('./request/index')
const api = require('./api/index')
const { CODE_ENUM, CODE_DIC } = require('./enum')
const { SYSTEM_PLATFORM_STR_DIC } = require('../../hardware/enum')
const { verifyToken } = require('./jwt/index')
const Logger = require('../../hardware/log/index')
const log = new Logger('back-server')
const app = express()

const apiControllerMap = {
	system: SystemController,
	user: UserController,
	app: AppController,
	file: FileController,
	hardware: HardwareController,
	itemNumber: itemNumberController,
	dic: dicController,
	business: businessController
}
let webSocketServer

function showMessageBox(message) {
	electron.dialog
		.showMessageBox({
			type: 'info',
			title: '提示',
			message,
			buttons: ['确定']
		})
		.then(() => {
			openServerConfig()
			electron.app.quit()
		})
}
function checkPort(port) {
	return new Promise((resolve, reject) => {
		const server = net.createServer().listen(port)
		server.once('error', (err) => {
			if (err.code === 'EADDRINUSE') {
				log.info(`端口${port}被占用`)
				showMessageBox(`端口${port}被占用,请修改配置文件重启应用！`)
				resolve(false)
			}
		})
		server.once('close', () => {
			log.info(`端口${port}可用，使用端口${port}`)
			resolve(true)
		})
		server.once('listening', () => {
			server.close()
			resolve(true)
		})
	})
}
function openServerConfig() {
	if (process.platform == SYSTEM_PLATFORM_STR_DIC.LINUX) {
		electron.shell.openPath(global.serverConfigPath)
	} else {
		electron.shell.openExternal(global.serverConfigPath)
	}
}

module.exports = {
	async init() {
		const bool = (await checkPort(global.serverConfig.backHttpServerPort)) && (await checkPort(global.serverConfig.backWebSocketServerPort))
		if (!bool) {
			return
		}
		try {
			global.SqliteDb = new SQLiteDB(global.databasePath)
			electron.app.isPackaged ? prodHandler() : devHandler()
		} catch (error) {
			log.error(`handle sqlite error: ${error}`)
		}
		webSocketServer = new WebSocketServer()
		AppController.initIotServerLink(null, global.serverConfig.iotInfo)

		// 生产环境配置静态资源
		if (electron.app.isPackaged) {
			app.use(
				serveStatic(path.join(process.resourcesPath, 'app.asar', 'dist'), {
					index: ['web.html', 'client.html']
				})
			)
		}

		// 使用中间件
		app.use(cors())
		// app.use(express.json())
		app.use(async (req, res, next) => {
			res.setHeader('Cache-Control', 'no-cache, no-store, max-age=0, must-revalidate')
			const parseUrl = url.parse(req.url, true)
			const info = parseUrl.pathname.split('/')
			if (!apiControllerMap[info[2]]) {
				Response.result(res, { code: CODE_ENUM.NOT_FOUND, msg: CODE_DIC[CODE_ENUM.NOT_FOUND] })
				return
			}
			// 匹配接口
			const apiList = api[info[2]]
			const apiObj = apiList.find((item) => item.url === parseUrl.pathname)
			if (!apiObj) {
				Response.result(res, { code: CODE_ENUM.NOT_FOUND, msg: CODE_DIC[CODE_ENUM.NOT_FOUND] })
				return
			}
			// 请求方法验证
			const requestMethodValid = Request.parse(req, res, { method: apiObj.method })
			if (!requestMethodValid) {
				return
			}
			// 请求参数验证
			let requestParams
			if (apiObj.method == 'POST') {
				requestParams = await Request.parsePostBody(req, res, apiObj.data)
			} else {
				requestParams = Request.parseGetParams(req, res, apiObj.params)
			}
			if (!requestParams) {
				return
			}
			const apiName = () => {
				try {
					apiControllerMap[info[2]][info[3]](res, requestParams, req)
				} catch (error) {
					Response.result(res, { code: CODE_ENUM.SERVER_ERROR, msg: CODE_DIC[CODE_ENUM.SERVER_ERROR] })
				}
			}
			// 免验证
			if (apiObj.isNotValid) {
				return apiName()
			}
			const requestHeader = req.headers
			// 鉴权

			if (apiObj.openObj && apiObj.openObj.includes(requestHeader['auth-key'])) {
				return apiName()
			}
			const token = requestHeader['authorization']
			if (!token) {
				return Response.result(res, { code: CODE_ENUM.UNAUTHORIZED, msg: CODE_DIC[CODE_ENUM.UNAUTHORIZED] })
			}
			const bool = verifyToken(token)
			if (!bool) {
				return Response.result(res, { code: CODE_ENUM.UNAUTHORIZED, msg: CODE_DIC[CODE_ENUM.UNAUTHORIZED] })
			}
			return apiName()
		})

		// 启动服务器
		app.listen(global.serverConfig.backHttpServerPort, () => {
			log.info(`back服务已启动，启动端口为：${global.serverConfig.backHttpServerPort}`)
		})
		app.on('error', (err) => {
			log.error(`back服务启动失败：${err}`)
		})
	},
	close() {
		webSocketServer && webSocketServer.close()
		webSocketServer = null
		global.SqliteDb && global.SqliteDb.closeConnection()
		global.SqliteDb = null
		AppController.closeIotServerLink()
	}
}
