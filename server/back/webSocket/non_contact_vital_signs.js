const CommonWebSocket = require('./common')
const { CODE_ENUM, ACTION_TYPE_DIC } = require('../../../hardware/enum')
const STWCNonContactVitalSigns = require('../../../hardware/vital_signs/http/stwc')

class NonContactVitalSignsWebSocketServer extends CommonWebSocket {
	#vitalSigns = null
	constructor() {
		if (NonContactVitalSignsWebSocketServer.instance) {
			return NonContactVitalSignsWebSocketServer.instance
		}
		super()
		NonContactVitalSignsWebSocketServer.instance = this
	}
	handle(clientData) {
		const { params, clientIp } = clientData
		const ws = this.allClients[clientIp]
		switch (params.action) {
			case ACTION_TYPE_DIC.INIT:
				this.#initLink(clientData)
				break
			case ACTION_TYPE_DIC.CLOSE:
				this.closeLink(clientData)
				break
			default:
				this.handleLinkErrorByAction(ws, params.action)
				break
		}
	}
	#initLink(clientData) {
		let { params } = clientData
		const { clientIp } = clientData
		params = Object.assign(global.serverConfig.nonContactVitalSignsInfo, params)
		const ws = this.allClients[clientIp]
		if (this.#vitalSigns) {
			return this.handleSuccessByAction(ws, params.action)
		}
		if (params.manufacturer == 1) {
			this.#initSTWCNonContactVitalSignsLink(ws, params)
		} else {
			return this.handleParamsErrorByAction(ws, params.action)
		}
	}
	#initSTWCNonContactVitalSignsLink(ws, params) {
		this.#vitalSigns = new STWCNonContactVitalSigns()
		this.#vitalSigns.on('vital-signs-callback', (arg) => {
			const { action, code, msg, data } = arg
			if (action == ACTION_TYPE_DIC.INIT) {
				code != CODE_ENUM.OK && this.removeListeners()
			} else if (action == ACTION_TYPE_DIC.CLOSE) {
				this.removeListeners()
			}
			const openClient = this.getOpenClient()
			openClient.forEach((client) => {
				this.resultWsClient(client, { action, code, msg, data })
			})
		})
		this.#vitalSigns.init(params)
	}
	closeLink(clientData) {
		const { clientIp } = clientData
		const ws = this.allClients[clientIp]
		if (!ws) {
			return
		}
		if (!this.#vitalSigns) {
			return this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
		}
		const openClient = this.getOpenClient()
		if (openClient.length > 1) {
			return this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
		}
		this.#vitalSigns.close()
	}
	removeListeners() {
		this.#vitalSigns.removeAllListeners('vital-signs-callback')
		this.#vitalSigns = null
	}
}

module.exports = NonContactVitalSignsWebSocketServer
