const CommonWebSocket = require('./common')
const { CODE_ENUM, ACTION_TYPE_DIC } = require('../../../hardware/enum')
const WristbandLockServer = require('../../../hardware/control_lock/serialport/index')
const wristbandLockServer = new WristbandLockServer()
const DiChuanSpTelescopicLockServer = require('../../../hardware/control_lock/serialport/gxx_d99_1')
const diChuanSpTelescopicLockServer = new DiChuanSpTelescopicLockServer()

const lockMap = {
	1: wristbandLockServer,
	2: diChuanSpTelescopicLockServer
}

class ControlLockWebSocketServer extends CommonWebSocket {
	#lock = null
	constructor() {
		if (ControlLockWebSocketServer.instance) {
			return ControlLockWebSocketServer.instance
		}
		super()
		ControlLockWebSocketServer.instance = this
	}
	handle(clientData) {
		const { action } = clientData.params
		const ws = this.allClients[clientData.clientIp]
		switch (action) {
			case ACTION_TYPE_DIC.INIT:
				this.#initLink(clientData)
				break
			case ACTION_TYPE_DIC.OPEN_LOCK:
				this.#openLock(clientData)
				break
			case ACTION_TYPE_DIC.CLOSE_LOCK:
				this.#closeLock(clientData)
				break
			case ACTION_TYPE_DIC.CLOSE:
				this.closeLink(clientData)
				break
			default:
				this.handleParamsErrorByAction(ws, action)
				break
		}
	}
	#initLink(clientData) {
		let { params } = clientData
		params = Object.assign(global.serverConfig.wristbandDrawerInfo, params)
		const ws = this.allClients[clientData.clientIp]
		if (!params.path || !Object.keys(lockMap).includes(String(params.manufacturer))) {
			return this.handleParamsErrorByAction(ws, params.action)
		}
		if (this.#lock) {
			return this.handleSuccessByAction(ws, params.action)
		}
		this.#lock = lockMap[params.manufacturer]
		this.#lock.on('control-lock-callback', (arg) => {
			const { action, code, msg, data } = arg
			if (action == ACTION_TYPE_DIC.INIT) {
				code != CODE_ENUM.OK && this.removeListeners()
			} else if (action == ACTION_TYPE_DIC.CLOSE) {
				this.removeListeners()
			}
			const openClient = this.getOpenClient()
			openClient.forEach((client) => {
				this.resultWsClient(client, { action, code, msg, data })
			})
		})
		this.#lock.init(params.path, params.baudRate)
	}
	#openLock(clientData) {
		const { clientIp, params } = clientData
		const ws = this.allClients[clientIp]
		if (!this.#lock) {
			return this.handleLinkErrorByAction(ws, params.action)
		}
		this.#lock.openLock()
	}
	#closeLock(clientData) {
		const { clientIp, params } = clientData
		const ws = this.allClients[clientIp]
		if (!this.#lock) {
			return this.handleLinkErrorByAction(ws, params.action)
		}
		this.#lock.closeLock()
	}
	closeLink(clientData) {
		const ws = this.allClients[clientData.clientIp]
		if (this.#lock) {
			this.#lock.close()
		} else {
			this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
		}
	}
	removeListeners() {
		this.#lock && this.#lock.removeAllListeners('control-lock-callback')
		this.#lock = null
	}
}

module.exports = ControlLockWebSocketServer
