const WebSocket = require('ws')
const { app } = require('electron')
const Logger = require('../../../hardware/log/index')
const log = new Logger('ws-server')
const { DATA_CODE_DIC, DATA_CODE_ENUM, CODE_ENUM, CODE_DIC } = require('../../../hardware/enum')
const { getDeviceAuthInfo } = require('../tools/index')
class CommonWebSocket {
	constructor() {
		this.logger = log
		this.allClients = {}
	}
	handle() {}
	closeLink() {}
	removeListeners() {}
	async #validateAuthState() {
		const { data } = await getDeviceAuthInfo()
		return data.state
	}
	addWsClient(client) {
		const { clientIp, ws } = client
		this.allClients[clientIp] = ws
		// 监听客户端推送的消息
		ws.on('message', async (message) => {
			const authState = app.isPackaged ? await this.#validateAuthState() : true
			if (!authState) {
				this.logger.info('鉴权失败！')
				return this.resultWsClient(ws, {
					action: 'auth',
					code: CODE_ENUM.IS_NOT_ACCESS,
					msg: CODE_DIC[CODE_ENUM.IS_NOT_ACCESS]
				})
			}
			this.logger.info(`${clientIp} Received: ${message}`)
			try {
				// 尝试将消息解析为 JSON 对象
				const parsedMessage = JSON.parse(message)
				if (typeof parsedMessage === 'object' && parsedMessage !== null) {
					this.handle({ clientIp, params: parsedMessage })
				} else {
					ws.send('Received message is not a valid JSON object.')
				}
			} catch (error) {
				ws.send(`Failed to parse message:${error}`)
			}
		})
		// 客户端关闭连接
		ws.on('close', () => {
			this.logger.info(`${clientIp} Client disconnected`)
			// delete this.allClients[clientIp]
			const openClient = this.getOpenClient()
			if (openClient.length == 0) {
				return this.closeLink({ clientIp })
			}
		})
	}
	/**
	 * 获取当前连接正常的客户端
	 */
	getOpenClient() {
		const openClient = []
		for (const key in this.allClients) {
			const client = this.allClients[key]
			if (client.readyState === WebSocket.OPEN) {
				openClient.push(client)
			}
		}
		return openClient
	}
	resultWsClient(ws, arg) {
		ws && ws.send(JSON.stringify(arg))
	}
	handleSuccessByAction(ws, action, data) {
		this.resultWsClient(ws, {
			action,
			code: CODE_ENUM.OK,
			msg: CODE_DIC[CODE_ENUM.OK],
			data: {
				flag: DATA_CODE_ENUM.ACTION_SUCCESS,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_SUCCESS],
				...data
			}
		})
	}
	handleLinkErrorByAction(ws, action) {
		this.resultWsClient(ws, {
			action,
			code: CODE_ENUM.LINK_ERROR,
			msg: CODE_DIC[CODE_ENUM.LINK_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			}
		})
	}
	handleParamsErrorByAction(ws, action) {
		this.resultWsClient(ws, {
			action,
			code: CODE_ENUM.PARAMS_ERROR,
			msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			}
		})
	}
	handleSendMessageErrorByAction(ws, action) {
		this.resultWsClient(ws, {
			action,
			code: CODE_ENUM.SEND_MESSAGE_ERROR,
			msg: CODE_DIC[CODE_ENUM.SEND_MESSAGE_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
			}
		})
	}
}

module.exports = CommonWebSocket
