const WebSocket = require('ws')
const QrCodeWebSocketServer = require('./qr_code')
const LightWebSocketServer = require('./light')
const LockWebSocketServer = require('./lock')
const ControlLockWebSocketServer = require('./control_lock')
const RfidWebSocketServer = require('./rfid')
const UsbWebSocketServer = require('./usb')
const SignFingerWebSocketServer = require('./sign_finger')
const ContactVitalSignsWebSocketServer = require('./contact_vital_signs')
const NonContactVitalSignsWebSocketServer = require('./non_contact_vital_signs')

class WebSocketServer {
	constructor() {
		this.wss = null
		this.qrCodeWebSocketServer = null
		this.lightWebSocketServer = null
		this.lockWebSocketServer = null
		this.controlLockWebSocketServer = null
		this.rfidWebSocketServer = null
		this.usbWebSocketServer = null
		this.signFingerWebSocketServer = null
		this.contactVitalSignsWebSocketServer = null
		this.nonContactVitalSignsWebSocketServer = null
		this.init()
	}
	init() {
		this.wss = new WebSocket.Server({ port: global.serverConfig.backWebSocketServerPort })
		this.wss.on('close', () => {
			this.wss = null
			this.qrCodeWebSocketServer = null
			this.lightWebSocketServer = null
			this.lockWebSocketServer = null
			this.controlLockWebSocketServer = null
			this.rfidWebSocketServer = null
			this.usbWebSocketServer = null
			this.signFingerWebSocketServer = null
			this.contactVitalSignsWebSocketServer = null
			this.nonContactVitalSignsWebSocketServer = null
		})
		this.wss.on('connection', (ws, req) => {
			// 获取客户端的 IP 地址
			const clientIp = req.connection.remoteAddress
			switch (req.url) {
				case '/qrCode':
					if (!this.qrCodeWebSocketServer) {
						this.qrCodeWebSocketServer = new QrCodeWebSocketServer()
					}
					this.qrCodeWebSocketServer.addWsClient({ clientIp, ws })
					break
				case '/light':
					if (!this.lightWebSocketServer) {
						this.lightWebSocketServer = new LightWebSocketServer()
					}
					this.lightWebSocketServer.addWsClient({ clientIp, ws })
					break
				case '/lock':
					if (!this.lockWebSocketServer) {
						this.lockWebSocketServer = new LockWebSocketServer()
					}
					this.lockWebSocketServer.addWsClient({ clientIp, ws })
					break
				case '/rfid':
					if (!this.rfidWebSocketServer) {
						this.rfidWebSocketServer = new RfidWebSocketServer()
					}
					this.rfidWebSocketServer.addWsClient({ clientIp, ws })
					break
				case '/usb':
					if (!this.usbWebSocketServer) {
						this.usbWebSocketServer = new UsbWebSocketServer()
					}
					this.usbWebSocketServer.addWsClient({ clientIp, ws })
					break
				case '/controlLock':
					if (!this.controlLockWebSocketServer) {
						this.controlLockWebSocketServer = new ControlLockWebSocketServer()
					}
					this.controlLockWebSocketServer.addWsClient({ clientIp, ws })
					break
				case '/signFinger':
					if (!this.signFingerWebSocketServer) {
						this.signFingerWebSocketServer = new SignFingerWebSocketServer()
					}
					this.signFingerWebSocketServer.addWsClient({ clientIp, ws })
					break
				case '/contactVitalSigns':
					if (!this.contactVitalSignsWebSocketServer) {
						this.contactVitalSignsWebSocketServer = new ContactVitalSignsWebSocketServer()
					}
					this.contactVitalSignsWebSocketServer.addWsClient({ clientIp, ws })
					break
				case '/nonContactVitalSigns':
					if (!this.nonContactVitalSignsWebSocketServer) {
						this.nonContactVitalSignsWebSocketServer = new NonContactVitalSignsWebSocketServer()
					}
					this.nonContactVitalSignsWebSocketServer.addWsClient({ clientIp, ws })
					break
				default:
					break
			}
		})
	}
	close() {
		if (this.wss) {
			this.wss.close()
		}
		this.wss = null
	}
}

module.exports = WebSocketServer
