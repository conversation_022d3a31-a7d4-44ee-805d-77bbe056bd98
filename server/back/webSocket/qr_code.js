const CommonWebSocket = require('./common')
const { CODE_ENUM, ACTION_TYPE_DIC } = require('../../../hardware/enum')
const QrCode = require('../../../hardware/qr_code/serialport/index')

let webContents
let keyDownValue = ''

const qrCodeMap = {
	1: 'USB',
	2: 'SerialPort'
}

class QrCodeWebSocketServer extends CommonWebSocket {
	#qrCode = null
	#manufacturer = 1
	#isInit = false
	constructor() {
		if (QrCodeWebSocketServer.instance) {
			return QrCodeWebSocketServer.instance
		}
		super()
		QrCodeWebSocketServer.instance = this
	}
	handle(clientData) {
		const { action } = clientData.params
		const ws = this.allClients[clientData.clientIp]
		switch (action) {
			case ACTION_TYPE_DIC.INIT:
				this.#initLink(clientData)
				break
			case ACTION_TYPE_DIC.ADD_KEY_DOWN_LISTENER:
				if (!webContents) {
					return this.handleLinkErrorByAction(ws, action)
				}
				this.#addInputEventListeners(ws, action)
				break
			case ACTION_TYPE_DIC.REMOVE_KEY_DOWN_LISTENER:
				this.#removeInputEventListeners()
				this.handleSuccessByAction(ws, action)
				break
			case ACTION_TYPE_DIC.CLOSE:
				this.closeLink(clientData)
				break
			default:
				this.handleParamsErrorByAction(ws, action)
				break
		}
	}
	#initLink(clientData) {
		let { params } = clientData
		params = Object.assign(global.serverConfig.qrCodeInfo, params)
		const ws = this.allClients[clientData.clientIp]
		this.#manufacturer = params.manufacturer
		if (!Object.keys(qrCodeMap).includes(String(this.#manufacturer))) {
			return this.handleParamsErrorByAction(ws, params.action)
		}
		if (this.#isInit) {
			return this.handleSuccessByAction(ws, params.action)
		}
		if (this.#manufacturer == 1) {
			this.#initUsbLink(ws, params)
		} else if (this.#manufacturer == 2) {
			this.#initSerialPortLink(ws, params)
		}
	}
	#initSerialPortLink(ws, params) {
		if (!params.path || !params.baudRate) {
			return this.handleParamsErrorByAction(ws, params.action)
		}
		if (this.#qrCode) {
			return this.handleSuccessByAction(ws, params.action)
		}
		this.#qrCode = new QrCode()
		this.#qrCode.on('qrcode-callback', (arg) => {
			const { action, code, msg, data } = arg
			if (action == ACTION_TYPE_DIC.INIT) {
				if (code == CODE_ENUM.OK) {
					this.#isInit = true
				} else {
					this.removeListeners()
				}
			} else if (action == ACTION_TYPE_DIC.CLOSE) {
				this.removeListeners()
			}
			const openClient = this.getOpenClient()
			openClient.forEach((client) => {
				this.resultWsClient(client, { action, code, msg, data })
			})
		})
		this.#qrCode.init(params.path, params.baudRate)
	}
	#initUsbLink(ws, params) {
		this.#isInit = true
		if (!global.isCreateWindow || !webContents) {
			webContents = global.mainWin ? global.mainWin.webContents : null
		}
		this.handleSuccessByAction(ws, params.action)
	}
	closeLink(clientData) {
		const ws = this.allClients[clientData.clientIp]
		if (!ws) {
			return
		}
		if (!this.#isInit) {
			return this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
		}
		if (this.#manufacturer == 1) {
			this.#removeInputEventListeners()
			this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
		} else if (this.#manufacturer == 2) {
			this.#qrCode.close()
		}
	}
	#addInputEventListeners(ws, action) {
		this.#removeInputEventListeners()
		webContents.on('before-input-event', (event, input) => {
			if (input.type !== 'keyDown') {
				return
			}
			if (input.key === 'Enter') {
				this.handleSuccessByAction(ws, ACTION_TYPE_DIC.KEY_DOWN_VALUE, { result: keyDownValue })
				keyDownValue = ''
			} else {
				keyDownValue += input.key
			}
		})
		this.handleSuccessByAction(ws, action)
	}
	#removeInputEventListeners() {
		webContents && webContents.removeAllListeners('before-input-event')
		this.#isInit = false
	}
	removeListeners() {
		if (this.#manufacturer == 1) {
			this.#removeInputEventListeners()
		} else if (this.#manufacturer == 2) {
			this.#qrCode.removeAllListeners('qrcode-callback')
			this.#qrCode = null
		}
		this.#isInit = false
	}
}

module.exports = QrCodeWebSocketServer
