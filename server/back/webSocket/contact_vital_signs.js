const CommonWebSocket = require('./common')
const { CODE_ENUM, ACTION_TYPE_DIC } = require('../../../hardware/enum')
const SelfDevContactVitalSigns = require('../../../hardware/vital_signs/serialport/self_dev')

class ContactVitalSignsWebSocketServer extends CommonWebSocket {
	#vitalSigns = null
	constructor() {
		if (ContactVitalSignsWebSocketServer.instance) {
			return ContactVitalSignsWebSocketServer.instance
		}
		super()
		ContactVitalSignsWebSocketServer.instance = this
	}
	handle(clientData) {
		const { params, clientIp } = clientData
		const ws = this.allClients[clientIp]
		switch (params.action) {
			case ACTION_TYPE_DIC.INIT:
				this.#initLink(clientData)
				break
			case ACTION_TYPE_DIC.CLOSE:
				this.closeLink(clientData)
				break
			case ACTION_TYPE_DIC.START_COLLECTION:
			case ACTION_TYPE_DIC.END_COLLECTION:
				this.#handleVitalSignsAction(clientData)
				break
			default:
				this.handleLinkErrorByAction(ws, params.action)
				break
		}
	}
	#initLink(clientData) {
		const { clientIp } = clientData
		let { params } = clientData
		params = Object.assign(global.serverConfig.contactVitalSignsInfo, params)
		const ws = this.allClients[clientIp]
		if (this.#vitalSigns) {
			return this.handleSuccessByAction(ws, params.action)
		}
		if (params.manufacturer == 1) {
			this.#initSelfDevContactVitalSignsLink(ws, params)
		} else {
			return this.handleParamsErrorByAction(ws, params.action)
		}
	}
	#initSelfDevContactVitalSignsLink(ws, params) {
		if (!params.path) {
			return this.handleParamsErrorByAction(ws, params.action)
		}
		this.#vitalSigns = new SelfDevContactVitalSigns()
		this.#vitalSigns.on('vital-signs-callback', (arg) => {
			const { action, code, msg, data } = arg
			if (action == ACTION_TYPE_DIC.INIT) {
				code != CODE_ENUM.OK && this.removeListeners()
			} else if (action == ACTION_TYPE_DIC.CLOSE) {
				this.removeListeners()
			}
			const openClient = this.getOpenClient()
			openClient.forEach((client) => {
				this.resultWsClient(client, { action, code, msg, data })
			})
		})
		this.#vitalSigns.init(params)
	}
	#handleVitalSignsAction(clientData) {
		const { clientIp, params } = clientData
		const ws = this.allClients[clientIp]
		if (!ws) {
			return
		}
		if (!this.#vitalSigns) {
			return this.handleLinkErrorByAction(ws, params.action)
		}
		this.#vitalSigns.handle(params)
	}
	closeLink(clientData) {
		const { clientIp } = clientData
		const ws = this.allClients[clientIp]
		if (!ws) {
			return
		}
		if (!this.#vitalSigns) {
			return this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
		}
		const openClient = this.getOpenClient()
		if (openClient.length > 1) {
			return this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
		}
		this.#vitalSigns.close()
	}
	removeListeners() {
		this.#vitalSigns.removeAllListeners('vital-signs-callback')
		this.#vitalSigns = null
	}
}

module.exports = ContactVitalSignsWebSocketServer
