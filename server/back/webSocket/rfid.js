const CommonWebSocket = require('./common')
const { CODE_ENUM, ACTION_TYPE_DIC } = require('../../../hardware/enum')
const KuAnLowWsRfidServer = require('../../../hardware/rfid/websocket/gxx_k32_1')
const kuAnLowWsRfidServer = new KuAnLowWsRfidServer()
const KuAnHighWsRfidServer = require('../../../hardware/rfid/websocket/gxx_k32_2')
const kuAnHighWsRfidServer = new KuAnHighWsRfidServer()
const DiChuanLowSpRfidServer = require('../../../hardware/rfid/serialport/gxx_d99_1')
const diChuanLowSpRfidServer = new DiChuanLowSpRfidServer()
const DiChuanHighSpRfidServer = require('../../../hardware/rfid/serialport/gxx_d99_2')
const diChuanHighSpRfidServer = new DiChuanHighSpRfidServer()
const rfidMap = {
	1: kuAnLowWsRfidServer,
	2: kuAnHighWsRfidServer,
	3: diChuanLowSpRfidServer,
	4: diChuanHighSpRfidServer
}

class RfidWebSocketServer extends CommonWebSocket {
	#rfid = null
	#manufacturer = 1
	constructor() {
		if (RfidWebSocketServer.instance) {
			return RfidWebSocketServer.instance
		}
		super()
		RfidWebSocketServer.instance = this
	}
	handle(clientData) {
		const { action } = clientData.params
		const ws = this.allClients[clientData.clientIp]
		switch (action) {
			case ACTION_TYPE_DIC.INIT:
				this.#initLink(clientData)
				break
			case ACTION_TYPE_DIC.CLOSE:
				this.closeLink(clientData)
				break
			case ACTION_TYPE_DIC.CHECK_RFID:
				this.#checkRfidCode(clientData)
				break
			case ACTION_TYPE_DIC.SET_DOOR_LIGHT:
				this.#setLight(clientData)
				break
			case ACTION_TYPE_DIC.SET_ORANGE_COLOR:
				this.#setOrangeColor(clientData)
				break
			case ACTION_TYPE_DIC.SET_POLLING_CHANNELS:
				this.#setPollingChannels(clientData)
				break
			default:
				this.handleParamsErrorByAction(ws, action)
				break
		}
	}
	#initLink(clientData) {
		let { params } = clientData
		// 查询主柜配置信息
		let mainRfidInfo = {}
		for (const key in global.serverConfig.rfidInfo) {
			if (global.serverConfig.rfidInfo[key].isMain) {
				mainRfidInfo = global.serverConfig.rfidInfo[key]
			}
		}
		params = Object.assign(mainRfidInfo, params)
		const ws = this.allClients[clientData.clientIp]
		this.#manufacturer = params.manufacturer
		if (!params.path || !Object.keys(rfidMap).includes(String(this.#manufacturer))) {
			return this.handleParamsErrorByAction(ws, params.action)
		}
		if (this.#rfid) {
			return this.handleSuccessByAction(ws, params.action)
		}
		this.#rfid = rfidMap[this.#manufacturer]
		this.#rfid.on('rfid-callback', (arg) => {
			const { action, code, msg, data } = arg
			if (action == ACTION_TYPE_DIC.INIT) {
				code != CODE_ENUM.OK && this.removeListeners()
			} else if (action == ACTION_TYPE_DIC.CLOSE) {
				this.removeListeners()
			}
			const openClient = this.getOpenClient()
			openClient.forEach((client) => {
				this.resultWsClient(client, { action, code, msg, data })
			})
		})
		this.#rfid.init(params.path, params.baudRate)
	}
	#checkRfidCode(clientData) {
		const { clientIp, params } = clientData
		const ws = this.allClients[clientIp]
		if (!this.#rfid) {
			return this.handleLinkErrorByAction(ws, params.action)
		}
		if (String(params.address).length > 0 && params.door > 0) {
			this.#rfid.checkRfidCode(params)
		} else {
			this.handleParamsErrorByAction(ws, params.action)
		}
	}
	#setLight(clientData) {
		const { clientIp, params } = clientData
		const ws = this.allClients[clientIp]
		if (!this.#rfid) {
			return this.handleLinkErrorByAction(ws, params.action)
		}
		if (this.#manufacturer != 3) {
			return this.handleParamsErrorByAction(ws, params.action)
		}
		if (String(params.address).length > 0 && params.door > 0 && params.lightType) {
			this.#rfid.setLight(params)
		} else {
			this.handleParamsErrorByAction(ws, params.action)
		}
	}
	#setOrangeColor(clientData) {
		const { clientIp, params } = clientData
		const ws = this.allClients[clientIp]
		if (!this.#rfid) {
			return this.handleLinkErrorByAction(ws, params.action)
		}
		if (this.#manufacturer != 3) {
			return this.handleParamsErrorByAction(ws, params.action)
		}
		if (String(params.address).length > 0 && params.green >= 0 && params.green <= 255 && params.red >= 0 && params.red <= 255) {
			this.#rfid.setLight(params)
		} else {
			this.handleParamsErrorByAction(ws, params.action)
		}
	}
	#setPollingChannels(clientData) {
		const { clientIp, params } = clientData
		const ws = this.allClients[clientIp]
		if (!this.#rfid) {
			return this.handleLinkErrorByAction(ws, params.action)
		}
		if (this.#manufacturer != 3) {
			return this.handleParamsErrorByAction(ws, params.action)
		}
		if (String(params.address).length > 0 && params.channels) {
			this.#rfid.setPollingChannels(params)
		} else {
			this.handleParamsErrorByAction(ws, params.action)
		}
	}
	closeLink(clientData) {
		const ws = this.allClients[clientData.clientIp]
		if (this.#rfid) {
			this.#rfid.close()
		} else {
			this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
		}
	}
	removeListeners() {
		this.#rfid.removeAllListeners('rfid-callback')
		this.#rfid = null
	}
}

module.exports = RfidWebSocketServer
