const WebSocket = require('ws')
const SystemService = require('../service/system')
const { downloadFileByUrl, installPackage } = require('../tools/index')
const Logger = require('../../../hardware/log/index')
const log = new Logger('online-ws')
const onlineSocket = {
	upgradeWs: null,
	data: {
		terminalType: '', // 终端类型
		terminalVersion: '', // 当前版本
		terminalIp: '', // 本机IP地址
		mac: '' // 本机MAC地址
	},
	heartbeatTimer: null,
	clearSocketTimer: null,
	reconnectTimer: null,
	mac: '',
	terminalIp: '',
	progressTimeout: 200, // 推送下载进度间隔(ms): 默认200ms
	totalSize: 0, // 升级安装包大小
	/**
	 * 描述：创建ws
	 * serveIp: 服务IP
	 * servePort：服务端口
	 * terminalType：终端类型
	 * terminalVersion： 终端版本
	 * upgrade 升级回调
	 */
	createWs() {
		log.info('建立连接')
		const propertyData = SystemService.getIpMacInfo()
		const serverConfig = global.serverConfig
		const terminalType = this.propertyData.sysArch == 'arm64' ? 'chc-cupboard-arm' : this.propertyData.sysArch == 'x64' ? 'chc-cupboard-uos' : 'chc-cupboard-win'
		const params = {
			serveIp: serverConfig.businessInfo.serviceIp,
			servePort: serverConfig.businessInfo.socketPort,
			terminalType,
			terminalVersion: serverConfig.version
		}
		if (!params.serveIp || !params.servePort || !params.terminalType || !params.terminalVersion || serverConfig.businessInfo.communicationMode == 2) {
			return
		}
		try {
			this.data.mac = this.mac || propertyData.mac
			this.data.terminalIp = this.terminalIp || propertyData.address
			if (!this.terminalIp) {
				this.terminalIp = propertyData.address
			}
			this.terminalIp = this.data.terminalIp
			this.data.terminalType = params.terminalType
			this.data.terminalVersion = params.terminalVersion
			const wsUrl = `ws://${params.serveIp}:${params.servePort}/chc-service/mpConfigSocket/terminalStatus?mac=${this.data.mac}&terminalType=${this.data.terminalType}`
			log.info('连接地址', wsUrl)
			this.upgradeWs = new WebSocket(wsUrl)
			this.upgradeWs.onopen = () => {
				// 上报状态
				this.upgradeWs.send(
					JSON.stringify({
						command: 'status',
						type: 'request',
						data: this.data
					})
				)
				clearInterval(this.heartbeatTimer)
				this.heartbeatTimer = null
				// 维持心跳
				this.heartbeatTimer = setInterval(() => {
					if (this.upgradeWs && this.upgradeWs.readyState === 1) {
						this.upgradeWs.send(
							JSON.stringify({
								command: 'heartbeat',
								type: 'request',
								data: this.data
							})
						)
					} else {
						log.info('心跳检测失败，关闭定时器', '')
						clearInterval(this.heartbeatTimer)
						this.heartbeatTimer = null
					}
				}, 2 * 59 * 1000)
			}
			this.upgradeWs.onmessage = (response) => {
				log.info('接收到信息')
				const data = response.data && JSON.parse(response.data)
				if (data.code === 0) {
					const command = data.command
					switch (command) {
						case 'upgrade':
							log.info('升级', data.data)
							// 强制升级
							/**
							 * data.data.downloadUrl 下载地址
							 * data.data.version 版本
							 */
							const downloadInfo = data.data
							const downloadUrl = downloadInfo.fileUrl || downloadInfo.url || downloadInfo.downloadUrl
							downloadFileByUrl(
								downloadUrl,
								(progress) => {
									this.postProgress(progress, downloadInfo)
									if (progress == 100) {
										const fileName = downloadUrl.substring(downloadUrl.lastIndexOf('/') + 1)
										installPackage({ fileName })
									}
								},
								(error) => {
									log.info(`下载升级包失败，错误信息：${error}`)
								}
							)
							break
						case 'escalation':
							// 被动上报状态
							this.upgradeWs.send(
								JSON.stringify({
									command: 'status',
									type: 'request',
									data: this.data
								})
							)
							break
						default:
							break
					}
				} else {
					log.info('数据异常:', data.msg, data.code)
				}
			}
			this.upgradeWs.onclose = () => {
				log.info('onclose')
				this.reconnectWs(params)
			}
			this.upgradeWs.onerror = (error) => {
				log.info('ws连接失败', error)
				this.reconnectWs(params)
			}
		} catch (err) {
			log.info(err, '连接异常')
		}
	},
	/**
	 * 描述：断开重连ws
	 */
	reconnectWs(params) {
		log.info('执行ws重连')
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer)
			this.reconnectTimer = null
		}
		this.reconnectTimer = setTimeout(() => {
			this.upgradeWs = null
			try {
				this.createWs(params)
			} catch (error) {
				log.info('重连ws失败', error)
			}
		}, 60 * 1000)
	},
	/**
	 * 描述：关闭ws
	 */
	closeWs() {
		this.upgradeWs && this.upgradeWs.close()
		this.upgradeWs = null
		this.heartbeatTimer && clearInterval(this.heartbeatTimer)
		this.heartbeatTimer = null
	},
	/**
	 * 描述：清除Socket
	 */
	clearSocket(params) {
		if (this.clearSocketTimer) {
			clearInterval(this.clearSocketTimer)
			this.clearSocketTimer = null
		}
		this.clearSocketTimer = setInterval(() => {
			this.closeWs()
			this.createWs(params)
		}, 24 * 60 * 60 * 1000)
	},

	// 上报进度
	postProgress(progress, oldDownloadInfo) {
		const progressNum = parseInt(progress)
		const downloadInfo = {
			...oldDownloadInfo,
			progress: progressNum
		}
		this.upgradeWs.send('download-progress', downloadInfo)
	}
}

module.exports = onlineSocket
