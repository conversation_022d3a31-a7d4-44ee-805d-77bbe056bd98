const CommonWebSocket = require('./common')
const { CODE_ENUM, DATA_CODE_DIC, DATA_CODE_ENUM, ACTION_TYPE_DIC } = require('../../../hardware/enum')
const Printer = require('../../../hardware/printer/index')
const { createDir } = require('../tools/index')
const { ipcMain } = require('electron')
const fs = require('fs')
const path = require('path')
const printer = new Printer()
const IdCard = require('../../../hardware/id_card/http/index')
const Wristband = require('../../../hardware/wristband/http/index')
let IDCard
let wristband
let webContents
let keyDownValue = ''

class UsbWebSocketServer extends CommonWebSocket {
	#isInit = false
	constructor() {
		if (UsbWebSocketServer.instance) {
			return UsbWebSocketServer.instance
		}
		super()
		UsbWebSocketServer.instance = this
	}
	handle(clientData) {
		const { action } = clientData.params
		const ws = this.allClients[clientData.clientIp]
		if (action != ACTION_TYPE_DIC.INIT && !this.#isInit) {
			return this.handleLinkErrorByAction(ws, action)
		}
		switch (action) {
			// 初始化
			case ACTION_TYPE_DIC.INIT:
				this.#initLink(clientData)
				break
			// 摄像头模块
			case ACTION_TYPE_DIC.GET_VIDEO_DEVICE:
			case ACTION_TYPE_DIC.OPEN_CAMERA:
			case ACTION_TYPE_DIC.ROTATE_CAMERA:
			case ACTION_TYPE_DIC.CAPTURE_IMAGE:
			case ACTION_TYPE_DIC.CLOSE_CAMERA:
				if (!webContents) {
					return this.handleSendMessageErrorByAction(ws, action)
				}
				webContents.send('usb-device', clientData.params)
				break
			// 多频读卡器模块
			case ACTION_TYPE_DIC.ADD_KEY_DOWN_LISTENER:
				if (!webContents) {
					return this.handleSendMessageErrorByAction(ws, action)
				}
				this.#addInputEventListeners(ws, action)
				break
			case ACTION_TYPE_DIC.REMOVE_KEY_DOWN_LISTENER:
				this.#removeInputEventListeners()
				this.handleSuccessByAction(ws, action)
				break
			// 打印机模块
			case ACTION_TYPE_DIC.GET_PRINTER_LIST:
				if (!webContents) {
					return this.handleSendMessageErrorByAction(ws, action)
				}
				this.#getPrinters(ws, action)
				break
			case ACTION_TYPE_DIC.PRINT:
				this.#printFile(clientData)
				break
			// 身份证读卡器模块
			case ACTION_TYPE_DIC.READ_ID_CARD:
				IDCard.removeAllListeners('idCard-callback')
				IDCard.on('idCard-callback', (e) => {
					this.resultWsClient(ws, e)
				})
				IDCard.getIdCard()
				break
			// 手环读卡器模块
			case ACTION_TYPE_DIC.READ_WRISTBAND:
				wristband.removeAllListeners('wristband-callback')
				wristband.on('wristband-callback', (e) => {
					this.resultWsClient(ws, e)
				})
				wristband.findM1Card()
				break
			// 关闭
			case ACTION_TYPE_DIC.CLOSE:
				this.closeLink(clientData)
				break
			default:
				this.handleParamsErrorByAction(ws, action)
				break
		}
	}
	#initLink(clientData) {
		const { clientIp, params } = clientData
		const ws = this.allClients[clientIp]
		this.allClients[clientIp] = ws
		if (this.#isInit) {
			return this.handleSuccessByAction(ws, params.action)
		}
		this.#isInit = true
		if (global.isCreateWindow) {
			this.#addIpcUsbDeviceResultListeners()
			if (!webContents) {
				webContents = global.mainWin.webContents
			}
		}
		if (!IDCard) {
			IDCard = new IdCard()
		}
		if (!wristband) {
			wristband = new Wristband()
		}
		this.handleSuccessByAction(ws, params.action)
	}
	#getPrinters(ws, action) {
		const printerList = webContents.getPrinters()
		this.handleSuccessByAction(ws, action, { result: printerList })
	}
	/**
	 * 打印文件
	 * @param {Object} clientData
	 * @param {Object} params
	 * @param {String} params.type // image: 图片 text: 文本 默认文本
	 * @param {String} params.printName // 打印机名称
	 * @param {Buffer} params.buffer
	 * @param {String} params.action
	 * @param {Boolean} params.isColorPrint
	 * @returns
	 */
	#printFile(clientData) {
		const { clientIp, params } = clientData
		const ws = this.allClients[clientIp]
		if (!params.printName || !params.buffer) {
			return this.handleParamsErrorByAction(ws, params.action)
		}
		params.type = params.type || 'text'
		let ext = ''
		let buffer = ''
		if (params.type !== 'text') {
			const base64PrefixRegex = /^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]*);base64,/
			const match = params.buffer.match(base64PrefixRegex)
			try {
				if (match) {
					const arr = match[1].split('/')
					if (arr[0] !== params.type) {
						return this.handleParamsErrorByAction(ws, params.action)
					}
					ext = arr[1]
					buffer = Buffer.from(params.buffer.split(',')[1], 'base64')
				}
			} catch (error) {
				ext = 'txt'
				buffer = Buffer.from(params.buffer)
			}
		} else {
			ext = 'txt'
			buffer = Buffer.from(params.buffer)
		}
		const fileName = `print-file.${ext}` // 保存的文件名
		createDir(global.packagePath)
		const fileUrl = path.join(global.packagePath, fileName)
		fs.writeFile(fileUrl, buffer, async (err) => {
			if (err) {
				this.logger.error('打印文件保存失败！', err)
				this.resultWsClient(ws, {
					action: params.action,
					code: CODE_ENUM.SEND_MESSAGE_ERROR,
					msg: '打印失败！',
					data: {
						flag: DATA_CODE_ENUM.ACTION_ERROR,
						message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
					}
				})
			} else {
				const bool = await printer.print({ ...params, fileUrl })
				// 删除文件
				fs.unlink(fileUrl, (error) => {
					if (error) {
						this.logger.error(`删除文件失败，${error}`)
					}
				})
				this.resultWsClient(ws, {
					action: params.action,
					code: bool ? CODE_ENUM.OK : CODE_ENUM.SEND_MESSAGE_ERROR,
					msg: `打印${bool ? '成功' : '失败！'}`,
					data: {
						flag: bool ? DATA_CODE_ENUM.ACTION_SUCCESS : DATA_CODE_ENUM.ACTION_ERROR,
						message: bool ? DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_SUCCESS] : DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR]
					}
				})
			}
		})
	}
	#resultAllClient(arg) {
		const { type, success, data } = arg
		const openClient = this.getOpenClient()
		openClient.forEach((client) => {
			if (success) {
				this.handleSuccessByAction(client, type, { result: data })
			} else {
				this.handleSendMessageErrorByAction(client, type, { result: data })
			}
		})
	}
	closeLink(clientData) {
		const ws = this.allClients[clientData.clientIp]
		const openClient = this.getOpenClient()
		if (openClient.length == 0) {
			this.removeListeners()
			wristband && wristband.close()
			IDCard && IDCard.close()
		}
		this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
	}
	#addInputEventListeners(ws, action) {
		this.#removeInputEventListeners()
		webContents.on('before-input-event', (event, input) => {
			if (input.type !== 'keyDown') {
				return
			}
			if (input.key === 'Enter') {
				this.handleSuccessByAction(ws, ACTION_TYPE_DIC.KEY_DOWN_VALUE, { result: keyDownValue })
				keyDownValue = ''
			} else {
				keyDownValue += input.key
			}
		})
		this.handleSuccessByAction(ws, action)
	}
	#removeInputEventListeners() {
		webContents && webContents.removeAllListeners('before-input-event')
	}
	#addIpcUsbDeviceResultListeners() {
		this.#removeIpcUsbDeviceResultListeners()
		ipcMain.on('usb-device-result', (event, arg) => {
			this.#resultAllClient(arg)
		})
	}
	#removeIpcUsbDeviceResultListeners() {
		ipcMain.removeAllListeners('usb-device-result')
	}
	removeListeners() {
		this.#removeIpcUsbDeviceResultListeners()
		this.#removeInputEventListeners()
		this.#isInit = false
	}
}

module.exports = UsbWebSocketServer
