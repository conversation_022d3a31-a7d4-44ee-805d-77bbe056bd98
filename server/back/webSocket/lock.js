const CommonWebSocket = require('./common')
const onlyLock = require('../../../hardware/lock/only_open')
const { CODE_ENUM, ACTION_TYPE_DIC } = require('../../../hardware/enum')
class LockWebSocketServer extends CommonWebSocket {
	#lockApi = null
	constructor() {
		if (LockWebSocketServer.instance) {
			return LockWebSocketServer.instance
		}
		super()
		LockWebSocketServer.instance = this
	}
	handle(clientData) {
		const { action } = clientData.params
		const ws = this.allClients[clientData.clientIp]
		switch (action) {
			case ACTION_TYPE_DIC.INIT:
				this.#initLink(clientData)
				break
			case ACTION_TYPE_DIC.OPEN_LOCK:
				this.#openLock(clientData)
				break
			case ACTION_TYPE_DIC.CHECK_LOCK:
				this.#checkLockStatus(clientData)
				break
			case ACTION_TYPE_DIC.CLOSE:
				this.closeLink(clientData)
				break
			default:
				this.handleParamsErrorByAction(ws, action)
				break
		}
	}
	#initLink(clientData) {
		const { params, clientIp } = clientData
		const ws = this.allClients[clientIp]
		if (this.#lockApi) {
			return this.handleSuccessByAction(ws, params.action)
		}
		this.#lockApi = new onlyLock()
		this.#lockApi.on('only-lock-callback', (arg) => {
			const { action, code, msg, data } = arg
			if (action == ACTION_TYPE_DIC.INIT) {
				code != CODE_ENUM.OK && this.removeListeners()
			} else if (action == ACTION_TYPE_DIC.CLOSE) {
				this.removeListeners()
			}
			const openClient = this.getOpenClient()
			openClient.forEach((client) => {
				this.resultWsClient(client, { action, code, msg, data })
			})
		})
		this.#lockApi.initLink(params)
	}
	#openLock(clientData) {
		const { params, clientIp } = clientData
		const ws = this.allClients[clientIp]
		if (!this.#lockApi) {
			return this.handleLinkErrorByAction(ws, params.action)
		}
		this.#lockApi.openLock(params)
	}
	#checkLockStatus(clientData) {
		const { params, clientIp } = clientData
		const ws = this.allClients[clientIp]
		if (!this.#lockApi) {
			return this.handleLinkErrorByAction(ws, params.action)
		}
		this.#lockApi.checkLockStatus(params)
	}
	closeLink(clientData) {
		const ws = this.allClients[clientData.clientIp]
		if (this.#lockApi) {
			this.#lockApi.closeLink()
		} else {
			this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
		}
	}
	removeListeners() {
		this.#lockApi.removeAllListeners('only-lock-callback')
		this.#lockApi = null
	}
}

module.exports = LockWebSocketServer
