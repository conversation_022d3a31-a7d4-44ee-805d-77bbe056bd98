const CommonWebSocket = require('./common')
const { CODE_ENUM, ACTION_TYPE_DIC } = require('../../../hardware/enum')
const KuanSpLightServer = require('../../../hardware/light/serialport/gxx_k32_1')
const kuanSpLightServer = new KuanSpLightServer()
const KuanWsLightServer = require('../../../hardware/light/websocket/gxx_k32_1')
const kuanWsLightServer = new KuanWsLightServer()
const JiJiaSpLightServer = require('../../../hardware/light/serialport/gxx_j88_1')
const jiJiaSpLightServer = new JiJiaSpLightServer()
const DiChuanSpLightServer = require('../../../hardware/light/serialport/gxx_d99_1')
const diChuanSpLightServer = new DiChuanSpLightServer()
const lightMap = {
	1: kuanSpLightServer,
	2: ji<PERSON>iaSpLightServer,
	3: diChuanSpLightServer,
	4: kuanWsLightServer
}

class LightWebSocketServer extends CommonWebSocket {
	#light = null
	#manufacturer = 1
	constructor() {
		if (LightWebSocketServer.instance) {
			return LightWebSocketServer.instance
		}
		super()
		LightWebSocketServer.instance = this
	}
	handle(clientData) {
		const { action } = clientData.params
		const ws = this.allClients[clientData.clientIp]
		switch (action) {
			case ACTION_TYPE_DIC.INIT:
				this.#initLink(clientData)
				break
			case ACTION_TYPE_DIC.OPEN_LIGHT:
			case ACTION_TYPE_DIC.CLOSE_LIGHT:
				this.#handleLight(clientData)
				break
			case ACTION_TYPE_DIC.CLOSE:
				this.closeLink(clientData)
				break
			default:
				this.handleParamsErrorByAction(ws, action)
				break
		}
	}
	#initLink(clientData) {
		let { params } = clientData
		const { clientIp } = clientData
		params = Object.assign(global.serverConfig.supplementLightInfo, params)
		const ws = this.allClients[clientIp]
		this.#manufacturer = params.manufacturer
		if (!params.path || !Object.keys(lightMap).includes(String(this.#manufacturer))) {
			return this.handleParamsErrorByAction(ws, params.action)
		}
		this.allClients[clientIp] = ws
		if (this.#light) {
			return this.handleSuccessByAction(ws, params.action)
		}
		this.#light = lightMap[this.#manufacturer]
		this.#light.on('light-callback', (arg) => {
			const { action, code, msg, data } = arg
			if (action == ACTION_TYPE_DIC.INIT) {
				code != CODE_ENUM.OK && this.removeListeners()
			} else if (action == ACTION_TYPE_DIC.CLOSE) {
				this.removeListeners()
			}
			const openClient = this.getOpenClient()
			openClient.forEach((client) => {
				this.resultWsClient(client, { action, code, msg, data })
			})
		})
		this.#light.init(params.path, params.baudRate)
	}
	#handleLight(clientData) {
		const { clientIp } = clientData
		let { params } = clientData
		params = Object.assign(global.serverConfig.supplementLightInfo, params)
		const ws = this.allClients[clientIp]
		if (!this.#light) {
			return this.handleLinkErrorByAction(ws, params.action)
		}
		if (this.#manufacturer == 3 && !(String(params.address).length > 0 && params.door > 0)) {
			return this.handleParamsErrorByAction(ws, params.action)
		} else {
			this.#light.handle(params)
		}
	}
	closeLink(clientData) {
		const ws = this.allClients[clientData.clientIp]
		if (this.#light) {
			this.#light.close()
		} else {
			this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
		}
	}
	removeListeners() {
		this.#light.removeAllListeners('light-callback')
		this.#light = null
	}
}

module.exports = LightWebSocketServer
