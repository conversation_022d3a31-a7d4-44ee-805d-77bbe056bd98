const CommonWebSocket = require('./common')
const { CODE_ENUM, ACTION_TYPE_DIC } = require('../../../hardware/enum')
const SignFinger = require('../../../hardware/sign_finger/websocket')
const TYPE_1 = 1
const signFingerMap = {
	[TYPE_1]: 'websocket'
}

class SignFingerWebSocketServer extends CommonWebSocket {
	#signFinger = null
	#manufacturer = TYPE_1
	#isInit = false
	constructor() {
		if (SignFingerWebSocketServer.instance) {
			return SignFingerWebSocketServer.instance
		}
		super()
		SignFingerWebSocketServer.instance = this
	}
	handle(clientData) {
		const params = clientData.params
		const ws = this.allClients[clientData.clientIp]
		switch (params.action) {
			case ACTION_TYPE_DIC.INIT:
				this.#initLink(clientData)
				break
			case ACTION_TYPE_DIC.CLOSE:
				this.closeLink(clientData)
				break
			default:
				if (this.#isInit) {
					this.#signFinger.handle(params)
				} else {
					this.handleLinkErrorByAction(ws, params.action)
				}
				break
		}
	}
	#initLink(clientData) {
		let { params } = clientData
		params = Object.assign(global.serverConfig.signFingerInfo, params)
		const ws = this.allClients[clientData.clientIp]
		this.#manufacturer = params.manufacturer
		if (!Object.keys(signFingerMap).includes(String(this.#manufacturer))) {
			return this.handleParamsErrorByAction(ws, params.action)
		}
		if (this.#isInit) {
			return this.handleSuccessByAction(ws, params.action)
		}
		if (this.#manufacturer == TYPE_1) {
			this.#initWebSocketLink(ws, params)
		}
	}
	#initWebSocketLink(ws, params) {
		if (this.#signFinger) {
			return this.handleSuccessByAction(ws, params.action)
		}
		this.#signFinger = new SignFinger()
		this.#signFinger.on('sign-finger-callback', (arg) => {
			const { code, msg, data, action } = arg
			switch (action) {
				case ACTION_TYPE_DIC.INIT:
					if (code !== CODE_ENUM.OK) {
						this.removeListeners()
					} else {
						this.#isInit = true
					}
					break
				case ACTION_TYPE_DIC.CLOSE:
					this.removeListeners()
					this.#isInit = false
					break
				case ACTION_TYPE_DIC.ERROR:
					!this.#isInit && this.removeListeners()
					break
				default:
					break
			}
			const openClient = this.getOpenClient()
			openClient.forEach((client) => {
				this.resultWsClient(client, { action, code, msg, data })
			})
		})
		this.#signFinger.init(params)
	}
	closeLink(clientData) {
		const ws = this.allClients[clientData.clientIp]
		if (!ws) {
			return
		}
		if (!this.#isInit) {
			return this.handleSuccessByAction(ws, ACTION_TYPE_DIC.CLOSE)
		}
		if (this.#manufacturer == TYPE_1) {
			this.#signFinger.close()
		}
	}
	removeListeners() {
		if (this.#manufacturer == 1) {
			this.#signFinger.removeAllListeners('sign-finger-callback')
			this.#signFinger = null
		}
		this.#isInit = false
	}
}

module.exports = SignFingerWebSocketServer
