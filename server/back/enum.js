const CODE_ENUM = {
	OK: 200,
	PARAMS_ERROR: 400,
	UNAUTHORIZED: 401,
	FORBIDDEN: 403,
	NOT_FOUND: 404,
	SERVER_ERROR: 500,
	SERVER_UNAVAILABLE: 501,
	GATEWAY_ERROR: 502,
	SERVICE_UNAVAILABLE: 503,
	GATEWAY_TIMEOUT: 504
}

const CODE_DIC = {
	[CODE_ENUM.OK]: '成功',
	[CODE_ENUM.PARAMS_ERROR]: '请求参数错误',
	[CODE_ENUM.UNAUTHORIZED]: '未授权',
	[CODE_ENUM.FORBIDDEN]: '禁止访问',
	[CODE_ENUM.NOT_FOUND]: '资源不存在',
	[CODE_ENUM.SERVER_ERROR]: '服务器内部错误',
	[CODE_ENUM.SERVER_UNAVAILABLE]: '服务不可用',
	[CODE_ENUM.GATEWAY_ERROR]: '网关错误',
	[CODE_ENUM.SERVICE_UNAVAILABLE]: '服务不可用',
	[CODE_ENUM.GATEWAY_TIMEOUT]: '网关超时'
}

const SEX_ENUM = {
	undefined: '0',
	null: '0',
	NONE: '0',
	MALE: '1',
	FEMALE: '2'
}

const SEX_DIC = {
	[SEX_ENUM.NONE]: '未知',
	[SEX_ENUM.MALE]: '男',
	[SEX_ENUM.FEMALE]: '女'
}

const ROLE_ENUM = {
	undefined: 0,
	null: 0,
	NONE: 0,
	SYSTEM_ADMIN_ROLE: 1,
	ADMIN_ROLE: 2,
	USER_ROLE: 3
}

const ROLE_DIC = {
	[ROLE_ENUM.NONE]: '未知',
	[ROLE_ENUM.SYSTEM_ADMIN_ROLE]: '超级管理员',
	[ROLE_ENUM.ADMIN_ROLE]: '管理员',
	[ROLE_ENUM.USER_ROLE]: '用户'
}

// 授权标识
const AUTH_OBJ_DIC = {
	APP: 'app', // 内部调用
	THIRD: 'third' // 第三方调用
}

module.exports = {
	CODE_ENUM,
	CODE_DIC,
	SEX_ENUM,
	SEX_DIC,
	ROLE_ENUM,
	ROLE_DIC,
	AUTH_OBJ_DIC
}
