const ItemNumber = require('../entity/item_number')
const ItemNumberMapper = require('../mapper/item_number')
const { CODE_ENUM } = require('../enum')

const itemNumberService = {
	getAllItemNumber: () => {
		return new Promise(async (resolve) => {
			const { success, data } = await ItemNumberMapper.findAllItemNumber()
			resolve({ code: success ? CODE_ENUM.OK : CODE_ENUM.SERVER_ERROR, msg: `查询${success ? '成功' : '失败'}`, data })
		})
	},
	checkItemNumber: (params) => {
		return new Promise(async (resolve) => {
			const itemNumber = new ItemNumber(params)
			if (!itemNumber.itemNumber) {
				resolve({ code: CODE_ENUM.PARAMS_ERROR, msg: '料号不能为空！' })
			} else {
				const { success, data } = await ItemNumberMapper.findInfoByItemNumber(itemNumber.itemNumber)
				resolve({ code: success ? CODE_ENUM.OK : CODE_ENUM.SERVER_ERROR, msg: `查询${success ? '成功' : '失败'}`, data })
			}
		})
	}
}

module.exports = itemNumberService
