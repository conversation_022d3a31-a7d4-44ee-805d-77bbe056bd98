const { exec } = require('child_process')
const SerialPort = require('serialport')
const { getSerialPortList } = require('../../../hardware/tools')
const Osk = require('../../../hardware/osk/index')
const ControlLock = require('../../../hardware/control_lock/serialport/index')
const controlLock = new ControlLock()
const QrCode = require('../../../hardware/qr_code/serialport/index')
const qrCode = new QrCode()
const Logger = require('../../../hardware/log/index')
const log = new Logger('hardware-service')
const { CODE_ENUM } = require('../enum')
const { ACTION_TYPE_DIC, SYSTEM_PLATFORM_STR_DIC } = require('../../../hardware/enum')
const platform = process.platform
let osk

function linuxOpenSoftKeyboard() {
	return new Promise((resolve) => {
		let tag = 1
		exec('ps aux', (err, stdout) => {
			if (err) {
				resolve({ code: CODE_ENUM.SERVER_ERROR, msg: '开启软键盘失败!' })
				return
			}
			stdout.split('\n').filter((line) => {
				const processMessage = line.trim().split(/\s+/)
				// processMessage[0]进程名称 ， processMessage[1]进程id
				const processName = processMessage[0]
				if (processName == 'onboard') {
					tag = 0
				}
			})
			if (tag == 1) {
				exec('onboard', (err) => {
					resolve({ code: !err ? CODE_ENUM.OK : CODE_ENUM.SERVER_ERROR, msg: `开启软键盘${!err ? '成功' : '失败'}` })
				})
			}
		})
	})
}

function winOpenSoftKeyboard() {
	if (!osk) {
		osk = new Osk()
		osk.init()
	}
	osk.open()
	return Promise.resolve({ code: CODE_ENUM.OK, msg: '开启软键盘成功' })
}

const HardwareService = {
	/**
	 * 打开系统软键盘
	 */
	openSoftKeyboard: async () => {
		if (platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
			return winOpenSoftKeyboard()
		} else {
			return await linuxOpenSoftKeyboard()
		}
	},

	/**
	 * 关闭系统软键盘
	 */
	closeSoftKeyboard: () => {
		return new Promise((resolve) => {
			exec(platform == SYSTEM_PLATFORM_STR_DIC.LINUX ? 'killall -9 onboard' : 'taskkill /f /im osk.exe', (err) => {
				resolve({ code: !err ? CODE_ENUM.OK : CODE_ENUM.SERVER_ERROR, msg: `关闭软键盘${!err ? '成功' : '失败'}` })
			})
		})
	},

	getSerialPortList: () => {
		return new Promise(async (resolve) => {
			const serialPortList = await getSerialPortList()
			resolve({ code: CODE_ENUM.OK, msg: '查询成功', data: serialPortList })
		})
	},

	/**
	 * 批量测试串口
	 * @param {Array<String>} pathList com List
	 * @param {Array<Number>} baudRateList 波特率 List
	 * @returns {Promise}
	 */
	batchTestSerialPort: (list) => {
		return new Promise((resolve) => {
			const result = []
			if (!list || !list.length) {
				resolve({ code: CODE_ENUM.SERVER_ERROR, msg: '参数错误' })
				return
			}
			let currentIndex = 0
			const testSerialPort = () => {
				const item = list[currentIndex]
				let port = new SerialPort(item.path, { baudRate: parseInt(item.baudRate) }, (err) => {
					result.push({ path: item.path, baudRate: item.baudRate, msg: `连接${err ? '失败' : '成功'}` })
					currentIndex++
					if (err) {
						log.info(`串口${item.path}连接失败:`, JSON.stringify(err))
					}
					port.close(() => {
						port = null
						if (currentIndex == list.length) {
							resolve({ code: CODE_ENUM.OK, msg: '测试串口成功', data: result })
						} else {
							testSerialPort()
						}
					})
				})
			}
			testSerialPort()
		})
	},

	/**
	 * 打开电控锁
	 * @param {String} com com
	 * @param {Number} baudRate baudRate
	 * @returns {Promise}
	 */
	openControlLock: (com, baudRate) => {
		return new Promise((resolve) => {
			controlLock.on('control-lock-callback', (arg) => {
				const { action, code, msg } = arg
				switch (action) {
					case ACTION_TYPE_DIC.INIT:
						if (code == 0) {
							controlLock.handle(ACTION_TYPE_DIC.OPEN_LOCK)
						} else {
							resolve({ code, msg })
						}
						break
					case ACTION_TYPE_DIC.OPEN_LOCK:
						controlLock.close()
						resolve({ code, msg })
						break
					default:
						resolve({ code, msg })
						break
				}
			})
			controlLock.init(com, baudRate)
		})
	},

	/**
	 * 初始化二维码串口连接
	 * @param {String} com com
	 * @param {Number} baudRate baudRate
	 * @returns {Promise}
	 */
	initQrCode(com, baudRate) {
		return new Promise((resolve) => {
			if (!com || !baudRate) {
				resolve({ code: CODE_ENUM.SERVER_ERROR, msg: '参数错误' })
				return
			}
			qrCode.init(com, baudRate)
			qrCode.on('qrcode-callback', (arg) => {
				const { action, code, msg } = arg
				if (action == ACTION_TYPE_DIC.INIT) {
					resolve({ code, msg })
				}
			})
		})
	},

	/**
	 * 查询二维码串口数据
	 * @returns {Promise}
	 */
	receiveQrCodeMessage() {
		return new Promise((resolve) => {
			qrCode.on('qrcode-callback', (arg) => {
				const { action, code, msg, data } = arg
				if (action == ACTION_TYPE_DIC.MESSAGE) {
					resolve({ code, msg, data })
				}
			})
		})
	},

	/**
	 * 关闭二维码串口连接
	 * @returns {Promise}
	 */
	closeQrCode() {
		return new Promise((resolve) => {
			qrCode.close()
			qrCode.on('qrcode-callback', (arg) => {
				const { action, code, msg } = arg
				if (action == ACTION_TYPE_DIC.CLOSE) {
					resolve({ code, msg })
				}
			})
		})
	}
}

module.exports = HardwareService
