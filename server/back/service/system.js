const { app, screen } = require('electron')
const { exec } = require('child_process')
const si = require('systeminformation')
const os = require('os')
const dns = require('dns')
const { actualVersion } = require('../../../package.json')
const Logger = require('../../../hardware/log/index')
const log = new Logger('system-service')
const { CODE_DIC, CODE_ENUM } = require('../enum')
const { SYSTEM_PLATFORM_STR_DIC, SYSTEM_ARCH_STR_DIC } = require('../../../hardware/enum')
const zeroRegex = /(?:[0]{1,2}[:-]){5}[0]{1,2}/
const arch = process.arch
const platform = os.platform()
const systemProperty = {}

function getDnsInfo() {
	return dns.getServers()
}
async function defaultHandler(part) {
	const networkInfo = { ip: part.address || '', mac: part.mac || '', netmask: part.netmask || '', gateway: '', dns: [] }
	if (networkInfo.ip) {
		networkInfo.dns = getDnsInfo()
	}
	networkInfo.gateway = await getDefaultGateway()
	return Promise.resolve(networkInfo)
}

/**
 * 获取ip、mac、netmask地址
 */
function getIpMacInfo() {
	return getNetworkInfo((part) => {
		return part || {}
	})
}

function getNetworkInfo(handler = defaultHandler) {
	const list = os.networkInterfaces()
	const partList = []
	for (const [_, parts] of Object.entries(list)) {
		if (!parts) continue
		for (const part of parts) {
			if (part.family == 'IPv4') {
				partList.push(part)
			}
		}
	}
	if (!partList.length) return handler({})
	for (let i = 0; i < partList.length; i++) {
		const part = partList[i]
		if (!part.internal && zeroRegex.test(part.mac) === false) {
			return handler(part)
		}
	}
	const somePart = partList.filter((item) => item.internal)
	return handler(somePart[0] || {})
}

/**
 * 子网掩码转换为对应的位数
 * @param {string} netmask 子网掩码
 * @returns {number} 返回转换后的位数
 */
function netmaskToNumber(netmask) {
	const binaryMask = netmask
		.split('.')
		.map((byte) => {
			return parseInt(byte, 10).toString(2).padStart(8, '0')
		})
		.join('')

	return binaryMask.replace(/0/g, '').length
}

function getDefaultGateway() {
	return new Promise((resolve) => {
		let command
		if (platform == SYSTEM_PLATFORM_STR_DIC.LINUX) {
			command = "ip route show | grep default | awk '{print $3}'"
		} else if (platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
			command = 'route print | find "0.0.0.0"'
		} else {
			log.error('Unsupported platform.')
			return resolve('')
		}
		exec(command, (error, stdout) => {
			if (error) {
				console.error(`exec error: ${error}`)
				return resolve('')
			}
			const str = stdout.trim()
			if (platform == SYSTEM_PLATFORM_STR_DIC.WIN) {
				resolve(parseStr(str))
			} else {
				resolve(str)
			}
		})
	})
}

function parseStr(str) {
	const items = str.trim().split(/\s+/)
	let result = ''
	for (let i = 2; i < items.length; i = i + 5) {
		if (!['On-link'].includes(items[i])) {
			result = items[i]
			break
		}
	}
	return result
}

function handleLocalNetworkByArm64(ip = '', dns = '', gateway = '', netmask = 24) {
	return new Promise((resolve) => {
		const ethX = 'con-eth0'
		if (typeof netmask == 'string') {
			netmask = netmaskToNumber(netmask)
		}
		if (typeof netmask != 'number') {
			resolve({ code: CODE_ENUM.PARAMS_ERROR, msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR] })
			return
		}
		netmask = parseInt(netmask, 10)
		if (!(netmask >= 1 && netmask <= 32)) {
			resolve({ code: CODE_ENUM.PARAMS_ERROR, msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR] })
			return
		}
		const cmd = `sudo nmcli connection modify ${ethX} ipv4.addresses ${ip}/${netmask} ipv4.dns ${dns} ipv4.gateway ${gateway} ipv4.method manual && sudo nmcli connection reload && sudo nmcli networking off && sleep 5 && sudo nmcli networking on`
		log.info('设置本机网络命令行', cmd)
		exec(cmd, (err) => {
			if (err) {
				log.error(`handleLocalNetworkByArm64 err: ${err}`)
				resolve({ code: CODE_ENUM.SERVER_ERROR, msg: '失败' })
			} else {
				resolve({ code: CODE_ENUM.OK, msg: '成功' })
			}
		})
	})
}

async function handleLocalNetwork(args) {
	if (!args.ip || !args.dns || !args.gateway || !args.netmask) {
		const logInfo = `参数不完整，请检查参数，当前参数：${JSON.stringify(args)}`
		log.info(logInfo)
		return Promise.resolve({ code: CODE_ENUM.PARAMS_ERROR, msg: logInfo })
	}
	if (arch != SYSTEM_ARCH_STR_DIC.ARM64) {
		const logInfo = `网络信息只支持arm64架构，当前arch: ${arch}`
		log.info(logInfo)
		return Promise.resolve({ code: CODE_ENUM.SERVER_ERROR, msg: logInfo })
	}
	return await handleLocalNetworkByArm64(args.ip, args.dns, args.gateway, args.netmask)
}

function handlePanelByArm64(type) {
	return new Promise((resolve) => {
		log.info(`开始执行 handle-panel: ${type}_panel`)
		exec(`bash -c /root/scripts/${type}_panel.sh`, { shell: '/bin/bash' }, (error) => {
			if (error) {
				log.info(`handle-panel: ${type}_panel 执行失败,error: ${error}`)
				resolve({ code: CODE_ENUM.SERVER_ERROR, msg: '失败' })
			} else {
				log.info(`handle-panel: ${type}_panel 执行成功`)
				resolve({ code: CODE_ENUM.OK, msg: '成功' })
			}
		})
	})
}

function handleDisplayModeByArm64(mode) {
	return new Promise((resolve) => {
		log.info(`开始执行 set-display-mode: set_display_mode ${mode}`)
		exec(`bash /root/scripts/set_display_mode.sh ${mode}`, { shell: '/bin/bash' }, (error) => {
			if (error) {
				log.info(`set-display-mode: set_display_mode ${mode} 执行失败,error: ${error}`)
				resolve({ code: CODE_ENUM.SERVER_ERROR, msg: '失败' })
			} else {
				log.info(`set-display-mode: set_display_mode ${mode} 执行成功`)
				resolve({ code: CODE_ENUM.OK, msg: '成功' })
			}
		})
	})
}

function handleMouseCursorModeByArm64(mode) {
	return new Promise((resolve) => {
		log.info(`开始执行 set-mouse-cursor: ${mode}_cursor`)
		exec(`bash /root/scripts/${mode}_cursor.sh`, { shell: '/bin/bash' }, (error) => {
			if (error) {
				log.info(`set-mouse-cursor: ${mode}_cursor 执行失败,error: ${error}`)
				resolve({ code: CODE_ENUM.SERVER_ERROR, msg: '失败' })
			} else {
				log.info(`set-mouse-cursor: ${mode}_cursor 执行成功`)
				resolve({ code: CODE_ENUM.OK, msg: '成功' })
			}
		})
	})
}

function getOtherInfo() {
	const version = systemProperty.version || actualVersion || app.getVersion() // SDK版本
	const { address: ip, mac } = getIpMacInfo()
	Object.assign(systemProperty, { version, ip, mac })
	return { version, ip, mac }
}

function getCpuInfo() {
	return new Promise(async (res) => {
		if (systemProperty.threadNumber && systemProperty.cpuName) {
			res({ threadNumber: systemProperty.threadNumber, cpuName: systemProperty.cpuName })
		} else {
			const cpu = await si.cpu()
			const threadNumber = cpu.cores // CPU核心线程数
			const cpuName = `${cpu.manufacturer} ${cpu.brand}` // cpu名称
			Object.assign(systemProperty, { threadNumber, cpuName })
			res({ threadNumber, cpuName })
		}
	})
}

function getMemInfo() {
	return new Promise(async (res) => {
		const mem = await si.mem()
		const memory = numToMB(mem.total) // 总内存
		const free = numToMB(mem.free) // 空闲内存
		const used = numToMB(mem.used) // 使用内存
		Object.assign(systemProperty, { memory })
		res({ memory, free, used })
	})
}

function getOsInfo() {
	return new Promise(async (res) => {
		if (systemProperty.sysVersion && systemProperty.sysPlatform && systemProperty.sysArch) {
			res({ sysVersion: systemProperty.sysVersion, sysPlatform: systemProperty.sysPlatform, sysArch: systemProperty.sysArch })
		} else {
			const osInfo = await si.osInfo()
			const sysVersion = osInfo.distro // 系统版本
			const sysPlatform = osInfo.platform // 系统平台
			const sysArch = osInfo.arch // 系统架构
			Object.assign(systemProperty, { sysVersion, sysPlatform, sysArch })
			res({ sysVersion, sysPlatform, sysArch })
		}
	})
}

function getCpuUsedInfo() {
	return new Promise(async (res) => {
		const currentLoad = await si.currentLoad()
		const cpuUsed = Math.floor(currentLoad.currentLoad) // cpu使用率
		res({ cpuUsed })
	})
}

function getDiskInfo() {
	return new Promise(async (res) => {
		const diskInfo = await si.fsSize()
		let diskTotal = 0 // 磁盘总大小
		let diskUsed = 0 // 磁盘已使用大小
		let diskFree = 0 // 磁盘空闲大小
		diskInfo.forEach((item) => {
			diskTotal += item.size
			diskUsed += item.used
			diskFree += item.available
		})
		diskTotal = numToMB(diskTotal)
		diskUsed = numToMB(diskUsed)
		diskFree = numToMB(diskFree)
		Object.assign(systemProperty, { diskTotal })
		res({ diskTotal, diskUsed, diskFree })
	})
}

function numToMB(num) {
	return (num / 1024 / 1024).toFixed(2)
}

const SystemService = {
	getIpMacInfo,
	/**
	 * 获取本机IP和MAC地址等信息
	 * @returns {Object}
	 */
	getNetworkInfo: async () => {
		const data = await getNetworkInfo()
		const code = CODE_ENUM.OK
		if (!data.ip) {
			return Promise.resolve({ code, msg: '获取网络信息失败', data })
		}
		return Promise.resolve({ code, msg: CODE_DIC[code], data })
	},
	/**
	 * 设置网络信息
	 * @param {Object} args
	 * @returns {Object}
	 */
	setNetworkInfo: async (body) => {
		const data = await getNetworkInfo()
		if (body.ip != data.ip || !data.dns.includes(body.dns) || body.gateway != data.gateway || body.netmask != data.netmask) {
			return handleLocalNetwork(body)
		} else {
			return Promise.resolve({ code: CODE_ENUM.OK, msg: '设置成功！' })
		}
	},
	/**
	 * 重启系统
	 */
	rebootSystem: () => {
		const map = {
			win32_x64: 'shutdown -r -t 0',
			win32_ia32: 'shutdown -r -t 0',
			linux_arm64: 'sudo shutdown -r now',
			linux_x64: 'shutdown -r now'
		}
		return new Promise((resolve, reject) => {
			const command = map[`${platform}_${arch}`]
			log.info('系统重启命令', command)
			exec(command, (err, stdout, stderr) => {
				log.info('系统重启命令err', err)
				if (err) {
					resolve({ code: CODE_ENUM.SERVER_ERROR, msg: '重启失败！' })
				} else {
					resolve({ code: CODE_ENUM.OK, msg: '重启成功' })
				}
			})
		})
	},
	/**
	 * 关机
	 */
	closeSystem: () => {
		const map = {
			win32_x64: 'shutdown -s -t 0',
			win32_ia32: 'shutdown -s -t 0',
			linux_arm64: 'sudo shutdown -h now',
			linux_x64: 'shutdown -h now'
		}
		return new Promise((resolve, reject) => {
			const command = map[`${platform}_${arch}`]
			log.info('系统关机命令', command)
			exec(command, (err, stdout, stderr) => {
				if (err) {
					resolve({ code: CODE_ENUM.SERVER_ERROR, msg: '关机失败！' })
				} else {
					resolve({ code: CODE_ENUM.OK, msg: '关机成功' })
				}
			})
		})
	},
	/**
	 * 获取系统所有屏幕信息
	 * @returns {Array<Object>}
	 */
	getAllDisplays: () => {
		const list = screen.getAllDisplays()
		return { code: CODE_ENUM.OK, msg: '获取屏幕信息成功', data: list }
	},
	/**
	 * ping ip
	 * @returns {String}
	 */
	pingIp: (ip) => {
		return new Promise((resolve) => {
			if (!ip) {
				resolve({ code: CODE_ENUM.PARAMS_ERROR, msg: 'ip参数缺失' })
			}
			exec(`chcp 65001 && ping ${ip}`, (error, stdout) => {
				if (error) {
					const logInfo = JSON.stringify(error)
					log.error(`ping ${ip} failed, error: ${logInfo}`)
					resolve({ code: CODE_ENUM.SERVER_ERROR, msg: logInfo })
				} else {
					log.info(`ping-ip-result: ${stdout}`)
					resolve({ code: CODE_ENUM.OK, msg: '操作成功', data: { msg: stdout } })
				}
			})
		})
	},
	/**
	 * 系统任务栏 显示/隐藏
	 * @param {String} type hide or show
	 * @returns {Object}
	 */
	handlePanel: async (type) => {
		if (!['hide', 'show'].includes(type)) {
			const logInfo = `参数异常：${type}`
			log.info(logInfo)
			return Promise.resolve({ code: CODE_ENUM.PARAMS_ERROR, msg: logInfo })
		}
		if (arch != SYSTEM_ARCH_STR_DIC.ARM64) {
			const logInfo = `系统任务栏只支持arm64架构，当前arch: ${arch}`
			log.info(logInfo)
			return Promise.resolve({ code: CODE_ENUM.SERVER_ERROR, msg: logInfo })
		}
		return await handlePanelByArm64(type)
	},
	/**
	 * 系统显示屏模式
	 * @param {Number} mode 1:LVDS竖屏模式 or 2:HDMI竖屏模式 or 3:HDMI+VGA双屏模式 or 4:HDMI(主)+VGA双屏模式
	 * @returns {Object}
	 */
	handleDisplayMode: async (mode) => {
		if (![1, 2, 3, 4].includes(Number(mode))) {
			const logInfo = `参数异常：${mode}`
			log.info(logInfo)
			return Promise.resolve({ code: CODE_ENUM.PARAMS_ERROR, msg: logInfo })
		}
		if (arch != SYSTEM_ARCH_STR_DIC.ARM64) {
			const logInfo = `系统显示屏模式只支持arm64架构，当前arch: ${arch}`
			log.info(logInfo)
			return Promise.resolve({ code: CODE_ENUM.SERVER_ERROR, msg: logInfo })
		}
		return await handleDisplayModeByArm64(mode)
	},
	/**
	 * 系统鼠标光标模式
	 * @param {String} mode hide or show
	 * @returns {Object}
	 */
	handleMouseCursorMode: async (mode) => {
		if (!['hide', 'show'].includes(mode)) {
			const logInfo = `参数异常：${mode}`
			log.info(logInfo)
			return Promise.resolve({ code: CODE_ENUM.PARAMS_ERROR, msg: logInfo })
		}
		if (arch != SYSTEM_ARCH_STR_DIC.ARM64) {
			const logInfo = `鼠标光标模式只支持arm64架构，当前arch: ${arch}`
			log.info(logInfo)
			return Promise.resolve({ code: CODE_ENUM.SERVER_ERROR, msg: logInfo })
		}
		return await handleMouseCursorModeByArm64(mode)
	},
	/**
	 * 获取系统属性 CPU、温度、CPU 占用率、CPU 平均占用率、CPU 最大占用率、内存占用率等
	 * @param {Array<String>} props
	 * @returns {Promise}
	 */
	getSystemProperties: async (props) => {
		props = props ? props.split(',') : []
		let result = {}
		if (!props.length) {
			const otherInfo = getOtherInfo()
			const cpuInfo = await getCpuInfo()
			const memInfo = await getMemInfo()
			const osInfo = await getOsInfo()
			const cpuUsedInfo = await getCpuUsedInfo()
			const diskInfo = await getDiskInfo()
			result = { ...otherInfo, ...cpuInfo, ...memInfo, ...osInfo, ...cpuUsedInfo, ...diskInfo }
			log.info(`getSystemInfo: ${JSON.stringify(result)}`)
			return Promise.resolve(result)
		}
		const obj = { ...systemProperty }
		for (let i = 0; i < props.length; i++) {
			const prop = props[i]
			if (obj[prop]) {
				continue
			}
			if (['version', 'ip', 'mac'].includes(prop)) {
				const otherInfo = getOtherInfo()
				Object.assign(obj, otherInfo)
			} else if (['cpuName', 'threadNumber'].includes(prop)) {
				const cpuInfo = await getCpuInfo()
				Object.assign(obj, cpuInfo)
			} else if (['memory', 'free', 'used'].includes(prop)) {
				const memInfo = await getMemInfo()
				Object.assign(obj, memInfo)
			} else if (['sysVersion', 'sysPlatform', 'sysArch'].includes(prop)) {
				const osInfo = await getOsInfo()
				Object.assign(obj, osInfo)
			} else if (['cpuUsed'].includes(prop)) {
				const cpuUsedInfo = await getCpuUsedInfo()
				Object.assign(obj, cpuUsedInfo)
			} else if (['diskTotal', 'diskUsed', 'diskFree'].includes(prop)) {
				const diskInfo = await getDiskInfo()
				Object.assign(obj, diskInfo)
			}
		}
		// 过滤出只需查询的数据
		props.forEach((prop) => {
			result[prop] = obj[prop]
		})
		log.info(`getSystemInfo: ${JSON.stringify(result)}`)
		return Promise.resolve(result)
	}
}

module.exports = SystemService
