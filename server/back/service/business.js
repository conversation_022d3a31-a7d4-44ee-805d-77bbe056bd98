const commonMapper = require('../mapper/business')
const onlyLock = require('../../../hardware/lock/only_open')
const { CODE_ENUM, ACTION_TYPE_DIC } = require('../../../hardware/enum')
const onlineSocket = require('../webSocket/onlineSocket')
let lock = null
function initLink(params, httpCallback) {
	let num = 0
	const openParams = Object.assign(params, { action: 'openLock' })
	if (lock) {
		lock.openLock(openParams)
	}
	lock = new onlyLock()
	lock.on('only-lock-callback', (arg) => {
		const { action, code } = arg
		if (action == ACTION_TYPE_DIC.INIT) {
			if (code != CODE_ENUM.OK) {
				removeListeners()
				httpCallback({ code: CODE_ENUM.SERVER_ERROR, data: false, msg: '失败' })
			} else {
				lock.openLock(openParams)
			}
		} else if (action == ACTION_TYPE_DIC.OPEN_LOCK) {
			if (code != CODE_ENUM.OK) {
				httpCallback({ code: CODE_ENUM.SERVER_ERROR, data: false, msg: '失败' })
			} else {
				httpCallback({ data: true, msg: '成功' })
			}
			num++
			if (num == openParams.openLockList.length) {
				lock.closeLink({ action: ACTION_TYPE_DIC.CLOSE })
			}
		} else if (action == ACTION_TYPE_DIC.CLOSE) {
			removeListeners()
		}
	})
	let mainLockInfo = { action: 'init' }
	for (const key in global.serverConfig.lockInfo) {
		if (global.serverConfig.lockInfo[key].isMain) {
			mainLockInfo = global.serverConfig.lockInfo[key]
		}
	}
	const initParams = Object.assign(mainLockInfo, { action: 'init' })
	lock.initLink(initParams)
}
function removeListeners() {
	lock.removeAllListeners('only-lock-callback')
	lock = null
}
function parseStringToArray(input) {
	const lockInfo = global.serverConfig.lockInfo
	let oldNum = 0
	let door = input
	let address = '1'
	end: for (const key in lockInfo) {
		const lock = lockInfo[key]
		const maxNumArr = lock.maxNum.split(',') || []
		const lockPlateArr = lock.lockPlate.split(',') || []
		for (let i = 0; i < maxNumArr.length; i++) {
			if (door > Number(maxNumArr[i]) + oldNum) {
				oldNum += maxNumArr[i]
			} else {
				door = door - oldNum
				address = lockPlateArr[i]
				break end
			}
		}
	}
	const result = { address, door }
	return result
}
const businessService = {
	// 柜子列表
	getSidesAllList: () => {
		return new Promise(async (resolve) => {
			const { success, data } = await commonMapper.findAllList('sides')
			resolve({ success, data })
		})
	},
	//已经使用的柜格
	getIscdsUserList: () => {
		return new Promise(async (resolve) => {
			const { success, data } = await commonMapper.findIscdsUserList('sides_user')
			resolve({ success, data })
		})
	},
	getIscdsUserNum: () => {
		return new Promise(async (resolve) => {
			const allData = await commonMapper.findAllList('sides')
			let allNum = 0

			allData.data.forEach((item) => {
				allNum += Number(item.gf)
			})

			const userData = await commonMapper.findExistAllList('sides_user')
			const userNum = userData.data.length
			const data = {
				allNum,
				userNum,
				freeNum: allNum - userNum,
				faultNum: 0
			}
			resolve({ success: true, data })
		})
	},
	addIscdsUserList: (param) => {
		return new Promise(async (resolve) => {
			commonMapper.addIscdsUserList(param)
			resolve({ success: true, msg: '关联成功' })
		})
	},
	addAccessRecords: (param) => {
		return new Promise(async (resolve) => {
			const sidesNames = typeof param.sidesName === 'string' ? param.sidesName.split(',') : [param.sidesName]
			for (const sidesName of sidesNames) {
				const rows = await commonMapper.addList('access_records', { ...param, sidesName })
				if (!rows) {
					return resolve({ success: false, msg: `更新异常！` })
				}
			}
			resolve({ success: true, msg: '更新成功' })
		})
	},
	getAccessRecords: (param) => {
		return new Promise(async (resolve) => {
			const { data, total, totalPages, success } = await commonMapper.findPageist('access_records', param)
			if (!success) {
				return resolve({ success: false, msg: '查询异常！', data: { data, total, totalPages } })
			}
			resolve({ success: true, msg: '查询成功', data: { data, total, totalPages } })
		})
	},
	delIscdsUserList: (ids) => {
		return new Promise(async (resolve) => {
			const rows = await commonMapper.batchFlagDelete('sides_user', 'id', ids)
			if (!rows) {
				return resolve({ success: false, msg: '删除异常！' })
			}
			resolve({ success: true, msg: '删除成功' })
		})
	},
	updateIscdsUserStatus: (data) => {
		return new Promise(async (resolve) => {
			const ids = typeof data.id === 'string' ? data.id.split(',') : [data.id]
			// 循环执行更新操作
			for (const id of ids) {
				const rows = await commonMapper.updateList('sides_user', id, { ...data, id })
				if (!rows) {
					return resolve({ success: false, msg: `更新异常！` })
				}
			}
			resolve({ success: true, msg: '更新成功' })
		})
	},
	remoteLock: (params) => {
		return new Promise(async (resolve) => {
			const arr = []
			if (params.openAllLock) {
				const { data } = await commonMapper.findAllList('sides')
				let result = []
				let maxGf = 0

				data.forEach((item) => {
					maxGf += Number(item.gf)
				})
				result = Array.from({ length: maxGf }, (_, i) => i + 1)
				result.forEach((item, index) => {
					arr[index] = parseStringToArray(item)
				})
			} else {
				params.onlyLockList.forEach((item, index) => {
					arr[index] = parseStringToArray(item.inboxNum)
				})
			}
			const lockParams = {
				openLockList: arr
			}
			initLink(lockParams, (R) => {
				resolve(R)
			})
		})
	},
	synchronizeData: (data) => {
		return new Promise((resolve) => {
			commonMapper.synchronizeData(data)
			resolve({ success: true, msg: '操作成功' })
		})
	},
	synchronizeAllData: (data) => {
		return new Promise((resolve) => {
			commonMapper.synchronizeALLData(data)
			resolve({ success: true, msg: '操作成功' })
		})
	},
	connectionSocket: (data) => {
		return new Promise((resolve) => {
			console.log(data.flag)
			if (data.flag) {
				onlineSocket.createWs()
			} else {
				onlineSocket.closeWs()
			}
			resolve({ success: true, msg: '操作成功' })
		})
	}
}

module.exports = businessService
