const { app, desktopCapturer } = require('electron')
const fs = require('fs')
const { exec } = require('child_process')
const MqttServer = require('../mqtt/index')
const { shortcutName, actualVersion } = require('../../../package.json')
const { CODE_ENUM, CODE_DIC } = require('../enum')
const HARDWARE_ENUM = require('../../../hardware/enum')
const { delay } = require('../../../hardware/tools')
const { installPackage, downloadFileByUrl, rebootApp, getDeviceAuthInfo, exportDevicesInfo } = require('../tools/index')
const ItemNumberMapper = require('../mapper/item_number')
const commonMapper = require('../mapper/business')
const Logger = require('../../../hardware/log/index')
const log = new Logger('app-service')
const arch = process.arch
let itemNumber = ''
function uninstallAppByArm64(appName) {
	return new Promise((resolve) => {
		exec(`sudo dpkg -r ${appName}`, (error) => {
			if (error) {
				const logInfo = `卸载出错: ${error}`
				log.error(logInfo)
				resolve({ code: CODE_ENUM.SERVER_ERROR, msg: logInfo })
			} else {
				const logInfo = '正在卸载中...'
				log.info(logInfo)
				resolve({ code: CODE_ENUM.OK, msg: logInfo })
			}
		})
	})
}
function getTerminalConfigInfo() {
	if (!fs.existsSync(global.serverConfigPath)) {
		const logInfo = `获取终端配置信息失败, 原因: 配置文件${global.serverConfigPath}不存在`
		log.error(logInfo)
		return { code: CODE_ENUM.SERVER_ERROR, msg: logInfo }
	}
	try {
		const str = fs.readFileSync(global.serverConfigPath)
		return { code: CODE_ENUM.OK, msg: '获取配置成功', data: { ...JSON.parse(str), version: actualVersion } }
	} catch (error) {
		const logInfo = `获取终端配置信息失败, 原因: ${JSON.stringify(error)}`
		log.error(logInfo)
		return { code: CODE_ENUM.SERVER_ERROR, msg: logInfo }
	}
}

function setTerminalConfigInfo(body) {
	const result = getTerminalConfigInfo()
	if (result.code !== CODE_ENUM.OK) {
		return result
	}
	const oldConfig = result.data
	const newConfig = { ...oldConfig, ...body }
	const backHttpServerPort = Number(body.backHttpServerPort || oldConfig.backHttpServerPort)
	const backWebSocketServerPort = Number(body.backWebSocketServerPort || oldConfig.backWebSocketServerPort)
	if (!isPort(backHttpServerPort) || !isPort(backWebSocketServerPort)) {
		return { code: CODE_ENUM.SERVER_ERROR, msg: '保存配置信息失败' }
	}
	fs.writeFileSync(global.serverConfigPath, JSON.stringify(newConfig))
	global.serverConfig = newConfig
	delay(500).then(() => {
		//start 更新之后，需要对柜子进行操作 加个判断，需要在修改柜型的页面才会对副柜修改
		if (newConfig.businessInfo.mainCabinetModel != oldConfig.businessInfo.mainCabinetModel || newConfig.businessInfo.secondaryCabinetModel != oldConfig.businessInfo.secondaryCabinetModel) {
			commonMapper.updateSidesList(newConfig.businessInfo)
		}
		// end
		//start 更新之后，需要对人员进行操作 加个判断，切换模式了，把所有非管理员用户，隐藏
		if (newConfig.businessInfo.communicationMode != oldConfig.businessInfo.communicationMode || newConfig.businessInfo.userType != oldConfig.businessInfo.userType) {
			commonMapper.deleteUserNoAdmin()
		}
		// end

		//start 更新之后，如果是开启了离线双面柜，需要进行立即同步操作
		// if (newConfig.businessInfo.communicationMode == 2 && newConfig.businessInfo.mainScreenType != 1 && newConfig.businessInfo.dataAddress) {
		// 	commonMapper.synchronizeALLData()
		// }
		// end
		// 智能硬件云平台配置
		const newIotConfig = newConfig.iotInfo
		if (!comparisonIotConfig(oldConfig.iotInfo, newIotConfig) || !global.mqttServer) {
			initIotServerLink(newIotConfig)
		}
		if (oldConfig.backHttpServerPort != backHttpServerPort || oldConfig.backWebSocketServerPort != backWebSocketServerPort || newConfig.businessInfo.serviceIp != oldConfig.businessInfo.serviceIp || newConfig.businessInfo.httpPort != oldConfig.businessInfo.httpPort) {
			// 重启应用
			rebootApp()
		}
	})
	return { code: CODE_ENUM.OK, msg: '设置配置信息成功', data: newConfig }
}

function comparisonIotConfig(oldIotConfig, newIotConfig) {
	return oldIotConfig.ip == newIotConfig.ip && oldIotConfig.port == newIotConfig.port && oldIotConfig.productKey == newIotConfig.productKey
}

function initIotServerLink(body) {
	return new Promise(async (resolve) => {
		if (!body.ip || !body.port || !body.productKey) {
			const bool = await closeMqttServer()
			return resolve(bool)
		}
		if (global.mqttServer) {
			const bool = await closeMqttServer()
			if (!bool) {
				return resolve(false)
			}
		}
		global.mqttServer = new MqttServer(body)
		global.mqttServer.on('mqtt-callback', (arg) => {
			const { action, code } = arg
			if (action == HARDWARE_ENUM.ACTION_TYPE_DIC.INIT) {
				if (code != HARDWARE_ENUM.CODE_ENUM.OK) {
					removeMqttAllListeners()
					global.mqttServer = null
				}
				return resolve(code != HARDWARE_ENUM.CODE_ENUM.OK)
			}
		})
		global.mqttServer.init()
	})
}

function removeMqttAllListeners() {
	if (global.mqttServer) {
		global.mqttServer.removeAllListeners('mqtt-callback')
	}
}

function closeMqttServer() {
	return new Promise((resolve) => {
		if (!global.mqttServer) {
			return resolve(true)
		}
		global.mqttServer.on('mqtt-callback', (arg) => {
			const { action, code } = arg
			if (action == HARDWARE_ENUM.ACTION_TYPE_DIC.CLOSE) {
				if (code == HARDWARE_ENUM.CODE_ENUM.OK) {
					removeMqttAllListeners()
					global.mqttServer = null
				}
				return resolve(code != HARDWARE_ENUM.CODE_ENUM.OK)
			}
		})
		global.mqttServer.close()
	})
}

function isPort(port) {
	if (typeof port !== 'number') {
		return false
	}
	const reg = /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/
	return reg.test(port)
}

const appService = {
	/**
	 * 获取终端配置信息
	 * @returns {Object}
	 */
	getTerminalConfigInfo: async () => {
		const { code, data, msg } = getTerminalConfigInfo()
		if (code !== CODE_ENUM.OK) {
			return Promise.resolve({ code, data, msg })
		}
		const { itemNumberInfo = {} } = data
		if (itemNumber == itemNumberInfo.itemNumber || !itemNumberInfo.itemNumber) {
			return Promise.resolve({ code, data, msg })
		}
		itemNumber = itemNumberInfo.itemNumber
		const { success, data: itemNumberData } = await ItemNumberMapper.findInfoByItemNumber(itemNumber)
		if (!success) {
			return Promise.resolve({ code, data, msg })
		}
		if (!itemNumberData.config) {
			setTerminalConfigInfo(data)
			return Promise.resolve({ code, data, msg })
		}
		const itemNumberObj = JSON.parse(itemNumberData.config)
		for (const key in itemNumberObj) {
			data[key] = Object.assign(itemNumberObj[key], data[key])
		}
		setTerminalConfigInfo(data)
		return Promise.resolve({ code, data, msg })
	},
	/**
	 * 设置终端配置信息
	 * @returns {Object}
	 */
	setTerminalConfigInfo,
	/**
	 * 卸载app应用
	 * @param {String} appName
	 */
	uninstallApp: async (appName) => {
		if (arch != HARDWARE_ENUM.SYSTEM_ARCH_STR_DIC.ARM64) {
			const logInfo = `卸载app应用只支持arm64架构，当前arch: ${arch}`
			log.info(logInfo)
			return Promise.resolve({ code: CODE_ENUM.SERVER_ERROR, msg: logInfo })
		}
		return await uninstallAppByArm64(appName)
	},
	/**
	 * 重启app
	 */
	rebootApp: () => {
		return rebootApp()
	},
	/**
	 * 关闭app
	 */
	closeApp: () => {
		log.info('退出app')
		app.exit(0)
		global.mainWin = null
	},
	/**
	 * 安装应用
	 */
	installPackage: (body) => {
		return installPackage(body)
	},
	/**
	 * 初始化智能硬件云平台连接
	 */
	initIotServerLink: async (body) => {
		const { iotInfo } = global.serverConfig
		if (comparisonIotConfig(iotInfo, body) && global.mqttServer) {
			return Promise.resolve({ code: CODE_ENUM.OK, msg: CODE_DIC[CODE_ENUM.OK] })
		}
		const bool = await initIotServerLink(body)
		return Promise.resolve({ code: bool ? CODE_ENUM.OK : CODE_ENUM.SERVER_ERROR, msg: CODE_DIC[bool ? CODE_ENUM.OK : CODE_ENUM.SERVER_ERROR] })
	},
	/**
	 * 关闭智能硬件云平台连接
	 */
	closeIotServerLink: async () => {
		const bool = await closeMqttServer()
		return Promise.resolve({ code: bool ? CODE_ENUM.OK : CODE_ENUM.SERVER_ERROR, msg: CODE_DIC[bool ? CODE_ENUM.OK : CODE_ENUM.SERVER_ERROR] })
	},
	/**
	 * 导出设备信息文件
	 */
	exportDevicesInfo,
	/**
	 * 获取设备授权信息
	 */
	getDeviceAuthInfo,
	/**
	 * 导出配置文件
	 */
	exportConfigFile: () => {
		if (!global.serverConfigPath) {
			log.error('导出配置文件失败，未找到配置文件路径')
			return null
		}
		const fileStream = fs.createReadStream(global.serverConfigPath)
		return fileStream
	},
	/**
	 * 导出数据库文件
	 */
	exportDbFile: () => {
		if (!global.databasePath) {
			log.error('导出数据库文件失败，未找到数据库文件路径')
			return null
		}
		const fileStream = fs.createReadStream(global.databasePath)
		return fileStream
	},
	/**
	 * 获取可以被捕获的独立窗口列表
	 */
	getDesktopCapturerSources: () => {
		return new Promise(async (resolve, reject) => {
			try {
				const sources = await desktopCapturer.getSources({ types: ['window', 'screen'] })
				for (const source of sources) {
					source.isApp = source.name === shortcutName
				}
				resolve({ code: CODE_ENUM.OK, msg: CODE_DIC[CODE_ENUM.OK], data: sources })
			} catch (error) {
				resolve({ code: CODE_ENUM.SERVER_ERROR, msg: CODE_DIC[CODE_ENUM.SERVER_ERROR], data: [] })
			}
		})
	},
	upgradeApp: (body) => {
		const downloadUrl = body.url
		downloadFileByUrl(
			downloadUrl,
			(progress) => {
				if (progress == 100) {
					const fileName = downloadUrl.substring(downloadUrl.lastIndexOf('/') + 1)
					installPackage({ fileName })
				}
			},
			(error) => {
				log.info(`下载升级包失败，错误信息：${error}`)
			}
		)
	}
}

module.exports = appService
