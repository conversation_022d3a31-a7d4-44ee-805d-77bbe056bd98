const { shell } = require('electron')
const path = require('path')
const fs = require('fs')
const archiver = require('archiver')
const querystring = require('querystring')
const { createDir } = require('../tools/index')
const { CODE_ENUM } = require('../enum')
const { SYSTEM_PLATFORM_STR_DIC } = require('../../../hardware/enum')
const Logger = require('../../../hardware/log/index')
const log = new Logger('file-service')

const FileService = {
	getLogs: (archiveName) => {
		return new Promise((resolve) => {
			const zipPath = path.join(global.basePath, archiveName)
			const directory = global.logFilePath
			const output = fs.createWriteStream(zipPath)
			const archive = archiver('zip', {
				zlib: { level: 9 } // 压缩级别
			})
			// 监听输出流关闭事件
			output.on('close', () => {
				const fileStream = fs.createReadStream(zipPath)
				log.info(`log目录压缩完成，共：${(archive.pointer() / 1024 / 1024).toFixed(2)}M`, 'success')
				resolve(fileStream)
			})
			// 监听压缩过程中的错误
			archive.on('error', () => {
				resolve(false)
			})
			// 将输出流绑定到归档
			archive.pipe(output)
			// 添加目录下所有文件到压缩文件
			archive.directory(directory, false)
			// 完成归档
			archive.finalize()
		})
	},
	upload: (boundary, body) => {
		return new Promise((resolve, reject) => {
			createDir(global.packagePath)
			let fileName = '' // 文件名
			// 边界字符串
			const file = querystring.parse(body, '\r\n', ':')
			// 获取文件名
			const fileInfo = file['Content-Disposition'].split('; ')
			for (const value in fileInfo) {
				const item = fileInfo[value]
				if (item.indexOf('filename=') !== -1) {
					fileName = item.substring(10, item.length - 1)
					const index = fileName.lastIndexOf(process.platform === SYSTEM_PLATFORM_STR_DIC.LINUX ? '/' : '\\')
					fileName = fileName.substring(index + 1)
				}
			}
			const entireData = body.toString()
			const contentType = file['Content-Type'].substring(1)
			// 获取文件二进制数据开始位置，即contentType的结尾
			const upperBoundary = entireData.indexOf(contentType) + contentType.length
			const shorterData = entireData.substring(upperBoundary)
			// 替换开始位置的空格
			const binaryDataAlmost = shorterData.replace(/^\s\s*/, '').replace(/\s\s*$/, '')
			// 去除数据末尾的额外数据，即: "--"+ boundary + "--"
			const binaryData = binaryDataAlmost
				.substring(0, binaryDataAlmost.indexOf(`--${boundary}--`))
				.replace(/\r\n$/, '')
				.replace(/\n$/, '')
			// 保存文件
			fs.writeFile(`${global.packagePath}/${fileName}`, binaryData, 'binary', (err) => {
				if (err) {
					log.info(`文件上传失败, err: ${err}`)
					resolve({ code: CODE_ENUM.SERVER_ERROR, msg: `文件上传失败, err: ${err}` })
				}
				resolve({ code: CODE_ENUM.OK, msg: '文件上传成功', data: { fileName } })
			})
		})
	},
	/**
	 * 保存质检报告
	 */
	saveReport: (fileName) => {
		const copyFilePath = path.join(global.packagePath, fileName)
		if (!fs.existsSync(copyFilePath)) {
			return { code: CODE_ENUM.SERVER_ERROR, msg: '保存质检报告失败！' }
		}
		createDir(global.reportPath)
		fs.copyFileSync(copyFilePath, `${global.reportPath}/${fileName}`)
		fs.unlinkSync(copyFilePath)
		// 打开文件存储目录
		shell.openPath(global.reportPath)
		return { code: CODE_ENUM.OK, msg: '质检报告保存成功' }
	},
	/**
	 * 拷贝授权文件
	 * @param {String} fileName
	 */
	copyAuthFile: (fileName) => {
		const copyFilePath = path.join(global.packagePath, fileName)
		if (!fs.existsSync(copyFilePath)) {
			return { code: CODE_ENUM.SERVER_ERROR, msg: '授权失败！' }
		}
		fs.copyFileSync(copyFilePath, global.licenseDeviceDatPath)
		fs.unlinkSync(copyFilePath)
		return { code: CODE_ENUM.OK, msg: '授权成功' }
	},
	/**
	 * 拷贝配置文件
	 * @param {String} fileName
	 */
	copyConfigFile: (fileName) => {
		const copyFilePath = path.join(global.packagePath, fileName)
		if (!fs.existsSync(copyFilePath)) {
			return { code: CODE_ENUM.SERVER_ERROR, msg: '保存配置文件失败！' }
		}
		fs.copyFileSync(copyFilePath, global.serverConfigPath)
		fs.unlinkSync(copyFilePath)
		return { code: CODE_ENUM.OK, msg: '保存配置文件成功' }
	},
	/**
	 * 拷贝数据库文件
	 * @param {String} fileName
	 */
	copyDbFile: (fileName) => {
		const copyFilePath = path.join(global.packagePath, fileName)
		if (!fs.existsSync(copyFilePath)) {
			return { code: CODE_ENUM.SERVER_ERROR, msg: '保存数据库文件失败！' }
		}
		fs.copyFileSync(copyFilePath, global.databasePath)
		fs.unlinkSync(copyFilePath)
		return { code: CODE_ENUM.OK, msg: '保存数据库文件成功' }
	}
}

module.exports = FileService
