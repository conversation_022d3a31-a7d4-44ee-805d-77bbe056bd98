class ItemNumber {
	#id
	#itemNumber
	#model
	#code
	#version
	#sex
	#tag
	#config
	#createTime
	#updateTime
	#isDeleted
	constructor(item) {
		this.#id = item.id || ''
		this.#itemNumber = item.itemNumber || ''
		this.#model = item.model || ''
		this.#code = item.code || ''
		this.#version = item.version || ''
		this.#sex = item.sex || ''
		this.#tag = item.tag || ''
		this.#config = item.config || ''
		this.#createTime = item.createTime || ''
		this.#updateTime = item.updateTime || ''
		this.#isDeleted = item.isDeleted || 'N'
	}
	get id() {
		return this.#id
	}
	set id(id) {
		this.#id = id
	}
	get itemNumber() {
		return this.#itemNumber
	}
	set itemNumber(itemNumber) {
		this.#itemNumber = itemNumber
	}
	get model() {
		return this.#model
	}
	set model(model) {
		this.#model = model
	}
	get code() {
		return this.#code
	}
	set code(code) {
		this.#code = code
	}
	get version() {
		return this.#version
	}
	set version(version) {
		this.#version = version
	}
	get sex() {
		return this.#sex
	}
	set sex(sex) {
		this.#sex = sex
	}
	get tag() {
		return this.#tag
	}
	set tag(tag) {
		this.#tag = tag
	}
	get config() {
		return this.#config
	}
	set config(config) {
		this.#config = config
	}
	get createTime() {
		return this.#createTime
	}
	set createTime(createTime) {
		this.#createTime = createTime
	}
	get updateTime() {
		return this.#updateTime
	}
	set updateTime(updateTime) {
		this.#updateTime = updateTime
	}
	get isDeleted() {
		return this.#isDeleted
	}
	set isDeleted(isDeleted) {
		this.#isDeleted = isDeleted
	}
}

module.exports = ItemNumber
