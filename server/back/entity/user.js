class User {
	#id
	#loginId
	#password
	#name
	#age
	#sex
	#avatar
	#feature
	#role
	#loginTime
	#createTime
	#updateTime
	#isDeleted
	#associatedPoliceId
	#associatedPoliceName
	#certificateNumber
	#policeNumber
	#associatedIscds
	#userType
	#faceImg
	#gesture
	#fingerprint
	#associatedPoliceNumber
	constructor(user) {
		this.#id = user.id || ''
		this.#loginId = user.loginId || ''
		this.#password = user.password || ''
		this.#name = user.name || ''
		this.#age = user.age || ''
		this.#sex = user.sex || ''
		this.#avatar = user.avatar || ''
		this.#feature = user.feature || ''
		this.#role = user.role || '3'
		this.#loginTime = user.loginTime || ''
		this.#createTime = user.createTime || ''
		this.#updateTime = user.updateTime || ''
		this.#isDeleted = user.isDeleted || 'N'
		this.#associatedPoliceId = user.associatedPoliceId || ''
		this.#associatedPoliceName = user.associatedPoliceName || ''
		this.#certificateNumber = user.certificateNumber || ''
		this.#policeNumber = user.policeNumber || ''
		this.#associatedIscds = user.associatedIscds || ''
		this.#userType = user.userType || ''
		this.#faceImg = user.faceImg || ''
		this.#gesture = user.gesture || ''
		this.#fingerprint = user.fingerprint || ''
		this.#associatedPoliceNumber = user.associatedPoliceNumber || ''
	}
	get id() {
		return this.#id
	}
	set id(id) {
		this.#id = id
	}
	get loginId() {
		return this.#loginId
	}
	set loginId(loginId) {
		this.#loginId = loginId
	}
	get password() {
		return this.#password
	}
	set password(password) {
		this.#password = password
	}
	get name() {
		return this.#name
	}
	set name(name) {
		this.#name = name
	}
	get age() {
		return this.#age
	}
	set age(age) {
		this.#age = age
	}
	get sex() {
		return this.#sex
	}
	set sex(sex) {
		this.#sex = sex
	}
	get avatar() {
		return this.#avatar
	}
	set avatar(avatar) {
		this.#avatar = avatar
	}
	get feature() {
		return this.#feature
	}
	set feature(feature) {
		this.#feature = feature
	}
	get role() {
		return this.#role
	}
	set role(role) {
		this.#role = role
	}
	get loginTime() {
		return this.#loginTime
	}
	set loginTime(loginTime) {
		this.#loginTime = loginTime
	}
	get createTime() {
		return this.#createTime
	}
	set createTime(createTime) {
		this.#createTime = createTime
	}
	get updateTime() {
		return this.#updateTime
	}
	set updateTime(updateTime) {
		this.#updateTime = updateTime
	}
	get isDeleted() {
		return this.#isDeleted
	}
	set isDeleted(isDeleted) {
		this.#isDeleted = isDeleted
	}
	//start
	get faceImg() {
		return this.#faceImg
	}
	set faceImg(faceImg) {
		this.#faceImg = faceImg
	}
	get userType() {
		return this.#userType
	}
	set userType(userType) {
		this.#userType = userType
	}
	get associatedIscds() {
		return this.#associatedIscds
	}
	set associatedIscds(associatedIscds) {
		this.#associatedIscds = associatedIscds
	}
	get policeNumber() {
		return this.#policeNumber
	}
	set policeNumber(policeNumber) {
		this.#policeNumber = policeNumber
	}
	get certificateNumber() {
		return this.#certificateNumber
	}
	set certificateNumber(certificateNumber) {
		this.#certificateNumber = certificateNumber
	}
	get associatedPoliceId() {
		return this.#associatedPoliceId
	}
	set associatedPoliceId(associatedPoliceId) {
		this.#associatedPoliceId = associatedPoliceId
	}
	get associatedPoliceName() {
		return this.#associatedPoliceName
	}
	set associatedPoliceName(associatedPoliceName) {
		this.#associatedPoliceName = associatedPoliceName
	}
	get gesture() {
		return this.#gesture
	}
	set gesture(gesture) {
		this.#gesture = gesture
	}
	get fingerprint() {
		return this.#fingerprint
	}
	set fingerprint(fingerprint) {
		this.#fingerprint = fingerprint
	}
	get associatedPoliceNumber() {
		return this.#associatedPoliceNumber
	}
	set associatedPoliceNumber(associatedPoliceNumber) {
		this.#associatedPoliceNumber = associatedPoliceNumber
	}
}

module.exports = User
