const Response = {
	result(res, R = {}, headers = {}) {
		res.writeHead(200, Object.assign({ 'Content-Type': 'application/json' }, headers))
		const r = Object.assign({ code: 200, msg: '成功' }, R)
		res.end(JSON.stringify(r))
	},
	resultFileStream(res, fileStream, headers = {}) {
		res.writeHead(200, Object.assign({ 'Content-Type': 'application/octet-stream' }, headers))
		res.body = fileStream
	}
}

module.exports = Response
