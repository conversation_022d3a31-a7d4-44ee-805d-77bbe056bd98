const url = require('url')
const querystring = require('querystring')
const { CODE_ENUM, CODE_DIC } = require('../enum')
const Response = require('../response/index')

function parseRequestParams(requestParams, apiParams) {
	if (!apiParams) {
		return { success: true }
	}
	const apiParamsRequiredKeys = Object.keys(apiParams).filter((key) => {
		if (apiParams[key].required) {
			return key
		}
	})
	if (!apiParamsRequiredKeys) {
		return { success: true }
	}
	let defectKey = ''
	for (let i = 0; i < apiParamsRequiredKeys.length; i++) {
		const key = apiParamsRequiredKeys[i]
		if (!requestParams[key]) {
			defectKey += `${key}, `
		}
	}
	if (defectKey) {
		return { success: false, msg: `缺少${defectKey.substring(0, defectKey.length - 1)}字段` }
	}
	return { success: true }
}

function isBinary(apiParams) {
	if (!apiParams) {
		return false
	}
	const apiParamsTypeKeys = Object.keys(apiParams).filter((key) => apiParams[key].type == 'binary')
	return apiParamsTypeKeys.length > 0
}

const Request = {
	parse(req, res, params = {}) {
		const requestMethod = req.method
		if (params.method && params.method.length && !params.method.includes(String(requestMethod).toLocaleUpperCase())) {
			Response.result(res, { code: CODE_ENUM.PARAMS_ERROR, msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR], data: null })
			return false
		}
		return true
	},
	parseGetParams(req, res, params) {
		// 解析URL
		const parsedUrl = url.parse(req.url)
		// 解析查询字符串参数
		const requestParams = querystring.parse(parsedUrl.query)
		const { success, msg } = parseRequestParams(requestParams, params)
		if (!success) {
			Response.result(res, { code: CODE_ENUM.PARAMS_ERROR, msg, data: null })
			return false
		}
		return requestParams
	},
	parsePostBody(req, res, data) {
		return new Promise((resolve) => {
			if (isBinary(data)) {
				req.setEncoding('binary')
			}
			let body = ''
			req.on('data', (chunk) => {
				body += chunk
			})
			req.on('end', () => {
				if (!body) {
					Response.result(res, { code: CODE_ENUM.PARAMS_ERROR, msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR], data: null })
					resolve(false)
					return
				}
				const contentType = req.headers['content-type']
				let requestParams = body
				if (contentType.indexOf('application/x-www-form-urlencoded') > -1) {
					requestParams = querystring.parse(body)
				} else if (contentType.indexOf('application/json') > -1) {
					requestParams = JSON.parse(body)
				} else {
					return resolve(requestParams)
				}
				const { success, msg } = parseRequestParams(requestParams, data)
				if (!success) {
					Response.result(res, { code: CODE_ENUM.PARAMS_ERROR, msg, data: null })
					resolve(false)
				} else {
					resolve(requestParams)
				}
			})
		})
	}
}

module.exports = Request
