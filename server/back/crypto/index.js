const CryptoJS = require('crypto-js')
const key = CryptoJS.enc.Utf8.parse('SUNDUN01SUNDUN01')
const iv = CryptoJS.enc.Utf8.parse('ABCDEF0123456789')

/**
 * AES加密算法
 * @param {String} str
 * @returns {String}
 */
function aesEncrypt(str) {
	const srcs = CryptoJS.enc.Utf8.parse(str)
	const encrypted = CryptoJS.AES.encrypt(srcs, key, { iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
	return encrypted.ciphertext.toString().toUpperCase()
}
/**
 * AES解密算法
 * @param {String} str
 * @returns {String}
 */
function aesDecrypt(str) {
	const encryptedHexStr = CryptoJS.enc.Hex.parse(str)
	const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
	const decrypt = CryptoJS.AES.decrypt(srcs, key, { iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
	const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
	return decryptedStr.toString()
}

module.exports = { aesEncrypt, aesDecrypt }
