const ENUM = require('./enum')

const SEX_DIC = [
	{
		label: ENUM.SEX_DIC[ENUM.SEX_ENUM.NONE],
		value: ENUM.SEX_ENUM.NONE
	},
	{
		label: ENUM.SEX_DIC[ENUM.SEX_ENUM.MALE],
		value: ENUM.SEX_ENUM.MALE
	},
	{
		label: ENUM.SEX_DIC[ENUM.SEX_ENUM.FEMALE],
		value: ENUM.SEX_ENUM.FEMALE
	}
]

const ROLE_DIC = [
	{
		label: ENUM.ROLE_DIC[ENUM.ROLE_ENUM.ADMIN_ROLE],
		value: ENUM.ROLE_ENUM.ADMIN_ROLE
	},
	{
		label: ENUM.ROLE_DIC[ENUM.ROLE_ENUM.USER_ROLE],
		value: ENUM.ROLE_ENUM.USER_ROLE
	}
]

module.exports = {
	SEX_DIC,
	ROLE_DIC
}
