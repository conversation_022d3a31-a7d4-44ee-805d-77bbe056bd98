const fs = require('fs')
const fetch = require('node-fetch')
const path = require('path')
const { delay } = require('../../../hardware/tools')
const HARDWARE_ENUM = require('../../../hardware/enum')
const Logger = require('../../../hardware/log/index')
const log = new Logger('app-service')
const { spawn, exec } = require('child_process')
const { CODE_ENUM } = require('../enum')
const { name, shortcutName } = require('../../../package.json')
const { app } = require('electron')
const HSAuth = require('../../../hardware/auth/hs_auth')
let hsAuth = null
/**
 * @description: 时间格式化方法
 * @param {日期时间传入一个new Date} date
 * @param {需要格式化的格式,可以传一个自定义函数} formatter
 * @param {不足两位是否补0} isPad
 */
function generateTime(date, formatter, isPad = true) {
	date = date ? date : new Date()
	formatter = _formatNormalize(formatter)
	const dateInfo = {
		year: date.getFullYear(),
		month: date.getMonth() + 1,
		date: date.getDate(),
		hours: date.getHours(),
		minutes: date.getMinutes(),
		seconds: date.getSeconds()
	}
	dateInfo.yyyy = isPad ? dateInfo.year.toString().padStart(4, '0') : dateInfo.year
	dateInfo.MM = isPad ? dateInfo.month.toString().padStart(2, '0') : dateInfo.month
	dateInfo.dd = isPad ? dateInfo.date.toString().padStart(2, '0') : dateInfo.date
	dateInfo.HH = isPad ? dateInfo.hours.toString().padStart(2, '0') : dateInfo.hours
	dateInfo.mm = isPad ? dateInfo.minutes.toString().padStart(2, '0') : dateInfo.minutes
	dateInfo.ss = isPad ? dateInfo.seconds.toString().padStart(2, '0') : dateInfo.seconds
	return formatter(dateInfo)
}

// 辅助函数
function _formatNormalize(formatter) {
	if (typeof formatter == 'function') {
		return formatter
	}
	if (typeof formatter !== 'string') {
		throw new TypeError('formatter must be string or function')
	}
	if (formatter === 'date') {
		formatter = 'yyyy-MM-dd'
	} else if (formatter === 'datetime') {
		formatter = 'yyyy-MM-dd HH:mm:ss'
	}
	return (dateInfo) => {
		const { yyyy, MM, dd, HH, mm, ss } = dateInfo
		return formatter.replace('yyyy', yyyy).replace('MM', MM).replace('dd', dd).replace('HH', HH).replace('mm', mm).replace('ss', ss)
	}
}

/**
 * 判断目录是否存在，不存在则创建
 * recursive: 表示多层目录时递归创建
 * @param {String} path
 */
function createDir(path) {
	if (!fs.existsSync(path)) {
		fs.mkdirSync(path, { recursive: true })
	}
}

//低版本删除文件夹
function deleteFolderRecursively(folderPath) {
	if (fs.existsSync(folderPath)) {
		fs.readdirSync(folderPath).forEach((file, index) => {
			const curPath = path.join(folderPath, file)
			if (fs.lstatSync(curPath).isDirectory()) {
				// 递归删除文件夹
				deleteFolderRecursively(curPath)
			} else {
				// 删除文件
				fs.unlinkSync(curPath)
			}
		})
		// 删除空文件夹
		fs.rmdirSync(folderPath)
	} else {
		console.log('No such directory!')
	}
}
/**
 * 根据文件路径获取文件名
 * @param {String} filePath
 * @returns {String}
 */
function getFileNameByPath(filePath) {
	return filePath.substring(filePath.lastIndexOf('/'))
}
/**
 * 比较版本号
 * @param {String} currentVersion 当前版本号
 * @param {String} upgradeVersion 更新版本号
 * @returns {Number} 大于0 升级
 */
function versionCompare(currentVersion, upgradeVersion) {
	const regexp = /^((\d\.+){3,})+((\d{4}\.\d{2}\.\d{2}\.\d{4})$)/
	if (!regexp.test(currentVersion) || !regexp.test(upgradeVersion)) {
		return 0
	}
	const lenDiff = replaceDot(upgradeVersion).length - replaceDot(currentVersion).length
	function handleVersion(version) {
		const versionArr = version.split('.')
		for (let i = 0; i < lenDiff; i++) {
			versionArr.splice(i + 3, 0, '0')
		}
		version = versionArr.join('.')
		return version
	}
	if (lenDiff != 0) {
		if (lenDiff > 0) {
			currentVersion = handleVersion(currentVersion)
		} else {
			upgradeVersion = handleVersion(upgradeVersion)
		}
	}
	let prev = replaceDot(currentVersion)
	let last = replaceDot(upgradeVersion)
	const maxLen = Math.max(prev.length, last.length)
	if (prev.length < maxLen) {
		prev += '0'
	}
	if (last.length < maxLen) {
		last += '0'
	}
	return Number(last) - Number(prev)
}
function replaceDot(str) {
	return str.replace(/\./g, '')
}
/**
 * 链接下载安装包
 */
async function downloadFileByUrl(url, progressFn, errorFn) {
	createDir(global.packagePath)
	try {
		const response = await fetch(encodeURI(url))
		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`)
		}
		const postProgressFn = progressFn.bind(this)
		const progressTimeout = 200 // 推送下载进度间隔(ms): 默认200ms
		const fileName = url.substring(url.lastIndexOf('/') + 1)
		const stream = fs.createWriteStream(path.join(global.packagePath, fileName))
		const totalSize = response.headers.get('content-length')
		let dataBuffer = ''
		let receivedLength = 0
		let flag = false
		let oldProgress = 0
		response.body
			.on('data', (chunk) => {
				dataBuffer += chunk
				receivedLength += chunk.length
				const progress = (receivedLength / totalSize).toFixed(2) * 100
				flag = progress == 100
				if (!flag && oldProgress != progress) {
					postProgressFn(progress)
					oldProgress = progress
				}
			})
			.on('end', async () => {
				flag = true
				await delay(progressTimeout)
				postProgressFn(100)
			})
			.on('error', errorFn)
			.pipe(stream)
	} catch (error) {
		errorFn(error)
	}
}
/**
 * 安装应用
 */
function installPackage(body) {
	const platform = process.platform
	const fileName = body.fileName
	const installFilePath = path.join(global.packagePath, fileName)
	if (platform == HARDWARE_ENUM.SYSTEM_PLATFORM_STR_DIC.WIN) {
		return installPackageByWin({ installFilePath })
	} else {
		return installPackageByLinux({ installFilePath, fileName })
	}
}
function installPackageByWin(options) {
	return new Promise((resolve) => {
		const { installFilePath } = options
		log.info(`开始安装，安装地址：${installFilePath}`)
		try {
			const install = spawn(installFilePath, { detached: true })
			install.on('close', () => {
				resolve({ code: CODE_ENUM.OK, msg: '安装成功', data: null })
			})
		} catch (error) {
			const logInfo = `安装失败！请到${installFilePath}路径下执行安装！`
			log.error(logInfo)
			resolve({ code: CODE_ENUM.SERVER_ERROR, msg: logInfo, data: null })
		}
	})
}
function installTrue(installFilePath, fileName) {
	return new Promise((resolve, reject) => {
		const dirArr = fs.readdirSync(`${global.packagePath}/opt/gosuncn/`)
		const isInstallOwn = dirArr.includes(name)
		let command = ''
		if (isInstallOwn) {
			command = `
				#移除没有赋权的文件
				rm -rf ${global.packagePath}/opt/gosuncn/${name}/chrome-sandbox
				#复制并覆盖到原安装目录下
				cp -rf ${global.packagePath}/opt/gosuncn/${name} /opt/gosuncn
			`
		} else {
			command = `
				#安装deb包
				sudo dpkg -i ${installFilePath}
				#提示用户安装完成
				notify-send -t 3000 '${fileName}' '安装完成！'
			`
		}
		log.info('安装执行指令：', command)
		try {
			exec(command, async (err, stdout, stderr) => {
				if (err) {
					throw new Error(err)
				} else {
					resolve({ code: CODE_ENUM.OK, msg: '正在安装中...', data: null })
					delay(2000).then(() => {
						log.info('进入到重启命令')
						// 重启应用
						isInstallOwn && rebootApp()
					})
				}
			})
		} catch (error) {
			const logInfo = `安装失败！请到${installFilePath}路径下执行安装！`
			log.error(logInfo)
			resolve({ code: CODE_ENUM.SERVER_ERROR, msg: logInfo, data: null })
		}
	})
}
function installPackageByLinux(options) {
	return new Promise((resolve, reject) => {
		const { installFilePath, fileName } = options
		const command = `
				#解压deb包
				notify-send -t 3000 '${shortcutName}' '安装包正在解压，请耐心等待！'
				dpkg-deb -x ${installFilePath} ${global.packagePath}
				notify-send -t 3000 '${shortcutName}' '安装包解压完成！'
			`
		try {
			exec(command, async (err, stdout, stderr) => {
				if (err) {
					throw new Error(err)
				} else {
					resolve(await installTrue(installFilePath, fileName))
				}
			})
		} catch (error) {
			const logInfo = `安装失败！请到${installFilePath}路径下执行安装！`
			log.error(error)
			resolve({ code: CODE_ENUM.SERVER_ERROR, msg: logInfo, data: null })
		}
	})
}
async function rebootApp() {
	return new Promise((resolve, reject) => {
		try {
			log.info('重启app')
			app.relaunch({ args: process.argv.slice(1).concat(['--relaunch']) })
			app.exit(0)
			resolve({ code: CODE_ENUM.OK, msg: '重启成功' })
			global.mainWin = null
		} catch (error) {
			log.error(`重启app err: ${error}`)
			resolve({ code: CODE_ENUM.SERVER_ERROR, msg: '重启应用失败!' })
		}
	})
}
/**
 * 导出设备信息文件
 */
function exportDevicesInfo() {
	return new Promise(async (resolve, reject) => {
		if (!hsAuth) {
			hsAuth = new HSAuth()
		}
		const stream = await hsAuth.collectDevicesInfo()
		resolve(stream)
	})
}
/**
 * 获取设备授权信息
 */
function getDeviceAuthInfo() {
	return new Promise(async (resolve, reject) => {
		if (!hsAuth) {
			hsAuth = new HSAuth()
		}
		if (HSAuth.authInfo?.state) {
			return resolve({ code: CODE_ENUM.OK, msg: '授权文件存在', data: HSAuth.authInfo })
		}
		const { success, message } = await hsAuth.checkLicense()
		if (success) {
			resolve({ code: CODE_ENUM.OK, msg: '授权文件存在', data: HSAuth.authInfo })
		} else {
			resolve({ code: CODE_ENUM.SERVER_ERROR, msg: message || '授权文件不存在', data: {} })
		}
	})
}
module.exports = {
	rebootApp,
	installPackage,
	versionCompare,
	downloadFileByUrl,
	generateTime,
	createDir,
	getFileNameByPath,
	deleteFolderRecursively,
	getDeviceAuthInfo,
	exportDevicesInfo
}
