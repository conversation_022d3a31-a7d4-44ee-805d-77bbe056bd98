const fs = require('fs')
const fetch = require('node-fetch')
const cryptoJS = require('crypto-js') // 进行 AES 对称加密
const JsRsaSign = require('jsrsasign') // 生成 RSA 密钥对
const JSEncrypt = require('jsencrypt-node') // 进行 RSA 加解密
const { getMacAddress } = require('./tools')

/**
 * rsa 生成私钥 和 公钥
 */
function generateRSAKey() {
	/**
	 * privateKey: 客户端RSA私钥
	 * publicKey: 客户端RSA公钥
	 */
	const rsaKeyPair = JsRsaSign.KEYUTIL.generateKeypair('RSA', 1024)
	// 获取公钥
	let publicKey = JsRsaSign.KEYUTIL.getPEM(rsaKeyPair.pubKeyObj)
	publicKey = publicKey.replace(/\r\n/g, '')
	publicKey = publicKey.replace('-----BEGIN PUBLIC KEY-----', '')
	publicKey = publicKey.replace('-----END PUBLIC KEY-----', '')
	// 获取私钥
	let privateKey = JsRsaSign.KEYUTIL.getPEM(rsaKeyPair.prvKeyObj, 'PKCS8PRV')
	privateKey = privateKey.replace(/\r\n/g, '')
	privateKey = privateKey.replace('-----BEGIN PRIVATE KEY-----', '')
	privateKey = privateKey.replace('-----END PRIVATE KEY-----', '')
	return { privateKey, publicKey }
}
/**
 * rsa encrypt
 */
function rsaEncrypt(encrypted, publicKey) {
	if (!publicKey) return ''
	const encrypt = new JSEncrypt()
	encrypt.setPublicKey(publicKey)
	const cipherText = encrypt.encrypt(encrypted)
	return cipherText.toString()
}
/**
 * rsa decrypt
 */
function rsaDecrypt(decrypted, privateKey) {
	if (!privateKey) return ''
	const privateKeyParse = `-----BEGIN PRIVATE KEY-----\n${privateKey}\n-----END PRIVATE KEY-----`
	const jsEncrypt = new JSEncrypt()
	jsEncrypt.setPrivateKey(privateKeyParse)
	const decrypt = jsEncrypt.decrypt(decrypted)
	return decrypt.toString(cryptoJS.enc.Utf8)
}
/**
 * aes 生成 数据加密 key， 同时用后台publicKey加密
 */
function generateAESKey(publicKey) {
	const key = cryptoJS.lib.WordArray.random(128 / 8).toString()
	const encryptKey = rsaEncrypt(key, publicKey)
	return { key, encryptKey }
}
/**
 * aes 数据加密
 */
function aesEncrypt(encrypted, aesKey) {
	const srcs = cryptoJS.enc.Utf8.parse(encrypted)
	const key = cryptoJS.enc.Utf8.parse(aesKey)
	const encrypt = cryptoJS.AES.encrypt(srcs, key, { mode: cryptoJS.mode.ECB, padding: cryptoJS.pad.Pkcs7 })
	return encrypt.toString()
}
/**
 * aes 数据解密
 */
function aesDecrypt(decrypted, aesKey) {
	const key = cryptoJS.enc.Utf8.parse(aesKey)
	const decrypt = cryptoJS.AES.decrypt(decrypted, key, { mode: cryptoJS.mode.ECB, padding: cryptoJS.pad.Pkcs7 })
	const decryptedStr = decrypt.toString(cryptoJS.enc.Utf8)
	return decryptedStr.toString()
}

/**
 * 获取授权文件
 * @param {String} fileUrl
 * @returns
 */
function getAuthFile(fileUrl) {
	return new Promise((resolve, reject) => {
		if (!fileUrl) {
			return reject(false)
		}
		const stream = fs.createWriteStream(global.iotAuthFilePath)
		fetch(encodeURI(fileUrl))
			.then((res) => {
				if (!res.ok) {
					throw new Error(`HTTP error! status: ${res.status}`)
				}
				return res.body
			})
			.then((body) => {
				body.pipe(stream)
				body.on('end', () => {
					resolve(true)
				})
				body.on('error', () => {
					resolve(false)
				})
			})
			.catch(() => {
				resolve(false)
			})
	})
}

/**
 * 授权文件解密
 * @returns {Object}
 */
function decryptAuthFile() {
	const aesKey = 'gosun-device-iot'
	try {
		const authString = fs.readFileSync(global.iotAuthFilePath).toString()
		const authResult = aesDecrypt(authString, aesKey)
		return authResult
	} catch (e) {
		return null
	}
}

/**
 * 授权信息比对: mac、 productKey
 * @param {Object} authObj
 * @returns {Boolean}
 */
function authComparison(authObj) {
	const macAddress = getMacAddress()
	const productKey = global.serverConfig.iotInfo?.productKey || ''
	const authObjParse = JSON.parse(authObj)
	return macAddress == authObjParse.mac && productKey == authObjParse.productKey
}

module.exports = {
	generateRSAKey,
	rsaEncrypt,
	rsaDecrypt,
	generateAESKey,
	aesEncrypt,
	aesDecrypt,
	getAuthFile,
	decryptAuthFile,
	authComparison
}
