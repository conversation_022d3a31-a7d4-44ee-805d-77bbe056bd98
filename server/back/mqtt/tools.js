// const fetch = require('node-fetch')
const { exec } = require('child_process')
// const fs = require('fs')
// const path = require('path')
const SystemService = require('../service/system')
const AppController = require('../controller/app')
// const { delay } = require('../../../hardware/tools')
const { SYSTEM_PLATFORM_STR_DIC } = require('../../../hardware/enum')

function getNetworkInfo() {
	const data = SystemService.getIpMacInfo()
	return { ip: data.address, mac: data.mac }
}
function getMacAddress() {
	const { mac } = getNetworkInfo()
	return mac.replace(/:/g, '') || ''
}
async function getProperties(props = []) {
	const result = {}
	const flag = props.length == 0
	// 获取设备名称
	const isGetName = flag || props.includes('name')
	if (isGetName) {
		const { iotInfo, itemNumberInfo } = global.serverConfig
		if (iotInfo.productName) {
			result.name = iotInfo.productName
		} else {
			const { ip } = result.ip ? result : getNetworkInfo()
			result.name = `${itemNumberInfo.name}(${ip})`
		}
	}
	// 获取终端配置管理
	const isGetDeviceManage = flag || props.includes('deviceManage')
	if (isGetDeviceManage) {
		const { ip } = result.ip ? result : getNetworkInfo()
		result.deviceManage = `http://${ip}:${global.serverConfig.backHttpServerPort}`
	}
	// 获取设备型号
	const isGetModel = flag || props.includes('model')
	if (isGetModel) {
		const { iotInfo } = global.serverConfig
		result.model = iotInfo.model || ''
	}
	const data = await SystemService.getSystemProperties(props.join(','))
	return Promise.resolve({ ...data, ...result })
}
async function updateDateTime(dateTime) {
	return new Promise((resolve, reject) => {
		let command = ''
		if (process.platform === SYSTEM_PLATFORM_STR_DIC.WIN) {
			const dateTimeArr = dateTime.split(' ')
			const date = dateTimeArr[0]
			const time = dateTimeArr[1]
			command = `date ${date} && time ${time}`
		} else {
			command = `sudo date -s "${dateTime}"`
		}
		exec(command, (err, stdout, stderr) => {
			return resolve(!err)
		})
	})
}
function rebootSystem() {
	return SystemService.rebootSystem()
}

module.exports = {
	getMacAddress,
	getProperties,
	updateDateTime,
	rebootSystem
}
