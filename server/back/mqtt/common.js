const { EventEmitter } = require('events')
const { CODE_DIC, CODE_ENUM, DATA_CODE_DIC, DATA_CODE_ENUM } = require('../../../hardware/enum')
const Logger = require('../../../hardware/log/index')
const log = new Logger('mqtt')

class MqttServerCom<PERSON> extends EventEmitter {
	constructor() {
		super()
		this.logger = log
	}
	handleSuccessByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.OK,
			msg: CODE_DIC[CODE_ENUM.OK],
			data: {
				flag: DATA_CODE_ENUM.ACTION_SUCCESS,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_SUCCESS],
				...data
			}
		})
	}
	handleLinkErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.LINK_ERROR,
			msg: CODE_DIC[CODE_ENUM.LINK_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}
	handleParamsErrorByAction(action, data) {
		this.#callback({
			action,
			code: CODE_ENUM.PARAMS_ERROR,
			msg: CODE_DIC[CODE_ENUM.PARAMS_ERROR],
			data: {
				flag: DATA_CODE_ENUM.ACTION_ERROR,
				message: DATA_CODE_DIC[DATA_CODE_ENUM.ACTION_ERROR],
				...data
			}
		})
	}
	#callback(data) {
		this.emit('mqtt-callback', data)
	}
	removeAllEventListener() {
		this.removeAllListeners('mqtt-callback')
	}
}

module.exports = MqttServerCommon
