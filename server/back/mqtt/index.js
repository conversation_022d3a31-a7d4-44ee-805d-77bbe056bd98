const MqttServerCommon = require('./common')
const Mqtt = require('mqtt')
const { ACTION_TYPE_DIC } = require('../../../hardware/enum')
const { getMacAddress, getProperties, updateDateTime, rebootSystem } = require('./tools')
const { versionCompare, downloadFileByUrl, installPackage, rebootApp } = require('../tools/index')
const { rsaDecrypt, generateAESKey, aesEncrypt, aesDecrypt, getAuthFile } = require('./crypto')
const { actualVersion } = require('../../../package.json')
const EVENT_OBJECT = {
	getLicense: 'license/get', // 获取授权
	postProperty: 'property/post', // 属性上报
	getProperty: 'property/get_reply', // 属性获取
	getOta: 'ota/get', // 获取设备版本
	// 事件
	diskCapacityAlarmEvent: 'event/diskCapacityAlarmEvent', // 磁盘容量告警事件
	faultReportEvent: 'event/faultReportEvent', // 故障上报事件
	upgradeEvent: 'event/upgradeEvent', // 设备升级进度事件
	// 功能
	reboot: 'service/reboot_reply', // 重启应用
	rebootDevice: 'service/rebootDevice_reply', // 重启设备
	restart: 'service/restart_reply', // 重启设备
	upgrade: 'service/upgrade_reply', // 设备升级
	ntp: 'service/ntp_reply', // 同步设备时间
	license: 'service/license_reply', // license授权文件
	postEncrypt: 'encrypt/get' // 数据加密密钥交换
}
const RESULT_EVENT_OBJECT = {
	'license/get_reply': 'getLicense',
	'property/get': 'postProperty',
	'property/get_reply': 'getProperty',
	'service/upgrade': 'upgrade',
	'ota/get_reply': 'upgradeEvent',
	'service/reboot': 'reboot',
	'service/license': 'license',
	'service/rebootDevice': 'rebootDevice',
	'service/restart': 'restart',
	'service/ntp': 'ntp',
	'encrypt/get_reply': 'getOta'
}

class MqttServer extends MqttServerCommon {
	#iotAddress
	#macAddress
	#options
	#client = null
	#publishPrefix
	#topicPrefix
	#localServerConfig
	#iotServerConfig = { encryptType: 1 } // 先将数据加密方式置为默认值1
	static eventObj = EVENT_OBJECT
	/**
	 * @param {Object} options 配置参数
	 * @param {Required String} options.ip iot地址 ip
	 * @param {Required Number} options.port iot地址 port
	 * @param {Required String} options.productKey 产品编码
	 * @param {Required String} options.deviceCode 设备唯一标识，如mac
	 * @param {Required String} options.model 设备型号，没有则用m1
	 * @param {Required Number} options.reconnectionTime 重连间隔时间
	 */
	constructor(options) {
		if (MqttServer.instance) {
			return MqttServer.instance
		}
		super()
		MqttServer.instance = this
		this.#iotAddress = `ws://${options.ip}:${options.port}/mqtt` // mqtt连接地址
		this.#macAddress = getMacAddress()
		this.#options = {
			productKey: options.productKey,
			deviceCode: options.deviceCode || this.#macAddress,
			model: options.model || 'm1',
			reconnectionTime: options.reconnectionTime || 5000
		}
		this.#publishPrefix = `sys/${this.#options.productKey}/${this.#options.deviceCode}/s/`
		this.#topicPrefix = `sys/${this.#options.productKey}/${this.#options.deviceCode}/c/`
	}
	/**
	 * 初始化
	 */
	init() {
		if (!this.#iotAddress) {
			return this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
		}
		const options = {
			clientId: `${this.#options.productKey}&${this.#options.deviceCode}&${this.#options.model}`,
			username: this.#macAddress,
			password: this.#macAddress,
			clean: true, // true: 清除会话, false: 保留会话
			connectTimeout: 8 * 1000, // 重连时间
			reconnectPeriod: this.#options.reconnectionTime,
			keepalive: 60
		}
		this.#client = Mqtt.connect(this.#iotAddress, options)
		this.#client.on('connect', async () => {
			const handleConnection = async () => {
				const bool = await this.#receive()
				if (bool) {
					this.handleSuccessByAction(ACTION_TYPE_DIC.INIT)
				} else {
					await handleConnection()
				}
			}
			try {
				await handleConnection()
			} catch (error) {
				console.error('Error during connection handling:', error)
			}
		})
		this.#client.on('reconnect', () => {
			this.logger.info('重连了reconnect...')
		})
		this.#client.on('error', async (err) => {
			this.logger.info(`服务异常${err}`)
			if (String(err).indexOf('Not authorized') != -1) {
				await this.#destroy()
				return this.handleLinkErrorByAction(ACTION_TYPE_DIC.INIT)
			}
		})
		this.#client.on('close', () => {
			this.logger.info('mqtt close')
			this.handleLinkErrorByAction(ACTION_TYPE_DIC.CLOSE)
		})
		this.#client.on('message', async (topic, message) => {
			const type = topic.replace(this.#topicPrefix, '')
			/**
			 * 上行请求数据，返回字段为data
			 * 下行(被动)接收IOT平台数据, 返回字段为params
			 */
			const result = JSON.parse(message) || {}
			const data = this.#dataDecrypt(result)
			this.logger.info('mqttServer接收到 message: ', type, data)
			switch (type) {
				// 下载授权文件
				case 'license/get_reply':
					await getAuthFile(data.fileUrl)
					this.handleSuccessByAction(RESULT_EVENT_OBJECT[type])
					break
				// 保存服务端授权公钥
				case 'encrypt/get_reply':
					/**
					 * 保存服务端数据传输授权文件公钥
					 * data
					 * encryptType 数据加密方式 1.不加密 2.AES+RSA双向加密
					 * publicKey 服务端RSA公钥
					 */
					this.logger.info('获取到服务器加密公钥信息', data)
					this.#iotServerConfig = data
					/* const aes = generateAESKey(data.publicKey)
					Object.assign(this.#localServerConfig, { aesKey: aes.key, encryptKey: aes.encryptKey }) */
					// 上报版本信息
					this.#publish({ action: RESULT_EVENT_OBJECT[type], version: actualVersion })
					break
				// 属性获取
				case 'property/get':
					const newData = data.filter((item) => item != 'all')
					const properties = await getProperties(newData)
					this.logger.info('属性获取: ', properties)
					this.#publish({ action: RESULT_EVENT_OBJECT[type], ...properties })
					break
				// 属性上报回调
				case 'property/get_reply':
					this.logger.info('属性上报回调: ', data)
					break
				// 同步设备时间
				case 'service/ntp':
					const updateState = await updateDateTime(data.time)
					this.mqtt.publish(RESULT_EVENT_OBJECT[type], { success: updateState })
					break
				// 设备升级
				case 'service/upgrade':
					this.#upgrade(data, type)
					const replyData = {
						taskId: data.taskId,
						packId: data.packId
					}
					this.#publish({ action: RESULT_EVENT_OBJECT[type], ...replyData })
					break
				// 上报版本信息回调
				case 'ota/get_reply':
					this.logger.info('最新升级包信息, 比对版本信息, 进行升级, 推送升级进度')
					this.#upgrade(data, type)
					break
				// 重启应用
				case 'service/reboot':
					this.#publish({ action: RESULT_EVENT_OBJECT[type], success: true })
					rebootApp()
					break
				// 重启设备
				case 'service/rebootDevice':
				case 'service/restart':
					this.#publish({ action: RESULT_EVENT_OBJECT[type], success: true })
					rebootSystem()
					break
				default:
					break
			}
		})
	}
	/**
	 * 关闭
	 */
	close() {
		if (this.#client && this.#client.end) {
			this.#destroy()
			this.#client = null
		} else {
			this.handleSuccessByAction(ACTION_TYPE_DIC.CLOSE)
		}
		this.removeAllEventListener()
	}
	/**
	 * 应用升级
	 * @param {Object} data
	 * @returns
	 */
	#upgrade(data, type) {
		this.logger.info(`比较版本号， 当前版本号：${actualVersion}，远程版本号${data.version}`)
		const num = versionCompare(actualVersion, data.version)
		this.logger.info(`版本比对结果：${num}`)
		if (num <= 0) {
			return
		}
		const downloadUrl = data.fileUrl || data.url
		this.logger.info(`开始下载升级包，下载路径：${downloadUrl}`)
		downloadFileByUrl(
			downloadUrl,
			(progress) => {
				this.#publish({ action: RESULT_EVENT_OBJECT[type], ...data, progress })
				if (progress == 100) {
					const fileName = downloadUrl.substring(downloadUrl.lastIndexOf('/') + 1)
					installPackage({ fileName })
				}
			},
			(error) => {
				this.logger.info(`下载升级包失败，错误信息：${error}`)
			}
		)
	}
	/**
	 * 发布
	 */
	#publish(params) {
		const { action } = params
		const topic = this.#publishPrefix + EVENT_OBJECT[action]
		if (EVENT_OBJECT[action]) {
			const isEncrypt = this.#iotServerConfig.encryptType == 2
			const message = {
				id: Date.now(),
				method: EVENT_OBJECT[action].replace(/\//g, '.')
			}
			if (isEncrypt) {
				const aes = generateAESKey(this.#iotServerConfig.publicKey)
				params = JSON.stringify(params)
				message.params = aesEncrypt(params, aes.key)
				message.aesKey = aes.encryptKey
			} else {
				message.params = params
			}
			this.#client.publish(topic, JSON.stringify(message))
		} else {
			this.logger.info(`推送主题：${action}失败`)
			this.handleParamsErrorByAction(action)
		}
	}
	/**
	 * 订阅
	 */
	#receive() {
		return new Promise((resolve) => {
			this.#client.subscribe(`${this.#topicPrefix}#`, async (error) => {
				if (error) {
					this.logger.info(`订阅主题失败: ${error}`)
					resolve(false)
				} else {
					this.logger.info('订阅主题成功')
					resolve(true)
				}
			})
		})
	}
	/**
	 * 销毁
	 */
	#destroy() {
		return new Promise((resolve) => {
			if (this.#client && this.#client.end) {
				this.#client.end(false, () => {
					this.logger.info('Successfully disconnected!')
					resolve(true)
				})
			} else {
				this.#client = null
				resolve(true)
			}
		})
	}
	/**
	 * 数据解码
	 */
	#dataDecrypt(result) {
		let data = (result.data ? result.data : result.params) || {}
		if (this.#iotServerConfig.encryptType == 2) {
			const { privateKey } = this.#localServerConfig
			const aesKey = rsaDecrypt(result.aesKey, privateKey)
			data = aesDecrypt(data, aesKey)
			data = JSON.parse(data)
		}
		return data
	}
}

module.exports = MqttServer
