/**
 * 协议字典
 */
const PROTOCOL = {
	WS: 1
}
/**
 * 协议前缀字典
 */
const PROTOCOL_PREFIX = {
	SELF_DEV: 'ws://localhost:8899'
}
/**
 * 模式字典
 */
const BOARD_TYPE = {
	NET: 0, // 网络模式（GXX-B10款）
	SCREEN: 1, // 投屏模式（GXX-A10款）
	MULTI_USB: 2, // 多模态设备USB模式（GXX-D10款）
	MULTI_NET: 3, // 多模态设备网络模式（GXX-D10款）
	HW: 4 // 汉王 签名捺印
}

const SIGN_FINGER = {
	PROTOCOL,
	PROTOCOL_PREFIX,
	BOARD_TYPE,
	/**
	 * 外设名称: 签名捺印 - 网络模式(GXX-B10款)
	 * 系统: *
	 * 产商: *
	 * 协议: websocket
	 */
	SIGN_INFO_WS_NET_INFO: {
		path: PROTOCOL_PREFIX.SELF_DEV,
		manufacturer: PROTOCOL.WS,
		boardType: BOARD_TYPE.NET,
		ip: '',
		port: 45008
	},
	/**
	 * 外设名称: 签名捺印 - 投屏模式(GXX-A10款)
	 * 系统: *
	 * 产商: *
	 * 协议: websocket
	 */
	SIGN_INFO_WS_SCREEN_INFO: {
		path: PROTOCOL_PREFIX.SELF_DEV,
		manufacturer: PROTOCOL.WS,
		boardType: BOARD_TYPE.SCREEN
	},
	/**
	 * 外设名称: 签名捺印 - 多模态设备USB模式(GXX-D10款)
	 * 系统: *
	 * 产商: *
	 * 协议: websocket
	 */
	SIGN_INFO_WS_MULTI_USB_INFO: {
		path: PROTOCOL_PREFIX.SELF_DEV,
		manufacturer: PROTOCOL.WS,
		boardType: BOARD_TYPE.MULTI_USB
	},
	/**
	 * 外设名称: 签名捺印 - 多模态设备网络模式(GXX-D10款)
	 * 系统: *
	 * 产商: *
	 * 协议: websocket
	 */
	SIGN_INFO_WS_MULTI_NET_INFO: {
		path: PROTOCOL_PREFIX.SELF_DEV,
		manufacturer: PROTOCOL.WS,
		boardType: BOARD_TYPE.MULTI_NET,
		ip: '',
		port: 45008
	},
	/**
	 * 外设名称: 签名捺印 - HW
	 * 系统: *
	 * 产商: *
	 * 协议: websocket
	 */
	SIGN_INFO_WS_HW_INFO: {
		path: PROTOCOL_PREFIX.SELF_DEV,
		manufacturer: PROTOCOL.WS,
		boardType: BOARD_TYPE.HW
	}
}

module.exports = SIGN_FINGER
