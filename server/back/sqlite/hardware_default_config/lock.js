/**
 * 产商字典
 */
const MANUFACTURER = {
	KA_SP: 1,
	JJ_SP: 2,
	DC_SP: 3,
	KA_WS: 4
}

const LOCK = {
	MANUFACTURER,
	/**
	 * 外设名称: 锁控
	 * 系统: win
	 * 产商: KA
	 * 协议: serialport
	 */
	WIN_KA_SP_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.KA_SP, path: 'COM1', baudRate: 9600, lockPlate: 0, maxNum: 12 }
	},
	/**
	 * 外设名称: 锁控
	 * 系统: uos
	 * 产商: KA
	 * 协议: serialport
	 */
	UOS_KA_SP_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.KA_SP, path: '/dev/ttyS0', baudRate: 9600, lockPlate: 0, maxNum: 12 }
	},
	/**
	 * 外设名称: 锁控
	 * 系统: win
	 * 产商: JJ
	 * 协议: serialport
	 */
	WIN_JJ_SP_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.JJ_SP, path: 'COM3', baudRate: 57600, lockPlate: 0, maxNum: 12 }
	},
	/**
	 * 外设名称: 锁控
	 * 系统: linux
	 * 产商: JJ
	 * 协议: serialport
	 */
	LINUX_JJ_SP_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.JJ_SP, path: '/dev/ttyS3', baudRate: 57600, lockPlate: 0, maxNum: 12 }
	},
	/**
	 * 外设名称: 锁控
	 * 系统: win
	 * 产商: DC
	 * 协议: serialport
	 */
	WIN_DC_SP_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.DC_SP, path: 'COM3', baudRate: 9600, lockPlate: 0, maxNum: 12 }
	},
	/**
	 * 外设名称: 锁控
	 * 系统: linux
	 * 产商: DC
	 * 协议: serialport
	 */
	LINUX_DC_SP_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.DC_SP, path: '/dev/ttyS3', baudRate: 9600, lockPlate: 0, maxNum: 12 }
	},
	/**
	 * 外设名称: 锁控
	 * 系统: win
	 * 产商: KA
	 * 协议: websocket
	 */
	WIN_KA_WS_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.KA_WS, path: 'ws://127.0.0.1:24245/locker', maxNum: 12 }
	},
	/**
	 * 外设名称: 锁控
	 * 系统: uos
	 * 产商: KA
	 * 协议: websocket
	 */
	UOS_KA_WS_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.KA_WS, path: 'ws://127.0.0.1:8540/locker', maxNum: 12 }
	}
}

module.exports = LOCK
