/**
 * 产商字典
 */
const MANUFACTURER = {
	KA_SP: 1,
	SELF_DEV: 2,
	DC_SP: 3,
	KA_WS: 4
}

const SUPPLEMENT_LIGHT = {
	MANUFACTURER,
	/**
	 * 外设名称: 补光灯
	 * 系统: win
	 * 产商: KA
	 * 协议: serialport
	 */
	WIN_KA_SP_INFO: {
		manufacturer: MANUFACTURER.KA_SP,
		path: 'COM1',
		baudRate: 9600
	},
	/**
	 * 外设名称: 补光灯
	 * 系统: uos
	 * 产商: KA
	 * 协议: serialport
	 */
	UOS_KA_SP_INFO: {
		manufacturer: MANUFACTURER.KA_SP,
		path: '/dev/ttyS0',
		baudRate: 9600
	},
	/**
	 * 外设名称: 补光灯
	 * 系统: *
	 * 产商: 自研(self development)
	 * 协议: serialport
	 */
	SD_SP_INFO: {
		manufacturer: MANUFACTURER.SELF_DEV,
		path: '',
		baudRate: 9600
	},
	/**
	 * 外设名称: 补光灯
	 * 系统: win
	 * 产商: DC
	 * 协议: serialport
	 */
	WIN_DC_SP_INFO: {
		manufacturer: MANUFACTURER.DC_SP,
		path: 'COM3',
		baudRate: 9600,
		address: 0,
		door: 7
	},
	/**
	 * 外设名称: 补光灯
	 * 系统: linux
	 * 产商: DC
	 * 协议: serialport
	 */
	LINUX_DC_SP_INFO: {
		manufacturer: MANUFACTURER.DC_SP,
		path: '/dev/ttyS3',
		baudRate: 9600,
		address: 0,
		door: 7
	},
	/**
	 * 外设名称: 补光灯
	 * 系统: win
	 * 产商: KA
	 * 协议: websocket
	 */
	WIN_KA_WS_INFO: {
		manufacturer: MANUFACTURER.KA_WS,
		path: 'ws://127.0.0.1:24246/light'
	},
	/**
	 * 外设名称: 补光灯
	 * 系统: uos
	 * 产商: KA
	 * 协议: websocket
	 */
	UOS_KA_WS_INFO: {
		manufacturer: MANUFACTURER.KA_WS,
		path: 'ws://127.0.0.1:8540/light'
	}
}

module.exports = SUPPLEMENT_LIGHT
