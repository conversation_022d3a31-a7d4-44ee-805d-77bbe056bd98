/**
 * 产商字典
 */
const MANUFACTURER = {
	SELF_DEV: 1
}

const CONTACT_VITAL_SIGNS = {
	MANUFACTURER,
	/**
	 * 外设名称: 接触式生命体征
	 * 系统: linux
	 * 产商: 自研(self development)
	 * 协议: serialport
	 */
	LINUX_SP_INFO: {
		manufacturer: MANUFACTURER.SELF_DEV,
		path: '/dev/ttyUSB0',
		baudRate: 38400
	},
	/**
	 * 外设名称: 接触式生命体征
	 * 系统: win
	 * 产商: 自研(self development)
	 * 协议: serialport
	 */
	WIN_SP_INFO: {
		manufacturer: MANUFACTURER.SELF_DEV,
		path: '',
		baudRate: 38400
	}
}

module.exports = CONTACT_VITAL_SIGNS
