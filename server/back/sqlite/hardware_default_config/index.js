const BUSINESS = require('./business')
const CAMERA = require('./camera')
const CONTACT_VITAL_SIGNS = require('./contact_vital_signs')
const DEVICES = require('./devices')
const FREQUENCY_CARD_READER = require('./frequency_card_reader')
const HIGH_SPEED_SCANNER = require('./high_speed_scanner')
const ID_CARD = require('./id_card')
const IOT = require('./iot')
const LOCK = require('./lock')
const NON_CONTACT_VITAL_SIGNS = require('./non_contact_vital_signs')
const PRINTER = require('./printer')
const QR_CODE = require('./qr_code')
const RFID = require('./rfid')
const SIGN_FINGER = require('./sign_finger')
const SUPPLEMENT_LIGHT = require('./supplement_light')
const WRISTBAND_DRAWER = require('./wristband_drawer')
const WRISTBAND = require('./wristband')

module.exports = {
	BUSINESS,
	CAMERA,
	CONTACT_VITAL_SIGNS,
	DEVICES,
	FREQUENCY_CARD_READER,
	HIGH_SPEED_SCANNER,
	ID_CARD,
	IOT,
	LOCK,
	NON_CONTACT_VITAL_SIGNS,
	PRINTER,
	QR_CODE,
	RFID,
	SIGN_FINGER,
	SUPPLEMENT_LIGHT,
	WRISTBAND_DRAWER,
	WRISTBAND
}
