/**
 * 产商字典
 */
const MANUFACTURER = {
	KA_LOW_WS: 1,
	KA_HIGH_WS: 2,
	DC_LOW_SP: 3,
	DC_HIGH_SP: 4
}

const RFID = {
	MANUFACTURER,
	/**
	 * 外设名称: RFID
	 * 系统: win
	 * 产商: KA 低频
	 * 协议: websocket
	 */
	WIN_KA_LOW_WS_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.KA_LOW_WS, path: 'ws://127.0.0.1:21233/rfid', maxNum: 12 }
	},
	/**
	 * 外设名称: RFID
	 * 系统: win
	 * 产商: KA 高频
	 * 协议: websocket
	 */
	WIN_KA_HIGH_WS_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.KA_HIGH_WS, path: 'ws://127.0.0.1:21232/highrfid', maxNum: 12, address: '' }
	},
	/**
	 * 外设名称: RFID
	 * 系统: uos
	 * 产商: KA 高频
	 * 协议: websocket
	 */
	UOS_KA_HIGH_WS_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.KA_HIGH_WS, path: 'ws://127.0.0.1:21232/highFre', maxNum: 12, address: '' }
	},
	/**
	 * 外设名称: RFID
	 * 系统: win
	 * 产商: DC 低频
	 * 协议: serialport
	 */
	WIN_DC_LOW_SP_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.DC_LOW_SP, path: 'COM4', maxNum: 12, baudRate: 115200, address: '' }
	},
	/**
	 * 外设名称: RFID
	 * 系统: win
	 * 产商: DC 高频
	 * 协议: serialport
	 */
	WIN_DC_HIGH_SP_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.DC_HIGH_SP, path: 'COM4', maxNum: 12, baudRate: 19200, address: '' }
	},
	/**
	 * 外设名称: RFID
	 * 系统: linux
	 * 产商: DC 低频
	 * 协议: serialport
	 */
	LINUX_DC_LOW_SP_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.DC_LOW_SP, path: '/dev/ttyS1', maxNum: 12, baudRate: 115200, address: '' }
	},
	/**
	 * 外设名称: RFID
	 * 系统: linux
	 * 产商: DC 高频
	 * 协议: serialport
	 */
	LINUX_DC_HIGH_SP_INFO: {
		A: { isMain: true, manufacturer: MANUFACTURER.DC_HIGH_SP, path: '/dev/ttyS1', maxNum: 12, baudRate: 19200, address: '' }
	}
}

module.exports = RFID
