const Tables = require('./tables')
const fs = require('fs')
// 查询通用数据库所有表
function checkCommonDbAllTable() {
	return new Promise((resolve, reject) => {
		try {
			global.SqliteDb.executeQuery(`SELECT name FROM sqlite_master WHERE type='table'`, [], (tables, err) => {
				if (err) {
					return resolve([])
				}
				const tableNameList = tables.map((item) => item.name)
				return resolve(tableNameList)
			})
		} catch (error) {
			console.error('查询tables失败:', error)
			return resolve([])
		}
	})
}

// 查询通用数据库表所有字段
function checkCommonDbTableAllColumn(tableName) {
	return new Promise((resolve, reject) => {
		try {
			global.SqliteDb.executeQuery(`PRAGMA table_info(${tableName})`, [], (columns, err) => {
				if (err) {
					return resolve([])
				}
				const columnNameList = columns.map((item) => item.name)
				return resolve(columnNameList)
			})
		} catch (error) {
			console.error('查询columns失败:', error)
			return resolve([])
		}
	})
}

// 往表添加字段
function addColumnToTable(tableName, columnList) {
	return new Promise((resolve, reject) => {
		for (let i = 0; i < columnList.length; i++) {
			const column = columnList[i]
			global.SqliteDb.executeQuery(`ALTER TABLE ${tableName} ADD COLUMN ${column.name} ${column.type || 'TEXT'}`, [], (_, err) => {
				if (err) {
					console.error(`添加字段 ${column.name} 到表 ${tableName} 失败:`, err)
				}
				if (i === columnList.length - 1) {
					console.log('所有字段添加完成')
					return resolve()
				}
			})
		}
	})
}

function handler() {
	return new Promise(async (resolve, reject) => {
		const tableNameList = await checkCommonDbAllTable()
		// 筛选出不存在的表
		let tablesDiff = Tables
		if (tableNameList.length) {
			tablesDiff = Tables.filter((item) => !tableNameList.includes(item.name))
		}
		//start
		if (tablesDiff.length > 0) {
			if (fs.existsSync(global.appDatabasePath)) {
				fs.copyFileSync(global.appDatabasePath, global.databasePath)
			}
		}
		// end

		// 创建不存在的表
		// const initTablePromise = []
		// tablesDiff.forEach((table) => {
		// 	global.SqliteDb.createTable(table.name, table.columns, () => {
		// 		initTablePromise.push(() => Promise.resolve())
		// 	})
		// })
		// await Promise.all(initTablePromise)
		// // 筛选出已存在的表
		// const tablesSame = Tables.filter((item) => tableNameList.includes(item.name))
		// if (!tablesSame.length) {
		// 	return resolve(true)
		// }
		// const addColumnToTablePromise = []
		// for (let i = 0; i < tablesSame.length; i++) {
		// 	const table = tablesSame[i]
		// 	// 查询表字段
		// 	const columnNameList = await checkCommonDbTableAllColumn(table.name)
		// 	// 筛选出未存在的字段
		// 	const diffColumns = table.columns.filter((item) => !columnNameList.includes(item.name))
		// 	if (!diffColumns.length) {
		// 		continue
		// 	}
		// 	addColumnToTablePromise.push(addColumnToTable(table.name, diffColumns))
		// 	if (i === tablesSame.length - 1) {
		// 		// 批量添加字段
		// 		Promise.all(addColumnToTablePromise).then(() => resolve(true))
		// 	}
		// }
	})
}

module.exports = handler
