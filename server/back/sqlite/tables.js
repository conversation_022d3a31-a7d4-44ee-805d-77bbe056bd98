const { SEX_ENUM } = require('../enum')

const Tables = [
	{
		name: global.userTableName,
		columns: [
			{ name: 'id', type: 'TEXT PRIMARY KEY NOT NULL' },
			{ name: 'loginId', type: 'TEXT NOT NULL' },
			{ name: 'password', type: 'TEXT NOT NULL' },
			{ name: 'name', type: 'TEXT NOT NULL' },
			{ name: 'age', type: 'INTEGER' },
			{ name: 'sex', type: `CHAR DEFAULT ${SEX_ENUM.MALE}` },
			{ name: 'avatar', type: 'TEXT' },
			{ name: 'feature', type: 'TEXT' },
			{ name: 'role', type: 'INTEGER' },
			{ name: 'loginTime', type: 'DATETIME' },
			{ name: 'createTime', type: 'DATETIME NOT NULL' },
			{ name: 'updateTime', type: 'DATETIME NOT NULL' },
			{ name: 'isDeleted', type: 'CHAR DEFAULT N NOT NULL' },

			{ name: 'userType', type: 'TEXT' }, //1：管理员 2：民警 3：嫌疑人
			{ name: 'gesture', type: 'TEXT' }, // 手势值
			{ name: 'policeNumber', type: 'TEXT' }, // 警号
			{ name: 'certificateNumber', type: 'TEXT' }, // 身份证号码
			{ name: 'associatedPoliceId', type: 'TEXT' }, // 关联民警id
			{ name: 'associatedPoliceName', type: 'TEXT' }, // 关联民警name
			{ name: 'faceImg', type: 'TEXT' }, //人脸特征
			{ name: 'fingerprint', type: 'TEXT' }, //指纹信息
			{ name: 'associatedIscds', type: 'TEXT' }, //关联柜子
			{ name: 'associatedPoliceNumber', type: 'TEXT' } //关联民警警号
		]
	},
	{
		name: global.itemNumberTableName,
		columns: [
			{ name: 'id', type: 'TEXT PRIMARY KEY NOT NULL' },
			{ name: 'itemNumber', type: 'TEXT NOT NULL' },
			{ name: 'name', type: 'TEXT NOT NULL' },
			{ name: 'model', type: 'TEXT NOT NULL' },
			{ name: 'code', type: 'TEXT NOT NULL' },
			{ name: 'version', type: 'TEXT NOT NULL' },
			{ name: 'tag', type: 'TEXT' },
			{ name: 'config', type: 'TEXT' },
			{ name: 'createTime', type: 'DATETIME NOT NULL' },
			{ name: 'updateTime', type: 'DATETIME NOT NULL' },
			{ name: 'isDeleted', type: 'CHAR DEFAULT N NOT NULL' }
		]
	},
	{
		name: 'access_records', //存取记录
		columns: [
			{ name: 'id', type: 'TEXT PRIMARY KEY NOT NULL' },
			{ name: 'sidesName', type: 'TEXT' }, //保管柜
			{ name: 'openTime', type: 'DATETIME' }, //开柜时间
			{ name: 'closeTime', type: 'DATETIME' }, //关柜时间
			{ name: 'operatorName', type: 'TEXT' }, //操作人
			{ name: 'operatorType', type: 'TEXT' }, //操作类型
			{ name: 'userType', type: 'TEXT' }, //操作人类型
			{ name: 'associatedPoliceId', type: 'TEXT' }, //嫌疑人存关联民警id，民警存自己的id
			{ name: 'urlList', type: 'TEXT' }, // 签名记录
			{ name: 'userName', type: 'TEXT' } // 柜格所属人
		]
	},
	{
		name: 'sides', // 柜子
		columns: [
			{ name: 'id', type: 'TEXT PRIMARY KEY NOT NULL' },
			{ name: 'cabinetId', type: 'TEXT' }, // 柜子id
			{ name: 'cabinetType', type: 'TEXT' }, // 柜子类型 1主柜，2副柜
			{ name: 'name', type: 'TEXT' }, //名字[A,B,C]
			{ name: 'gf', type: 'TEXT' } //形态
		]
	},
	{
		name: 'sides_user', //用户柜格关联表
		columns: [
			{ name: 'id', type: 'TEXT PRIMARY KEY NOT NULL' },
			{ name: 'sidesName', type: 'TEXT' }, // 小柜子名字
			{ name: 'userId', type: 'TEXT' }, // 用户id
			{ name: 'userName', type: 'TEXT' }, // 绑定的人
			{ name: 'isDeleted', type: 'TEXT' }, // 是否取消绑定了 Y绑定中 N解绑了
			{ name: 'userType', type: 'TEXT' },
			{ name: 'status', type: 'TEXT' }, //记录使用场景
			{ name: 'associatedPoliceId', type: 'TEXT' } // 嫌疑人存关联民警id，民警存自己的id
		]
	}
]

module.exports = Tables
