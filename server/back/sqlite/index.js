const sqlite3 = require('sqlite3').verbose()
const Logger = require('../../../hardware/log/index')
const log = new Logger('sqlite-server')

class SQLiteDB {
	constructor(databasePath) {
		this.db = new sqlite3.Database(databasePath)
	}

	createTable(tableName, columns, callback) {
		const columnDefinitions = columns.map((column) => `${column.name} ${column.type}`).join(', ')
		const query = `CREATE TABLE IF NOT EXISTS ${tableName} (${columnDefinitions})`
		this.db.run(query, (result, err) => {
			if (err) {
				log.info('create table error', tableName, JSON.stringify(columns))
			} else {
				callback && callback(result)
			}
		})
	}

	insertData(tableName, data, callback) {
		const columns = Object.keys(data).join(', ')
		const placeholders = Object.keys(data)
			.map(() => '?')
			.join(', ')
		const values = Object.values(data)
		const query = `INSERT INTO ${tableName} (${columns}) VALUES (${placeholders})`
		this.db.run(query, values, function (err) {
			if (err) {
				callback && callback(err)
			} else {
				callback && callback(null, data.id || this.lastID)
			}
		})
	}

	updateData(tableName, data, condition, callback) {
		const setClause = Object.keys(data)
			.map((column) => `${column} = ?`)
			.join(', ')
		const values = Object.values(data)
		const query = `UPDATE ${tableName} SET ${setClause} WHERE ${condition}`
		this.db.run(query, values, callback)
	}

	deleteData(tableName, condition, callback) {
		const query = `DELETE FROM ${tableName} WHERE ${condition}`
		this.db.run(query, {}, callback)
	}

	executeQuery(query, params, callback) {
		this.db.all(query, params, (err, rows) => {
			if (err) {
				console.error(err)
			} else {
				callback(rows)
			}
		})
	}

	copyData(tableName) {
		return new Promise(async (resolve, reject) => {
			const exists = await this.tableExists(tableName)
			if (exists) {
				const delOldTableQuery = `DELETE FROM ${tableName}`
				this.db.run(delOldTableQuery, (err) => {
					if (err) return reject(err)
					return resolve()
				})
			} else {
				const renameTableQuery = `ALTER TABLE ${tableName} RENAME TO ${tableName}_copy;`
				this.db.run(renameTableQuery, (err) => {
					if (err) return reject(err)
					const createNewUserTableQuery = `CREATE TABLE ${tableName} AS SELECT * FROM ${tableName}_copy LIMIT 0;`
					this.db.run(createNewUserTableQuery, (err) => {
						if (err) return reject(err)
						resolve()
					})
				})
			}
		})
	}

	tableExists(tableName) {
		const name = `${tableName}_copy`
		return new Promise((resolve, reject) => {
			const query = `SELECT name FROM sqlite_master WHERE type='table' AND name=?;`
			this.db.get(query, [name], (err, row) => {
				if (err) {
					console.error('Error checking if table exists:', err)
					return reject(err)
				}
				// 如果查询返回了行，则表存在
				resolve(row !== undefined)
			})
		})
	}
	/**
	 * 获取分页数据
	 * @param tableName
	 * @param page
	 * @param pageSize
	 * @param condition // const condition = "column_name = 'value'";
	 * @param checkColumns // const checkColumns = column_name1, column_name2
	 * @returns {Promise<unknown>}
	 */
	getPagedData(tableName, page, pageSize, condition, checkColumns = '*', orderBy = '') {
		const offset = (page - 1) * pageSize
		const conditionStr = condition ? `WHERE ${condition}` : 'WHERE 1=1'
		const countQuery = `SELECT COUNT(*) as total FROM ${tableName} ${conditionStr}`
		const dataQuery = `SELECT ${checkColumns} FROM ${tableName} ${conditionStr} ${orderBy} LIMIT ? OFFSET ?`
		return new Promise((resolve, reject) => {
			this.db.serialize(() => {
				this.db.get(countQuery, (err, row) => {
					if (err) {
						reject(err)
					} else {
						const total = row.total

						this.db.all(dataQuery, [pageSize, offset], (err, rows) => {
							if (err) {
								reject(err)
							} else {
								const totalPages = Math.ceil(total / pageSize)

								resolve({ data: rows, total, totalPages })
							}
						})
					}
				})
			})
		})
	}
	closeConnection() {
		this.db.close()
	}
}
module.exports = SQLiteDB
