const Tables = require('./tables')
const { aesEncrypt } = require('../crypto/index')
const { generateTime } = require('../tools/index')
const CONFIG = require('./hardware_default_config/index')
const { ROLE_ENUM } = require('../enum')
const { v4: uuidv4 } = require('uuid')
// 根据sql 查询通用数据库表所有数据
function checkCommonDbTableAllDataBySql(sql, params = []) {
	return new Promise((resolve, reject) => {
		if (!global.SqliteDb) {
			return resolve([])
		}
		global.SqliteDb.executeQuery(sql, params, (rows, err) => {
			if (err) {
				resolve([])
			} else {
				resolve(rows)
			}
		})
	})
}

// 通用数据库表插入数据
function commonDbInstallData(tableName, installData) {
	return new Promise((resolve, reject) => {
		if (!global.SqliteDb) {
			return resolve()
		}
		for (let i = 0; i < installData.length; i++) {
			const item = installData[i]
			global.SqliteDb.insertData(tableName, item, () => {
				if (i == installData.length - 1) {
					resolve()
				}
			})
		}
	})
}

// 根据key 比对数据
function comparison(list1, list2, key) {
	if (list1.length == 0) {
		return list2
	}
	return list2.filter((item) => {
		return !list1.some((row) => row[key] == item[key])
	})
}

const initTableAfterHandlerMap = {
	[global.userTableName]: () => initUserTableAfterHandler(),
	[global.itemNumberTableName]: () => initItemNumberTableAfterHandler(),
	['sides']: () => initSidesTableAfterHandler(),
	undefined: () => console.log('不存在！')
}

const initUserTableAfterHandler = async () => {
	const time = generateTime(new Date(), 'datetime')
	const defaultUserList = [
		{
			id: uuidv4(),
			loginId: 'gly',
			password: aesEncrypt('123456'),
			name: '内置管理员',
			role: ROLE_ENUM.SYSTEM_ADMIN_ROLE,
			userType: '1',
			gesture: '0,1,2,4,6,7,8',
			certificateNumber: '',
			createTime: time,
			updateTime: time,
			isDeleted: 'N'
		},
		{
			id: uuidv4(),
			loginId: 'sysAdmin',
			password: aesEncrypt('Gosuncn@2819'),
			name: 'sysAdmin',
			userType: '1',
			role: ROLE_ENUM.SYSTEM_ADMIN_ROLE,
			createTime: time,
			updateTime: time,
			isDeleted: 'N'
		},
		{
			id: uuidv4(),
			loginId: 'admin',
			password: aesEncrypt('A123@2819'),
			name: 'admin',
			userType: '1',
			role: ROLE_ENUM.ADMIN_ROLE,
			createTime: time,
			updateTime: time,
			isDeleted: 'N'
		}
	]
	const column = 'loginId'
	// 查数据
	const rows = await checkCommonDbTableAllDataBySql(`SELECT ${column} FROM ${global.userTableName}`)
	// 比对数据
	const installData = comparison(rows, defaultUserList, column)
	// 插入数据
	await commonDbInstallData(global.userTableName, installData)
	return Promise.resolve()
}
const initItemNumberTableAfterHandler = async () => {
	const time = generateTime(new Date(), 'datetime')
	const defaultItemNumberList = [
		{
			id: uuidv4(),
			itemNumber: '8010-00203',
			name: '新一代随身物品柜',
			model: 'GXX-XYD-SSWPG-A1',
			code: 'A1',
			version: 'A1',
			tag: '00-00-00-00',
			config: JSON.stringify({
				colorCameraInfo: CONFIG.CAMERA.COLOR_WEBRTC_INFO,
				blackCameraInfo: CONFIG.CAMERA.BLACK_WEBRTC_INFO,
				printerInfo: CONFIG.PRINTER.INFO,
				wristbandDrawerInfo: CONFIG.WRISTBAND_DRAWER.SD_SP_INFO,
				wristbandInfo: CONFIG.WRISTBAND.HTTP_INFO,
				idCardInfo: CONFIG.ID_CARD.NCN_HTTP_INFO,
				businessInfo: CONFIG.BUSINESS.SSWPG_INFO,
				iotInfo: CONFIG.IOT.SD_SSWPG_MQTT_INFO,
				supplementLightInfo: CONFIG.SUPPLEMENT_LIGHT.LINUX_DC_SP_INFO,
				deviceListInfo: [
					// 料号绑定的外设列表
					CONFIG.DEVICES.CAMERA, //摄像头
					CONFIG.DEVICES.LOCK, //锁控
					CONFIG.DEVICES.SUPPLEMENT_LIGHT, //补光灯
					CONFIG.DEVICES.WRISTBAND_DRAWER, //手环抽屉
					CONFIG.DEVICES.SIGN, // 签名板
					CONFIG.DEVICES.FREQUENCY_CARD_READER //多频读卡器
				]
			}),
			createTime: time,
			updateTime: time,
			isDeleted: 'N'
		}
	]
	const column = 'itemNumber'
	// 查数据
	const rows = await checkCommonDbTableAllDataBySql(`SELECT ${column} FROM ${global.itemNumberTableName}`)
	// 比对数据
	const installData = comparison(rows, defaultItemNumberList, column)
	// 插入数据
	await commonDbInstallData(global.itemNumberTableName, installData)
	return Promise.resolve()
}

const initSidesTableAfterHandler = async () => {
	const defaultSidesList = [{ id: uuidv4(), name: 'A', cabinetType: '1', cabinetId: '4', gf: '19' }]
	const column = 'name'
	// 查数据
	const rows = await checkCommonDbTableAllDataBySql(`SELECT ${column} FROM sides`)
	// 比对数据
	const installData = comparison(rows, defaultSidesList, column)
	// 插入数据
	await commonDbInstallData('sides', installData)
	return Promise.resolve()
}
function handler() {
	return new Promise((resolve, reject) => {
		if (!Tables.length) {
			return resolve(false)
		}
		if (!global.SqliteDb) {
			return resolve(false)
		}
		const promiseAll = []
		for (let i = 0; i < Tables.length; i++) {
			const table = Tables[i]
			global.SqliteDb.createTable(table.name, table.columns, () => {
				initTableAfterHandlerMap[table.name] && initTableAfterHandlerMap[table.name]()
				if (i === Tables.length - 1) {
					Promise.all(promiseAll).then(() => resolve(true))
				}
			})
		}
	})
}

module.exports = handler
