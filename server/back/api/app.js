const { AUTH_OBJ_DIC } = require('../enum')
const baseUrl = '/api'

module.exports = [
	{
		url: `${baseUrl}/app/getTerminalConfigInfo`,
		method: 'GET',
		isNotValid: true,
		responseData: {
			authKey: { type: 'string' },
			version: { type: 'string' },
			backHttpServerPort: { type: 'number' },
			backWebSocketServerPort: { type: 'number' },
			itemNumberInfo: { type: 'object' },
			businessInfo: { type: 'object' }
		}
	},
	{
		url: `${baseUrl}/app/setTerminalConfigInfo`,
		method: 'POST',
		data: {
			itemNumberInfo: { type: 'object' }
		},
		openObj: [AUTH_OBJ_DIC.APP],
		responseData: {
			authKey: { type: 'string' },
			version: { type: 'string' },
			backHttpServerPort: { type: 'number' },
			backWebSocketServerPort: { type: 'number' },
			itemNumberInfo: { type: 'object' },
			businessInfo: { type: 'object' }
		}
	},
	{
		url: `${baseUrl}/app/uninstallApp`,
		method: 'POST',
		data: {
			appName: { type: 'string', required: true }
		},
		responseData: null
	},
	{
		url: `${baseUrl}/app/rebootApp`,
		method: 'GET',
		isNotValid: true,
		responseData: null
	},
	{
		url: `${baseUrl}/app/closeApp`,
		method: 'GET',
		isNotValid: true,
		responseData: null
	},
	{
		url: `${baseUrl}/app/installPackage`,
		method: 'POST',
		data: {
			fileName: { type: 'string', required: true }
		},
		responseData: null
	},
	{
		url: `${baseUrl}/app/openDevTools`,
		method: 'GET',
		isNotValid: true,
		responseData: null
	},
	{
		url: `${baseUrl}/app/setFullScreen`,
		method: 'POST',
		isNotValid: true,
		data: {
			model: { type: 'number', required: true }
		},
		responseData: null
	},
	{
		url: `${baseUrl}/app/initIotServerLink`,
		method: 'POST',
		data: {
			ip: { type: 'string', required: true },
			port: { type: 'number', required: true },
			productKey: { type: 'string', required: true },
			deviceCode: { type: 'string' },
			model: { type: 'string' },
			reconnectionTime: { type: 'number' }
		},
		responseData: null
	},
	{
		url: `${baseUrl}/app/closeIotServerLink`,
		method: 'GET',
		responseData: null
	},
	{
		url: `${baseUrl}/app/exportDevicesInfo`,
		method: 'GET',
		responseData: 'binary'
	},
	{
		url: `${baseUrl}/app/getDeviceAuthInfo`,
		method: 'GET',
		responseData: {
			fileName: { type: 'string' },
			state: { type: 'boolean' },
			periodBegin: { type: 'string' },
			periodEnd: { type: 'string' }
		}
	},
	{
		url: `${baseUrl}/app/exportConfigFile`,
		method: 'GET',
		responseData: 'binary'
	},
	{
		url: `${baseUrl}/app/exportDbFile`,
		method: 'GET',
		responseData: 'binary'
	},
	{
		url: `${baseUrl}/app/getDesktopCapturerSources`,
		method: 'GET',
		isNotValid: true,
		responseData: {
			id: { type: 'string' }
		}
	},
	{
		url: `${baseUrl}/app/upgradeApp`,
		method: 'POST',
		isNotValid: true
	}
]
