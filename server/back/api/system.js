const { AUTH_OBJ_DIC } = require('../enum')
const baseUrl = '/api'

module.exports = [
	{
		url: `${baseUrl}/system/getNetworkInfo`,
		method: 'GET',
		isNotValid: true,
		responseData: {
			ip: { type: 'string' },
			mac: { type: 'string' },
			netmask: { type: 'string' },
			gateway: { type: 'string' },
			dns: { type: 'array' }
		}
	},
	{
		url: `${baseUrl}/system/setNetworkInfo`,
		method: 'POST',
		data: {
			ip: { type: 'string', required: true },
			dns: { type: 'string', required: true },
			gateway: { type: 'string', required: true },
			netmask: { type: 'string', required: true }
		},
		openObj: [AUTH_OBJ_DIC.THIRD, AUTH_OBJ_DIC.APP],
		responseData: null
	},
	{
		url: `${baseUrl}/system/rebootSystem`,
		method: 'GET',
		openObj: [AUTH_OBJ_DIC.THIRD, AUTH_OBJ_DIC.APP],
		responseData: null
	},
	{
		url: `${baseUrl}/system/closeSystem`,
		method: 'GET',
		openObj: [AUTH_OBJ_DIC.THIRD, AUTH_OBJ_DIC.APP],
		responseData: null
	},
	{
		url: `${baseUrl}/system/getAllDisplays`,
		method: 'GET',
		isNotValid: true,
		responseData: [
			{
				size: {
					width: { type: 'number' },
					height: { type: 'number' }
				}
			}
		]
	},
	{
		url: `${baseUrl}/system/pingIp`,
		method: 'GET',
		params: {
			ip: { type: 'string', required: true }
		},
		isNotValid: true,
		responseData: {
			msg: { type: 'string' }
		}
	},
	{
		url: `${baseUrl}/system/handlePanel`,
		method: 'GET',
		params: {
			type: { type: 'string', required: true }
		},
		isNotValid: true,
		responseData: null
	},
	{
		url: `${baseUrl}/system/handleDisplayMode`,
		method: 'GET',
		params: {
			mode: { type: 'number', required: true }
		},
		isNotValid: true,
		responseData: null
	},
	{
		url: `${baseUrl}/system/handleMouseCursorMode`,
		method: 'GET',
		params: {
			mode: { type: 'string', required: true }
		},
		isNotValid: true,
		responseData: null
	},
	{
		url: `${baseUrl}/system/getSystemProperties`,
		method: 'GET',
		params: {
			searchProps: { type: 'string', required: false }
		},
		openObj: [AUTH_OBJ_DIC.THIRD, AUTH_OBJ_DIC.APP],
		responseData: {
			version: { type: 'string' },
			ip: { type: 'string' },
			mac: { type: 'string' },
			threadNumber: { type: 'number' },
			cpuName: { type: 'string' },
			memory: { type: 'number' },
			free: { type: 'number' },
			used: { type: 'number' },
			sysVersion: { type: 'string' },
			sysPlatform: { type: 'string' },
			sysArch: { type: 'string' },
			cpuUsed: { type: 'number' }
		}
	}
]
