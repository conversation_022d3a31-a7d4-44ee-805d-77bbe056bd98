const { AUTH_OBJ_DIC } = require('../enum')
const baseUrl = '/api'

module.exports = [
	{
		url: `${baseUrl}/user/login`,
		method: 'POST',
		data: {
			loginId: { type: 'string', required: true },
			password: { type: 'string', required: true }
		},
		isNotValid: true,
		responseData: {
			id: { type: 'string' },
			loginId: { type: 'string' },
			name: { type: 'string' },
			age: { type: 'number' },
			sex: { type: 'string' },
			avatar: { type: 'string' },
			role: { type: 'number' },
			loginTime: { type: 'string' }
		}
	},
	{
		url: `${baseUrl}/user/gestureLogin`,
		method: 'POST',
		data: {
			gesture: { type: 'string', required: true }
		},
		openObj: [AUTH_OBJ_DIC.APP]
	},
	{
		url: `${baseUrl}/user/register`,
		method: 'POST',
		data: {},
		openObj: [AUTH_OBJ_DIC.APP],
		responseData: null
	},
	{
		url: `${baseUrl}/user/resetPassword`,
		method: 'POST',
		data: {
			id: { type: 'number', required: true }
		},
		responseData: {
			id: { type: 'number' },
			password: { type: 'string' }
		}
	},
	{
		url: `${baseUrl}/user/updatePassword`,
		method: 'POST',
		data: {
			id: { type: 'number', required: true },
			oldPassword: { type: 'string', required: true },
			newPassword: { type: 'string', required: true },
			confirmNewPassword: { type: 'string', required: true }
		},
		responseData: null
	},
	{
		url: `${baseUrl}/user/update`,
		method: 'POST',
		data: {
			id: { type: 'string', required: true },
			loginId: { type: 'string', required: false },
			name: { type: 'string', required: false },
			age: { type: 'number', required: false },
			sex: { type: 'string', required: false },
			avatar: { type: 'string', required: false }
		},
		openObj: [AUTH_OBJ_DIC.APP],
		responseData: null
	},
	{
		url: `${baseUrl}/user/delete`,
		method: 'DELETE',
		params: {
			id: { type: 'string', required: true }
		},
		responseData: null
	},
	{
		url: `${baseUrl}/user/pageList`,
		method: 'POST',
		data: {
			page: { type: 'number', required: true },
			size: { type: 'number', required: true }
		},
		openObj: [AUTH_OBJ_DIC.APP],
		responseData: {
			data: { type: 'array' },
			total: { type: 'number' },
			totalPages: { type: 'number' },
			page: { type: 'number' },
			size: { type: 'number' }
		}
	},
	{
		url: `${baseUrl}/user/findInfoById`,
		method: 'GET',
		params: {
			id: { type: 'number', required: true }
		},
		openObj: [AUTH_OBJ_DIC.APP],
		responseData: {
			id: { type: 'string' },
			loginId: { type: 'string' },
			name: { type: 'string' },
			age: { type: 'number' },
			sex: { type: 'string' },
			avatar: { type: 'string' },
			role: { type: 'number' },
			loginTime: { type: 'string' }
		}
	},
	{
		url: `${baseUrl}/user/getFaceDetectInfo`,
		method: 'POST',
		data: {
			type: { type: 'number' },
			base64: { type: 'string', required: true }
		},
		openObj: [AUTH_OBJ_DIC.THIRD, AUTH_OBJ_DIC.APP],
		responseData: [
			{
				x: { type: 'number' },
				y: { type: 'number' },
				width: { type: 'number' },
				height: { type: 'number' }
			}
		]
	},
	{
		url: `${baseUrl}/user/faceCompareManyByBase64`,
		method: 'POST',
		data: {
			type: { type: 'number' },
			base64: { type: 'string', required: true }
		},
		openObj: [AUTH_OBJ_DIC.THIRD, AUTH_OBJ_DIC.APP],
		responseData: [
			{
				id: { type: 'string' },
				loginId: { type: 'string' },
				name: { type: 'string' },
				age: { type: 'number' },
				sex: { type: 'string' },
				avatar: { type: 'string' },
				role: { type: 'number' },
				loginTime: { type: 'string' },
				similarity: { type: 'number' }
			}
		]
	},
	{
		url: `${baseUrl}/user/batchDelete`,
		method: 'DELETE',
		openObj: [AUTH_OBJ_DIC.APP],
		data: {
			ids: { type: 'string', required: true }
		}
	},
	{
		url: `${baseUrl}/user/fingerCompareManyByFingerFeature`,
		method: 'POST',
		data: {
			fingerFeature: { type: 'string', required: true }
		},
		openObj: [AUTH_OBJ_DIC.APP]
	},
	{
		url: `${baseUrl}/user/userListImport`,
		method: 'POST',
		isNotValid: true,
		data: {
			file: { type: 'binary', required: true }
		},
		openObj: [AUTH_OBJ_DIC.APP]
	},
	{
		url: `${baseUrl}/user/faceListImport`,
		method: 'POST',
		isNotValid: true,
		data: {
			file: { type: 'binary', required: true }
		},
		openObj: [AUTH_OBJ_DIC.APP]
	},
	{
		url: `${baseUrl}/user/allUserList`,
		method: 'GET',
		isNotValid: true,
		openObj: [AUTH_OBJ_DIC.APP]
	},
	{
		url: `${baseUrl}/user/synchronizePersonnel`,
		method: 'POST',
		isNotValid: true,
		openObj: [AUTH_OBJ_DIC.APP]
	}
]
