const baseUrl = '/api'

module.exports = [
	{
		url: `${baseUrl}/hardware/openSoftKeyboard`,
		method: 'GET',
		isNotValid: true,
		responseData: null
	},
	{
		url: `${baseUrl}/hardware/closeSoftKeyboard`,
		method: 'GET',
		isNotValid: true,
		responseData: null
	},
	{
		url: `${baseUrl}/hardware/getSerialPortList`,
		method: 'GET',
		isNotValid: true,
		responseData: [
			{
				label: '串口',
				value: '串口'
			}
		]
	},
	{
		url: `${baseUrl}/hardware/batchTestSerialPort`,
		method: 'POST',
		data: {
			list: { type: 'array', required: true }
		},
		isNotValid: true,
		responseData: [
			{
				path: '串口',
				baudRate: '波特率',
				msg: '是否成功'
			}
		]
	},
	{
		url: `${baseUrl}/hardware/openControlLock`,
		method: 'POST',
		data: {
			com: { type: 'string', required: true },
			baudRate: { type: 'number', required: true }
		},
		isNotValid: true,
		responseData: null
	}
]
