const baseUrl = '/api'

module.exports = [
	{
		url: `${baseUrl}/file/getLogs`,
		method: 'GET',
		responseData: 'binary'
	},
	{
		url: `${baseUrl}/file/upload`,
		method: 'POST',
		data: {
			file: { type: 'binary', required: true }
		},
		isNotValid: true,
		responseData: {
			fileName: { type: 'string' }
		}
	},
	{
		url: `${baseUrl}/file/saveReport`,
		method: 'POST',
		data: {
			fileName: { type: 'string', required: true }
		},
		isNotValid: true,
		responseData: null
	},
	{
		url: `${baseUrl}/file/copyAuthFile`,
		method: 'POST',
		data: {
			fileName: { type: 'string', required: true }
		},
		responseData: null
	},
	{
		url: `${baseUrl}/file/copyConfigFile`,
		method: 'POST',
		data: {
			fileName: { type: 'string', required: true }
		},
		responseData: null
	},
	{
		url: `${baseUrl}/file/copyDbFile`,
		method: 'POST',
		data: {
			fileName: { type: 'string', required: true }
		},
		responseData: null
	}
]
