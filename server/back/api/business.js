const baseUrl = '/api'
const { AUTH_OBJ_DIC } = require('../enum')
module.exports = [
	// 获取柜子列表
	{
		url: `${baseUrl}/business/getSidesAllList`,
		openObj: [AUTH_OBJ_DIC.APP],
		method: 'GET'
	},
	// 获取所有正在使用的柜格
	{
		url: `${baseUrl}/business/getIscdsUserList`,
		openObj: [AUTH_OBJ_DIC.APP],
		method: 'GET'
	},
	// 获取柜格使用统计数据
	{
		url: `${baseUrl}/business/getIscdsUserNum`,
		openObj: [AUTH_OBJ_DIC.APP],
		method: 'GET'
	},
	// 新增一个用户柜子关联
	{
		url: `${baseUrl}/business/addIscdsUserList`,
		openObj: [AUTH_OBJ_DIC.APP],
		method: 'POST'
	},
	// 新增存取记录
	{
		url: `${baseUrl}/business/addAccessRecords`,
		openObj: [AUTH_OBJ_DIC.APP],
		method: 'POST'
	},
	// 存取记录
	{
		url: `${baseUrl}/business/getAccessRecords`,
		openObj: [AUTH_OBJ_DIC.APP],
		method: 'GET'
	},
	// 删除柜格使用绑定关系
	{
		url: `${baseUrl}/business/delIscdsUserList`,
		openObj: [AUTH_OBJ_DIC.APP],
		method: 'DELETE',
		data: {
			ids: { type: 'string', required: true }
		}
	},
	// 修改柜子绑定的状态
	{
		url: `${baseUrl}/business/updateIscdsUserStatus`,
		openObj: [AUTH_OBJ_DIC.APP],
		method: 'POST'
	},
	// 远程开柜
	{
		url: `${baseUrl}/business/remoteOpenLock`,
		method: 'POST',
		isNotValid: true
	},
	// 同步数据
	{
		url: `${baseUrl}/business/synchronizeData`,
		method: 'POST',
		isNotValid: true
	},
	// 同步所有数据
	{
		url: `${baseUrl}/business/synchronizeAllData`,
		method: 'GET',
		isNotValid: true
	},
	// 改变线上模式的socket连接状态
	{
		url: `${baseUrl}/business/connectionSocket`,
		method: 'GET',
		isNotValid: true
	}
]
