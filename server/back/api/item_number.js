const { AUTH_OBJ_DIC } = require('../enum')
const baseUrl = '/api'

module.exports = [
	{
		url: `${baseUrl}/itemNumber/getAllItemNumber`,
		method: 'GET',
		openObj: [AUTH_OBJ_DIC.THIRD, AUTH_OBJ_DIC.APP],
		responseData: [
			{
				id: '',
				itemNumber: '',
				model: '',
				code: '',
				version: '',
				sex: '',
				tag: '',
				config: ''
			}
		]
	},
	{
		url: `${baseUrl}/itemNumber/checkItemNumber`,
		method: 'GET',
		params: {
			itemNumber: { type: 'string', required: true }
		},
		openObj: [AUTH_OBJ_DIC.THIRD, AUTH_OBJ_DIC.APP],
		responseData: {
			id: '',
			itemNumber: '',
			model: '',
			code: '',
			version: '',
			sex: '',
			tag: '',
			config: ''
		}
	}
]
