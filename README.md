
### Node 版本要求
```
14.21.3 32
```

### python 版本要求
```
2.7.*
```

### Visual Studio (使用 C++ 的桌面开发) 版本要求
```
2017
可通过命令下载：npm install -global --production --verbose windows-build-tools
```

### npm 配置
```
npm config set registry https://registry.npmmirror.com/
npm config set node_sass_mirror https://registry.npmmirror.com/-/binary/node-sass/
npm config set electron_mirror https://registry.npmmirror.com/-/binary/electron/
npm config set electron_builder_binaries_mirror https://registry.npmmirror.com/-/binary/electron-builder-binaries/
npm config set atom_mirror https://registry.npmmirror.com/-/binary/atom/
npm config set sqlite3_mirror https://registry.npmmirror.com/-/binary/sqlite3/
npm config set sharp_libvips_mirror https://registry.npmmirror.com/-/binary/sharp-libvips/
npm config set sharp_mirror https://registry.npmmirror.com/-/binary/sharp/
npm config set fsevents_mirror https://registry.npmmirror.com/-/binary/fsevents/
```

### 下载依赖本地缓存
```
electron: https://registry.npmmirror.com/binary.html?path=electron/v10.1.5/
```

### Project 依赖安装
```
npm i -d --force
npm i sqlite3@5.1.0 --build-from-source sqlite3已经改为4.2版本，不需要这个了
```

### Project 依赖安装后
```
npm run rebuild
npm run install-package-after sqlite3已经改为4.2版本，不需要这个了
```

### 开发环境运行命令
```
npm run dev:web
win: npm run dev:win
arm64: npm run dev:linux-arm64
amd64: npm run dev:linux-amd64
```

### 生产环境打包前置命令
```
客户端：npm run build:before
服务：npm run build:server
```

### 生产环境打包
```
npm run build:web
win: npm run build:win-exe
arm64: npm run build:linux-arm64-deb
amd64: npm run build:linux-amd64-deb
```

### 格式化
```
npm run lint
```

### 后台接口文档路径
```
/server/back/api/index
```

### sqlite3 数据库可视化工具 DB Browser
```
下载地址: https://github.com/sqlitebrowser/sqlitebrowser/releases/tag/v3.12.2
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
