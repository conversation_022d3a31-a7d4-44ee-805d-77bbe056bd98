{
  // Default options
  'functions': true,
  'variableNameTransforms': ['dasherize']
}


{{#block "sprites"}}
{{#each sprites}}
@sprite-{{../spritesheet_info.name}}-{{name}}: {{px.offset_x}} {{px.offset_y}} {{px.width}} {{px.height}};
{{/each}}
{{/block}}

{{#block "spritesheet"}}
@sprite-{{spritesheet_info.strings.name_width}}: {{spritesheet.px.width}};
@sprite-{{spritesheet_info.strings.name_height}}: {{spritesheet.px.height}};
@sprite-{{spritesheet_info.strings.name_image}}: '{{{spritesheet.escaped_image}}}';
{{/block}}

{{#block "sprite-functions"}}
{{#if options.functions}}
.sprite-width(@sprite) {
  width: extract(@sprite, 3);
}

.sprite-height(@sprite) {
  height: extract(@sprite, 4);
}

.sprite-position(@sprite) {
  @sprite-offset-x: extract(@sprite, 1);
  @sprite-offset-y: extract(@sprite, 2);
  background-position: @sprite-offset-x @sprite-offset-y;
}

.sprite-{{spritesheet_info.name}}-image() {
  {{! DEV: We slice to trim off excess quotes on an escaped URL }}
  background-image: e(%('url(%a)', e(@sprite-{{spritesheet_info.strings.name_image}})));
  background-repeat: no-repeat;
  display: inline-block;
  vertical-align: middle;
}

.sprite-{{spritesheet_info.name}}(@sprite) {
  .sprite-{{spritesheet_info.name}}-image();
  .sprite-position(@sprite);
  .sprite-width(@sprite);
  .sprite-height(@sprite);
}
{{/if}}
{{/block}}

{{#block "spritesheet-functions"}}
{{#each sprites}}
.sprite-{{../spritesheet_info.name}}-{{name}}() {
	.sprite-{{../spritesheet_info.name}}(@sprite-{{../spritesheet_info.name}}-{{name}})
}
{{/each}}
{{/block}}
