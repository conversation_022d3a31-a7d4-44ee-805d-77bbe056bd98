## configure file version
version=2.0
## Parameter: input size for face detection. 
## Parameter Constraints: [int]
FaceDetect_size=320
## Parameter: FaceDetect Model Name. 
## Parameter Constraints: [string] just recording and try to not work
FaceDetectModelName=FaceDetector_Yunet320_int8_20231115.rknn
#FaceDetect_IR
FaceDetectModelName_IR=RetinaFace_ir_mnet25_fp32.rknn
## Parameter: threshold for face detect 
## Parameter Constraints: [float], Ranges: [0, 1]
detect_threshold=0.5
detect_threshold_IR=0.6
## Parameter: open antispoofingdetect or not
## Parameter Constraints: [int], 0 or 1
is_open_antispoofing=1
## Parameter: threshold for antispoofing detection. 
## Parameter Constraints: [float], Ranges: 0.0 <= detection_thresh <= 1.0. High confidence, more faces are non-liveFace
antispoofing_threshold=0.575
## Parameter: AntiSpoofing Model Name. 
AntiSpoofingModelName=mobileNetv2_AS_fp32_20231026.rknn
## Parameter: AntiSpoofing Model input size. 
AntiSpoofing_width=154
AntiSpoofing_height=154
## Parameter: open AntiSpoofing_IR or not
## Parameter Constraints: [int], 0 or 1
is_open_antispoofing_IR=1 
## Parameter: AntiSpoofing_IR Model Name. 
AntiSpoofingModelName_IR=mobileNetv2_AS_IR_fp16_20240428.rknn
antispoofing_threshold_IR=0.50
antispoofing_threshold_IOU=-1.0
## Parameter: FaceReg Model Name. 
## Parameter Constraints: [string] just recording and try to not work
FaceRegModelName=GoFaceReco_Airface_L106_V4_20190929.rknn
## Parameter: use default models params. 
## Parameter Constraints: [int], 0 or 1
UseDefault=1
## Parameter: use default scale width. 
## Parameter Constraints: [int]
scale_w=640
## Parameter: use default scale height. 
## Parameter Constraints: [int]
scale_h=360
