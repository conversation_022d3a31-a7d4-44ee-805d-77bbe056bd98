## configure file version
version=2.0

## Parameter: input size for face detection. 
## Parameter Constraints: [int]
FaceDetect_size=320

## Parameter: FaceDetect Model Name. 
## Parameter Constraints: [string] just recording and try to not work
FaceDetectModelName=FaceDetector_Yunet320_int8_20231115.rknn


FaceDetectModelName_IR=mobilenet0.25_epoch_66.rknn
## Parameter: threshold for face detect 
## Parameter Constraints: [float], Ranges: [0, 1]
detect_threshold=0.45
detect_threshold_IR=0.8
## Parameter: open antispoofingdetect or not
## Parameter Constraints: [int], 0 or 1
is_open_antispoofing=1


## Parameter: threshold for antispoofing detection. 
## Parameter Constraints: [float], Ranges: 0.0 <= detection_thresh <= 1.0. High confidence, more faces are non-liveFace
antispoofing_threshold=0.575

## Parameter: AntiSpoofing Model Name. 
AntiSpoofingModelName=mobileNet_AS_fp32_20231026
## Parameter: AntiSpoofing Model input size. 
AntiSpoofing_width=154
AntiSpoofing_height=154


## Parameter: open AntiSpoofing_IR or not
## Parameter Constraints: [int], 0 or 1
is_open_antispoofing_IR=1 

## Parameter: AntiSpoofing_IR Model Name. 
AntiSpoofingModelName_IR=mobileNetv2_AS_IR_fp32_20240126
antispoofing_threshold_IR=0.55

antispoofing_threshold_IOU=0


## Parameter: FaceReg Model Name. 
## Parameter Constraints: [string] just recording and try to not work
FaceRegModelName=GoFaceReco_Airface_L106_V4_20190929.rknn


## Parameter: use default models params. 
## Parameter Constraints: [int], 0 or 1
UseDefault=1

## Parameter: use default scale width. 
## Parameter Constraints: [int]
scale_w=640


## Parameter: use default scale height. 
## Parameter Constraints: [int]
scale_h=360