7767517
963 1201
Input                    data                     0 1 data
BinaryOp                 underscorencnn_0_minusscalar0 1 1 data underscorencnn_0_minusscalar0 0=1 1=1 2=1.275000e+02
BinaryOp                 underscorencnn_1_mulscalar0 1 1 underscorencnn_0_minusscalar0 underscorencnn_1_mulscalar0 0=2 1=1 2=7.812500e-03
Convolution              conv_1_conv2d            1 1 underscorencnn_1_mulscalar0 conv_1_batchnorm 0=64 1=3 3=2 4=1 5=1 6=1728
Split                    splitncnn_0              1 2 conv_1_batchnorm conv_1_batchnorm_splitncnn_0 conv_1_batchnorm_splitncnn_1
Sigmoid                  conv_1_swish             1 1 conv_1_batchnorm_splitncnn_1 conv_1_swish
BinaryOp                 underscorencnn_2_mul0    2 1 conv_1_batchnorm_splitncnn_0 conv_1_swish underscorencnn_2_mul0 0=2
ConvolutionDepthWise     conv_2_dw_conv2d         1 1 underscorencnn_2_mul0 conv_2_dw_batchnorm 0=64 1=3 4=1 5=1 6=576 7=64
Split                    splitncnn_1              1 2 conv_2_dw_batchnorm conv_2_dw_batchnorm_splitncnn_0 conv_2_dw_batchnorm_splitncnn_1
Sigmoid                  conv_2_dw_swish          1 1 conv_2_dw_batchnorm_splitncnn_1 conv_2_dw_swish
BinaryOp                 underscorencnn_3_mul1    2 1 conv_2_dw_batchnorm_splitncnn_0 conv_2_dw_swish underscorencnn_3_mul1 0=2
Convolution              dconv_23_conv_sep_conv2d 1 1 underscorencnn_3_mul1 dconv_23_conv_sep_batchnorm 0=128 1=1 5=1 6=8192
Split                    splitncnn_2              1 2 dconv_23_conv_sep_batchnorm dconv_23_conv_sep_batchnorm_splitncnn_0 dconv_23_conv_sep_batchnorm_splitncnn_1
Sigmoid                  dconv_23_conv_sep_swish  1 1 dconv_23_conv_sep_batchnorm_splitncnn_1 dconv_23_conv_sep_swish
BinaryOp                 underscorencnn_4_mul2    2 1 dconv_23_conv_sep_batchnorm_splitncnn_0 dconv_23_conv_sep_swish underscorencnn_4_mul2 0=2
ConvolutionDepthWise     dconv_23_conv_dw_conv2d  1 1 underscorencnn_4_mul2 dconv_23_conv_dw_batchnorm 0=128 1=3 3=2 4=1 5=1 6=1152 7=128
Split                    splitncnn_3              1 2 dconv_23_conv_dw_batchnorm dconv_23_conv_dw_batchnorm_splitncnn_0 dconv_23_conv_dw_batchnorm_splitncnn_1
Sigmoid                  dconv_23_conv_dw_swish   1 1 dconv_23_conv_dw_batchnorm_splitncnn_1 dconv_23_conv_dw_swish
BinaryOp                 underscorencnn_5_mul3    2 1 dconv_23_conv_dw_batchnorm_splitncnn_0 dconv_23_conv_dw_swish underscorencnn_5_mul3 0=2
Convolution              dconv_23_conv_proj_conv2d 1 1 underscorencnn_5_mul3 dconv_23_conv_proj_batchnorm 0=64 1=1 5=1 6=8192
Split                    splitncnn_4              1 3 dconv_23_conv_proj_batchnorm dconv_23_conv_proj_batchnorm_splitncnn_0 dconv_23_conv_proj_batchnorm_splitncnn_1 dconv_23_conv_proj_batchnorm_splitncnn_2
Pooling                  dconv_23_cbam_avgpooling 1 1 dconv_23_conv_proj_batchnorm_splitncnn_2 dconv_23_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             dconv_23_cbam_F_avg_o_fc1 1 1 dconv_23_cbam_avgpooling dconv_23_cbam_F_avg_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_5              1 2 dconv_23_cbam_F_avg_o_fc1 dconv_23_cbam_F_avg_o_fc1_splitncnn_0 dconv_23_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  dconv_23_cbam_F_avg_o_swish 1 1 dconv_23_cbam_F_avg_o_fc1_splitncnn_1 dconv_23_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_6_mul4    2 1 dconv_23_cbam_F_avg_o_fc1_splitncnn_0 dconv_23_cbam_F_avg_o_swish underscorencnn_6_mul4 0=2
Convolution              dconv_23_cbam_F_avg_o_fc2 1 1 underscorencnn_6_mul4 dconv_23_cbam_F_avg_o_fc2 0=64 1=1 5=1 6=1024
Pooling                  dconv_23_cbam_maxpooling 1 1 dconv_23_conv_proj_batchnorm_splitncnn_1 dconv_23_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             dconv_23_cbam_F_max_o_fc1 1 1 dconv_23_cbam_maxpooling dconv_23_cbam_F_max_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_6              1 2 dconv_23_cbam_F_max_o_fc1 dconv_23_cbam_F_max_o_fc1_splitncnn_0 dconv_23_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  dconv_23_cbam_F_max_o_swish 1 1 dconv_23_cbam_F_max_o_fc1_splitncnn_1 dconv_23_cbam_F_max_o_swish
BinaryOp                 underscorencnn_7_mul5    2 1 dconv_23_cbam_F_max_o_fc1_splitncnn_0 dconv_23_cbam_F_max_o_swish underscorencnn_7_mul5 0=2
Convolution              dconv_23_cbam_F_max_o_fc2 1 1 underscorencnn_7_mul5 dconv_23_cbam_F_max_o_fc2 0=64 1=1 5=1 6=1024
BinaryOp                 dconv_23_cbam_ele_add    2 1 dconv_23_cbam_F_avg_o_fc2 dconv_23_cbam_F_max_o_fc2 dconv_23_cbam_ele_add
TanH                     dconv_23_cbam_cn_tanh    1 1 dconv_23_cbam_ele_add dconv_23_cbam_cn_tanh
BinaryOp                 underscorencnn_8_minusscalar1 1 1 dconv_23_cbam_cn_tanh underscorencnn_8_minusscalar1 0=1 1=1 2=-1.000000e+00
BinaryOp                 dconv_23_cbam_mul1       2 1 dconv_23_conv_proj_batchnorm_splitncnn_0 underscorencnn_8_minusscalar1 dconv_23_cbam_mul1 0=2
Split                    splitncnn_7              1 2 dconv_23_cbam_mul1 dconv_23_cbam_mul1_splitncnn_0 dconv_23_cbam_mul1_splitncnn_1
Convolution              res_3_block0_conv_sep_conv2d 1 1 dconv_23_cbam_mul1_splitncnn_1 res_3_block0_conv_sep_batchnorm 0=128 1=1 5=1 6=8192
Split                    splitncnn_8              1 2 res_3_block0_conv_sep_batchnorm res_3_block0_conv_sep_batchnorm_splitncnn_0 res_3_block0_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_block0_conv_sep_swish 1 1 res_3_block0_conv_sep_batchnorm_splitncnn_1 res_3_block0_conv_sep_swish
BinaryOp                 underscorencnn_9_mul6    2 1 res_3_block0_conv_sep_batchnorm_splitncnn_0 res_3_block0_conv_sep_swish underscorencnn_9_mul6 0=2
ConvolutionDepthWise     res_3_block0_conv_dw_conv2d 1 1 underscorencnn_9_mul6 res_3_block0_conv_dw_batchnorm 0=128 1=3 4=1 5=1 6=1152 7=128
Split                    splitncnn_9              1 2 res_3_block0_conv_dw_batchnorm res_3_block0_conv_dw_batchnorm_splitncnn_0 res_3_block0_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_block0_conv_dw_swish 1 1 res_3_block0_conv_dw_batchnorm_splitncnn_1 res_3_block0_conv_dw_swish
BinaryOp                 underscorencnn_10_mul7   2 1 res_3_block0_conv_dw_batchnorm_splitncnn_0 res_3_block0_conv_dw_swish underscorencnn_10_mul7 0=2
Convolution              res_3_block0_conv_proj_conv2d 1 1 underscorencnn_10_mul7 res_3_block0_conv_proj_batchnorm 0=64 1=1 5=1 6=8192
Split                    splitncnn_10             1 3 res_3_block0_conv_proj_batchnorm res_3_block0_conv_proj_batchnorm_splitncnn_0 res_3_block0_conv_proj_batchnorm_splitncnn_1 res_3_block0_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_block0_cbam_avgpooling 1 1 res_3_block0_conv_proj_batchnorm_splitncnn_2 res_3_block0_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_block0_cbam_F_avg_o_fc1 1 1 res_3_block0_cbam_avgpooling res_3_block0_cbam_F_avg_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_11             1 2 res_3_block0_cbam_F_avg_o_fc1 res_3_block0_cbam_F_avg_o_fc1_splitncnn_0 res_3_block0_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_block0_cbam_F_avg_o_swish 1 1 res_3_block0_cbam_F_avg_o_fc1_splitncnn_1 res_3_block0_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_11_mul8   2 1 res_3_block0_cbam_F_avg_o_fc1_splitncnn_0 res_3_block0_cbam_F_avg_o_swish underscorencnn_11_mul8 0=2
Convolution              res_3_block0_cbam_F_avg_o_fc2 1 1 underscorencnn_11_mul8 res_3_block0_cbam_F_avg_o_fc2 0=64 1=1 5=1 6=1024
Pooling                  res_3_block0_cbam_maxpooling 1 1 res_3_block0_conv_proj_batchnorm_splitncnn_1 res_3_block0_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_block0_cbam_F_max_o_fc1 1 1 res_3_block0_cbam_maxpooling res_3_block0_cbam_F_max_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_12             1 2 res_3_block0_cbam_F_max_o_fc1 res_3_block0_cbam_F_max_o_fc1_splitncnn_0 res_3_block0_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_block0_cbam_F_max_o_swish 1 1 res_3_block0_cbam_F_max_o_fc1_splitncnn_1 res_3_block0_cbam_F_max_o_swish
BinaryOp                 underscorencnn_12_mul9   2 1 res_3_block0_cbam_F_max_o_fc1_splitncnn_0 res_3_block0_cbam_F_max_o_swish underscorencnn_12_mul9 0=2
Convolution              res_3_block0_cbam_F_max_o_fc2 1 1 underscorencnn_12_mul9 res_3_block0_cbam_F_max_o_fc2 0=64 1=1 5=1 6=1024
BinaryOp                 res_3_block0_cbam_ele_add 2 1 res_3_block0_cbam_F_avg_o_fc2 res_3_block0_cbam_F_max_o_fc2 res_3_block0_cbam_ele_add
TanH                     res_3_block0_cbam_cn_tanh 1 1 res_3_block0_cbam_ele_add res_3_block0_cbam_cn_tanh
BinaryOp                 underscorencnn_13_minusscalar2 1 1 res_3_block0_cbam_cn_tanh underscorencnn_13_minusscalar2 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_block0_cbam_mul1   2 1 res_3_block0_conv_proj_batchnorm_splitncnn_0 underscorencnn_13_minusscalar2 res_3_block0_cbam_mul1 0=2
BinaryOp                 underscorencnn_14_plus0  2 1 res_3_block0_cbam_mul1 dconv_23_cbam_mul1_splitncnn_0 underscorencnn_14_plus0
Split                    splitncnn_13             1 2 underscorencnn_14_plus0 underscorencnn_14_plus0_splitncnn_0 underscorencnn_14_plus0_splitncnn_1
Convolution              res_3_block1_conv_sep_conv2d 1 1 underscorencnn_14_plus0_splitncnn_1 res_3_block1_conv_sep_batchnorm 0=128 1=1 5=1 6=8192
Split                    splitncnn_14             1 2 res_3_block1_conv_sep_batchnorm res_3_block1_conv_sep_batchnorm_splitncnn_0 res_3_block1_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_block1_conv_sep_swish 1 1 res_3_block1_conv_sep_batchnorm_splitncnn_1 res_3_block1_conv_sep_swish
BinaryOp                 underscorencnn_15_mul10  2 1 res_3_block1_conv_sep_batchnorm_splitncnn_0 res_3_block1_conv_sep_swish underscorencnn_15_mul10 0=2
ConvolutionDepthWise     res_3_block1_conv_dw_conv2d 1 1 underscorencnn_15_mul10 res_3_block1_conv_dw_batchnorm 0=128 1=3 4=1 5=1 6=1152 7=128
Split                    splitncnn_15             1 2 res_3_block1_conv_dw_batchnorm res_3_block1_conv_dw_batchnorm_splitncnn_0 res_3_block1_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_block1_conv_dw_swish 1 1 res_3_block1_conv_dw_batchnorm_splitncnn_1 res_3_block1_conv_dw_swish
BinaryOp                 underscorencnn_16_mul11  2 1 res_3_block1_conv_dw_batchnorm_splitncnn_0 res_3_block1_conv_dw_swish underscorencnn_16_mul11 0=2
Convolution              res_3_block1_conv_proj_conv2d 1 1 underscorencnn_16_mul11 res_3_block1_conv_proj_batchnorm 0=64 1=1 5=1 6=8192
Split                    splitncnn_16             1 3 res_3_block1_conv_proj_batchnorm res_3_block1_conv_proj_batchnorm_splitncnn_0 res_3_block1_conv_proj_batchnorm_splitncnn_1 res_3_block1_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_block1_cbam_avgpooling 1 1 res_3_block1_conv_proj_batchnorm_splitncnn_2 res_3_block1_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_block1_cbam_F_avg_o_fc1 1 1 res_3_block1_cbam_avgpooling res_3_block1_cbam_F_avg_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_17             1 2 res_3_block1_cbam_F_avg_o_fc1 res_3_block1_cbam_F_avg_o_fc1_splitncnn_0 res_3_block1_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_block1_cbam_F_avg_o_swish 1 1 res_3_block1_cbam_F_avg_o_fc1_splitncnn_1 res_3_block1_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_17_mul12  2 1 res_3_block1_cbam_F_avg_o_fc1_splitncnn_0 res_3_block1_cbam_F_avg_o_swish underscorencnn_17_mul12 0=2
Convolution              res_3_block1_cbam_F_avg_o_fc2 1 1 underscorencnn_17_mul12 res_3_block1_cbam_F_avg_o_fc2 0=64 1=1 5=1 6=1024
Pooling                  res_3_block1_cbam_maxpooling 1 1 res_3_block1_conv_proj_batchnorm_splitncnn_1 res_3_block1_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_block1_cbam_F_max_o_fc1 1 1 res_3_block1_cbam_maxpooling res_3_block1_cbam_F_max_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_18             1 2 res_3_block1_cbam_F_max_o_fc1 res_3_block1_cbam_F_max_o_fc1_splitncnn_0 res_3_block1_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_block1_cbam_F_max_o_swish 1 1 res_3_block1_cbam_F_max_o_fc1_splitncnn_1 res_3_block1_cbam_F_max_o_swish
BinaryOp                 underscorencnn_18_mul13  2 1 res_3_block1_cbam_F_max_o_fc1_splitncnn_0 res_3_block1_cbam_F_max_o_swish underscorencnn_18_mul13 0=2
Convolution              res_3_block1_cbam_F_max_o_fc2 1 1 underscorencnn_18_mul13 res_3_block1_cbam_F_max_o_fc2 0=64 1=1 5=1 6=1024
BinaryOp                 res_3_block1_cbam_ele_add 2 1 res_3_block1_cbam_F_avg_o_fc2 res_3_block1_cbam_F_max_o_fc2 res_3_block1_cbam_ele_add
TanH                     res_3_block1_cbam_cn_tanh 1 1 res_3_block1_cbam_ele_add res_3_block1_cbam_cn_tanh
BinaryOp                 underscorencnn_19_minusscalar3 1 1 res_3_block1_cbam_cn_tanh underscorencnn_19_minusscalar3 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_block1_cbam_mul1   2 1 res_3_block1_conv_proj_batchnorm_splitncnn_0 underscorencnn_19_minusscalar3 res_3_block1_cbam_mul1 0=2
BinaryOp                 underscorencnn_20_plus1  2 1 res_3_block1_cbam_mul1 underscorencnn_14_plus0_splitncnn_0 underscorencnn_20_plus1
Split                    splitncnn_19             1 2 underscorencnn_20_plus1 underscorencnn_20_plus1_splitncnn_0 underscorencnn_20_plus1_splitncnn_1
Convolution              res_3_block2_conv_sep_conv2d 1 1 underscorencnn_20_plus1_splitncnn_1 res_3_block2_conv_sep_batchnorm 0=128 1=1 5=1 6=8192
Split                    splitncnn_20             1 2 res_3_block2_conv_sep_batchnorm res_3_block2_conv_sep_batchnorm_splitncnn_0 res_3_block2_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_block2_conv_sep_swish 1 1 res_3_block2_conv_sep_batchnorm_splitncnn_1 res_3_block2_conv_sep_swish
BinaryOp                 underscorencnn_21_mul14  2 1 res_3_block2_conv_sep_batchnorm_splitncnn_0 res_3_block2_conv_sep_swish underscorencnn_21_mul14 0=2
ConvolutionDepthWise     res_3_block2_conv_dw_conv2d 1 1 underscorencnn_21_mul14 res_3_block2_conv_dw_batchnorm 0=128 1=3 4=1 5=1 6=1152 7=128
Split                    splitncnn_21             1 2 res_3_block2_conv_dw_batchnorm res_3_block2_conv_dw_batchnorm_splitncnn_0 res_3_block2_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_block2_conv_dw_swish 1 1 res_3_block2_conv_dw_batchnorm_splitncnn_1 res_3_block2_conv_dw_swish
BinaryOp                 underscorencnn_22_mul15  2 1 res_3_block2_conv_dw_batchnorm_splitncnn_0 res_3_block2_conv_dw_swish underscorencnn_22_mul15 0=2
Convolution              res_3_block2_conv_proj_conv2d 1 1 underscorencnn_22_mul15 res_3_block2_conv_proj_batchnorm 0=64 1=1 5=1 6=8192
Split                    splitncnn_22             1 3 res_3_block2_conv_proj_batchnorm res_3_block2_conv_proj_batchnorm_splitncnn_0 res_3_block2_conv_proj_batchnorm_splitncnn_1 res_3_block2_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_block2_cbam_avgpooling 1 1 res_3_block2_conv_proj_batchnorm_splitncnn_2 res_3_block2_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_block2_cbam_F_avg_o_fc1 1 1 res_3_block2_cbam_avgpooling res_3_block2_cbam_F_avg_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_23             1 2 res_3_block2_cbam_F_avg_o_fc1 res_3_block2_cbam_F_avg_o_fc1_splitncnn_0 res_3_block2_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_block2_cbam_F_avg_o_swish 1 1 res_3_block2_cbam_F_avg_o_fc1_splitncnn_1 res_3_block2_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_23_mul16  2 1 res_3_block2_cbam_F_avg_o_fc1_splitncnn_0 res_3_block2_cbam_F_avg_o_swish underscorencnn_23_mul16 0=2
Convolution              res_3_block2_cbam_F_avg_o_fc2 1 1 underscorencnn_23_mul16 res_3_block2_cbam_F_avg_o_fc2 0=64 1=1 5=1 6=1024
Pooling                  res_3_block2_cbam_maxpooling 1 1 res_3_block2_conv_proj_batchnorm_splitncnn_1 res_3_block2_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_block2_cbam_F_max_o_fc1 1 1 res_3_block2_cbam_maxpooling res_3_block2_cbam_F_max_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_24             1 2 res_3_block2_cbam_F_max_o_fc1 res_3_block2_cbam_F_max_o_fc1_splitncnn_0 res_3_block2_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_block2_cbam_F_max_o_swish 1 1 res_3_block2_cbam_F_max_o_fc1_splitncnn_1 res_3_block2_cbam_F_max_o_swish
BinaryOp                 underscorencnn_24_mul17  2 1 res_3_block2_cbam_F_max_o_fc1_splitncnn_0 res_3_block2_cbam_F_max_o_swish underscorencnn_24_mul17 0=2
Convolution              res_3_block2_cbam_F_max_o_fc2 1 1 underscorencnn_24_mul17 res_3_block2_cbam_F_max_o_fc2 0=64 1=1 5=1 6=1024
BinaryOp                 res_3_block2_cbam_ele_add 2 1 res_3_block2_cbam_F_avg_o_fc2 res_3_block2_cbam_F_max_o_fc2 res_3_block2_cbam_ele_add
TanH                     res_3_block2_cbam_cn_tanh 1 1 res_3_block2_cbam_ele_add res_3_block2_cbam_cn_tanh
BinaryOp                 underscorencnn_25_minusscalar4 1 1 res_3_block2_cbam_cn_tanh underscorencnn_25_minusscalar4 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_block2_cbam_mul1   2 1 res_3_block2_conv_proj_batchnorm_splitncnn_0 underscorencnn_25_minusscalar4 res_3_block2_cbam_mul1 0=2
BinaryOp                 underscorencnn_26_plus2  2 1 res_3_block2_cbam_mul1 underscorencnn_20_plus1_splitncnn_0 underscorencnn_26_plus2
Split                    splitncnn_25             1 2 underscorencnn_26_plus2 underscorencnn_26_plus2_splitncnn_0 underscorencnn_26_plus2_splitncnn_1
Convolution              res_3_block3_conv_sep_conv2d 1 1 underscorencnn_26_plus2_splitncnn_1 res_3_block3_conv_sep_batchnorm 0=128 1=1 5=1 6=8192
Split                    splitncnn_26             1 2 res_3_block3_conv_sep_batchnorm res_3_block3_conv_sep_batchnorm_splitncnn_0 res_3_block3_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_block3_conv_sep_swish 1 1 res_3_block3_conv_sep_batchnorm_splitncnn_1 res_3_block3_conv_sep_swish
BinaryOp                 underscorencnn_27_mul18  2 1 res_3_block3_conv_sep_batchnorm_splitncnn_0 res_3_block3_conv_sep_swish underscorencnn_27_mul18 0=2
ConvolutionDepthWise     res_3_block3_conv_dw_conv2d 1 1 underscorencnn_27_mul18 res_3_block3_conv_dw_batchnorm 0=128 1=3 4=1 5=1 6=1152 7=128
Split                    splitncnn_27             1 2 res_3_block3_conv_dw_batchnorm res_3_block3_conv_dw_batchnorm_splitncnn_0 res_3_block3_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_block3_conv_dw_swish 1 1 res_3_block3_conv_dw_batchnorm_splitncnn_1 res_3_block3_conv_dw_swish
BinaryOp                 underscorencnn_28_mul19  2 1 res_3_block3_conv_dw_batchnorm_splitncnn_0 res_3_block3_conv_dw_swish underscorencnn_28_mul19 0=2
Convolution              res_3_block3_conv_proj_conv2d 1 1 underscorencnn_28_mul19 res_3_block3_conv_proj_batchnorm 0=64 1=1 5=1 6=8192
Split                    splitncnn_28             1 3 res_3_block3_conv_proj_batchnorm res_3_block3_conv_proj_batchnorm_splitncnn_0 res_3_block3_conv_proj_batchnorm_splitncnn_1 res_3_block3_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_block3_cbam_avgpooling 1 1 res_3_block3_conv_proj_batchnorm_splitncnn_2 res_3_block3_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_block3_cbam_F_avg_o_fc1 1 1 res_3_block3_cbam_avgpooling res_3_block3_cbam_F_avg_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_29             1 2 res_3_block3_cbam_F_avg_o_fc1 res_3_block3_cbam_F_avg_o_fc1_splitncnn_0 res_3_block3_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_block3_cbam_F_avg_o_swish 1 1 res_3_block3_cbam_F_avg_o_fc1_splitncnn_1 res_3_block3_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_29_mul20  2 1 res_3_block3_cbam_F_avg_o_fc1_splitncnn_0 res_3_block3_cbam_F_avg_o_swish underscorencnn_29_mul20 0=2
Convolution              res_3_block3_cbam_F_avg_o_fc2 1 1 underscorencnn_29_mul20 res_3_block3_cbam_F_avg_o_fc2 0=64 1=1 5=1 6=1024
Pooling                  res_3_block3_cbam_maxpooling 1 1 res_3_block3_conv_proj_batchnorm_splitncnn_1 res_3_block3_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_block3_cbam_F_max_o_fc1 1 1 res_3_block3_cbam_maxpooling res_3_block3_cbam_F_max_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_30             1 2 res_3_block3_cbam_F_max_o_fc1 res_3_block3_cbam_F_max_o_fc1_splitncnn_0 res_3_block3_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_block3_cbam_F_max_o_swish 1 1 res_3_block3_cbam_F_max_o_fc1_splitncnn_1 res_3_block3_cbam_F_max_o_swish
BinaryOp                 underscorencnn_30_mul21  2 1 res_3_block3_cbam_F_max_o_fc1_splitncnn_0 res_3_block3_cbam_F_max_o_swish underscorencnn_30_mul21 0=2
Convolution              res_3_block3_cbam_F_max_o_fc2 1 1 underscorencnn_30_mul21 res_3_block3_cbam_F_max_o_fc2 0=64 1=1 5=1 6=1024
BinaryOp                 res_3_block3_cbam_ele_add 2 1 res_3_block3_cbam_F_avg_o_fc2 res_3_block3_cbam_F_max_o_fc2 res_3_block3_cbam_ele_add
TanH                     res_3_block3_cbam_cn_tanh 1 1 res_3_block3_cbam_ele_add res_3_block3_cbam_cn_tanh
BinaryOp                 underscorencnn_31_minusscalar5 1 1 res_3_block3_cbam_cn_tanh underscorencnn_31_minusscalar5 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_block3_cbam_mul1   2 1 res_3_block3_conv_proj_batchnorm_splitncnn_0 underscorencnn_31_minusscalar5 res_3_block3_cbam_mul1 0=2
BinaryOp                 underscorencnn_32_plus3  2 1 res_3_block3_cbam_mul1 underscorencnn_26_plus2_splitncnn_0 underscorencnn_32_plus3
Split                    splitncnn_31             1 2 underscorencnn_32_plus3 underscorencnn_32_plus3_splitncnn_0 underscorencnn_32_plus3_splitncnn_1
Convolution              res_3_block4_conv_sep_conv2d 1 1 underscorencnn_32_plus3_splitncnn_1 res_3_block4_conv_sep_batchnorm 0=128 1=1 5=1 6=8192
Split                    splitncnn_32             1 2 res_3_block4_conv_sep_batchnorm res_3_block4_conv_sep_batchnorm_splitncnn_0 res_3_block4_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_block4_conv_sep_swish 1 1 res_3_block4_conv_sep_batchnorm_splitncnn_1 res_3_block4_conv_sep_swish
BinaryOp                 underscorencnn_33_mul22  2 1 res_3_block4_conv_sep_batchnorm_splitncnn_0 res_3_block4_conv_sep_swish underscorencnn_33_mul22 0=2
ConvolutionDepthWise     res_3_block4_conv_dw_conv2d 1 1 underscorencnn_33_mul22 res_3_block4_conv_dw_batchnorm 0=128 1=3 4=1 5=1 6=1152 7=128
Split                    splitncnn_33             1 2 res_3_block4_conv_dw_batchnorm res_3_block4_conv_dw_batchnorm_splitncnn_0 res_3_block4_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_block4_conv_dw_swish 1 1 res_3_block4_conv_dw_batchnorm_splitncnn_1 res_3_block4_conv_dw_swish
BinaryOp                 underscorencnn_34_mul23  2 1 res_3_block4_conv_dw_batchnorm_splitncnn_0 res_3_block4_conv_dw_swish underscorencnn_34_mul23 0=2
Convolution              res_3_block4_conv_proj_conv2d 1 1 underscorencnn_34_mul23 res_3_block4_conv_proj_batchnorm 0=64 1=1 5=1 6=8192
Split                    splitncnn_34             1 3 res_3_block4_conv_proj_batchnorm res_3_block4_conv_proj_batchnorm_splitncnn_0 res_3_block4_conv_proj_batchnorm_splitncnn_1 res_3_block4_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_block4_cbam_avgpooling 1 1 res_3_block4_conv_proj_batchnorm_splitncnn_2 res_3_block4_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_block4_cbam_F_avg_o_fc1 1 1 res_3_block4_cbam_avgpooling res_3_block4_cbam_F_avg_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_35             1 2 res_3_block4_cbam_F_avg_o_fc1 res_3_block4_cbam_F_avg_o_fc1_splitncnn_0 res_3_block4_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_block4_cbam_F_avg_o_swish 1 1 res_3_block4_cbam_F_avg_o_fc1_splitncnn_1 res_3_block4_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_35_mul24  2 1 res_3_block4_cbam_F_avg_o_fc1_splitncnn_0 res_3_block4_cbam_F_avg_o_swish underscorencnn_35_mul24 0=2
Convolution              res_3_block4_cbam_F_avg_o_fc2 1 1 underscorencnn_35_mul24 res_3_block4_cbam_F_avg_o_fc2 0=64 1=1 5=1 6=1024
Pooling                  res_3_block4_cbam_maxpooling 1 1 res_3_block4_conv_proj_batchnorm_splitncnn_1 res_3_block4_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_block4_cbam_F_max_o_fc1 1 1 res_3_block4_cbam_maxpooling res_3_block4_cbam_F_max_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_36             1 2 res_3_block4_cbam_F_max_o_fc1 res_3_block4_cbam_F_max_o_fc1_splitncnn_0 res_3_block4_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_block4_cbam_F_max_o_swish 1 1 res_3_block4_cbam_F_max_o_fc1_splitncnn_1 res_3_block4_cbam_F_max_o_swish
BinaryOp                 underscorencnn_36_mul25  2 1 res_3_block4_cbam_F_max_o_fc1_splitncnn_0 res_3_block4_cbam_F_max_o_swish underscorencnn_36_mul25 0=2
Convolution              res_3_block4_cbam_F_max_o_fc2 1 1 underscorencnn_36_mul25 res_3_block4_cbam_F_max_o_fc2 0=64 1=1 5=1 6=1024
BinaryOp                 res_3_block4_cbam_ele_add 2 1 res_3_block4_cbam_F_avg_o_fc2 res_3_block4_cbam_F_max_o_fc2 res_3_block4_cbam_ele_add
TanH                     res_3_block4_cbam_cn_tanh 1 1 res_3_block4_cbam_ele_add res_3_block4_cbam_cn_tanh
BinaryOp                 underscorencnn_37_minusscalar6 1 1 res_3_block4_cbam_cn_tanh underscorencnn_37_minusscalar6 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_block4_cbam_mul1   2 1 res_3_block4_conv_proj_batchnorm_splitncnn_0 underscorencnn_37_minusscalar6 res_3_block4_cbam_mul1 0=2
BinaryOp                 underscorencnn_38_plus4  2 1 res_3_block4_cbam_mul1 underscorencnn_32_plus3_splitncnn_0 underscorencnn_38_plus4
Split                    splitncnn_37             1 2 underscorencnn_38_plus4 underscorencnn_38_plus4_splitncnn_0 underscorencnn_38_plus4_splitncnn_1
Convolution              res_3_block5_conv_sep_conv2d 1 1 underscorencnn_38_plus4_splitncnn_1 res_3_block5_conv_sep_batchnorm 0=128 1=1 5=1 6=8192
Split                    splitncnn_38             1 2 res_3_block5_conv_sep_batchnorm res_3_block5_conv_sep_batchnorm_splitncnn_0 res_3_block5_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_block5_conv_sep_swish 1 1 res_3_block5_conv_sep_batchnorm_splitncnn_1 res_3_block5_conv_sep_swish
BinaryOp                 underscorencnn_39_mul26  2 1 res_3_block5_conv_sep_batchnorm_splitncnn_0 res_3_block5_conv_sep_swish underscorencnn_39_mul26 0=2
ConvolutionDepthWise     res_3_block5_conv_dw_conv2d 1 1 underscorencnn_39_mul26 res_3_block5_conv_dw_batchnorm 0=128 1=3 4=1 5=1 6=1152 7=128
Split                    splitncnn_39             1 2 res_3_block5_conv_dw_batchnorm res_3_block5_conv_dw_batchnorm_splitncnn_0 res_3_block5_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_block5_conv_dw_swish 1 1 res_3_block5_conv_dw_batchnorm_splitncnn_1 res_3_block5_conv_dw_swish
BinaryOp                 underscorencnn_40_mul27  2 1 res_3_block5_conv_dw_batchnorm_splitncnn_0 res_3_block5_conv_dw_swish underscorencnn_40_mul27 0=2
Convolution              res_3_block5_conv_proj_conv2d 1 1 underscorencnn_40_mul27 res_3_block5_conv_proj_batchnorm 0=64 1=1 5=1 6=8192
Split                    splitncnn_40             1 3 res_3_block5_conv_proj_batchnorm res_3_block5_conv_proj_batchnorm_splitncnn_0 res_3_block5_conv_proj_batchnorm_splitncnn_1 res_3_block5_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_block5_cbam_avgpooling 1 1 res_3_block5_conv_proj_batchnorm_splitncnn_2 res_3_block5_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_block5_cbam_F_avg_o_fc1 1 1 res_3_block5_cbam_avgpooling res_3_block5_cbam_F_avg_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_41             1 2 res_3_block5_cbam_F_avg_o_fc1 res_3_block5_cbam_F_avg_o_fc1_splitncnn_0 res_3_block5_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_block5_cbam_F_avg_o_swish 1 1 res_3_block5_cbam_F_avg_o_fc1_splitncnn_1 res_3_block5_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_41_mul28  2 1 res_3_block5_cbam_F_avg_o_fc1_splitncnn_0 res_3_block5_cbam_F_avg_o_swish underscorencnn_41_mul28 0=2
Convolution              res_3_block5_cbam_F_avg_o_fc2 1 1 underscorencnn_41_mul28 res_3_block5_cbam_F_avg_o_fc2 0=64 1=1 5=1 6=1024
Pooling                  res_3_block5_cbam_maxpooling 1 1 res_3_block5_conv_proj_batchnorm_splitncnn_1 res_3_block5_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_block5_cbam_F_max_o_fc1 1 1 res_3_block5_cbam_maxpooling res_3_block5_cbam_F_max_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_42             1 2 res_3_block5_cbam_F_max_o_fc1 res_3_block5_cbam_F_max_o_fc1_splitncnn_0 res_3_block5_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_block5_cbam_F_max_o_swish 1 1 res_3_block5_cbam_F_max_o_fc1_splitncnn_1 res_3_block5_cbam_F_max_o_swish
BinaryOp                 underscorencnn_42_mul29  2 1 res_3_block5_cbam_F_max_o_fc1_splitncnn_0 res_3_block5_cbam_F_max_o_swish underscorencnn_42_mul29 0=2
Convolution              res_3_block5_cbam_F_max_o_fc2 1 1 underscorencnn_42_mul29 res_3_block5_cbam_F_max_o_fc2 0=64 1=1 5=1 6=1024
BinaryOp                 res_3_block5_cbam_ele_add 2 1 res_3_block5_cbam_F_avg_o_fc2 res_3_block5_cbam_F_max_o_fc2 res_3_block5_cbam_ele_add
TanH                     res_3_block5_cbam_cn_tanh 1 1 res_3_block5_cbam_ele_add res_3_block5_cbam_cn_tanh
BinaryOp                 underscorencnn_43_minusscalar7 1 1 res_3_block5_cbam_cn_tanh underscorencnn_43_minusscalar7 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_block5_cbam_mul1   2 1 res_3_block5_conv_proj_batchnorm_splitncnn_0 underscorencnn_43_minusscalar7 res_3_block5_cbam_mul1 0=2
BinaryOp                 underscorencnn_44_plus5  2 1 res_3_block5_cbam_mul1 underscorencnn_38_plus4_splitncnn_0 underscorencnn_44_plus5
Split                    splitncnn_43             1 2 underscorencnn_44_plus5 underscorencnn_44_plus5_splitncnn_0 underscorencnn_44_plus5_splitncnn_1
Convolution              res_3_block6_conv_sep_conv2d 1 1 underscorencnn_44_plus5_splitncnn_1 res_3_block6_conv_sep_batchnorm 0=128 1=1 5=1 6=8192
Split                    splitncnn_44             1 2 res_3_block6_conv_sep_batchnorm res_3_block6_conv_sep_batchnorm_splitncnn_0 res_3_block6_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_block6_conv_sep_swish 1 1 res_3_block6_conv_sep_batchnorm_splitncnn_1 res_3_block6_conv_sep_swish
BinaryOp                 underscorencnn_45_mul30  2 1 res_3_block6_conv_sep_batchnorm_splitncnn_0 res_3_block6_conv_sep_swish underscorencnn_45_mul30 0=2
ConvolutionDepthWise     res_3_block6_conv_dw_conv2d 1 1 underscorencnn_45_mul30 res_3_block6_conv_dw_batchnorm 0=128 1=3 4=1 5=1 6=1152 7=128
Split                    splitncnn_45             1 2 res_3_block6_conv_dw_batchnorm res_3_block6_conv_dw_batchnorm_splitncnn_0 res_3_block6_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_block6_conv_dw_swish 1 1 res_3_block6_conv_dw_batchnorm_splitncnn_1 res_3_block6_conv_dw_swish
BinaryOp                 underscorencnn_46_mul31  2 1 res_3_block6_conv_dw_batchnorm_splitncnn_0 res_3_block6_conv_dw_swish underscorencnn_46_mul31 0=2
Convolution              res_3_block6_conv_proj_conv2d 1 1 underscorencnn_46_mul31 res_3_block6_conv_proj_batchnorm 0=64 1=1 5=1 6=8192
Split                    splitncnn_46             1 3 res_3_block6_conv_proj_batchnorm res_3_block6_conv_proj_batchnorm_splitncnn_0 res_3_block6_conv_proj_batchnorm_splitncnn_1 res_3_block6_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_block6_cbam_avgpooling 1 1 res_3_block6_conv_proj_batchnorm_splitncnn_2 res_3_block6_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_block6_cbam_F_avg_o_fc1 1 1 res_3_block6_cbam_avgpooling res_3_block6_cbam_F_avg_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_47             1 2 res_3_block6_cbam_F_avg_o_fc1 res_3_block6_cbam_F_avg_o_fc1_splitncnn_0 res_3_block6_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_block6_cbam_F_avg_o_swish 1 1 res_3_block6_cbam_F_avg_o_fc1_splitncnn_1 res_3_block6_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_47_mul32  2 1 res_3_block6_cbam_F_avg_o_fc1_splitncnn_0 res_3_block6_cbam_F_avg_o_swish underscorencnn_47_mul32 0=2
Convolution              res_3_block6_cbam_F_avg_o_fc2 1 1 underscorencnn_47_mul32 res_3_block6_cbam_F_avg_o_fc2 0=64 1=1 5=1 6=1024
Pooling                  res_3_block6_cbam_maxpooling 1 1 res_3_block6_conv_proj_batchnorm_splitncnn_1 res_3_block6_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_block6_cbam_F_max_o_fc1 1 1 res_3_block6_cbam_maxpooling res_3_block6_cbam_F_max_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_48             1 2 res_3_block6_cbam_F_max_o_fc1 res_3_block6_cbam_F_max_o_fc1_splitncnn_0 res_3_block6_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_block6_cbam_F_max_o_swish 1 1 res_3_block6_cbam_F_max_o_fc1_splitncnn_1 res_3_block6_cbam_F_max_o_swish
BinaryOp                 underscorencnn_48_mul33  2 1 res_3_block6_cbam_F_max_o_fc1_splitncnn_0 res_3_block6_cbam_F_max_o_swish underscorencnn_48_mul33 0=2
Convolution              res_3_block6_cbam_F_max_o_fc2 1 1 underscorencnn_48_mul33 res_3_block6_cbam_F_max_o_fc2 0=64 1=1 5=1 6=1024
BinaryOp                 res_3_block6_cbam_ele_add 2 1 res_3_block6_cbam_F_avg_o_fc2 res_3_block6_cbam_F_max_o_fc2 res_3_block6_cbam_ele_add
TanH                     res_3_block6_cbam_cn_tanh 1 1 res_3_block6_cbam_ele_add res_3_block6_cbam_cn_tanh
BinaryOp                 underscorencnn_49_minusscalar8 1 1 res_3_block6_cbam_cn_tanh underscorencnn_49_minusscalar8 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_block6_cbam_mul1   2 1 res_3_block6_conv_proj_batchnorm_splitncnn_0 underscorencnn_49_minusscalar8 res_3_block6_cbam_mul1 0=2
BinaryOp                 underscorencnn_50_plus6  2 1 res_3_block6_cbam_mul1 underscorencnn_44_plus5_splitncnn_0 underscorencnn_50_plus6
Split                    splitncnn_49             1 2 underscorencnn_50_plus6 underscorencnn_50_plus6_splitncnn_0 underscorencnn_50_plus6_splitncnn_1
Convolution              res_3_block7_conv_sep_conv2d 1 1 underscorencnn_50_plus6_splitncnn_1 res_3_block7_conv_sep_batchnorm 0=128 1=1 5=1 6=8192
Split                    splitncnn_50             1 2 res_3_block7_conv_sep_batchnorm res_3_block7_conv_sep_batchnorm_splitncnn_0 res_3_block7_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_block7_conv_sep_swish 1 1 res_3_block7_conv_sep_batchnorm_splitncnn_1 res_3_block7_conv_sep_swish
BinaryOp                 underscorencnn_51_mul34  2 1 res_3_block7_conv_sep_batchnorm_splitncnn_0 res_3_block7_conv_sep_swish underscorencnn_51_mul34 0=2
ConvolutionDepthWise     res_3_block7_conv_dw_conv2d 1 1 underscorencnn_51_mul34 res_3_block7_conv_dw_batchnorm 0=128 1=3 4=1 5=1 6=1152 7=128
Split                    splitncnn_51             1 2 res_3_block7_conv_dw_batchnorm res_3_block7_conv_dw_batchnorm_splitncnn_0 res_3_block7_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_block7_conv_dw_swish 1 1 res_3_block7_conv_dw_batchnorm_splitncnn_1 res_3_block7_conv_dw_swish
BinaryOp                 underscorencnn_52_mul35  2 1 res_3_block7_conv_dw_batchnorm_splitncnn_0 res_3_block7_conv_dw_swish underscorencnn_52_mul35 0=2
Convolution              res_3_block7_conv_proj_conv2d 1 1 underscorencnn_52_mul35 res_3_block7_conv_proj_batchnorm 0=64 1=1 5=1 6=8192
Split                    splitncnn_52             1 3 res_3_block7_conv_proj_batchnorm res_3_block7_conv_proj_batchnorm_splitncnn_0 res_3_block7_conv_proj_batchnorm_splitncnn_1 res_3_block7_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_block7_cbam_avgpooling 1 1 res_3_block7_conv_proj_batchnorm_splitncnn_2 res_3_block7_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_block7_cbam_F_avg_o_fc1 1 1 res_3_block7_cbam_avgpooling res_3_block7_cbam_F_avg_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_53             1 2 res_3_block7_cbam_F_avg_o_fc1 res_3_block7_cbam_F_avg_o_fc1_splitncnn_0 res_3_block7_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_block7_cbam_F_avg_o_swish 1 1 res_3_block7_cbam_F_avg_o_fc1_splitncnn_1 res_3_block7_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_53_mul36  2 1 res_3_block7_cbam_F_avg_o_fc1_splitncnn_0 res_3_block7_cbam_F_avg_o_swish underscorencnn_53_mul36 0=2
Convolution              res_3_block7_cbam_F_avg_o_fc2 1 1 underscorencnn_53_mul36 res_3_block7_cbam_F_avg_o_fc2 0=64 1=1 5=1 6=1024
Pooling                  res_3_block7_cbam_maxpooling 1 1 res_3_block7_conv_proj_batchnorm_splitncnn_1 res_3_block7_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_block7_cbam_F_max_o_fc1 1 1 res_3_block7_cbam_maxpooling res_3_block7_cbam_F_max_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_54             1 2 res_3_block7_cbam_F_max_o_fc1 res_3_block7_cbam_F_max_o_fc1_splitncnn_0 res_3_block7_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_block7_cbam_F_max_o_swish 1 1 res_3_block7_cbam_F_max_o_fc1_splitncnn_1 res_3_block7_cbam_F_max_o_swish
BinaryOp                 underscorencnn_54_mul37  2 1 res_3_block7_cbam_F_max_o_fc1_splitncnn_0 res_3_block7_cbam_F_max_o_swish underscorencnn_54_mul37 0=2
Convolution              res_3_block7_cbam_F_max_o_fc2 1 1 underscorencnn_54_mul37 res_3_block7_cbam_F_max_o_fc2 0=64 1=1 5=1 6=1024
BinaryOp                 res_3_block7_cbam_ele_add 2 1 res_3_block7_cbam_F_avg_o_fc2 res_3_block7_cbam_F_max_o_fc2 res_3_block7_cbam_ele_add
TanH                     res_3_block7_cbam_cn_tanh 1 1 res_3_block7_cbam_ele_add res_3_block7_cbam_cn_tanh
BinaryOp                 underscorencnn_55_minusscalar9 1 1 res_3_block7_cbam_cn_tanh underscorencnn_55_minusscalar9 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_block7_cbam_mul1   2 1 res_3_block7_conv_proj_batchnorm_splitncnn_0 underscorencnn_55_minusscalar9 res_3_block7_cbam_mul1 0=2
BinaryOp                 underscorencnn_56_plus7  2 1 res_3_block7_cbam_mul1 underscorencnn_50_plus6_splitncnn_0 underscorencnn_56_plus7
Split                    splitncnn_55             1 2 underscorencnn_56_plus7 underscorencnn_56_plus7_splitncnn_0 underscorencnn_56_plus7_splitncnn_1
Convolution              res_3_block8_conv_sep_conv2d 1 1 underscorencnn_56_plus7_splitncnn_1 res_3_block8_conv_sep_batchnorm 0=128 1=1 5=1 6=8192
Split                    splitncnn_56             1 2 res_3_block8_conv_sep_batchnorm res_3_block8_conv_sep_batchnorm_splitncnn_0 res_3_block8_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_block8_conv_sep_swish 1 1 res_3_block8_conv_sep_batchnorm_splitncnn_1 res_3_block8_conv_sep_swish
BinaryOp                 underscorencnn_57_mul38  2 1 res_3_block8_conv_sep_batchnorm_splitncnn_0 res_3_block8_conv_sep_swish underscorencnn_57_mul38 0=2
ConvolutionDepthWise     res_3_block8_conv_dw_conv2d 1 1 underscorencnn_57_mul38 res_3_block8_conv_dw_batchnorm 0=128 1=3 4=1 5=1 6=1152 7=128
Split                    splitncnn_57             1 2 res_3_block8_conv_dw_batchnorm res_3_block8_conv_dw_batchnorm_splitncnn_0 res_3_block8_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_block8_conv_dw_swish 1 1 res_3_block8_conv_dw_batchnorm_splitncnn_1 res_3_block8_conv_dw_swish
BinaryOp                 underscorencnn_58_mul39  2 1 res_3_block8_conv_dw_batchnorm_splitncnn_0 res_3_block8_conv_dw_swish underscorencnn_58_mul39 0=2
Convolution              res_3_block8_conv_proj_conv2d 1 1 underscorencnn_58_mul39 res_3_block8_conv_proj_batchnorm 0=64 1=1 5=1 6=8192
Split                    splitncnn_58             1 3 res_3_block8_conv_proj_batchnorm res_3_block8_conv_proj_batchnorm_splitncnn_0 res_3_block8_conv_proj_batchnorm_splitncnn_1 res_3_block8_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_block8_cbam_avgpooling 1 1 res_3_block8_conv_proj_batchnorm_splitncnn_2 res_3_block8_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_block8_cbam_F_avg_o_fc1 1 1 res_3_block8_cbam_avgpooling res_3_block8_cbam_F_avg_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_59             1 2 res_3_block8_cbam_F_avg_o_fc1 res_3_block8_cbam_F_avg_o_fc1_splitncnn_0 res_3_block8_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_block8_cbam_F_avg_o_swish 1 1 res_3_block8_cbam_F_avg_o_fc1_splitncnn_1 res_3_block8_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_59_mul40  2 1 res_3_block8_cbam_F_avg_o_fc1_splitncnn_0 res_3_block8_cbam_F_avg_o_swish underscorencnn_59_mul40 0=2
Convolution              res_3_block8_cbam_F_avg_o_fc2 1 1 underscorencnn_59_mul40 res_3_block8_cbam_F_avg_o_fc2 0=64 1=1 5=1 6=1024
Pooling                  res_3_block8_cbam_maxpooling 1 1 res_3_block8_conv_proj_batchnorm_splitncnn_1 res_3_block8_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_block8_cbam_F_max_o_fc1 1 1 res_3_block8_cbam_maxpooling res_3_block8_cbam_F_max_o_fc1 0=16 1=1 2=1024
Split                    splitncnn_60             1 2 res_3_block8_cbam_F_max_o_fc1 res_3_block8_cbam_F_max_o_fc1_splitncnn_0 res_3_block8_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_block8_cbam_F_max_o_swish 1 1 res_3_block8_cbam_F_max_o_fc1_splitncnn_1 res_3_block8_cbam_F_max_o_swish
BinaryOp                 underscorencnn_60_mul41  2 1 res_3_block8_cbam_F_max_o_fc1_splitncnn_0 res_3_block8_cbam_F_max_o_swish underscorencnn_60_mul41 0=2
Convolution              res_3_block8_cbam_F_max_o_fc2 1 1 underscorencnn_60_mul41 res_3_block8_cbam_F_max_o_fc2 0=64 1=1 5=1 6=1024
BinaryOp                 res_3_block8_cbam_ele_add 2 1 res_3_block8_cbam_F_avg_o_fc2 res_3_block8_cbam_F_max_o_fc2 res_3_block8_cbam_ele_add
TanH                     res_3_block8_cbam_cn_tanh 1 1 res_3_block8_cbam_ele_add res_3_block8_cbam_cn_tanh
BinaryOp                 underscorencnn_61_minusscalar10 1 1 res_3_block8_cbam_cn_tanh underscorencnn_61_minusscalar10 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_block8_cbam_mul1   2 1 res_3_block8_conv_proj_batchnorm_splitncnn_0 underscorencnn_61_minusscalar10 res_3_block8_cbam_mul1 0=2
BinaryOp                 underscorencnn_62_plus8  2 1 res_3_block8_cbam_mul1 underscorencnn_56_plus7_splitncnn_0 underscorencnn_62_plus8
Convolution              dconv_34_conv_sep_conv2d 1 1 underscorencnn_62_plus8 dconv_34_conv_sep_batchnorm 0=256 1=1 5=1 6=16384
Split                    splitncnn_61             1 2 dconv_34_conv_sep_batchnorm dconv_34_conv_sep_batchnorm_splitncnn_0 dconv_34_conv_sep_batchnorm_splitncnn_1
Sigmoid                  dconv_34_conv_sep_swish  1 1 dconv_34_conv_sep_batchnorm_splitncnn_1 dconv_34_conv_sep_swish
BinaryOp                 underscorencnn_63_mul42  2 1 dconv_34_conv_sep_batchnorm_splitncnn_0 dconv_34_conv_sep_swish underscorencnn_63_mul42 0=2
ConvolutionDepthWise     dconv_34_conv_dw_conv2d  1 1 underscorencnn_63_mul42 dconv_34_conv_dw_batchnorm 0=256 1=3 3=2 4=1 5=1 6=2304 7=256
Split                    splitncnn_62             1 2 dconv_34_conv_dw_batchnorm dconv_34_conv_dw_batchnorm_splitncnn_0 dconv_34_conv_dw_batchnorm_splitncnn_1
Sigmoid                  dconv_34_conv_dw_swish   1 1 dconv_34_conv_dw_batchnorm_splitncnn_1 dconv_34_conv_dw_swish
BinaryOp                 underscorencnn_64_mul43  2 1 dconv_34_conv_dw_batchnorm_splitncnn_0 dconv_34_conv_dw_swish underscorencnn_64_mul43 0=2
Convolution              dconv_34_conv_proj_conv2d 1 1 underscorencnn_64_mul43 dconv_34_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_63             1 3 dconv_34_conv_proj_batchnorm dconv_34_conv_proj_batchnorm_splitncnn_0 dconv_34_conv_proj_batchnorm_splitncnn_1 dconv_34_conv_proj_batchnorm_splitncnn_2
Pooling                  dconv_34_cbam_avgpooling 1 1 dconv_34_conv_proj_batchnorm_splitncnn_2 dconv_34_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             dconv_34_cbam_F_avg_o_fc1 1 1 dconv_34_cbam_avgpooling dconv_34_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_64             1 2 dconv_34_cbam_F_avg_o_fc1 dconv_34_cbam_F_avg_o_fc1_splitncnn_0 dconv_34_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  dconv_34_cbam_F_avg_o_swish 1 1 dconv_34_cbam_F_avg_o_fc1_splitncnn_1 dconv_34_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_65_mul44  2 1 dconv_34_cbam_F_avg_o_fc1_splitncnn_0 dconv_34_cbam_F_avg_o_swish underscorencnn_65_mul44 0=2
Convolution              dconv_34_cbam_F_avg_o_fc2 1 1 underscorencnn_65_mul44 dconv_34_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  dconv_34_cbam_maxpooling 1 1 dconv_34_conv_proj_batchnorm_splitncnn_1 dconv_34_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             dconv_34_cbam_F_max_o_fc1 1 1 dconv_34_cbam_maxpooling dconv_34_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_65             1 2 dconv_34_cbam_F_max_o_fc1 dconv_34_cbam_F_max_o_fc1_splitncnn_0 dconv_34_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  dconv_34_cbam_F_max_o_swish 1 1 dconv_34_cbam_F_max_o_fc1_splitncnn_1 dconv_34_cbam_F_max_o_swish
BinaryOp                 underscorencnn_66_mul45  2 1 dconv_34_cbam_F_max_o_fc1_splitncnn_0 dconv_34_cbam_F_max_o_swish underscorencnn_66_mul45 0=2
Convolution              dconv_34_cbam_F_max_o_fc2 1 1 underscorencnn_66_mul45 dconv_34_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 dconv_34_cbam_ele_add    2 1 dconv_34_cbam_F_avg_o_fc2 dconv_34_cbam_F_max_o_fc2 dconv_34_cbam_ele_add
TanH                     dconv_34_cbam_cn_tanh    1 1 dconv_34_cbam_ele_add dconv_34_cbam_cn_tanh
BinaryOp                 underscorencnn_67_minusscalar11 1 1 dconv_34_cbam_cn_tanh underscorencnn_67_minusscalar11 0=1 1=1 2=-1.000000e+00
BinaryOp                 dconv_34_cbam_mul1       2 1 dconv_34_conv_proj_batchnorm_splitncnn_0 underscorencnn_67_minusscalar11 dconv_34_cbam_mul1 0=2
Split                    splitncnn_66             1 2 dconv_34_cbam_mul1 dconv_34_cbam_mul1_splitncnn_0 dconv_34_cbam_mul1_splitncnn_1
Convolution              res_3_4_block0_conv_sep_conv2d 1 1 dconv_34_cbam_mul1_splitncnn_1 res_3_4_block0_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_67             1 2 res_3_4_block0_conv_sep_batchnorm res_3_4_block0_conv_sep_batchnorm_splitncnn_0 res_3_4_block0_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block0_conv_sep_swish 1 1 res_3_4_block0_conv_sep_batchnorm_splitncnn_1 res_3_4_block0_conv_sep_swish
BinaryOp                 underscorencnn_68_mul46  2 1 res_3_4_block0_conv_sep_batchnorm_splitncnn_0 res_3_4_block0_conv_sep_swish underscorencnn_68_mul46 0=2
ConvolutionDepthWise     res_3_4_block0_conv_dw_conv2d 1 1 underscorencnn_68_mul46 res_3_4_block0_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_68             1 2 res_3_4_block0_conv_dw_batchnorm res_3_4_block0_conv_dw_batchnorm_splitncnn_0 res_3_4_block0_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block0_conv_dw_swish 1 1 res_3_4_block0_conv_dw_batchnorm_splitncnn_1 res_3_4_block0_conv_dw_swish
BinaryOp                 underscorencnn_69_mul47  2 1 res_3_4_block0_conv_dw_batchnorm_splitncnn_0 res_3_4_block0_conv_dw_swish underscorencnn_69_mul47 0=2
Convolution              res_3_4_block0_conv_proj_conv2d 1 1 underscorencnn_69_mul47 res_3_4_block0_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_69             1 3 res_3_4_block0_conv_proj_batchnorm res_3_4_block0_conv_proj_batchnorm_splitncnn_0 res_3_4_block0_conv_proj_batchnorm_splitncnn_1 res_3_4_block0_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block0_cbam_avgpooling 1 1 res_3_4_block0_conv_proj_batchnorm_splitncnn_2 res_3_4_block0_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block0_cbam_F_avg_o_fc1 1 1 res_3_4_block0_cbam_avgpooling res_3_4_block0_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_70             1 2 res_3_4_block0_cbam_F_avg_o_fc1 res_3_4_block0_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block0_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block0_cbam_F_avg_o_swish 1 1 res_3_4_block0_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block0_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_70_mul48  2 1 res_3_4_block0_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block0_cbam_F_avg_o_swish underscorencnn_70_mul48 0=2
Convolution              res_3_4_block0_cbam_F_avg_o_fc2 1 1 underscorencnn_70_mul48 res_3_4_block0_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block0_cbam_maxpooling 1 1 res_3_4_block0_conv_proj_batchnorm_splitncnn_1 res_3_4_block0_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block0_cbam_F_max_o_fc1 1 1 res_3_4_block0_cbam_maxpooling res_3_4_block0_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_71             1 2 res_3_4_block0_cbam_F_max_o_fc1 res_3_4_block0_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block0_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block0_cbam_F_max_o_swish 1 1 res_3_4_block0_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block0_cbam_F_max_o_swish
BinaryOp                 underscorencnn_71_mul49  2 1 res_3_4_block0_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block0_cbam_F_max_o_swish underscorencnn_71_mul49 0=2
Convolution              res_3_4_block0_cbam_F_max_o_fc2 1 1 underscorencnn_71_mul49 res_3_4_block0_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block0_cbam_ele_add 2 1 res_3_4_block0_cbam_F_avg_o_fc2 res_3_4_block0_cbam_F_max_o_fc2 res_3_4_block0_cbam_ele_add
TanH                     res_3_4_block0_cbam_cn_tanh 1 1 res_3_4_block0_cbam_ele_add res_3_4_block0_cbam_cn_tanh
BinaryOp                 underscorencnn_72_minusscalar12 1 1 res_3_4_block0_cbam_cn_tanh underscorencnn_72_minusscalar12 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block0_cbam_mul1 2 1 res_3_4_block0_conv_proj_batchnorm_splitncnn_0 underscorencnn_72_minusscalar12 res_3_4_block0_cbam_mul1 0=2
BinaryOp                 underscorencnn_73_plus9  2 1 res_3_4_block0_cbam_mul1 dconv_34_cbam_mul1_splitncnn_0 underscorencnn_73_plus9
Split                    splitncnn_72             1 2 underscorencnn_73_plus9 underscorencnn_73_plus9_splitncnn_0 underscorencnn_73_plus9_splitncnn_1
Convolution              res_3_4_block1_conv_sep_conv2d 1 1 underscorencnn_73_plus9_splitncnn_1 res_3_4_block1_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_73             1 2 res_3_4_block1_conv_sep_batchnorm res_3_4_block1_conv_sep_batchnorm_splitncnn_0 res_3_4_block1_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block1_conv_sep_swish 1 1 res_3_4_block1_conv_sep_batchnorm_splitncnn_1 res_3_4_block1_conv_sep_swish
BinaryOp                 underscorencnn_74_mul50  2 1 res_3_4_block1_conv_sep_batchnorm_splitncnn_0 res_3_4_block1_conv_sep_swish underscorencnn_74_mul50 0=2
ConvolutionDepthWise     res_3_4_block1_conv_dw_conv2d 1 1 underscorencnn_74_mul50 res_3_4_block1_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_74             1 2 res_3_4_block1_conv_dw_batchnorm res_3_4_block1_conv_dw_batchnorm_splitncnn_0 res_3_4_block1_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block1_conv_dw_swish 1 1 res_3_4_block1_conv_dw_batchnorm_splitncnn_1 res_3_4_block1_conv_dw_swish
BinaryOp                 underscorencnn_75_mul51  2 1 res_3_4_block1_conv_dw_batchnorm_splitncnn_0 res_3_4_block1_conv_dw_swish underscorencnn_75_mul51 0=2
Convolution              res_3_4_block1_conv_proj_conv2d 1 1 underscorencnn_75_mul51 res_3_4_block1_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_75             1 3 res_3_4_block1_conv_proj_batchnorm res_3_4_block1_conv_proj_batchnorm_splitncnn_0 res_3_4_block1_conv_proj_batchnorm_splitncnn_1 res_3_4_block1_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block1_cbam_avgpooling 1 1 res_3_4_block1_conv_proj_batchnorm_splitncnn_2 res_3_4_block1_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block1_cbam_F_avg_o_fc1 1 1 res_3_4_block1_cbam_avgpooling res_3_4_block1_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_76             1 2 res_3_4_block1_cbam_F_avg_o_fc1 res_3_4_block1_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block1_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block1_cbam_F_avg_o_swish 1 1 res_3_4_block1_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block1_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_76_mul52  2 1 res_3_4_block1_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block1_cbam_F_avg_o_swish underscorencnn_76_mul52 0=2
Convolution              res_3_4_block1_cbam_F_avg_o_fc2 1 1 underscorencnn_76_mul52 res_3_4_block1_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block1_cbam_maxpooling 1 1 res_3_4_block1_conv_proj_batchnorm_splitncnn_1 res_3_4_block1_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block1_cbam_F_max_o_fc1 1 1 res_3_4_block1_cbam_maxpooling res_3_4_block1_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_77             1 2 res_3_4_block1_cbam_F_max_o_fc1 res_3_4_block1_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block1_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block1_cbam_F_max_o_swish 1 1 res_3_4_block1_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block1_cbam_F_max_o_swish
BinaryOp                 underscorencnn_77_mul53  2 1 res_3_4_block1_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block1_cbam_F_max_o_swish underscorencnn_77_mul53 0=2
Convolution              res_3_4_block1_cbam_F_max_o_fc2 1 1 underscorencnn_77_mul53 res_3_4_block1_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block1_cbam_ele_add 2 1 res_3_4_block1_cbam_F_avg_o_fc2 res_3_4_block1_cbam_F_max_o_fc2 res_3_4_block1_cbam_ele_add
TanH                     res_3_4_block1_cbam_cn_tanh 1 1 res_3_4_block1_cbam_ele_add res_3_4_block1_cbam_cn_tanh
BinaryOp                 underscorencnn_78_minusscalar13 1 1 res_3_4_block1_cbam_cn_tanh underscorencnn_78_minusscalar13 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block1_cbam_mul1 2 1 res_3_4_block1_conv_proj_batchnorm_splitncnn_0 underscorencnn_78_minusscalar13 res_3_4_block1_cbam_mul1 0=2
BinaryOp                 underscorencnn_79_plus10 2 1 res_3_4_block1_cbam_mul1 underscorencnn_73_plus9_splitncnn_0 underscorencnn_79_plus10
Split                    splitncnn_78             1 2 underscorencnn_79_plus10 underscorencnn_79_plus10_splitncnn_0 underscorencnn_79_plus10_splitncnn_1
Convolution              res_3_4_block2_conv_sep_conv2d 1 1 underscorencnn_79_plus10_splitncnn_1 res_3_4_block2_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_79             1 2 res_3_4_block2_conv_sep_batchnorm res_3_4_block2_conv_sep_batchnorm_splitncnn_0 res_3_4_block2_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block2_conv_sep_swish 1 1 res_3_4_block2_conv_sep_batchnorm_splitncnn_1 res_3_4_block2_conv_sep_swish
BinaryOp                 underscorencnn_80_mul54  2 1 res_3_4_block2_conv_sep_batchnorm_splitncnn_0 res_3_4_block2_conv_sep_swish underscorencnn_80_mul54 0=2
ConvolutionDepthWise     res_3_4_block2_conv_dw_conv2d 1 1 underscorencnn_80_mul54 res_3_4_block2_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_80             1 2 res_3_4_block2_conv_dw_batchnorm res_3_4_block2_conv_dw_batchnorm_splitncnn_0 res_3_4_block2_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block2_conv_dw_swish 1 1 res_3_4_block2_conv_dw_batchnorm_splitncnn_1 res_3_4_block2_conv_dw_swish
BinaryOp                 underscorencnn_81_mul55  2 1 res_3_4_block2_conv_dw_batchnorm_splitncnn_0 res_3_4_block2_conv_dw_swish underscorencnn_81_mul55 0=2
Convolution              res_3_4_block2_conv_proj_conv2d 1 1 underscorencnn_81_mul55 res_3_4_block2_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_81             1 3 res_3_4_block2_conv_proj_batchnorm res_3_4_block2_conv_proj_batchnorm_splitncnn_0 res_3_4_block2_conv_proj_batchnorm_splitncnn_1 res_3_4_block2_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block2_cbam_avgpooling 1 1 res_3_4_block2_conv_proj_batchnorm_splitncnn_2 res_3_4_block2_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block2_cbam_F_avg_o_fc1 1 1 res_3_4_block2_cbam_avgpooling res_3_4_block2_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_82             1 2 res_3_4_block2_cbam_F_avg_o_fc1 res_3_4_block2_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block2_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block2_cbam_F_avg_o_swish 1 1 res_3_4_block2_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block2_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_82_mul56  2 1 res_3_4_block2_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block2_cbam_F_avg_o_swish underscorencnn_82_mul56 0=2
Convolution              res_3_4_block2_cbam_F_avg_o_fc2 1 1 underscorencnn_82_mul56 res_3_4_block2_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block2_cbam_maxpooling 1 1 res_3_4_block2_conv_proj_batchnorm_splitncnn_1 res_3_4_block2_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block2_cbam_F_max_o_fc1 1 1 res_3_4_block2_cbam_maxpooling res_3_4_block2_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_83             1 2 res_3_4_block2_cbam_F_max_o_fc1 res_3_4_block2_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block2_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block2_cbam_F_max_o_swish 1 1 res_3_4_block2_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block2_cbam_F_max_o_swish
BinaryOp                 underscorencnn_83_mul57  2 1 res_3_4_block2_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block2_cbam_F_max_o_swish underscorencnn_83_mul57 0=2
Convolution              res_3_4_block2_cbam_F_max_o_fc2 1 1 underscorencnn_83_mul57 res_3_4_block2_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block2_cbam_ele_add 2 1 res_3_4_block2_cbam_F_avg_o_fc2 res_3_4_block2_cbam_F_max_o_fc2 res_3_4_block2_cbam_ele_add
TanH                     res_3_4_block2_cbam_cn_tanh 1 1 res_3_4_block2_cbam_ele_add res_3_4_block2_cbam_cn_tanh
BinaryOp                 underscorencnn_84_minusscalar14 1 1 res_3_4_block2_cbam_cn_tanh underscorencnn_84_minusscalar14 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block2_cbam_mul1 2 1 res_3_4_block2_conv_proj_batchnorm_splitncnn_0 underscorencnn_84_minusscalar14 res_3_4_block2_cbam_mul1 0=2
BinaryOp                 underscorencnn_85_plus11 2 1 res_3_4_block2_cbam_mul1 underscorencnn_79_plus10_splitncnn_0 underscorencnn_85_plus11
Split                    splitncnn_84             1 2 underscorencnn_85_plus11 underscorencnn_85_plus11_splitncnn_0 underscorencnn_85_plus11_splitncnn_1
Convolution              res_3_4_block3_conv_sep_conv2d 1 1 underscorencnn_85_plus11_splitncnn_1 res_3_4_block3_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_85             1 2 res_3_4_block3_conv_sep_batchnorm res_3_4_block3_conv_sep_batchnorm_splitncnn_0 res_3_4_block3_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block3_conv_sep_swish 1 1 res_3_4_block3_conv_sep_batchnorm_splitncnn_1 res_3_4_block3_conv_sep_swish
BinaryOp                 underscorencnn_86_mul58  2 1 res_3_4_block3_conv_sep_batchnorm_splitncnn_0 res_3_4_block3_conv_sep_swish underscorencnn_86_mul58 0=2
ConvolutionDepthWise     res_3_4_block3_conv_dw_conv2d 1 1 underscorencnn_86_mul58 res_3_4_block3_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_86             1 2 res_3_4_block3_conv_dw_batchnorm res_3_4_block3_conv_dw_batchnorm_splitncnn_0 res_3_4_block3_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block3_conv_dw_swish 1 1 res_3_4_block3_conv_dw_batchnorm_splitncnn_1 res_3_4_block3_conv_dw_swish
BinaryOp                 underscorencnn_87_mul59  2 1 res_3_4_block3_conv_dw_batchnorm_splitncnn_0 res_3_4_block3_conv_dw_swish underscorencnn_87_mul59 0=2
Convolution              res_3_4_block3_conv_proj_conv2d 1 1 underscorencnn_87_mul59 res_3_4_block3_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_87             1 3 res_3_4_block3_conv_proj_batchnorm res_3_4_block3_conv_proj_batchnorm_splitncnn_0 res_3_4_block3_conv_proj_batchnorm_splitncnn_1 res_3_4_block3_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block3_cbam_avgpooling 1 1 res_3_4_block3_conv_proj_batchnorm_splitncnn_2 res_3_4_block3_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block3_cbam_F_avg_o_fc1 1 1 res_3_4_block3_cbam_avgpooling res_3_4_block3_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_88             1 2 res_3_4_block3_cbam_F_avg_o_fc1 res_3_4_block3_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block3_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block3_cbam_F_avg_o_swish 1 1 res_3_4_block3_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block3_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_88_mul60  2 1 res_3_4_block3_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block3_cbam_F_avg_o_swish underscorencnn_88_mul60 0=2
Convolution              res_3_4_block3_cbam_F_avg_o_fc2 1 1 underscorencnn_88_mul60 res_3_4_block3_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block3_cbam_maxpooling 1 1 res_3_4_block3_conv_proj_batchnorm_splitncnn_1 res_3_4_block3_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block3_cbam_F_max_o_fc1 1 1 res_3_4_block3_cbam_maxpooling res_3_4_block3_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_89             1 2 res_3_4_block3_cbam_F_max_o_fc1 res_3_4_block3_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block3_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block3_cbam_F_max_o_swish 1 1 res_3_4_block3_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block3_cbam_F_max_o_swish
BinaryOp                 underscorencnn_89_mul61  2 1 res_3_4_block3_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block3_cbam_F_max_o_swish underscorencnn_89_mul61 0=2
Convolution              res_3_4_block3_cbam_F_max_o_fc2 1 1 underscorencnn_89_mul61 res_3_4_block3_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block3_cbam_ele_add 2 1 res_3_4_block3_cbam_F_avg_o_fc2 res_3_4_block3_cbam_F_max_o_fc2 res_3_4_block3_cbam_ele_add
TanH                     res_3_4_block3_cbam_cn_tanh 1 1 res_3_4_block3_cbam_ele_add res_3_4_block3_cbam_cn_tanh
BinaryOp                 underscorencnn_90_minusscalar15 1 1 res_3_4_block3_cbam_cn_tanh underscorencnn_90_minusscalar15 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block3_cbam_mul1 2 1 res_3_4_block3_conv_proj_batchnorm_splitncnn_0 underscorencnn_90_minusscalar15 res_3_4_block3_cbam_mul1 0=2
BinaryOp                 underscorencnn_91_plus12 2 1 res_3_4_block3_cbam_mul1 underscorencnn_85_plus11_splitncnn_0 underscorencnn_91_plus12
Split                    splitncnn_90             1 2 underscorencnn_91_plus12 underscorencnn_91_plus12_splitncnn_0 underscorencnn_91_plus12_splitncnn_1
Convolution              res_3_4_block4_conv_sep_conv2d 1 1 underscorencnn_91_plus12_splitncnn_1 res_3_4_block4_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_91             1 2 res_3_4_block4_conv_sep_batchnorm res_3_4_block4_conv_sep_batchnorm_splitncnn_0 res_3_4_block4_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block4_conv_sep_swish 1 1 res_3_4_block4_conv_sep_batchnorm_splitncnn_1 res_3_4_block4_conv_sep_swish
BinaryOp                 underscorencnn_92_mul62  2 1 res_3_4_block4_conv_sep_batchnorm_splitncnn_0 res_3_4_block4_conv_sep_swish underscorencnn_92_mul62 0=2
ConvolutionDepthWise     res_3_4_block4_conv_dw_conv2d 1 1 underscorencnn_92_mul62 res_3_4_block4_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_92             1 2 res_3_4_block4_conv_dw_batchnorm res_3_4_block4_conv_dw_batchnorm_splitncnn_0 res_3_4_block4_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block4_conv_dw_swish 1 1 res_3_4_block4_conv_dw_batchnorm_splitncnn_1 res_3_4_block4_conv_dw_swish
BinaryOp                 underscorencnn_93_mul63  2 1 res_3_4_block4_conv_dw_batchnorm_splitncnn_0 res_3_4_block4_conv_dw_swish underscorencnn_93_mul63 0=2
Convolution              res_3_4_block4_conv_proj_conv2d 1 1 underscorencnn_93_mul63 res_3_4_block4_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_93             1 3 res_3_4_block4_conv_proj_batchnorm res_3_4_block4_conv_proj_batchnorm_splitncnn_0 res_3_4_block4_conv_proj_batchnorm_splitncnn_1 res_3_4_block4_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block4_cbam_avgpooling 1 1 res_3_4_block4_conv_proj_batchnorm_splitncnn_2 res_3_4_block4_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block4_cbam_F_avg_o_fc1 1 1 res_3_4_block4_cbam_avgpooling res_3_4_block4_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_94             1 2 res_3_4_block4_cbam_F_avg_o_fc1 res_3_4_block4_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block4_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block4_cbam_F_avg_o_swish 1 1 res_3_4_block4_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block4_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_94_mul64  2 1 res_3_4_block4_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block4_cbam_F_avg_o_swish underscorencnn_94_mul64 0=2
Convolution              res_3_4_block4_cbam_F_avg_o_fc2 1 1 underscorencnn_94_mul64 res_3_4_block4_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block4_cbam_maxpooling 1 1 res_3_4_block4_conv_proj_batchnorm_splitncnn_1 res_3_4_block4_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block4_cbam_F_max_o_fc1 1 1 res_3_4_block4_cbam_maxpooling res_3_4_block4_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_95             1 2 res_3_4_block4_cbam_F_max_o_fc1 res_3_4_block4_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block4_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block4_cbam_F_max_o_swish 1 1 res_3_4_block4_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block4_cbam_F_max_o_swish
BinaryOp                 underscorencnn_95_mul65  2 1 res_3_4_block4_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block4_cbam_F_max_o_swish underscorencnn_95_mul65 0=2
Convolution              res_3_4_block4_cbam_F_max_o_fc2 1 1 underscorencnn_95_mul65 res_3_4_block4_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block4_cbam_ele_add 2 1 res_3_4_block4_cbam_F_avg_o_fc2 res_3_4_block4_cbam_F_max_o_fc2 res_3_4_block4_cbam_ele_add
TanH                     res_3_4_block4_cbam_cn_tanh 1 1 res_3_4_block4_cbam_ele_add res_3_4_block4_cbam_cn_tanh
BinaryOp                 underscorencnn_96_minusscalar16 1 1 res_3_4_block4_cbam_cn_tanh underscorencnn_96_minusscalar16 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block4_cbam_mul1 2 1 res_3_4_block4_conv_proj_batchnorm_splitncnn_0 underscorencnn_96_minusscalar16 res_3_4_block4_cbam_mul1 0=2
BinaryOp                 underscorencnn_97_plus13 2 1 res_3_4_block4_cbam_mul1 underscorencnn_91_plus12_splitncnn_0 underscorencnn_97_plus13
Split                    splitncnn_96             1 2 underscorencnn_97_plus13 underscorencnn_97_plus13_splitncnn_0 underscorencnn_97_plus13_splitncnn_1
Convolution              res_3_4_block5_conv_sep_conv2d 1 1 underscorencnn_97_plus13_splitncnn_1 res_3_4_block5_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_97             1 2 res_3_4_block5_conv_sep_batchnorm res_3_4_block5_conv_sep_batchnorm_splitncnn_0 res_3_4_block5_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block5_conv_sep_swish 1 1 res_3_4_block5_conv_sep_batchnorm_splitncnn_1 res_3_4_block5_conv_sep_swish
BinaryOp                 underscorencnn_98_mul66  2 1 res_3_4_block5_conv_sep_batchnorm_splitncnn_0 res_3_4_block5_conv_sep_swish underscorencnn_98_mul66 0=2
ConvolutionDepthWise     res_3_4_block5_conv_dw_conv2d 1 1 underscorencnn_98_mul66 res_3_4_block5_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_98             1 2 res_3_4_block5_conv_dw_batchnorm res_3_4_block5_conv_dw_batchnorm_splitncnn_0 res_3_4_block5_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block5_conv_dw_swish 1 1 res_3_4_block5_conv_dw_batchnorm_splitncnn_1 res_3_4_block5_conv_dw_swish
BinaryOp                 underscorencnn_99_mul67  2 1 res_3_4_block5_conv_dw_batchnorm_splitncnn_0 res_3_4_block5_conv_dw_swish underscorencnn_99_mul67 0=2
Convolution              res_3_4_block5_conv_proj_conv2d 1 1 underscorencnn_99_mul67 res_3_4_block5_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_99             1 3 res_3_4_block5_conv_proj_batchnorm res_3_4_block5_conv_proj_batchnorm_splitncnn_0 res_3_4_block5_conv_proj_batchnorm_splitncnn_1 res_3_4_block5_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block5_cbam_avgpooling 1 1 res_3_4_block5_conv_proj_batchnorm_splitncnn_2 res_3_4_block5_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block5_cbam_F_avg_o_fc1 1 1 res_3_4_block5_cbam_avgpooling res_3_4_block5_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_100            1 2 res_3_4_block5_cbam_F_avg_o_fc1 res_3_4_block5_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block5_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block5_cbam_F_avg_o_swish 1 1 res_3_4_block5_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block5_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_100_mul68 2 1 res_3_4_block5_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block5_cbam_F_avg_o_swish underscorencnn_100_mul68 0=2
Convolution              res_3_4_block5_cbam_F_avg_o_fc2 1 1 underscorencnn_100_mul68 res_3_4_block5_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block5_cbam_maxpooling 1 1 res_3_4_block5_conv_proj_batchnorm_splitncnn_1 res_3_4_block5_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block5_cbam_F_max_o_fc1 1 1 res_3_4_block5_cbam_maxpooling res_3_4_block5_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_101            1 2 res_3_4_block5_cbam_F_max_o_fc1 res_3_4_block5_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block5_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block5_cbam_F_max_o_swish 1 1 res_3_4_block5_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block5_cbam_F_max_o_swish
BinaryOp                 underscorencnn_101_mul69 2 1 res_3_4_block5_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block5_cbam_F_max_o_swish underscorencnn_101_mul69 0=2
Convolution              res_3_4_block5_cbam_F_max_o_fc2 1 1 underscorencnn_101_mul69 res_3_4_block5_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block5_cbam_ele_add 2 1 res_3_4_block5_cbam_F_avg_o_fc2 res_3_4_block5_cbam_F_max_o_fc2 res_3_4_block5_cbam_ele_add
TanH                     res_3_4_block5_cbam_cn_tanh 1 1 res_3_4_block5_cbam_ele_add res_3_4_block5_cbam_cn_tanh
BinaryOp                 underscorencnn_102_minusscalar17 1 1 res_3_4_block5_cbam_cn_tanh underscorencnn_102_minusscalar17 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block5_cbam_mul1 2 1 res_3_4_block5_conv_proj_batchnorm_splitncnn_0 underscorencnn_102_minusscalar17 res_3_4_block5_cbam_mul1 0=2
BinaryOp                 underscorencnn_103_plus14 2 1 res_3_4_block5_cbam_mul1 underscorencnn_97_plus13_splitncnn_0 underscorencnn_103_plus14
Split                    splitncnn_102            1 2 underscorencnn_103_plus14 underscorencnn_103_plus14_splitncnn_0 underscorencnn_103_plus14_splitncnn_1
Convolution              res_3_4_block6_conv_sep_conv2d 1 1 underscorencnn_103_plus14_splitncnn_1 res_3_4_block6_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_103            1 2 res_3_4_block6_conv_sep_batchnorm res_3_4_block6_conv_sep_batchnorm_splitncnn_0 res_3_4_block6_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block6_conv_sep_swish 1 1 res_3_4_block6_conv_sep_batchnorm_splitncnn_1 res_3_4_block6_conv_sep_swish
BinaryOp                 underscorencnn_104_mul70 2 1 res_3_4_block6_conv_sep_batchnorm_splitncnn_0 res_3_4_block6_conv_sep_swish underscorencnn_104_mul70 0=2
ConvolutionDepthWise     res_3_4_block6_conv_dw_conv2d 1 1 underscorencnn_104_mul70 res_3_4_block6_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_104            1 2 res_3_4_block6_conv_dw_batchnorm res_3_4_block6_conv_dw_batchnorm_splitncnn_0 res_3_4_block6_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block6_conv_dw_swish 1 1 res_3_4_block6_conv_dw_batchnorm_splitncnn_1 res_3_4_block6_conv_dw_swish
BinaryOp                 underscorencnn_105_mul71 2 1 res_3_4_block6_conv_dw_batchnorm_splitncnn_0 res_3_4_block6_conv_dw_swish underscorencnn_105_mul71 0=2
Convolution              res_3_4_block6_conv_proj_conv2d 1 1 underscorencnn_105_mul71 res_3_4_block6_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_105            1 3 res_3_4_block6_conv_proj_batchnorm res_3_4_block6_conv_proj_batchnorm_splitncnn_0 res_3_4_block6_conv_proj_batchnorm_splitncnn_1 res_3_4_block6_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block6_cbam_avgpooling 1 1 res_3_4_block6_conv_proj_batchnorm_splitncnn_2 res_3_4_block6_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block6_cbam_F_avg_o_fc1 1 1 res_3_4_block6_cbam_avgpooling res_3_4_block6_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_106            1 2 res_3_4_block6_cbam_F_avg_o_fc1 res_3_4_block6_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block6_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block6_cbam_F_avg_o_swish 1 1 res_3_4_block6_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block6_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_106_mul72 2 1 res_3_4_block6_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block6_cbam_F_avg_o_swish underscorencnn_106_mul72 0=2
Convolution              res_3_4_block6_cbam_F_avg_o_fc2 1 1 underscorencnn_106_mul72 res_3_4_block6_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block6_cbam_maxpooling 1 1 res_3_4_block6_conv_proj_batchnorm_splitncnn_1 res_3_4_block6_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block6_cbam_F_max_o_fc1 1 1 res_3_4_block6_cbam_maxpooling res_3_4_block6_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_107            1 2 res_3_4_block6_cbam_F_max_o_fc1 res_3_4_block6_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block6_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block6_cbam_F_max_o_swish 1 1 res_3_4_block6_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block6_cbam_F_max_o_swish
BinaryOp                 underscorencnn_107_mul73 2 1 res_3_4_block6_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block6_cbam_F_max_o_swish underscorencnn_107_mul73 0=2
Convolution              res_3_4_block6_cbam_F_max_o_fc2 1 1 underscorencnn_107_mul73 res_3_4_block6_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block6_cbam_ele_add 2 1 res_3_4_block6_cbam_F_avg_o_fc2 res_3_4_block6_cbam_F_max_o_fc2 res_3_4_block6_cbam_ele_add
TanH                     res_3_4_block6_cbam_cn_tanh 1 1 res_3_4_block6_cbam_ele_add res_3_4_block6_cbam_cn_tanh
BinaryOp                 underscorencnn_108_minusscalar18 1 1 res_3_4_block6_cbam_cn_tanh underscorencnn_108_minusscalar18 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block6_cbam_mul1 2 1 res_3_4_block6_conv_proj_batchnorm_splitncnn_0 underscorencnn_108_minusscalar18 res_3_4_block6_cbam_mul1 0=2
BinaryOp                 underscorencnn_109_plus15 2 1 res_3_4_block6_cbam_mul1 underscorencnn_103_plus14_splitncnn_0 underscorencnn_109_plus15
Split                    splitncnn_108            1 2 underscorencnn_109_plus15 underscorencnn_109_plus15_splitncnn_0 underscorencnn_109_plus15_splitncnn_1
Convolution              res_3_4_block7_conv_sep_conv2d 1 1 underscorencnn_109_plus15_splitncnn_1 res_3_4_block7_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_109            1 2 res_3_4_block7_conv_sep_batchnorm res_3_4_block7_conv_sep_batchnorm_splitncnn_0 res_3_4_block7_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block7_conv_sep_swish 1 1 res_3_4_block7_conv_sep_batchnorm_splitncnn_1 res_3_4_block7_conv_sep_swish
BinaryOp                 underscorencnn_110_mul74 2 1 res_3_4_block7_conv_sep_batchnorm_splitncnn_0 res_3_4_block7_conv_sep_swish underscorencnn_110_mul74 0=2
ConvolutionDepthWise     res_3_4_block7_conv_dw_conv2d 1 1 underscorencnn_110_mul74 res_3_4_block7_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_110            1 2 res_3_4_block7_conv_dw_batchnorm res_3_4_block7_conv_dw_batchnorm_splitncnn_0 res_3_4_block7_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block7_conv_dw_swish 1 1 res_3_4_block7_conv_dw_batchnorm_splitncnn_1 res_3_4_block7_conv_dw_swish
BinaryOp                 underscorencnn_111_mul75 2 1 res_3_4_block7_conv_dw_batchnorm_splitncnn_0 res_3_4_block7_conv_dw_swish underscorencnn_111_mul75 0=2
Convolution              res_3_4_block7_conv_proj_conv2d 1 1 underscorencnn_111_mul75 res_3_4_block7_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_111            1 3 res_3_4_block7_conv_proj_batchnorm res_3_4_block7_conv_proj_batchnorm_splitncnn_0 res_3_4_block7_conv_proj_batchnorm_splitncnn_1 res_3_4_block7_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block7_cbam_avgpooling 1 1 res_3_4_block7_conv_proj_batchnorm_splitncnn_2 res_3_4_block7_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block7_cbam_F_avg_o_fc1 1 1 res_3_4_block7_cbam_avgpooling res_3_4_block7_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_112            1 2 res_3_4_block7_cbam_F_avg_o_fc1 res_3_4_block7_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block7_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block7_cbam_F_avg_o_swish 1 1 res_3_4_block7_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block7_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_112_mul76 2 1 res_3_4_block7_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block7_cbam_F_avg_o_swish underscorencnn_112_mul76 0=2
Convolution              res_3_4_block7_cbam_F_avg_o_fc2 1 1 underscorencnn_112_mul76 res_3_4_block7_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block7_cbam_maxpooling 1 1 res_3_4_block7_conv_proj_batchnorm_splitncnn_1 res_3_4_block7_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block7_cbam_F_max_o_fc1 1 1 res_3_4_block7_cbam_maxpooling res_3_4_block7_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_113            1 2 res_3_4_block7_cbam_F_max_o_fc1 res_3_4_block7_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block7_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block7_cbam_F_max_o_swish 1 1 res_3_4_block7_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block7_cbam_F_max_o_swish
BinaryOp                 underscorencnn_113_mul77 2 1 res_3_4_block7_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block7_cbam_F_max_o_swish underscorencnn_113_mul77 0=2
Convolution              res_3_4_block7_cbam_F_max_o_fc2 1 1 underscorencnn_113_mul77 res_3_4_block7_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block7_cbam_ele_add 2 1 res_3_4_block7_cbam_F_avg_o_fc2 res_3_4_block7_cbam_F_max_o_fc2 res_3_4_block7_cbam_ele_add
TanH                     res_3_4_block7_cbam_cn_tanh 1 1 res_3_4_block7_cbam_ele_add res_3_4_block7_cbam_cn_tanh
BinaryOp                 underscorencnn_114_minusscalar19 1 1 res_3_4_block7_cbam_cn_tanh underscorencnn_114_minusscalar19 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block7_cbam_mul1 2 1 res_3_4_block7_conv_proj_batchnorm_splitncnn_0 underscorencnn_114_minusscalar19 res_3_4_block7_cbam_mul1 0=2
BinaryOp                 underscorencnn_115_plus16 2 1 res_3_4_block7_cbam_mul1 underscorencnn_109_plus15_splitncnn_0 underscorencnn_115_plus16
Split                    splitncnn_114            1 2 underscorencnn_115_plus16 underscorencnn_115_plus16_splitncnn_0 underscorencnn_115_plus16_splitncnn_1
Convolution              res_3_4_block8_conv_sep_conv2d 1 1 underscorencnn_115_plus16_splitncnn_1 res_3_4_block8_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_115            1 2 res_3_4_block8_conv_sep_batchnorm res_3_4_block8_conv_sep_batchnorm_splitncnn_0 res_3_4_block8_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block8_conv_sep_swish 1 1 res_3_4_block8_conv_sep_batchnorm_splitncnn_1 res_3_4_block8_conv_sep_swish
BinaryOp                 underscorencnn_116_mul78 2 1 res_3_4_block8_conv_sep_batchnorm_splitncnn_0 res_3_4_block8_conv_sep_swish underscorencnn_116_mul78 0=2
ConvolutionDepthWise     res_3_4_block8_conv_dw_conv2d 1 1 underscorencnn_116_mul78 res_3_4_block8_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_116            1 2 res_3_4_block8_conv_dw_batchnorm res_3_4_block8_conv_dw_batchnorm_splitncnn_0 res_3_4_block8_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block8_conv_dw_swish 1 1 res_3_4_block8_conv_dw_batchnorm_splitncnn_1 res_3_4_block8_conv_dw_swish
BinaryOp                 underscorencnn_117_mul79 2 1 res_3_4_block8_conv_dw_batchnorm_splitncnn_0 res_3_4_block8_conv_dw_swish underscorencnn_117_mul79 0=2
Convolution              res_3_4_block8_conv_proj_conv2d 1 1 underscorencnn_117_mul79 res_3_4_block8_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_117            1 3 res_3_4_block8_conv_proj_batchnorm res_3_4_block8_conv_proj_batchnorm_splitncnn_0 res_3_4_block8_conv_proj_batchnorm_splitncnn_1 res_3_4_block8_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block8_cbam_avgpooling 1 1 res_3_4_block8_conv_proj_batchnorm_splitncnn_2 res_3_4_block8_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block8_cbam_F_avg_o_fc1 1 1 res_3_4_block8_cbam_avgpooling res_3_4_block8_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_118            1 2 res_3_4_block8_cbam_F_avg_o_fc1 res_3_4_block8_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block8_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block8_cbam_F_avg_o_swish 1 1 res_3_4_block8_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block8_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_118_mul80 2 1 res_3_4_block8_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block8_cbam_F_avg_o_swish underscorencnn_118_mul80 0=2
Convolution              res_3_4_block8_cbam_F_avg_o_fc2 1 1 underscorencnn_118_mul80 res_3_4_block8_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block8_cbam_maxpooling 1 1 res_3_4_block8_conv_proj_batchnorm_splitncnn_1 res_3_4_block8_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block8_cbam_F_max_o_fc1 1 1 res_3_4_block8_cbam_maxpooling res_3_4_block8_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_119            1 2 res_3_4_block8_cbam_F_max_o_fc1 res_3_4_block8_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block8_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block8_cbam_F_max_o_swish 1 1 res_3_4_block8_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block8_cbam_F_max_o_swish
BinaryOp                 underscorencnn_119_mul81 2 1 res_3_4_block8_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block8_cbam_F_max_o_swish underscorencnn_119_mul81 0=2
Convolution              res_3_4_block8_cbam_F_max_o_fc2 1 1 underscorencnn_119_mul81 res_3_4_block8_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block8_cbam_ele_add 2 1 res_3_4_block8_cbam_F_avg_o_fc2 res_3_4_block8_cbam_F_max_o_fc2 res_3_4_block8_cbam_ele_add
TanH                     res_3_4_block8_cbam_cn_tanh 1 1 res_3_4_block8_cbam_ele_add res_3_4_block8_cbam_cn_tanh
BinaryOp                 underscorencnn_120_minusscalar20 1 1 res_3_4_block8_cbam_cn_tanh underscorencnn_120_minusscalar20 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block8_cbam_mul1 2 1 res_3_4_block8_conv_proj_batchnorm_splitncnn_0 underscorencnn_120_minusscalar20 res_3_4_block8_cbam_mul1 0=2
BinaryOp                 underscorencnn_121_plus17 2 1 res_3_4_block8_cbam_mul1 underscorencnn_115_plus16_splitncnn_0 underscorencnn_121_plus17
Split                    splitncnn_120            1 2 underscorencnn_121_plus17 underscorencnn_121_plus17_splitncnn_0 underscorencnn_121_plus17_splitncnn_1
Convolution              res_3_4_block9_conv_sep_conv2d 1 1 underscorencnn_121_plus17_splitncnn_1 res_3_4_block9_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_121            1 2 res_3_4_block9_conv_sep_batchnorm res_3_4_block9_conv_sep_batchnorm_splitncnn_0 res_3_4_block9_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block9_conv_sep_swish 1 1 res_3_4_block9_conv_sep_batchnorm_splitncnn_1 res_3_4_block9_conv_sep_swish
BinaryOp                 underscorencnn_122_mul82 2 1 res_3_4_block9_conv_sep_batchnorm_splitncnn_0 res_3_4_block9_conv_sep_swish underscorencnn_122_mul82 0=2
ConvolutionDepthWise     res_3_4_block9_conv_dw_conv2d 1 1 underscorencnn_122_mul82 res_3_4_block9_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_122            1 2 res_3_4_block9_conv_dw_batchnorm res_3_4_block9_conv_dw_batchnorm_splitncnn_0 res_3_4_block9_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block9_conv_dw_swish 1 1 res_3_4_block9_conv_dw_batchnorm_splitncnn_1 res_3_4_block9_conv_dw_swish
BinaryOp                 underscorencnn_123_mul83 2 1 res_3_4_block9_conv_dw_batchnorm_splitncnn_0 res_3_4_block9_conv_dw_swish underscorencnn_123_mul83 0=2
Convolution              res_3_4_block9_conv_proj_conv2d 1 1 underscorencnn_123_mul83 res_3_4_block9_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_123            1 3 res_3_4_block9_conv_proj_batchnorm res_3_4_block9_conv_proj_batchnorm_splitncnn_0 res_3_4_block9_conv_proj_batchnorm_splitncnn_1 res_3_4_block9_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block9_cbam_avgpooling 1 1 res_3_4_block9_conv_proj_batchnorm_splitncnn_2 res_3_4_block9_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block9_cbam_F_avg_o_fc1 1 1 res_3_4_block9_cbam_avgpooling res_3_4_block9_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_124            1 2 res_3_4_block9_cbam_F_avg_o_fc1 res_3_4_block9_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block9_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block9_cbam_F_avg_o_swish 1 1 res_3_4_block9_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block9_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_124_mul84 2 1 res_3_4_block9_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block9_cbam_F_avg_o_swish underscorencnn_124_mul84 0=2
Convolution              res_3_4_block9_cbam_F_avg_o_fc2 1 1 underscorencnn_124_mul84 res_3_4_block9_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block9_cbam_maxpooling 1 1 res_3_4_block9_conv_proj_batchnorm_splitncnn_1 res_3_4_block9_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block9_cbam_F_max_o_fc1 1 1 res_3_4_block9_cbam_maxpooling res_3_4_block9_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_125            1 2 res_3_4_block9_cbam_F_max_o_fc1 res_3_4_block9_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block9_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block9_cbam_F_max_o_swish 1 1 res_3_4_block9_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block9_cbam_F_max_o_swish
BinaryOp                 underscorencnn_125_mul85 2 1 res_3_4_block9_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block9_cbam_F_max_o_swish underscorencnn_125_mul85 0=2
Convolution              res_3_4_block9_cbam_F_max_o_fc2 1 1 underscorencnn_125_mul85 res_3_4_block9_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block9_cbam_ele_add 2 1 res_3_4_block9_cbam_F_avg_o_fc2 res_3_4_block9_cbam_F_max_o_fc2 res_3_4_block9_cbam_ele_add
TanH                     res_3_4_block9_cbam_cn_tanh 1 1 res_3_4_block9_cbam_ele_add res_3_4_block9_cbam_cn_tanh
BinaryOp                 underscorencnn_126_minusscalar21 1 1 res_3_4_block9_cbam_cn_tanh underscorencnn_126_minusscalar21 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block9_cbam_mul1 2 1 res_3_4_block9_conv_proj_batchnorm_splitncnn_0 underscorencnn_126_minusscalar21 res_3_4_block9_cbam_mul1 0=2
BinaryOp                 underscorencnn_127_plus18 2 1 res_3_4_block9_cbam_mul1 underscorencnn_121_plus17_splitncnn_0 underscorencnn_127_plus18
Split                    splitncnn_126            1 2 underscorencnn_127_plus18 underscorencnn_127_plus18_splitncnn_0 underscorencnn_127_plus18_splitncnn_1
Convolution              res_3_4_block10_conv_sep_conv2d 1 1 underscorencnn_127_plus18_splitncnn_1 res_3_4_block10_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_127            1 2 res_3_4_block10_conv_sep_batchnorm res_3_4_block10_conv_sep_batchnorm_splitncnn_0 res_3_4_block10_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block10_conv_sep_swish 1 1 res_3_4_block10_conv_sep_batchnorm_splitncnn_1 res_3_4_block10_conv_sep_swish
BinaryOp                 underscorencnn_128_mul86 2 1 res_3_4_block10_conv_sep_batchnorm_splitncnn_0 res_3_4_block10_conv_sep_swish underscorencnn_128_mul86 0=2
ConvolutionDepthWise     res_3_4_block10_conv_dw_conv2d 1 1 underscorencnn_128_mul86 res_3_4_block10_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_128            1 2 res_3_4_block10_conv_dw_batchnorm res_3_4_block10_conv_dw_batchnorm_splitncnn_0 res_3_4_block10_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block10_conv_dw_swish 1 1 res_3_4_block10_conv_dw_batchnorm_splitncnn_1 res_3_4_block10_conv_dw_swish
BinaryOp                 underscorencnn_129_mul87 2 1 res_3_4_block10_conv_dw_batchnorm_splitncnn_0 res_3_4_block10_conv_dw_swish underscorencnn_129_mul87 0=2
Convolution              res_3_4_block10_conv_proj_conv2d 1 1 underscorencnn_129_mul87 res_3_4_block10_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_129            1 3 res_3_4_block10_conv_proj_batchnorm res_3_4_block10_conv_proj_batchnorm_splitncnn_0 res_3_4_block10_conv_proj_batchnorm_splitncnn_1 res_3_4_block10_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block10_cbam_avgpooling 1 1 res_3_4_block10_conv_proj_batchnorm_splitncnn_2 res_3_4_block10_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block10_cbam_F_avg_o_fc1 1 1 res_3_4_block10_cbam_avgpooling res_3_4_block10_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_130            1 2 res_3_4_block10_cbam_F_avg_o_fc1 res_3_4_block10_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block10_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block10_cbam_F_avg_o_swish 1 1 res_3_4_block10_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block10_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_130_mul88 2 1 res_3_4_block10_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block10_cbam_F_avg_o_swish underscorencnn_130_mul88 0=2
Convolution              res_3_4_block10_cbam_F_avg_o_fc2 1 1 underscorencnn_130_mul88 res_3_4_block10_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block10_cbam_maxpooling 1 1 res_3_4_block10_conv_proj_batchnorm_splitncnn_1 res_3_4_block10_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block10_cbam_F_max_o_fc1 1 1 res_3_4_block10_cbam_maxpooling res_3_4_block10_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_131            1 2 res_3_4_block10_cbam_F_max_o_fc1 res_3_4_block10_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block10_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block10_cbam_F_max_o_swish 1 1 res_3_4_block10_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block10_cbam_F_max_o_swish
BinaryOp                 underscorencnn_131_mul89 2 1 res_3_4_block10_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block10_cbam_F_max_o_swish underscorencnn_131_mul89 0=2
Convolution              res_3_4_block10_cbam_F_max_o_fc2 1 1 underscorencnn_131_mul89 res_3_4_block10_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block10_cbam_ele_add 2 1 res_3_4_block10_cbam_F_avg_o_fc2 res_3_4_block10_cbam_F_max_o_fc2 res_3_4_block10_cbam_ele_add
TanH                     res_3_4_block10_cbam_cn_tanh 1 1 res_3_4_block10_cbam_ele_add res_3_4_block10_cbam_cn_tanh
BinaryOp                 underscorencnn_132_minusscalar22 1 1 res_3_4_block10_cbam_cn_tanh underscorencnn_132_minusscalar22 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block10_cbam_mul1 2 1 res_3_4_block10_conv_proj_batchnorm_splitncnn_0 underscorencnn_132_minusscalar22 res_3_4_block10_cbam_mul1 0=2
BinaryOp                 underscorencnn_133_plus19 2 1 res_3_4_block10_cbam_mul1 underscorencnn_127_plus18_splitncnn_0 underscorencnn_133_plus19
Split                    splitncnn_132            1 2 underscorencnn_133_plus19 underscorencnn_133_plus19_splitncnn_0 underscorencnn_133_plus19_splitncnn_1
Convolution              res_3_4_block11_conv_sep_conv2d 1 1 underscorencnn_133_plus19_splitncnn_1 res_3_4_block11_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_133            1 2 res_3_4_block11_conv_sep_batchnorm res_3_4_block11_conv_sep_batchnorm_splitncnn_0 res_3_4_block11_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block11_conv_sep_swish 1 1 res_3_4_block11_conv_sep_batchnorm_splitncnn_1 res_3_4_block11_conv_sep_swish
BinaryOp                 underscorencnn_134_mul90 2 1 res_3_4_block11_conv_sep_batchnorm_splitncnn_0 res_3_4_block11_conv_sep_swish underscorencnn_134_mul90 0=2
ConvolutionDepthWise     res_3_4_block11_conv_dw_conv2d 1 1 underscorencnn_134_mul90 res_3_4_block11_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_134            1 2 res_3_4_block11_conv_dw_batchnorm res_3_4_block11_conv_dw_batchnorm_splitncnn_0 res_3_4_block11_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block11_conv_dw_swish 1 1 res_3_4_block11_conv_dw_batchnorm_splitncnn_1 res_3_4_block11_conv_dw_swish
BinaryOp                 underscorencnn_135_mul91 2 1 res_3_4_block11_conv_dw_batchnorm_splitncnn_0 res_3_4_block11_conv_dw_swish underscorencnn_135_mul91 0=2
Convolution              res_3_4_block11_conv_proj_conv2d 1 1 underscorencnn_135_mul91 res_3_4_block11_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_135            1 3 res_3_4_block11_conv_proj_batchnorm res_3_4_block11_conv_proj_batchnorm_splitncnn_0 res_3_4_block11_conv_proj_batchnorm_splitncnn_1 res_3_4_block11_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block11_cbam_avgpooling 1 1 res_3_4_block11_conv_proj_batchnorm_splitncnn_2 res_3_4_block11_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block11_cbam_F_avg_o_fc1 1 1 res_3_4_block11_cbam_avgpooling res_3_4_block11_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_136            1 2 res_3_4_block11_cbam_F_avg_o_fc1 res_3_4_block11_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block11_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block11_cbam_F_avg_o_swish 1 1 res_3_4_block11_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block11_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_136_mul92 2 1 res_3_4_block11_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block11_cbam_F_avg_o_swish underscorencnn_136_mul92 0=2
Convolution              res_3_4_block11_cbam_F_avg_o_fc2 1 1 underscorencnn_136_mul92 res_3_4_block11_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block11_cbam_maxpooling 1 1 res_3_4_block11_conv_proj_batchnorm_splitncnn_1 res_3_4_block11_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block11_cbam_F_max_o_fc1 1 1 res_3_4_block11_cbam_maxpooling res_3_4_block11_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_137            1 2 res_3_4_block11_cbam_F_max_o_fc1 res_3_4_block11_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block11_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block11_cbam_F_max_o_swish 1 1 res_3_4_block11_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block11_cbam_F_max_o_swish
BinaryOp                 underscorencnn_137_mul93 2 1 res_3_4_block11_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block11_cbam_F_max_o_swish underscorencnn_137_mul93 0=2
Convolution              res_3_4_block11_cbam_F_max_o_fc2 1 1 underscorencnn_137_mul93 res_3_4_block11_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block11_cbam_ele_add 2 1 res_3_4_block11_cbam_F_avg_o_fc2 res_3_4_block11_cbam_F_max_o_fc2 res_3_4_block11_cbam_ele_add
TanH                     res_3_4_block11_cbam_cn_tanh 1 1 res_3_4_block11_cbam_ele_add res_3_4_block11_cbam_cn_tanh
BinaryOp                 underscorencnn_138_minusscalar23 1 1 res_3_4_block11_cbam_cn_tanh underscorencnn_138_minusscalar23 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block11_cbam_mul1 2 1 res_3_4_block11_conv_proj_batchnorm_splitncnn_0 underscorencnn_138_minusscalar23 res_3_4_block11_cbam_mul1 0=2
BinaryOp                 underscorencnn_139_plus20 2 1 res_3_4_block11_cbam_mul1 underscorencnn_133_plus19_splitncnn_0 underscorencnn_139_plus20
Split                    splitncnn_138            1 2 underscorencnn_139_plus20 underscorencnn_139_plus20_splitncnn_0 underscorencnn_139_plus20_splitncnn_1
Convolution              res_3_4_block12_conv_sep_conv2d 1 1 underscorencnn_139_plus20_splitncnn_1 res_3_4_block12_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_139            1 2 res_3_4_block12_conv_sep_batchnorm res_3_4_block12_conv_sep_batchnorm_splitncnn_0 res_3_4_block12_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block12_conv_sep_swish 1 1 res_3_4_block12_conv_sep_batchnorm_splitncnn_1 res_3_4_block12_conv_sep_swish
BinaryOp                 underscorencnn_140_mul94 2 1 res_3_4_block12_conv_sep_batchnorm_splitncnn_0 res_3_4_block12_conv_sep_swish underscorencnn_140_mul94 0=2
ConvolutionDepthWise     res_3_4_block12_conv_dw_conv2d 1 1 underscorencnn_140_mul94 res_3_4_block12_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_140            1 2 res_3_4_block12_conv_dw_batchnorm res_3_4_block12_conv_dw_batchnorm_splitncnn_0 res_3_4_block12_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block12_conv_dw_swish 1 1 res_3_4_block12_conv_dw_batchnorm_splitncnn_1 res_3_4_block12_conv_dw_swish
BinaryOp                 underscorencnn_141_mul95 2 1 res_3_4_block12_conv_dw_batchnorm_splitncnn_0 res_3_4_block12_conv_dw_swish underscorencnn_141_mul95 0=2
Convolution              res_3_4_block12_conv_proj_conv2d 1 1 underscorencnn_141_mul95 res_3_4_block12_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_141            1 3 res_3_4_block12_conv_proj_batchnorm res_3_4_block12_conv_proj_batchnorm_splitncnn_0 res_3_4_block12_conv_proj_batchnorm_splitncnn_1 res_3_4_block12_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block12_cbam_avgpooling 1 1 res_3_4_block12_conv_proj_batchnorm_splitncnn_2 res_3_4_block12_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block12_cbam_F_avg_o_fc1 1 1 res_3_4_block12_cbam_avgpooling res_3_4_block12_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_142            1 2 res_3_4_block12_cbam_F_avg_o_fc1 res_3_4_block12_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block12_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block12_cbam_F_avg_o_swish 1 1 res_3_4_block12_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block12_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_142_mul96 2 1 res_3_4_block12_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block12_cbam_F_avg_o_swish underscorencnn_142_mul96 0=2
Convolution              res_3_4_block12_cbam_F_avg_o_fc2 1 1 underscorencnn_142_mul96 res_3_4_block12_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block12_cbam_maxpooling 1 1 res_3_4_block12_conv_proj_batchnorm_splitncnn_1 res_3_4_block12_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block12_cbam_F_max_o_fc1 1 1 res_3_4_block12_cbam_maxpooling res_3_4_block12_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_143            1 2 res_3_4_block12_cbam_F_max_o_fc1 res_3_4_block12_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block12_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block12_cbam_F_max_o_swish 1 1 res_3_4_block12_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block12_cbam_F_max_o_swish
BinaryOp                 underscorencnn_143_mul97 2 1 res_3_4_block12_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block12_cbam_F_max_o_swish underscorencnn_143_mul97 0=2
Convolution              res_3_4_block12_cbam_F_max_o_fc2 1 1 underscorencnn_143_mul97 res_3_4_block12_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block12_cbam_ele_add 2 1 res_3_4_block12_cbam_F_avg_o_fc2 res_3_4_block12_cbam_F_max_o_fc2 res_3_4_block12_cbam_ele_add
TanH                     res_3_4_block12_cbam_cn_tanh 1 1 res_3_4_block12_cbam_ele_add res_3_4_block12_cbam_cn_tanh
BinaryOp                 underscorencnn_144_minusscalar24 1 1 res_3_4_block12_cbam_cn_tanh underscorencnn_144_minusscalar24 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block12_cbam_mul1 2 1 res_3_4_block12_conv_proj_batchnorm_splitncnn_0 underscorencnn_144_minusscalar24 res_3_4_block12_cbam_mul1 0=2
BinaryOp                 underscorencnn_145_plus21 2 1 res_3_4_block12_cbam_mul1 underscorencnn_139_plus20_splitncnn_0 underscorencnn_145_plus21
Split                    splitncnn_144            1 2 underscorencnn_145_plus21 underscorencnn_145_plus21_splitncnn_0 underscorencnn_145_plus21_splitncnn_1
Convolution              res_3_4_block13_conv_sep_conv2d 1 1 underscorencnn_145_plus21_splitncnn_1 res_3_4_block13_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_145            1 2 res_3_4_block13_conv_sep_batchnorm res_3_4_block13_conv_sep_batchnorm_splitncnn_0 res_3_4_block13_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block13_conv_sep_swish 1 1 res_3_4_block13_conv_sep_batchnorm_splitncnn_1 res_3_4_block13_conv_sep_swish
BinaryOp                 underscorencnn_146_mul98 2 1 res_3_4_block13_conv_sep_batchnorm_splitncnn_0 res_3_4_block13_conv_sep_swish underscorencnn_146_mul98 0=2
ConvolutionDepthWise     res_3_4_block13_conv_dw_conv2d 1 1 underscorencnn_146_mul98 res_3_4_block13_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_146            1 2 res_3_4_block13_conv_dw_batchnorm res_3_4_block13_conv_dw_batchnorm_splitncnn_0 res_3_4_block13_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block13_conv_dw_swish 1 1 res_3_4_block13_conv_dw_batchnorm_splitncnn_1 res_3_4_block13_conv_dw_swish
BinaryOp                 underscorencnn_147_mul99 2 1 res_3_4_block13_conv_dw_batchnorm_splitncnn_0 res_3_4_block13_conv_dw_swish underscorencnn_147_mul99 0=2
Convolution              res_3_4_block13_conv_proj_conv2d 1 1 underscorencnn_147_mul99 res_3_4_block13_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_147            1 3 res_3_4_block13_conv_proj_batchnorm res_3_4_block13_conv_proj_batchnorm_splitncnn_0 res_3_4_block13_conv_proj_batchnorm_splitncnn_1 res_3_4_block13_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block13_cbam_avgpooling 1 1 res_3_4_block13_conv_proj_batchnorm_splitncnn_2 res_3_4_block13_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block13_cbam_F_avg_o_fc1 1 1 res_3_4_block13_cbam_avgpooling res_3_4_block13_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_148            1 2 res_3_4_block13_cbam_F_avg_o_fc1 res_3_4_block13_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block13_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block13_cbam_F_avg_o_swish 1 1 res_3_4_block13_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block13_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_148_mul100 2 1 res_3_4_block13_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block13_cbam_F_avg_o_swish underscorencnn_148_mul100 0=2
Convolution              res_3_4_block13_cbam_F_avg_o_fc2 1 1 underscorencnn_148_mul100 res_3_4_block13_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block13_cbam_maxpooling 1 1 res_3_4_block13_conv_proj_batchnorm_splitncnn_1 res_3_4_block13_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block13_cbam_F_max_o_fc1 1 1 res_3_4_block13_cbam_maxpooling res_3_4_block13_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_149            1 2 res_3_4_block13_cbam_F_max_o_fc1 res_3_4_block13_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block13_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block13_cbam_F_max_o_swish 1 1 res_3_4_block13_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block13_cbam_F_max_o_swish
BinaryOp                 underscorencnn_149_mul101 2 1 res_3_4_block13_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block13_cbam_F_max_o_swish underscorencnn_149_mul101 0=2
Convolution              res_3_4_block13_cbam_F_max_o_fc2 1 1 underscorencnn_149_mul101 res_3_4_block13_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block13_cbam_ele_add 2 1 res_3_4_block13_cbam_F_avg_o_fc2 res_3_4_block13_cbam_F_max_o_fc2 res_3_4_block13_cbam_ele_add
TanH                     res_3_4_block13_cbam_cn_tanh 1 1 res_3_4_block13_cbam_ele_add res_3_4_block13_cbam_cn_tanh
BinaryOp                 underscorencnn_150_minusscalar25 1 1 res_3_4_block13_cbam_cn_tanh underscorencnn_150_minusscalar25 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block13_cbam_mul1 2 1 res_3_4_block13_conv_proj_batchnorm_splitncnn_0 underscorencnn_150_minusscalar25 res_3_4_block13_cbam_mul1 0=2
BinaryOp                 underscorencnn_151_plus22 2 1 res_3_4_block13_cbam_mul1 underscorencnn_145_plus21_splitncnn_0 underscorencnn_151_plus22
Split                    splitncnn_150            1 2 underscorencnn_151_plus22 underscorencnn_151_plus22_splitncnn_0 underscorencnn_151_plus22_splitncnn_1
Convolution              res_3_4_block14_conv_sep_conv2d 1 1 underscorencnn_151_plus22_splitncnn_1 res_3_4_block14_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_151            1 2 res_3_4_block14_conv_sep_batchnorm res_3_4_block14_conv_sep_batchnorm_splitncnn_0 res_3_4_block14_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block14_conv_sep_swish 1 1 res_3_4_block14_conv_sep_batchnorm_splitncnn_1 res_3_4_block14_conv_sep_swish
BinaryOp                 underscorencnn_152_mul102 2 1 res_3_4_block14_conv_sep_batchnorm_splitncnn_0 res_3_4_block14_conv_sep_swish underscorencnn_152_mul102 0=2
ConvolutionDepthWise     res_3_4_block14_conv_dw_conv2d 1 1 underscorencnn_152_mul102 res_3_4_block14_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_152            1 2 res_3_4_block14_conv_dw_batchnorm res_3_4_block14_conv_dw_batchnorm_splitncnn_0 res_3_4_block14_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block14_conv_dw_swish 1 1 res_3_4_block14_conv_dw_batchnorm_splitncnn_1 res_3_4_block14_conv_dw_swish
BinaryOp                 underscorencnn_153_mul103 2 1 res_3_4_block14_conv_dw_batchnorm_splitncnn_0 res_3_4_block14_conv_dw_swish underscorencnn_153_mul103 0=2
Convolution              res_3_4_block14_conv_proj_conv2d 1 1 underscorencnn_153_mul103 res_3_4_block14_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_153            1 3 res_3_4_block14_conv_proj_batchnorm res_3_4_block14_conv_proj_batchnorm_splitncnn_0 res_3_4_block14_conv_proj_batchnorm_splitncnn_1 res_3_4_block14_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block14_cbam_avgpooling 1 1 res_3_4_block14_conv_proj_batchnorm_splitncnn_2 res_3_4_block14_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block14_cbam_F_avg_o_fc1 1 1 res_3_4_block14_cbam_avgpooling res_3_4_block14_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_154            1 2 res_3_4_block14_cbam_F_avg_o_fc1 res_3_4_block14_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block14_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block14_cbam_F_avg_o_swish 1 1 res_3_4_block14_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block14_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_154_mul104 2 1 res_3_4_block14_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block14_cbam_F_avg_o_swish underscorencnn_154_mul104 0=2
Convolution              res_3_4_block14_cbam_F_avg_o_fc2 1 1 underscorencnn_154_mul104 res_3_4_block14_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block14_cbam_maxpooling 1 1 res_3_4_block14_conv_proj_batchnorm_splitncnn_1 res_3_4_block14_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block14_cbam_F_max_o_fc1 1 1 res_3_4_block14_cbam_maxpooling res_3_4_block14_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_155            1 2 res_3_4_block14_cbam_F_max_o_fc1 res_3_4_block14_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block14_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block14_cbam_F_max_o_swish 1 1 res_3_4_block14_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block14_cbam_F_max_o_swish
BinaryOp                 underscorencnn_155_mul105 2 1 res_3_4_block14_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block14_cbam_F_max_o_swish underscorencnn_155_mul105 0=2
Convolution              res_3_4_block14_cbam_F_max_o_fc2 1 1 underscorencnn_155_mul105 res_3_4_block14_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block14_cbam_ele_add 2 1 res_3_4_block14_cbam_F_avg_o_fc2 res_3_4_block14_cbam_F_max_o_fc2 res_3_4_block14_cbam_ele_add
TanH                     res_3_4_block14_cbam_cn_tanh 1 1 res_3_4_block14_cbam_ele_add res_3_4_block14_cbam_cn_tanh
BinaryOp                 underscorencnn_156_minusscalar26 1 1 res_3_4_block14_cbam_cn_tanh underscorencnn_156_minusscalar26 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block14_cbam_mul1 2 1 res_3_4_block14_conv_proj_batchnorm_splitncnn_0 underscorencnn_156_minusscalar26 res_3_4_block14_cbam_mul1 0=2
BinaryOp                 underscorencnn_157_plus23 2 1 res_3_4_block14_cbam_mul1 underscorencnn_151_plus22_splitncnn_0 underscorencnn_157_plus23
Split                    splitncnn_156            1 2 underscorencnn_157_plus23 underscorencnn_157_plus23_splitncnn_0 underscorencnn_157_plus23_splitncnn_1
Convolution              res_3_4_block15_conv_sep_conv2d 1 1 underscorencnn_157_plus23_splitncnn_1 res_3_4_block15_conv_sep_batchnorm 0=256 1=1 5=1 6=32768
Split                    splitncnn_157            1 2 res_3_4_block15_conv_sep_batchnorm res_3_4_block15_conv_sep_batchnorm_splitncnn_0 res_3_4_block15_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block15_conv_sep_swish 1 1 res_3_4_block15_conv_sep_batchnorm_splitncnn_1 res_3_4_block15_conv_sep_swish
BinaryOp                 underscorencnn_158_mul106 2 1 res_3_4_block15_conv_sep_batchnorm_splitncnn_0 res_3_4_block15_conv_sep_swish underscorencnn_158_mul106 0=2
ConvolutionDepthWise     res_3_4_block15_conv_dw_conv2d 1 1 underscorencnn_158_mul106 res_3_4_block15_conv_dw_batchnorm 0=256 1=3 4=1 5=1 6=2304 7=256
Split                    splitncnn_158            1 2 res_3_4_block15_conv_dw_batchnorm res_3_4_block15_conv_dw_batchnorm_splitncnn_0 res_3_4_block15_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_3_4_block15_conv_dw_swish 1 1 res_3_4_block15_conv_dw_batchnorm_splitncnn_1 res_3_4_block15_conv_dw_swish
BinaryOp                 underscorencnn_159_mul107 2 1 res_3_4_block15_conv_dw_batchnorm_splitncnn_0 res_3_4_block15_conv_dw_swish underscorencnn_159_mul107 0=2
Convolution              res_3_4_block15_conv_proj_conv2d 1 1 underscorencnn_159_mul107 res_3_4_block15_conv_proj_batchnorm 0=128 1=1 5=1 6=32768
Split                    splitncnn_159            1 3 res_3_4_block15_conv_proj_batchnorm res_3_4_block15_conv_proj_batchnorm_splitncnn_0 res_3_4_block15_conv_proj_batchnorm_splitncnn_1 res_3_4_block15_conv_proj_batchnorm_splitncnn_2
Pooling                  res_3_4_block15_cbam_avgpooling 1 1 res_3_4_block15_conv_proj_batchnorm_splitncnn_2 res_3_4_block15_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_3_4_block15_cbam_F_avg_o_fc1 1 1 res_3_4_block15_cbam_avgpooling res_3_4_block15_cbam_F_avg_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_160            1 2 res_3_4_block15_cbam_F_avg_o_fc1 res_3_4_block15_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block15_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block15_cbam_F_avg_o_swish 1 1 res_3_4_block15_cbam_F_avg_o_fc1_splitncnn_1 res_3_4_block15_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_160_mul108 2 1 res_3_4_block15_cbam_F_avg_o_fc1_splitncnn_0 res_3_4_block15_cbam_F_avg_o_swish underscorencnn_160_mul108 0=2
Convolution              res_3_4_block15_cbam_F_avg_o_fc2 1 1 underscorencnn_160_mul108 res_3_4_block15_cbam_F_avg_o_fc2 0=128 1=1 5=1 6=4096
Pooling                  res_3_4_block15_cbam_maxpooling 1 1 res_3_4_block15_conv_proj_batchnorm_splitncnn_1 res_3_4_block15_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_3_4_block15_cbam_F_max_o_fc1 1 1 res_3_4_block15_cbam_maxpooling res_3_4_block15_cbam_F_max_o_fc1 0=32 1=1 2=4096
Split                    splitncnn_161            1 2 res_3_4_block15_cbam_F_max_o_fc1 res_3_4_block15_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block15_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_3_4_block15_cbam_F_max_o_swish 1 1 res_3_4_block15_cbam_F_max_o_fc1_splitncnn_1 res_3_4_block15_cbam_F_max_o_swish
BinaryOp                 underscorencnn_161_mul109 2 1 res_3_4_block15_cbam_F_max_o_fc1_splitncnn_0 res_3_4_block15_cbam_F_max_o_swish underscorencnn_161_mul109 0=2
Convolution              res_3_4_block15_cbam_F_max_o_fc2 1 1 underscorencnn_161_mul109 res_3_4_block15_cbam_F_max_o_fc2 0=128 1=1 5=1 6=4096
BinaryOp                 res_3_4_block15_cbam_ele_add 2 1 res_3_4_block15_cbam_F_avg_o_fc2 res_3_4_block15_cbam_F_max_o_fc2 res_3_4_block15_cbam_ele_add
TanH                     res_3_4_block15_cbam_cn_tanh 1 1 res_3_4_block15_cbam_ele_add res_3_4_block15_cbam_cn_tanh
BinaryOp                 underscorencnn_162_minusscalar27 1 1 res_3_4_block15_cbam_cn_tanh underscorencnn_162_minusscalar27 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_3_4_block15_cbam_mul1 2 1 res_3_4_block15_conv_proj_batchnorm_splitncnn_0 underscorencnn_162_minusscalar27 res_3_4_block15_cbam_mul1 0=2
BinaryOp                 underscorencnn_163_plus24 2 1 res_3_4_block15_cbam_mul1 underscorencnn_157_plus23_splitncnn_0 underscorencnn_163_plus24
Convolution              dconv_45_conv_sep_conv2d 1 1 underscorencnn_163_plus24 dconv_45_conv_sep_batchnorm 0=1024 1=1 5=1 6=131072
Split                    splitncnn_162            1 2 dconv_45_conv_sep_batchnorm dconv_45_conv_sep_batchnorm_splitncnn_0 dconv_45_conv_sep_batchnorm_splitncnn_1
Sigmoid                  dconv_45_conv_sep_swish  1 1 dconv_45_conv_sep_batchnorm_splitncnn_1 dconv_45_conv_sep_swish
BinaryOp                 underscorencnn_164_mul110 2 1 dconv_45_conv_sep_batchnorm_splitncnn_0 dconv_45_conv_sep_swish underscorencnn_164_mul110 0=2
ConvolutionDepthWise     dconv_45_conv_dw_conv2d  1 1 underscorencnn_164_mul110 dconv_45_conv_dw_batchnorm 0=1024 1=3 3=2 4=1 5=1 6=9216 7=1024
Split                    splitncnn_163            1 2 dconv_45_conv_dw_batchnorm dconv_45_conv_dw_batchnorm_splitncnn_0 dconv_45_conv_dw_batchnorm_splitncnn_1
Sigmoid                  dconv_45_conv_dw_swish   1 1 dconv_45_conv_dw_batchnorm_splitncnn_1 dconv_45_conv_dw_swish
BinaryOp                 underscorencnn_165_mul111 2 1 dconv_45_conv_dw_batchnorm_splitncnn_0 dconv_45_conv_dw_swish underscorencnn_165_mul111 0=2
Convolution              dconv_45_conv_proj_conv2d 1 1 underscorencnn_165_mul111 dconv_45_conv_proj_batchnorm 0=256 1=1 5=1 6=262144
Split                    splitncnn_164            1 3 dconv_45_conv_proj_batchnorm dconv_45_conv_proj_batchnorm_splitncnn_0 dconv_45_conv_proj_batchnorm_splitncnn_1 dconv_45_conv_proj_batchnorm_splitncnn_2
Pooling                  dconv_45_cbam_avgpooling 1 1 dconv_45_conv_proj_batchnorm_splitncnn_2 dconv_45_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             dconv_45_cbam_F_avg_o_fc1 1 1 dconv_45_cbam_avgpooling dconv_45_cbam_F_avg_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_165            1 2 dconv_45_cbam_F_avg_o_fc1 dconv_45_cbam_F_avg_o_fc1_splitncnn_0 dconv_45_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  dconv_45_cbam_F_avg_o_swish 1 1 dconv_45_cbam_F_avg_o_fc1_splitncnn_1 dconv_45_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_166_mul112 2 1 dconv_45_cbam_F_avg_o_fc1_splitncnn_0 dconv_45_cbam_F_avg_o_swish underscorencnn_166_mul112 0=2
Convolution              dconv_45_cbam_F_avg_o_fc2 1 1 underscorencnn_166_mul112 dconv_45_cbam_F_avg_o_fc2 0=256 1=1 5=1 6=16384
Pooling                  dconv_45_cbam_maxpooling 1 1 dconv_45_conv_proj_batchnorm_splitncnn_1 dconv_45_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             dconv_45_cbam_F_max_o_fc1 1 1 dconv_45_cbam_maxpooling dconv_45_cbam_F_max_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_166            1 2 dconv_45_cbam_F_max_o_fc1 dconv_45_cbam_F_max_o_fc1_splitncnn_0 dconv_45_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  dconv_45_cbam_F_max_o_swish 1 1 dconv_45_cbam_F_max_o_fc1_splitncnn_1 dconv_45_cbam_F_max_o_swish
BinaryOp                 underscorencnn_167_mul113 2 1 dconv_45_cbam_F_max_o_fc1_splitncnn_0 dconv_45_cbam_F_max_o_swish underscorencnn_167_mul113 0=2
Convolution              dconv_45_cbam_F_max_o_fc2 1 1 underscorencnn_167_mul113 dconv_45_cbam_F_max_o_fc2 0=256 1=1 5=1 6=16384
BinaryOp                 dconv_45_cbam_ele_add    2 1 dconv_45_cbam_F_avg_o_fc2 dconv_45_cbam_F_max_o_fc2 dconv_45_cbam_ele_add
TanH                     dconv_45_cbam_cn_tanh    1 1 dconv_45_cbam_ele_add dconv_45_cbam_cn_tanh
BinaryOp                 underscorencnn_168_minusscalar28 1 1 dconv_45_cbam_cn_tanh underscorencnn_168_minusscalar28 0=1 1=1 2=-1.000000e+00
BinaryOp                 dconv_45_cbam_mul1       2 1 dconv_45_conv_proj_batchnorm_splitncnn_0 underscorencnn_168_minusscalar28 dconv_45_cbam_mul1 0=2
Split                    splitncnn_167            1 2 dconv_45_cbam_mul1 dconv_45_cbam_mul1_splitncnn_0 dconv_45_cbam_mul1_splitncnn_1
Convolution              res_5_block0_conv_sep_conv2d 1 1 dconv_45_cbam_mul1_splitncnn_1 res_5_block0_conv_sep_batchnorm 0=512 1=1 5=1 6=131072
Split                    splitncnn_168            1 2 res_5_block0_conv_sep_batchnorm res_5_block0_conv_sep_batchnorm_splitncnn_0 res_5_block0_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_5_block0_conv_sep_swish 1 1 res_5_block0_conv_sep_batchnorm_splitncnn_1 res_5_block0_conv_sep_swish
BinaryOp                 underscorencnn_169_mul114 2 1 res_5_block0_conv_sep_batchnorm_splitncnn_0 res_5_block0_conv_sep_swish underscorencnn_169_mul114 0=2
ConvolutionDepthWise     res_5_block0_conv_dw_conv2d 1 1 underscorencnn_169_mul114 res_5_block0_conv_dw_batchnorm 0=512 1=3 4=1 5=1 6=4608 7=512
Split                    splitncnn_169            1 2 res_5_block0_conv_dw_batchnorm res_5_block0_conv_dw_batchnorm_splitncnn_0 res_5_block0_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_5_block0_conv_dw_swish 1 1 res_5_block0_conv_dw_batchnorm_splitncnn_1 res_5_block0_conv_dw_swish
BinaryOp                 underscorencnn_170_mul115 2 1 res_5_block0_conv_dw_batchnorm_splitncnn_0 res_5_block0_conv_dw_swish underscorencnn_170_mul115 0=2
Convolution              res_5_block0_conv_proj_conv2d 1 1 underscorencnn_170_mul115 res_5_block0_conv_proj_batchnorm 0=256 1=1 5=1 6=131072
Split                    splitncnn_170            1 3 res_5_block0_conv_proj_batchnorm res_5_block0_conv_proj_batchnorm_splitncnn_0 res_5_block0_conv_proj_batchnorm_splitncnn_1 res_5_block0_conv_proj_batchnorm_splitncnn_2
Pooling                  res_5_block0_cbam_avgpooling 1 1 res_5_block0_conv_proj_batchnorm_splitncnn_2 res_5_block0_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_5_block0_cbam_F_avg_o_fc1 1 1 res_5_block0_cbam_avgpooling res_5_block0_cbam_F_avg_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_171            1 2 res_5_block0_cbam_F_avg_o_fc1 res_5_block0_cbam_F_avg_o_fc1_splitncnn_0 res_5_block0_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_5_block0_cbam_F_avg_o_swish 1 1 res_5_block0_cbam_F_avg_o_fc1_splitncnn_1 res_5_block0_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_171_mul116 2 1 res_5_block0_cbam_F_avg_o_fc1_splitncnn_0 res_5_block0_cbam_F_avg_o_swish underscorencnn_171_mul116 0=2
Convolution              res_5_block0_cbam_F_avg_o_fc2 1 1 underscorencnn_171_mul116 res_5_block0_cbam_F_avg_o_fc2 0=256 1=1 5=1 6=16384
Pooling                  res_5_block0_cbam_maxpooling 1 1 res_5_block0_conv_proj_batchnorm_splitncnn_1 res_5_block0_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_5_block0_cbam_F_max_o_fc1 1 1 res_5_block0_cbam_maxpooling res_5_block0_cbam_F_max_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_172            1 2 res_5_block0_cbam_F_max_o_fc1 res_5_block0_cbam_F_max_o_fc1_splitncnn_0 res_5_block0_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_5_block0_cbam_F_max_o_swish 1 1 res_5_block0_cbam_F_max_o_fc1_splitncnn_1 res_5_block0_cbam_F_max_o_swish
BinaryOp                 underscorencnn_172_mul117 2 1 res_5_block0_cbam_F_max_o_fc1_splitncnn_0 res_5_block0_cbam_F_max_o_swish underscorencnn_172_mul117 0=2
Convolution              res_5_block0_cbam_F_max_o_fc2 1 1 underscorencnn_172_mul117 res_5_block0_cbam_F_max_o_fc2 0=256 1=1 5=1 6=16384
BinaryOp                 res_5_block0_cbam_ele_add 2 1 res_5_block0_cbam_F_avg_o_fc2 res_5_block0_cbam_F_max_o_fc2 res_5_block0_cbam_ele_add
TanH                     res_5_block0_cbam_cn_tanh 1 1 res_5_block0_cbam_ele_add res_5_block0_cbam_cn_tanh
BinaryOp                 underscorencnn_173_minusscalar29 1 1 res_5_block0_cbam_cn_tanh underscorencnn_173_minusscalar29 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_5_block0_cbam_mul1   2 1 res_5_block0_conv_proj_batchnorm_splitncnn_0 underscorencnn_173_minusscalar29 res_5_block0_cbam_mul1 0=2
BinaryOp                 underscorencnn_174_plus25 2 1 res_5_block0_cbam_mul1 dconv_45_cbam_mul1_splitncnn_0 underscorencnn_174_plus25
Split                    splitncnn_173            1 2 underscorencnn_174_plus25 underscorencnn_174_plus25_splitncnn_0 underscorencnn_174_plus25_splitncnn_1
Convolution              res_5_block1_conv_sep_conv2d 1 1 underscorencnn_174_plus25_splitncnn_1 res_5_block1_conv_sep_batchnorm 0=512 1=1 5=1 6=131072
Split                    splitncnn_174            1 2 res_5_block1_conv_sep_batchnorm res_5_block1_conv_sep_batchnorm_splitncnn_0 res_5_block1_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_5_block1_conv_sep_swish 1 1 res_5_block1_conv_sep_batchnorm_splitncnn_1 res_5_block1_conv_sep_swish
BinaryOp                 underscorencnn_175_mul118 2 1 res_5_block1_conv_sep_batchnorm_splitncnn_0 res_5_block1_conv_sep_swish underscorencnn_175_mul118 0=2
ConvolutionDepthWise     res_5_block1_conv_dw_conv2d 1 1 underscorencnn_175_mul118 res_5_block1_conv_dw_batchnorm 0=512 1=3 4=1 5=1 6=4608 7=512
Split                    splitncnn_175            1 2 res_5_block1_conv_dw_batchnorm res_5_block1_conv_dw_batchnorm_splitncnn_0 res_5_block1_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_5_block1_conv_dw_swish 1 1 res_5_block1_conv_dw_batchnorm_splitncnn_1 res_5_block1_conv_dw_swish
BinaryOp                 underscorencnn_176_mul119 2 1 res_5_block1_conv_dw_batchnorm_splitncnn_0 res_5_block1_conv_dw_swish underscorencnn_176_mul119 0=2
Convolution              res_5_block1_conv_proj_conv2d 1 1 underscorencnn_176_mul119 res_5_block1_conv_proj_batchnorm 0=256 1=1 5=1 6=131072
Split                    splitncnn_176            1 3 res_5_block1_conv_proj_batchnorm res_5_block1_conv_proj_batchnorm_splitncnn_0 res_5_block1_conv_proj_batchnorm_splitncnn_1 res_5_block1_conv_proj_batchnorm_splitncnn_2
Pooling                  res_5_block1_cbam_avgpooling 1 1 res_5_block1_conv_proj_batchnorm_splitncnn_2 res_5_block1_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_5_block1_cbam_F_avg_o_fc1 1 1 res_5_block1_cbam_avgpooling res_5_block1_cbam_F_avg_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_177            1 2 res_5_block1_cbam_F_avg_o_fc1 res_5_block1_cbam_F_avg_o_fc1_splitncnn_0 res_5_block1_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_5_block1_cbam_F_avg_o_swish 1 1 res_5_block1_cbam_F_avg_o_fc1_splitncnn_1 res_5_block1_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_177_mul120 2 1 res_5_block1_cbam_F_avg_o_fc1_splitncnn_0 res_5_block1_cbam_F_avg_o_swish underscorencnn_177_mul120 0=2
Convolution              res_5_block1_cbam_F_avg_o_fc2 1 1 underscorencnn_177_mul120 res_5_block1_cbam_F_avg_o_fc2 0=256 1=1 5=1 6=16384
Pooling                  res_5_block1_cbam_maxpooling 1 1 res_5_block1_conv_proj_batchnorm_splitncnn_1 res_5_block1_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_5_block1_cbam_F_max_o_fc1 1 1 res_5_block1_cbam_maxpooling res_5_block1_cbam_F_max_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_178            1 2 res_5_block1_cbam_F_max_o_fc1 res_5_block1_cbam_F_max_o_fc1_splitncnn_0 res_5_block1_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_5_block1_cbam_F_max_o_swish 1 1 res_5_block1_cbam_F_max_o_fc1_splitncnn_1 res_5_block1_cbam_F_max_o_swish
BinaryOp                 underscorencnn_178_mul121 2 1 res_5_block1_cbam_F_max_o_fc1_splitncnn_0 res_5_block1_cbam_F_max_o_swish underscorencnn_178_mul121 0=2
Convolution              res_5_block1_cbam_F_max_o_fc2 1 1 underscorencnn_178_mul121 res_5_block1_cbam_F_max_o_fc2 0=256 1=1 5=1 6=16384
BinaryOp                 res_5_block1_cbam_ele_add 2 1 res_5_block1_cbam_F_avg_o_fc2 res_5_block1_cbam_F_max_o_fc2 res_5_block1_cbam_ele_add
TanH                     res_5_block1_cbam_cn_tanh 1 1 res_5_block1_cbam_ele_add res_5_block1_cbam_cn_tanh
BinaryOp                 underscorencnn_179_minusscalar30 1 1 res_5_block1_cbam_cn_tanh underscorencnn_179_minusscalar30 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_5_block1_cbam_mul1   2 1 res_5_block1_conv_proj_batchnorm_splitncnn_0 underscorencnn_179_minusscalar30 res_5_block1_cbam_mul1 0=2
BinaryOp                 underscorencnn_180_plus26 2 1 res_5_block1_cbam_mul1 underscorencnn_174_plus25_splitncnn_0 underscorencnn_180_plus26
Split                    splitncnn_179            1 2 underscorencnn_180_plus26 underscorencnn_180_plus26_splitncnn_0 underscorencnn_180_plus26_splitncnn_1
Convolution              res_5_block2_conv_sep_conv2d 1 1 underscorencnn_180_plus26_splitncnn_1 res_5_block2_conv_sep_batchnorm 0=512 1=1 5=1 6=131072
Split                    splitncnn_180            1 2 res_5_block2_conv_sep_batchnorm res_5_block2_conv_sep_batchnorm_splitncnn_0 res_5_block2_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_5_block2_conv_sep_swish 1 1 res_5_block2_conv_sep_batchnorm_splitncnn_1 res_5_block2_conv_sep_swish
BinaryOp                 underscorencnn_181_mul122 2 1 res_5_block2_conv_sep_batchnorm_splitncnn_0 res_5_block2_conv_sep_swish underscorencnn_181_mul122 0=2
ConvolutionDepthWise     res_5_block2_conv_dw_conv2d 1 1 underscorencnn_181_mul122 res_5_block2_conv_dw_batchnorm 0=512 1=3 4=1 5=1 6=4608 7=512
Split                    splitncnn_181            1 2 res_5_block2_conv_dw_batchnorm res_5_block2_conv_dw_batchnorm_splitncnn_0 res_5_block2_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_5_block2_conv_dw_swish 1 1 res_5_block2_conv_dw_batchnorm_splitncnn_1 res_5_block2_conv_dw_swish
BinaryOp                 underscorencnn_182_mul123 2 1 res_5_block2_conv_dw_batchnorm_splitncnn_0 res_5_block2_conv_dw_swish underscorencnn_182_mul123 0=2
Convolution              res_5_block2_conv_proj_conv2d 1 1 underscorencnn_182_mul123 res_5_block2_conv_proj_batchnorm 0=256 1=1 5=1 6=131072
Split                    splitncnn_182            1 3 res_5_block2_conv_proj_batchnorm res_5_block2_conv_proj_batchnorm_splitncnn_0 res_5_block2_conv_proj_batchnorm_splitncnn_1 res_5_block2_conv_proj_batchnorm_splitncnn_2
Pooling                  res_5_block2_cbam_avgpooling 1 1 res_5_block2_conv_proj_batchnorm_splitncnn_2 res_5_block2_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_5_block2_cbam_F_avg_o_fc1 1 1 res_5_block2_cbam_avgpooling res_5_block2_cbam_F_avg_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_183            1 2 res_5_block2_cbam_F_avg_o_fc1 res_5_block2_cbam_F_avg_o_fc1_splitncnn_0 res_5_block2_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_5_block2_cbam_F_avg_o_swish 1 1 res_5_block2_cbam_F_avg_o_fc1_splitncnn_1 res_5_block2_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_183_mul124 2 1 res_5_block2_cbam_F_avg_o_fc1_splitncnn_0 res_5_block2_cbam_F_avg_o_swish underscorencnn_183_mul124 0=2
Convolution              res_5_block2_cbam_F_avg_o_fc2 1 1 underscorencnn_183_mul124 res_5_block2_cbam_F_avg_o_fc2 0=256 1=1 5=1 6=16384
Pooling                  res_5_block2_cbam_maxpooling 1 1 res_5_block2_conv_proj_batchnorm_splitncnn_1 res_5_block2_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_5_block2_cbam_F_max_o_fc1 1 1 res_5_block2_cbam_maxpooling res_5_block2_cbam_F_max_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_184            1 2 res_5_block2_cbam_F_max_o_fc1 res_5_block2_cbam_F_max_o_fc1_splitncnn_0 res_5_block2_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_5_block2_cbam_F_max_o_swish 1 1 res_5_block2_cbam_F_max_o_fc1_splitncnn_1 res_5_block2_cbam_F_max_o_swish
BinaryOp                 underscorencnn_184_mul125 2 1 res_5_block2_cbam_F_max_o_fc1_splitncnn_0 res_5_block2_cbam_F_max_o_swish underscorencnn_184_mul125 0=2
Convolution              res_5_block2_cbam_F_max_o_fc2 1 1 underscorencnn_184_mul125 res_5_block2_cbam_F_max_o_fc2 0=256 1=1 5=1 6=16384
BinaryOp                 res_5_block2_cbam_ele_add 2 1 res_5_block2_cbam_F_avg_o_fc2 res_5_block2_cbam_F_max_o_fc2 res_5_block2_cbam_ele_add
TanH                     res_5_block2_cbam_cn_tanh 1 1 res_5_block2_cbam_ele_add res_5_block2_cbam_cn_tanh
BinaryOp                 underscorencnn_185_minusscalar31 1 1 res_5_block2_cbam_cn_tanh underscorencnn_185_minusscalar31 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_5_block2_cbam_mul1   2 1 res_5_block2_conv_proj_batchnorm_splitncnn_0 underscorencnn_185_minusscalar31 res_5_block2_cbam_mul1 0=2
BinaryOp                 underscorencnn_186_plus27 2 1 res_5_block2_cbam_mul1 underscorencnn_180_plus26_splitncnn_0 underscorencnn_186_plus27
Split                    splitncnn_185            1 2 underscorencnn_186_plus27 underscorencnn_186_plus27_splitncnn_0 underscorencnn_186_plus27_splitncnn_1
Convolution              res_5_block3_conv_sep_conv2d 1 1 underscorencnn_186_plus27_splitncnn_1 res_5_block3_conv_sep_batchnorm 0=512 1=1 5=1 6=131072
Split                    splitncnn_186            1 2 res_5_block3_conv_sep_batchnorm res_5_block3_conv_sep_batchnorm_splitncnn_0 res_5_block3_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_5_block3_conv_sep_swish 1 1 res_5_block3_conv_sep_batchnorm_splitncnn_1 res_5_block3_conv_sep_swish
BinaryOp                 underscorencnn_187_mul126 2 1 res_5_block3_conv_sep_batchnorm_splitncnn_0 res_5_block3_conv_sep_swish underscorencnn_187_mul126 0=2
ConvolutionDepthWise     res_5_block3_conv_dw_conv2d 1 1 underscorencnn_187_mul126 res_5_block3_conv_dw_batchnorm 0=512 1=3 4=1 5=1 6=4608 7=512
Split                    splitncnn_187            1 2 res_5_block3_conv_dw_batchnorm res_5_block3_conv_dw_batchnorm_splitncnn_0 res_5_block3_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_5_block3_conv_dw_swish 1 1 res_5_block3_conv_dw_batchnorm_splitncnn_1 res_5_block3_conv_dw_swish
BinaryOp                 underscorencnn_188_mul127 2 1 res_5_block3_conv_dw_batchnorm_splitncnn_0 res_5_block3_conv_dw_swish underscorencnn_188_mul127 0=2
Convolution              res_5_block3_conv_proj_conv2d 1 1 underscorencnn_188_mul127 res_5_block3_conv_proj_batchnorm 0=256 1=1 5=1 6=131072
Split                    splitncnn_188            1 3 res_5_block3_conv_proj_batchnorm res_5_block3_conv_proj_batchnorm_splitncnn_0 res_5_block3_conv_proj_batchnorm_splitncnn_1 res_5_block3_conv_proj_batchnorm_splitncnn_2
Pooling                  res_5_block3_cbam_avgpooling 1 1 res_5_block3_conv_proj_batchnorm_splitncnn_2 res_5_block3_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_5_block3_cbam_F_avg_o_fc1 1 1 res_5_block3_cbam_avgpooling res_5_block3_cbam_F_avg_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_189            1 2 res_5_block3_cbam_F_avg_o_fc1 res_5_block3_cbam_F_avg_o_fc1_splitncnn_0 res_5_block3_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_5_block3_cbam_F_avg_o_swish 1 1 res_5_block3_cbam_F_avg_o_fc1_splitncnn_1 res_5_block3_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_189_mul128 2 1 res_5_block3_cbam_F_avg_o_fc1_splitncnn_0 res_5_block3_cbam_F_avg_o_swish underscorencnn_189_mul128 0=2
Convolution              res_5_block3_cbam_F_avg_o_fc2 1 1 underscorencnn_189_mul128 res_5_block3_cbam_F_avg_o_fc2 0=256 1=1 5=1 6=16384
Pooling                  res_5_block3_cbam_maxpooling 1 1 res_5_block3_conv_proj_batchnorm_splitncnn_1 res_5_block3_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_5_block3_cbam_F_max_o_fc1 1 1 res_5_block3_cbam_maxpooling res_5_block3_cbam_F_max_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_190            1 2 res_5_block3_cbam_F_max_o_fc1 res_5_block3_cbam_F_max_o_fc1_splitncnn_0 res_5_block3_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_5_block3_cbam_F_max_o_swish 1 1 res_5_block3_cbam_F_max_o_fc1_splitncnn_1 res_5_block3_cbam_F_max_o_swish
BinaryOp                 underscorencnn_190_mul129 2 1 res_5_block3_cbam_F_max_o_fc1_splitncnn_0 res_5_block3_cbam_F_max_o_swish underscorencnn_190_mul129 0=2
Convolution              res_5_block3_cbam_F_max_o_fc2 1 1 underscorencnn_190_mul129 res_5_block3_cbam_F_max_o_fc2 0=256 1=1 5=1 6=16384
BinaryOp                 res_5_block3_cbam_ele_add 2 1 res_5_block3_cbam_F_avg_o_fc2 res_5_block3_cbam_F_max_o_fc2 res_5_block3_cbam_ele_add
TanH                     res_5_block3_cbam_cn_tanh 1 1 res_5_block3_cbam_ele_add res_5_block3_cbam_cn_tanh
BinaryOp                 underscorencnn_191_minusscalar32 1 1 res_5_block3_cbam_cn_tanh underscorencnn_191_minusscalar32 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_5_block3_cbam_mul1   2 1 res_5_block3_conv_proj_batchnorm_splitncnn_0 underscorencnn_191_minusscalar32 res_5_block3_cbam_mul1 0=2
BinaryOp                 underscorencnn_192_plus28 2 1 res_5_block3_cbam_mul1 underscorencnn_186_plus27_splitncnn_0 underscorencnn_192_plus28
Split                    splitncnn_191            1 2 underscorencnn_192_plus28 underscorencnn_192_plus28_splitncnn_0 underscorencnn_192_plus28_splitncnn_1
Convolution              res_5_block4_conv_sep_conv2d 1 1 underscorencnn_192_plus28_splitncnn_1 res_5_block4_conv_sep_batchnorm 0=512 1=1 5=1 6=131072
Split                    splitncnn_192            1 2 res_5_block4_conv_sep_batchnorm res_5_block4_conv_sep_batchnorm_splitncnn_0 res_5_block4_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_5_block4_conv_sep_swish 1 1 res_5_block4_conv_sep_batchnorm_splitncnn_1 res_5_block4_conv_sep_swish
BinaryOp                 underscorencnn_193_mul130 2 1 res_5_block4_conv_sep_batchnorm_splitncnn_0 res_5_block4_conv_sep_swish underscorencnn_193_mul130 0=2
ConvolutionDepthWise     res_5_block4_conv_dw_conv2d 1 1 underscorencnn_193_mul130 res_5_block4_conv_dw_batchnorm 0=512 1=3 4=1 5=1 6=4608 7=512
Split                    splitncnn_193            1 2 res_5_block4_conv_dw_batchnorm res_5_block4_conv_dw_batchnorm_splitncnn_0 res_5_block4_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_5_block4_conv_dw_swish 1 1 res_5_block4_conv_dw_batchnorm_splitncnn_1 res_5_block4_conv_dw_swish
BinaryOp                 underscorencnn_194_mul131 2 1 res_5_block4_conv_dw_batchnorm_splitncnn_0 res_5_block4_conv_dw_swish underscorencnn_194_mul131 0=2
Convolution              res_5_block4_conv_proj_conv2d 1 1 underscorencnn_194_mul131 res_5_block4_conv_proj_batchnorm 0=256 1=1 5=1 6=131072
Split                    splitncnn_194            1 3 res_5_block4_conv_proj_batchnorm res_5_block4_conv_proj_batchnorm_splitncnn_0 res_5_block4_conv_proj_batchnorm_splitncnn_1 res_5_block4_conv_proj_batchnorm_splitncnn_2
Pooling                  res_5_block4_cbam_avgpooling 1 1 res_5_block4_conv_proj_batchnorm_splitncnn_2 res_5_block4_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_5_block4_cbam_F_avg_o_fc1 1 1 res_5_block4_cbam_avgpooling res_5_block4_cbam_F_avg_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_195            1 2 res_5_block4_cbam_F_avg_o_fc1 res_5_block4_cbam_F_avg_o_fc1_splitncnn_0 res_5_block4_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_5_block4_cbam_F_avg_o_swish 1 1 res_5_block4_cbam_F_avg_o_fc1_splitncnn_1 res_5_block4_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_195_mul132 2 1 res_5_block4_cbam_F_avg_o_fc1_splitncnn_0 res_5_block4_cbam_F_avg_o_swish underscorencnn_195_mul132 0=2
Convolution              res_5_block4_cbam_F_avg_o_fc2 1 1 underscorencnn_195_mul132 res_5_block4_cbam_F_avg_o_fc2 0=256 1=1 5=1 6=16384
Pooling                  res_5_block4_cbam_maxpooling 1 1 res_5_block4_conv_proj_batchnorm_splitncnn_1 res_5_block4_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_5_block4_cbam_F_max_o_fc1 1 1 res_5_block4_cbam_maxpooling res_5_block4_cbam_F_max_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_196            1 2 res_5_block4_cbam_F_max_o_fc1 res_5_block4_cbam_F_max_o_fc1_splitncnn_0 res_5_block4_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_5_block4_cbam_F_max_o_swish 1 1 res_5_block4_cbam_F_max_o_fc1_splitncnn_1 res_5_block4_cbam_F_max_o_swish
BinaryOp                 underscorencnn_196_mul133 2 1 res_5_block4_cbam_F_max_o_fc1_splitncnn_0 res_5_block4_cbam_F_max_o_swish underscorencnn_196_mul133 0=2
Convolution              res_5_block4_cbam_F_max_o_fc2 1 1 underscorencnn_196_mul133 res_5_block4_cbam_F_max_o_fc2 0=256 1=1 5=1 6=16384
BinaryOp                 res_5_block4_cbam_ele_add 2 1 res_5_block4_cbam_F_avg_o_fc2 res_5_block4_cbam_F_max_o_fc2 res_5_block4_cbam_ele_add
TanH                     res_5_block4_cbam_cn_tanh 1 1 res_5_block4_cbam_ele_add res_5_block4_cbam_cn_tanh
BinaryOp                 underscorencnn_197_minusscalar33 1 1 res_5_block4_cbam_cn_tanh underscorencnn_197_minusscalar33 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_5_block4_cbam_mul1   2 1 res_5_block4_conv_proj_batchnorm_splitncnn_0 underscorencnn_197_minusscalar33 res_5_block4_cbam_mul1 0=2
BinaryOp                 underscorencnn_198_plus29 2 1 res_5_block4_cbam_mul1 underscorencnn_192_plus28_splitncnn_0 underscorencnn_198_plus29
Split                    splitncnn_197            1 2 underscorencnn_198_plus29 underscorencnn_198_plus29_splitncnn_0 underscorencnn_198_plus29_splitncnn_1
Convolution              res_5_block5_conv_sep_conv2d 1 1 underscorencnn_198_plus29_splitncnn_1 res_5_block5_conv_sep_batchnorm 0=512 1=1 5=1 6=131072
Split                    splitncnn_198            1 2 res_5_block5_conv_sep_batchnorm res_5_block5_conv_sep_batchnorm_splitncnn_0 res_5_block5_conv_sep_batchnorm_splitncnn_1
Sigmoid                  res_5_block5_conv_sep_swish 1 1 res_5_block5_conv_sep_batchnorm_splitncnn_1 res_5_block5_conv_sep_swish
BinaryOp                 underscorencnn_199_mul134 2 1 res_5_block5_conv_sep_batchnorm_splitncnn_0 res_5_block5_conv_sep_swish underscorencnn_199_mul134 0=2
ConvolutionDepthWise     res_5_block5_conv_dw_conv2d 1 1 underscorencnn_199_mul134 res_5_block5_conv_dw_batchnorm 0=512 1=3 4=1 5=1 6=4608 7=512
Split                    splitncnn_199            1 2 res_5_block5_conv_dw_batchnorm res_5_block5_conv_dw_batchnorm_splitncnn_0 res_5_block5_conv_dw_batchnorm_splitncnn_1
Sigmoid                  res_5_block5_conv_dw_swish 1 1 res_5_block5_conv_dw_batchnorm_splitncnn_1 res_5_block5_conv_dw_swish
BinaryOp                 underscorencnn_200_mul135 2 1 res_5_block5_conv_dw_batchnorm_splitncnn_0 res_5_block5_conv_dw_swish underscorencnn_200_mul135 0=2
Convolution              res_5_block5_conv_proj_conv2d 1 1 underscorencnn_200_mul135 res_5_block5_conv_proj_batchnorm 0=256 1=1 5=1 6=131072
Split                    splitncnn_200            1 3 res_5_block5_conv_proj_batchnorm res_5_block5_conv_proj_batchnorm_splitncnn_0 res_5_block5_conv_proj_batchnorm_splitncnn_1 res_5_block5_conv_proj_batchnorm_splitncnn_2
Pooling                  res_5_block5_cbam_avgpooling 1 1 res_5_block5_conv_proj_batchnorm_splitncnn_2 res_5_block5_cbam_avgpooling 0=1 1=7 4=1 5=1
InnerProduct             res_5_block5_cbam_F_avg_o_fc1 1 1 res_5_block5_cbam_avgpooling res_5_block5_cbam_F_avg_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_201            1 2 res_5_block5_cbam_F_avg_o_fc1 res_5_block5_cbam_F_avg_o_fc1_splitncnn_0 res_5_block5_cbam_F_avg_o_fc1_splitncnn_1
Sigmoid                  res_5_block5_cbam_F_avg_o_swish 1 1 res_5_block5_cbam_F_avg_o_fc1_splitncnn_1 res_5_block5_cbam_F_avg_o_swish
BinaryOp                 underscorencnn_201_mul136 2 1 res_5_block5_cbam_F_avg_o_fc1_splitncnn_0 res_5_block5_cbam_F_avg_o_swish underscorencnn_201_mul136 0=2
Convolution              res_5_block5_cbam_F_avg_o_fc2 1 1 underscorencnn_201_mul136 res_5_block5_cbam_F_avg_o_fc2 0=256 1=1 5=1 6=16384
Pooling                  res_5_block5_cbam_maxpooling 1 1 res_5_block5_conv_proj_batchnorm_splitncnn_1 res_5_block5_cbam_maxpooling 1=7 4=1 5=1
InnerProduct             res_5_block5_cbam_F_max_o_fc1 1 1 res_5_block5_cbam_maxpooling res_5_block5_cbam_F_max_o_fc1 0=64 1=1 2=16384
Split                    splitncnn_202            1 2 res_5_block5_cbam_F_max_o_fc1 res_5_block5_cbam_F_max_o_fc1_splitncnn_0 res_5_block5_cbam_F_max_o_fc1_splitncnn_1
Sigmoid                  res_5_block5_cbam_F_max_o_swish 1 1 res_5_block5_cbam_F_max_o_fc1_splitncnn_1 res_5_block5_cbam_F_max_o_swish
BinaryOp                 underscorencnn_202_mul137 2 1 res_5_block5_cbam_F_max_o_fc1_splitncnn_0 res_5_block5_cbam_F_max_o_swish underscorencnn_202_mul137 0=2
Convolution              res_5_block5_cbam_F_max_o_fc2 1 1 underscorencnn_202_mul137 res_5_block5_cbam_F_max_o_fc2 0=256 1=1 5=1 6=16384
BinaryOp                 res_5_block5_cbam_ele_add 2 1 res_5_block5_cbam_F_avg_o_fc2 res_5_block5_cbam_F_max_o_fc2 res_5_block5_cbam_ele_add
TanH                     res_5_block5_cbam_cn_tanh 1 1 res_5_block5_cbam_ele_add res_5_block5_cbam_cn_tanh
BinaryOp                 underscorencnn_203_minusscalar34 1 1 res_5_block5_cbam_cn_tanh underscorencnn_203_minusscalar34 0=1 1=1 2=-1.000000e+00
BinaryOp                 res_5_block5_cbam_mul1   2 1 res_5_block5_conv_proj_batchnorm_splitncnn_0 underscorencnn_203_minusscalar34 res_5_block5_cbam_mul1 0=2
BinaryOp                 underscorencnn_204_plus30 2 1 res_5_block5_cbam_mul1 underscorencnn_198_plus29_splitncnn_0 underscorencnn_204_plus30
Convolution              conv_6sep_conv2d         1 1 underscorencnn_204_plus30 conv_6sep_batchnorm 0=1024 1=1 5=1 6=262144
Split                    splitncnn_203            1 2 conv_6sep_batchnorm conv_6sep_batchnorm_splitncnn_0 conv_6sep_batchnorm_splitncnn_1
Sigmoid                  conv_6sep_swish          1 1 conv_6sep_batchnorm_splitncnn_1 conv_6sep_swish
BinaryOp                 underscorencnn_205_mul138 2 1 conv_6sep_batchnorm_splitncnn_0 conv_6sep_swish underscorencnn_205_mul138 0=2
ConvolutionDepthWise     conv_6dw7_7_conv2d       1 1 underscorencnn_205_mul138 conv_6dw7_7_batchnorm 0=1024 1=7 5=1 6=50176 7=1024
InnerProduct             pre_fc1                  1 1 conv_6dw7_7_batchnorm fc1 0=512 1=1 2=524288
