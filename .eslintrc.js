module.exports = {
	root: true,
	parser: 'vue-eslint-parser',
	env: {
		browser: true,
		es6: true,
		node: true
	},
	extends: [
		'plugin:vue/essential',
		'plugin:prettier/recommended' // 如果同时使用了eslint和prettier发送能冲突，会使用prettier认为对的规则
	],
	plugins: [
		'prettier' // eslint会使用prettier的规则对代码格式化
	],
	parserOptions: {
		parser: 'babel-eslint',
		ecmaVersion: 6
	},
	ignorePatterns: ['uiGoVideo/'],
	// 全局变量
	globals: {
		isSuccessCode: true,
		SYSTEM_ARCH: true,
		SYSTEM_PLATFORM: true
	},
	rules: {
		'prettier/prettier': 2, // 对于不符合prettier规范的写法，eslint会提示报错
		'no-tabs': 0,
		indent: [
			2,
			'tab',
			{
				SwitchCase: 1
			}
		], // 使用tab缩进否则报错
		'space-before-function-paren': 0,
		eqeqeq: 0, // 没有使用全等则警告
		'prefer-promise-reject-errors': 0,
		'no-fallthrough': 1,
		'no-callback-literal': 0,
		'node/no-callback-literal': 0,
		'no-var': 2, // 要求使用 let 或 const 而不是 var
		'prefer-const': 2, // 要求使用 const 声明那些声明后不再被修改的变量
		'no-const-assign': 2, // 禁止修改 const 声明的变量
		'no-new-object': 2, // 禁用 Object 的构造函数
		'object-shorthand': [2, 'always'], // 对象方法的简写方式
		'no-prototype-builtins': 2, // 不要直接使用 Object.prototype 的方法
		'no-array-constructor': 2, // 禁用 Array 构造函数
		'prefer-template': 2,
		'template-curly-spacing': [2, 'never'],
		'no-eval': 0,
		'no-useless-escape': 0, // 禁用不必要的转义字符
		'no-new-func': 0,
		'space-before-blocks': 0,
		// 'no-loop-func': 2,
		// 'no-param-reassign': [2, { props: true }],
		'prefer-spread': 2,
		'no-duplicate-imports': 2,
		// 'import/imports-first': 2,
		// 'import/no-webpack-loader-syntax': 2,
		'no-iterator': 2, // 禁用 __iterator__ 属性
		'no-undef': 2 // 禁用未声明的变量，除非它们在 /*global */ 注释中被提到
		// 'no-unused-vars': [1, { vars: 'local', args: 'after-used' }] // 禁止出现未使用过的变量
	}
}
